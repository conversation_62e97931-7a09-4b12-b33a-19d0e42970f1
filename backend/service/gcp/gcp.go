package gcp

import (
	"context"
	"fmt"
	"strings"

	"cloud.google.com/go/asset/apiv1"
	"cloud.google.com/go/asset/apiv1/assetpb"
	"go.uber.org/zap"
	"google.golang.org/api/compute/v1"
	"google.golang.org/api/option"
)

// Service represents the GCP service
type Service struct {
	logger        *zap.Logger
	computeService *compute.Service
	assetClient   *asset.Client
	projectID     string
}

// Resource represents a GCP resource
type Resource struct {
	Provider string            `json:"provider"`
	Type     string            `json:"type"`
	ID       string            `json:"id"`
	Name     string            `json:"name"`
	Zone     string            `json:"zone"`
	Region   string            `json:"region"`
	Tags     map[string]string `json:"tags"`
	State    string            `json:"state"`
}

// New creates a new GCP service
func New(logger *zap.Logger, projectID string) (*Service, error) {
	ctx := context.Background()

	// Create compute service
	computeService, err := compute.NewService(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to create compute service: %w", err)
	}

	// Create asset client
	assetClient, err := asset.NewClient(ctx)
	if err != nil {
		logger.Warn("Failed to create asset client, some features may be limited", zap.Error(err))
	}

	return &Service{
		logger:        logger,
		computeService: computeService,
		assetClient:   assetClient,
		projectID:     projectID,
	}, nil
}

// DiscoverComputeInstances discovers Compute Engine instances
func (s *Service) DiscoverComputeInstances(ctx context.Context) ([]Resource, error) {
	s.logger.Info("Discovering GCP Compute instances")

	var resources []Resource

	// List all zones
	zoneList, err := s.computeService.Zones.List(s.projectID).Context(ctx).Do()
	if err != nil {
		return nil, fmt.Errorf("failed to list zones: %w", err)
	}

	for _, zone := range zoneList.Items {
		// List instances in each zone
		instanceList, err := s.computeService.Instances.List(s.projectID, zone.Name).Context(ctx).Do()
		if err != nil {
			s.logger.Warn("Failed to list instances in zone", zap.String("zone", zone.Name), zap.Error(err))
			continue
		}

		for _, instance := range instanceList.Items {
			resource := Resource{
				Provider: "gcp",
				Type:     "compute.instance",
				ID:       fmt.Sprintf("projects/%s/zones/%s/instances/%s", s.projectID, zone.Name, instance.Name),
				Name:     instance.Name,
				Zone:     zone.Name,
				Region:   extractRegionFromZone(zone.Name),
				State:    instance.Status,
				Tags:     make(map[string]string),
			}

			// Extract labels as tags
			if instance.Labels != nil {
				for key, value := range instance.Labels {
					resource.Tags[key] = value
				}
			}

			// Extract metadata as tags
			if instance.Metadata != nil {
				for _, item := range instance.Metadata.Items {
					if item.Value != nil {
						resource.Tags["metadata."+item.Key] = *item.Value
					}
				}
			}

			resources = append(resources, resource)
		}
	}

	s.logger.Info("Discovered GCP Compute instances", zap.Int("count", len(resources)))
	return resources, nil
}

// DiscoverNetworks discovers VPC networks
func (s *Service) DiscoverNetworks(ctx context.Context) ([]Resource, error) {
	s.logger.Info("Discovering GCP Networks")

	networkList, err := s.computeService.Networks.List(s.projectID).Context(ctx).Do()
	if err != nil {
		return nil, fmt.Errorf("failed to list networks: %w", err)
	}

	var resources []Resource
	for _, network := range networkList.Items {
		resource := Resource{
			Provider: "gcp",
			Type:     "compute.network",
			ID:       fmt.Sprintf("projects/%s/global/networks/%s", s.projectID, network.Name),
			Name:     network.Name,
			Region:   "global",
			Tags:     make(map[string]string),
		}

		// Add network-specific information as tags
		resource.Tags["routing_mode"] = network.RoutingConfig.RoutingMode
		if network.Description != "" {
			resource.Tags["description"] = network.Description
		}

		resources = append(resources, resource)
	}

	s.logger.Info("Discovered GCP Networks", zap.Int("count", len(resources)))
	return resources, nil
}

// DiscoverFirewalls discovers firewall rules
func (s *Service) DiscoverFirewalls(ctx context.Context) ([]Resource, error) {
	s.logger.Info("Discovering GCP Firewalls")

	firewallList, err := s.computeService.Firewalls.List(s.projectID).Context(ctx).Do()
	if err != nil {
		return nil, fmt.Errorf("failed to list firewalls: %w", err)
	}

	var resources []Resource
	for _, firewall := range firewallList.Items {
		resource := Resource{
			Provider: "gcp",
			Type:     "compute.firewall",
			ID:       fmt.Sprintf("projects/%s/global/firewalls/%s", s.projectID, firewall.Name),
			Name:     firewall.Name,
			Region:   "global",
			Tags:     make(map[string]string),
		}

		// Add firewall-specific information as tags
		resource.Tags["direction"] = firewall.Direction
		if firewall.Description != "" {
			resource.Tags["description"] = firewall.Description
		}
		if len(firewall.TargetTags) > 0 {
			resource.Tags["target_tags"] = strings.Join(firewall.TargetTags, ",")
		}

		resources = append(resources, resource)
	}

	s.logger.Info("Discovered GCP Firewalls", zap.Int("count", len(resources)))
	return resources, nil
}

// SearchResources searches for resources based on criteria
func (s *Service) SearchResources(ctx context.Context, resourceType string, filters map[string]string) ([]Resource, error) {
	s.logger.Info("Searching GCP resources", 
		zap.String("type", resourceType), 
		zap.Any("filters", filters))

	var resources []Resource
	var err error

	switch resourceType {
	case "compute.instance", "instance", "":
		resources, err = s.DiscoverComputeInstances(ctx)
	case "compute.network", "network":
		resources, err = s.DiscoverNetworks(ctx)
	case "compute.firewall", "firewall":
		resources, err = s.DiscoverFirewalls(ctx)
	default:
		// If no specific type, discover all
		instances, err1 := s.DiscoverComputeInstances(ctx)
		if err1 != nil {
			s.logger.Warn("Failed to discover compute instances", zap.Error(err1))
		} else {
			resources = append(resources, instances...)
		}

		networks, err2 := s.DiscoverNetworks(ctx)
		if err2 != nil {
			s.logger.Warn("Failed to discover networks", zap.Error(err2))
		} else {
			resources = append(resources, networks...)
		}

		firewalls, err3 := s.DiscoverFirewalls(ctx)
		if err3 != nil {
			s.logger.Warn("Failed to discover firewalls", zap.Error(err3))
		} else {
			resources = append(resources, firewalls...)
		}
	}

	if err != nil {
		return nil, err
	}

	// Apply filters
	if len(filters) > 0 {
		filtered := make([]Resource, 0)
		for _, resource := range resources {
			matches := true
			for key, value := range filters {
				if tagValue, exists := resource.Tags[key]; !exists || tagValue != value {
					matches = false
					break
				}
			}
			if matches {
				filtered = append(filtered, resource)
			}
		}
		resources = filtered
	}

	s.logger.Info("Search completed", zap.Int("results", len(resources)))
	return resources, nil
}

// GetResource gets a specific resource by ID
func (s *Service) GetResource(ctx context.Context, resourceID string) (*Resource, error) {
	s.logger.Info("Getting GCP resource", zap.String("id", resourceID))

	// Try to find the resource by searching all types
	resources, err := s.SearchResources(ctx, "", nil)
	if err != nil {
		return nil, err
	}

	for _, resource := range resources {
		if resource.ID == resourceID {
			return &resource, nil
		}
	}

	return nil, fmt.Errorf("resource not found: %s", resourceID)
}

// Health checks the health of the GCP service
func (s *Service) Health(ctx context.Context) error {
	// Try to make a simple API call to verify connectivity
	_, err := s.computeService.Projects.Get(s.projectID).Context(ctx).Do()
	if err != nil {
		return fmt.Errorf("GCP health check failed: %w", err)
	}
	return nil
}

// Helper function to extract region from zone name
func extractRegionFromZone(zone string) string {
	// Zone format is typically "region-zone" (e.g., "us-central1-a")
	parts := strings.Split(zone, "-")
	if len(parts) >= 2 {
		return strings.Join(parts[:len(parts)-1], "-")
	}
	return zone
}
