import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { Layout } from './components/Layout';
import { Dashboard } from './pages/Dashboard';
import SearchDashboard from './pages/SearchDashboard';
import DiscoveryWizard from './pages/DiscoveryWizard';
import WorkflowExecutor from './pages/WorkflowExecutor';
import EnvoyConfigEditor from './pages/EnvoyConfigEditor';
import AutoscalerDashboard from './pages/AutoscalerDashboard';
import AuditViewer from './pages/AuditViewer';
import DatabaseAdmin from './pages/DatabaseAdmin';
import './index.css';

function App() {
  return (
    <div className="min-h-screen bg-theme-bg-container">
      <Layout>
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/search" element={<SearchDashboard />} />
          <Route path="/discovery" element={<DiscoveryWizard />} />
          <Route path="/workflows" element={<WorkflowExecutor />} />
          <Route path="/envoy" element={<EnvoyConfigEditor />} />
          <Route path="/autoscaler" element={<AutoscalerDashboard />} />
          <Route path="/audit" element={<AuditViewer />} />
          <Route path="/database" element={<DatabaseAdmin />} />
        </Routes>
      </Layout>
    </div>
  );
}

export default App;
