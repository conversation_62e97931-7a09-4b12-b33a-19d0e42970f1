// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`renders correctly 1`] = `
<DocumentFragment>
  <div
    class="MuiGrid-root MuiGrid-container MuiGrid-direction-xs-column css-zxxfrz-MuiGrid-root"
    style="min-height: 80vh;"
  >
    <div
      class="MuiGrid-root MuiGrid-item css-15yh1qb-MuiGrid-root"
    >
      <svg
        aria-hidden="true"
        class="MuiSvgIcon-root MuiSvgIcon-fontSizeInherit css-1yk9s9q-MuiSvgIcon-root"
        data-testid="ThumbDownIcon"
        focusable="false"
        viewBox="0 0 24 24"
      >
        <path
          d="M15 3H6c-.83 0-1.54.5-1.84 1.22l-3.02 7.05c-.09.23-.14.47-.14.73v2c0 1.1.9 2 2 2h6.31l-.95 4.57-.03.32c0 .41.17.79.44 1.06L9.83 23l6.59-6.59c.36-.36.58-.86.58-1.41V5c0-1.1-.9-2-2-2zm4 0v12h4V3h-4z"
        />
      </svg>
    </div>
    <div
      class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
    >
      <h3
        class="MuiTypography-root MuiTypography-h3 MuiTypography-alignCenter css-1gpd3y6-MuiTypography-root"
      >
        <div
          class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
        >
          Whoops...
        </div>
        <div
          class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
        >
          Looks like you took a wrong turn
        </div>
      </h3>
      <h6
        class="MuiTypography-root MuiTypography-h6 MuiTypography-alignCenter css-1yp68kf-MuiTypography-root"
      >
        &lt; 404 Not Found &gt;
      </h6>
    </div>
  </div>
</DocumentFragment>
`;
