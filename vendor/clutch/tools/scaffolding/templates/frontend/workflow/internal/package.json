{
  "name": "@clutch-sh/{{ .PackageName }}",
  "version": "1.0.0-beta",
  "private": true,
  "description": " {{ .Description }}",
  "license": "Apache-2.0",
  "author": "{{ .DeveloperEmail }}",
  "main": "dist/index.js",
  "scripts": {
    "build": "yarn run clean && yarn run compile",
    "clean": "yarn run package:clean",
    "compile": "yarn run package:compile",
    "compile:dev": "yarn run package:compile:dev",
    "compile:watch": "yarn run package:compile:watch",
    "lint": "yarn run package:lint",
    "lint:fix": "yarn run lint --fix",
    "publishBeta": "../../../tools/publish-frontend.sh {{ .PackageName }}",
    "test": "yarn run package:test",
    "test:coverage": "yarn run test --collect-coverage",
    "test:watch": "yarn run test --watch"
  },
  "dependencies": {
    {{- if .IsW<PERSON>rdTemplate}}
    "@clutch-sh/core": "workspace:^",
    "@clutch-sh/wizard": "workspace:^"
    {{- else}}
    "@clutch-sh/core": "workspace:^"
    {{- end}}
  },
  "devDependencies": {
    "@clutch-sh/tools": "workspace:^"
  },
  "peerDependencies": {
    "react": "^17.0.2",
    "react-dom": "^17.0.2",
    "typescript": "^4.2.3"
  },
  "engines": {
    "node": ">=18 <19",
    "yarn": "^4.5.0"
  },
  "packageManager": "yarn@4.5.0",
  "stableVersion": "1.0.0-beta"
}
