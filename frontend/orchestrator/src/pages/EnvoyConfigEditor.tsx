import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../store/store';
import {
  fetchEnvoyConfigs,
  fetchEnvoyNodes,
  createEnvoyConfig,
  setSelectedConfig,
} from '../store/slices/envoySlice';
import {
  GlobeAltIcon,
  PlusIcon,
  DocumentTextIcon,
} from '@heroicons/react/24/outline';

const EnvoyConfigEditor: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { configs, nodes, selectedConfig, loading, error } = useSelector(
    (state: RootState) => state.envoy
  );

  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newConfig, setNewConfig] = useState({
    node_id: '',
    cluster_name: '',
    config: '',
    version: '1.0.0',
  });

  useEffect(() => {
    dispatch(fetchEnvoyConfigs());
    dispatch(fetchEnvoyNodes());
  }, [dispatch]);

  const handleCreateConfig = () => {
    dispatch(createEnvoyConfig(newConfig));
    setNewConfig({ node_id: '', cluster_name: '', config: '', version: '1.0.0' });
    setShowCreateForm(false);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Envoy Configuration</h1>
          <p className="mt-1 text-sm text-gray-400">
            Manage Envoy proxy configurations and deployments
          </p>
        </div>
        <button
          onClick={() => setShowCreateForm(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          New Config
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Configurations List */}
        <div className="bg-gray-800 shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-white mb-4">
              Configurations
            </h3>

            {loading ? (
              <div className="flex items-center justify-center h-32">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500"></div>
              </div>
            ) : (
              <div className="space-y-3">
                {configs.map((config) => (
                  <div
                    key={config.id}
                    className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                      selectedConfig?.id === config.id
                        ? 'border-cyan-500 bg-cyan-500/10'
                        : 'border-gray-600 hover:bg-gray-700'
                    }`}
                    onClick={() => dispatch(setSelectedConfig(config))}
                  >
                    <div className="flex items-start justify-between">
                      <div>
                        <h4 className="text-lg font-medium text-white">{config.cluster_name}</h4>
                        <p className="mt-1 text-sm text-gray-400">Node: {config.node_id}</p>
                        <p className="mt-1 text-xs text-gray-500">Version: {config.version}</p>
                      </div>
                      <DocumentTextIcon className="h-6 w-6 text-gray-400" />
                    </div>
                  </div>
                ))}

                {configs.length === 0 && (
                  <div className="text-center py-8">
                    <GlobeAltIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-300">No configurations</h3>
                    <p className="mt-1 text-sm text-gray-400">
                      Create your first Envoy configuration.
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Config Editor */}
        <div className="bg-gray-800 shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-white mb-4">
              Configuration Editor
            </h3>

            {selectedConfig ? (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300">
                    Cluster Name
                  </label>
                  <input
                    type="text"
                    className="mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500"
                    value={selectedConfig.cluster_name}
                    readOnly
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300">
                    Node ID
                  </label>
                  <input
                    type="text"
                    className="mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500"
                    value={selectedConfig.node_id}
                    readOnly
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300">
                    Configuration (YAML)
                  </label>
                  <textarea
                    className="mt-1 block w-full h-64 px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white font-mono text-sm focus:outline-none focus:ring-2 focus:ring-cyan-500"
                    value={selectedConfig.config}
                    readOnly
                  />
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-300">Select a configuration</h3>
                <p className="mt-1 text-sm text-gray-400">
                  Choose a configuration from the list to view and edit it.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Connected Nodes */}
      <div className="bg-gray-800 shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-white mb-4">
            Connected Envoy Nodes
          </h3>

          {nodes.length === 0 ? (
            <div className="text-center py-8">
              <GlobeAltIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-300">No nodes connected</h3>
              <p className="mt-1 text-sm text-gray-400">
                Envoy nodes will appear here when they connect to the control plane.
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
              {nodes.map((node) => (
                <div key={node.id} className="border border-gray-600 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="text-lg font-medium text-white">{node.id}</h4>
                      <p className="text-sm text-gray-400">Cluster: {node.cluster}</p>
                      <p className="text-xs text-gray-500">Version: {node.version}</p>
                    </div>
                    <div className={`w-3 h-3 rounded-full ${
                      node.status === 'connected' ? 'bg-green-400' : 'bg-red-400'
                    }`} />
                  </div>
                  <p className="mt-2 text-xs text-gray-500">
                    Last seen: {new Date(node.last_seen).toLocaleString()}
                  </p>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Create Config Modal */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-gray-800">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-white mb-4">Create New Configuration</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300">Node ID</label>
                  <input
                    type="text"
                    className="mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500"
                    value={newConfig.node_id}
                    onChange={(e) => setNewConfig({ ...newConfig, node_id: e.target.value })}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300">Cluster Name</label>
                  <input
                    type="text"
                    className="mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500"
                    value={newConfig.cluster_name}
                    onChange={(e) => setNewConfig({ ...newConfig, cluster_name: e.target.value })}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300">Configuration</label>
                  <textarea
                    className="mt-1 block w-full h-32 px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white font-mono text-sm focus:outline-none focus:ring-2 focus:ring-cyan-500"
                    value={newConfig.config}
                    onChange={(e) => setNewConfig({ ...newConfig, config: e.target.value })}
                    placeholder="Enter YAML configuration..."
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowCreateForm(false)}
                  className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
                >
                  Cancel
                </button>
                <button
                  onClick={handleCreateConfig}
                  className="px-4 py-2 bg-cyan-600 text-white rounded-md hover:bg-cyan-700"
                >
                  Create
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EnvoyConfigEditor;
