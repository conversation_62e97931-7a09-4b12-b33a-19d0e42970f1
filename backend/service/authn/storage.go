package authn

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"golang.org/x/oauth2"
	"go.uber.org/zap"
)

// MemoryStorage implements in-memory token storage
type MemoryStorage struct {
	logger *zap.Logger
	tokens map[string]map[string]*StoredToken
	mu     sync.RWMutex
}

// StoredToken represents a token stored in memory
type StoredToken struct {
	Token     *oauth2.Token `json:"token"`
	CreatedAt time.Time     `json:"created_at"`
	UpdatedAt time.Time     `json:"updated_at"`
}

// NewMemoryStorage creates a new in-memory token storage
func NewMemoryStorage(logger *zap.Logger) *MemoryStorage {
	storage := &MemoryStorage{
		logger: logger,
		tokens: make(map[string]map[string]*StoredToken),
	}

	// Start cleanup goroutine
	go storage.cleanupExpiredTokens()

	return storage
}

// Store stores a token for a user and provider
func (s *MemoryStorage) Store(ctx context.Context, userID, provider string, token *oauth2.Token) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	key := s.makeKey(userID, provider)
	
	if s.tokens[userID] == nil {
		s.tokens[userID] = make(map[string]*StoredToken)
	}

	now := time.Now()
	storedToken := &StoredToken{
		Token:     token,
		CreatedAt: now,
		UpdatedAt: now,
	}

	// Update creation time if this is a new token
	if existing, exists := s.tokens[userID][provider]; exists {
		storedToken.CreatedAt = existing.CreatedAt
	}

	s.tokens[userID][provider] = storedToken

	s.logger.Debug("Token stored",
		zap.String("user_id", userID),
		zap.String("provider", provider),
		zap.String("key", key),
		zap.Time("expires", token.Expiry))

	return nil
}

// Read reads a token for a user and provider
func (s *MemoryStorage) Read(ctx context.Context, userID, provider string) (*oauth2.Token, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	key := s.makeKey(userID, provider)

	userTokens, exists := s.tokens[userID]
	if !exists {
		return nil, fmt.Errorf("no tokens found for user %s", userID)
	}

	storedToken, exists := userTokens[provider]
	if !exists {
		return nil, fmt.Errorf("no token found for user %s and provider %s", userID, provider)
	}

	s.logger.Debug("Token retrieved",
		zap.String("user_id", userID),
		zap.String("provider", provider),
		zap.String("key", key),
		zap.Time("expires", storedToken.Token.Expiry))

	return storedToken.Token, nil
}

// Delete deletes a token for a user and provider
func (s *MemoryStorage) Delete(ctx context.Context, userID, provider string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	key := s.makeKey(userID, provider)

	userTokens, exists := s.tokens[userID]
	if !exists {
		return fmt.Errorf("no tokens found for user %s", userID)
	}

	if _, exists := userTokens[provider]; !exists {
		return fmt.Errorf("no token found for user %s and provider %s", userID, provider)
	}

	delete(userTokens, provider)

	// Clean up empty user map
	if len(userTokens) == 0 {
		delete(s.tokens, userID)
	}

	s.logger.Debug("Token deleted",
		zap.String("user_id", userID),
		zap.String("provider", provider),
		zap.String("key", key))

	return nil
}

// List lists all tokens for a user
func (s *MemoryStorage) List(ctx context.Context, userID string) (map[string]*oauth2.Token, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	userTokens, exists := s.tokens[userID]
	if !exists {
		return make(map[string]*oauth2.Token), nil
	}

	result := make(map[string]*oauth2.Token, len(userTokens))
	for provider, storedToken := range userTokens {
		result[provider] = storedToken.Token
	}

	s.logger.Debug("Tokens listed",
		zap.String("user_id", userID),
		zap.Int("count", len(result)))

	return result, nil
}

// GetStats returns storage statistics
func (s *MemoryStorage) GetStats(ctx context.Context) (map[string]interface{}, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	totalTokens := 0
	expiredTokens := 0
	now := time.Now()

	for _, userTokens := range s.tokens {
		for _, storedToken := range userTokens {
			totalTokens++
			if !storedToken.Token.Expiry.IsZero() && storedToken.Token.Expiry.Before(now) {
				expiredTokens++
			}
		}
	}

	stats := map[string]interface{}{
		"total_users":    len(s.tokens),
		"total_tokens":   totalTokens,
		"expired_tokens": expiredTokens,
		"storage_type":   "memory",
	}

	return stats, nil
}

// makeKey creates a storage key from user ID and provider
func (s *MemoryStorage) makeKey(userID, provider string) string {
	return fmt.Sprintf("%s:%s", userID, provider)
}

// cleanupExpiredTokens periodically removes expired tokens
func (s *MemoryStorage) cleanupExpiredTokens() {
	ticker := time.NewTicker(1 * time.Hour)
	defer ticker.Stop()

	for range ticker.C {
		s.performCleanup()
	}
}

// performCleanup removes expired tokens from storage
func (s *MemoryStorage) performCleanup() {
	s.mu.Lock()
	defer s.mu.Unlock()

	now := time.Now()
	cleanedCount := 0

	for userID, userTokens := range s.tokens {
		for provider, storedToken := range userTokens {
			// Remove expired tokens (with 5 minute grace period)
			if !storedToken.Token.Expiry.IsZero() && 
			   storedToken.Token.Expiry.Add(5*time.Minute).Before(now) {
				delete(userTokens, provider)
				cleanedCount++
			}
		}

		// Clean up empty user maps
		if len(userTokens) == 0 {
			delete(s.tokens, userID)
		}
	}

	if cleanedCount > 0 {
		s.logger.Info("Cleaned up expired tokens",
			zap.Int("cleaned_count", cleanedCount),
			zap.Int("remaining_users", len(s.tokens)))
	}
}

// Health checks the health of the storage
func (s *MemoryStorage) Health(ctx context.Context) error {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// Simple health check - storage is healthy if we can access the map
	_ = len(s.tokens)
	return nil
}

// Export exports all tokens to JSON (for backup/migration)
func (s *MemoryStorage) Export(ctx context.Context) ([]byte, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	exportData := make(map[string]map[string]*StoredToken)
	for userID, userTokens := range s.tokens {
		exportData[userID] = make(map[string]*StoredToken)
		for provider, storedToken := range userTokens {
			exportData[userID][provider] = storedToken
		}
	}

	data, err := json.MarshalIndent(exportData, "", "  ")
	if err != nil {
		return nil, fmt.Errorf("failed to marshal export data: %w", err)
	}

	s.logger.Info("Tokens exported",
		zap.Int("users", len(exportData)))

	return data, nil
}

// Import imports tokens from JSON (for backup/migration)
func (s *MemoryStorage) Import(ctx context.Context, data []byte) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	var importData map[string]map[string]*StoredToken
	if err := json.Unmarshal(data, &importData); err != nil {
		return fmt.Errorf("failed to unmarshal import data: %w", err)
	}

	// Clear existing tokens
	s.tokens = make(map[string]map[string]*StoredToken)

	// Import new tokens
	for userID, userTokens := range importData {
		s.tokens[userID] = make(map[string]*StoredToken)
		for provider, storedToken := range userTokens {
			s.tokens[userID][provider] = storedToken
		}
	}

	s.logger.Info("Tokens imported",
		zap.Int("users", len(importData)))

	return nil
}

// Clear removes all tokens from storage
func (s *MemoryStorage) Clear(ctx context.Context) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	oldCount := len(s.tokens)
	s.tokens = make(map[string]map[string]*StoredToken)

	s.logger.Info("Storage cleared",
		zap.Int("removed_users", oldCount))

	return nil
}
