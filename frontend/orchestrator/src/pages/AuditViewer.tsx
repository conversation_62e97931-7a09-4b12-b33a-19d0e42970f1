import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../store/store';
import { fetchAuditEvents, setQuery } from '../store/slices/auditSlice';
import {
  ShieldCheckIcon,
  MagnifyingGlassIcon,
  CheckCircleIcon,
  XCircleIcon,
} from '@heroicons/react/24/outline';

const AuditViewer: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { events, loading, error, totalCount, query } = useSelector(
    (state: RootState) => state.audit
  );

  const [filters, setFilters] = useState({
    user_id: '',
    action: '',
    resource: '',
    start_time: '',
    end_time: '',
  });

  useEffect(() => {
    dispatch(fetchAuditEvents({ limit: 50 }));
  }, [dispatch]);

  const handleSearch = () => {
    const searchQuery = { ...filters, limit: 50 };
    dispatch(setQuery(searchQuery));
    dispatch(fetchAuditEvents(searchQuery));
  };

  const getActionColor = (action: string) => {
    if (action.includes('create') || action.includes('add')) return 'text-green-400';
    if (action.includes('delete') || action.includes('remove')) return 'text-red-400';
    if (action.includes('update') || action.includes('modify')) return 'text-yellow-400';
    return 'text-blue-400';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-white">Audit Trail</h1>
        <p className="mt-1 text-sm text-gray-400">
          Tamper-proof audit logs powered by ImmuDB
        </p>
      </div>

      {/* Filters */}
      <div className="bg-gray-800 shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-white mb-4">Filters</h3>
          
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
            <div>
              <label className="block text-sm font-medium text-gray-300">User ID</label>
              <input
                type="text"
                className="mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500"
                value={filters.user_id}
                onChange={(e) => setFilters({ ...filters, user_id: e.target.value })}
                placeholder="Filter by user..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300">Action</label>
              <input
                type="text"
                className="mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500"
                value={filters.action}
                onChange={(e) => setFilters({ ...filters, action: e.target.value })}
                placeholder="Filter by action..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300">Resource</label>
              <input
                type="text"
                className="mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500"
                value={filters.resource}
                onChange={(e) => setFilters({ ...filters, resource: e.target.value })}
                placeholder="Filter by resource..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300">Start Date</label>
              <input
                type="datetime-local"
                className="mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500"
                value={filters.start_time}
                onChange={(e) => setFilters({ ...filters, start_time: e.target.value })}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300">End Date</label>
              <input
                type="datetime-local"
                className="mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500"
                value={filters.end_time}
                onChange={(e) => setFilters({ ...filters, end_time: e.target.value })}
              />
            </div>

            <div className="flex items-end">
              <button
                onClick={handleSearch}
                disabled={loading}
                className="w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 disabled:opacity-50"
              >
                <MagnifyingGlassIcon className="h-4 w-4 mr-2" />
                Search
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Results */}
      <div className="bg-gray-800 shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg leading-6 font-medium text-white">Audit Events</h3>
            <span className="text-sm text-gray-400">{totalCount} events found</span>
          </div>

          {error && (
            <div className="mb-4 bg-red-50 border border-red-200 rounded-md p-4">
              <div className="text-sm text-red-700">{error}</div>
            </div>
          )}

          {loading ? (
            <div className="flex items-center justify-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500"></div>
            </div>
          ) : (
            <div className="space-y-4">
              {events.length === 0 ? (
                <div className="text-center py-12">
                  <ShieldCheckIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-300">No audit events found</h3>
                  <p className="mt-1 text-sm text-gray-400">
                    Try adjusting your search criteria.
                  </p>
                </div>
              ) : (
                events.map((event) => (
                  <div
                    key={event.id}
                    className="border border-gray-600 rounded-lg p-4"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          {event.success ? (
                            <CheckCircleIcon className="h-5 w-5 text-green-400" />
                          ) : (
                            <XCircleIcon className="h-5 w-5 text-red-400" />
                          )}
                          <span className={`font-medium ${getActionColor(event.action)}`}>
                            {event.action}
                          </span>
                          <span className="text-gray-400">on</span>
                          <span className="text-white">{event.resource}</span>
                        </div>
                        
                        <div className="mt-2 text-sm text-gray-400">
                          <p>User: {event.user_id}</p>
                          <p>Resource ID: {event.resource_id}</p>
                          <p>IP: {event.ip_address}</p>
                          {event.error_msg && (
                            <p className="text-red-400">Error: {event.error_msg}</p>
                          )}
                        </div>

                        {Object.keys(event.details).length > 0 && (
                          <div className="mt-3">
                            <details className="group">
                              <summary className="cursor-pointer text-sm text-cyan-400 hover:text-cyan-300">
                                View Details
                              </summary>
                              <pre className="mt-2 text-xs text-gray-400 bg-gray-700 p-2 rounded overflow-x-auto">
                                {JSON.stringify(event.details, null, 2)}
                              </pre>
                            </details>
                          </div>
                        )}
                      </div>
                      
                      <div className="text-right text-sm text-gray-400">
                        <p>{new Date(event.timestamp).toLocaleDateString()}</p>
                        <p>{new Date(event.timestamp).toLocaleTimeString()}</p>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AuditViewer;
