package main

import (
	"fmt"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
)

// Shortlink Service handlers
func getShortlinks(c *fiber.Ctx) error {
	userID := c.Query("user_id", "")
	limit := c.QueryInt("limit", 50)
	offset := c.QueryInt("offset", 0)
	
	shortlinks := []fiber.Map{
		{
			"id":          "link-1",
			"short_code":  "docs",
			"original_url": "https://docs.cainuro.com/orchestrator",
			"title":       "CAINuro Documentation",
			"description": "Official documentation for CAINuro Orchestrator",
			"tags":        []string{"docs", "help", "guide"},
			"created_by":  "admin",
			"active":      true,
			"click_count": 156,
			"created_at":  time.Now().Add(-7 * 24 * time.Hour),
			"updated_at":  time.Now().Add(-1 * 24 * time.Hour),
		},
		{
			"id":          "link-2",
			"short_code":  "status",
			"original_url": "https://status.cainuro.com",
			"title":       "System Status",
			"description": "Real-time system status and uptime",
			"tags":        []string{"status", "monitoring", "uptime"},
			"created_by":  "admin",
			"active":      true,
			"click_count": 89,
			"created_at":  time.Now().Add(-5 * 24 * time.Hour),
			"updated_at":  time.Now().Add(-2 * time.Hour),
		},
	}

	return c.JSON(fiber.Map{
		"shortlinks": shortlinks,
		"total":      len(shortlinks),
		"limit":      limit,
		"offset":     offset,
		"user_id":    userID,
	})
}

func postShortlink(c *fiber.Ctx) error {
	var req map[string]interface{}
	if err := c.BodyParser(&req); err != nil {
		return c.Status(400).JSON(fiber.Map{"error": "Invalid request body"})
	}

	shortCode := req["custom_code"]
	if shortCode == nil || shortCode == "" {
		shortCode = fmt.Sprintf("link%d", time.Now().Unix()%100000)
	}

	shortlink := fiber.Map{
		"id":          fmt.Sprintf("link-%d", time.Now().Unix()),
		"short_code":  shortCode,
		"original_url": req["url"],
		"title":       req["title"],
		"description": req["description"],
		"tags":        req["tags"],
		"created_by":  "user123",
		"active":      true,
		"click_count": 0,
		"created_at":  time.Now(),
		"updated_at":  time.Now(),
	}

	return c.Status(201).JSON(shortlink)
}

func getShortlink(c *fiber.Ctx) error {
	code := c.Params("code")
	
	shortlink := fiber.Map{
		"id":          "link-1",
		"short_code":  code,
		"original_url": "https://docs.cainuro.com/orchestrator",
		"title":       "CAINuro Documentation",
		"description": "Official documentation for CAINuro Orchestrator",
		"tags":        []string{"docs", "help", "guide"},
		"created_by":  "admin",
		"active":      true,
		"click_count": 156,
		"last_clicked": time.Now().Add(-30 * time.Minute),
		"created_at":  time.Now().Add(-7 * 24 * time.Hour),
		"updated_at":  time.Now().Add(-1 * 24 * time.Hour),
	}

	return c.JSON(shortlink)
}

func putShortlink(c *fiber.Ctx) error {
	code := c.Params("code")
	
	var req map[string]interface{}
	if err := c.BodyParser(&req); err != nil {
		return c.Status(400).JSON(fiber.Map{"error": "Invalid request body"})
	}

	shortlink := fiber.Map{
		"id":          "link-1",
		"short_code":  code,
		"original_url": "https://docs.cainuro.com/orchestrator",
		"title":       req["title"],
		"description": req["description"],
		"tags":        req["tags"],
		"active":      req["active"],
		"updated_at":  time.Now(),
	}

	return c.JSON(shortlink)
}

func deleteShortlink(c *fiber.Ctx) error {
	code := c.Params("code")
	
	return c.JSON(fiber.Map{
		"message":    "Shortlink deleted successfully",
		"short_code": code,
		"deleted_at": time.Now(),
	})
}

func getShortlinkAnalytics(c *fiber.Ctx) error {
	code := c.Params("code")
	
	analytics := fiber.Map{
		"short_code":    code,
		"total_clicks":  156,
		"unique_clicks": 134,
		"clicks_by_date": fiber.Map{
			time.Now().Format("2006-01-02"):                    15,
			time.Now().Add(-1 * 24 * time.Hour).Format("2006-01-02"): 23,
			time.Now().Add(-2 * 24 * time.Hour).Format("2006-01-02"): 18,
		},
		"referrers": fiber.Map{
			"direct":                    45,
			"https://google.com":        32,
			"https://github.com":        28,
			"https://docs.cainuro.com":  15,
		},
		"countries": fiber.Map{
			"US": 67,
			"CA": 23,
			"GB": 18,
			"DE": 12,
		},
		"browsers": fiber.Map{
			"Chrome":  89,
			"Firefox": 23,
			"Safari":  18,
			"Edge":    10,
		},
		"platforms": fiber.Map{
			"Windows": 67,
			"macOS":   34,
			"Linux":   23,
			"Mobile":  16,
		},
		"last_updated": time.Now(),
	}

	return c.JSON(analytics)
}

func getShortlinkSearch(c *fiber.Ctx) error {
	query := c.Query("q", "")
	userID := c.Query("user_id", "")
	
	results := []fiber.Map{
		{
			"id":          "link-1",
			"short_code":  "docs",
			"original_url": "https://docs.cainuro.com/orchestrator",
			"title":       "CAINuro Documentation",
			"description": "Official documentation for CAINuro Orchestrator",
			"tags":        []string{"docs", "help", "guide"},
			"score":       95.5,
		},
	}

	return c.JSON(fiber.Map{
		"query":   query,
		"user_id": userID,
		"results": results,
		"total":   len(results),
	})
}

func postShortlinkClick(c *fiber.Ctx) error {
	code := c.Params("code")
	
	var req map[string]interface{}
	if err := c.BodyParser(&req); err != nil {
		return c.Status(400).JSON(fiber.Map{"error": "Invalid request body"})
	}

	return c.JSON(fiber.Map{
		"short_code":    code,
		"redirect_url":  "https://docs.cainuro.com/orchestrator",
		"click_tracked": true,
		"timestamp":     time.Now(),
	})
}

// Project Management Service handlers
func getProjects(c *fiber.Ctx) error {
	limit := c.QueryInt("limit", 50)
	offset := c.QueryInt("offset", 0)
	
	projects := []fiber.Map{
		{
			"id":          "proj-web-platform",
			"name":        "Web Platform",
			"description": "Main web application platform",
			"status":      "active",
			"owner":       "platform-team",
			"team":        []string{"alice", "bob", "charlie"},
			"tags":        []string{"web", "platform", "production"},
			"labels":      fiber.Map{"priority": "high", "team": "platform"},
			"created_at":  time.Now().Add(-30 * 24 * time.Hour),
			"updated_at":  time.Now().Add(-1 * time.Hour),
			"budget": fiber.Map{
				"total":     10000.0,
				"spent":     7500.0,
				"remaining": 2500.0,
				"currency":  "USD",
			},
		},
		{
			"id":          "proj-data-pipeline",
			"name":        "Data Pipeline",
			"description": "ETL and data processing pipeline",
			"status":      "active",
			"owner":       "data-team",
			"team":        []string{"david", "eve"},
			"tags":        []string{"data", "etl", "analytics"},
			"labels":      fiber.Map{"priority": "medium", "team": "data"},
			"created_at":  time.Now().Add(-15 * 24 * time.Hour),
			"updated_at":  time.Now().Add(-30 * time.Minute),
		},
	}

	return c.JSON(fiber.Map{
		"projects": projects,
		"total":    len(projects),
		"limit":    limit,
		"offset":   offset,
	})
}

func postProject(c *fiber.Ctx) error {
	var req map[string]interface{}
	if err := c.BodyParser(&req); err != nil {
		return c.Status(400).JSON(fiber.Map{"error": "Invalid request body"})
	}

	project := fiber.Map{
		"id":          fmt.Sprintf("proj-%d", time.Now().Unix()),
		"name":        req["name"],
		"description": req["description"],
		"status":      "planning",
		"owner":       req["owner"],
		"team":        req["team"],
		"tags":        req["tags"],
		"labels":      req["labels"],
		"resources":   []fiber.Map{},
		"environments": []fiber.Map{},
		"created_at":  time.Now(),
		"updated_at":  time.Now(),
		"budget":      req["budget"],
	}

	return c.Status(201).JSON(project)
}

func getProject(c *fiber.Ctx) error {
	id := c.Params("id")
	
	project := fiber.Map{
		"id":          id,
		"name":        "Web Platform",
		"description": "Main web application platform",
		"status":      "active",
		"owner":       "platform-team",
		"team":        []string{"alice", "bob", "charlie"},
		"tags":        []string{"web", "platform", "production"},
		"labels":      fiber.Map{"priority": "high", "team": "platform"},
		"resources": []fiber.Map{
			{
				"id":       "vpc-12345",
				"type":     "aws.vpc",
				"name":     "platform-vpc",
				"provider": "aws",
				"region":   "us-east-1",
				"status":   "active",
			},
		},
		"environments": []fiber.Map{
			{
				"id":     "env-prod",
				"name":   "Production",
				"type":   "production",
				"status": "active",
				"url":    "https://app.cainuro.com",
			},
		},
		"created_at": time.Now().Add(-30 * 24 * time.Hour),
		"updated_at": time.Now().Add(-1 * time.Hour),
		"budget": fiber.Map{
			"total":     10000.0,
			"spent":     7500.0,
			"remaining": 2500.0,
			"currency":  "USD",
		},
	}

	return c.JSON(project)
}

func putProject(c *fiber.Ctx) error {
	id := c.Params("id")
	
	var req map[string]interface{}
	if err := c.BodyParser(&req); err != nil {
		return c.Status(400).JSON(fiber.Map{"error": "Invalid request body"})
	}

	project := fiber.Map{
		"id":          id,
		"name":        req["name"],
		"description": req["description"],
		"status":      req["status"],
		"team":        req["team"],
		"tags":        req["tags"],
		"labels":      req["labels"],
		"updated_at":  time.Now(),
	}

	return c.JSON(project)
}

func deleteProject(c *fiber.Ctx) error {
	id := c.Params("id")
	
	return c.JSON(fiber.Map{
		"message":    "Project deleted successfully",
		"project_id": id,
		"deleted_at": time.Now(),
	})
}

func postProjectSearch(c *fiber.Ctx) error {
	var req map[string]interface{}
	if err := c.BodyParser(&req); err != nil {
		return c.Status(400).JSON(fiber.Map{"error": "Invalid request body"})
	}

	query := ""
	if req["query"] != nil {
		query = req["query"].(string)
	}

	projects := []fiber.Map{
		{
			"id":          "proj-web-platform",
			"name":        "Web Platform",
			"description": "Main web application platform",
			"status":      "active",
			"owner":       "platform-team",
			"score":       95.5,
		},
	}

	// Simple filtering based on query
	if query != "" {
		var filtered []fiber.Map
		for _, project := range projects {
			name := strings.ToLower(project["name"].(string))
			desc := strings.ToLower(project["description"].(string))
			if strings.Contains(name, strings.ToLower(query)) || 
			   strings.Contains(desc, strings.ToLower(query)) {
				filtered = append(filtered, project)
			}
		}
		projects = filtered
	}

	return c.JSON(fiber.Map{
		"query":    query,
		"projects": projects,
		"total":    len(projects),
	})
}

func postProjectResource(c *fiber.Ctx) error {
	projectID := c.Params("id")
	
	var req map[string]interface{}
	if err := c.BodyParser(&req); err != nil {
		return c.Status(400).JSON(fiber.Map{"error": "Invalid request body"})
	}

	resource := fiber.Map{
		"id":         req["id"],
		"type":       req["type"],
		"name":       req["name"],
		"provider":   req["provider"],
		"region":     req["region"],
		"status":     "active",
		"added_at":   time.Now(),
		"project_id": projectID,
	}

	return c.Status(201).JSON(resource)
}

func deleteProjectResource(c *fiber.Ctx) error {
	projectID := c.Params("id")
	resourceID := c.Params("resourceId")
	
	return c.JSON(fiber.Map{
		"message":     "Resource removed from project",
		"project_id":  projectID,
		"resource_id": resourceID,
		"removed_at":  time.Now(),
	})
}

func postProjectEnvironment(c *fiber.Ctx) error {
	projectID := c.Params("id")
	
	var req map[string]interface{}
	if err := c.BodyParser(&req); err != nil {
		return c.Status(400).JSON(fiber.Map{"error": "Invalid request body"})
	}

	environment := fiber.Map{
		"id":         fmt.Sprintf("env-%d", time.Now().Unix()),
		"name":       req["name"],
		"type":       req["type"],
		"status":     "active",
		"url":        req["url"],
		"variables":  req["variables"],
		"project_id": projectID,
		"created_at": time.Now(),
		"updated_at": time.Now(),
	}

	return c.Status(201).JSON(environment)
}
