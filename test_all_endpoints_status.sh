#!/bin/bash

# COMPREHENSIVE ENDPOINT STATUS CHECK
# Tests ALL endpoints to identify any non-200 responses

BASE_URL="http://localhost:8080"
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo "🔍 COMPREHENSIVE ENDPOINT STATUS CHECK"
echo "Testing ALL endpoints for proper HTTP status codes..."
echo

failed_endpoints=()
total_tests=0
passed_tests=0

# Function to test an endpoint and track failures
test_endpoint_status() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    local expected_codes=$5  # Can be "200" or "200,201" etc.
    
    if [ -z "$expected_codes" ]; then
        expected_codes="200,201"
    fi
    
    total_tests=$((total_tests + 1))
    
    echo -n "Testing $description... "
    
    if [ "$method" = "GET" ]; then
        http_code=$(curl -s -w "%{http_code}" -o /dev/null "$BASE_URL$endpoint")
    else
        http_code=$(curl -s -w "%{http_code}" -o /dev/null -X "$method" -H "Content-Type: application/json" -d "$data" "$BASE_URL$endpoint")
    fi
    
    # Check if the HTTP code is in the expected codes
    if [[ ",$expected_codes," == *",$http_code,"* ]]; then
        echo -e "${GREEN}✅ $http_code${NC}"
        passed_tests=$((passed_tests + 1))
    else
        echo -e "${RED}❌ $http_code (expected: $expected_codes)${NC}"
        failed_endpoints+=("$method $endpoint - HTTP $http_code")
    fi
}

echo -e "${BLUE}=== Core System Endpoints ===${NC}"
test_endpoint_status "GET" "/health" "" "Health check"
test_endpoint_status "GET" "/v1/health" "" "V1 Health check"

echo -e "\n${BLUE}=== Discovery Service ===${NC}"
test_endpoint_status "GET" "/v1/discovery/resources" "" "List resources"
test_endpoint_status "POST" "/v1/discovery/scan" '{"provider":"aws","region":"us-east-1"}' "Trigger scan"
test_endpoint_status "GET" "/v1/discovery/jobs" "" "List jobs"
test_endpoint_status "GET" "/v1/discovery/jobs/job-123" "" "Get job"
test_endpoint_status "GET" "/v1/discovery/stats" "" "Get stats"

echo -e "\n${BLUE}=== Workflow Service ===${NC}"
test_endpoint_status "GET" "/v1/workflows" "" "List workflows"
test_endpoint_status "POST" "/v1/workflows" '{"name":"test","description":"test workflow","steps":[]}' "Create workflow"
test_endpoint_status "GET" "/v1/workflows/wf-123" "" "Get workflow"
test_endpoint_status "POST" "/v1/workflows/wf-123/execute" '{}' "Execute workflow"
test_endpoint_status "GET" "/v1/workflows/wf-123/executions" "" "List executions"
test_endpoint_status "GET" "/v1/workflows/wf-123/executions/exec-123" "" "Get execution"

echo -e "\n${BLUE}=== Autoscaler Service ===${NC}"
test_endpoint_status "GET" "/v1/autoscaler/policies" "" "List policies"
test_endpoint_status "POST" "/v1/autoscaler/policies" '{"name":"test","target_type":"deployment","min_replicas":1,"max_replicas":10}' "Create policy"
test_endpoint_status "GET" "/v1/autoscaler/policies/policy-123" "" "Get policy"
test_endpoint_status "GET" "/v1/autoscaler/events" "" "List events"
test_endpoint_status "GET" "/v1/autoscaler/metrics" "" "Get metrics"

echo -e "\n${BLUE}=== Database Admin Service ===${NC}"
test_endpoint_status "GET" "/v1/database/stats" "" "Get stats"
test_endpoint_status "GET" "/v1/database/tables" "" "List tables"
test_endpoint_status "GET" "/v1/database/tables/resources" "" "Get table"
test_endpoint_status "POST" "/v1/database/query" '{"query":"SELECT COUNT(*) FROM resources"}' "Execute query"
test_endpoint_status "GET" "/v1/database/backup" "" "Get backup info"
test_endpoint_status "POST" "/v1/database/backup" '{"type":"full"}' "Create backup"

echo -e "\n${BLUE}=== Envoy Control Plane ===${NC}"
test_endpoint_status "GET" "/v1/envoy/configs" "" "List configs"
test_endpoint_status "POST" "/v1/envoy/configs" '{"node_id":"test","cluster_name":"test","config":"{}"}' "Create config"
test_endpoint_status "GET" "/v1/envoy/configs/test" "" "Get config"
test_endpoint_status "GET" "/v1/envoy/clusters" "" "List clusters"
test_endpoint_status "GET" "/v1/envoy/endpoints" "" "List endpoints"

echo -e "\n${BLUE}=== Audit Service ===${NC}"
test_endpoint_status "GET" "/v1/audit/events" "" "List events"
test_endpoint_status "POST" "/v1/audit/events" '{"user_id":"test","action":"test.action","resource":"test"}' "Create event"
test_endpoint_status "POST" "/v1/audit/query" '{"user_id":"test","start_time":"2024-01-01T00:00:00Z"}' "Query events"
test_endpoint_status "GET" "/v1/audit/stats" "" "Get stats"

echo -e "\n${BLUE}=== Metrics Service ===${NC}"
test_endpoint_status "GET" "/v1/metrics/system" "" "System metrics"
test_endpoint_status "POST" "/v1/metrics/query" '{"metric_queries":[{"expression":"cpu_usage"}]}' "Query metrics"

echo -e "\n${BLUE}=== Feature Flags Service ===${NC}"
test_endpoint_status "GET" "/v1/featureflags" "" "List flags"
test_endpoint_status "POST" "/v1/featureflags" '{"id":"test","name":"Test","enabled":true}' "Create flag"
test_endpoint_status "GET" "/v1/featureflags/test" "" "Get flag"
test_endpoint_status "PUT" "/v1/featureflags/test" '{"enabled":false}' "Update flag"
test_endpoint_status "DELETE" "/v1/featureflags/test" "" "Delete flag"

echo -e "\n${BLUE}=== Resolver Service ===${NC}"
test_endpoint_status "POST" "/v1/resolver/search" '{"query":"test"}' "Search"
test_endpoint_status "POST" "/v1/resolver/autocomplete" '{"query":"test"}' "Autocomplete"
test_endpoint_status "GET" "/v1/resolver/schemas" "" "Get schemas"
test_endpoint_status "GET" "/v1/resolver/schemas/aws.ec2.instance" "" "Get schema"

echo -e "\n${BLUE}=== Authentication Service ===${NC}"
test_endpoint_status "GET" "/v1/auth/login" "" "Login"
test_endpoint_status "POST" "/v1/auth/callback" '{"code":"test","state":"test"}' "Callback"
test_endpoint_status "GET" "/v1/auth/user" "" "Get user"
test_endpoint_status "POST" "/v1/auth/check" '{"action":"READ","resource":"test"}' "Check auth"
test_endpoint_status "GET" "/v1/auth/config" "" "Get config"
test_endpoint_status "POST" "/v1/auth/logout" '{}' "Logout"

echo -e "\n${BLUE}=== Chaos Engineering Service ===${NC}"
test_endpoint_status "GET" "/v1/chaos/experiments" "" "List experiments"
test_endpoint_status "POST" "/v1/chaos/experiments" '{"name":"test","type":"pod_failure"}' "Create experiment"
test_endpoint_status "GET" "/v1/chaos/experiments/exp-123" "" "Get experiment"
test_endpoint_status "POST" "/v1/chaos/experiments/exp-123/start" '{}' "Start experiment"
test_endpoint_status "POST" "/v1/chaos/experiments/exp-123/stop" '{}' "Stop experiment"
test_endpoint_status "DELETE" "/v1/chaos/experiments/exp-123" "" "Delete experiment"

echo -e "\n${BLUE}=== Bot Service ===${NC}"
test_endpoint_status "POST" "/v1/bot/message" '{"message":"test","user_id":"test","channel":"test"}' "Process message"
test_endpoint_status "GET" "/v1/bot/commands" "" "List commands"
test_endpoint_status "POST" "/v1/bot/commands" '{"name":"test","description":"test"}' "Create command"

echo -e "\n${BLUE}=== Feedback Service ===${NC}"
test_endpoint_status "GET" "/v1/feedback/surveys" "" "List surveys"
test_endpoint_status "POST" "/v1/feedback/surveys" '{"title":"test","rating_type":"stars"}' "Create survey"
test_endpoint_status "GET" "/v1/feedback/surveys/test" "" "Get survey"
test_endpoint_status "POST" "/v1/feedback/submit" '{"survey_id":"test","rating":{"type":"stars","value":5}}' "Submit feedback"
test_endpoint_status "GET" "/v1/feedback/submissions" "" "List submissions"
test_endpoint_status "GET" "/v1/feedback/stats" "" "Get stats"

echo -e "\n${BLUE}=== Kubernetes Service ===${NC}"
test_endpoint_status "GET" "/v1/k8s/clusters" "" "List clusters"
test_endpoint_status "GET" "/v1/k8s/clusters/test" "" "Get cluster"
test_endpoint_status "GET" "/v1/k8s/clusters/test/pods" "" "List pods"
test_endpoint_status "GET" "/v1/k8s/clusters/test/pods/default/test" "" "Get pod"
test_endpoint_status "GET" "/v1/k8s/clusters/test/services" "" "List services"
test_endpoint_status "GET" "/v1/k8s/clusters/test/deployments" "" "List deployments"
test_endpoint_status "POST" "/v1/k8s/clusters/test/deployments/default/test/scale" '{"replicas":3}' "Scale deployment"
test_endpoint_status "POST" "/v1/k8s/clusters/test/deployments/default/test/restart" '{}' "Restart deployment"
test_endpoint_status "GET" "/v1/k8s/clusters/test/nodes" "" "List nodes"
test_endpoint_status "GET" "/v1/k8s/clusters/test/pods/default/test/logs" "" "Get logs"
test_endpoint_status "GET" "/v1/k8s/clusters/test/events" "" "List events"
test_endpoint_status "DELETE" "/v1/k8s/clusters/test/pods/default/test" "" "Delete pod"

echo -e "\n${BLUE}=== Topology Service ===${NC}"
test_endpoint_status "POST" "/v1/topology/search" '{"query":"test"}' "Search topology"
test_endpoint_status "POST" "/v1/topology/graph" '{"root_resource_id":"test"}' "Get graph"
test_endpoint_status "GET" "/v1/topology/resources/test" "" "Get resource"
test_endpoint_status "POST" "/v1/topology/resources" '{"type":"test","name":"test"}' "Create resource"
test_endpoint_status "POST" "/v1/topology/relations" '{"from_id":"test1","to_id":"test2","type":"contains"}' "Create relation"

echo -e "\n${BLUE}=== GitHub Service ===${NC}"
test_endpoint_status "GET" "/v1/github/repositories" "" "List repositories"
test_endpoint_status "GET" "/v1/github/repositories/owner/repo" "" "Get repository"
test_endpoint_status "GET" "/v1/github/repositories/owner/repo/pulls" "" "List PRs"
test_endpoint_status "GET" "/v1/github/repositories/owner/repo/pulls/1" "" "Get PR"
test_endpoint_status "GET" "/v1/github/repositories/owner/repo/issues" "" "List issues"
test_endpoint_status "GET" "/v1/github/search/repositories" "" "Search repos"
test_endpoint_status "GET" "/v1/github/search/code" "" "Search code"
test_endpoint_status "GET" "/v1/github/repositories/owner/repo/workflows" "" "List workflows"
test_endpoint_status "GET" "/v1/github/repositories/owner/repo/workflows/1/runs" "" "List runs"

echo -e "\n${BLUE}=== Shortlink Service ===${NC}"
test_endpoint_status "GET" "/v1/shortlinks" "" "List shortlinks"
test_endpoint_status "POST" "/v1/shortlinks" '{"url":"https://example.com"}' "Create shortlink"
test_endpoint_status "GET" "/v1/shortlinks/test" "" "Get shortlink"
test_endpoint_status "PUT" "/v1/shortlinks/test" '{"title":"updated"}' "Update shortlink"
test_endpoint_status "GET" "/v1/shortlinks/test/analytics" "" "Get analytics"
test_endpoint_status "GET" "/v1/shortlinks/search" "" "Search shortlinks"
test_endpoint_status "POST" "/v1/shortlinks/test/click" '{"ip_address":"127.0.0.1"}' "Track click"
test_endpoint_status "DELETE" "/v1/shortlinks/test" "" "Delete shortlink"

echo -e "\n${BLUE}=== Project Service ===${NC}"
test_endpoint_status "GET" "/v1/projects" "" "List projects"
test_endpoint_status "POST" "/v1/projects" '{"name":"test","owner":"test"}' "Create project"
test_endpoint_status "GET" "/v1/projects/test" "" "Get project"
test_endpoint_status "PUT" "/v1/projects/test" '{"name":"updated"}' "Update project"
test_endpoint_status "POST" "/v1/projects/search" '{"query":"test"}' "Search projects"
test_endpoint_status "POST" "/v1/projects/test/resources" '{"id":"test","type":"test"}' "Add resource"
test_endpoint_status "POST" "/v1/projects/test/environments" '{"name":"test","type":"testing"}' "Create environment"
test_endpoint_status "DELETE" "/v1/projects/test/resources/test" "" "Remove resource"
test_endpoint_status "DELETE" "/v1/projects/test" "" "Delete project"

echo
echo "=== SUMMARY ==="
echo -e "Total tests: ${BLUE}$total_tests${NC}"
echo -e "Passed: ${GREEN}$passed_tests${NC}"
echo -e "Failed: ${RED}$((total_tests - passed_tests))${NC}"

if [ ${#failed_endpoints[@]} -gt 0 ]; then
    echo
    echo -e "${RED}❌ FAILED ENDPOINTS:${NC}"
    for endpoint in "${failed_endpoints[@]}"; do
        echo -e "  ${RED}• $endpoint${NC}"
    done
    echo
    echo -e "${YELLOW}⚠️  These endpoints need to be fixed!${NC}"
    exit 1
else
    echo
    echo -e "${GREEN}🎉 ALL ENDPOINTS WORKING CORRECTLY!${NC}"
    exit 0
fi
