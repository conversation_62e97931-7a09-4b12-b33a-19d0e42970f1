import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import axios from 'axios';

export interface Resource {
  provider: string;
  type: string;
  name: string;
  id: string;
  tags: Record<string, string>;
}

export interface SearchState {
  resources: Resource[];
  loading: boolean;
  error: string | null;
  totalCount: number;
  query: string;
  provider: string;
}

const initialState: SearchState = {
  resources: [],
  loading: false,
  error: null,
  totalCount: 0,
  query: '',
  provider: '',
};

// Async thunks
export const searchResources = createAsyncThunk(
  'search/searchResources',
  async (params: { query?: string; provider?: string; limit?: number }) => {
    const response = await axios.post('/v1/discovery/search', params);
    return response.data;
  }
);

export const getResource = createAsyncThunk(
  'search/getResource',
  async (resourceId: string) => {
    const response = await axios.get(`/v1/discovery/resources/${resourceId}`);
    return response.data.resource;
  }
);

const searchSlice = createSlice({
  name: 'search',
  initialState,
  reducers: {
    setQuery: (state, action: PayloadAction<string>) => {
      state.query = action.payload;
    },
    setProvider: (state, action: PayloadAction<string>) => {
      state.provider = action.payload;
    },
    clearResults: (state) => {
      state.resources = [];
      state.totalCount = 0;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(searchResources.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(searchResources.fulfilled, (state, action) => {
        state.loading = false;
        state.resources = action.payload.resources;
        state.totalCount = action.payload.total_count;
      })
      .addCase(searchResources.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Search failed';
      })
      .addCase(getResource.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getResource.fulfilled, (state, action) => {
        state.loading = false;
        // Update the resource in the list if it exists
        const index = state.resources.findIndex(r => r.id === action.payload.id);
        if (index !== -1) {
          state.resources[index] = action.payload;
        }
      })
      .addCase(getResource.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to get resource';
      });
  },
});

export const { setQuery, setProvider, clearResults } = searchSlice.actions;
export default searchSlice.reducer;
