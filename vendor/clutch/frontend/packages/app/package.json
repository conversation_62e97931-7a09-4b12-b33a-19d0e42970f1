{"name": "@clutch-sh/app", "version": "4.0.0-beta", "private": true, "description": "Clutch Application Components", "license": "Apache-2.0", "author": "<EMAIL>", "scripts": {"build": "yarn register-workflows && react-app-rewired build", "clean": "rm -rf build", "eject": "react-scripts eject", "lint": "yarn register-workflows && yarn run package:lint", "lint:fix": "yarn run lint --fix", "register-workflows": "yarn run workspace:registerWorkflows $INIT_CWD/src", "start": "yarn register-workflows && DANGEROUSLY_DISABLE_HOST_CHECK=true GENERATE_SOURCEMAP=false react-app-rewired start", "test": "yarn register-workflows && yarn run package:test", "test:coverage": "yarn run test --collect-coverage", "test:e2e": "cypress run"}, "dependencies": {"@clutch-sh/core": "workspace:^", "react-app-rewired": "^2.1.8"}, "devDependencies": {"@clutch-sh/tools": "workspace:^", "cypress": "9.7.0", "esbuild-loader": "^3.0.0"}, "peerDependencies": {"react": "^17.0.2", "react-dom": "^17.0.2", "typescript": "^4.2.3"}, "engines": {"node": ">=18 <19", "yarn": "^4.5.0"}, "packageManager": "yarn@4.5.0", "proxy": "http://localhost:8080", "stableVersion": "3.0.0-beta"}