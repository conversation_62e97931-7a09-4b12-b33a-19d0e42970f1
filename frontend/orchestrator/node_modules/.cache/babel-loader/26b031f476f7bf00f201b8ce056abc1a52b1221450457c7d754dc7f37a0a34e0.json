{"ast": null, "code": "import _objectSpread from\"/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState}from'react';import{useDispatch}from'react-redux';import{searchResources}from'../store/slices/searchSlice';import{CloudIcon,KeyIcon,MagnifyingGlassIcon,CheckCircleIcon,ExclamationTriangleIcon}from'@heroicons/react/24/outline';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const DiscoveryWizard=()=>{const dispatch=useDispatch();const[currentStep,setCurrentStep]=useState(1);const[credentials,setCredentials]=useState({provider:'aws'});const[isValidating,setIsValidating]=useState(false);const[validationResult,setValidationResult]=useState(null);const steps=[{id:1,name:'Choose Provider',description:'Select your cloud provider'},{id:2,name:'Add Credentials',description:'Configure authentication'},{id:3,name:'Configure Discovery',description:'Set discovery parameters'},{id:4,name:'Launch Discovery',description:'Start resource discovery'}];const providers=[{id:'aws',name:'Amazon Web Services',icon:'🟠',description:'Discover EC2, S3, RDS, and other AWS resources',fields:['accessKey','secretKey','region']},{id:'gcp',name:'Google Cloud Platform',icon:'🔵',description:'Discover Compute Engine, Cloud Storage, and GCP resources',fields:['projectId']},{id:'azure',name:'Microsoft Azure',icon:'🔷',description:'Discover Virtual Machines, Storage, and Azure resources',fields:['subscriptionId','tenantId']}];const handleProviderSelect=providerId=>{setCredentials({provider:providerId});setCurrentStep(2);};const handleCredentialChange=(field,value)=>{setCredentials(prev=>_objectSpread(_objectSpread({},prev),{},{[field]:value}));};const validateCredentials=async()=>{setIsValidating(true);setValidationResult(null);// Simulate credential validation\nsetTimeout(()=>{setValidationResult('success');setIsValidating(false);},2000);};const launchDiscovery=()=>{// Launch discovery workflow\ndispatch(searchResources({provider:credentials.provider,limit:100}));setCurrentStep(4);};const renderStepContent=()=>{switch(currentStep){case 1:return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(CloudIcon,{className:\"mx-auto h-12 w-12 text-cyan-400\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"mt-2 text-lg font-medium text-white\",children:\"Choose Cloud Provider\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-gray-400\",children:\"Select the cloud provider you want to discover resources from\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 gap-4 sm:grid-cols-3\",children:providers.map(provider=>/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleProviderSelect(provider.id),className:\"relative rounded-lg border border-gray-600 bg-gray-800 p-6 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-cyan-500 transition-colors\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-3xl mb-3\",children:provider.icon}),/*#__PURE__*/_jsx(\"h4\",{className:\"text-lg font-medium text-white\",children:provider.name}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-sm text-gray-400\",children:provider.description})]})},provider.id))})]});case 2:const selectedProvider=providers.find(p=>p.id===credentials.provider);return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(KeyIcon,{className:\"mx-auto h-12 w-12 text-cyan-400\"}),/*#__PURE__*/_jsxs(\"h3\",{className:\"mt-2 text-lg font-medium text-white\",children:[\"Configure \",selectedProvider===null||selectedProvider===void 0?void 0:selectedProvider.name,\" Credentials\"]}),/*#__PURE__*/_jsxs(\"p\",{className:\"mt-1 text-sm text-gray-400\",children:[\"Enter your authentication credentials for \",selectedProvider===null||selectedProvider===void 0?void 0:selectedProvider.name]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[credentials.provider==='aws'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-300\",children:\"Access Key ID\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",className:\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\",value:credentials.accessKey||'',onChange:e=>handleCredentialChange('accessKey',e.target.value),placeholder:\"AKIA...\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-300\",children:\"Secret Access Key\"}),/*#__PURE__*/_jsx(\"input\",{type:\"password\",className:\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\",value:credentials.secretKey||'',onChange:e=>handleCredentialChange('secretKey',e.target.value),placeholder:\"\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-300\",children:\"Region\"}),/*#__PURE__*/_jsxs(\"select\",{className:\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\",value:credentials.region||'',onChange:e=>handleCredentialChange('region',e.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select a region\"}),/*#__PURE__*/_jsx(\"option\",{value:\"us-east-1\",children:\"US East (N. Virginia)\"}),/*#__PURE__*/_jsx(\"option\",{value:\"us-west-2\",children:\"US West (Oregon)\"}),/*#__PURE__*/_jsx(\"option\",{value:\"eu-west-1\",children:\"Europe (Ireland)\"}),/*#__PURE__*/_jsx(\"option\",{value:\"ap-southeast-1\",children:\"Asia Pacific (Singapore)\"})]})]})]}),credentials.provider==='gcp'&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-300\",children:\"Project ID\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",className:\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\",value:credentials.projectId||'',onChange:e=>handleCredentialChange('projectId',e.target.value),placeholder:\"my-gcp-project\"})]}),credentials.provider==='azure'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-300\",children:\"Subscription ID\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",className:\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\",value:credentials.subscriptionId||'',onChange:e=>handleCredentialChange('subscriptionId',e.target.value),placeholder:\"xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-300\",children:\"Tenant ID\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",className:\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\",value:credentials.tenantId||'',onChange:e=>handleCredentialChange('tenantId',e.target.value),placeholder:\"xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setCurrentStep(1),className:\"px-4 py-2 border border-gray-600 rounded-md text-gray-300 hover:bg-gray-700\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:validateCredentials,disabled:isValidating,className:\"px-4 py-2 bg-cyan-600 text-white rounded-md hover:bg-cyan-700 disabled:opacity-50\",children:isValidating?'Validating...':'Validate & Continue'})]}),validationResult==='success'&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 text-green-400\",children:[/*#__PURE__*/_jsx(CheckCircleIcon,{className:\"h-5 w-5\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Credentials validated successfully!\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setCurrentStep(3),className:\"ml-4 px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700\",children:\"Continue\"})]}),validationResult==='error'&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 text-red-400\",children:[/*#__PURE__*/_jsx(ExclamationTriangleIcon,{className:\"h-5 w-5\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Credential validation failed. Please check your credentials.\"})]})]});case 3:return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(MagnifyingGlassIcon,{className:\"mx-auto h-12 w-12 text-cyan-400\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"mt-2 text-lg font-medium text-white\",children:\"Configure Discovery\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-gray-400\",children:\"Set parameters for resource discovery\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-300\",children:\"Discovery Scope\"}),/*#__PURE__*/_jsxs(\"select\",{className:\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\",children:[/*#__PURE__*/_jsx(\"option\",{children:\"All Resources\"}),/*#__PURE__*/_jsx(\"option\",{children:\"Compute Only\"}),/*#__PURE__*/_jsx(\"option\",{children:\"Storage Only\"}),/*#__PURE__*/_jsx(\"option\",{children:\"Network Only\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-300\",children:\"Resource Tags Filter\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",className:\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\",placeholder:\"Environment=production,Team=platform\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",className:\"h-4 w-4 text-cyan-600 focus:ring-cyan-500 border-gray-600 rounded bg-gray-700\",defaultChecked:true}),/*#__PURE__*/_jsx(\"label\",{className:\"ml-2 block text-sm text-gray-300\",children:\"Cache results for faster subsequent searches\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setCurrentStep(2),className:\"px-4 py-2 border border-gray-600 rounded-md text-gray-300 hover:bg-gray-700\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:launchDiscovery,className:\"px-4 py-2 bg-cyan-600 text-white rounded-md hover:bg-cyan-700\",children:\"Launch Discovery\"})]})]});case 4:return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(CheckCircleIcon,{className:\"mx-auto h-12 w-12 text-green-400\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"mt-2 text-lg font-medium text-white\",children:\"Discovery Launched!\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-gray-400\",children:\"Resource discovery is now running in the background\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gray-700 rounded-lg p-4\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"text-md font-medium text-white mb-2\",children:\"Discovery Status\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-300\",children:\"Provider:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-white\",children:credentials.provider.toUpperCase()})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-300\",children:\"Status:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-green-400\",children:\"Running\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-300\",children:\"Resources Found:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-white\",children:\"42\"})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex justify-center\",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>window.location.href='/search',className:\"px-6 py-2 bg-cyan-600 text-white rounded-md hover:bg-cyan-700\",children:\"View Results\"})})]});default:return null;}};return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-white\",children:\"Discovery Wizard\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-gray-400\",children:\"Multi-step wizard to add credentials and launch discovery workflows\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-gray-800 shadow rounded-lg\",children:/*#__PURE__*/_jsx(\"div\",{className:\"px-4 py-5 sm:p-6\",children:/*#__PURE__*/_jsx(\"nav\",{\"aria-label\":\"Progress\",children:/*#__PURE__*/_jsx(\"ol\",{className:\"flex items-center\",children:steps.map((step,stepIdx)=>/*#__PURE__*/_jsxs(\"li\",{className:\"\".concat(stepIdx!==steps.length-1?'pr-8 sm:pr-20':'',\" relative\"),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"relative flex h-8 w-8 items-center justify-center rounded-full \".concat(step.id<=currentStep?'bg-cyan-600 text-white':'border-2 border-gray-600 bg-gray-800 text-gray-400'),children:step.id<currentStep?/*#__PURE__*/_jsx(CheckCircleIcon,{className:\"h-5 w-5\"}):/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium\",children:step.id})}),/*#__PURE__*/_jsxs(\"div\",{className:\"ml-4 min-w-0 flex-1\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium \".concat(step.id<=currentStep?'text-white':'text-gray-400'),children:step.name}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-400\",children:step.description})]})]}),stepIdx!==steps.length-1&&/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-4 left-4 -ml-px mt-0.5 h-full w-0.5 bg-gray-600\"})]},step.id))})})})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-gray-800 shadow rounded-lg\",children:/*#__PURE__*/_jsx(\"div\",{className:\"px-4 py-5 sm:p-6\",children:renderStepContent()})})]});};export default DiscoveryWizard;", "map": {"version": 3, "names": ["React", "useState", "useDispatch", "searchResources", "CloudIcon", "KeyIcon", "MagnifyingGlassIcon", "CheckCircleIcon", "ExclamationTriangleIcon", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "DiscoveryWizard", "dispatch", "currentStep", "setCurrentStep", "credentials", "setCredentials", "provider", "isValidating", "setIsValidating", "validationResult", "setValidationResult", "steps", "id", "name", "description", "providers", "icon", "fields", "handleProviderSelect", "providerId", "handleCredentialChange", "field", "value", "prev", "_objectSpread", "validateCredentials", "setTimeout", "launchDiscovery", "limit", "renderStepContent", "className", "children", "map", "onClick", "<PERSON><PERSON><PERSON><PERSON>", "find", "p", "type", "accessKey", "onChange", "e", "target", "placeholder", "secret<PERSON>ey", "region", "projectId", "subscriptionId", "tenantId", "disabled", "defaultChecked", "toUpperCase", "window", "location", "href", "step", "stepIdx", "concat", "length"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/DiscoveryWizard.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useDispatch } from 'react-redux';\nimport { AppDispatch } from '../store/store';\nimport { searchResources } from '../store/slices/searchSlice';\nimport {\n  CloudIcon,\n  KeyIcon,\n  MagnifyingGlassIcon,\n  CheckCircleIcon,\n  ExclamationTriangleIcon,\n} from '@heroicons/react/24/outline';\n\ninterface CredentialForm {\n  provider: 'aws' | 'gcp' | 'azure';\n  accessKey?: string;\n  secretKey?: string;\n  region?: string;\n  projectId?: string;\n  subscriptionId?: string;\n  tenantId?: string;\n}\n\nconst DiscoveryWizard: React.FC = () => {\n  const dispatch = useDispatch<AppDispatch>();\n  const [currentStep, setCurrentStep] = useState(1);\n  const [credentials, setCredentials] = useState<CredentialForm>({\n    provider: 'aws',\n  });\n  const [isValidating, setIsValidating] = useState(false);\n  const [validationResult, setValidationResult] = useState<'success' | 'error' | null>(null);\n\n  const steps = [\n    { id: 1, name: 'Choose Provider', description: 'Select your cloud provider' },\n    { id: 2, name: 'Add Credentials', description: 'Configure authentication' },\n    { id: 3, name: 'Configure Discovery', description: 'Set discovery parameters' },\n    { id: 4, name: 'Launch Discovery', description: 'Start resource discovery' },\n  ];\n\n  const providers = [\n    {\n      id: 'aws',\n      name: 'Amazon Web Services',\n      icon: '🟠',\n      description: 'Discover EC2, S3, RDS, and other AWS resources',\n      fields: ['accessKey', 'secretKey', 'region'],\n    },\n    {\n      id: 'gcp',\n      name: 'Google Cloud Platform',\n      icon: '🔵',\n      description: 'Discover Compute Engine, Cloud Storage, and GCP resources',\n      fields: ['projectId'],\n    },\n    {\n      id: 'azure',\n      name: 'Microsoft Azure',\n      icon: '🔷',\n      description: 'Discover Virtual Machines, Storage, and Azure resources',\n      fields: ['subscriptionId', 'tenantId'],\n    },\n  ];\n\n  const handleProviderSelect = (providerId: 'aws' | 'gcp' | 'azure') => {\n    setCredentials({ provider: providerId });\n    setCurrentStep(2);\n  };\n\n  const handleCredentialChange = (field: string, value: string) => {\n    setCredentials(prev => ({ ...prev, [field]: value }));\n  };\n\n  const validateCredentials = async () => {\n    setIsValidating(true);\n    setValidationResult(null);\n\n    // Simulate credential validation\n    setTimeout(() => {\n      setValidationResult('success');\n      setIsValidating(false);\n    }, 2000);\n  };\n\n  const launchDiscovery = () => {\n    // Launch discovery workflow\n    dispatch(searchResources({\n      provider: credentials.provider,\n      limit: 100,\n    }));\n    setCurrentStep(4);\n  };\n\n  const renderStepContent = () => {\n    switch (currentStep) {\n      case 1:\n        return (\n          <div className=\"space-y-6\">\n            <div className=\"text-center\">\n              <CloudIcon className=\"mx-auto h-12 w-12 text-cyan-400\" />\n              <h3 className=\"mt-2 text-lg font-medium text-white\">Choose Cloud Provider</h3>\n              <p className=\"mt-1 text-sm text-gray-400\">\n                Select the cloud provider you want to discover resources from\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-3\">\n              {providers.map((provider) => (\n                <button\n                  key={provider.id}\n                  onClick={() => handleProviderSelect(provider.id as any)}\n                  className=\"relative rounded-lg border border-gray-600 bg-gray-800 p-6 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-cyan-500 transition-colors\"\n                >\n                  <div className=\"text-center\">\n                    <div className=\"text-3xl mb-3\">{provider.icon}</div>\n                    <h4 className=\"text-lg font-medium text-white\">{provider.name}</h4>\n                    <p className=\"mt-2 text-sm text-gray-400\">{provider.description}</p>\n                  </div>\n                </button>\n              ))}\n            </div>\n          </div>\n        );\n\n      case 2:\n        const selectedProvider = providers.find(p => p.id === credentials.provider);\n        return (\n          <div className=\"space-y-6\">\n            <div className=\"text-center\">\n              <KeyIcon className=\"mx-auto h-12 w-12 text-cyan-400\" />\n              <h3 className=\"mt-2 text-lg font-medium text-white\">\n                Configure {selectedProvider?.name} Credentials\n              </h3>\n              <p className=\"mt-1 text-sm text-gray-400\">\n                Enter your authentication credentials for {selectedProvider?.name}\n              </p>\n            </div>\n\n            <div className=\"space-y-4\">\n              {credentials.provider === 'aws' && (\n                <>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-300\">Access Key ID</label>\n                    <input\n                      type=\"text\"\n                      className=\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\"\n                      value={credentials.accessKey || ''}\n                      onChange={(e) => handleCredentialChange('accessKey', e.target.value)}\n                      placeholder=\"AKIA...\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-300\">Secret Access Key</label>\n                    <input\n                      type=\"password\"\n                      className=\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\"\n                      value={credentials.secretKey || ''}\n                      onChange={(e) => handleCredentialChange('secretKey', e.target.value)}\n                      placeholder=\"••••••••••••••••••••••••••••••••••••••••\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-300\">Region</label>\n                    <select\n                      className=\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\"\n                      value={credentials.region || ''}\n                      onChange={(e) => handleCredentialChange('region', e.target.value)}\n                    >\n                      <option value=\"\">Select a region</option>\n                      <option value=\"us-east-1\">US East (N. Virginia)</option>\n                      <option value=\"us-west-2\">US West (Oregon)</option>\n                      <option value=\"eu-west-1\">Europe (Ireland)</option>\n                      <option value=\"ap-southeast-1\">Asia Pacific (Singapore)</option>\n                    </select>\n                  </div>\n                </>\n              )}\n\n              {credentials.provider === 'gcp' && (\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-300\">Project ID</label>\n                  <input\n                    type=\"text\"\n                    className=\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\"\n                    value={credentials.projectId || ''}\n                    onChange={(e) => handleCredentialChange('projectId', e.target.value)}\n                    placeholder=\"my-gcp-project\"\n                  />\n                </div>\n              )}\n\n              {credentials.provider === 'azure' && (\n                <>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-300\">Subscription ID</label>\n                    <input\n                      type=\"text\"\n                      className=\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\"\n                      value={credentials.subscriptionId || ''}\n                      onChange={(e) => handleCredentialChange('subscriptionId', e.target.value)}\n                      placeholder=\"xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-300\">Tenant ID</label>\n                    <input\n                      type=\"text\"\n                      className=\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\"\n                      value={credentials.tenantId || ''}\n                      onChange={(e) => handleCredentialChange('tenantId', e.target.value)}\n                      placeholder=\"xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx\"\n                    />\n                  </div>\n                </>\n              )}\n            </div>\n\n            <div className=\"flex justify-between\">\n              <button\n                onClick={() => setCurrentStep(1)}\n                className=\"px-4 py-2 border border-gray-600 rounded-md text-gray-300 hover:bg-gray-700\"\n              >\n                Back\n              </button>\n              <button\n                onClick={validateCredentials}\n                disabled={isValidating}\n                className=\"px-4 py-2 bg-cyan-600 text-white rounded-md hover:bg-cyan-700 disabled:opacity-50\"\n              >\n                {isValidating ? 'Validating...' : 'Validate & Continue'}\n              </button>\n            </div>\n\n            {validationResult === 'success' && (\n              <div className=\"flex items-center space-x-2 text-green-400\">\n                <CheckCircleIcon className=\"h-5 w-5\" />\n                <span>Credentials validated successfully!</span>\n                <button\n                  onClick={() => setCurrentStep(3)}\n                  className=\"ml-4 px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700\"\n                >\n                  Continue\n                </button>\n              </div>\n            )}\n\n            {validationResult === 'error' && (\n              <div className=\"flex items-center space-x-2 text-red-400\">\n                <ExclamationTriangleIcon className=\"h-5 w-5\" />\n                <span>Credential validation failed. Please check your credentials.</span>\n              </div>\n            )}\n          </div>\n        );\n\n      case 3:\n        return (\n          <div className=\"space-y-6\">\n            <div className=\"text-center\">\n              <MagnifyingGlassIcon className=\"mx-auto h-12 w-12 text-cyan-400\" />\n              <h3 className=\"mt-2 text-lg font-medium text-white\">Configure Discovery</h3>\n              <p className=\"mt-1 text-sm text-gray-400\">\n                Set parameters for resource discovery\n              </p>\n            </div>\n\n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300\">Discovery Scope</label>\n                <select className=\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\">\n                  <option>All Resources</option>\n                  <option>Compute Only</option>\n                  <option>Storage Only</option>\n                  <option>Network Only</option>\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300\">Resource Tags Filter</label>\n                <input\n                  type=\"text\"\n                  className=\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\"\n                  placeholder=\"Environment=production,Team=platform\"\n                />\n              </div>\n\n              <div className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  className=\"h-4 w-4 text-cyan-600 focus:ring-cyan-500 border-gray-600 rounded bg-gray-700\"\n                  defaultChecked\n                />\n                <label className=\"ml-2 block text-sm text-gray-300\">\n                  Cache results for faster subsequent searches\n                </label>\n              </div>\n            </div>\n\n            <div className=\"flex justify-between\">\n              <button\n                onClick={() => setCurrentStep(2)}\n                className=\"px-4 py-2 border border-gray-600 rounded-md text-gray-300 hover:bg-gray-700\"\n              >\n                Back\n              </button>\n              <button\n                onClick={launchDiscovery}\n                className=\"px-4 py-2 bg-cyan-600 text-white rounded-md hover:bg-cyan-700\"\n              >\n                Launch Discovery\n              </button>\n            </div>\n          </div>\n        );\n\n      case 4:\n        return (\n          <div className=\"space-y-6\">\n            <div className=\"text-center\">\n              <CheckCircleIcon className=\"mx-auto h-12 w-12 text-green-400\" />\n              <h3 className=\"mt-2 text-lg font-medium text-white\">Discovery Launched!</h3>\n              <p className=\"mt-1 text-sm text-gray-400\">\n                Resource discovery is now running in the background\n              </p>\n            </div>\n\n            <div className=\"bg-gray-700 rounded-lg p-4\">\n              <h4 className=\"text-md font-medium text-white mb-2\">Discovery Status</h4>\n              <div className=\"space-y-2\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-300\">Provider:</span>\n                  <span className=\"text-white\">{credentials.provider.toUpperCase()}</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-300\">Status:</span>\n                  <span className=\"text-green-400\">Running</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-300\">Resources Found:</span>\n                  <span className=\"text-white\">42</span>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex justify-center\">\n              <button\n                onClick={() => window.location.href = '/search'}\n                className=\"px-6 py-2 bg-cyan-600 text-white rounded-md hover:bg-cyan-700\"\n              >\n                View Results\n              </button>\n            </div>\n          </div>\n        );\n\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div>\n        <h1 className=\"text-2xl font-bold text-white\">Discovery Wizard</h1>\n        <p className=\"mt-1 text-sm text-gray-400\">\n          Multi-step wizard to add credentials and launch discovery workflows\n        </p>\n      </div>\n\n      {/* Progress Steps */}\n      <div className=\"bg-gray-800 shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <nav aria-label=\"Progress\">\n            <ol className=\"flex items-center\">\n              {steps.map((step, stepIdx) => (\n                <li key={step.id} className={`${stepIdx !== steps.length - 1 ? 'pr-8 sm:pr-20' : ''} relative`}>\n                  <div className=\"flex items-center\">\n                    <div\n                      className={`relative flex h-8 w-8 items-center justify-center rounded-full ${\n                        step.id <= currentStep\n                          ? 'bg-cyan-600 text-white'\n                          : 'border-2 border-gray-600 bg-gray-800 text-gray-400'\n                      }`}\n                    >\n                      {step.id < currentStep ? (\n                        <CheckCircleIcon className=\"h-5 w-5\" />\n                      ) : (\n                        <span className=\"text-sm font-medium\">{step.id}</span>\n                      )}\n                    </div>\n                    <div className=\"ml-4 min-w-0 flex-1\">\n                      <p className={`text-sm font-medium ${step.id <= currentStep ? 'text-white' : 'text-gray-400'}`}>\n                        {step.name}\n                      </p>\n                      <p className=\"text-sm text-gray-400\">{step.description}</p>\n                    </div>\n                  </div>\n                  {stepIdx !== steps.length - 1 && (\n                    <div className=\"absolute top-4 left-4 -ml-px mt-0.5 h-full w-0.5 bg-gray-600\" />\n                  )}\n                </li>\n              ))}\n            </ol>\n          </nav>\n        </div>\n      </div>\n\n      {/* Step Content */}\n      <div className=\"bg-gray-800 shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          {renderStepContent()}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default DiscoveryWizard;\n"], "mappings": "yJAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,WAAW,KAAQ,aAAa,CAEzC,OAASC,eAAe,KAAQ,6BAA6B,CAC7D,OACEC,SAAS,CACTC,OAAO,CACPC,mBAAmB,CACnBC,eAAe,CACfC,uBAAuB,KAClB,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAYrC,KAAM,CAAAC,eAAyB,CAAGA,CAAA,GAAM,CACtC,KAAM,CAAAC,QAAQ,CAAGd,WAAW,CAAc,CAAC,CAC3C,KAAM,CAACe,WAAW,CAAEC,cAAc,CAAC,CAAGjB,QAAQ,CAAC,CAAC,CAAC,CACjD,KAAM,CAACkB,WAAW,CAAEC,cAAc,CAAC,CAAGnB,QAAQ,CAAiB,CAC7DoB,QAAQ,CAAE,KACZ,CAAC,CAAC,CACF,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAGtB,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACuB,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGxB,QAAQ,CAA6B,IAAI,CAAC,CAE1F,KAAM,CAAAyB,KAAK,CAAG,CACZ,CAAEC,EAAE,CAAE,CAAC,CAAEC,IAAI,CAAE,iBAAiB,CAAEC,WAAW,CAAE,4BAA6B,CAAC,CAC7E,CAAEF,EAAE,CAAE,CAAC,CAAEC,IAAI,CAAE,iBAAiB,CAAEC,WAAW,CAAE,0BAA2B,CAAC,CAC3E,CAAEF,EAAE,CAAE,CAAC,CAAEC,IAAI,CAAE,qBAAqB,CAAEC,WAAW,CAAE,0BAA2B,CAAC,CAC/E,CAAEF,EAAE,CAAE,CAAC,CAAEC,IAAI,CAAE,kBAAkB,CAAEC,WAAW,CAAE,0BAA2B,CAAC,CAC7E,CAED,KAAM,CAAAC,SAAS,CAAG,CAChB,CACEH,EAAE,CAAE,KAAK,CACTC,IAAI,CAAE,qBAAqB,CAC3BG,IAAI,CAAE,IAAI,CACVF,WAAW,CAAE,gDAAgD,CAC7DG,MAAM,CAAE,CAAC,WAAW,CAAE,WAAW,CAAE,QAAQ,CAC7C,CAAC,CACD,CACEL,EAAE,CAAE,KAAK,CACTC,IAAI,CAAE,uBAAuB,CAC7BG,IAAI,CAAE,IAAI,CACVF,WAAW,CAAE,2DAA2D,CACxEG,MAAM,CAAE,CAAC,WAAW,CACtB,CAAC,CACD,CACEL,EAAE,CAAE,OAAO,CACXC,IAAI,CAAE,iBAAiB,CACvBG,IAAI,CAAE,IAAI,CACVF,WAAW,CAAE,yDAAyD,CACtEG,MAAM,CAAE,CAAC,gBAAgB,CAAE,UAAU,CACvC,CAAC,CACF,CAED,KAAM,CAAAC,oBAAoB,CAAIC,UAAmC,EAAK,CACpEd,cAAc,CAAC,CAAEC,QAAQ,CAAEa,UAAW,CAAC,CAAC,CACxChB,cAAc,CAAC,CAAC,CAAC,CACnB,CAAC,CAED,KAAM,CAAAiB,sBAAsB,CAAGA,CAACC,KAAa,CAAEC,KAAa,GAAK,CAC/DjB,cAAc,CAACkB,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAACF,KAAK,EAAGC,KAAK,EAAG,CAAC,CACvD,CAAC,CAED,KAAM,CAAAG,mBAAmB,CAAG,KAAAA,CAAA,GAAY,CACtCjB,eAAe,CAAC,IAAI,CAAC,CACrBE,mBAAmB,CAAC,IAAI,CAAC,CAEzB;AACAgB,UAAU,CAAC,IAAM,CACfhB,mBAAmB,CAAC,SAAS,CAAC,CAC9BF,eAAe,CAAC,KAAK,CAAC,CACxB,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,CAED,KAAM,CAAAmB,eAAe,CAAGA,CAAA,GAAM,CAC5B;AACA1B,QAAQ,CAACb,eAAe,CAAC,CACvBkB,QAAQ,CAAEF,WAAW,CAACE,QAAQ,CAC9BsB,KAAK,CAAE,GACT,CAAC,CAAC,CAAC,CACHzB,cAAc,CAAC,CAAC,CAAC,CACnB,CAAC,CAED,KAAM,CAAA0B,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,OAAQ3B,WAAW,EACjB,IAAK,EAAC,CACJ,mBACEL,KAAA,QAAKiC,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBlC,KAAA,QAAKiC,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BpC,IAAA,CAACN,SAAS,EAACyC,SAAS,CAAC,iCAAiC,CAAE,CAAC,cACzDnC,IAAA,OAAImC,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,uBAAqB,CAAI,CAAC,cAC9EpC,IAAA,MAAGmC,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,+DAE1C,CAAG,CAAC,EACD,CAAC,cAENpC,IAAA,QAAKmC,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CACnDhB,SAAS,CAACiB,GAAG,CAAE1B,QAAQ,eACtBX,IAAA,WAEEsC,OAAO,CAAEA,CAAA,GAAMf,oBAAoB,CAACZ,QAAQ,CAACM,EAAS,CAAE,CACxDkB,SAAS,CAAC,oJAAoJ,CAAAC,QAAA,cAE9JlC,KAAA,QAAKiC,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BpC,IAAA,QAAKmC,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEzB,QAAQ,CAACU,IAAI,CAAM,CAAC,cACpDrB,IAAA,OAAImC,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAEzB,QAAQ,CAACO,IAAI,CAAK,CAAC,cACnElB,IAAA,MAAGmC,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAEzB,QAAQ,CAACQ,WAAW,CAAI,CAAC,EACjE,CAAC,EARDR,QAAQ,CAACM,EASR,CACT,CAAC,CACC,CAAC,EACH,CAAC,CAGV,IAAK,EAAC,CACJ,KAAM,CAAAsB,gBAAgB,CAAGnB,SAAS,CAACoB,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACxB,EAAE,GAAKR,WAAW,CAACE,QAAQ,CAAC,CAC3E,mBACET,KAAA,QAAKiC,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBlC,KAAA,QAAKiC,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BpC,IAAA,CAACL,OAAO,EAACwC,SAAS,CAAC,iCAAiC,CAAE,CAAC,cACvDjC,KAAA,OAAIiC,SAAS,CAAC,qCAAqC,CAAAC,QAAA,EAAC,YACxC,CAACG,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAErB,IAAI,CAAC,cACpC,EAAI,CAAC,cACLhB,KAAA,MAAGiC,SAAS,CAAC,4BAA4B,CAAAC,QAAA,EAAC,4CACE,CAACG,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAErB,IAAI,EAChE,CAAC,EACD,CAAC,cAENhB,KAAA,QAAKiC,SAAS,CAAC,WAAW,CAAAC,QAAA,EACvB3B,WAAW,CAACE,QAAQ,GAAK,KAAK,eAC7BT,KAAA,CAAAE,SAAA,EAAAgC,QAAA,eACElC,KAAA,QAAAkC,QAAA,eACEpC,IAAA,UAAOmC,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,eAAa,CAAO,CAAC,cAChFpC,IAAA,UACE0C,IAAI,CAAC,MAAM,CACXP,SAAS,CAAC,0IAA0I,CACpJR,KAAK,CAAElB,WAAW,CAACkC,SAAS,EAAI,EAAG,CACnCC,QAAQ,CAAGC,CAAC,EAAKpB,sBAAsB,CAAC,WAAW,CAAEoB,CAAC,CAACC,MAAM,CAACnB,KAAK,CAAE,CACrEoB,WAAW,CAAC,SAAS,CACtB,CAAC,EACC,CAAC,cACN7C,KAAA,QAAAkC,QAAA,eACEpC,IAAA,UAAOmC,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,mBAAiB,CAAO,CAAC,cACpFpC,IAAA,UACE0C,IAAI,CAAC,UAAU,CACfP,SAAS,CAAC,0IAA0I,CACpJR,KAAK,CAAElB,WAAW,CAACuC,SAAS,EAAI,EAAG,CACnCJ,QAAQ,CAAGC,CAAC,EAAKpB,sBAAsB,CAAC,WAAW,CAAEoB,CAAC,CAACC,MAAM,CAACnB,KAAK,CAAE,CACrEoB,WAAW,CAAC,kPAA0C,CACvD,CAAC,EACC,CAAC,cACN7C,KAAA,QAAAkC,QAAA,eACEpC,IAAA,UAAOmC,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,QAAM,CAAO,CAAC,cACzElC,KAAA,WACEiC,SAAS,CAAC,0IAA0I,CACpJR,KAAK,CAAElB,WAAW,CAACwC,MAAM,EAAI,EAAG,CAChCL,QAAQ,CAAGC,CAAC,EAAKpB,sBAAsB,CAAC,QAAQ,CAAEoB,CAAC,CAACC,MAAM,CAACnB,KAAK,CAAE,CAAAS,QAAA,eAElEpC,IAAA,WAAQ2B,KAAK,CAAC,EAAE,CAAAS,QAAA,CAAC,iBAAe,CAAQ,CAAC,cACzCpC,IAAA,WAAQ2B,KAAK,CAAC,WAAW,CAAAS,QAAA,CAAC,uBAAqB,CAAQ,CAAC,cACxDpC,IAAA,WAAQ2B,KAAK,CAAC,WAAW,CAAAS,QAAA,CAAC,kBAAgB,CAAQ,CAAC,cACnDpC,IAAA,WAAQ2B,KAAK,CAAC,WAAW,CAAAS,QAAA,CAAC,kBAAgB,CAAQ,CAAC,cACnDpC,IAAA,WAAQ2B,KAAK,CAAC,gBAAgB,CAAAS,QAAA,CAAC,0BAAwB,CAAQ,CAAC,EAC1D,CAAC,EACN,CAAC,EACN,CACH,CAEA3B,WAAW,CAACE,QAAQ,GAAK,KAAK,eAC7BT,KAAA,QAAAkC,QAAA,eACEpC,IAAA,UAAOmC,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,YAAU,CAAO,CAAC,cAC7EpC,IAAA,UACE0C,IAAI,CAAC,MAAM,CACXP,SAAS,CAAC,0IAA0I,CACpJR,KAAK,CAAElB,WAAW,CAACyC,SAAS,EAAI,EAAG,CACnCN,QAAQ,CAAGC,CAAC,EAAKpB,sBAAsB,CAAC,WAAW,CAAEoB,CAAC,CAACC,MAAM,CAACnB,KAAK,CAAE,CACrEoB,WAAW,CAAC,gBAAgB,CAC7B,CAAC,EACC,CACN,CAEAtC,WAAW,CAACE,QAAQ,GAAK,OAAO,eAC/BT,KAAA,CAAAE,SAAA,EAAAgC,QAAA,eACElC,KAAA,QAAAkC,QAAA,eACEpC,IAAA,UAAOmC,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,iBAAe,CAAO,CAAC,cAClFpC,IAAA,UACE0C,IAAI,CAAC,MAAM,CACXP,SAAS,CAAC,0IAA0I,CACpJR,KAAK,CAAElB,WAAW,CAAC0C,cAAc,EAAI,EAAG,CACxCP,QAAQ,CAAGC,CAAC,EAAKpB,sBAAsB,CAAC,gBAAgB,CAAEoB,CAAC,CAACC,MAAM,CAACnB,KAAK,CAAE,CAC1EoB,WAAW,CAAC,sCAAsC,CACnD,CAAC,EACC,CAAC,cACN7C,KAAA,QAAAkC,QAAA,eACEpC,IAAA,UAAOmC,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,WAAS,CAAO,CAAC,cAC5EpC,IAAA,UACE0C,IAAI,CAAC,MAAM,CACXP,SAAS,CAAC,0IAA0I,CACpJR,KAAK,CAAElB,WAAW,CAAC2C,QAAQ,EAAI,EAAG,CAClCR,QAAQ,CAAGC,CAAC,EAAKpB,sBAAsB,CAAC,UAAU,CAAEoB,CAAC,CAACC,MAAM,CAACnB,KAAK,CAAE,CACpEoB,WAAW,CAAC,sCAAsC,CACnD,CAAC,EACC,CAAC,EACN,CACH,EACE,CAAC,cAEN7C,KAAA,QAAKiC,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCpC,IAAA,WACEsC,OAAO,CAAEA,CAAA,GAAM9B,cAAc,CAAC,CAAC,CAAE,CACjC2B,SAAS,CAAC,6EAA6E,CAAAC,QAAA,CACxF,MAED,CAAQ,CAAC,cACTpC,IAAA,WACEsC,OAAO,CAAER,mBAAoB,CAC7BuB,QAAQ,CAAEzC,YAAa,CACvBuB,SAAS,CAAC,mFAAmF,CAAAC,QAAA,CAE5FxB,YAAY,CAAG,eAAe,CAAG,qBAAqB,CACjD,CAAC,EACN,CAAC,CAELE,gBAAgB,GAAK,SAAS,eAC7BZ,KAAA,QAAKiC,SAAS,CAAC,4CAA4C,CAAAC,QAAA,eACzDpC,IAAA,CAACH,eAAe,EAACsC,SAAS,CAAC,SAAS,CAAE,CAAC,cACvCnC,IAAA,SAAAoC,QAAA,CAAM,qCAAmC,CAAM,CAAC,cAChDpC,IAAA,WACEsC,OAAO,CAAEA,CAAA,GAAM9B,cAAc,CAAC,CAAC,CAAE,CACjC2B,SAAS,CAAC,2EAA2E,CAAAC,QAAA,CACtF,UAED,CAAQ,CAAC,EACN,CACN,CAEAtB,gBAAgB,GAAK,OAAO,eAC3BZ,KAAA,QAAKiC,SAAS,CAAC,0CAA0C,CAAAC,QAAA,eACvDpC,IAAA,CAACF,uBAAuB,EAACqC,SAAS,CAAC,SAAS,CAAE,CAAC,cAC/CnC,IAAA,SAAAoC,QAAA,CAAM,8DAA4D,CAAM,CAAC,EACtE,CACN,EACE,CAAC,CAGV,IAAK,EAAC,CACJ,mBACElC,KAAA,QAAKiC,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBlC,KAAA,QAAKiC,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BpC,IAAA,CAACJ,mBAAmB,EAACuC,SAAS,CAAC,iCAAiC,CAAE,CAAC,cACnEnC,IAAA,OAAImC,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,qBAAmB,CAAI,CAAC,cAC5EpC,IAAA,MAAGmC,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,uCAE1C,CAAG,CAAC,EACD,CAAC,cAENlC,KAAA,QAAKiC,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBlC,KAAA,QAAAkC,QAAA,eACEpC,IAAA,UAAOmC,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,iBAAe,CAAO,CAAC,cAClFlC,KAAA,WAAQiC,SAAS,CAAC,0IAA0I,CAAAC,QAAA,eAC1JpC,IAAA,WAAAoC,QAAA,CAAQ,eAAa,CAAQ,CAAC,cAC9BpC,IAAA,WAAAoC,QAAA,CAAQ,cAAY,CAAQ,CAAC,cAC7BpC,IAAA,WAAAoC,QAAA,CAAQ,cAAY,CAAQ,CAAC,cAC7BpC,IAAA,WAAAoC,QAAA,CAAQ,cAAY,CAAQ,CAAC,EACvB,CAAC,EACN,CAAC,cAENlC,KAAA,QAAAkC,QAAA,eACEpC,IAAA,UAAOmC,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,sBAAoB,CAAO,CAAC,cACvFpC,IAAA,UACE0C,IAAI,CAAC,MAAM,CACXP,SAAS,CAAC,0IAA0I,CACpJY,WAAW,CAAC,sCAAsC,CACnD,CAAC,EACC,CAAC,cAEN7C,KAAA,QAAKiC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCpC,IAAA,UACE0C,IAAI,CAAC,UAAU,CACfP,SAAS,CAAC,+EAA+E,CACzFmB,cAAc,MACf,CAAC,cACFtD,IAAA,UAAOmC,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,8CAEpD,CAAO,CAAC,EACL,CAAC,EACH,CAAC,cAENlC,KAAA,QAAKiC,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCpC,IAAA,WACEsC,OAAO,CAAEA,CAAA,GAAM9B,cAAc,CAAC,CAAC,CAAE,CACjC2B,SAAS,CAAC,6EAA6E,CAAAC,QAAA,CACxF,MAED,CAAQ,CAAC,cACTpC,IAAA,WACEsC,OAAO,CAAEN,eAAgB,CACzBG,SAAS,CAAC,+DAA+D,CAAAC,QAAA,CAC1E,kBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CAGV,IAAK,EAAC,CACJ,mBACElC,KAAA,QAAKiC,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBlC,KAAA,QAAKiC,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BpC,IAAA,CAACH,eAAe,EAACsC,SAAS,CAAC,kCAAkC,CAAE,CAAC,cAChEnC,IAAA,OAAImC,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,qBAAmB,CAAI,CAAC,cAC5EpC,IAAA,MAAGmC,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,qDAE1C,CAAG,CAAC,EACD,CAAC,cAENlC,KAAA,QAAKiC,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzCpC,IAAA,OAAImC,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,kBAAgB,CAAI,CAAC,cACzElC,KAAA,QAAKiC,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBlC,KAAA,QAAKiC,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCpC,IAAA,SAAMmC,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,WAAS,CAAM,CAAC,cAChDpC,IAAA,SAAMmC,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAE3B,WAAW,CAACE,QAAQ,CAAC4C,WAAW,CAAC,CAAC,CAAO,CAAC,EACrE,CAAC,cACNrD,KAAA,QAAKiC,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCpC,IAAA,SAAMmC,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,SAAO,CAAM,CAAC,cAC9CpC,IAAA,SAAMmC,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,SAAO,CAAM,CAAC,EAC5C,CAAC,cACNlC,KAAA,QAAKiC,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCpC,IAAA,SAAMmC,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,kBAAgB,CAAM,CAAC,cACvDpC,IAAA,SAAMmC,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,IAAE,CAAM,CAAC,EACnC,CAAC,EACH,CAAC,EACH,CAAC,cAENpC,IAAA,QAAKmC,SAAS,CAAC,qBAAqB,CAAAC,QAAA,cAClCpC,IAAA,WACEsC,OAAO,CAAEA,CAAA,GAAMkB,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAG,SAAU,CAChDvB,SAAS,CAAC,+DAA+D,CAAAC,QAAA,CAC1E,cAED,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,CAGV,QACE,MAAO,KAAI,CACf,CACF,CAAC,CAED,mBACElC,KAAA,QAAKiC,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExBlC,KAAA,QAAAkC,QAAA,eACEpC,IAAA,OAAImC,SAAS,CAAC,+BAA+B,CAAAC,QAAA,CAAC,kBAAgB,CAAI,CAAC,cACnEpC,IAAA,MAAGmC,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,qEAE1C,CAAG,CAAC,EACD,CAAC,cAGNpC,IAAA,QAAKmC,SAAS,CAAC,+BAA+B,CAAAC,QAAA,cAC5CpC,IAAA,QAAKmC,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BpC,IAAA,QAAK,aAAW,UAAU,CAAAoC,QAAA,cACxBpC,IAAA,OAAImC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAC9BpB,KAAK,CAACqB,GAAG,CAAC,CAACsB,IAAI,CAAEC,OAAO,gBACvB1D,KAAA,OAAkBiC,SAAS,IAAA0B,MAAA,CAAKD,OAAO,GAAK5C,KAAK,CAAC8C,MAAM,CAAG,CAAC,CAAG,eAAe,CAAG,EAAE,aAAY,CAAA1B,QAAA,eAC7FlC,KAAA,QAAKiC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCpC,IAAA,QACEmC,SAAS,mEAAA0B,MAAA,CACPF,IAAI,CAAC1C,EAAE,EAAIV,WAAW,CAClB,wBAAwB,CACxB,oDAAoD,CACvD,CAAA6B,QAAA,CAEFuB,IAAI,CAAC1C,EAAE,CAAGV,WAAW,cACpBP,IAAA,CAACH,eAAe,EAACsC,SAAS,CAAC,SAAS,CAAE,CAAC,cAEvCnC,IAAA,SAAMmC,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAEuB,IAAI,CAAC1C,EAAE,CAAO,CACtD,CACE,CAAC,cACNf,KAAA,QAAKiC,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClCpC,IAAA,MAAGmC,SAAS,wBAAA0B,MAAA,CAAyBF,IAAI,CAAC1C,EAAE,EAAIV,WAAW,CAAG,YAAY,CAAG,eAAe,CAAG,CAAA6B,QAAA,CAC5FuB,IAAI,CAACzC,IAAI,CACT,CAAC,cACJlB,IAAA,MAAGmC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEuB,IAAI,CAACxC,WAAW,CAAI,CAAC,EACxD,CAAC,EACH,CAAC,CACLyC,OAAO,GAAK5C,KAAK,CAAC8C,MAAM,CAAG,CAAC,eAC3B9D,IAAA,QAAKmC,SAAS,CAAC,8DAA8D,CAAE,CAChF,GAxBMwB,IAAI,CAAC1C,EAyBV,CACL,CAAC,CACA,CAAC,CACF,CAAC,CACH,CAAC,CACH,CAAC,cAGNjB,IAAA,QAAKmC,SAAS,CAAC,+BAA+B,CAAAC,QAAA,cAC5CpC,IAAA,QAAKmC,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAC9BF,iBAAiB,CAAC,CAAC,CACjB,CAAC,CACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAA7B,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}