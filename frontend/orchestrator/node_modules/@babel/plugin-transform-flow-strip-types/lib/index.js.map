{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_pluginSyntaxFlow", "_core", "_default", "exports", "default", "declare", "api", "opts", "assertVersion", "FLOW_DIRECTIVE", "skipStrip", "requireDirective", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "inherits", "syntaxFlow", "visitor", "Program", "path", "file", "ast", "comments", "directiveFound", "comment", "test", "value", "replace", "trim", "ignore", "ImportDeclaration", "node", "specifiers", "length", "typeCount", "for<PERSON>ach", "importKind", "remove", "Flow", "buildCodeFrameError", "ClassPrivateProperty", "typeAnnotation", "Class", "implements", "get", "child", "isClassProperty", "decorators", "variance", "AssignmentPattern", "left", "optional", "Function", "params", "type", "shift", "i", "param", "t", "isMethod", "predicate", "TypeCastExpression", "expression", "isTypeCastExpression", "replaceWith", "CallExpression", "typeArguments", "JSXOpeningElement", "OptionalCallExpression", "NewExpression"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport syntaxFlow from \"@babel/plugin-syntax-flow\";\nimport { types as t, type NodePath } from \"@babel/core\";\n\nexport interface Options {\n  requireDirective?: boolean;\n  allowDeclareFields?: boolean;\n}\n\nexport default declare((api, opts: Options) => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  const FLOW_DIRECTIVE = /@flow(?:\\s+(?:strict(?:-local)?|weak))?|@noflow/;\n\n  let skipStrip = false;\n\n  const { requireDirective = false } = opts;\n\n  if (!process.env.BABEL_8_BREAKING) {\n    // eslint-disable-next-line no-var\n    var { allowDeclareFields = false } = opts;\n  }\n\n  return {\n    name: \"transform-flow-strip-types\",\n    inherits: syntaxFlow,\n\n    visitor: {\n      Program(\n        path,\n        {\n          file: {\n            ast: { comments },\n          },\n        },\n      ) {\n        skipStrip = false;\n        let directiveFound = false;\n\n        if (comments) {\n          for (const comment of comments) {\n            if (FLOW_DIRECTIVE.test(comment.value)) {\n              directiveFound = true;\n\n              // remove flow directive\n              comment.value = comment.value.replace(FLOW_DIRECTIVE, \"\");\n\n              // remove the comment completely if it only consists of whitespace and/or stars\n              if (!comment.value.replace(/\\*/g, \"\").trim()) {\n                comment.ignore = true;\n              }\n            }\n          }\n        }\n\n        if (!directiveFound && requireDirective) {\n          skipStrip = true;\n        }\n      },\n      ImportDeclaration(path) {\n        if (skipStrip) return;\n        if (!path.node.specifiers.length) return;\n\n        let typeCount = 0;\n\n        // @ts-expect-error importKind is only in importSpecifier\n        path.node.specifiers.forEach(({ importKind }) => {\n          if (importKind === \"type\" || importKind === \"typeof\") {\n            typeCount++;\n          }\n        });\n\n        if (typeCount === path.node.specifiers.length) {\n          path.remove();\n        }\n      },\n\n      Flow(\n        path: NodePath<\n          t.Flow | t.ImportDeclaration | t.ExportDeclaration | t.ImportSpecifier\n        >,\n      ) {\n        if (skipStrip) {\n          throw path.buildCodeFrameError(\n            \"A @flow directive is required when using Flow annotations with \" +\n              \"the `requireDirective` option.\",\n          );\n        }\n\n        path.remove();\n      },\n\n      ClassPrivateProperty(path) {\n        if (skipStrip) return;\n        path.node.typeAnnotation = null;\n      },\n\n      Class(path) {\n        if (skipStrip) return;\n        path.node.implements = null;\n\n        // We do this here instead of in a `ClassProperty` visitor because the class transform\n        // would transform the class before we reached the class property.\n        path.get(\"body.body\").forEach(child => {\n          if (child.isClassProperty()) {\n            const { node } = child;\n\n            if (!process.env.BABEL_8_BREAKING) {\n              if (!allowDeclareFields && node.declare) {\n                throw child.buildCodeFrameError(\n                  `The 'declare' modifier is only allowed when the ` +\n                    `'allowDeclareFields' option of ` +\n                    `@babel/plugin-transform-flow-strip-types or ` +\n                    `@babel/preset-flow is enabled.`,\n                );\n              }\n            }\n\n            if (node.declare) {\n              child.remove();\n            } else {\n              if (!process.env.BABEL_8_BREAKING) {\n                if (!allowDeclareFields && !node.value && !node.decorators) {\n                  child.remove();\n                  return;\n                }\n              }\n\n              node.variance = null;\n              node.typeAnnotation = null;\n            }\n          }\n        });\n      },\n\n      AssignmentPattern({ node }) {\n        if (skipStrip) return;\n        // @ts-expect-error optional is not in TSAsExpression\n        if (node.left.optional) {\n          // @ts-expect-error optional is not in TSAsExpression\n          node.left.optional = false;\n        }\n      },\n\n      Function({ node }) {\n        if (skipStrip) return;\n        if (\n          node.params.length > 0 &&\n          node.params[0].type === \"Identifier\" &&\n          node.params[0].name === \"this\"\n        ) {\n          node.params.shift();\n        }\n        for (let i = 0; i < node.params.length; i++) {\n          let param = node.params[i];\n          if (param.type === \"AssignmentPattern\") {\n            // @ts-expect-error: refine AST types, the left of an assignment pattern as a binding\n            // must not be a MemberExpression\n            param = param.left;\n          }\n          // @ts-expect-error optional is not in TSAsExpression\n          if (param.optional) {\n            // @ts-expect-error optional is not in TSAsExpression\n            param.optional = false;\n          }\n        }\n\n        if (!t.isMethod(node)) {\n          node.predicate = null;\n        }\n      },\n\n      TypeCastExpression(path) {\n        if (skipStrip) return;\n        let { node } = path;\n        do {\n          // @ts-expect-error node is a search pointer\n          node = node.expression;\n        } while (t.isTypeCastExpression(node));\n        path.replaceWith(node);\n      },\n\n      CallExpression({ node }) {\n        if (skipStrip) return;\n        node.typeArguments = null;\n      },\n\n      JSXOpeningElement({ node }) {\n        if (skipStrip) return;\n        node.typeArguments = null;\n      },\n\n      OptionalCallExpression({ node }) {\n        if (skipStrip) return;\n        node.typeArguments = null;\n      },\n\n      NewExpression({ node }) {\n        if (skipStrip) return;\n        node.typeArguments = null;\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,iBAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AAAwD,IAAAG,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAOzC,IAAAC,0BAAO,EAAC,CAACC,GAAG,EAAEC,IAAa,KAAK;EAC7CD,GAAG,CAACE,aAAa,CAAkB,CAAE,CAAC;EAEtC,MAAMC,cAAc,GAAG,iDAAiD;EAExE,IAAIC,SAAS,GAAG,KAAK;EAErB,MAAM;IAAEC,gBAAgB,GAAG;EAAM,CAAC,GAAGJ,IAAI;EAEN;IAEjC,IAAI;MAAEK,kBAAkB,GAAG;IAAM,CAAC,GAAGL,IAAI;EAC3C;EAEA,OAAO;IACLM,IAAI,EAAE,4BAA4B;IAClCC,QAAQ,EAAEC,yBAAU;IAEpBC,OAAO,EAAE;MACPC,OAAOA,CACLC,IAAI,EACJ;QACEC,IAAI,EAAE;UACJC,GAAG,EAAE;YAAEC;UAAS;QAClB;MACF,CAAC,EACD;QACAX,SAAS,GAAG,KAAK;QACjB,IAAIY,cAAc,GAAG,KAAK;QAE1B,IAAID,QAAQ,EAAE;UACZ,KAAK,MAAME,OAAO,IAAIF,QAAQ,EAAE;YAC9B,IAAIZ,cAAc,CAACe,IAAI,CAACD,OAAO,CAACE,KAAK,CAAC,EAAE;cACtCH,cAAc,GAAG,IAAI;cAGrBC,OAAO,CAACE,KAAK,GAAGF,OAAO,CAACE,KAAK,CAACC,OAAO,CAACjB,cAAc,EAAE,EAAE,CAAC;cAGzD,IAAI,CAACc,OAAO,CAACE,KAAK,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC,EAAE;gBAC5CJ,OAAO,CAACK,MAAM,GAAG,IAAI;cACvB;YACF;UACF;QACF;QAEA,IAAI,CAACN,cAAc,IAAIX,gBAAgB,EAAE;UACvCD,SAAS,GAAG,IAAI;QAClB;MACF,CAAC;MACDmB,iBAAiBA,CAACX,IAAI,EAAE;QACtB,IAAIR,SAAS,EAAE;QACf,IAAI,CAACQ,IAAI,CAACY,IAAI,CAACC,UAAU,CAACC,MAAM,EAAE;QAElC,IAAIC,SAAS,GAAG,CAAC;QAGjBf,IAAI,CAACY,IAAI,CAACC,UAAU,CAACG,OAAO,CAAC,CAAC;UAAEC;QAAW,CAAC,KAAK;UAC/C,IAAIA,UAAU,KAAK,MAAM,IAAIA,UAAU,KAAK,QAAQ,EAAE;YACpDF,SAAS,EAAE;UACb;QACF,CAAC,CAAC;QAEF,IAAIA,SAAS,KAAKf,IAAI,CAACY,IAAI,CAACC,UAAU,CAACC,MAAM,EAAE;UAC7Cd,IAAI,CAACkB,MAAM,CAAC,CAAC;QACf;MACF,CAAC;MAEDC,IAAIA,CACFnB,IAEC,EACD;QACA,IAAIR,SAAS,EAAE;UACb,MAAMQ,IAAI,CAACoB,mBAAmB,CAC5B,iEAAiE,GAC/D,gCACJ,CAAC;QACH;QAEApB,IAAI,CAACkB,MAAM,CAAC,CAAC;MACf,CAAC;MAEDG,oBAAoBA,CAACrB,IAAI,EAAE;QACzB,IAAIR,SAAS,EAAE;QACfQ,IAAI,CAACY,IAAI,CAACU,cAAc,GAAG,IAAI;MACjC,CAAC;MAEDC,KAAKA,CAACvB,IAAI,EAAE;QACV,IAAIR,SAAS,EAAE;QACfQ,IAAI,CAACY,IAAI,CAACY,UAAU,GAAG,IAAI;QAI3BxB,IAAI,CAACyB,GAAG,CAAC,WAAW,CAAC,CAACT,OAAO,CAACU,KAAK,IAAI;UACrC,IAAIA,KAAK,CAACC,eAAe,CAAC,CAAC,EAAE;YAC3B,MAAM;cAAEf;YAAK,CAAC,GAAGc,KAAK;YAEa;cACjC,IAAI,CAAChC,kBAAkB,IAAIkB,IAAI,CAACzB,OAAO,EAAE;gBACvC,MAAMuC,KAAK,CAACN,mBAAmB,CAC7B,kDAAkD,GAChD,iCAAiC,GACjC,8CAA8C,GAC9C,gCACJ,CAAC;cACH;YACF;YAEA,IAAIR,IAAI,CAACzB,OAAO,EAAE;cAChBuC,KAAK,CAACR,MAAM,CAAC,CAAC;YAChB,CAAC,MAAM;cAC8B;gBACjC,IAAI,CAACxB,kBAAkB,IAAI,CAACkB,IAAI,CAACL,KAAK,IAAI,CAACK,IAAI,CAACgB,UAAU,EAAE;kBAC1DF,KAAK,CAACR,MAAM,CAAC,CAAC;kBACd;gBACF;cACF;cAEAN,IAAI,CAACiB,QAAQ,GAAG,IAAI;cACpBjB,IAAI,CAACU,cAAc,GAAG,IAAI;YAC5B;UACF;QACF,CAAC,CAAC;MACJ,CAAC;MAEDQ,iBAAiBA,CAAC;QAAElB;MAAK,CAAC,EAAE;QAC1B,IAAIpB,SAAS,EAAE;QAEf,IAAIoB,IAAI,CAACmB,IAAI,CAACC,QAAQ,EAAE;UAEtBpB,IAAI,CAACmB,IAAI,CAACC,QAAQ,GAAG,KAAK;QAC5B;MACF,CAAC;MAEDC,QAAQA,CAAC;QAAErB;MAAK,CAAC,EAAE;QACjB,IAAIpB,SAAS,EAAE;QACf,IACEoB,IAAI,CAACsB,MAAM,CAACpB,MAAM,GAAG,CAAC,IACtBF,IAAI,CAACsB,MAAM,CAAC,CAAC,CAAC,CAACC,IAAI,KAAK,YAAY,IACpCvB,IAAI,CAACsB,MAAM,CAAC,CAAC,CAAC,CAACvC,IAAI,KAAK,MAAM,EAC9B;UACAiB,IAAI,CAACsB,MAAM,CAACE,KAAK,CAAC,CAAC;QACrB;QACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzB,IAAI,CAACsB,MAAM,CAACpB,MAAM,EAAEuB,CAAC,EAAE,EAAE;UAC3C,IAAIC,KAAK,GAAG1B,IAAI,CAACsB,MAAM,CAACG,CAAC,CAAC;UAC1B,IAAIC,KAAK,CAACH,IAAI,KAAK,mBAAmB,EAAE;YAGtCG,KAAK,GAAGA,KAAK,CAACP,IAAI;UACpB;UAEA,IAAIO,KAAK,CAACN,QAAQ,EAAE;YAElBM,KAAK,CAACN,QAAQ,GAAG,KAAK;UACxB;QACF;QAEA,IAAI,CAACO,WAAC,CAACC,QAAQ,CAAC5B,IAAI,CAAC,EAAE;UACrBA,IAAI,CAAC6B,SAAS,GAAG,IAAI;QACvB;MACF,CAAC;MAEDC,kBAAkBA,CAAC1C,IAAI,EAAE;QACvB,IAAIR,SAAS,EAAE;QACf,IAAI;UAAEoB;QAAK,CAAC,GAAGZ,IAAI;QACnB,GAAG;UAEDY,IAAI,GAAGA,IAAI,CAAC+B,UAAU;QACxB,CAAC,QAAQJ,WAAC,CAACK,oBAAoB,CAAChC,IAAI,CAAC;QACrCZ,IAAI,CAAC6C,WAAW,CAACjC,IAAI,CAAC;MACxB,CAAC;MAEDkC,cAAcA,CAAC;QAAElC;MAAK,CAAC,EAAE;QACvB,IAAIpB,SAAS,EAAE;QACfoB,IAAI,CAACmC,aAAa,GAAG,IAAI;MAC3B,CAAC;MAEDC,iBAAiBA,CAAC;QAAEpC;MAAK,CAAC,EAAE;QAC1B,IAAIpB,SAAS,EAAE;QACfoB,IAAI,CAACmC,aAAa,GAAG,IAAI;MAC3B,CAAC;MAEDE,sBAAsBA,CAAC;QAAErC;MAAK,CAAC,EAAE;QAC/B,IAAIpB,SAAS,EAAE;QACfoB,IAAI,CAACmC,aAAa,GAAG,IAAI;MAC3B,CAAC;MAEDG,aAAaA,CAAC;QAAEtC;MAAK,CAAC,EAAE;QACtB,IAAIpB,SAAS,EAAE;QACfoB,IAAI,CAACmC,aAAa,GAAG,IAAI;MAC3B;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}