{"ast": null, "code": "import { objectSpread2 as _objectSpread2 } from '../_virtual/_rollupPluginBabelHelpers.js';\nfunction merge(target, source) {\n  Object.keys(source).forEach(function (key) {\n    if (source[key] instanceof Object) {\n      if (target[key]) {\n        Object.assign(source[key], merge(target[key], source[key]));\n      }\n    }\n  });\n  return _objectSpread2(_objectSpread2({}, target), source);\n}\nexport default merge;", "map": {"version": 3, "names": ["objectSpread2", "_objectSpread2", "merge", "target", "source", "Object", "keys", "for<PERSON>ach", "key", "assign"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/node_modules/@monaco-editor/loader/lib/es/utils/deepMerge.js"], "sourcesContent": ["import { objectSpread2 as _objectSpread2 } from '../_virtual/_rollupPluginBabelHelpers.js';\n\nfunction merge(target, source) {\n  Object.keys(source).forEach(function (key) {\n    if (source[key] instanceof Object) {\n      if (target[key]) {\n        Object.assign(source[key], merge(target[key], source[key]));\n      }\n    }\n  });\n  return _objectSpread2(_objectSpread2({}, target), source);\n}\n\nexport default merge;\n"], "mappings": "AAAA,SAASA,aAAa,IAAIC,cAAc,QAAQ,0CAA0C;AAE1F,SAASC,KAAKA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAC7BC,MAAM,CAACC,IAAI,CAACF,MAAM,CAAC,CAACG,OAAO,CAAC,UAAUC,GAAG,EAAE;IACzC,IAAIJ,MAAM,CAACI,GAAG,CAAC,YAAYH,MAAM,EAAE;MACjC,IAAIF,MAAM,CAACK,GAAG,CAAC,EAAE;QACfH,MAAM,CAACI,MAAM,CAACL,MAAM,CAACI,GAAG,CAAC,EAAEN,KAAK,CAACC,MAAM,CAACK,GAAG,CAAC,EAAEJ,MAAM,CAACI,GAAG,CAAC,CAAC,CAAC;MAC7D;IACF;EACF,CAAC,CAAC;EACF,OAAOP,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEE,MAAM,CAAC,EAAEC,MAAM,CAAC;AAC3D;AAEA,eAAeF,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}