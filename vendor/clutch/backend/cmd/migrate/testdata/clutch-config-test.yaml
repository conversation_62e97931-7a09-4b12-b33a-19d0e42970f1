gateway:
  logger:
    pretty: true
    level: DEBUG
  stats:
    flush_interval: 1s
    log_reporter: {}
  timeouts:
    default: 15s
  listener:
    tcp:
      address: 0.0.0.0
      port: 8080
      secure: false
services:
  - name: clutch.service.db.postgres
    typed_config:
      "@type": types.google.com/clutch.config.service.db.postgres.v1.Config
      connection:
        host: 0.0.0.0
        port: 5432
        user: clutch
        ssl_mode: DISABLE
        dbname: clutch
        password: clutch
