package project

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"
)

// Service represents the project management service
type Service struct {
	logger   *zap.Logger
	projects map[string]*Project
	mu       sync.RWMutex
}

// Project represents a project
type Project struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Status      ProjectStatus          `json:"status"`
	Owner       string                 `json:"owner"`
	Team        []string               `json:"team"`
	Tags        []string               `json:"tags"`
	Labels      map[string]string      `json:"labels"`
	Resources   []ProjectResource      `json:"resources"`
	Environments []Environment         `json:"environments"`
	Metadata    map[string]interface{} `json:"metadata"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	StartDate   *time.Time             `json:"start_date,omitempty"`
	EndDate     *time.Time             `json:"end_date,omitempty"`
	Budget      *Budget                `json:"budget,omitempty"`
	Compliance  *ComplianceInfo        `json:"compliance,omitempty"`
}

// ProjectStatus represents the status of a project
type ProjectStatus string

const (
	ProjectStatusPlanning   ProjectStatus = "planning"
	ProjectStatusActive     ProjectStatus = "active"
	ProjectStatusOnHold     ProjectStatus = "on_hold"
	ProjectStatusCompleted  ProjectStatus = "completed"
	ProjectStatusArchived   ProjectStatus = "archived"
	ProjectStatusCancelled  ProjectStatus = "cancelled"
)

// ProjectResource represents a resource associated with a project
type ProjectResource struct {
	ID       string            `json:"id"`
	Type     string            `json:"type"`
	Name     string            `json:"name"`
	Provider string            `json:"provider"`
	Region   string            `json:"region,omitempty"`
	Status   string            `json:"status"`
	Tags     map[string]string `json:"tags,omitempty"`
	Cost     *ResourceCost     `json:"cost,omitempty"`
}

// Environment represents a project environment
type Environment struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Type        EnvironmentType        `json:"type"`
	Status      string                 `json:"status"`
	URL         string                 `json:"url,omitempty"`
	Resources   []ProjectResource      `json:"resources"`
	Variables   map[string]string      `json:"variables,omitempty"`
	Secrets     []string               `json:"secrets,omitempty"`
	Deployments []Deployment           `json:"deployments"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// EnvironmentType represents the type of environment
type EnvironmentType string

const (
	EnvironmentTypeDevelopment EnvironmentType = "development"
	EnvironmentTypeStaging     EnvironmentType = "staging"
	EnvironmentTypeProduction  EnvironmentType = "production"
	EnvironmentTypeTesting     EnvironmentType = "testing"
)

// Deployment represents a deployment to an environment
type Deployment struct {
	ID          string                 `json:"id"`
	Version     string                 `json:"version"`
	Status      DeploymentStatus       `json:"status"`
	DeployedBy  string                 `json:"deployed_by"`
	DeployedAt  time.Time              `json:"deployed_at"`
	CompletedAt *time.Time             `json:"completed_at,omitempty"`
	RollbackTo  string                 `json:"rollback_to,omitempty"`
	Changes     []string               `json:"changes,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// DeploymentStatus represents the status of a deployment
type DeploymentStatus string

const (
	DeploymentStatusPending    DeploymentStatus = "pending"
	DeploymentStatusInProgress DeploymentStatus = "in_progress"
	DeploymentStatusCompleted  DeploymentStatus = "completed"
	DeploymentStatusFailed     DeploymentStatus = "failed"
	DeploymentStatusRolledBack DeploymentStatus = "rolled_back"
)

// Budget represents project budget information
type Budget struct {
	Total     float64   `json:"total"`
	Spent     float64   `json:"spent"`
	Remaining float64   `json:"remaining"`
	Currency  string    `json:"currency"`
	Period    string    `json:"period"`
	UpdatedAt time.Time `json:"updated_at"`
}

// ResourceCost represents the cost of a resource
type ResourceCost struct {
	Monthly   float64   `json:"monthly"`
	Daily     float64   `json:"daily"`
	Currency  string    `json:"currency"`
	UpdatedAt time.Time `json:"updated_at"`
}

// ComplianceInfo represents compliance information
type ComplianceInfo struct {
	Standards   []string  `json:"standards"`
	Status      string    `json:"status"`
	LastAudit   time.Time `json:"last_audit"`
	NextAudit   time.Time `json:"next_audit"`
	Issues      []string  `json:"issues,omitempty"`
	Certifications []string `json:"certifications,omitempty"`
}

// CreateProjectRequest represents a project creation request
type CreateProjectRequest struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Owner       string                 `json:"owner"`
	Team        []string               `json:"team,omitempty"`
	Tags        []string               `json:"tags,omitempty"`
	Labels      map[string]string      `json:"labels,omitempty"`
	StartDate   *time.Time             `json:"start_date,omitempty"`
	EndDate     *time.Time             `json:"end_date,omitempty"`
	Budget      *Budget                `json:"budget,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// UpdateProjectRequest represents a project update request
type UpdateProjectRequest struct {
	Name        *string                `json:"name,omitempty"`
	Description *string                `json:"description,omitempty"`
	Status      *ProjectStatus         `json:"status,omitempty"`
	Team        []string               `json:"team,omitempty"`
	Tags        []string               `json:"tags,omitempty"`
	Labels      map[string]string      `json:"labels,omitempty"`
	EndDate     *time.Time             `json:"end_date,omitempty"`
	Budget      *Budget                `json:"budget,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// SearchRequest represents a project search request
type SearchRequest struct {
	Query  string        `json:"query,omitempty"`
	Status ProjectStatus `json:"status,omitempty"`
	Owner  string        `json:"owner,omitempty"`
	Tags   []string      `json:"tags,omitempty"`
	Limit  int           `json:"limit,omitempty"`
	Offset int           `json:"offset,omitempty"`
}

// New creates a new project service
func New(logger *zap.Logger) *Service {
	service := &Service{
		logger:   logger,
		projects: make(map[string]*Project),
	}

	// Initialize with example projects
	service.initializeExampleProjects()

	return service
}

// CreateProject creates a new project
func (s *Service) CreateProject(ctx context.Context, req *CreateProjectRequest) (*Project, error) {
	s.mu.Lock()
	defer s.mu.Unlock()

	project := &Project{
		ID:          fmt.Sprintf("proj-%d", time.Now().Unix()),
		Name:        req.Name,
		Description: req.Description,
		Status:      ProjectStatusPlanning,
		Owner:       req.Owner,
		Team:        req.Team,
		Tags:        req.Tags,
		Labels:      req.Labels,
		Resources:   []ProjectResource{},
		Environments: []Environment{},
		Metadata:    req.Metadata,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		StartDate:   req.StartDate,
		EndDate:     req.EndDate,
		Budget:      req.Budget,
	}

	s.projects[project.ID] = project

	s.logger.Info("Project created",
		zap.String("id", project.ID),
		zap.String("name", project.Name),
		zap.String("owner", project.Owner))

	return project, nil
}

// GetProject retrieves a project by ID
func (s *Service) GetProject(ctx context.Context, id string) (*Project, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	project, exists := s.projects[id]
	if !exists {
		return nil, fmt.Errorf("project not found: %s", id)
	}

	return project, nil
}

// UpdateProject updates a project
func (s *Service) UpdateProject(ctx context.Context, id string, req *UpdateProjectRequest) (*Project, error) {
	s.mu.Lock()
	defer s.mu.Unlock()

	project, exists := s.projects[id]
	if !exists {
		return nil, fmt.Errorf("project not found: %s", id)
	}

	// Apply updates
	if req.Name != nil {
		project.Name = *req.Name
	}
	if req.Description != nil {
		project.Description = *req.Description
	}
	if req.Status != nil {
		project.Status = *req.Status
	}
	if req.Team != nil {
		project.Team = req.Team
	}
	if req.Tags != nil {
		project.Tags = req.Tags
	}
	if req.Labels != nil {
		project.Labels = req.Labels
	}
	if req.EndDate != nil {
		project.EndDate = req.EndDate
	}
	if req.Budget != nil {
		project.Budget = req.Budget
	}
	if req.Metadata != nil {
		project.Metadata = req.Metadata
	}

	project.UpdatedAt = time.Now()

	s.logger.Info("Project updated",
		zap.String("id", project.ID),
		zap.String("name", project.Name))

	return project, nil
}

// DeleteProject deletes a project
func (s *Service) DeleteProject(ctx context.Context, id string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	project, exists := s.projects[id]
	if !exists {
		return fmt.Errorf("project not found: %s", id)
	}

	delete(s.projects, id)

	s.logger.Info("Project deleted",
		zap.String("id", project.ID),
		zap.String("name", project.Name))

	return nil
}

// ListProjects lists all projects
func (s *Service) ListProjects(ctx context.Context, limit, offset int) ([]*Project, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	projects := make([]*Project, 0, len(s.projects))
	for _, project := range s.projects {
		projects = append(projects, project)
	}

	// Apply pagination
	if offset >= len(projects) {
		return []*Project{}, nil
	}

	end := offset + limit
	if end > len(projects) {
		end = len(projects)
	}

	return projects[offset:end], nil
}

// SearchProjects searches for projects
func (s *Service) SearchProjects(ctx context.Context, req *SearchRequest) ([]*Project, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	var results []*Project

	for _, project := range s.projects {
		if s.matchesSearchCriteria(project, req) {
			results = append(results, project)
		}
	}

	// Apply pagination
	limit := req.Limit
	if limit == 0 {
		limit = 50
	}

	offset := req.Offset
	if offset >= len(results) {
		return []*Project{}, nil
	}

	end := offset + limit
	if end > len(results) {
		end = len(results)
	}

	return results[offset:end], nil
}

// AddResource adds a resource to a project
func (s *Service) AddResource(ctx context.Context, projectID string, resource ProjectResource) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	project, exists := s.projects[projectID]
	if !exists {
		return fmt.Errorf("project not found: %s", projectID)
	}

	project.Resources = append(project.Resources, resource)
	project.UpdatedAt = time.Now()

	s.logger.Info("Resource added to project",
		zap.String("project_id", projectID),
		zap.String("resource_id", resource.ID),
		zap.String("resource_type", resource.Type))

	return nil
}

// RemoveResource removes a resource from a project
func (s *Service) RemoveResource(ctx context.Context, projectID, resourceID string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	project, exists := s.projects[projectID]
	if !exists {
		return fmt.Errorf("project not found: %s", projectID)
	}

	for i, resource := range project.Resources {
		if resource.ID == resourceID {
			project.Resources = append(project.Resources[:i], project.Resources[i+1:]...)
			project.UpdatedAt = time.Now()

			s.logger.Info("Resource removed from project",
				zap.String("project_id", projectID),
				zap.String("resource_id", resourceID))

			return nil
		}
	}

	return fmt.Errorf("resource not found in project: %s", resourceID)
}

// CreateEnvironment creates a new environment for a project
func (s *Service) CreateEnvironment(ctx context.Context, projectID string, env Environment) (*Environment, error) {
	s.mu.Lock()
	defer s.mu.Unlock()

	project, exists := s.projects[projectID]
	if !exists {
		return nil, fmt.Errorf("project not found: %s", projectID)
	}

	env.ID = fmt.Sprintf("env-%d", time.Now().Unix())
	env.CreatedAt = time.Now()
	env.UpdatedAt = time.Now()

	project.Environments = append(project.Environments, env)
	project.UpdatedAt = time.Now()

	s.logger.Info("Environment created",
		zap.String("project_id", projectID),
		zap.String("env_id", env.ID),
		zap.String("env_name", env.Name))

	return &env, nil
}

// matchesSearchCriteria checks if a project matches search criteria
func (s *Service) matchesSearchCriteria(project *Project, req *SearchRequest) bool {
	// Query match
	if req.Query != "" {
		query := strings.ToLower(req.Query)
		if !strings.Contains(strings.ToLower(project.Name), query) &&
		   !strings.Contains(strings.ToLower(project.Description), query) &&
		   !strings.Contains(strings.ToLower(project.ID), query) {
			return false
		}
	}

	// Status filter
	if req.Status != "" && project.Status != req.Status {
		return false
	}

	// Owner filter
	if req.Owner != "" && project.Owner != req.Owner {
		return false
	}

	// Tags filter
	if len(req.Tags) > 0 {
		for _, reqTag := range req.Tags {
			found := false
			for _, projectTag := range project.Tags {
				if projectTag == reqTag {
					found = true
					break
				}
			}
			if !found {
				return false
			}
		}
	}

	return true
}

// initializeExampleProjects creates example projects
func (s *Service) initializeExampleProjects() {
	examples := []*Project{
		{
			ID:          "proj-web-platform",
			Name:        "Web Platform",
			Description: "Main web application platform",
			Status:      ProjectStatusActive,
			Owner:       "platform-team",
			Team:        []string{"alice", "bob", "charlie"},
			Tags:        []string{"web", "platform", "production"},
			Labels:      map[string]string{"priority": "high", "team": "platform"},
			Resources: []ProjectResource{
				{
					ID:       "vpc-12345",
					Type:     "aws.vpc",
					Name:     "platform-vpc",
					Provider: "aws",
					Region:   "us-east-1",
					Status:   "active",
				},
				{
					ID:       "cluster-67890",
					Type:     "k8s.cluster",
					Name:     "platform-cluster",
					Provider: "aws",
					Region:   "us-east-1",
					Status:   "ready",
				},
			},
			Environments: []Environment{
				{
					ID:     "env-prod",
					Name:   "Production",
					Type:   EnvironmentTypeProduction,
					Status: "active",
					URL:    "https://app.cainuro.com",
					Deployments: []Deployment{
						{
							ID:         "deploy-123",
							Version:    "v1.2.3",
							Status:     DeploymentStatusCompleted,
							DeployedBy: "alice",
							DeployedAt: time.Now().Add(-2 * time.Hour),
						},
					},
				},
			},
			CreatedAt: time.Now().Add(-30 * 24 * time.Hour),
			UpdatedAt: time.Now().Add(-1 * time.Hour),
			Budget: &Budget{
				Total:     10000.0,
				Spent:     7500.0,
				Remaining: 2500.0,
				Currency:  "USD",
				Period:    "monthly",
				UpdatedAt: time.Now(),
			},
		},
		{
			ID:          "proj-data-pipeline",
			Name:        "Data Pipeline",
			Description: "ETL and data processing pipeline",
			Status:      ProjectStatusActive,
			Owner:       "data-team",
			Team:        []string{"david", "eve"],
			Tags:        []string{"data", "etl", "analytics"},
			Labels:      map[string]string{"priority": "medium", "team": "data"},
			CreatedAt:   time.Now().Add(-15 * 24 * time.Hour),
			UpdatedAt:   time.Now().Add(-30 * time.Minute),
		},
	}

	for _, project := range examples {
		s.projects[project.ID] = project
	}

	s.logger.Info("Example projects initialized", zap.Int("count", len(examples)))
}

// Health checks the health of the project service
func (s *Service) Health(ctx context.Context) error {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// Simple health check - ensure we can access projects
	_ = len(s.projects)
	return nil
}
