{"ast": null, "code": "import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\nimport axios from 'axios';\nconst initialState = {\n  configs: [],\n  nodes: [],\n  selectedConfig: null,\n  loading: false,\n  error: null\n};\nexport const fetchEnvoyConfigs = createAsyncThunk('envoy/fetchConfigs', async () => {\n  const response = await axios.get('/v1/envoy/configs');\n  return response.data.configs;\n});\nexport const fetchEnvoyNodes = createAsyncThunk('envoy/fetchNodes', async () => {\n  const response = await axios.get('/v1/envoy/nodes');\n  return response.data.nodes;\n});\nexport const createEnvoyConfig = createAsyncThunk('envoy/createConfig', async config => {\n  const response = await axios.post('/v1/envoy/configs', config);\n  return response.data;\n});\nexport const updateEnvoyConfig = createAsyncThunk('envoy/updateConfig', async config => {\n  const response = await axios.put(`/v1/envoy/configs/${config.id}`, config);\n  return response.data;\n});\nconst envoySlice = createSlice({\n  name: 'envoy',\n  initialState,\n  reducers: {\n    setSelectedConfig: (state, action) => {\n      state.selectedConfig = action.payload;\n    }\n  },\n  extraReducers: builder => {\n    builder.addCase(fetchEnvoyConfigs.pending, state => {\n      state.loading = true;\n      state.error = null;\n    }).addCase(fetchEnvoyConfigs.fulfilled, (state, action) => {\n      state.loading = false;\n      state.configs = action.payload;\n    }).addCase(fetchEnvoyConfigs.rejected, (state, action) => {\n      state.loading = false;\n      state.error = action.error.message || 'Failed to fetch Envoy configs';\n    }).addCase(fetchEnvoyNodes.fulfilled, (state, action) => {\n      state.nodes = action.payload;\n    }).addCase(createEnvoyConfig.fulfilled, (state, action) => {\n      state.configs.push(action.payload);\n    }).addCase(updateEnvoyConfig.fulfilled, (state, action) => {\n      const index = state.configs.findIndex(c => c.id === action.payload.id);\n      if (index !== -1) {\n        state.configs[index] = action.payload;\n      }\n    });\n  }\n});\nexport const {\n  setSelectedConfig\n} = envoySlice.actions;\nexport default envoySlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "axios", "initialState", "configs", "nodes", "selectedConfig", "loading", "error", "fetchEnvoyConfigs", "response", "get", "data", "fetchEnvoyNodes", "createEnvoyConfig", "config", "post", "updateEnvoyConfig", "put", "id", "envoyS<PERSON>", "name", "reducers", "setSelectedConfig", "state", "action", "payload", "extraReducers", "builder", "addCase", "pending", "fulfilled", "rejected", "message", "push", "index", "findIndex", "c", "actions", "reducer"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/envoySlice.ts"], "sourcesContent": ["import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';\nimport axios from 'axios';\n\nexport interface EnvoyConfig {\n  id: string;\n  node_id: string;\n  cluster_name: string;\n  config: string;\n  version: string;\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface EnvoyNode {\n  id: string;\n  cluster: string;\n  version: string;\n  last_seen: string;\n  status: 'connected' | 'disconnected';\n}\n\nexport interface EnvoyState {\n  configs: EnvoyConfig[];\n  nodes: EnvoyNode[];\n  selectedConfig: EnvoyConfig | null;\n  loading: boolean;\n  error: string | null;\n}\n\nconst initialState: EnvoyState = {\n  configs: [],\n  nodes: [],\n  selectedConfig: null,\n  loading: false,\n  error: null,\n};\n\nexport const fetchEnvoyConfigs = createAsyncThunk(\n  'envoy/fetchConfigs',\n  async () => {\n    const response = await axios.get('/v1/envoy/configs');\n    return response.data.configs;\n  }\n);\n\nexport const fetchEnvoyNodes = createAsyncThunk(\n  'envoy/fetchNodes',\n  async () => {\n    const response = await axios.get('/v1/envoy/nodes');\n    return response.data.nodes;\n  }\n);\n\nexport const createEnvoyConfig = createAsyncThunk(\n  'envoy/createConfig',\n  async (config: Omit<EnvoyConfig, 'id' | 'created_at' | 'updated_at'>) => {\n    const response = await axios.post('/v1/envoy/configs', config);\n    return response.data;\n  }\n);\n\nexport const updateEnvoyConfig = createAsyncThunk(\n  'envoy/updateConfig',\n  async (config: EnvoyConfig) => {\n    const response = await axios.put(`/v1/envoy/configs/${config.id}`, config);\n    return response.data;\n  }\n);\n\nconst envoySlice = createSlice({\n  name: 'envoy',\n  initialState,\n  reducers: {\n    setSelectedConfig: (state, action: PayloadAction<EnvoyConfig | null>) => {\n      state.selectedConfig = action.payload;\n    },\n  },\n  extraReducers: (builder) => {\n    builder\n      .addCase(fetchEnvoyConfigs.pending, (state) => {\n        state.loading = true;\n        state.error = null;\n      })\n      .addCase(fetchEnvoyConfigs.fulfilled, (state, action) => {\n        state.loading = false;\n        state.configs = action.payload;\n      })\n      .addCase(fetchEnvoyConfigs.rejected, (state, action) => {\n        state.loading = false;\n        state.error = action.error.message || 'Failed to fetch Envoy configs';\n      })\n      .addCase(fetchEnvoyNodes.fulfilled, (state, action) => {\n        state.nodes = action.payload;\n      })\n      .addCase(createEnvoyConfig.fulfilled, (state, action) => {\n        state.configs.push(action.payload);\n      })\n      .addCase(updateEnvoyConfig.fulfilled, (state, action) => {\n        const index = state.configs.findIndex(c => c.id === action.payload.id);\n        if (index !== -1) {\n          state.configs[index] = action.payload;\n        }\n      });\n  },\n});\n\nexport const { setSelectedConfig } = envoySlice.actions;\nexport default envoySlice.reducer;\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,gBAAgB,QAAuB,kBAAkB;AAC/E,OAAOC,KAAK,MAAM,OAAO;AA4BzB,MAAMC,YAAwB,GAAG;EAC/BC,OAAO,EAAE,EAAE;EACXC,KAAK,EAAE,EAAE;EACTC,cAAc,EAAE,IAAI;EACpBC,OAAO,EAAE,KAAK;EACdC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,iBAAiB,GAAGR,gBAAgB,CAC/C,oBAAoB,EACpB,YAAY;EACV,MAAMS,QAAQ,GAAG,MAAMR,KAAK,CAACS,GAAG,CAAC,mBAAmB,CAAC;EACrD,OAAOD,QAAQ,CAACE,IAAI,CAACR,OAAO;AAC9B,CACF,CAAC;AAED,OAAO,MAAMS,eAAe,GAAGZ,gBAAgB,CAC7C,kBAAkB,EAClB,YAAY;EACV,MAAMS,QAAQ,GAAG,MAAMR,KAAK,CAACS,GAAG,CAAC,iBAAiB,CAAC;EACnD,OAAOD,QAAQ,CAACE,IAAI,CAACP,KAAK;AAC5B,CACF,CAAC;AAED,OAAO,MAAMS,iBAAiB,GAAGb,gBAAgB,CAC/C,oBAAoB,EACpB,MAAOc,MAA6D,IAAK;EACvE,MAAML,QAAQ,GAAG,MAAMR,KAAK,CAACc,IAAI,CAAC,mBAAmB,EAAED,MAAM,CAAC;EAC9D,OAAOL,QAAQ,CAACE,IAAI;AACtB,CACF,CAAC;AAED,OAAO,MAAMK,iBAAiB,GAAGhB,gBAAgB,CAC/C,oBAAoB,EACpB,MAAOc,MAAmB,IAAK;EAC7B,MAAML,QAAQ,GAAG,MAAMR,KAAK,CAACgB,GAAG,CAAC,qBAAqBH,MAAM,CAACI,EAAE,EAAE,EAAEJ,MAAM,CAAC;EAC1E,OAAOL,QAAQ,CAACE,IAAI;AACtB,CACF,CAAC;AAED,MAAMQ,UAAU,GAAGpB,WAAW,CAAC;EAC7BqB,IAAI,EAAE,OAAO;EACblB,YAAY;EACZmB,QAAQ,EAAE;IACRC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAyC,KAAK;MACvED,KAAK,CAAClB,cAAc,GAAGmB,MAAM,CAACC,OAAO;IACvC;EACF,CAAC;EACDC,aAAa,EAAGC,OAAO,IAAK;IAC1BA,OAAO,CACJC,OAAO,CAACpB,iBAAiB,CAACqB,OAAO,EAAGN,KAAK,IAAK;MAC7CA,KAAK,CAACjB,OAAO,GAAG,IAAI;MACpBiB,KAAK,CAAChB,KAAK,GAAG,IAAI;IACpB,CAAC,CAAC,CACDqB,OAAO,CAACpB,iBAAiB,CAACsB,SAAS,EAAE,CAACP,KAAK,EAAEC,MAAM,KAAK;MACvDD,KAAK,CAACjB,OAAO,GAAG,KAAK;MACrBiB,KAAK,CAACpB,OAAO,GAAGqB,MAAM,CAACC,OAAO;IAChC,CAAC,CAAC,CACDG,OAAO,CAACpB,iBAAiB,CAACuB,QAAQ,EAAE,CAACR,KAAK,EAAEC,MAAM,KAAK;MACtDD,KAAK,CAACjB,OAAO,GAAG,KAAK;MACrBiB,KAAK,CAAChB,KAAK,GAAGiB,MAAM,CAACjB,KAAK,CAACyB,OAAO,IAAI,+BAA+B;IACvE,CAAC,CAAC,CACDJ,OAAO,CAAChB,eAAe,CAACkB,SAAS,EAAE,CAACP,KAAK,EAAEC,MAAM,KAAK;MACrDD,KAAK,CAACnB,KAAK,GAAGoB,MAAM,CAACC,OAAO;IAC9B,CAAC,CAAC,CACDG,OAAO,CAACf,iBAAiB,CAACiB,SAAS,EAAE,CAACP,KAAK,EAAEC,MAAM,KAAK;MACvDD,KAAK,CAACpB,OAAO,CAAC8B,IAAI,CAACT,MAAM,CAACC,OAAO,CAAC;IACpC,CAAC,CAAC,CACDG,OAAO,CAACZ,iBAAiB,CAACc,SAAS,EAAE,CAACP,KAAK,EAAEC,MAAM,KAAK;MACvD,MAAMU,KAAK,GAAGX,KAAK,CAACpB,OAAO,CAACgC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAAClB,EAAE,KAAKM,MAAM,CAACC,OAAO,CAACP,EAAE,CAAC;MACtE,IAAIgB,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBX,KAAK,CAACpB,OAAO,CAAC+B,KAAK,CAAC,GAAGV,MAAM,CAACC,OAAO;MACvC;IACF,CAAC,CAAC;EACN;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEH;AAAkB,CAAC,GAAGH,UAAU,CAACkB,OAAO;AACvD,eAAelB,UAAU,CAACmB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}