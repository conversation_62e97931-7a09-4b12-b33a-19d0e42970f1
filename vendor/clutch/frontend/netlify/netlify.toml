[build]
  # Only build clutch-components site if frontend files change.
  ignore = "cd .. && git diff-tree --no-commit-id --name-only --quiet -r $COMMIT_REF -- '*.ts*' '*.js' 'yarn.lock' 'package.json' '**/netlify.toml' '*.html'"

[build.environment]
  NODE_VERSION = "18.19.0"

[[headers]]
  # Define which paths this specific [[headers]] block will cover.
  for = "/*"

  [headers.values]
    X-XSS-Protection = "1; mode=block"
