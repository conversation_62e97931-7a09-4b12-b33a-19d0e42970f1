{"compilerOptions": {"allowSyntheticDefaultImports": true, "baseUrl": "./", "composite": true, "declaration": true, "declarationMap": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "importsNotUsedAsValues": "error", "incremental": true, "isolatedModules": true, "jsx": "react", "lib": ["DOM", "ESNext"], "module": "esnext", "moduleResolution": "node", "noImplicitAny": false, "noImplicitReturns": true, "paths": {"@clutch-sh/*": ["api/*", "packages/*/src", "workflows/*/src"]}, "preserveConstEnums": true, "preserveSymlinks": true, "resolveJsonModule": true, "skipLibCheck": true, "strict": false, "target": "ES2018", "types": ["jest", "node", "long"]}, "exclude": ["node_modules", "**/*.stories.ts*", "**/*.stories.js*", "**/*.test.js*", "**/*.test.ts*", "**/dist/*"]}