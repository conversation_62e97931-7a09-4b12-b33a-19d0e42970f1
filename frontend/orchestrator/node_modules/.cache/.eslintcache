[{"/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/index.tsx": "1", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/reportWebVitals.ts": "2", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/store.ts": "3", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/App.tsx": "4", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/searchSlice.ts": "5", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/workflowSlice.ts": "6", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/envoySlice.ts": "7", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/auditSlice.ts": "8", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/autoscalerSlice.ts": "9", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/databaseSlice.ts": "10", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/components/Layout.tsx": "11", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/SearchDashboard.tsx": "12", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/Dashboard.tsx": "13", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/WorkflowExecutor.tsx": "14", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/AutoscalerDashboard.tsx": "15", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/DiscoveryWizard.tsx": "16", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/EnvoyConfigEditor.tsx": "17", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/AuditViewer.tsx": "18", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/DatabaseAdmin.tsx": "19", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/auth/AuthContext.tsx": "20", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/auth/LoginPage.tsx": "21", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/AdminDashboard.tsx": "22", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/UserManagement.tsx": "23", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/UserProfile.tsx": "24", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/Settings.tsx": "25", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/RBACAdmin.tsx": "26", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/FeatureFlagAdmin.tsx": "27", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/components/SearchField.tsx": "28"}, {"size": 778, "mtime": 1749058599021, "results": "29", "hashOfConfig": "30"}, {"size": 370, "mtime": 1749058755887, "results": "31", "hashOfConfig": "30"}, {"size": 863, "mtime": 1749063617349, "results": "32", "hashOfConfig": "30"}, {"size": 4948, "mtime": 1749097025918, "results": "33", "hashOfConfig": "30"}, {"size": 2725, "mtime": 1749063531345, "results": "34", "hashOfConfig": "30"}, {"size": 3481, "mtime": 1749063548264, "results": "35", "hashOfConfig": "30"}, {"size": 2849, "mtime": 1749063582338, "results": "36", "hashOfConfig": "30"}, {"size": 1957, "mtime": 1749063595368, "results": "37", "hashOfConfig": "30"}, {"size": 3681, "mtime": 1749063567934, "results": "38", "hashOfConfig": "30"}, {"size": 2204, "mtime": 1749063606689, "results": "39", "hashOfConfig": "30"}, {"size": 9142, "mtime": 1749099167172, "results": "40", "hashOfConfig": "30"}, {"size": 8594, "mtime": 1749063749914, "results": "41", "hashOfConfig": "30"}, {"size": 12377, "mtime": 1749092192506, "results": "42", "hashOfConfig": "30"}, {"size": 10204, "mtime": 1749063787790, "results": "43", "hashOfConfig": "30"}, {"size": 9190, "mtime": 1749063823453, "results": "44", "hashOfConfig": "30"}, {"size": 17002, "mtime": 1749064442452, "results": "45", "hashOfConfig": "30"}, {"size": 11841, "mtime": 1749064586885, "results": "46", "hashOfConfig": "30"}, {"size": 9026, "mtime": 1749063897136, "results": "47", "hashOfConfig": "30"}, {"size": 8828, "mtime": 1749063931077, "results": "48", "hashOfConfig": "30"}, {"size": 10592, "mtime": 1749089058339, "results": "49", "hashOfConfig": "30"}, {"size": 6612, "mtime": 1749087527402, "results": "50", "hashOfConfig": "30"}, {"size": 12215, "mtime": 1749097173645, "results": "51", "hashOfConfig": "30"}, {"size": 21378, "mtime": 1749090687306, "results": "52", "hashOfConfig": "30"}, {"size": 14536, "mtime": 1749090017788, "results": "53", "hashOfConfig": "30"}, {"size": 18823, "mtime": 1749090144932, "results": "54", "hashOfConfig": "30"}, {"size": 32379, "mtime": 1749098111693, "results": "55", "hashOfConfig": "30"}, {"size": 16911, "mtime": 1749098739465, "results": "56", "hashOfConfig": "30"}, {"size": 7862, "mtime": 1749099139576, "results": "57", "hashOfConfig": "30"}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ojadns", {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/index.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/reportWebVitals.ts", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/store.ts", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/App.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/searchSlice.ts", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/workflowSlice.ts", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/envoySlice.ts", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/auditSlice.ts", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/autoscalerSlice.ts", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/databaseSlice.ts", ["142"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/components/Layout.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/SearchDashboard.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/Dashboard.tsx", ["143"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/WorkflowExecutor.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/AutoscalerDashboard.tsx", ["144"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/DiscoveryWizard.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/EnvoyConfigEditor.tsx", ["145"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/AuditViewer.tsx", ["146"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/DatabaseAdmin.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/auth/AuthContext.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/auth/LoginPage.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/AdminDashboard.tsx", ["147", "148", "149"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/UserManagement.tsx", ["150"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/UserProfile.tsx", ["151"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/Settings.tsx", ["152", "153", "154", "155"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/RBACAdmin.tsx", ["156", "157", "158", "159", "160"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/FeatureFlagAdmin.tsx", ["161", "162", "163", "164", "165"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/components/SearchField.tsx", [], [], {"ruleId": "166", "severity": 1, "message": "167", "line": 1, "column": 41, "nodeType": "168", "messageId": "169", "endLine": 1, "endColumn": 54}, {"ruleId": "166", "severity": 1, "message": "170", "line": 5, "column": 3, "nodeType": "168", "messageId": "169", "endLine": 5, "endColumn": 15}, {"ruleId": "166", "severity": 1, "message": "171", "line": 18, "column": 43, "nodeType": "168", "messageId": "169", "endLine": 18, "endColumn": 50}, {"ruleId": "166", "severity": 1, "message": "172", "line": 19, "column": 52, "nodeType": "168", "messageId": "169", "endLine": 19, "endColumn": 57}, {"ruleId": "166", "severity": 1, "message": "173", "line": 14, "column": 47, "nodeType": "168", "messageId": "169", "endLine": 14, "endColumn": 52}, {"ruleId": "166", "severity": 1, "message": "174", "line": 10, "column": 3, "nodeType": "168", "messageId": "169", "endLine": 10, "endColumn": 26}, {"ruleId": "166", "severity": 1, "message": "175", "line": 13, "column": 3, "nodeType": "168", "messageId": "169", "endLine": 13, "endColumn": 10}, {"ruleId": "166", "severity": 1, "message": "176", "line": 14, "column": 3, "nodeType": "168", "messageId": "169", "endLine": 14, "endColumn": 18}, {"ruleId": "166", "severity": 1, "message": "177", "line": 4, "column": 3, "nodeType": "168", "messageId": "169", "endLine": 4, "endColumn": 12}, {"ruleId": "166", "severity": 1, "message": "178", "line": 8, "column": 3, "nodeType": "168", "messageId": "169", "endLine": 8, "endColumn": 12}, {"ruleId": "166", "severity": 1, "message": "176", "line": 8, "column": 3, "nodeType": "168", "messageId": "169", "endLine": 8, "endColumn": 18}, {"ruleId": "166", "severity": 1, "message": "179", "line": 9, "column": 3, "nodeType": "168", "messageId": "169", "endLine": 9, "endColumn": 12}, {"ruleId": "166", "severity": 1, "message": "180", "line": 42, "column": 11, "nodeType": "168", "messageId": "169", "endLine": 42, "endColumn": 15}, {"ruleId": "181", "severity": 1, "message": "182", "line": 74, "column": 6, "nodeType": "183", "endLine": 74, "endColumn": 8, "suggestions": "184"}, {"ruleId": "166", "severity": 1, "message": "175", "line": 10, "column": 3, "nodeType": "168", "messageId": "169", "endLine": 10, "endColumn": 10}, {"ruleId": "166", "severity": 1, "message": "180", "line": 49, "column": 11, "nodeType": "168", "messageId": "169", "endLine": 49, "endColumn": 15}, {"ruleId": "166", "severity": 1, "message": "185", "line": 57, "column": 10, "nodeType": "168", "messageId": "169", "endLine": 57, "endColumn": 23}, {"ruleId": "181", "severity": 1, "message": "186", "line": 63, "column": 6, "nodeType": "183", "endLine": 63, "endColumn": 17, "suggestions": "187"}, {"ruleId": "166", "severity": 1, "message": "188", "line": 153, "column": 9, "nodeType": "168", "messageId": "169", "endLine": 153, "endColumn": 21}, {"ruleId": "166", "severity": 1, "message": "189", "line": 4, "column": 3, "nodeType": "168", "messageId": "169", "endLine": 4, "endColumn": 11}, {"ruleId": "166", "severity": 1, "message": "175", "line": 8, "column": 3, "nodeType": "168", "messageId": "169", "endLine": 8, "endColumn": 10}, {"ruleId": "166", "severity": 1, "message": "190", "line": 12, "column": 3, "nodeType": "168", "messageId": "169", "endLine": 12, "endColumn": 10}, {"ruleId": "166", "severity": 1, "message": "180", "line": 41, "column": 11, "nodeType": "168", "messageId": "169", "endLine": 41, "endColumn": 15}, {"ruleId": "166", "severity": 1, "message": "185", "line": 46, "column": 10, "nodeType": "168", "messageId": "169", "endLine": 46, "endColumn": 23}, "@typescript-eslint/no-unused-vars", "'PayloadAction' is defined but never used.", "Identifier", "unusedVar", "'ChartBarIcon' is defined but never used.", "'metrics' is assigned a value but never used.", "'error' is assigned a value but never used.", "'query' is assigned a value but never used.", "'ExclamationTriangleIcon' is defined but never used.", "'EyeIcon' is defined but never used.", "'CircleStackIcon' is defined but never used.", "'UsersIcon' is defined but never used.", "'ClockIcon' is defined but never used.", "'CloudIcon' is defined but never used.", "'user' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchSettings'. Either include it or remove the dependency array.", "ArrayExpression", ["191"], "'showEditModal' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["192"], "'handleUpdate' is assigned a value but never used.", "'FlagIcon' is defined but never used.", "'CogIcon' is defined but never used.", {"desc": "193", "fix": "194"}, {"desc": "195", "fix": "196"}, "Update the dependencies array to be: [fetchSettings]", {"range": "197", "text": "198"}, "Update the dependencies array to be: [activeTab, fetchData]", {"range": "199", "text": "200"}, [1787, 1789], "[fetchSettings]", [1579, 1590], "[activeTab, fetchData]"]