import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import axios from 'axios';

export interface EnvoyConfig {
  id: string;
  node_id: string;
  cluster_name: string;
  config: string;
  version: string;
  created_at: string;
  updated_at: string;
}

export interface EnvoyNode {
  id: string;
  cluster: string;
  version: string;
  last_seen: string;
  status: 'connected' | 'disconnected';
}

export interface EnvoyState {
  configs: EnvoyConfig[];
  nodes: EnvoyNode[];
  selectedConfig: EnvoyConfig | null;
  loading: boolean;
  error: string | null;
}

const initialState: EnvoyState = {
  configs: [],
  nodes: [],
  selectedConfig: null,
  loading: false,
  error: null,
};

export const fetchEnvoyConfigs = createAsyncThunk(
  'envoy/fetchConfigs',
  async () => {
    const response = await axios.get('/v1/envoy/configs');
    return response.data.configs;
  }
);

export const fetchEnvoyNodes = createAsyncThunk(
  'envoy/fetchNodes',
  async () => {
    const response = await axios.get('/v1/envoy/nodes');
    return response.data.nodes;
  }
);

export const createEnvoyConfig = createAsyncThunk(
  'envoy/createConfig',
  async (config: Omit<EnvoyConfig, 'id' | 'created_at' | 'updated_at'>) => {
    const response = await axios.post('/v1/envoy/configs', config);
    return response.data;
  }
);

export const updateEnvoyConfig = createAsyncThunk(
  'envoy/updateConfig',
  async (config: EnvoyConfig) => {
    const response = await axios.put(`/v1/envoy/configs/${config.id}`, config);
    return response.data;
  }
);

const envoySlice = createSlice({
  name: 'envoy',
  initialState,
  reducers: {
    setSelectedConfig: (state, action: PayloadAction<EnvoyConfig | null>) => {
      state.selectedConfig = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchEnvoyConfigs.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchEnvoyConfigs.fulfilled, (state, action) => {
        state.loading = false;
        state.configs = action.payload;
      })
      .addCase(fetchEnvoyConfigs.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch Envoy configs';
      })
      .addCase(fetchEnvoyNodes.fulfilled, (state, action) => {
        state.nodes = action.payload;
      })
      .addCase(createEnvoyConfig.fulfilled, (state, action) => {
        state.configs.push(action.payload);
      })
      .addCase(updateEnvoyConfig.fulfilled, (state, action) => {
        const index = state.configs.findIndex(c => c.id === action.payload.id);
        if (index !== -1) {
          state.configs[index] = action.payload;
        }
      });
  },
});

export const { setSelectedConfig } = envoySlice.actions;
export default envoySlice.reducer;
