import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { AuthProvider, useAuth, ProtectedRoute } from './auth/AuthContext';
import { LoginPage } from './auth/LoginPage';
import { Layout } from './components/Layout';
import { Dashboard } from './pages/Dashboard';
import SearchDashboard from './pages/SearchDashboard';
import DiscoveryWizard from './pages/DiscoveryWizard';
import WorkflowExecutor from './pages/WorkflowExecutor';
import EnvoyConfigEditor from './pages/EnvoyConfigEditor';
import AutoscalerDashboard from './pages/AutoscalerDashboard';
import AuditViewer from './pages/AuditViewer';
import DatabaseAdmin from './pages/DatabaseAdmin';
import AdminDashboard from './pages/AdminDashboard';
import UserManagement from './pages/UserManagement';
import UserProfile from './pages/UserProfile';
import Settings from './pages/Settings';
import FeatureFlagAdmin from './pages/FeatureFlagAdmin';
import RBACAdmin from './pages/RBACAdmin';
import './index.css';

// App Router Component (inside AuthProvider)
function AppRouter() {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-white mx-auto mb-4"></div>
          <div className="text-white text-lg">Loading CAINuro Orchestrator...</div>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <LoginPage />;
  }

  return (
    <div className="min-h-screen bg-theme-bg-container">
      <Layout>
        <Routes>
          <Route path="/" element={
            <ProtectedRoute>
              <Dashboard />
            </ProtectedRoute>
          } />
          <Route path="/dashboard" element={
            <ProtectedRoute>
              <Dashboard />
            </ProtectedRoute>
          } />
          <Route path="/search" element={
            <ProtectedRoute requiredPermission={{ action: "READ", resource: "discovery" }}>
              <SearchDashboard />
            </ProtectedRoute>
          } />
          <Route path="/discovery" element={
            <ProtectedRoute requiredPermission={{ action: "READ", resource: "discovery" }}>
              <DiscoveryWizard />
            </ProtectedRoute>
          } />
          <Route path="/workflows" element={
            <ProtectedRoute requiredPermission={{ action: "EXECUTE", resource: "workflows" }}>
              <WorkflowExecutor />
            </ProtectedRoute>
          } />
          <Route path="/envoy" element={
            <ProtectedRoute requiredPermission={{ action: "READ", resource: "envoy" }}>
              <EnvoyConfigEditor />
            </ProtectedRoute>
          } />
          <Route path="/autoscaler" element={
            <ProtectedRoute requiredPermission={{ action: "READ", resource: "autoscaler" }}>
              <AutoscalerDashboard />
            </ProtectedRoute>
          } />
          <Route path="/audit" element={
            <ProtectedRoute requiredPermission={{ action: "READ", resource: "audit" }}>
              <AuditViewer />
            </ProtectedRoute>
          } />
          <Route path="/database" element={
            <ProtectedRoute requiredPermission={{ action: "READ", resource: "database" }}>
              <DatabaseAdmin />
            </ProtectedRoute>
          } />

          {/* Admin Routes */}
          <Route path="/admin" element={
            <ProtectedRoute requiredPermission={{ action: "READ", resource: "admin" }}>
              <AdminDashboard />
            </ProtectedRoute>
          } />
          <Route path="/admin/users" element={
            <ProtectedRoute requiredPermission={{ action: "READ", resource: "admin" }}>
              <UserManagement />
            </ProtectedRoute>
          } />
          <Route path="/admin/settings" element={
            <ProtectedRoute requiredPermission={{ action: "READ", resource: "admin" }}>
              <Settings />
            </ProtectedRoute>
          } />
          <Route path="/admin/featureflags" element={
            <ProtectedRoute requiredPermission={{ action: "READ", resource: "admin" }}>
              <FeatureFlagAdmin />
            </ProtectedRoute>
          } />
          <Route path="/admin/rbac" element={
            <ProtectedRoute requiredPermission={{ action: "READ", resource: "admin" }}>
              <RBACAdmin />
            </ProtectedRoute>
          } />

          {/* User Routes */}
          <Route path="/profile" element={
            <ProtectedRoute>
              <UserProfile />
            </ProtectedRoute>
          } />
        </Routes>
      </Layout>
    </div>
  );
}

// Main App Component
function App() {
  return (
    <AuthProvider>
      <AppRouter />
    </AuthProvider>
  );
}

export default App;
