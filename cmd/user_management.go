package main

import (
	"crypto/rand"
	"encoding/hex"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"golang.org/x/crypto/bcrypt"
)

// User represents a user in the system
type User struct {
	ID           string                 `json:"id"`
	Username     string                 `json:"username"`
	Email        string                 `json:"email"`
	Name         string                 `json:"name"`
	PasswordHash string                 `json:"-"` // Never expose password hash
	Roles        []string               `json:"roles"`
	Groups       []string               `json:"groups"`
	Permissions  []string               `json:"permissions"`
	Active       bool                   `json:"active"`
	CreatedAt    time.Time              `json:"created_at"`
	UpdatedAt    time.Time              `json:"updated_at"`
	LastLogin    *time.Time             `json:"last_login,omitempty"`
	Preferences  map[string]interface{} `json:"preferences"`
	Metadata     map[string]interface{} `json:"metadata"`
}

// UserSession represents an active user session
type UserSession struct {
	ID        string    `json:"id"`
	UserID    string    `json:"user_id"`
	Token     string    `json:"token"`
	ExpiresAt time.Time `json:"expires_at"`
	CreatedAt time.Time `json:"created_at"`
	IPAddress string    `json:"ip_address"`
	UserAgent string    `json:"user_agent"`
}

// Global user storage (in production, this would be a database)
var (
	users    = make(map[string]*User)
	sessions = make(map[string]*UserSession)
)

// Initialize default admin user
func initializeUserManagement() {
	// Create default admin user
	adminPasswordHash, _ := bcrypt.GenerateFromPassword([]byte("admin"), bcrypt.DefaultCost)

	adminUser := &User{
		ID:           "admin",
		Username:     "admin",
		Email:        "<EMAIL>",
		Name:         "CAINuro Administrator",
		PasswordHash: string(adminPasswordHash),
		Roles:        []string{"admin", "operator", "viewer"},
		Groups:       []string{"cainuro-admins", "cainuro-operators", "cainuro-users"},
		Permissions: []string{
			"*", // Admin has all permissions
		},
		Active:    true,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		Preferences: map[string]interface{}{
			"theme":    "dark",
			"timezone": "UTC",
			"language": "en",
		},
		Metadata: map[string]interface{}{
			"created_by": "system",
			"source":     "default",
		},
	}

	users[adminUser.Username] = adminUser
	users[adminUser.ID] = adminUser // Allow lookup by both username and ID

	// Create a demo user
	userPasswordHash, _ := bcrypt.GenerateFromPassword([]byte("user"), bcrypt.DefaultCost)

	demoUser := &User{
		ID:           "demo_user",
		Username:     "user",
		Email:        "<EMAIL>",
		Name:         "CAINuro Demo User",
		PasswordHash: string(userPasswordHash),
		Roles:        []string{"viewer", "discovery_user"},
		Groups:       []string{"cainuro-users"},
		Permissions: []string{
			"discovery:read",
			"workflows:read",
			"audit:read",
			"metrics:read",
		},
		Active:    true,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		Preferences: map[string]interface{}{
			"theme":    "light",
			"timezone": "UTC",
			"language": "en",
		},
		Metadata: map[string]interface{}{
			"created_by": "system",
			"source":     "demo",
		},
	}

	users[demoUser.Username] = demoUser
	users[demoUser.ID] = demoUser
}

// Login with username and password
func loginWithPassword(c *fiber.Ctx) error {
	var req struct {
		Username string `json:"username"`
		Password string `json:"password"`
	}

	if err := c.BodyParser(&req); err != nil {
		return c.Status(400).JSON(fiber.Map{"error": "Invalid request body"})
	}

	// Find user
	user, exists := users[req.Username]
	if !exists {
		return c.Status(401).JSON(fiber.Map{"error": "Invalid credentials"})
	}

	// Check password
	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.Password)); err != nil {
		return c.Status(401).JSON(fiber.Map{"error": "Invalid credentials"})
	}

	// Check if user is active
	if !user.Active {
		return c.Status(401).JSON(fiber.Map{"error": "Account is disabled"})
	}

	// Generate session token
	token := generateSessionToken()

	// Create session
	session := &UserSession{
		ID:        generateSessionID(),
		UserID:    user.ID,
		Token:     token,
		ExpiresAt: time.Now().Add(24 * time.Hour), // 24 hour session
		CreatedAt: time.Now(),
		IPAddress: c.IP(),
		UserAgent: c.Get("User-Agent"),
	}

	sessions[token] = session

	// Update last login
	now := time.Now()
	user.LastLogin = &now
	user.UpdatedAt = now

	// Set HTTP-only cookie
	c.Cookie(&fiber.Cookie{
		Name:     "auth_token",
		Value:    token,
		Expires:  session.ExpiresAt,
		HTTPOnly: true,
		Secure:   false, // Set to true in production with HTTPS
		SameSite: "Lax",
	})

	// Return user info (without sensitive data)
	return c.JSON(fiber.Map{
		"user": sanitizeUser(user),
		"session": fiber.Map{
			"expires_at": session.ExpiresAt,
		},
		"message": "Login successful",
	})
}

// Get current user info
func getCurrentUser(c *fiber.Ctx) error {
	user := c.Locals("user").(*User)
	return c.JSON(sanitizeUser(user))
}

// Change password
func changePassword(c *fiber.Ctx) error {
	user := c.Locals("user").(*User)

	var req struct {
		CurrentPassword string `json:"current_password"`
		NewPassword     string `json:"new_password"`
	}

	if err := c.BodyParser(&req); err != nil {
		return c.Status(400).JSON(fiber.Map{"error": "Invalid request body"})
	}

	// Verify current password
	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.CurrentPassword)); err != nil {
		return c.Status(401).JSON(fiber.Map{"error": "Current password is incorrect"})
	}

	// Validate new password
	if len(req.NewPassword) < 4 {
		return c.Status(400).JSON(fiber.Map{"error": "New password must be at least 4 characters"})
	}

	// Hash new password
	newPasswordHash, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		return c.Status(500).JSON(fiber.Map{"error": "Failed to hash password"})
	}

	// Update password
	user.PasswordHash = string(newPasswordHash)
	user.UpdatedAt = time.Now()

	return c.JSON(fiber.Map{
		"message": "Password changed successfully",
	})
}

// Logout
func logout(c *fiber.Ctx) error {
	token := c.Cookies("auth_token")
	if token != "" {
		delete(sessions, token)
	}

	// Clear cookie
	c.Cookie(&fiber.Cookie{
		Name:     "auth_token",
		Value:    "",
		Expires:  time.Now().Add(-time.Hour),
		HTTPOnly: true,
	})

	return c.JSON(fiber.Map{
		"message": "Logged out successfully",
	})
}

// List all users (admin only)
func listUsers(c *fiber.Ctx) error {
	user := c.Locals("user").(*User)

	// Check if user has admin role
	if !hasRole(user, "admin") {
		return c.Status(403).JSON(fiber.Map{"error": "Admin access required"})
	}

	var userList []*User
	seen := make(map[string]bool)

	for _, u := range users {
		if !seen[u.ID] {
			userList = append(userList, u)
			seen[u.ID] = true
		}
	}

	// Sanitize users
	var sanitizedUsers []fiber.Map
	for _, u := range userList {
		sanitizedUsers = append(sanitizedUsers, sanitizeUser(u))
	}

	return c.JSON(fiber.Map{
		"users": sanitizedUsers,
		"total": len(sanitizedUsers),
	})
}

// Create new user (admin only)
func createUser(c *fiber.Ctx) error {
	user := c.Locals("user").(*User)

	// Check if user has admin role
	if !hasRole(user, "admin") {
		return c.Status(403).JSON(fiber.Map{"error": "Admin access required"})
	}

	var req struct {
		Username string   `json:"username"`
		Email    string   `json:"email"`
		Name     string   `json:"name"`
		Password string   `json:"password"`
		Roles    []string `json:"roles"`
		Groups   []string `json:"groups"`
	}

	if err := c.BodyParser(&req); err != nil {
		return c.Status(400).JSON(fiber.Map{"error": "Invalid request body"})
	}

	// Validate required fields
	if req.Username == "" || req.Email == "" || req.Password == "" {
		return c.Status(400).JSON(fiber.Map{"error": "Username, email, and password are required"})
	}

	// Check if user already exists
	if _, exists := users[req.Username]; exists {
		return c.Status(409).JSON(fiber.Map{"error": "Username already exists"})
	}

	// Hash password
	passwordHash, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return c.Status(500).JSON(fiber.Map{"error": "Failed to hash password"})
	}

	// Create user
	newUser := &User{
		ID:           generateUserID(),
		Username:     req.Username,
		Email:        req.Email,
		Name:         req.Name,
		PasswordHash: string(passwordHash),
		Roles:        req.Roles,
		Groups:       req.Groups,
		Permissions:  generatePermissionsFromRoles(req.Roles),
		Active:       true,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
		Preferences: map[string]interface{}{
			"theme":    "dark",
			"timezone": "UTC",
			"language": "en",
		},
		Metadata: map[string]interface{}{
			"created_by": user.Username,
		},
	}

	users[newUser.Username] = newUser
	users[newUser.ID] = newUser

	return c.Status(201).JSON(sanitizeUser(newUser))
}

// Get single user (admin only)
func getUser(c *fiber.Ctx) error {
	user := c.Locals("user").(*User)

	// Check if user has admin role
	if !hasRole(user, "admin") {
		return c.Status(403).JSON(fiber.Map{"error": "Admin access required"})
	}

	userID := c.Params("id")
	if userID == "" {
		return c.Status(400).JSON(fiber.Map{"error": "User ID is required"})
	}

	// Find user by ID or username
	var targetUser *User
	for _, u := range users {
		if u.ID == userID || u.Username == userID {
			targetUser = u
			break
		}
	}

	if targetUser == nil {
		return c.Status(404).JSON(fiber.Map{"error": "User not found"})
	}

	return c.JSON(sanitizeUser(targetUser))
}

// Update user (admin only)
func updateUser(c *fiber.Ctx) error {
	user := c.Locals("user").(*User)

	// Check if user has admin role
	if !hasRole(user, "admin") {
		return c.Status(403).JSON(fiber.Map{"error": "Admin access required"})
	}

	userID := c.Params("id")
	if userID == "" {
		return c.Status(400).JSON(fiber.Map{"error": "User ID is required"})
	}

	var req struct {
		Email    string   `json:"email"`
		Name     string   `json:"name"`
		Roles    []string `json:"roles"`
		Groups   []string `json:"groups"`
		Active   *bool    `json:"active"`
		Password string   `json:"password,omitempty"`
	}

	if err := c.BodyParser(&req); err != nil {
		return c.Status(400).JSON(fiber.Map{"error": "Invalid request body"})
	}

	// Find user by ID or username
	var targetUser *User
	var userKey string
	for key, u := range users {
		if u.ID == userID || u.Username == userID {
			targetUser = u
			userKey = key
			break
		}
	}

	if targetUser == nil {
		return c.Status(404).JSON(fiber.Map{"error": "User not found"})
	}

	// Prevent admin from deactivating themselves
	if targetUser.Username == user.Username && req.Active != nil && !*req.Active {
		return c.Status(400).JSON(fiber.Map{"error": "Cannot deactivate your own account"})
	}

	// Update fields
	if req.Email != "" {
		targetUser.Email = req.Email
	}
	if req.Name != "" {
		targetUser.Name = req.Name
	}
	if req.Roles != nil {
		targetUser.Roles = req.Roles
		targetUser.Permissions = generatePermissionsFromRoles(req.Roles)
	}
	if req.Groups != nil {
		targetUser.Groups = req.Groups
	}
	if req.Active != nil {
		targetUser.Active = *req.Active
	}
	if req.Password != "" {
		// Hash new password
		passwordHash, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
		if err != nil {
			return c.Status(500).JSON(fiber.Map{"error": "Failed to hash password"})
		}
		targetUser.PasswordHash = string(passwordHash)
	}

	targetUser.UpdatedAt = time.Now()

	// Update in both maps
	users[userKey] = targetUser
	if userKey != targetUser.Username {
		users[targetUser.Username] = targetUser
	}
	if userKey != targetUser.ID {
		users[targetUser.ID] = targetUser
	}

	return c.JSON(sanitizeUser(targetUser))
}

// Delete user (admin only)
func deleteUser(c *fiber.Ctx) error {
	user := c.Locals("user").(*User)

	// Check if user has admin role
	if !hasRole(user, "admin") {
		return c.Status(403).JSON(fiber.Map{"error": "Admin access required"})
	}

	userID := c.Params("id")
	if userID == "" {
		return c.Status(400).JSON(fiber.Map{"error": "User ID is required"})
	}

	// Find user by ID or username
	var targetUser *User
	for _, u := range users {
		if u.ID == userID || u.Username == userID {
			targetUser = u
			break
		}
	}

	if targetUser == nil {
		return c.Status(404).JSON(fiber.Map{"error": "User not found"})
	}

	// Prevent admin from deleting themselves
	if targetUser.Username == user.Username {
		return c.Status(400).JSON(fiber.Map{"error": "Cannot delete your own account"})
	}

	// Remove from all maps
	delete(users, targetUser.ID)
	delete(users, targetUser.Username)
	delete(users, targetUser.Email)

	// Remove any active sessions for this user
	for token, session := range sessions {
		if session.UserID == targetUser.ID {
			delete(sessions, token)
		}
	}

	return c.JSON(fiber.Map{
		"message":    "User deleted successfully",
		"deleted_at": time.Now(),
		"user_id":    targetUser.ID,
		"username":   targetUser.Username,
	})
}

// Helper functions

func generateSessionToken() string {
	bytes := make([]byte, 32)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

func generateSessionID() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

func generateUserID() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

func sanitizeUser(user *User) fiber.Map {
	return fiber.Map{
		"id":          user.ID,
		"username":    user.Username,
		"email":       user.Email,
		"name":        user.Name,
		"roles":       user.Roles,
		"groups":      user.Groups,
		"permissions": user.Permissions,
		"active":      user.Active,
		"created_at":  user.CreatedAt,
		"updated_at":  user.UpdatedAt,
		"last_login":  user.LastLogin,
		"preferences": user.Preferences,
		"metadata":    user.Metadata,
	}
}

func hasRole(user *User, role string) bool {
	for _, r := range user.Roles {
		if r == role {
			return true
		}
	}
	return false
}

// Enhanced authorization check handler for password-based auth
func postAuthCheckPassword(c *fiber.Ctx) error {
	var req struct {
		Action   string                 `json:"action"`
		Resource string                 `json:"resource"`
		Context  map[string]interface{} `json:"context,omitempty"`
	}

	if err := c.BodyParser(&req); err != nil {
		return c.Status(400).JSON(fiber.Map{"error": "Invalid request body"})
	}

	user := c.Locals("user").(*User)
	if user == nil {
		return c.JSON(fiber.Map{
			"decision": fiber.Map{
				"allowed": false,
				"reason":  "Not authenticated",
			},
			"action":   req.Action,
			"resource": req.Resource,
		})
	}

	// Check permissions
	allowed := checkUserPermission(user, req.Action, req.Resource)
	reason := "Allowed by policy"
	if !allowed {
		reason = "Access denied by policy"
	}

	return c.JSON(fiber.Map{
		"decision": fiber.Map{
			"allowed": allowed,
			"reason":  reason,
		},
		"action":   req.Action,
		"resource": req.Resource,
		"subject": fiber.Map{
			"user":   user.Email,
			"groups": user.Groups,
			"roles":  user.Roles,
		},
	})
}

// Check user permissions
func checkUserPermission(user *User, action, resource string) bool {
	if user == nil {
		return false
	}

	// Admin users with "*" permission have access to everything
	if contains(user.Permissions, "*") {
		return true
	}

	// Check for exact permission match
	exactPermission := resource + ":" + strings.ToLower(action)
	if contains(user.Permissions, exactPermission) {
		return true
	}

	// Check for wildcard resource permissions
	wildcardPermission := "*:" + strings.ToLower(action)
	if contains(user.Permissions, wildcardPermission) {
		return true
	}

	// Check for resource-level permissions
	resourcePermission := resource + ":*"
	if contains(user.Permissions, resourcePermission) {
		return true
	}

	// Check role-based permissions
	if hasRole(user, "admin") {
		return true
	}

	if hasRole(user, "operator") && (strings.ToLower(action) == "read" || strings.ToLower(action) == "execute") {
		return true
	}

	if hasRole(user, "viewer") && strings.ToLower(action) == "read" {
		return true
	}

	// Check group-based permissions
	for _, group := range user.Groups {
		if group == "cainuro-admins" {
			return true
		}
		if group == "cainuro-operators" && (strings.ToLower(action) == "read" || strings.ToLower(action) == "execute") {
			return true
		}
		if group == "cainuro-users" && strings.ToLower(action) == "read" {
			return true
		}
	}

	return false
}

// contains function is defined in advanced_resolver.go to avoid duplication

func generatePermissionsFromRoles(roles []string) []string {
	permissions := []string{}

	for _, role := range roles {
		switch role {
		case "admin":
			permissions = append(permissions, "*")
		case "operator":
			permissions = append(permissions, "*:read", "*:execute", "workflows:create", "workflows:update")
		case "viewer":
			permissions = append(permissions, "*:read")
		case "discovery_user":
			permissions = append(permissions, "discovery:read", "discovery:search")
		case "workflow_user":
			permissions = append(permissions, "workflows:read", "workflows:execute")
		}
	}

	return permissions
}

// Authentication middleware for password-based auth
func passwordAuthMiddleware() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// Skip auth for public endpoints
		if strings.HasPrefix(c.Path(), "/v1/auth/login") ||
			strings.HasPrefix(c.Path(), "/v1/auth/config") ||
			c.Path() == "/health" ||
			strings.HasPrefix(c.Path(), "/static/") ||
			c.Path() == "/" ||
			c.Path() == "/favicon.ico" {
			return c.Next()
		}

		// Get token from cookie
		token := c.Cookies("auth_token")
		if token == "" {
			return c.Status(401).JSON(fiber.Map{
				"code":  "UNAUTHENTICATED",
				"error": "Authentication required",
			})
		}

		// Find session
		session, exists := sessions[token]
		if !exists {
			return c.Status(401).JSON(fiber.Map{
				"code":  "UNAUTHENTICATED",
				"error": "Invalid session",
			})
		}

		// Check if session is expired
		if time.Now().After(session.ExpiresAt) {
			delete(sessions, token)
			return c.Status(401).JSON(fiber.Map{
				"code":  "UNAUTHENTICATED",
				"error": "Session expired",
			})
		}

		// Find user
		user, exists := users[session.UserID]
		if !exists || !user.Active {
			return c.Status(401).JSON(fiber.Map{
				"code":  "UNAUTHENTICATED",
				"error": "User not found or inactive",
			})
		}

		// Store user in context
		c.Locals("user", user)

		return c.Next()
	}
}
