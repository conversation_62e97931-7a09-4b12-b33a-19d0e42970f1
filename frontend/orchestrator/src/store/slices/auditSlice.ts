import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import axios from 'axios';

export interface AuditEvent {
  id: string;
  timestamp: string;
  user_id: string;
  action: string;
  resource: string;
  resource_id: string;
  details: Record<string, any>;
  ip_address: string;
  user_agent: string;
  success: boolean;
  error_msg?: string;
}

export interface AuditQuery {
  user_id?: string;
  action?: string;
  resource?: string;
  resource_id?: string;
  start_time?: string;
  end_time?: string;
  limit?: number;
}

export interface AuditState {
  events: AuditEvent[];
  loading: boolean;
  error: string | null;
  totalCount: number;
  query: AuditQuery;
}

const initialState: AuditState = {
  events: [],
  loading: false,
  error: null,
  totalCount: 0,
  query: {},
};

export const fetchAuditEvents = createAsyncThunk(
  'audit/fetchEvents',
  async (query: AuditQuery) => {
    const response = await axios.post('/v1/audit/query', query);
    return response.data;
  }
);

const auditSlice = createSlice({
  name: 'audit',
  initialState,
  reducers: {
    setQuery: (state, action: PayloadAction<AuditQuery>) => {
      state.query = action.payload;
    },
    clearEvents: (state) => {
      state.events = [];
      state.totalCount = 0;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchAuditEvents.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAuditEvents.fulfilled, (state, action) => {
        state.loading = false;
        state.events = action.payload.events;
        state.totalCount = action.payload.total_count;
      })
      .addCase(fetchAuditEvents.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch audit events';
      });
  },
});

export const { setQuery, clearEvents } = auditSlice.actions;
export default auditSlice.reducer;
