{"version": 3, "file": "static/js/488.322e7791.chunk.js", "mappings": "maAAA,IAAIA,EAAEC,EAAEC,EAAEC,EAAEC,EAAEC,GAAG,EAAEC,EAAE,SAASN,GAAGO,iBAAiB,YAAY,SAASN,GAAGA,EAAEO,YAAYH,EAAEJ,EAAEQ,UAAUT,EAAEC,GAAG,IAAG,EAAG,EAAES,EAAE,WAAW,OAAOC,OAAOC,aAAaA,YAAYC,kBAAkBD,YAAYC,iBAAiB,cAAc,EAAE,EAAEC,EAAE,WAAW,IAAId,EAAEU,IAAI,OAAOV,GAAGA,EAAEe,iBAAiB,CAAC,EAAEC,EAAE,SAAShB,EAAEC,GAAG,IAAIC,EAAEQ,IAAIP,EAAE,WAA8J,OAAnJE,GAAG,EAAEF,EAAE,qBAAqBD,IAAIe,SAASC,cAAcJ,IAAI,EAAEX,EAAE,YAAYc,SAASE,aAAahB,EAAE,UAAUD,EAAEkB,OAAOjB,EAAED,EAAEkB,KAAKC,QAAQ,KAAK,OAAa,CAACC,KAAKtB,EAAEuB,WAAM,IAAStB,GAAG,EAAEA,EAAEuB,OAAO,OAAOC,MAAM,EAAEC,QAAQ,GAAGC,GAAG,MAAMC,OAAOC,KAAKC,MAAM,KAAKF,OAAOG,KAAKC,MAAM,cAAcD,KAAKE,UAAU,MAAMC,eAAe/B,EAAE,EAAEgC,EAAE,SAASnC,EAAEC,EAAEC,GAAG,IAAI,GAAGkC,oBAAoBC,oBAAoBC,SAAStC,GAAG,CAAC,IAAIG,EAAE,IAAIiC,qBAAqB,SAASpC,GAAGuC,QAAQC,UAAUC,MAAM,WAAWxC,EAAED,EAAE0C,aAAa,GAAG,IAAI,OAAOvC,EAAEwC,QAAQC,OAAOC,OAAO,CAACzB,KAAKpB,EAAE8C,UAAS,GAAI5C,GAAG,CAAC,IAAIC,CAAC,CAAC,CAAC,MAAMH,GAAG,CAAC,EAAE+C,EAAE,SAAS/C,EAAEC,EAAEC,EAAEC,GAAG,IAAIC,EAAEC,EAAE,OAAO,SAASC,GAAGL,EAAEsB,OAAO,IAAIjB,GAAGH,MAAME,EAAEJ,EAAEsB,OAAOnB,GAAG,UAAK,IAASA,KAAKA,EAAEH,EAAEsB,MAAMtB,EAAEwB,MAAMpB,EAAEJ,EAAEuB,OAAO,SAASxB,EAAEC,GAAG,OAAOD,EAAEC,EAAE,GAAG,OAAOD,EAAEC,EAAE,GAAG,oBAAoB,MAAM,CAApE,CAAsEA,EAAEsB,MAAMrB,GAAGF,EAAEC,GAAG,CAAC,EAAE+C,EAAE,SAAShD,GAAGiD,uBAAuB,WAAW,OAAOA,uBAAuB,WAAW,OAAOjD,GAAG,GAAG,GAAG,EAAEkD,EAAE,SAASlD,GAAG,IAAIC,EAAE,SAASA,GAAG,aAAaA,EAAEmB,MAAM,WAAWH,SAASkC,iBAAiBnD,EAAEC,EAAE,EAAEM,iBAAiB,mBAAmBN,GAAE,GAAIM,iBAAiB,WAAWN,GAAE,EAAG,EAAEmD,EAAE,SAASpD,GAAG,IAAIC,GAAE,EAAG,OAAO,SAASC,GAAGD,IAAID,EAAEE,GAAGD,GAAE,EAAG,CAAC,EAAEoD,GAAG,EAAEC,EAAE,WAAW,MAAM,WAAWrC,SAASkC,iBAAiBlC,SAASC,aAAa,IAAI,CAAC,EAAEqC,EAAE,SAASvD,GAAG,WAAWiB,SAASkC,iBAAiBE,GAAG,IAAIA,EAAE,qBAAqBrD,EAAEoB,KAAKpB,EAAES,UAAU,EAAE+C,IAAI,EAAEC,EAAE,WAAWlD,iBAAiB,mBAAmBgD,GAAE,GAAIhD,iBAAiB,qBAAqBgD,GAAE,EAAG,EAAEC,EAAE,WAAWE,oBAAoB,mBAAmBH,GAAE,GAAIG,oBAAoB,qBAAqBH,GAAE,EAAG,EAAEI,EAAE,WAAW,OAAON,EAAE,IAAIA,EAAEC,IAAIG,IAAInD,GAAG,WAAWsD,YAAY,WAAWP,EAAEC,IAAIG,GAAG,GAAG,EAAE,KAAK,CAAC,mBAAII,GAAkB,OAAOR,CAAC,EAAE,EAAES,EAAE,SAAS9D,GAAGiB,SAASC,aAAaX,iBAAiB,sBAAsB,WAAW,OAAOP,GAAG,IAAG,GAAIA,GAAG,EAAE+D,EAAE,CAAC,KAAK,KAAKC,EAAE,SAAShE,EAAEC,GAAGA,EAAEA,GAAG,CAAC,EAAE6D,GAAG,WAAW,IAAI5D,EAAEC,EAAEwD,IAAIvD,EAAEY,EAAE,OAAOX,EAAE8B,EAAE,SAAS,SAASnC,GAAGA,EAAEiE,SAAS,SAASjE,GAAG,2BAA2BA,EAAEsB,OAAOjB,EAAE6D,aAAalE,EAAEmE,UAAUhE,EAAE0D,kBAAkBzD,EAAEmB,MAAMQ,KAAKqC,IAAIpE,EAAEmE,UAAUrD,IAAI,GAAGV,EAAEsB,QAAQ2C,KAAKrE,GAAGE,GAAE,IAAK,GAAG,IAAIG,IAAIH,EAAE6C,EAAE/C,EAAEI,EAAE2D,EAAE9D,EAAEqE,kBAAkBhE,GAAG,SAASH,GAAGC,EAAEY,EAAE,OAAOd,EAAE6C,EAAE/C,EAAEI,EAAE2D,EAAE9D,EAAEqE,kBAAkBtB,GAAG,WAAW5C,EAAEmB,MAAMX,YAAYkB,MAAM3B,EAAEM,UAAUP,GAAE,EAAG,GAAG,IAAI,GAAG,EAAEqE,EAAE,CAAC,GAAG,KAAKC,EAAE,SAASxE,EAAEC,GAAGA,EAAEA,GAAG,CAAC,EAAE+D,EAAEZ,GAAG,WAAW,IAAIlD,EAAEC,EAAEa,EAAE,MAAM,GAAGZ,EAAE,EAAEC,EAAE,GAAGK,EAAE,SAASV,GAAGA,EAAEiE,SAAS,SAASjE,GAAG,IAAIA,EAAEyE,eAAe,CAAC,IAAIxE,EAAEI,EAAE,GAAGH,EAAEG,EAAEA,EAAEqE,OAAO,GAAGtE,GAAGJ,EAAEmE,UAAUjE,EAAEiE,UAAU,KAAKnE,EAAEmE,UAAUlE,EAAEkE,UAAU,KAAK/D,GAAGJ,EAAEuB,MAAMlB,EAAEgE,KAAKrE,KAAKI,EAAEJ,EAAEuB,MAAMlB,EAAE,CAACL,GAAG,CAAC,IAAII,EAAED,EAAEoB,QAAQpB,EAAEoB,MAAMnB,EAAED,EAAEuB,QAAQrB,EAAEH,IAAI,EAAEY,EAAEqB,EAAE,eAAezB,GAAGI,IAAIZ,EAAE6C,EAAE/C,EAAEG,EAAEoE,EAAEtE,EAAEqE,kBAAkBpB,GAAG,WAAWxC,EAAEI,EAAE6D,eAAezE,GAAE,EAAG,IAAII,GAAG,WAAWF,EAAE,EAAED,EAAEa,EAAE,MAAM,GAAGd,EAAE6C,EAAE/C,EAAEG,EAAEoE,EAAEtE,EAAEqE,kBAAkBtB,GAAG,WAAW,OAAO9C,GAAG,GAAG,IAAI0D,WAAW1D,EAAE,GAAG,IAAI,EAAE0E,EAAE,CAACC,SAAQ,EAAGC,SAAQ,GAAIC,EAAE,IAAIlD,KAAKmD,EAAE,SAAS7E,EAAEC,GAAGJ,IAAIA,EAAEI,EAAEH,EAAEE,EAAED,EAAE,IAAI2B,KAAKoD,EAAEvB,qBAAqBwB,IAAI,EAAEA,EAAE,WAAW,GAAGjF,GAAG,GAAGA,EAAEC,EAAE6E,EAAE,CAAC,IAAI3E,EAAE,CAAC+E,UAAU,cAAc7D,KAAKtB,EAAEoB,KAAKgE,OAAOpF,EAAEoF,OAAOC,WAAWrF,EAAEqF,WAAWlB,UAAUnE,EAAES,UAAU6E,gBAAgBtF,EAAES,UAAUR,GAAGE,EAAE8D,SAAS,SAASjE,GAAGA,EAAEI,EAAE,IAAID,EAAE,EAAE,CAAC,EAAEoF,EAAE,SAASvF,GAAG,GAAGA,EAAEqF,WAAW,CAAC,IAAIpF,GAAGD,EAAES,UAAU,KAAK,IAAIoB,KAAKjB,YAAYkB,OAAO9B,EAAES,UAAU,eAAeT,EAAEoB,KAAK,SAASpB,EAAEC,GAAG,IAAIC,EAAE,WAAW8E,EAAEhF,EAAEC,GAAGG,GAAG,EAAED,EAAE,WAAWC,GAAG,EAAEA,EAAE,WAAWsD,oBAAoB,YAAYxD,EAAE0E,GAAGlB,oBAAoB,gBAAgBvD,EAAEyE,EAAE,EAAErE,iBAAiB,YAAYL,EAAE0E,GAAGrE,iBAAiB,gBAAgBJ,EAAEyE,EAAE,CAAhO,CAAkO3E,EAAED,GAAGgF,EAAE/E,EAAED,EAAE,CAAC,EAAEiF,EAAE,SAASjF,GAAG,CAAC,YAAY,UAAU,aAAa,eAAeiE,SAAS,SAAShE,GAAG,OAAOD,EAAEC,EAAEsF,EAAEX,EAAE,GAAG,EAAEY,EAAE,CAAC,IAAI,KAAKC,EAAE,SAASvF,EAAEE,GAAGA,EAAEA,GAAG,CAAC,EAAE0D,GAAG,WAAW,IAAIzD,EAAEK,EAAEiD,IAAI7C,EAAEE,EAAE,OAAOgC,EAAE,SAAShD,GAAGA,EAAEmE,UAAUzD,EAAEmD,kBAAkB/C,EAAES,MAAMvB,EAAEsF,gBAAgBtF,EAAEmE,UAAUrD,EAAEY,QAAQ2C,KAAKrE,GAAGK,GAAE,GAAI,EAAEgD,EAAE,SAASrD,GAAGA,EAAEiE,QAAQjB,EAAE,EAAEM,EAAEnB,EAAE,cAAckB,GAAGhD,EAAE0C,EAAE7C,EAAEY,EAAE0E,EAAEpF,EAAEkE,kBAAkBhB,GAAGJ,EAAEE,GAAG,WAAWC,EAAEC,EAAEqB,eAAerB,EAAEY,YAAY,KAAKZ,GAAGhD,GAAG,WAAW,IAAIA,EAAEQ,EAAEE,EAAE,OAAOX,EAAE0C,EAAE7C,EAAEY,EAAE0E,EAAEpF,EAAEkE,kBAAkBnE,EAAE,GAAGF,GAAG,EAAED,EAAE,KAAKiF,EAAE1E,kBAAkBD,EAAE0C,EAAE7C,EAAEkE,KAAK/D,GAAG4E,GAAG,GAAG,GAAG,EAAEQ,EAAE,EAAEC,EAAE,IAAIC,EAAE,EAAEC,EAAE,SAAS7F,GAAGA,EAAEiE,SAAS,SAASjE,GAAGA,EAAE8F,gBAAgBH,EAAE5D,KAAKgE,IAAIJ,EAAE3F,EAAE8F,eAAeF,EAAE7D,KAAKqC,IAAIwB,EAAE5F,EAAE8F,eAAeJ,EAAEE,GAAGA,EAAED,GAAG,EAAE,EAAE,EAAE,GAAG,EAAEK,EAAE,WAAW,OAAO5F,EAAEsF,EAAE9E,YAAYqF,kBAAkB,CAAC,EAAEC,EAAE,WAAW,qBAAqBtF,aAAaR,IAAIA,EAAE+B,EAAE,QAAQ0D,EAAE,CAACzE,KAAK,QAAQ0B,UAAS,EAAGqD,kBAAkB,IAAI,EAAEC,EAAE,CAAC,IAAI,KAAKC,EAAE,EAAEC,EAAE,WAAW,OAAON,IAAIK,CAAC,EAAEE,EAAE,GAAGC,EAAE,CAAC,EAAEC,EAAE,SAASzG,GAAG,IAAIC,EAAEsG,EAAEA,EAAE7B,OAAO,GAAGxE,EAAEsG,EAAExG,EAAE8F,eAAe,GAAG5F,GAAGqG,EAAE7B,OAAO,IAAI1E,EAAE0G,SAASzG,EAAE0G,QAAQ,CAAC,GAAGzG,EAAEA,EAAEwB,QAAQ2C,KAAKrE,GAAGE,EAAEyG,QAAQ5E,KAAKqC,IAAIlE,EAAEyG,QAAQ3G,EAAE0G,cAAc,CAAC,IAAIvG,EAAE,CAACwB,GAAG3B,EAAE8F,cAAca,QAAQ3G,EAAE0G,SAAShF,QAAQ,CAAC1B,IAAIwG,EAAErG,EAAEwB,IAAIxB,EAAEoG,EAAElC,KAAKlE,EAAE,CAACoG,EAAEK,MAAM,SAAS5G,EAAEC,GAAG,OAAOA,EAAE0G,QAAQ3G,EAAE2G,OAAO,IAAIJ,EAAEM,OAAO,IAAI5C,SAAS,SAASjE,UAAUwG,EAAExG,EAAE2B,GAAG,GAAG,CAAC,EAAEmF,EAAE,SAAS9G,EAAEC,GAAGA,EAAEA,GAAG,CAAC,EAAE6D,GAAG,WAAW,IAAI5D,EAAEgG,IAAI,IAAI/F,EAAEC,EAAEY,EAAE,OAAOX,EAAE,SAASL,GAAGA,EAAEiE,SAAS,SAASjE,GAAIA,EAAE8F,eAAeW,EAAEzG,GAAG,gBAAgBA,EAAEmF,YAAcoB,EAAEQ,MAAM,SAAS9G,GAAG,OAAOA,EAAEyB,QAAQqF,MAAM,SAAS9G,GAAG,OAAOD,EAAE0G,WAAWzG,EAAEyG,UAAU1G,EAAEmE,YAAYlE,EAAEkE,SAAS,GAAG,KAAKsC,EAAEzG,EAAG,IAAI,IAAIC,EAAEC,GAAGD,EAAE8B,KAAKgE,IAAIQ,EAAE7B,OAAO,EAAE3C,KAAKC,MAAMsE,IAAI,KAAKC,EAAEtG,IAAIC,GAAGA,EAAEyG,UAAUvG,EAAEmB,QAAQnB,EAAEmB,MAAMrB,EAAEyG,QAAQvG,EAAEsB,QAAQxB,EAAEwB,QAAQvB,IAAI,EAAEO,EAAEyB,EAAE,QAAQ9B,EAAE,CAAC8F,kBAAkB,QAAQjG,EAAED,EAAEkG,yBAAoB,IAASjG,EAAEA,EAAE,KAAKC,EAAE4C,EAAE/C,EAAEI,EAAEgG,EAAEnG,EAAEqE,kBAAkB5D,IAAI,2BAA2BC,QAAQ,kBAAkBqG,uBAAuBC,WAAWvG,EAAEiC,QAAQ,CAACvB,KAAK,cAAc0B,UAAS,IAAKI,GAAG,WAAW7C,EAAEK,EAAEiE,eAAevE,EAAEmB,MAAM,GAAG+E,IAAI,IAAIlG,EAAEmB,MAAM,EAAEnB,EAAEsB,QAAQ,IAAIvB,GAAE,EAAG,IAAIG,GAAG,WAAWiG,EAAE,GAAGF,EAAEL,IAAI5F,EAAEY,EAAE,OAAOb,EAAE4C,EAAE/C,EAAEI,EAAEgG,EAAEnG,EAAEqE,iBAAiB,IAAI,GAAG,EAAE4C,EAAE,CAAC,KAAK,KAAKC,EAAE,CAAC,EAAEC,EAAE,SAASpH,EAAEC,GAAGA,EAAEA,GAAG,CAAC,EAAE6D,GAAG,WAAW,IAAI5D,EAAEC,EAAEwD,IAAIvD,EAAEY,EAAE,OAAOX,EAAE,SAASL,GAAG,IAAIC,EAAED,EAAEA,EAAE0E,OAAO,GAAGzE,GAAGA,EAAEkE,UAAUhE,EAAE0D,kBAAkBzD,EAAEmB,MAAMQ,KAAKqC,IAAInE,EAAEkE,UAAUrD,IAAI,GAAGV,EAAEsB,QAAQ,CAACzB,GAAGC,IAAI,EAAEQ,EAAEyB,EAAE,2BAA2B9B,GAAG,GAAGK,EAAE,CAACR,EAAE6C,EAAE/C,EAAEI,EAAE8G,EAAEjH,EAAEqE,kBAAkB,IAAIjB,EAAED,GAAG,WAAW+D,EAAE/G,EAAEuB,MAAMtB,EAAEK,EAAEiE,eAAejE,EAAEwD,aAAaiD,EAAE/G,EAAEuB,KAAI,EAAGzB,GAAE,GAAI,IAAI,CAAC,UAAU,SAAS+D,SAAS,SAASjE,GAAGO,iBAAiBP,GAAG,WAAW,OAAO4D,WAAWP,EAAE,EAAE,IAAG,EAAG,IAAIH,EAAEG,GAAG/C,GAAG,SAASH,GAAGC,EAAEY,EAAE,OAAOd,EAAE6C,EAAE/C,EAAEI,EAAE8G,EAAEjH,EAAEqE,kBAAkBtB,GAAG,WAAW5C,EAAEmB,MAAMX,YAAYkB,MAAM3B,EAAEM,UAAU0G,EAAE/G,EAAEuB,KAAI,EAAGzB,GAAE,EAAG,GAAG,GAAG,CAAC,GAAG,EAAEmH,EAAE,CAAC,IAAI,MAAMC,GAAE,SAAStH,EAAEC,GAAGgB,SAASC,aAAa4C,GAAG,WAAW,OAAO9D,EAAEC,EAAE,IAAI,aAAagB,SAASsG,WAAWhH,iBAAiB,QAAQ,WAAW,OAAOP,EAAEC,EAAE,IAAG,GAAI2D,WAAW3D,EAAE,EAAE,EAAEuH,GAAE,SAASxH,EAAEC,GAAGA,EAAEA,GAAG,CAAC,EAAE,IAAIC,EAAEc,EAAE,QAAQb,EAAE4C,EAAE/C,EAAEE,EAAEmH,EAAEpH,EAAEqE,kBAAkBgD,IAAG,WAAW,IAAIlH,EAAEM,IAAI,GAAGN,EAAE,CAAC,IAAIC,EAAED,EAAEqH,cAAc,GAAGpH,GAAG,GAAGA,EAAEO,YAAYkB,MAAM,OAAO5B,EAAEqB,MAAMQ,KAAKqC,IAAI/D,EAAES,IAAI,GAAGZ,EAAEwB,QAAQ,CAACtB,GAAGD,GAAE,GAAIG,GAAG,WAAWJ,EAAEc,EAAE,OAAO,IAAIb,EAAE4C,EAAE/C,EAAEE,EAAEmH,EAAEpH,EAAEqE,oBAAmB,EAAG,GAAG,CAAC,GAAG,C", "sources": ["../node_modules/web-vitals/dist/web-vitals.js"], "sourcesContent": ["var e,n,t,i,r,a=-1,o=function(e){addEventListener(\"pageshow\",(function(n){n.persisted&&(a=n.timeStamp,e(n))}),!0)},c=function(){return window.performance&&performance.getEntriesByType&&performance.getEntriesByType(\"navigation\")[0]},u=function(){var e=c();return e&&e.activationStart||0},f=function(e,n){var t=c(),i=\"navigate\";a>=0?i=\"back-forward-cache\":t&&(document.prerendering||u()>0?i=\"prerender\":document.wasDiscarded?i=\"restore\":t.type&&(i=t.type.replace(/_/g,\"-\")));return{name:e,value:void 0===n?-1:n,rating:\"good\",delta:0,entries:[],id:\"v3-\".concat(Date.now(),\"-\").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:i}},s=function(e,n,t){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var i=new PerformanceObserver((function(e){Promise.resolve().then((function(){n(e.getEntries())}))}));return i.observe(Object.assign({type:e,buffered:!0},t||{})),i}}catch(e){}},d=function(e,n,t,i){var r,a;return function(o){n.value>=0&&(o||i)&&((a=n.value-(r||0))||void 0===r)&&(r=n.value,n.delta=a,n.rating=function(e,n){return e>n[1]?\"poor\":e>n[0]?\"needs-improvement\":\"good\"}(n.value,t),e(n))}},l=function(e){requestAnimationFrame((function(){return requestAnimationFrame((function(){return e()}))}))},p=function(e){var n=function(n){\"pagehide\"!==n.type&&\"hidden\"!==document.visibilityState||e(n)};addEventListener(\"visibilitychange\",n,!0),addEventListener(\"pagehide\",n,!0)},v=function(e){var n=!1;return function(t){n||(e(t),n=!0)}},m=-1,h=function(){return\"hidden\"!==document.visibilityState||document.prerendering?1/0:0},g=function(e){\"hidden\"===document.visibilityState&&m>-1&&(m=\"visibilitychange\"===e.type?e.timeStamp:0,T())},y=function(){addEventListener(\"visibilitychange\",g,!0),addEventListener(\"prerenderingchange\",g,!0)},T=function(){removeEventListener(\"visibilitychange\",g,!0),removeEventListener(\"prerenderingchange\",g,!0)},E=function(){return m<0&&(m=h(),y(),o((function(){setTimeout((function(){m=h(),y()}),0)}))),{get firstHiddenTime(){return m}}},C=function(e){document.prerendering?addEventListener(\"prerenderingchange\",(function(){return e()}),!0):e()},L=[1800,3e3],w=function(e,n){n=n||{},C((function(){var t,i=E(),r=f(\"FCP\"),a=s(\"paint\",(function(e){e.forEach((function(e){\"first-contentful-paint\"===e.name&&(a.disconnect(),e.startTime<i.firstHiddenTime&&(r.value=Math.max(e.startTime-u(),0),r.entries.push(e),t(!0)))}))}));a&&(t=d(e,r,L,n.reportAllChanges),o((function(i){r=f(\"FCP\"),t=d(e,r,L,n.reportAllChanges),l((function(){r.value=performance.now()-i.timeStamp,t(!0)}))})))}))},b=[.1,.25],S=function(e,n){n=n||{},w(v((function(){var t,i=f(\"CLS\",0),r=0,a=[],c=function(e){e.forEach((function(e){if(!e.hadRecentInput){var n=a[0],t=a[a.length-1];r&&e.startTime-t.startTime<1e3&&e.startTime-n.startTime<5e3?(r+=e.value,a.push(e)):(r=e.value,a=[e])}})),r>i.value&&(i.value=r,i.entries=a,t())},u=s(\"layout-shift\",c);u&&(t=d(e,i,b,n.reportAllChanges),p((function(){c(u.takeRecords()),t(!0)})),o((function(){r=0,i=f(\"CLS\",0),t=d(e,i,b,n.reportAllChanges),l((function(){return t()}))})),setTimeout(t,0))})))},A={passive:!0,capture:!0},I=new Date,P=function(i,r){e||(e=r,n=i,t=new Date,k(removeEventListener),F())},F=function(){if(n>=0&&n<t-I){var r={entryType:\"first-input\",name:e.type,target:e.target,cancelable:e.cancelable,startTime:e.timeStamp,processingStart:e.timeStamp+n};i.forEach((function(e){e(r)})),i=[]}},M=function(e){if(e.cancelable){var n=(e.timeStamp>1e12?new Date:performance.now())-e.timeStamp;\"pointerdown\"==e.type?function(e,n){var t=function(){P(e,n),r()},i=function(){r()},r=function(){removeEventListener(\"pointerup\",t,A),removeEventListener(\"pointercancel\",i,A)};addEventListener(\"pointerup\",t,A),addEventListener(\"pointercancel\",i,A)}(n,e):P(n,e)}},k=function(e){[\"mousedown\",\"keydown\",\"touchstart\",\"pointerdown\"].forEach((function(n){return e(n,M,A)}))},D=[100,300],x=function(t,r){r=r||{},C((function(){var a,c=E(),u=f(\"FID\"),l=function(e){e.startTime<c.firstHiddenTime&&(u.value=e.processingStart-e.startTime,u.entries.push(e),a(!0))},m=function(e){e.forEach(l)},h=s(\"first-input\",m);a=d(t,u,D,r.reportAllChanges),h&&p(v((function(){m(h.takeRecords()),h.disconnect()}))),h&&o((function(){var o;u=f(\"FID\"),a=d(t,u,D,r.reportAllChanges),i=[],n=-1,e=null,k(addEventListener),o=l,i.push(o),F()}))}))},B=0,R=1/0,H=0,N=function(e){e.forEach((function(e){e.interactionId&&(R=Math.min(R,e.interactionId),H=Math.max(H,e.interactionId),B=H?(H-R)/7+1:0)}))},O=function(){return r?B:performance.interactionCount||0},q=function(){\"interactionCount\"in performance||r||(r=s(\"event\",N,{type:\"event\",buffered:!0,durationThreshold:0}))},j=[200,500],_=0,z=function(){return O()-_},G=[],J={},K=function(e){var n=G[G.length-1],t=J[e.interactionId];if(t||G.length<10||e.duration>n.latency){if(t)t.entries.push(e),t.latency=Math.max(t.latency,e.duration);else{var i={id:e.interactionId,latency:e.duration,entries:[e]};J[i.id]=i,G.push(i)}G.sort((function(e,n){return n.latency-e.latency})),G.splice(10).forEach((function(e){delete J[e.id]}))}},Q=function(e,n){n=n||{},C((function(){var t;q();var i,r=f(\"INP\"),a=function(e){e.forEach((function(e){(e.interactionId&&K(e),\"first-input\"===e.entryType)&&(!G.some((function(n){return n.entries.some((function(n){return e.duration===n.duration&&e.startTime===n.startTime}))}))&&K(e))}));var n,t=(n=Math.min(G.length-1,Math.floor(z()/50)),G[n]);t&&t.latency!==r.value&&(r.value=t.latency,r.entries=t.entries,i())},c=s(\"event\",a,{durationThreshold:null!==(t=n.durationThreshold)&&void 0!==t?t:40});i=d(e,r,j,n.reportAllChanges),c&&(\"PerformanceEventTiming\"in window&&\"interactionId\"in PerformanceEventTiming.prototype&&c.observe({type:\"first-input\",buffered:!0}),p((function(){a(c.takeRecords()),r.value<0&&z()>0&&(r.value=0,r.entries=[]),i(!0)})),o((function(){G=[],_=O(),r=f(\"INP\"),i=d(e,r,j,n.reportAllChanges)})))}))},U=[2500,4e3],V={},W=function(e,n){n=n||{},C((function(){var t,i=E(),r=f(\"LCP\"),a=function(e){var n=e[e.length-1];n&&n.startTime<i.firstHiddenTime&&(r.value=Math.max(n.startTime-u(),0),r.entries=[n],t())},c=s(\"largest-contentful-paint\",a);if(c){t=d(e,r,U,n.reportAllChanges);var m=v((function(){V[r.id]||(a(c.takeRecords()),c.disconnect(),V[r.id]=!0,t(!0))}));[\"keydown\",\"click\"].forEach((function(e){addEventListener(e,(function(){return setTimeout(m,0)}),!0)})),p(m),o((function(i){r=f(\"LCP\"),t=d(e,r,U,n.reportAllChanges),l((function(){r.value=performance.now()-i.timeStamp,V[r.id]=!0,t(!0)}))}))}}))},X=[800,1800],Y=function e(n){document.prerendering?C((function(){return e(n)})):\"complete\"!==document.readyState?addEventListener(\"load\",(function(){return e(n)}),!0):setTimeout(n,0)},Z=function(e,n){n=n||{};var t=f(\"TTFB\"),i=d(e,t,X,n.reportAllChanges);Y((function(){var r=c();if(r){var a=r.responseStart;if(a<=0||a>performance.now())return;t.value=Math.max(a-u(),0),t.entries=[r],i(!0),o((function(){t=f(\"TTFB\",0),(i=d(e,t,X,n.reportAllChanges))(!0)}))}}))};export{b as CLSThresholds,L as FCPThresholds,D as FIDThresholds,j as INPThresholds,U as LCPThresholds,X as TTFBThresholds,S as getCLS,w as getFCP,x as getFID,Q as getINP,W as getLCP,Z as getTTFB,S as onCLS,w as onFCP,x as onFID,Q as onINP,W as onLCP,Z as onTTFB};\n"], "names": ["e", "n", "t", "i", "r", "a", "o", "addEventListener", "persisted", "timeStamp", "c", "window", "performance", "getEntriesByType", "u", "activationStart", "f", "document", "prerendering", "wasDiscarded", "type", "replace", "name", "value", "rating", "delta", "entries", "id", "concat", "Date", "now", "Math", "floor", "random", "navigationType", "s", "PerformanceObserver", "supportedEntryTypes", "includes", "Promise", "resolve", "then", "getEntries", "observe", "Object", "assign", "buffered", "d", "l", "requestAnimationFrame", "p", "visibilityState", "v", "m", "h", "g", "T", "y", "removeEventListener", "E", "setTimeout", "firstHiddenTime", "C", "L", "w", "for<PERSON>ach", "disconnect", "startTime", "max", "push", "reportAllChanges", "b", "S", "hadRecentInput", "length", "takeRecords", "A", "passive", "capture", "I", "P", "k", "F", "entryType", "target", "cancelable", "processingStart", "M", "D", "x", "B", "R", "H", "N", "interactionId", "min", "O", "interactionCount", "q", "durationThreshold", "j", "_", "z", "G", "J", "K", "duration", "latency", "sort", "splice", "Q", "some", "PerformanceEventTiming", "prototype", "U", "V", "W", "X", "Y", "readyState", "Z", "responseStart"], "sourceRoot": ""}