{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/SearchDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { searchResources, setQuery, setProvider, clearResults } from '../store/slices/searchSlice';\nimport { MagnifyingGlassIcon, CloudIcon, TagIcon, ServerIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SearchDashboard = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    resources,\n    loading,\n    error,\n    totalCount,\n    query,\n    provider\n  } = useSelector(state => state.search);\n  const [localQuery, setLocalQuery] = useState(query);\n  const [localProvider, setLocalProvider] = useState(provider);\n  const providers = [{\n    value: '',\n    label: 'All Providers'\n  }, {\n    value: 'aws',\n    label: 'AWS'\n  }, {\n    value: 'gcp',\n    label: 'GCP'\n  }, {\n    value: 'azure',\n    label: 'Azure'\n  }];\n  const handleSearch = () => {\n    dispatch(setQuery(localQuery));\n    dispatch(setProvider(localProvider));\n    dispatch(searchResources({\n      query: localQuery,\n      provider: localProvider,\n      limit: 50\n    }));\n  };\n  const handleClear = () => {\n    setLocalQuery('');\n    setLocalProvider('');\n    dispatch(clearResults());\n  };\n  useEffect(() => {\n    // Load initial data\n    dispatch(searchResources({\n      limit: 20\n    }));\n  }, [dispatch]);\n  const getProviderColor = provider => {\n    switch (provider.toLowerCase()) {\n      case 'aws':\n        return 'bg-orange-100 text-orange-800';\n      case 'gcp':\n        return 'bg-blue-100 text-blue-800';\n      case 'azure':\n        return 'bg-cyan-100 text-cyan-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold text-white\",\n        children: \"Resource Discovery\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-1 text-sm text-gray-400\",\n        children: \"Search and discover resources across all your cloud providers\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 shadow rounded-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-4 py-5 sm:p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 gap-4 sm:grid-cols-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"sm:col-span-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"search\",\n              className: \"block text-sm font-medium text-gray-300\",\n              children: \"Search Query\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-1 relative rounded-md shadow-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n                  className: \"h-5 w-5 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 82,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"search\",\n                className: \"block w-full pl-10 pr-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent\",\n                placeholder: \"Search by name, type, or tags...\",\n                value: localQuery,\n                onChange: e => setLocalQuery(e.target.value),\n                onKeyPress: e => e.key === 'Enter' && handleSearch()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"provider\",\n              className: \"block text-sm font-medium text-gray-300\",\n              children: \"Provider\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"provider\",\n              className: \"mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-600 bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent rounded-md\",\n              value: localProvider,\n              onChange: e => setLocalProvider(e.target.value),\n              children: providers.map(p => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: p.value,\n                children: p.label\n              }, p.value, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-end space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: handleSearch,\n              disabled: loading,\n              className: \"flex-1 bg-cyan-600 border border-transparent rounded-md py-2 px-4 inline-flex justify-center text-sm font-medium text-white hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 disabled:opacity-50\",\n              children: loading ? 'Searching...' : 'Search'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: handleClear,\n              className: \"bg-gray-600 border border-transparent rounded-md py-2 px-4 inline-flex justify-center text-sm font-medium text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500\",\n              children: \"Clear\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 shadow rounded-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-4 py-5 sm:p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg leading-6 font-medium text-white\",\n            children: \"Search Results\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-400\",\n            children: [totalCount, \" resources found\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4 bg-red-50 border border-red-200 rounded-md p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-red-700\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 13\n        }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center h-32\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: resources.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-12\",\n            children: [/*#__PURE__*/_jsxDEV(CloudIcon, {\n              className: \"mx-auto h-12 w-12 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"mt-2 text-sm font-medium text-gray-300\",\n              children: \"No resources found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-gray-400\",\n              children: \"Try adjusting your search criteria or run a discovery scan.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 17\n          }, this) : resources.map(resource => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border border-gray-600 rounded-lg p-4 hover:bg-gray-700 transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start justify-between\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(ServerIcon, {\n                    className: \"h-5 w-5 text-gray-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 176,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-lg font-medium text-white\",\n                    children: resource.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 177,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getProviderColor(resource.provider)}`,\n                    children: resource.provider.toUpperCase()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 178,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mt-1 text-sm text-gray-400\",\n                  children: resource.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mt-1 text-xs text-gray-500 font-mono\",\n                  children: resource.id\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 21\n            }, this), Object.keys(resource.tags).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-1 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(TagIcon, {\n                  className: \"h-4 w-4 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-400\",\n                  children: \"Tags\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap gap-1\",\n                children: Object.entries(resource.tags).map(([key, value]) => /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-600 text-gray-200\",\n                  children: [key, \": \", value]\n                }, key, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 29\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 23\n            }, this)]\n          }, resource.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 63,\n    columnNumber: 5\n  }, this);\n};\n_s(SearchDashboard, \"B5aoGKEWbQTdCosbmUU3N1BcpO4=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = SearchDashboard;\nexport default SearchDashboard;\nvar _c;\n$RefreshReg$(_c, \"SearchDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useDispatch", "useSelector", "searchResources", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "clearResults", "MagnifyingGlassIcon", "CloudIcon", "TagIcon", "ServerIcon", "jsxDEV", "_jsxDEV", "SearchDashboard", "_s", "dispatch", "resources", "loading", "error", "totalCount", "query", "provider", "state", "search", "localQuery", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "localProvider", "setL<PERSON>al<PERSON><PERSON><PERSON>", "providers", "value", "label", "handleSearch", "limit", "handleClear", "getProviderColor", "toLowerCase", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "htmlFor", "type", "id", "placeholder", "onChange", "e", "target", "onKeyPress", "key", "map", "p", "onClick", "disabled", "length", "resource", "name", "toUpperCase", "Object", "keys", "tags", "entries", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/SearchDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { RootState, AppDispatch } from '../store/store';\nimport { searchResources, setQuery, setProvider, clearResults } from '../store/slices/searchSlice';\nimport {\n  MagnifyingGlassIcon,\n  CloudIcon,\n  TagIcon,\n  ServerIcon,\n} from '@heroicons/react/24/outline';\n\nconst SearchDashboard: React.FC = () => {\n  const dispatch = useDispatch<AppDispatch>();\n  const { resources, loading, error, totalCount, query, provider } = useSelector(\n    (state: RootState) => state.search\n  );\n\n  const [localQuery, setLocalQuery] = useState(query);\n  const [localProvider, setLocalProvider] = useState(provider);\n\n  const providers = [\n    { value: '', label: 'All Providers' },\n    { value: 'aws', label: 'AWS' },\n    { value: 'gcp', label: 'GCP' },\n    { value: 'azure', label: 'Azure' },\n  ];\n\n  const handleSearch = () => {\n    dispatch(setQuery(localQuery));\n    dispatch(setProvider(localProvider));\n    dispatch(searchResources({\n      query: localQuery,\n      provider: localProvider,\n      limit: 50,\n    }));\n  };\n\n  const handleClear = () => {\n    setLocalQuery('');\n    setLocalProvider('');\n    dispatch(clearResults());\n  };\n\n  useEffect(() => {\n    // Load initial data\n    dispatch(searchResources({ limit: 20 }));\n  }, [dispatch]);\n\n  const getProviderColor = (provider: string) => {\n    switch (provider.toLowerCase()) {\n      case 'aws':\n        return 'bg-orange-100 text-orange-800';\n      case 'gcp':\n        return 'bg-blue-100 text-blue-800';\n      case 'azure':\n        return 'bg-cyan-100 text-cyan-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div>\n        <h1 className=\"text-2xl font-bold text-white\">Resource Discovery</h1>\n        <p className=\"mt-1 text-sm text-gray-400\">\n          Search and discover resources across all your cloud providers\n        </p>\n      </div>\n\n      {/* Search Form */}\n      <div className=\"bg-gray-800 shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-4\">\n            <div className=\"sm:col-span-2\">\n              <label htmlFor=\"search\" className=\"block text-sm font-medium text-gray-300\">\n                Search Query\n              </label>\n              <div className=\"mt-1 relative rounded-md shadow-sm\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n                </div>\n                <input\n                  type=\"text\"\n                  id=\"search\"\n                  className=\"block w-full pl-10 pr-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent\"\n                  placeholder=\"Search by name, type, or tags...\"\n                  value={localQuery}\n                  onChange={(e) => setLocalQuery(e.target.value)}\n                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}\n                />\n              </div>\n            </div>\n\n            <div>\n              <label htmlFor=\"provider\" className=\"block text-sm font-medium text-gray-300\">\n                Provider\n              </label>\n              <select\n                id=\"provider\"\n                className=\"mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-600 bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent rounded-md\"\n                value={localProvider}\n                onChange={(e) => setLocalProvider(e.target.value)}\n              >\n                {providers.map((p) => (\n                  <option key={p.value} value={p.value}>\n                    {p.label}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            <div className=\"flex items-end space-x-2\">\n              <button\n                type=\"button\"\n                onClick={handleSearch}\n                disabled={loading}\n                className=\"flex-1 bg-cyan-600 border border-transparent rounded-md py-2 px-4 inline-flex justify-center text-sm font-medium text-white hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 disabled:opacity-50\"\n              >\n                {loading ? 'Searching...' : 'Search'}\n              </button>\n              <button\n                type=\"button\"\n                onClick={handleClear}\n                className=\"bg-gray-600 border border-transparent rounded-md py-2 px-4 inline-flex justify-center text-sm font-medium text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500\"\n              >\n                Clear\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Results */}\n      <div className=\"bg-gray-800 shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h3 className=\"text-lg leading-6 font-medium text-white\">\n              Search Results\n            </h3>\n            <span className=\"text-sm text-gray-400\">\n              {totalCount} resources found\n            </span>\n          </div>\n\n          {error && (\n            <div className=\"mb-4 bg-red-50 border border-red-200 rounded-md p-4\">\n              <div className=\"text-sm text-red-700\">{error}</div>\n            </div>\n          )}\n\n          {loading ? (\n            <div className=\"flex items-center justify-center h-32\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500\"></div>\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              {resources.length === 0 ? (\n                <div className=\"text-center py-12\">\n                  <CloudIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n                  <h3 className=\"mt-2 text-sm font-medium text-gray-300\">No resources found</h3>\n                  <p className=\"mt-1 text-sm text-gray-400\">\n                    Try adjusting your search criteria or run a discovery scan.\n                  </p>\n                </div>\n              ) : (\n                resources.map((resource) => (\n                  <div\n                    key={resource.id}\n                    className=\"border border-gray-600 rounded-lg p-4 hover:bg-gray-700 transition-colors\"\n                  >\n                    <div className=\"flex items-start justify-between\">\n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center space-x-2\">\n                          <ServerIcon className=\"h-5 w-5 text-gray-400\" />\n                          <h4 className=\"text-lg font-medium text-white\">{resource.name}</h4>\n                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getProviderColor(resource.provider)}`}>\n                            {resource.provider.toUpperCase()}\n                          </span>\n                        </div>\n                        <p className=\"mt-1 text-sm text-gray-400\">{resource.type}</p>\n                        <p className=\"mt-1 text-xs text-gray-500 font-mono\">{resource.id}</p>\n                      </div>\n                    </div>\n\n                    {Object.keys(resource.tags).length > 0 && (\n                      <div className=\"mt-3\">\n                        <div className=\"flex items-center space-x-1 mb-2\">\n                          <TagIcon className=\"h-4 w-4 text-gray-400\" />\n                          <span className=\"text-sm text-gray-400\">Tags</span>\n                        </div>\n                        <div className=\"flex flex-wrap gap-1\">\n                          {Object.entries(resource.tags).map(([key, value]) => (\n                            <span\n                              key={key}\n                              className=\"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-600 text-gray-200\"\n                            >\n                              {key}: {value}\n                            </span>\n                          ))}\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                ))\n              )}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SearchDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,SAASC,eAAe,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,YAAY,QAAQ,6BAA6B;AAClG,SACEC,mBAAmB,EACnBC,SAAS,EACTC,OAAO,EACPC,UAAU,QACL,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAMC,QAAQ,GAAGd,WAAW,CAAc,CAAC;EAC3C,MAAM;IAAEe,SAAS;IAAEC,OAAO;IAAEC,KAAK;IAAEC,UAAU;IAAEC,KAAK;IAAEC;EAAS,CAAC,GAAGnB,WAAW,CAC3EoB,KAAgB,IAAKA,KAAK,CAACC,MAC9B,CAAC;EAED,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAACqB,KAAK,CAAC;EACnD,MAAM,CAACM,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAACsB,QAAQ,CAAC;EAE5D,MAAMO,SAAS,GAAG,CAChB;IAAEC,KAAK,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAgB,CAAC,EACrC;IAAED,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAM,CAAC,EAC9B;IAAED,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAM,CAAC,EAC9B;IAAED,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAQ,CAAC,CACnC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzBhB,QAAQ,CAACX,QAAQ,CAACoB,UAAU,CAAC,CAAC;IAC9BT,QAAQ,CAACV,WAAW,CAACqB,aAAa,CAAC,CAAC;IACpCX,QAAQ,CAACZ,eAAe,CAAC;MACvBiB,KAAK,EAAEI,UAAU;MACjBH,QAAQ,EAAEK,aAAa;MACvBM,KAAK,EAAE;IACT,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxBR,aAAa,CAAC,EAAE,CAAC;IACjBE,gBAAgB,CAAC,EAAE,CAAC;IACpBZ,QAAQ,CAACT,YAAY,CAAC,CAAC,CAAC;EAC1B,CAAC;EAEDN,SAAS,CAAC,MAAM;IACd;IACAe,QAAQ,CAACZ,eAAe,CAAC;MAAE6B,KAAK,EAAE;IAAG,CAAC,CAAC,CAAC;EAC1C,CAAC,EAAE,CAACjB,QAAQ,CAAC,CAAC;EAEd,MAAMmB,gBAAgB,GAAIb,QAAgB,IAAK;IAC7C,QAAQA,QAAQ,CAACc,WAAW,CAAC,CAAC;MAC5B,KAAK,KAAK;QACR,OAAO,+BAA+B;MACxC,KAAK,KAAK;QACR,OAAO,2BAA2B;MACpC,KAAK,OAAO;QACV,OAAO,2BAA2B;MACpC;QACE,OAAO,2BAA2B;IACtC;EACF,CAAC;EAED,oBACEvB,OAAA;IAAKwB,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBzB,OAAA;MAAAyB,QAAA,gBACEzB,OAAA;QAAIwB,SAAS,EAAC,+BAA+B;QAAAC,QAAA,EAAC;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrE7B,OAAA;QAAGwB,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAE1C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGN7B,OAAA;MAAKwB,SAAS,EAAC,+BAA+B;MAAAC,QAAA,eAC5CzB,OAAA;QAAKwB,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BzB,OAAA;UAAKwB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDzB,OAAA;YAAKwB,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BzB,OAAA;cAAO8B,OAAO,EAAC,QAAQ;cAACN,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE5E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR7B,OAAA;cAAKwB,SAAS,EAAC,oCAAoC;cAAAC,QAAA,gBACjDzB,OAAA;gBAAKwB,SAAS,EAAC,sEAAsE;gBAAAC,QAAA,eACnFzB,OAAA,CAACL,mBAAmB;kBAAC6B,SAAS,EAAC;gBAAuB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC,eACN7B,OAAA;gBACE+B,IAAI,EAAC,MAAM;gBACXC,EAAE,EAAC,QAAQ;gBACXR,SAAS,EAAC,yLAAyL;gBACnMS,WAAW,EAAC,kCAAkC;gBAC9ChB,KAAK,EAAEL,UAAW;gBAClBsB,QAAQ,EAAGC,CAAC,IAAKtB,aAAa,CAACsB,CAAC,CAACC,MAAM,CAACnB,KAAK,CAAE;gBAC/CoB,UAAU,EAAGF,CAAC,IAAKA,CAAC,CAACG,GAAG,KAAK,OAAO,IAAInB,YAAY,CAAC;cAAE;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN7B,OAAA;YAAAyB,QAAA,gBACEzB,OAAA;cAAO8B,OAAO,EAAC,UAAU;cAACN,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE9E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR7B,OAAA;cACEgC,EAAE,EAAC,UAAU;cACbR,SAAS,EAAC,mLAAmL;cAC7LP,KAAK,EAAEH,aAAc;cACrBoB,QAAQ,EAAGC,CAAC,IAAKpB,gBAAgB,CAACoB,CAAC,CAACC,MAAM,CAACnB,KAAK,CAAE;cAAAQ,QAAA,EAEjDT,SAAS,CAACuB,GAAG,CAAEC,CAAC,iBACfxC,OAAA;gBAAsBiB,KAAK,EAAEuB,CAAC,CAACvB,KAAM;gBAAAQ,QAAA,EAClCe,CAAC,CAACtB;cAAK,GADGsB,CAAC,CAACvB,KAAK;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEZ,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN7B,OAAA;YAAKwB,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvCzB,OAAA;cACE+B,IAAI,EAAC,QAAQ;cACbU,OAAO,EAAEtB,YAAa;cACtBuB,QAAQ,EAAErC,OAAQ;cAClBmB,SAAS,EAAC,2OAA2O;cAAAC,QAAA,EAEpPpB,OAAO,GAAG,cAAc,GAAG;YAAQ;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACT7B,OAAA;cACE+B,IAAI,EAAC,QAAQ;cACbU,OAAO,EAAEpB,WAAY;cACrBG,SAAS,EAAC,gNAAgN;cAAAC,QAAA,EAC3N;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7B,OAAA;MAAKwB,SAAS,EAAC,+BAA+B;MAAAC,QAAA,eAC5CzB,OAAA;QAAKwB,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BzB,OAAA;UAAKwB,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDzB,OAAA;YAAIwB,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAEzD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL7B,OAAA;YAAMwB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GACpClB,UAAU,EAAC,kBACd;UAAA;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EAELvB,KAAK,iBACJN,OAAA;UAAKwB,SAAS,EAAC,qDAAqD;UAAAC,QAAA,eAClEzB,OAAA;YAAKwB,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAEnB;UAAK;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CACN,EAEAxB,OAAO,gBACNL,OAAA;UAAKwB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,eACpDzB,OAAA;YAAKwB,SAAS,EAAC;UAA8D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CAAC,gBAEN7B,OAAA;UAAKwB,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBrB,SAAS,CAACuC,MAAM,KAAK,CAAC,gBACrB3C,OAAA;YAAKwB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCzB,OAAA,CAACJ,SAAS;cAAC4B,SAAS,EAAC;YAAiC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzD7B,OAAA;cAAIwB,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9E7B,OAAA;cAAGwB,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE1C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,GAENzB,SAAS,CAACmC,GAAG,CAAEK,QAAQ,iBACrB5C,OAAA;YAEEwB,SAAS,EAAC,2EAA2E;YAAAC,QAAA,gBAErFzB,OAAA;cAAKwB,SAAS,EAAC,kCAAkC;cAAAC,QAAA,eAC/CzB,OAAA;gBAAKwB,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrBzB,OAAA;kBAAKwB,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CzB,OAAA,CAACF,UAAU;oBAAC0B,SAAS,EAAC;kBAAuB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAChD7B,OAAA;oBAAIwB,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,EAAEmB,QAAQ,CAACC;kBAAI;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnE7B,OAAA;oBAAMwB,SAAS,EAAE,2EAA2EF,gBAAgB,CAACsB,QAAQ,CAACnC,QAAQ,CAAC,EAAG;oBAAAgB,QAAA,EAC/HmB,QAAQ,CAACnC,QAAQ,CAACqC,WAAW,CAAC;kBAAC;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACN7B,OAAA;kBAAGwB,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAEmB,QAAQ,CAACb;gBAAI;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7D7B,OAAA;kBAAGwB,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,EAAEmB,QAAQ,CAACZ;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAELkB,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACK,IAAI,CAAC,CAACN,MAAM,GAAG,CAAC,iBACpC3C,OAAA;cAAKwB,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBzB,OAAA;gBAAKwB,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/CzB,OAAA,CAACH,OAAO;kBAAC2B,SAAS,EAAC;gBAAuB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC7C7B,OAAA;kBAAMwB,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACN7B,OAAA;gBAAKwB,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAClCsB,MAAM,CAACG,OAAO,CAACN,QAAQ,CAACK,IAAI,CAAC,CAACV,GAAG,CAAC,CAAC,CAACD,GAAG,EAAErB,KAAK,CAAC,kBAC9CjB,OAAA;kBAEEwB,SAAS,EAAC,0FAA0F;kBAAAC,QAAA,GAEnGa,GAAG,EAAC,IAAE,EAACrB,KAAK;gBAAA,GAHRqB,GAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAIJ,CACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA,GAlCIe,QAAQ,CAACZ,EAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAmCb,CACN;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3B,EAAA,CA1MID,eAAyB;EAAA,QACZZ,WAAW,EACuCC,WAAW;AAAA;AAAA6D,EAAA,GAF1ElD,eAAyB;AA4M/B,eAAeA,eAAe;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}