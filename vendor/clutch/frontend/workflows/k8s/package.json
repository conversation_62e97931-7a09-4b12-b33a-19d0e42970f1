{"name": "@clutch-sh/k8s", "version": "4.0.0-beta", "description": "Clutch K8s Workflows", "license": "Apache-2.0", "author": "<EMAIL>", "main": "dist/index.js", "files": ["dist"], "scripts": {"build": "yarn clean && yarn compile", "clean": "yarn run package:clean", "compile": "yarn run package:compile", "compile:dev": "yarn run package:compile:dev", "compile:watch": "yarn run package:compile:watch", "lint": "yarn run package:lint", "lint:fix": "yarn lint --fix", "publishBeta": "../../../tools/publish-frontend.sh k8s", "test": "yarn run package:test", "test:coverage": "yarn test --collect-coverage", "test:watch": "yarn test --watch"}, "dependencies": {"@clutch-sh/api": "workspace:^", "@clutch-sh/core": "workspace:^", "@clutch-sh/data-layout": "workspace:^", "@clutch-sh/wizard": "workspace:^", "@hookform/resolvers": "2.8.8", "lodash": "^4.17.0", "react-hook-form": "^7.25.3", "react-timeago": "^7.0.0", "yup": "^0.32.8"}, "devDependencies": {"@clutch-sh/tools": "workspace:^"}, "peerDependencies": {"@emotion/styled": "^11.8.1", "@mui/icons-material": "^5.11.0", "@mui/material": "^5.11.0", "react": "^17.0.2", "react-dom": "^17.0.2", "typescript": "^4.2.3"}, "engines": {"node": ">=18 <19", "yarn": "^4.5.0"}, "packageManager": "yarn@4.5.0", "stableVersion": "3.0.0-beta"}