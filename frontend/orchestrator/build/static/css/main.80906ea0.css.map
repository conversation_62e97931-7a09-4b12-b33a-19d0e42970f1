{"version": 3, "file": "static/css/main.80906ea0.css", "mappings": "AAAA,wCAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,yBAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,oBAAc,CAAd,kCAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,yBAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,oBAAc,CAAd;;CAAc,CAAd,uCAAc,CAAd,qBAAc,CAAd,8BAAc,CAAd,wCAAc,CAAd,4BAAc,CAAd,uCAAc,CAAd,sCAAc,CAAd,8BAAc,CAAd,eAAc,CAAd,UAAc,CAAd,wBAAc,CAAd,uBAAc,CAAd,aAAc,CAAd,QAAc,CAAd,4DAAc,CAAd,gCAAc,CAAd,mCAAc,CAAd,mBAAc,CAAd,eAAc,CAAd,uBAAc,CAAd,2BAAc,CAAd,8CAAc,CAAd,0DAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,aAAc,CAAd,iBAAc,CAAd,sBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,oBAAc,CAAd,aAAc,CAAd,mEAAc,CAAd,aAAc,CAAd,mBAAc,CAAd,cAAc,CAAd,+BAAc,CAAd,mBAAc,CAAd,sBAAc,CAAd,mBAAc,CAAd,QAAc,CAAd,SAAc,CAAd,iCAAc,CAAd,gHAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,4BAAc,CAAd,gCAAc,CAAd,+BAAc,CAAd,mEAAc,CAAd,0CAAc,CAAd,mBAAc,CAAd,mDAAc,CAAd,sDAAc,CAAd,YAAc,CAAd,yBAAc,CAAd,2DAAc,CAAd,iBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,QAAc,CAAd,SAAc,CAAd,gBAAc,CAAd,wBAAc,CAAd,sDAAc,CAAd,SAAc,CAAd,mCAAc,CAAd,wBAAc,CAAd,4DAAc,CAAd,qBAAc,CAAd,qBAAc,CAAd,cAAc,CAAd,uDAAc,CAAd,2CAAc,CAAd,uCAAc,CAAd,iCAAc,CAAd,wBAAc,CAAd,wCAAc,CAAd,UAAc,CAAd,+BAAc,CAAd,yIAAc,CAAd,QAAc,CAEd,wCAAmB,CAAnB,qBAAmB,CAAnB,2BAAmB,CAAnB,2BAAmB,CAAnB,+BAAmB,CAAnB,eAAmB,CAAnB,gBAAmB,CAAnB,yBAAmB,CAAnB,cAAmB,CAAnB,iBAAmB,CAAnB,gBAAmB,CAAnB,YAAmB,CAAnB,gBAAmB,CAAnB,eAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,yBAAmB,CAAnB,iBAAmB,CAAnB,wBAAmB,CAAnB,yBAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,uBAAmB,CAAnB,wBAAmB,CAAnB,sBAAmB,CAAnB,yBAAmB,CAAnB,wBAAmB,CAAnB,yBAAmB,CAAnB,2BAAmB,CAAnB,uBAAmB,CAAnB,sBAAmB,CAAnB,uBAAmB,CAAnB,qBAAmB,CAAnB,wBAAmB,CAAnB,uBAAmB,CAAnB,qBAAmB,CAAnB,oBAAmB,CAAnB,sBAAmB,CAAnB,kBAAmB,CAAnB,gCAAmB,CAAnB,kBAAmB,CAAnB,oBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,iBAAmB,CAAnB,gBAAmB,CAAnB,mBAAmB,CAAnB,kBAAmB,CAAnB,kBAAmB,CAAnB,gBAAmB,CAAnB,mBAAmB,CAAnB,8BAAmB,CAAnB,YAAmB,CAAnB,qBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,iBAAmB,CAAnB,gBAAmB,CAAnB,eAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,eAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,oBAAmB,CAAnB,0BAAmB,CAAnB,yBAAmB,CAAnB,gBAAmB,CAAnB,4BAAmB,CAAnB,sBAAmB,CAAnB,wMAAmB,CAAnB,+BAAmB,EAAnB,kEAAmB,CAAnB,0CAAmB,EAAnB,+CAAmB,CAAnB,8BAAmB,CAAnB,0DAAmB,CAAnB,0DAAmB,CAAnB,+BAAmB,CAAnB,yBAAmB,CAAnB,mCAAmB,CAAnB,+BAAmB,CAAnB,gCAAmB,CAAnB,oCAAmB,CAAnB,qCAAmB,CAAnB,sCAAmB,CAAnB,8CAAmB,CAAnB,iBAAmB,CAAnB,gBAAmB,CAAnB,iBAAmB,CAAnB,eAAmB,CAAnB,kBAAmB,CAAnB,iBAAmB,CAAnB,wBAAmB,CAAnB,uBAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,0GAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,0GAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,yBAAmB,CAAnB,sBAAmB,CAAnB,kBAAmB,CAAnB,6BAAmB,CAAnB,+BAAmB,CAAnB,kCAAmB,CAAnB,+BAAmB,CAAnB,iCAAmB,CAAnB,wBAAmB,CAAnB,0BAAmB,CAAnB,iCAAmB,CAAnB,mCAAmB,CAAnB,gCAAmB,CAAnB,8BAAmB,CAAnB,2CAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,sDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,qDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,qDAAmB,CAAnB,qCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,0CAAmB,CAAnB,sCAAmB,CAAnB,mCAAmB,CAAnB,iBAAmB,CAAnB,wDAAmB,CAAnB,oCAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,2CAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,2CAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,4BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,0CAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,2BAAmB,CAAnB,qBAAmB,CAAnB,wDAAmB,CAAnB,wCAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,kCAAmB,CAAnB,mCAAmB,CAAnB,6FAAmB,CAAnB,qFAAmB,CAAnB,0EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,0EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,sEAAmB,CAAnB,mBAAmB,CAAnB,kBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,oBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,wBAAmB,CAAnB,mBAAmB,CAAnB,6BAAmB,CAAnB,qBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,mDAAmB,CAAnB,8CAAmB,CAAnB,2CAAmB,CAAnB,4CAAmB,CAAnB,8CAAmB,CAAnB,0CAAmB,CAAnB,gDAAmB,CAAnB,8CAAmB,CAAnB,0CAAmB,CAAnB,yBAAmB,CAAnB,0BAAmB,CAAnB,yBAAmB,CAAnB,uBAAmB,CAAnB,2BAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,yBAAmB,CAAnB,wBAAmB,CAAnB,0BAAmB,CAAnB,8BAAmB,CAAnB,4BAAmB,CAAnB,qEAAmB,CAAnB,0BAAmB,CAAnB,gBAAmB,CAAnB,4BAAmB,CAAnB,mBAAmB,CAAnB,2BAAmB,CAAnB,aAAmB,CAAnB,yBAAmB,CAAnB,kBAAmB,CAAnB,2BAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,yBAAmB,CAAnB,gBAAmB,CAAnB,0BAAmB,CAAnB,4BAAmB,CAAnB,8BAAmB,CAAnB,6BAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,+BAAmB,CAAnB,UAAmB,CAAnB,+CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,6DAAmB,CAAnB,aAAmB,CAAnB,sDAAmB,CAAnB,kEAAmB,CAAnB,4FAAmB,CAAnB,mEAAmB,CAAnB,kGAAmB,CAAnB,mDAAmB,CAAnB,4DAAmB,CAAnB,0EAAmB,CAAnB,iGAAmB,CAAnB,qEAAmB,CAAnB,kGAAmB,CAAnB,4CAAmB,CAAnB,sDAAmB,CAAnB,+CAAmB,CAAnB,8QAAmB,CAAnB,sQAAmB,CAAnB,gEAAmB,CAAnB,kDAAmB,CAAnB,6IAAmB,CAAnB,yFAAmB,CAAnB,uHAAmB,CAAnB,kDAAmB,CAAnB,qCAAmB,CAEnB,MAEE,0BAA2B,CAC3B,4BAA6B,CAC7B,0BAA2B,CAC3B,4BAA6B,CAC7B,uBAAwB,CACxB,6BAA8B,CAC9B,yBAA6B,CAC7B,4BACF,CAEA,mBACE,uBAA2B,CAC3B,yBAA6B,CAC7B,0BAA2B,CAC3B,4BAA6B,CAC7B,oBAAwB,CACxB,6BAA8B,CAC9B,4BAA6B,CAC7B,8BACF,CA0CA,oBAEE,UAAW,CADX,SAEF,CAGE,2CAAkB,CAAlB,wBAAkB,CAAlB,qDAAkB,CAIlB,2CAA0B,CAA1B,wBAA0B,CAA1B,0EAA0B,CAI1B,iDAAkB,CAAlB,wBAAkB,CAAlB,wDAAkB,CAGpB,KACE,sFAEF,CAvFA,6DAwFA,CAxFA,aAwFA,CAxFA,4CAwFA,CAxFA,yCAwFA,CAxFA,iBAwFA,CAxFA,6LAwFA,CAxFA,2CAwFA,CAxFA,wBAwFA,CAxFA,uDAwFA,CAxFA,2CAwFA,CAxFA,wBAwFA,CAxFA,wDAwFA,CAxFA,2CAwFA,CAxFA,wBAwFA,CAxFA,qDAwFA,CAxFA,2CAwFA,CAxFA,wBAwFA,CAxFA,qDAwFA,CAxFA,4CAwFA,CAxFA,wBAwFA,CAxFA,sDAwFA,CAxFA,0CAwFA,CAxFA,wBAwFA,CAxFA,sDAwFA,CAxFA,uFAwFA,CAxFA,yDAwFA,CAxFA,iEAwFA,CAxFA,+CAwFA,CAxFA,aAwFA,CAxFA,+CAwFA,CAxFA,4CAwFA,CAxFA,UAwFA,CAxFA,+CAwFA,CAxFA,mDAwFA,CAxFA,2CAwFA,CAxFA,wBAwFA,CAxFA,qDAwFA,CAxFA,+CAwFA,CAxFA,aAwFA,CAxFA,+CAwFA,CAxFA,kDAwFA,CAxFA,kBAwFA,CAxFA,+HAwFA,CAxFA,kGAwFA,CAxFA,iHAwFA,CAxFA,wFAwFA,CAxFA,+HAwFA,CAxFA,wGAwFA,CAxFA,8CAwFA,CAxFA,+CAwFA,CAxFA,wDAwFA,CAxFA,+CAwFA,CAxFA,uDAwFA,CAxFA,+CAwFA,CAxFA,yDAwFA,CAxFA,gDAwFA,CAxFA,uDAwFA,CAxFA,8CAwFA,CAxFA,uDAwFA,CAxFA,sDAwFA,CAxFA,4CAwFA,CAxFA,cAwFA,CAxFA,6LAwFA,CAxFA,yDAwFA,CAxFA,6FAwFA,CAxFA,yDAwFA,CAxFA,iEAwFA,CAxFA,uFAwFA,CAxFA,yCAwFA,CAxFA,kEAwFA,CAxFA,8DAwFA,CAxFA,8DAwFA,CAxFA,8DAwFA,CAxFA,uBAwFA,CAxFA,6BAwFA,CAxFA,oBAwFA,CAxFA,6BAwFA,CAxFA,8BAwFA,CAxFA,mBAwFA,EAxFA,gDAwFA,CAxFA,4BAwFA,CAxFA,2BAwFA,CAxFA,kBAwFA,EAxFA,mDAwFA,CAxFA,6BAwFA,CAxFA,sBAwFA,CAxFA,wBAwFA,CAxFA,qBAwFA,CAxFA,8DAwFA,CAxFA,8DAwFA,CAxFA,8DAwFA,CAxFA,mCAwFA,CAxFA,6BAwFA", "sources": ["index.css"], "sourcesContent": ["@tailwind base;\n@tailwind components;\n@tailwind utilities;\n\n:root {\n  /* CAINuro Design System - Dark Theme (Default) */\n  --theme-bg-primary: #0e0f0f;\n  --theme-bg-secondary: #1b1b1e;\n  --theme-bg-sidebar: #0e0f0f;\n  --theme-bg-container: #0e0f0f;\n  --theme-bg-chat: #1b1b1e;\n  --theme-bg-chat-input: #27282a;\n  --theme-text-primary: #ffffff;\n  --theme-text-secondary: rgba(255,255,255,.6);\n}\n\n[data-theme=\"light\"] {\n  --theme-bg-primary: #ffffff;\n  --theme-bg-secondary: #ffffff;\n  --theme-bg-sidebar: #edf2fa;\n  --theme-bg-container: #f9fbfd;\n  --theme-bg-chat: #ffffff;\n  --theme-bg-chat-input: #eaeaea;\n  --theme-text-primary: #0e0f0f;\n  --theme-text-secondary: #7a7d7e;\n}\n\n@layer base {\n  html {\n    font-family: 'Inter', system-ui, sans-serif;\n  }\n\n  body {\n    margin: 0;\n    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n      sans-serif;\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n    background-color: var(--theme-bg-primary);\n    color: var(--theme-text-primary);\n  }\n}\n\n@layer components {\n  .btn-primary {\n    @apply bg-cyan-600 hover:bg-cyan-700 text-white font-medium py-2 px-4 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:ring-offset-2;\n  }\n\n  .btn-secondary {\n    @apply bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;\n  }\n\n  .input-field {\n    @apply block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent;\n  }\n\n  .card {\n    @apply bg-gray-800 shadow rounded-lg;\n  }\n\n  .card-header {\n    @apply px-4 py-5 sm:p-6;\n  }\n}\n\n/* Custom scrollbar */\n::-webkit-scrollbar {\n  width: 8px;\n  height: 8px;\n}\n\n::-webkit-scrollbar-track {\n  @apply bg-gray-800;\n}\n\n::-webkit-scrollbar-thumb {\n  @apply bg-gray-600 rounded;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  @apply bg-gray-500;\n}\n\ncode {\n  font-family: 'JetBrains Mono', source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n"], "names": [], "sourceRoot": ""}