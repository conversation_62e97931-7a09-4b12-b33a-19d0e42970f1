{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _t from \"@monaco-editor/loader\";\nimport { memo as Te } from \"react\";\nimport ke, { useState as re, useRef as S, use<PERSON><PERSON>back as oe, useEffect as ne } from \"react\";\nimport Se from \"@monaco-editor/loader\";\nimport { memo as ye } from \"react\";\nimport K from \"react\";\nvar le = {\n    wrapper: {\n      display: \"flex\",\n      position: \"relative\",\n      textAlign: \"initial\"\n    },\n    fullWidth: {\n      width: \"100%\"\n    },\n    hide: {\n      display: \"none\"\n    }\n  },\n  v = le;\nimport me from \"react\";\nvar ae = {\n    container: {\n      display: \"flex\",\n      height: \"100%\",\n      width: \"100%\",\n      justifyContent: \"center\",\n      alignItems: \"center\"\n    }\n  },\n  Y = ae;\nfunction Me(_ref) {\n  let {\n    children: e\n  } = _ref;\n  return me.createElement(\"div\", {\n    style: Y.container\n  }, e);\n}\nvar Z = Me;\nvar $ = Z;\nfunction Ee(_ref2) {\n  let {\n    width: e,\n    height: r,\n    isEditorReady: n,\n    loading: t,\n    _ref: a,\n    className: m,\n    wrapperProps: E\n  } = _ref2;\n  return K.createElement(\"section\", _objectSpread({\n    style: _objectSpread(_objectSpread({}, v.wrapper), {}, {\n      width: e,\n      height: r\n    })\n  }, E), !n && K.createElement($, null, t), K.createElement(\"div\", {\n    ref: a,\n    style: _objectSpread(_objectSpread({}, v.fullWidth), !n && v.hide),\n    className: m\n  }));\n}\nvar ee = Ee;\nvar H = ye(ee);\nimport { useEffect as xe } from \"react\";\nfunction Ce(e) {\n  xe(e, []);\n}\nvar k = Ce;\nimport { useEffect as ge, useRef as Re } from \"react\";\nfunction he(e, r) {\n  let n = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : !0;\n  let t = Re(!0);\n  ge(t.current || !n ? () => {\n    t.current = !1;\n  } : e, r);\n}\nvar l = he;\nfunction D() {}\nfunction h(e, r, n, t) {\n  return De(e, t) || be(e, r, n, t);\n}\nfunction De(e, r) {\n  return e.editor.getModel(te(e, r));\n}\nfunction be(e, r, n, t) {\n  return e.editor.createModel(r, n, t ? te(e, t) : void 0);\n}\nfunction te(e, r) {\n  return e.Uri.parse(r);\n}\nfunction Oe(_ref3) {\n  let {\n    original: e,\n    modified: r,\n    language: n,\n    originalLanguage: t,\n    modifiedLanguage: a,\n    originalModelPath: m,\n    modifiedModelPath: E,\n    keepCurrentOriginalModel: g = !1,\n    keepCurrentModifiedModel: N = !1,\n    theme: x = \"light\",\n    loading: P = \"Loading...\",\n    options: y = {},\n    height: V = \"100%\",\n    width: z = \"100%\",\n    className: F,\n    wrapperProps: j = {},\n    beforeMount: A = D,\n    onMount: q = D\n  } = _ref3;\n  let [M, O] = re(!1),\n    [T, s] = re(!0),\n    u = S(null),\n    c = S(null),\n    w = S(null),\n    d = S(q),\n    o = S(A),\n    b = S(!1);\n  k(() => {\n    let i = Se.init();\n    return i.then(f => (c.current = f) && s(!1)).catch(f => (f === null || f === void 0 ? void 0 : f.type) !== \"cancelation\" && console.error(\"Monaco initialization: error:\", f)), () => u.current ? I() : i.cancel();\n  }), l(() => {\n    if (u.current && c.current) {\n      let i = u.current.getOriginalEditor(),\n        f = h(c.current, e || \"\", t || n || \"text\", m || \"\");\n      f !== i.getModel() && i.setModel(f);\n    }\n  }, [m], M), l(() => {\n    if (u.current && c.current) {\n      let i = u.current.getModifiedEditor(),\n        f = h(c.current, r || \"\", a || n || \"text\", E || \"\");\n      f !== i.getModel() && i.setModel(f);\n    }\n  }, [E], M), l(() => {\n    let i = u.current.getModifiedEditor();\n    i.getOption(c.current.editor.EditorOption.readOnly) ? i.setValue(r || \"\") : r !== i.getValue() && (i.executeEdits(\"\", [{\n      range: i.getModel().getFullModelRange(),\n      text: r || \"\",\n      forceMoveMarkers: !0\n    }]), i.pushUndoStop());\n  }, [r], M), l(() => {\n    var _u$current;\n    (_u$current = u.current) === null || _u$current === void 0 || (_u$current = _u$current.getModel()) === null || _u$current === void 0 || _u$current.original.setValue(e || \"\");\n  }, [e], M), l(() => {\n    let {\n      original: i,\n      modified: f\n    } = u.current.getModel();\n    c.current.editor.setModelLanguage(i, t || n || \"text\"), c.current.editor.setModelLanguage(f, a || n || \"text\");\n  }, [n, t, a], M), l(() => {\n    var _c$current;\n    (_c$current = c.current) === null || _c$current === void 0 || _c$current.editor.setTheme(x);\n  }, [x], M), l(() => {\n    var _u$current2;\n    (_u$current2 = u.current) === null || _u$current2 === void 0 || _u$current2.updateOptions(y);\n  }, [y], M);\n  let L = oe(() => {\n      var _u$current3;\n      if (!c.current) return;\n      o.current(c.current);\n      let i = h(c.current, e || \"\", t || n || \"text\", m || \"\"),\n        f = h(c.current, r || \"\", a || n || \"text\", E || \"\");\n      (_u$current3 = u.current) === null || _u$current3 === void 0 || _u$current3.setModel({\n        original: i,\n        modified: f\n      });\n    }, [n, r, a, e, t, m, E]),\n    U = oe(() => {\n      var _c$current2;\n      !b.current && w.current && (u.current = c.current.editor.createDiffEditor(w.current, _objectSpread({\n        automaticLayout: !0\n      }, y)), L(), (_c$current2 = c.current) !== null && _c$current2 !== void 0 && _c$current2.editor.setTheme(x), O(!0), b.current = !0);\n    }, [y, x, L]);\n  ne(() => {\n    M && d.current(u.current, c.current);\n  }, [M]), ne(() => {\n    !T && !M && U();\n  }, [T, M, U]);\n  function I() {\n    var _u$current4, _i$original, _i$modified, _u$current5;\n    let i = (_u$current4 = u.current) === null || _u$current4 === void 0 ? void 0 : _u$current4.getModel();\n    g || i !== null && i !== void 0 && (_i$original = i.original) !== null && _i$original !== void 0 && _i$original.dispose(), N || i !== null && i !== void 0 && (_i$modified = i.modified) !== null && _i$modified !== void 0 && _i$modified.dispose(), (_u$current5 = u.current) === null || _u$current5 === void 0 ? void 0 : _u$current5.dispose();\n  }\n  return ke.createElement(H, {\n    width: z,\n    height: V,\n    isEditorReady: M,\n    loading: P,\n    _ref: w,\n    className: F,\n    wrapperProps: j\n  });\n}\nvar ie = Oe;\nvar we = Te(ie);\nimport { useState as Ie } from \"react\";\nimport ce from \"@monaco-editor/loader\";\nfunction Pe() {\n  let [e, r] = Ie(ce.__getMonacoInstance());\n  return k(() => {\n    let n;\n    return e || (n = ce.init(), n.then(t => {\n      r(t);\n    })), () => {\n      var _n;\n      return (_n = n) === null || _n === void 0 ? void 0 : _n.cancel();\n    };\n  }), e;\n}\nvar Le = Pe;\nimport { memo as ze } from \"react\";\nimport We, { useState as ue, useEffect as W, useRef as C, useCallback as _e } from \"react\";\nimport Ne from \"@monaco-editor/loader\";\nimport { useEffect as Ue, useRef as ve } from \"react\";\nfunction He(e) {\n  let r = ve();\n  return Ue(() => {\n    r.current = e;\n  }, [e]), r.current;\n}\nvar se = He;\nvar _ = new Map();\nfunction Ve(_ref4) {\n  let {\n    defaultValue: e,\n    defaultLanguage: r,\n    defaultPath: n,\n    value: t,\n    language: a,\n    path: m,\n    theme: E = \"light\",\n    line: g,\n    loading: N = \"Loading...\",\n    options: x = {},\n    overrideServices: P = {},\n    saveViewState: y = !0,\n    keepCurrentModel: V = !1,\n    width: z = \"100%\",\n    height: F = \"100%\",\n    className: j,\n    wrapperProps: A = {},\n    beforeMount: q = D,\n    onMount: M = D,\n    onChange: O,\n    onValidate: T = D\n  } = _ref4;\n  let [s, u] = ue(!1),\n    [c, w] = ue(!0),\n    d = C(null),\n    o = C(null),\n    b = C(null),\n    L = C(M),\n    U = C(q),\n    I = C(),\n    i = C(t),\n    f = se(m),\n    Q = C(!1),\n    B = C(!1);\n  k(() => {\n    let p = Ne.init();\n    return p.then(R => (d.current = R) && w(!1)).catch(R => (R === null || R === void 0 ? void 0 : R.type) !== \"cancelation\" && console.error(\"Monaco initialization: error:\", R)), () => o.current ? pe() : p.cancel();\n  }), l(() => {\n    var _o$current, _o$current2, _o$current3, _o$current4;\n    let p = h(d.current, e || t || \"\", r || a || \"\", m || n || \"\");\n    p !== ((_o$current = o.current) === null || _o$current === void 0 ? void 0 : _o$current.getModel()) && (y && _.set(f, (_o$current2 = o.current) === null || _o$current2 === void 0 ? void 0 : _o$current2.saveViewState()), (_o$current3 = o.current) !== null && _o$current3 !== void 0 && _o$current3.setModel(p), y && ((_o$current4 = o.current) === null || _o$current4 === void 0 ? void 0 : _o$current4.restoreViewState(_.get(m))));\n  }, [m], s), l(() => {\n    var _o$current5;\n    (_o$current5 = o.current) === null || _o$current5 === void 0 || _o$current5.updateOptions(x);\n  }, [x], s), l(() => {\n    !o.current || t === void 0 || (o.current.getOption(d.current.editor.EditorOption.readOnly) ? o.current.setValue(t) : t !== o.current.getValue() && (B.current = !0, o.current.executeEdits(\"\", [{\n      range: o.current.getModel().getFullModelRange(),\n      text: t,\n      forceMoveMarkers: !0\n    }]), o.current.pushUndoStop(), B.current = !1));\n  }, [t], s), l(() => {\n    var _o$current6, _d$current;\n    let p = (_o$current6 = o.current) === null || _o$current6 === void 0 ? void 0 : _o$current6.getModel();\n    p && a && ((_d$current = d.current) === null || _d$current === void 0 ? void 0 : _d$current.editor.setModelLanguage(p, a));\n  }, [a], s), l(() => {\n    var _o$current7;\n    g !== void 0 && ((_o$current7 = o.current) === null || _o$current7 === void 0 ? void 0 : _o$current7.revealLine(g));\n  }, [g], s), l(() => {\n    var _d$current2;\n    (_d$current2 = d.current) === null || _d$current2 === void 0 || _d$current2.editor.setTheme(E);\n  }, [E], s);\n  let X = _e(() => {\n    if (!(!b.current || !d.current) && !Q.current) {\n      var _d$current3;\n      U.current(d.current);\n      let p = m || n,\n        R = h(d.current, t || e || \"\", r || a || \"\", p || \"\");\n      o.current = (_d$current3 = d.current) === null || _d$current3 === void 0 ? void 0 : _d$current3.editor.create(b.current, _objectSpread({\n        model: R,\n        automaticLayout: !0\n      }, x), P), y && o.current.restoreViewState(_.get(p)), d.current.editor.setTheme(E), g !== void 0 && o.current.revealLine(g), u(!0), Q.current = !0;\n    }\n  }, [e, r, n, t, a, m, x, P, y, E, g]);\n  W(() => {\n    s && L.current(o.current, d.current);\n  }, [s]), W(() => {\n    !c && !s && X();\n  }, [c, s, X]), i.current = t, W(() => {\n    var _I$current, _o$current8;\n    s && O && ((_I$current = I.current) !== null && _I$current !== void 0 && _I$current.dispose(), I.current = (_o$current8 = o.current) === null || _o$current8 === void 0 ? void 0 : _o$current8.onDidChangeModelContent(p => {\n      B.current || O(o.current.getValue(), p);\n    }));\n  }, [s, O]), W(() => {\n    if (s) {\n      let p = d.current.editor.onDidChangeMarkers(R => {\n        var _o$current$getModel;\n        let G = (_o$current$getModel = o.current.getModel()) === null || _o$current$getModel === void 0 ? void 0 : _o$current$getModel.uri;\n        if (G && R.find(J => J.path === G.path)) {\n          let J = d.current.editor.getModelMarkers({\n            resource: G\n          });\n          T === null || T === void 0 || T(J);\n        }\n      });\n      return () => {\n        p === null || p === void 0 || p.dispose();\n      };\n    }\n    return () => {};\n  }, [s, T]);\n  function pe() {\n    var _I$current2, _o$current$getModel2;\n    (_I$current2 = I.current) !== null && _I$current2 !== void 0 && _I$current2.dispose(), V ? y && _.set(m, o.current.saveViewState()) : (_o$current$getModel2 = o.current.getModel()) === null || _o$current$getModel2 === void 0 ? void 0 : _o$current$getModel2.dispose(), o.current.dispose();\n  }\n  return We.createElement(H, {\n    width: z,\n    height: F,\n    isEditorReady: s,\n    loading: N,\n    _ref: b,\n    className: j,\n    wrapperProps: A\n  });\n}\nvar fe = Ve;\nvar de = ze(fe);\nvar Ft = de;\nexport { we as DiffEditor, de as Editor, Ft as default, _t as loader, Le as useMonaco };", "map": {"version": 3, "names": ["_t", "memo", "Te", "ke", "useState", "re", "useRef", "S", "useCallback", "oe", "useEffect", "ne", "Se", "ye", "K", "le", "wrapper", "display", "position", "textAlign", "fullWidth", "width", "hide", "v", "me", "ae", "container", "height", "justifyContent", "alignItems", "Y", "Me", "_ref", "children", "e", "createElement", "style", "Z", "$", "Ee", "_ref2", "r", "isEditorReady", "n", "loading", "t", "a", "className", "m", "wrapperProps", "E", "_objectSpread", "ref", "ee", "H", "xe", "Ce", "k", "ge", "Re", "he", "arguments", "length", "undefined", "current", "l", "D", "h", "De", "be", "editor", "getModel", "te", "createModel", "<PERSON><PERSON>", "parse", "Oe", "_ref3", "original", "modified", "language", "originalLanguage", "modifiedLanguage", "originalModelPath", "modifiedModelPath", "keepCurrentOriginalModel", "g", "keepCurrentModifiedModel", "N", "theme", "x", "P", "options", "y", "V", "z", "F", "j", "beforeMount", "A", "onMount", "q", "M", "O", "T", "s", "u", "c", "w", "d", "o", "b", "i", "init", "then", "f", "catch", "type", "console", "error", "I", "cancel", "getOriginalEditor", "setModel", "getModifiedEditor", "getOption", "EditorOption", "readOnly", "setValue", "getValue", "executeEdits", "range", "getFullModelRange", "text", "forceMoveMarkers", "pushUndoStop", "_u$current", "setModelLanguage", "_c$current", "setTheme", "_u$current2", "updateOptions", "L", "_u$current3", "U", "_c$current2", "createDiffEditor", "automaticLayout", "_u$current4", "_i$original", "_i$modified", "_u$current5", "dispose", "ie", "we", "Ie", "ce", "Pe", "__getMonacoInstance", "_n", "Le", "ze", "We", "ue", "W", "C", "_e", "Ne", "Ue", "ve", "He", "se", "_", "Map", "Ve", "_ref4", "defaultValue", "defaultLanguage", "defaultPath", "value", "path", "line", "overrideServices", "saveViewState", "keepCurrentModel", "onChange", "onValidate", "Q", "B", "p", "R", "pe", "_o$current", "_o$current2", "_o$current3", "_o$current4", "set", "restoreViewState", "get", "_o$current5", "_o$current6", "_d$current", "_o$current7", "revealLine", "_d$current2", "X", "_d$current3", "create", "model", "_I$current", "_o$current8", "onDidChangeModelContent", "onDidChangeMarkers", "_o$current$getModel", "G", "uri", "find", "J", "getModelMarkers", "resource", "_I$current2", "_o$current$getModel2", "fe", "de", "Ft", "DiffE<PERSON>or", "Editor", "default", "loader", "useMonaco"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/node_modules/@monaco-editor/react/src/index.ts", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/node_modules/@monaco-editor/react/src/DiffEditor/index.ts", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/node_modules/@monaco-editor/react/src/DiffEditor/DiffEditor.tsx", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/node_modules/@monaco-editor/react/src/MonacoContainer/index.ts", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/node_modules/@monaco-editor/react/src/MonacoContainer/MonacoContainer.tsx", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/node_modules/@monaco-editor/react/src/MonacoContainer/styles.ts", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/node_modules/@monaco-editor/react/src/Loading/Loading.tsx", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/node_modules/@monaco-editor/react/src/Loading/styles.ts", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/node_modules/@monaco-editor/react/src/Loading/index.ts", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/node_modules/@monaco-editor/react/src/hooks/useMount/index.ts", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/node_modules/@monaco-editor/react/src/hooks/useUpdate/index.ts", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/node_modules/@monaco-editor/react/src/utils/index.ts", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/node_modules/@monaco-editor/react/src/hooks/useMonaco/index.ts", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/node_modules/@monaco-editor/react/src/Editor/index.ts", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/node_modules/@monaco-editor/react/src/Editor/Editor.tsx", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/node_modules/@monaco-editor/react/src/hooks/usePrevious/index.ts"], "sourcesContent": ["import loader from '@monaco-editor/loader';\nexport { loader };\n\nimport DiffEditor from './DiffEditor';\nexport * from './DiffEditor/types';\nexport { DiffEditor };\n\nimport useMonaco from './hooks/useMonaco';\nexport { useMonaco };\n\nimport Editor from './Editor';\nexport * from './Editor/types';\nexport { Editor };\nexport default Editor;\n\n// Monaco\nimport type * as monaco from 'monaco-editor/esm/vs/editor/editor.api';\nexport type Monaco = typeof monaco;\n\n// Default themes\nexport type Theme = 'vs-dark' | 'light';\n", "import { memo } from 'react';\n\nimport DiffEditor from './DiffEditor';\n\nexport * from './types';\n\nexport default memo(DiffEditor);\n", "'use client';\n\nimport React, { useState, useRef, useCallback, useEffect } from 'react';\nimport loader from '@monaco-editor/loader';\n\nimport MonacoContainer from '../MonacoContainer';\nimport useMount from '../hooks/useMount';\nimport useUpdate from '../hooks/useUpdate';\nimport { noop, getOrCreateModel } from '../utils';\nimport { type DiffEditorProps, type MonacoDiffEditor } from './types';\nimport { type Monaco } from '..';\n\nfunction DiffEditor({\n  original,\n  modified,\n  language,\n  originalLanguage,\n  modifiedLanguage,\n  originalModelPath,\n  modifiedModelPath,\n  keepCurrentOriginalModel = false,\n  keepCurrentModifiedModel = false,\n  theme = 'light',\n  loading = 'Loading...',\n  options = {},\n  height = '100%',\n  width = '100%',\n  className,\n  wrapperProps = {},\n  beforeMount = noop,\n  onMount = noop,\n}: DiffEditorProps) {\n  const [isEditorReady, setIsEditorReady] = useState(false);\n  const [isMonacoMounting, setIsMonacoMounting] = useState(true);\n  const editorRef = useRef<MonacoDiffEditor | null>(null);\n  const monacoRef = useRef<Monaco | null>(null);\n  const containerRef = useRef<HTMLDivElement>(null);\n  const onMountRef = useRef(onMount);\n  const beforeMountRef = useRef(beforeMount);\n  const preventCreation = useRef(false);\n\n  useMount(() => {\n    const cancelable = loader.init();\n\n    cancelable\n      .then((monaco) => (monacoRef.current = monaco) && setIsMonacoMounting(false))\n      .catch(\n        (error) =>\n          error?.type !== 'cancelation' && console.error('Monaco initialization: error:', error),\n      );\n\n    return () => (editorRef.current ? disposeEditor() : cancelable.cancel());\n  });\n\n  useUpdate(\n    () => {\n      if (editorRef.current && monacoRef.current) {\n        const originalEditor = editorRef.current.getOriginalEditor();\n        const model = getOrCreateModel(\n          monacoRef.current,\n          original || '',\n          originalLanguage || language || 'text',\n          originalModelPath || '',\n        );\n\n        if (model !== originalEditor.getModel()) {\n          originalEditor.setModel(model);\n        }\n      }\n    },\n    [originalModelPath],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      if (editorRef.current && monacoRef.current) {\n        const modifiedEditor = editorRef.current.getModifiedEditor();\n        const model = getOrCreateModel(\n          monacoRef.current,\n          modified || '',\n          modifiedLanguage || language || 'text',\n          modifiedModelPath || '',\n        );\n\n        if (model !== modifiedEditor.getModel()) {\n          modifiedEditor.setModel(model);\n        }\n      }\n    },\n    [modifiedModelPath],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      const modifiedEditor = editorRef.current!.getModifiedEditor();\n      if (modifiedEditor.getOption(monacoRef.current!.editor.EditorOption.readOnly)) {\n        modifiedEditor.setValue(modified || '');\n      } else {\n        if (modified !== modifiedEditor.getValue()) {\n          modifiedEditor.executeEdits('', [\n            {\n              range: modifiedEditor.getModel()!.getFullModelRange(),\n              text: modified || '',\n              forceMoveMarkers: true,\n            },\n          ]);\n\n          modifiedEditor.pushUndoStop();\n        }\n      }\n    },\n    [modified],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      editorRef.current?.getModel()?.original.setValue(original || '');\n    },\n    [original],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      const { original, modified } = editorRef.current!.getModel()!;\n\n      monacoRef.current!.editor.setModelLanguage(original, originalLanguage || language || 'text');\n      monacoRef.current!.editor.setModelLanguage(modified, modifiedLanguage || language || 'text');\n    },\n    [language, originalLanguage, modifiedLanguage],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      monacoRef.current?.editor.setTheme(theme);\n    },\n    [theme],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      editorRef.current?.updateOptions(options);\n    },\n    [options],\n    isEditorReady,\n  );\n\n  const setModels = useCallback(() => {\n    if (!monacoRef.current) return;\n    beforeMountRef.current(monacoRef.current);\n    const originalModel = getOrCreateModel(\n      monacoRef.current,\n      original || '',\n      originalLanguage || language || 'text',\n      originalModelPath || '',\n    );\n\n    const modifiedModel = getOrCreateModel(\n      monacoRef.current,\n      modified || '',\n      modifiedLanguage || language || 'text',\n      modifiedModelPath || '',\n    );\n\n    editorRef.current?.setModel({\n      original: originalModel,\n      modified: modifiedModel,\n    });\n  }, [\n    language,\n    modified,\n    modifiedLanguage,\n    original,\n    originalLanguage,\n    originalModelPath,\n    modifiedModelPath,\n  ]);\n\n  const createEditor = useCallback(() => {\n    if (!preventCreation.current && containerRef.current) {\n      editorRef.current = monacoRef.current!.editor.createDiffEditor(containerRef.current, {\n        automaticLayout: true,\n        ...options,\n      });\n\n      setModels();\n\n      monacoRef.current?.editor.setTheme(theme);\n\n      setIsEditorReady(true);\n      preventCreation.current = true;\n    }\n  }, [options, theme, setModels]);\n\n  useEffect(() => {\n    if (isEditorReady) {\n      onMountRef.current(editorRef.current!, monacoRef.current!);\n    }\n  }, [isEditorReady]);\n\n  useEffect(() => {\n    !isMonacoMounting && !isEditorReady && createEditor();\n  }, [isMonacoMounting, isEditorReady, createEditor]);\n\n  function disposeEditor() {\n    const models = editorRef.current?.getModel();\n\n    if (!keepCurrentOriginalModel) {\n      models?.original?.dispose();\n    }\n\n    if (!keepCurrentModifiedModel) {\n      models?.modified?.dispose();\n    }\n\n    editorRef.current?.dispose();\n  }\n\n  return (\n    <MonacoContainer\n      width={width}\n      height={height}\n      isEditorReady={isEditorReady}\n      loading={loading}\n      _ref={containerRef}\n      className={className}\n      wrapperProps={wrapperProps}\n    />\n  );\n}\n\nexport default DiffEditor;\n", "import { memo } from 'react';\n\nimport MonacoContainer from './MonacoContainer';\n\nexport default memo(MonacoContainer);\n", "import React from 'react';\n\nimport styles from './styles';\nimport Loading from '../Loading';\nimport { type ContainerProps } from './types';\n\n// ** forwardref render functions do not support proptypes or defaultprops **\n// one of the reasons why we use a separate prop for passing ref instead of using forwardref\n\nfunction MonacoContainer({\n  width,\n  height,\n  isEditorReady,\n  loading,\n  _ref,\n  className,\n  wrapperProps,\n}: ContainerProps) {\n  return (\n    <section style={{ ...styles.wrapper, width, height }} {...wrapperProps}>\n      {!isEditorReady && <Loading>{loading}</Loading>}\n      <div\n        ref={_ref}\n        style={{ ...styles.fullWidth, ...(!isEditorReady && styles.hide) }}\n        className={className}\n      />\n    </section>\n  );\n}\n\nexport default MonacoContainer;\n", "import { type CSSProperties } from 'react';\n\nconst styles: Record<string, CSSProperties> = {\n  wrapper: {\n    display: 'flex',\n    position: 'relative',\n    textAlign: 'initial',\n  },\n  fullWidth: {\n    width: '100%',\n  },\n  hide: {\n    display: 'none',\n  },\n};\n\nexport default styles;\n", "import React, { type PropsWithChildren } from 'react';\n\nimport styles from './styles';\n\nfunction Loading({ children }: PropsWithChildren) {\n  return <div style={styles.container}>{children}</div>;\n}\n\nexport default Loading;\n", "import { type CSSProperties } from 'react';\n\nconst styles: Record<string, CSSProperties> = {\n  container: {\n    display: 'flex',\n    height: '100%',\n    width: '100%',\n    justifyContent: 'center',\n    alignItems: 'center',\n  },\n};\n\nexport default styles;\n", "import Loading from './Loading';\n\nexport default Loading;\n", "import { useEffect, type EffectCallback } from 'react';\n\nfunction useMount(effect: EffectCallback) {\n  useEffect(effect, []);\n}\n\nexport default useMount;\n", "import { useEffect, useRef, type DependencyList, type EffectCallback } from 'react';\n\nfunction useUpdate(effect: EffectCallback, deps: DependencyList, applyChanges = true) {\n  const isInitialMount = useRef(true);\n\n  useEffect(\n    isInitialMount.current || !applyChanges\n      ? () => {\n          isInitialMount.current = false;\n        }\n      : effect,\n    deps,\n  );\n}\n\nexport default useUpdate;\n", "import { type Monaco } from '..';\n\n/**\n * noop is a helper function that does nothing\n * @returns undefined\n */\nfunction noop() {\n  /** no-op */\n}\n\n/**\n * getOrCreateModel is a helper function that will return a model if it exists\n * or create a new model if it does not exist.\n * This is useful for when you want to create a model for a file that may or may not exist yet.\n * @param monaco The monaco instance\n * @param value The value of the model\n * @param language The language of the model\n * @param path The path of the model\n * @returns The model that was found or created\n */\nfunction getOrCreateModel(monaco: Monaco, value: string, language: string, path: string) {\n  return getModel(monaco, path) || createModel(monaco, value, language, path);\n}\n\n/**\n * getModel is a helper function that will return a model if it exists\n * or return undefined if it does not exist.\n * @param monaco The monaco instance\n * @param path The path of the model\n * @returns The model that was found or undefined\n */\nfunction getModel(monaco: Monaco, path: string) {\n  return monaco.editor.getModel(createModelUri(monaco, path));\n}\n\n/**\n * createModel is a helper function that will create a new model\n * @param monaco The monaco instance\n * @param value The value of the model\n * @param language The language of the model\n * @param path The path of the model\n * @returns The model that was created\n */\nfunction createModel(monaco: Monaco, value: string, language?: string, path?: string) {\n  return monaco.editor.createModel(\n    value,\n    language,\n    path ? createModelUri(monaco, path) : undefined,\n  );\n}\n\n/**\n * createModelUri is a helper function that will create a new model uri\n * @param monaco The monaco instance\n * @param path The path of the model\n * @returns The model uri that was created\n */\nfunction createModelUri(monaco: Monaco, path: string) {\n  return monaco.Uri.parse(path);\n}\n\nexport { noop, getOrCreateModel };\n", "import { useState } from 'react';\nimport loader from '@monaco-editor/loader';\n\nimport useMount from '../useMount';\n\nfunction useMonaco() {\n  const [monaco, setMonaco] = useState(loader.__getMonacoInstance());\n\n  useMount(() => {\n    let cancelable: ReturnType<typeof loader.init>;\n\n    if (!monaco) {\n      cancelable = loader.init();\n\n      cancelable.then((monaco) => {\n        setMonaco(monaco);\n      });\n    }\n\n    return () => cancelable?.cancel();\n  });\n\n  return monaco;\n}\n\nexport default useMonaco;\n", "import { memo } from 'react';\n\nimport Editor from './Editor';\n\nexport * from './types';\n\nexport default memo(Editor);\n", "'use client';\n\nimport React, { useState, useEffect, useRef, useCallback } from 'react';\nimport loader from '@monaco-editor/loader';\nimport useMount from '../hooks/useMount';\nimport useUpdate from '../hooks/useUpdate';\nimport usePrevious from '../hooks/usePrevious';\nimport { type IDisposable, type editor } from 'monaco-editor';\nimport { noop, getOrCreateModel } from '../utils';\nimport { type EditorProps } from './types';\nimport { type Monaco } from '..';\nimport MonacoContainer from '../MonacoContainer';\n\nconst viewStates = new Map();\n\nfunction Editor({\n  defaultValue,\n  defaultLanguage,\n  defaultPath,\n  value,\n  language,\n  path,\n  /* === */\n  theme = 'light',\n  line,\n  loading = 'Loading...',\n  options = {},\n  overrideServices = {},\n  saveViewState = true,\n  keepCurrentModel = false,\n  /* === */\n  width = '100%',\n  height = '100%',\n  className,\n  wrapperProps = {},\n  /* === */\n  beforeMount = noop,\n  onMount = noop,\n  onChange,\n  onValidate = noop,\n}: EditorProps) {\n  const [isEditorReady, setIsEditorReady] = useState(false);\n  const [isMonacoMounting, setIsMonacoMounting] = useState(true);\n  const monacoRef = useRef<Monaco | null>(null);\n  const editorRef = useRef<editor.IStandaloneCodeEditor | null>(null);\n  const containerRef = useRef<HTMLDivElement>(null);\n  const onMountRef = useRef(onMount);\n  const beforeMountRef = useRef(beforeMount);\n  const subscriptionRef = useRef<IDisposable>();\n  const valueRef = useRef(value);\n  const previousPath = usePrevious(path);\n  const preventCreation = useRef(false);\n  const preventTriggerChangeEvent = useRef<boolean>(false);\n\n  useMount(() => {\n    const cancelable = loader.init();\n\n    cancelable\n      .then((monaco) => (monacoRef.current = monaco) && setIsMonacoMounting(false))\n      .catch(\n        (error) =>\n          error?.type !== 'cancelation' && console.error('Monaco initialization: error:', error),\n      );\n\n    return () => (editorRef.current ? disposeEditor() : cancelable.cancel());\n  });\n\n  useUpdate(\n    () => {\n      const model = getOrCreateModel(\n        monacoRef.current!,\n        defaultValue || value || '',\n        defaultLanguage || language || '',\n        path || defaultPath || '',\n      );\n\n      if (model !== editorRef.current?.getModel()) {\n        if (saveViewState) viewStates.set(previousPath, editorRef.current?.saveViewState());\n        editorRef.current?.setModel(model);\n        if (saveViewState) editorRef.current?.restoreViewState(viewStates.get(path));\n      }\n    },\n    [path],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      editorRef.current?.updateOptions(options);\n    },\n    [options],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      if (!editorRef.current || value === undefined) return;\n      if (editorRef.current.getOption(monacoRef.current!.editor.EditorOption.readOnly)) {\n        editorRef.current.setValue(value);\n      } else if (value !== editorRef.current.getValue()) {\n        preventTriggerChangeEvent.current = true;\n        editorRef.current.executeEdits('', [\n          {\n            range: editorRef.current.getModel()!.getFullModelRange(),\n            text: value,\n            forceMoveMarkers: true,\n          },\n        ]);\n\n        editorRef.current.pushUndoStop();\n        preventTriggerChangeEvent.current = false;\n      }\n    },\n    [value],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      const model = editorRef.current?.getModel();\n      if (model && language) monacoRef.current?.editor.setModelLanguage(model, language);\n    },\n    [language],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      // reason for undefined check: https://github.com/suren-atoyan/monaco-react/pull/188\n      if (line !== undefined) {\n        editorRef.current?.revealLine(line);\n      }\n    },\n    [line],\n    isEditorReady,\n  );\n\n  useUpdate(\n    () => {\n      monacoRef.current?.editor.setTheme(theme);\n    },\n    [theme],\n    isEditorReady,\n  );\n\n  const createEditor = useCallback(() => {\n    if (!containerRef.current || !monacoRef.current) return;\n    if (!preventCreation.current) {\n      beforeMountRef.current(monacoRef.current);\n      const autoCreatedModelPath = path || defaultPath;\n\n      const defaultModel = getOrCreateModel(\n        monacoRef.current,\n        value || defaultValue || '',\n        defaultLanguage || language || '',\n        autoCreatedModelPath || '',\n      );\n\n      editorRef.current = monacoRef.current?.editor.create(\n        containerRef.current,\n        {\n          model: defaultModel,\n          automaticLayout: true,\n          ...options,\n        },\n        overrideServices,\n      );\n\n      saveViewState && editorRef.current.restoreViewState(viewStates.get(autoCreatedModelPath));\n\n      monacoRef.current.editor.setTheme(theme);\n\n      if (line !== undefined) {\n        editorRef.current.revealLine(line);\n      }\n\n      setIsEditorReady(true);\n      preventCreation.current = true;\n    }\n  }, [\n    defaultValue,\n    defaultLanguage,\n    defaultPath,\n    value,\n    language,\n    path,\n    options,\n    overrideServices,\n    saveViewState,\n    theme,\n    line,\n  ]);\n\n  useEffect(() => {\n    if (isEditorReady) {\n      onMountRef.current(editorRef.current!, monacoRef.current!);\n    }\n  }, [isEditorReady]);\n\n  useEffect(() => {\n    !isMonacoMounting && !isEditorReady && createEditor();\n  }, [isMonacoMounting, isEditorReady, createEditor]);\n\n  // subscription\n  // to avoid unnecessary updates (attach - dispose listener) in subscription\n  valueRef.current = value;\n\n  // onChange\n  useEffect(() => {\n    if (isEditorReady && onChange) {\n      subscriptionRef.current?.dispose();\n      subscriptionRef.current = editorRef.current?.onDidChangeModelContent((event) => {\n        if (!preventTriggerChangeEvent.current) {\n          onChange(editorRef.current!.getValue(), event);\n        }\n      });\n    }\n  }, [isEditorReady, onChange]);\n\n  // onValidate\n  useEffect(() => {\n    if (isEditorReady) {\n      const changeMarkersListener = monacoRef.current!.editor.onDidChangeMarkers((uris) => {\n        const editorUri = editorRef.current!.getModel()?.uri;\n\n        if (editorUri) {\n          const currentEditorHasMarkerChanges = uris.find((uri) => uri.path === editorUri.path);\n          if (currentEditorHasMarkerChanges) {\n            const markers = monacoRef.current!.editor.getModelMarkers({\n              resource: editorUri,\n            });\n            onValidate?.(markers);\n          }\n        }\n      });\n\n      return () => {\n        changeMarkersListener?.dispose();\n      };\n    }\n    return () => {\n      // eslint happy\n    };\n  }, [isEditorReady, onValidate]);\n\n  function disposeEditor() {\n    subscriptionRef.current?.dispose();\n\n    if (keepCurrentModel) {\n      saveViewState && viewStates.set(path, editorRef.current!.saveViewState());\n    } else {\n      editorRef.current!.getModel()?.dispose();\n    }\n\n    editorRef.current!.dispose();\n  }\n\n  return (\n    <MonacoContainer\n      width={width}\n      height={height}\n      isEditorReady={isEditorReady}\n      loading={loading}\n      _ref={containerRef}\n      className={className}\n      wrapperProps={wrapperProps}\n    />\n  );\n}\n\nexport default Editor;\n", "import { useEffect, useRef } from 'react';\n\nfunction usePrevious<T>(value: T) {\n  const ref = useRef<T>();\n\n  useEffect(() => {\n    ref.current = value;\n  }, [value]);\n\n  return ref.current;\n}\n\nexport default usePrevious;\n"], "mappings": ";AAAA,OAAOA,EAAA,MAAY;ACAnB,SAASC,IAAA,IAAAC,EAAA,QAAY;ACErB,OAAOC,EAAA,IAASC,QAAA,IAAAC,EAAA,EAAUC,MAAA,IAAAC,CAAA,EAAQC,WAAA,IAAAC,EAAA,EAAaC,SAAA,IAAAC,EAAA,QAAiB;AAChE,OAAOC,EAAA,MAAY;ACHnB,SAASX,IAAA,IAAAY,EAAA,QAAY;ACArB,OAAOC,CAAA,MAAW;ACElB,IAAMC,EAAA,GAAwC;IAC5CC,OAAA,EAAS;MACPC,OAAA,EAAS;MACTC,QAAA,EAAU;MACVC,SAAA,EAAW;IACb;IACAC,SAAA,EAAW;MACTC,KAAA,EAAO;IACT;IACAC,IAAA,EAAM;MACJL,OAAA,EAAS;IACX;EACF;EAEOM,CAAA,GAAQR,EAAA;AChBf,OAAOS,EAAA,MAAuC;ACE9C,IAAMC,EAAA,GAAwC;IAC5CC,SAAA,EAAW;MACTT,OAAA,EAAS;MACTU,MAAA,EAAQ;MACRN,KAAA,EAAO;MACPO,cAAA,EAAgB;MAChBC,UAAA,EAAY;IACd;EACF;EAEOC,CAAA,GAAQL,EAAA;ADRf,SAASM,GAAAC,IAAA,EAAyC;EAAA,IAAjC;IAAEC,QAAA,EAAAC;EAAS,IAAAF,IAAA;EAC1B,OAAOR,EAAA,CAAAW,aAAA,CAAC;IAAIC,KAAA,EAAON,CAAA,CAAOJ;EAAA,GAAYQ,CAAS,CACjD;AAAA;AAEA,IAAOG,CAAA,GAAQN,EAAA;AENf,IAAOO,CAAA,GAAQD,CAAA;AJOf,SAASE,GAAAC,KAAA,EAQU;EAAA,IARM;IACvBnB,KAAA,EAAAa,CAAA;IACAP,MAAA,EAAAc,CAAA;IACAC,aAAA,EAAAC,CAAA;IACAC,OAAA,EAAAC,CAAA;IACAb,IAAA,EAAAc,CAAA;IACAC,SAAA,EAAAC,CAAA;IACAC,YAAA,EAAAC;EACF,IAAAV,KAAA;EACE,OACE1B,CAAA,CAAAqB,aAAA,CAAC,WAAAgB,aAAA;IAAQf,KAAA,EAAAe,aAAA,CAAAA,aAAA,KAAY5B,CAAA,CAAOP,OAAA;MAASK,KAAA,EAAAa,CAAA;MAAOP,MAAA,EAAAc;IAAO;EAAA,GAAOS,CAAA,GACvD,CAACP,CAAA,IAAiB7B,CAAA,CAAAqB,aAAA,CAACG,CAAA,QAASO,CAAQ,GACrC/B,CAAA,CAAAqB,aAAA,CAAC;IACCiB,GAAA,EAAKN,CAAA;IACLV,KAAA,EAAAe,aAAA,CAAAA,aAAA,KAAY5B,CAAA,CAAOH,SAAA,GAAe,CAACuB,CAAA,IAAiBpB,CAAA,CAAOD,IAAM;IACjEyB,SAAA,EAAWC;EAAA,CACb,CACF,CAEJ;AAAA;AAEA,IAAOK,EAAA,GAAQd,EAAA;AD1Bf,IAAOe,CAAA,GAAQzC,EAAA,CAAKwC,EAAe;AMJnC,SAAS3C,SAAA,IAAA6C,EAAA,QAAsC;AAE/C,SAASC,GAAStB,CAAA,EAAwB;EACxCqB,EAAA,CAAUrB,CAAA,EAAQ,EAAE,CACtB;AAAA;AAEA,IAAOuB,CAAA,GAAQD,EAAA;ACNf,SAAS9C,SAAA,IAAAgD,EAAA,EAAWpD,MAAA,IAAAqD,EAAA,QAAwD;AAE5E,SAASC,GAAU1B,CAAA,EAAwBO,CAAA,EAA2C;EAAA,IAArBE,CAAA,GAAAkB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAe;EAC9E,IAAMhB,CAAA,GAAiBc,EAAA,CAAO,EAAI;EAElCD,EAAA,CACEb,CAAA,CAAemB,OAAA,IAAW,CAACrB,CAAA,GACvB,MAAM;IACJE,CAAA,CAAemB,OAAA,GAAU,EAC3B;EAAA,IACA9B,CAAA,EACJO,CACF,CACF;AAAA;AAEA,IAAOwB,CAAA,GAAQL,EAAA;ACTf,SAASM,EAAA,EAAO,CAEhB;AAYA,SAASC,EAAiBjC,CAAA,EAAgBO,CAAA,EAAeE,CAAA,EAAkBE,CAAA,EAAc;EACvF,OAAOuB,EAAA,CAASlC,CAAA,EAAQW,CAAI,KAAKwB,EAAA,CAAYnC,CAAA,EAAQO,CAAA,EAAOE,CAAA,EAAUE,CAAI,CAC5E;AAAA;AASA,SAASuB,GAASlC,CAAA,EAAgBO,CAAA,EAAc;EAC9C,OAAOP,CAAA,CAAOoC,MAAA,CAAOC,QAAA,CAASC,EAAA,CAAetC,CAAA,EAAQO,CAAI,CAAC,CAC5D;AAAA;AAUA,SAAS4B,GAAYnC,CAAA,EAAgBO,CAAA,EAAeE,CAAA,EAAmBE,CAAA,EAAe;EACpF,OAAOX,CAAA,CAAOoC,MAAA,CAAOG,WAAA,CACnBhC,CAAA,EACAE,CAAA,EACAE,CAAA,GAAO2B,EAAA,CAAetC,CAAA,EAAQW,CAAI,IAAI,MACxC,CACF;AAAA;AAQA,SAAS2B,GAAetC,CAAA,EAAgBO,CAAA,EAAc;EACpD,OAAOP,CAAA,CAAOwC,GAAA,CAAIC,KAAA,CAAMlC,CAAI,CAC9B;AAAA;AT/CA,SAASmC,GAAAC,KAAA,EAmBW;EAAA,IAnBA;IAClBC,QAAA,EAAA5C,CAAA;IACA6C,QAAA,EAAAtC,CAAA;IACAuC,QAAA,EAAArC,CAAA;IACAsC,gBAAA,EAAApC,CAAA;IACAqC,gBAAA,EAAApC,CAAA;IACAqC,iBAAA,EAAAnC,CAAA;IACAoC,iBAAA,EAAAlC,CAAA;IACAmC,wBAAA,EAAAC,CAAA,GAA2B;IAC3BC,wBAAA,EAAAC,CAAA,GAA2B;IAC3BC,KAAA,EAAAC,CAAA,GAAQ;IACR9C,OAAA,EAAA+C,CAAA,GAAU;IACVC,OAAA,EAAAC,CAAA,GAAU,CAAC;IACXlE,MAAA,EAAAmE,CAAA,GAAS;IACTzE,KAAA,EAAA0E,CAAA,GAAQ;IACRhD,SAAA,EAAAiD,CAAA;IACA/C,YAAA,EAAAgD,CAAA,GAAe,CAAC;IAChBC,WAAA,EAAAC,CAAA,GAAcjC,CAAA;IACdkC,OAAA,EAAAC,CAAA,GAAUnC;EACZ,IAAAW,KAAA;EACE,IAAM,CAACyB,CAAA,EAAeC,CAAgB,IAAIlG,EAAA,CAAS,EAAK;IAClD,CAACmG,CAAA,EAAkBC,CAAmB,IAAIpG,EAAA,CAAS,EAAI;IACvDqG,CAAA,GAAYnG,CAAA,CAAgC,IAAI;IAChDoG,CAAA,GAAYpG,CAAA,CAAsB,IAAI;IACtCqG,CAAA,GAAerG,CAAA,CAAuB,IAAI;IAC1CsG,CAAA,GAAatG,CAAA,CAAO8F,CAAO;IAC3BS,CAAA,GAAiBvG,CAAA,CAAO4F,CAAW;IACnCY,CAAA,GAAkBxG,CAAA,CAAO,EAAK;EAEpCkD,CAAA,CAAS,MAAM;IACb,IAAMuD,CAAA,GAAapG,EAAA,CAAOqG,IAAA,CAAK;IAE/B,OAAAD,CAAA,CACGE,IAAA,CAAMC,CAAA,KAAYR,CAAA,CAAU3C,OAAA,GAAUmD,CAAA,KAAWV,CAAA,CAAoB,EAAK,CAAC,EAC3EW,KAAA,CACED,CAAA,IACC,CAAAA,CAAA,aAAAA,CAAA,uBAAAA,CAAA,CAAOE,IAAA,MAAS,iBAAiBC,OAAA,CAAQC,KAAA,CAAM,iCAAiCJ,CAAK,CACzF,GAEK,MAAOT,CAAA,CAAU1C,OAAA,GAAUwD,CAAA,CAAc,IAAIR,CAAA,CAAWS,MAAA,CAAO,CACxE;EAAA,CAAC,GAEDxD,CAAA,CACE,MAAM;IACJ,IAAIyC,CAAA,CAAU1C,OAAA,IAAW2C,CAAA,CAAU3C,OAAA,EAAS;MAC1C,IAAMgD,CAAA,GAAiBN,CAAA,CAAU1C,OAAA,CAAQ0D,iBAAA,CAAkB;QACrDP,CAAA,GAAQhD,CAAA,CACZwC,CAAA,CAAU3C,OAAA,EACV9B,CAAA,IAAY,IACZW,CAAA,IAAoBF,CAAA,IAAY,QAChCK,CAAA,IAAqB,EACvB;MAEImE,CAAA,KAAUH,CAAA,CAAezC,QAAA,CAAS,KACpCyC,CAAA,CAAeW,QAAA,CAASR,CAAK;IAAA;EAGnC,GACA,CAACnE,CAAiB,GAClBsD,CACF,GAEArC,CAAA,CACE,MAAM;IACJ,IAAIyC,CAAA,CAAU1C,OAAA,IAAW2C,CAAA,CAAU3C,OAAA,EAAS;MAC1C,IAAMgD,CAAA,GAAiBN,CAAA,CAAU1C,OAAA,CAAQ4D,iBAAA,CAAkB;QACrDT,CAAA,GAAQhD,CAAA,CACZwC,CAAA,CAAU3C,OAAA,EACVvB,CAAA,IAAY,IACZK,CAAA,IAAoBH,CAAA,IAAY,QAChCO,CAAA,IAAqB,EACvB;MAEIiE,CAAA,KAAUH,CAAA,CAAezC,QAAA,CAAS,KACpCyC,CAAA,CAAeW,QAAA,CAASR,CAAK;IAAA;EAGnC,GACA,CAACjE,CAAiB,GAClBoD,CACF,GAEArC,CAAA,CACE,MAAM;IACJ,IAAM+C,CAAA,GAAiBN,CAAA,CAAU1C,OAAA,CAAS4D,iBAAA,CAAkB;IACxDZ,CAAA,CAAea,SAAA,CAAUlB,CAAA,CAAU3C,OAAA,CAASM,MAAA,CAAOwD,YAAA,CAAaC,QAAQ,IAC1Ef,CAAA,CAAegB,QAAA,CAASvF,CAAA,IAAY,EAAE,IAElCA,CAAA,KAAauE,CAAA,CAAeiB,QAAA,CAAS,MACvCjB,CAAA,CAAekB,YAAA,CAAa,IAAI,CAC9B;MACEC,KAAA,EAAOnB,CAAA,CAAezC,QAAA,CAAS,EAAG6D,iBAAA,CAAkB;MACpDC,IAAA,EAAM5F,CAAA,IAAY;MAClB6F,gBAAA,EAAkB;IACpB,CACF,CAAC,GAEDtB,CAAA,CAAeuB,YAAA,CAAa,EAGlC;EAAA,GACA,CAAC9F,CAAQ,GACT6D,CACF,GAEArC,CAAA,CACE,MAAM;IAAA,IAAAuE,UAAA;IACJ,CAAAA,UAAA,GAAA9B,CAAA,CAAU1C,OAAA,cAAAwE,UAAA,gBAAAA,UAAA,GAAVA,UAAA,CAAmBjE,QAAA,CAAS,eAAAiE,UAAA,eAA5BA,UAAA,CAA+B1D,QAAA,CAASkD,QAAA,CAAS9F,CAAA,IAAY,EAAE,CACjE;EAAA,GACA,CAACA,CAAQ,GACToE,CACF,GAEArC,CAAA,CACE,MAAM;IACJ,IAAM;MAAEa,QAAA,EAAAkC,CAAA;MAAUjC,QAAA,EAAAoC;IAAS,IAAIT,CAAA,CAAU1C,OAAA,CAASO,QAAA,CAAS;IAE3DoC,CAAA,CAAU3C,OAAA,CAASM,MAAA,CAAOmE,gBAAA,CAAiBzB,CAAA,EAAUnE,CAAA,IAAoBF,CAAA,IAAY,MAAM,GAC3FgE,CAAA,CAAU3C,OAAA,CAASM,MAAA,CAAOmE,gBAAA,CAAiBtB,CAAA,EAAUrE,CAAA,IAAoBH,CAAA,IAAY,MAAM,CAC7F;EAAA,GACA,CAACA,CAAA,EAAUE,CAAA,EAAkBC,CAAgB,GAC7CwD,CACF,GAEArC,CAAA,CACE,MAAM;IAAA,IAAAyE,UAAA;IACJ,CAAAA,UAAA,GAAA/B,CAAA,CAAU3C,OAAA,cAAA0E,UAAA,eAAVA,UAAA,CAAmBpE,MAAA,CAAOqE,QAAA,CAASjD,CAAK,CAC1C;EAAA,GACA,CAACA,CAAK,GACNY,CACF,GAEArC,CAAA,CACE,MAAM;IAAA,IAAA2E,WAAA;IACJ,CAAAA,WAAA,GAAAlC,CAAA,CAAU1C,OAAA,cAAA4E,WAAA,eAAVA,WAAA,CAAmBC,aAAA,CAAchD,CAAO,CAC1C;EAAA,GACA,CAACA,CAAO,GACRS,CACF;EAEA,IAAMwC,CAAA,GAAYrI,EAAA,CAAY,MAAM;MAAA,IAAAsI,WAAA;MAClC,IAAI,CAACpC,CAAA,CAAU3C,OAAA,EAAS;MACxB8C,CAAA,CAAe9C,OAAA,CAAQ2C,CAAA,CAAU3C,OAAO;MACxC,IAAMgD,CAAA,GAAgB7C,CAAA,CACpBwC,CAAA,CAAU3C,OAAA,EACV9B,CAAA,IAAY,IACZW,CAAA,IAAoBF,CAAA,IAAY,QAChCK,CAAA,IAAqB,EACvB;QAEMmE,CAAA,GAAgBhD,CAAA,CACpBwC,CAAA,CAAU3C,OAAA,EACVvB,CAAA,IAAY,IACZK,CAAA,IAAoBH,CAAA,IAAY,QAChCO,CAAA,IAAqB,EACvB;MAEA,CAAA6F,WAAA,GAAArC,CAAA,CAAU1C,OAAA,cAAA+E,WAAA,eAAVA,WAAA,CAAmBpB,QAAA,CAAS;QAC1B7C,QAAA,EAAUkC,CAAA;QACVjC,QAAA,EAAUoC;MACZ,CAAC,CACH;IAAA,GAAG,CACDxE,CAAA,EACAF,CAAA,EACAK,CAAA,EACAZ,CAAA,EACAW,CAAA,EACAG,CAAA,EACAE,CACF,CAAC;IAEK8F,CAAA,GAAevI,EAAA,CAAY,MAAM;MAAA,IAAAwI,WAAA;MACjC,CAAClC,CAAA,CAAgB/C,OAAA,IAAW4C,CAAA,CAAa5C,OAAA,KAC3C0C,CAAA,CAAU1C,OAAA,GAAU2C,CAAA,CAAU3C,OAAA,CAASM,MAAA,CAAO4E,gBAAA,CAAiBtC,CAAA,CAAa5C,OAAA,EAAAb,aAAA;QAC1EgG,eAAA,EAAiB;MAAA,GACdtD,CACL,CAAC,GAEDiD,CAAA,CAAU,IAAAG,WAAA,GAEVtC,CAAA,CAAU3C,OAAA,cAAAiF,WAAA,eAAVA,WAAA,CAAmB3E,MAAA,CAAOqE,QAAA,CAASjD,CAAK,GAExCa,CAAA,CAAiB,EAAI,GACrBQ,CAAA,CAAgB/C,OAAA,GAAU,GAE9B;IAAA,GAAG,CAAC6B,CAAA,EAASH,CAAA,EAAOoD,CAAS,CAAC;EAE9BnI,EAAA,CAAU,MAAM;IACV2F,CAAA,IACFO,CAAA,CAAW7C,OAAA,CAAQ0C,CAAA,CAAU1C,OAAA,EAAU2C,CAAA,CAAU3C,OAAQ,CAE7D;EAAA,GAAG,CAACsC,CAAa,CAAC,GAElB3F,EAAA,CAAU,MAAM;IACd,CAAC6F,CAAA,IAAoB,CAACF,CAAA,IAAiB0C,CAAA,CAAa,CACtD;EAAA,GAAG,CAACxC,CAAA,EAAkBF,CAAA,EAAe0C,CAAY,CAAC;EAElD,SAASxB,EAAA,EAAgB;IAAA,IAAA4B,WAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,WAAA;IACvB,IAAMvC,CAAA,IAAAoC,WAAA,GAAS1C,CAAA,CAAU1C,OAAA,cAAAoF,WAAA,uBAAVA,WAAA,CAAmB7E,QAAA,CAAS;IAEtCe,CAAA,IACH0B,CAAA,aAAAA,CAAA,gBAAAqC,WAAA,GAAArC,CAAA,CAAQlC,QAAA,cAAAuE,WAAA,eAARA,WAAA,CAAkBG,OAAA,CAAQ,GAGvBhE,CAAA,IACHwB,CAAA,aAAAA,CAAA,gBAAAsC,WAAA,GAAAtC,CAAA,CAAQjC,QAAA,cAAAuE,WAAA,eAARA,WAAA,CAAkBE,OAAA,CAAQ,IAAAD,WAAA,GAG5B7C,CAAA,CAAU1C,OAAA,cAAAuF,WAAA,uBAAVA,WAAA,CAAmBC,OAAA,CAAQ,CAC7B;EAAA;EAEA,OACErJ,EAAA,CAAAgC,aAAA,CAACmB,CAAA;IACCjC,KAAA,EAAO0E,CAAA;IACPpE,MAAA,EAAQmE,CAAA;IACRpD,aAAA,EAAe4D,CAAA;IACf1D,OAAA,EAAS+C,CAAA;IACT3D,IAAA,EAAM4E,CAAA;IACN7D,SAAA,EAAWiD,CAAA;IACX/C,YAAA,EAAcgD;EAAA,CAChB,CAEJ;AAAA;AAEA,IAAOwD,EAAA,GAAQ7E,EAAA;ADtOf,IAAO8E,EAAA,GAAQxJ,EAAA,CAAKuJ,EAAU;AWN9B,SAASrJ,QAAA,IAAAuJ,EAAA,QAAgB;AACzB,OAAOC,EAAA,MAAY;AAInB,SAASC,GAAA,EAAY;EACnB,IAAM,CAAC3H,CAAA,EAAQO,CAAS,IAAIkH,EAAA,CAASC,EAAA,CAAOE,mBAAA,CAAoB,CAAC;EAEjE,OAAArG,CAAA,CAAS,MAAM;IACb,IAAId,CAAA;IAEJ,OAAKT,CAAA,KACHS,CAAA,GAAaiH,EAAA,CAAO3C,IAAA,CAAK,GAEzBtE,CAAA,CAAWuE,IAAA,CAAMrE,CAAA,IAAW;MAC1BJ,CAAA,CAAUI,CAAM,CAClB;IAAA,CAAC,IAGI;MAAA,IAAAkH,EAAA;MAAA,QAAAA,EAAA,GAAMpH,CAAA,cAAAoH,EAAA,uBAAAA,EAAA,CAAYtC,MAAA,CAAO,CAClC;IAAA;EAAA,CAAC,GAEMvF,CACT;AAAA;AAEA,IAAO8H,EAAA,GAAQH,EAAA;ACzBf,SAAS5J,IAAA,IAAAgK,EAAA,QAAY;ACErB,OAAOC,EAAA,IAAS9J,QAAA,IAAA+J,EAAA,EAAUzJ,SAAA,IAAA0J,CAAA,EAAW9J,MAAA,IAAA+J,CAAA,EAAQ7J,WAAA,IAAA8J,EAAA,QAAmB;AAChE,OAAOC,EAAA,MAAY;ACHnB,SAAS7J,SAAA,IAAA8J,EAAA,EAAWlK,MAAA,IAAAmK,EAAA,QAAc;AAElC,SAASC,GAAexI,CAAA,EAAU;EAChC,IAAMO,CAAA,GAAMgI,EAAA,CAAU;EAEtB,OAAAD,EAAA,CAAU,MAAM;IACd/H,CAAA,CAAIuB,OAAA,GAAU9B,CAChB;EAAA,GAAG,CAACA,CAAK,CAAC,GAEHO,CAAA,CAAIuB,OACb;AAAA;AAEA,IAAO2G,EAAA,GAAQD,EAAA;ADCf,IAAME,CAAA,GAAa,IAAIC,GAAA;AAEvB,SAASC,GAAAC,KAAA,EAyBO;EAAA,IAzBA;IACdC,YAAA,EAAA9I,CAAA;IACA+I,eAAA,EAAAxI,CAAA;IACAyI,WAAA,EAAAvI,CAAA;IACAwI,KAAA,EAAAtI,CAAA;IACAmC,QAAA,EAAAlC,CAAA;IACAsI,IAAA,EAAApI,CAAA;IAEAyC,KAAA,EAAAvC,CAAA,GAAQ;IACRmI,IAAA,EAAA/F,CAAA;IACA1C,OAAA,EAAA4C,CAAA,GAAU;IACVI,OAAA,EAAAF,CAAA,GAAU,CAAC;IACX4F,gBAAA,EAAA3F,CAAA,GAAmB,CAAC;IACpB4F,aAAA,EAAA1F,CAAA,GAAgB;IAChB2F,gBAAA,EAAA1F,CAAA,GAAmB;IAEnBzE,KAAA,EAAA0E,CAAA,GAAQ;IACRpE,MAAA,EAAAqE,CAAA,GAAS;IACTjD,SAAA,EAAAkD,CAAA;IACAhD,YAAA,EAAAkD,CAAA,GAAe,CAAC;IAEhBD,WAAA,EAAAG,CAAA,GAAcnC,CAAA;IACdkC,OAAA,EAAAE,CAAA,GAAUpC,CAAA;IACVuH,QAAA,EAAAlF,CAAA;IACAmF,UAAA,EAAAlF,CAAA,GAAatC;EACf,IAAA6G,KAAA;EACE,IAAM,CAACtE,CAAA,EAAeC,CAAgB,IAAIyD,EAAA,CAAS,EAAK;IAClD,CAACxD,CAAA,EAAkBC,CAAmB,IAAIuD,EAAA,CAAS,EAAI;IACvDtD,CAAA,GAAYwD,CAAA,CAAsB,IAAI;IACtCvD,CAAA,GAAYuD,CAAA,CAA4C,IAAI;IAC5DtD,CAAA,GAAesD,CAAA,CAAuB,IAAI;IAC1CvB,CAAA,GAAauB,CAAA,CAAO/D,CAAO;IAC3B0C,CAAA,GAAiBqB,CAAA,CAAOhE,CAAW;IACnCmB,CAAA,GAAkB6C,CAAA,CAAoB;IACtCrD,CAAA,GAAWqD,CAAA,CAAOxH,CAAK;IACvBsE,CAAA,GAAewD,EAAA,CAAY3H,CAAI;IAC/B2I,CAAA,GAAkBtB,CAAA,CAAO,EAAK;IAC9BuB,CAAA,GAA4BvB,CAAA,CAAgB,EAAK;EAEvD5G,CAAA,CAAS,MAAM;IACb,IAAMoI,CAAA,GAAatB,EAAA,CAAOtD,IAAA,CAAK;IAE/B,OAAA4E,CAAA,CACG3E,IAAA,CAAM4E,CAAA,KAAYjF,CAAA,CAAU7C,OAAA,GAAU8H,CAAA,KAAWlF,CAAA,CAAoB,EAAK,CAAC,EAC3EQ,KAAA,CACE0E,CAAA,IACC,CAAAA,CAAA,aAAAA,CAAA,uBAAAA,CAAA,CAAOzE,IAAA,MAAS,iBAAiBC,OAAA,CAAQC,KAAA,CAAM,iCAAiCuE,CAAK,CACzF,GAEK,MAAOhF,CAAA,CAAU9C,OAAA,GAAU+H,EAAA,CAAc,IAAIF,CAAA,CAAWpE,MAAA,CAAO,CACxE;EAAA,CAAC,GAEDxD,CAAA,CACE,MAAM;IAAA,IAAA+H,UAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,WAAA;IACJ,IAAMN,CAAA,GAAQ1H,CAAA,CACZ0C,CAAA,CAAU7C,OAAA,EACV9B,CAAA,IAAgBW,CAAA,IAAS,IACzBJ,CAAA,IAAmBK,CAAA,IAAY,IAC/BE,CAAA,IAAQL,CAAA,IAAe,EACzB;IAEIkJ,CAAA,OAAAG,UAAA,GAAUlF,CAAA,CAAU9C,OAAA,cAAAgI,UAAA,uBAAVA,UAAA,CAAmBzH,QAAA,CAAS,OACpCsB,CAAA,IAAe+E,CAAA,CAAWwB,GAAA,CAAIjF,CAAA,GAAA8E,WAAA,GAAcnF,CAAA,CAAU9C,OAAA,cAAAiI,WAAA,uBAAVA,WAAA,CAAmBV,aAAA,CAAc,CAAC,IAAAW,WAAA,GAClFpF,CAAA,CAAU9C,OAAA,cAAAkI,WAAA,eAAVA,WAAA,CAAmBvE,QAAA,CAASkE,CAAK,GAC7BhG,CAAA,MAAAsG,WAAA,GAAerF,CAAA,CAAU9C,OAAA,cAAAmI,WAAA,uBAAVA,WAAA,CAAmBE,gBAAA,CAAiBzB,CAAA,CAAW0B,GAAA,CAAItJ,CAAI,CAAC,GAE/E;EAAA,GACA,CAACA,CAAI,GACLyD,CACF,GAEAxC,CAAA,CACE,MAAM;IAAA,IAAAsI,WAAA;IACJ,CAAAA,WAAA,GAAAzF,CAAA,CAAU9C,OAAA,cAAAuI,WAAA,eAAVA,WAAA,CAAmB1D,aAAA,CAAcnD,CAAO,CAC1C;EAAA,GACA,CAACA,CAAO,GACRe,CACF,GAEAxC,CAAA,CACE,MAAM;IACA,CAAC6C,CAAA,CAAU9C,OAAA,IAAWnB,CAAA,KAAU,WAChCiE,CAAA,CAAU9C,OAAA,CAAQ6D,SAAA,CAAUhB,CAAA,CAAU7C,OAAA,CAASM,MAAA,CAAOwD,YAAA,CAAaC,QAAQ,IAC7EjB,CAAA,CAAU9C,OAAA,CAAQgE,QAAA,CAASnF,CAAK,IACvBA,CAAA,KAAUiE,CAAA,CAAU9C,OAAA,CAAQiE,QAAA,CAAS,MAC9C2D,CAAA,CAA0B5H,OAAA,GAAU,IACpC8C,CAAA,CAAU9C,OAAA,CAAQkE,YAAA,CAAa,IAAI,CACjC;MACEC,KAAA,EAAOrB,CAAA,CAAU9C,OAAA,CAAQO,QAAA,CAAS,EAAG6D,iBAAA,CAAkB;MACvDC,IAAA,EAAMxF,CAAA;MACNyF,gBAAA,EAAkB;IACpB,CACF,CAAC,GAEDxB,CAAA,CAAU9C,OAAA,CAAQuE,YAAA,CAAa,GAC/BqD,CAAA,CAA0B5H,OAAA,GAAU,IAExC;EAAA,GACA,CAACnB,CAAK,GACN4D,CACF,GAEAxC,CAAA,CACE,MAAM;IAAA,IAAAuI,WAAA,EAAAC,UAAA;IACJ,IAAMZ,CAAA,IAAAW,WAAA,GAAQ1F,CAAA,CAAU9C,OAAA,cAAAwI,WAAA,uBAAVA,WAAA,CAAmBjI,QAAA,CAAS;IACtCsH,CAAA,IAAS/I,CAAA,MAAA2J,UAAA,GAAU5F,CAAA,CAAU7C,OAAA,cAAAyI,UAAA,uBAAVA,UAAA,CAAmBnI,MAAA,CAAOmE,gBAAA,CAAiBoD,CAAA,EAAO/I,CAAQ,CACnF;EAAA,GACA,CAACA,CAAQ,GACT2D,CACF,GAEAxC,CAAA,CACE,MAAM;IAAA,IAAAyI,WAAA;IAEApH,CAAA,KAAS,YAAAoH,WAAA,GACX5F,CAAA,CAAU9C,OAAA,cAAA0I,WAAA,uBAAVA,WAAA,CAAmBC,UAAA,CAAWrH,CAAI,CAEtC;EAAA,GACA,CAACA,CAAI,GACLmB,CACF,GAEAxC,CAAA,CACE,MAAM;IAAA,IAAA2I,WAAA;IACJ,CAAAA,WAAA,GAAA/F,CAAA,CAAU7C,OAAA,cAAA4I,WAAA,eAAVA,WAAA,CAAmBtI,MAAA,CAAOqE,QAAA,CAASzF,CAAK,CAC1C;EAAA,GACA,CAACA,CAAK,GACNuD,CACF;EAEA,IAAMoG,CAAA,GAAevC,EAAA,CAAY,MAAM;IACrC,IAAI,GAACvD,CAAA,CAAa/C,OAAA,IAAW,CAAC6C,CAAA,CAAU7C,OAAA,KACpC,CAAC2H,CAAA,CAAgB3H,OAAA,EAAS;MAAA,IAAA8I,WAAA;MAC5B9D,CAAA,CAAehF,OAAA,CAAQ6C,CAAA,CAAU7C,OAAO;MACxC,IAAM6H,CAAA,GAAuB7I,CAAA,IAAQL,CAAA;QAE/BmJ,CAAA,GAAe3H,CAAA,CACnB0C,CAAA,CAAU7C,OAAA,EACVnB,CAAA,IAASX,CAAA,IAAgB,IACzBO,CAAA,IAAmBK,CAAA,IAAY,IAC/B+I,CAAA,IAAwB,EAC1B;MAEA/E,CAAA,CAAU9C,OAAA,IAAA8I,WAAA,GAAUjG,CAAA,CAAU7C,OAAA,cAAA8I,WAAA,uBAAVA,WAAA,CAAmBxI,MAAA,CAAOyI,MAAA,CAC5ChG,CAAA,CAAa/C,OAAA,EAAAb,aAAA;QAEX6J,KAAA,EAAOlB,CAAA;QACP3C,eAAA,EAAiB;MAAA,GACdzD,CACL,GACAC,CACF,GAEAE,CAAA,IAAiBiB,CAAA,CAAU9C,OAAA,CAAQqI,gBAAA,CAAiBzB,CAAA,CAAW0B,GAAA,CAAIT,CAAoB,CAAC,GAExFhF,CAAA,CAAU7C,OAAA,CAAQM,MAAA,CAAOqE,QAAA,CAASzF,CAAK,GAEnCoC,CAAA,KAAS,UACXwB,CAAA,CAAU9C,OAAA,CAAQ2I,UAAA,CAAWrH,CAAI,GAGnCoB,CAAA,CAAiB,EAAI,GACrBiF,CAAA,CAAgB3H,OAAA,GAAU;IAAA;EAE9B,GAAG,CACD9B,CAAA,EACAO,CAAA,EACAE,CAAA,EACAE,CAAA,EACAC,CAAA,EACAE,CAAA,EACA0C,CAAA,EACAC,CAAA,EACAE,CAAA,EACA3C,CAAA,EACAoC,CACF,CAAC;EAED8E,CAAA,CAAU,MAAM;IACV3D,CAAA,IACFqC,CAAA,CAAW9E,OAAA,CAAQ8C,CAAA,CAAU9C,OAAA,EAAU6C,CAAA,CAAU7C,OAAQ,CAE7D;EAAA,GAAG,CAACyC,CAAa,CAAC,GAElB2D,CAAA,CAAU,MAAM;IACd,CAACzD,CAAA,IAAoB,CAACF,CAAA,IAAiBoG,CAAA,CAAa,CACtD;EAAA,GAAG,CAAClG,CAAA,EAAkBF,CAAA,EAAeoG,CAAY,CAAC,GAIlD7F,CAAA,CAAShD,OAAA,GAAUnB,CAAA,EAGnBuH,CAAA,CAAU,MAAM;IAAA,IAAA6C,UAAA,EAAAC,WAAA;IACVzG,CAAA,IAAiBF,CAAA,KACnB,CAAA0G,UAAA,GAAAzF,CAAA,CAAgBxD,OAAA,cAAAiJ,UAAA,eAAhBA,UAAA,CAAyBzD,OAAA,CAAQ,GACjChC,CAAA,CAAgBxD,OAAA,IAAAkJ,WAAA,GAAUpG,CAAA,CAAU9C,OAAA,cAAAkJ,WAAA,uBAAVA,WAAA,CAAmBC,uBAAA,CAAyBtB,CAAA,IAAU;MACzED,CAAA,CAA0B5H,OAAA,IAC7BuC,CAAA,CAASO,CAAA,CAAU9C,OAAA,CAASiE,QAAA,CAAS,GAAG4D,CAAK,CAEjD;IAAA,CAAC,EAEL;EAAA,GAAG,CAACpF,CAAA,EAAeF,CAAQ,CAAC,GAG5B6D,CAAA,CAAU,MAAM;IACd,IAAI3D,CAAA,EAAe;MACjB,IAAMoF,CAAA,GAAwBhF,CAAA,CAAU7C,OAAA,CAASM,MAAA,CAAO8I,kBAAA,CAAoBtB,CAAA,IAAS;QAAA,IAAAuB,mBAAA;QACnF,IAAMC,CAAA,IAAAD,mBAAA,GAAYvG,CAAA,CAAU9C,OAAA,CAASO,QAAA,CAAS,eAAA8I,mBAAA,uBAA5BA,mBAAA,CAA+BE,GAAA;QAEjD,IAAID,CAAA,IACoCxB,CAAA,CAAK0B,IAAA,CAAMC,CAAA,IAAQA,CAAA,CAAIrC,IAAA,KAASkC,CAAA,CAAUlC,IAAI,GACjD;UACjC,IAAMqC,CAAA,GAAU5G,CAAA,CAAU7C,OAAA,CAASM,MAAA,CAAOoJ,eAAA,CAAgB;YACxDC,QAAA,EAAUL;UACZ,CAAC;UACD9G,CAAA,aAAAA,CAAA,eAAAA,CAAA,CAAaiH,CAAO;QAAA;MAG1B,CAAC;MAED,OAAO,MAAM;QACX5B,CAAA,aAAAA,CAAA,eAAAA,CAAA,CAAuBrC,OAAA,CAAQ,CACjC;MAAA;IAAA;IAEF,OAAO,MAAM,CAEb,CACF;EAAA,GAAG,CAAC/C,CAAA,EAAeD,CAAU,CAAC;EAE9B,SAASuF,GAAA,EAAgB;IAAA,IAAA6B,WAAA,EAAAC,oBAAA;IACvB,CAAAD,WAAA,GAAApG,CAAA,CAAgBxD,OAAA,cAAA4J,WAAA,eAAhBA,WAAA,CAAyBpE,OAAA,CAAQ,GAE7B1D,CAAA,GACFD,CAAA,IAAiB+E,CAAA,CAAWwB,GAAA,CAAIpJ,CAAA,EAAM8D,CAAA,CAAU9C,OAAA,CAASuH,aAAA,CAAc,CAAC,KAAAsC,oBAAA,GAExE/G,CAAA,CAAU9C,OAAA,CAASO,QAAA,CAAS,eAAAsJ,oBAAA,uBAA5BA,oBAAA,CAA+BrE,OAAA,CAAQ,GAGzC1C,CAAA,CAAU9C,OAAA,CAASwF,OAAA,CAAQ,CAC7B;EAAA;EAEA,OACEU,EAAA,CAAA/H,aAAA,CAACmB,CAAA;IACCjC,KAAA,EAAO0E,CAAA;IACPpE,MAAA,EAAQqE,CAAA;IACRtD,aAAA,EAAe+D,CAAA;IACf7D,OAAA,EAAS4C,CAAA;IACTxD,IAAA,EAAM+E,CAAA;IACNhE,SAAA,EAAWkD,CAAA;IACXhD,YAAA,EAAckD;EAAA,CAChB,CAEJ;AAAA;AAEA,IAAO2H,EAAA,GAAQhD,EAAA;ADxQf,IAAOiD,EAAA,GAAQ9D,EAAA,CAAK6D,EAAM;AbO1B,IAAOE,EAAA,GAAQD,EAAA;AAAA,SAAArE,EAAA,IAAAuE,UAAA,EAAAF,EAAA,IAAAG,MAAA,EAAAF,EAAA,IAAAG,OAAA,EAAAnO,EAAA,IAAAoO,MAAA,EAAApE,EAAA,IAAAqE,SAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}