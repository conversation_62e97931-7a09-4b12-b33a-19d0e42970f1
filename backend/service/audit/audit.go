package audit

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/cainuro/orchestrator/internal/audit"
	"github.com/cainuro/orchestrator/internal/config"
	"go.uber.org/zap"
)

// Service defines the audit service interface
type Service interface {
	LogEvent(ctx context.Context, event *AuditEvent) error
	GetEvents(ctx context.Context, filter *EventFilter) ([]*AuditEvent, error)
	GetEventsByUser(ctx context.Context, userID string, limit int) ([]*AuditEvent, error)
	GetEventsByResource(ctx context.Context, resourceID string, limit int) ([]*AuditEvent, error)
}

// AuditEvent represents an audit event
type AuditEvent struct {
	ID         string                 `json:"id"`
	Timestamp  time.Time              `json:"timestamp"`
	UserID     string                 `json:"user_id"`
	Action     string                 `json:"action"`
	Resource   string                 `json:"resource"`
	ResourceID string                 `json:"resource_id"`
	Details    map[string]interface{} `json:"details"`
	IPAddress  string                 `json:"ip_address"`
	UserAgent  string                 `json:"user_agent"`
	Success    bool                   `json:"success"`
	Error      string                 `json:"error,omitempty"`
}

// EventFilter represents filters for querying audit events
type EventFilter struct {
	UserID     string    `json:"user_id,omitempty"`
	Action     string    `json:"action,omitempty"`
	Resource   string    `json:"resource,omitempty"`
	StartTime  time.Time `json:"start_time,omitempty"`
	EndTime    time.Time `json:"end_time,omitempty"`
	Limit      int       `json:"limit,omitempty"`
	Offset     int       `json:"offset,omitempty"`
}

// auditService implements the audit service
type auditService struct {
	config    *config.Config
	logger    *zap.Logger
	auditSink *audit.ImmuSink
}

// NewService creates a new audit service
func NewService(cfg *config.Config, logger *zap.Logger, auditSink *audit.ImmuSink) (Service, error) {
	return &auditService{
		config:    cfg,
		logger:    logger,
		auditSink: auditSink,
	}, nil
}

// LogEvent logs an audit event
func (s *auditService) LogEvent(ctx context.Context, event *AuditEvent) error {
	if event.ID == "" {
		event.ID = fmt.Sprintf("audit_%d", time.Now().UnixNano())
	}
	if event.Timestamp.IsZero() {
		event.Timestamp = time.Now()
	}

	// Convert event to JSON for storage
	eventData, err := json.Marshal(event)
	if err != nil {
		s.logger.Error("Failed to marshal audit event", zap.Error(err))
		return fmt.Errorf("failed to marshal audit event: %w", err)
	}

	// Store in ImmuDB for tamper-proof audit trail
	if err := s.auditSink.LogEvent(ctx, event.ID, eventData); err != nil {
		s.logger.Error("Failed to store audit event", zap.Error(err))
		return fmt.Errorf("failed to store audit event: %w", err)
	}

	s.logger.Info("Audit event logged",
		zap.String("event_id", event.ID),
		zap.String("user_id", event.UserID),
		zap.String("action", event.Action),
		zap.String("resource", event.Resource),
	)

	return nil
}

// GetEvents retrieves audit events based on filter
func (s *auditService) GetEvents(ctx context.Context, filter *EventFilter) ([]*AuditEvent, error) {
	events, err := s.auditSink.GetEvents(ctx, filter.Limit, filter.Offset)
	if err != nil {
		s.logger.Error("Failed to retrieve audit events", zap.Error(err))
		return nil, fmt.Errorf("failed to retrieve audit events: %w", err)
	}

	var auditEvents []*AuditEvent
	for _, eventData := range events {
		var event AuditEvent
		if err := json.Unmarshal(eventData, &event); err != nil {
			s.logger.Warn("Failed to unmarshal audit event", zap.Error(err))
			continue
		}

		// Apply filters
		if filter.UserID != "" && event.UserID != filter.UserID {
			continue
		}
		if filter.Action != "" && event.Action != filter.Action {
			continue
		}
		if filter.Resource != "" && event.Resource != filter.Resource {
			continue
		}
		if !filter.StartTime.IsZero() && event.Timestamp.Before(filter.StartTime) {
			continue
		}
		if !filter.EndTime.IsZero() && event.Timestamp.After(filter.EndTime) {
			continue
		}

		auditEvents = append(auditEvents, &event)
	}

	return auditEvents, nil
}

// GetEventsByUser retrieves audit events for a specific user
func (s *auditService) GetEventsByUser(ctx context.Context, userID string, limit int) ([]*AuditEvent, error) {
	filter := &EventFilter{
		UserID: userID,
		Limit:  limit,
	}
	return s.GetEvents(ctx, filter)
}

// GetEventsByResource retrieves audit events for a specific resource
func (s *auditService) GetEventsByResource(ctx context.Context, resourceID string, limit int) ([]*AuditEvent, error) {
	filter := &EventFilter{
		Resource: resourceID,
		Limit:    limit,
	}
	return s.GetEvents(ctx, filter)
}
