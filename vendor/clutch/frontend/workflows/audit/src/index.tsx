import type { BaseWorkflowProps, WorkflowConfiguration } from "@clutch-sh/core";

import AuditEvent from "./audit-event";
import AuditLog from "./logs";

export interface AuditLogProps extends BaseWorkflowProps {
  detailsPathPrefix?: string;
  downloadPrefix?: string;
}

export interface WorkflowProps extends BaseWorkflowProps {}

const register = (): WorkflowConfiguration => {
  return {
    developer: {
      name: "Lyft",
      contactUrl: "mailto:<EMAIL>",
    },
    path: "audit",
    group: "Audit",
    displayName: "Audit Trail",
    defaultLayoutProps: {
      variant: "standard",
      usesContext: true,
    },
    routes: {
      landing: {
        path: "/",
        displayName: "Logs",
        description: "View audit log",
        component: AuditLog,
      },
      event: {
        path: "/event/:id",
        displayName: "Event Details",
        description: "View audit event",
        component: AuditEvent,
        hideNav: true,
        layoutProps: {
          usesContext: false,
        },
      },
    },
  };
};

export default register;
