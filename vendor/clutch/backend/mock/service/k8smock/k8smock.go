package k8smock

import (
	"context"
	"math/rand"

	"github.com/golang/protobuf/ptypes/any"
	"github.com/uber-go/tally/v4"
	"go.uber.org/zap"
	batchv1 "k8s.io/api/batch/v1"

	k8sv1 "github.com/lyft/clutch/backend/api/k8s/v1"
	"github.com/lyft/clutch/backend/service"
	k8sservice "github.com/lyft/clutch/backend/service/k8s"
)

type svc struct{}

func (s *svc) GetPodLogs(ctx context.Context, clientset, cluster, namespace, name string, opts *k8sv1.PodLogsOptions) (*k8sv1.GetPodLogsResponse, error) {
	return &k8sv1.GetPodLogsResponse{Logs: []*k8sv1.PodLogLine{
		{Ts: "1", S: "Hello"},
		{Ts: "2", S: "world"},
		{Ts: "3", S: "Fizz"},
		{Ts: "4", S: "Buzz"},
	}}, nil
}

func (s *svc) DescribeHPA(ctx context.Context, clientset, cluster, namespace, name string) (*k8sv1.HPA, error) {
	hpa := &k8sv1.HPA{
		Cluster:   "fake-cluster-name",
		Namespace: namespace,
		Name:      name,
		Sizing: &k8sv1.HPA_Sizing{
			MinReplicas:     1,
			MaxReplicas:     100,
			CurrentReplicas: uint32(rand.Int31n(100)),
			DesiredReplicas: uint32(rand.Int31n(100)),
		},
		Labels:      map[string]string{"Label key": "Value"},
		Annotations: map[string]string{"Annotation key": "Value"},
	}
	return hpa, nil
}

func (*svc) ResizeHPA(ctx context.Context, clientset, cluster, namespace, name string, sizing *k8sv1.ResizeHPARequest_Sizing) error {
	return nil
}

func (*svc) DeleteHPA(ctx context.Context, clientset, cluster, namespace, name string) error {
	return nil
}

func (*svc) Manager() k8sservice.ClientsetManager {
	return nil
}

func (s *svc) DescribePod(_ context.Context, clientset, cluster, namespace, name string) (*k8sv1.Pod, error) {
	pod := &k8sv1.Pod{
		Cluster:   "fake-cluster-name",
		Namespace: namespace,
		Name:      name,
		NodeIp:    "********",
		PodIp:     "*******",
		State:     k8sv1.Pod_State(rand.Intn(len(k8sv1.Pod_State_value))),
		//StartTime:   ptypes.TimestampNow(),
		Labels:      map[string]string{"Key": "value"},
		Annotations: map[string]string{"Key": "value"},
	}
	return pod, nil
}

func (s *svc) ListPods(_ context.Context, clientset, cluster, namespace string, listOptions *k8sv1.ListOptions) ([]*k8sv1.Pod, error) {
	pods := []*k8sv1.Pod{
		{
			Cluster:     cluster,
			Namespace:   namespace,
			Name:        "name1",
			NodeIp:      "********",
			PodIp:       "*******",
			State:       k8sv1.Pod_State(rand.Intn(len(k8sv1.Pod_State_value))),
			Labels:      listOptions.Labels,
			Annotations: map[string]string{"Key": "value"},
		},
		{
			Cluster:     cluster,
			Namespace:   namespace,
			Name:        "name2",
			NodeIp:      "********",
			PodIp:       "*******",
			State:       k8sv1.Pod_State(rand.Intn(len(k8sv1.Pod_State_value))),
			Labels:      listOptions.Labels,
			Annotations: map[string]string{"Key": "value"},
		},
	}
	return pods, nil
}

func (*svc) DescribeDeployment(ctx context.Context, clientset, cluster, namespace, name string) (*k8sv1.Deployment, error) {
	return &k8sv1.Deployment{
		Cluster:     cluster,
		Namespace:   namespace,
		Name:        "deployment1",
		Labels:      map[string]string{"Key": "value"},
		Annotations: map[string]string{"Key": "value"},
	}, nil
}

func (s *svc) ListDeployments(_ context.Context, clientset, cluster, namespace string, listOptions *k8sv1.ListOptions) ([]*k8sv1.Deployment, error) {
	deployments := []*k8sv1.Deployment{
		{
			Cluster:     cluster,
			Namespace:   namespace,
			Name:        "deployment1",
			Labels:      map[string]string{"Key": "value"},
			Annotations: map[string]string{"Key": "value"},
		},
		{
			Cluster:     cluster,
			Namespace:   namespace,
			Name:        "deployment2",
			Labels:      map[string]string{"Key": "value"},
			Annotations: map[string]string{"Key": "value"},
		},
	}
	return deployments, nil
}

func (*svc) UpdateDeployment(ctx context.Context, clientset, cluster, namespace, name string, fields *k8sv1.UpdateDeploymentRequest_Fields) error {
	return nil
}

func (*svc) DeleteDeployment(ctx context.Context, clientset, cluster, namespace, name string) error {
	return nil
}

func (*svc) DescribeStatefulSet(ctx context.Context, clientset, cluster, namespace, name string) (*k8sv1.StatefulSet, error) {
	return &k8sv1.StatefulSet{
		Cluster:     cluster,
		Namespace:   namespace,
		Name:        "statefulset1",
		Labels:      map[string]string{"Key": "value"},
		Annotations: map[string]string{"Key": "value"},
	}, nil
}

func (s *svc) ListStatefulSets(_ context.Context, clientset, cluster, namespace string, listOptions *k8sv1.ListOptions) ([]*k8sv1.StatefulSet, error) {
	statefulsets := []*k8sv1.StatefulSet{
		{
			Cluster:     cluster,
			Namespace:   namespace,
			Name:        "statefulset1",
			Labels:      map[string]string{"Key": "value"},
			Annotations: map[string]string{"Key": "value"},
		},
		{
			Cluster:     cluster,
			Namespace:   namespace,
			Name:        "statefulset2",
			Labels:      map[string]string{"Key": "value"},
			Annotations: map[string]string{"Key": "value"},
		},
	}
	return statefulsets, nil
}

func (*svc) UpdateStatefulSet(ctx context.Context, clientset, cluster, namespace, name string, fields *k8sv1.UpdateStatefulSetRequest_Fields) error {
	return nil
}

func (*svc) DeleteStatefulSet(ctx context.Context, clientset, cluster, namespace, name string) error {
	return nil
}

func (*svc) DeletePod(ctx context.Context, clientset, cluster, namespace, name string) error {
	return nil
}

func (s *svc) UpdatePod(ctx context.Context, clientset, cluster, namespace, name string, expectedObjectMetaFields *k8sv1.ExpectedObjectMetaFields, objectMetaFields *k8sv1.ObjectMetaFields, removeObjectMetaFields *k8sv1.RemoveObjectMetaFields) error {
	return nil
}

func (s *svc) DescribeService(_ context.Context, clientset, cluster, namespace, name string) (*k8sv1.Service, error) {
	return &k8sv1.Service{
		Cluster:     "fake-cluster-name",
		Namespace:   namespace,
		Name:        name,
		Type:        k8sv1.Service_Type(rand.Intn(len(k8sv1.Service_Type_value))),
		Labels:      map[string]string{"Key": "value"},
		Annotations: map[string]string{"Key": "value"},
	}, nil
}

func (s *svc) ListServices(_ context.Context, clientset, cluster, namespace string, listOptions *k8sv1.ListOptions) ([]*k8sv1.Service, error) {
	services := []*k8sv1.Service{
		{
			Cluster:   "fake-cluster-name",
			Namespace: namespace,
			Name:      "service1",
		},
		{
			Cluster:   "fake-cluster-name",
			Namespace: namespace,
			Name:      "service2",
		},
	}
	return services, nil
}

func (*svc) DeleteService(ctx context.Context, clientset, cluster, namespace, name string) error {
	return nil
}

func (s *svc) DescribeCronJob(_ context.Context, clientset, cluster, namespace, name string) (*k8sv1.CronJob, error) {
	return &k8sv1.CronJob{
		Cluster:     "fake-cluster-name",
		Namespace:   namespace,
		Name:        name,
		Schedule:    "0 0 1 1 *",
		Labels:      map[string]string{"Key": "value"},
		Annotations: map[string]string{"Key": "value"},
	}, nil
}

func (s *svc) ListCronJobs(_ context.Context, clientset, cluster, namespace string, listOptions *k8sv1.ListOptions) ([]*k8sv1.CronJob, error) {
	crons := []*k8sv1.CronJob{
		{
			Cluster:     "fake-cluster-name",
			Namespace:   namespace,
			Name:        "cronjob1",
			Schedule:    "0 0 1 1 *",
			Labels:      map[string]string{"Key": "value"},
			Annotations: map[string]string{"Key": "value"},
		},
		{
			Cluster:     "fake-cluster-name",
			Namespace:   namespace,
			Name:        "cronjob2",
			Schedule:    "0 0 1 1 *",
			Labels:      map[string]string{"Key": "value"},
			Annotations: map[string]string{"Key": "value"},
		},
	}
	return crons, nil
}

func (*svc) DeleteCronJob(ctx context.Context, clientset, cluster, namespace, name string) error {
	return nil
}

func (s *svc) DescribeConfigMap(_ context.Context, clientset, cluster, namespace, name string) (*k8sv1.ConfigMap, error) {
	return &k8sv1.ConfigMap{
		Cluster:     "fake-cluster-name",
		Namespace:   namespace,
		Name:        name,
		Labels:      map[string]string{"Key": "value"},
		Annotations: map[string]string{"Key": "value"},
	}, nil
}

func (*svc) DeleteConfigMap(ctx context.Context, clientset, cluster, namespace, name string) error {
	return nil
}

func (s *svc) ListConfigMaps(_ context.Context, clientset, cluster, namespace string, listOptions *k8sv1.ListOptions) ([]*k8sv1.ConfigMap, error) {
	configMaps := []*k8sv1.ConfigMap{
		{
			Cluster:     "fake-cluster-name",
			Namespace:   namespace,
			Name:        "name1",
			Labels:      listOptions.Labels,
			Annotations: map[string]string{"Key": "value"},
		},
		{
			Cluster:     "fake-cluster-name",
			Namespace:   namespace,
			Name:        "name2",
			Labels:      listOptions.Labels,
			Annotations: map[string]string{"Key": "value"},
		},
		{
			Cluster:     "fake-cluster-name",
			Namespace:   namespace,
			Name:        "foo-bar",
			Labels:      listOptions.Labels,
			Annotations: map[string]string{"Key": "value"},
		},
		{
			Cluster:     "fake-cluster-name",
			Namespace:   namespace,
			Name:        "stuff-things",
			Labels:      listOptions.Labels,
			Annotations: map[string]string{"Key": "value"},
		},
	}
	return configMaps, nil
}

func (s *svc) DescribeJob(_ context.Context, clientset, cluster, namespace, name string) (*k8sv1.Job, error) {
	return &k8sv1.Job{
		Cluster:     "fake-cluster-name",
		Namespace:   namespace,
		Name:        name,
		Labels:      map[string]string{"Key": "value"},
		Annotations: map[string]string{"Key": "value"},
	}, nil
}

func (s *svc) ListJobs(_ context.Context, clientset, cluster, namespace string, listOptions *k8sv1.ListOptions) ([]*k8sv1.Job, error) {
	jobs := []*k8sv1.Job{
		{
			Cluster:     "fake-cluster-name",
			Namespace:   namespace,
			Name:        "name1",
			Labels:      listOptions.Labels,
			Annotations: map[string]string{"Key": "value"},
		},
		{
			Cluster:     "fake-cluster-name",
			Namespace:   namespace,
			Name:        "name2",
			Labels:      listOptions.Labels,
			Annotations: map[string]string{"Key": "value"},
		},
	}
	return jobs, nil
}

func (*svc) DeleteJob(ctx context.Context, clientset, cluster, namespace, name string) error {
	return nil
}

func (*svc) CreateJob(ctx context.Context, clientset, cluster, namespace string, job *batchv1.Job) (*k8sv1.Job, error) {
	return &k8sv1.Job{
		Cluster:   "fake-cluster-name",
		Namespace: namespace,
	}, nil
}

func (s *svc) DescribeNamespace(_ context.Context, clientset, cluster, name string) (*k8sv1.Namespace, error) {
	return &k8sv1.Namespace{
		Cluster: "fake-cluster-name",
		Name:    name,
		Labels:  map[string]string{"Key": "value"},
	}, nil
}

func (s *svc) ListEvents(_ context.Context, clientset, cluster, namespace, name string, kind k8sv1.ObjectKind) ([]*k8sv1.Event, error) {
	return []*k8sv1.Event{
		{
			Name:               "event1",
			Reason:             "reason-1",
			Description:        "description-1",
			Cluster:            "fake-cluster-name",
			Namespace:          namespace,
			InvolvedObjectName: "pod1",
			Kind:               kind,
		},
	}, nil
}

func (s *svc) DescribeNode(_ context.Context, clientset, cluster, name string) (*k8sv1.Node, error) {
	return &k8sv1.Node{
		Cluster:       "fake-cluster-name",
		Name:          name,
		Unschedulable: false,
	}, nil
}

func (s *svc) UpdateNode(_ context.Context, clientset, cluster, name string, unschedulable bool) error {
	return nil
}

func (*svc) Clientsets(ctx context.Context) ([]string, error) {
	return []string{"fake-user@fake-cluster"}, nil
}

func (*svc) GetK8sClientset(ctx context.Context, clientset string) (k8sservice.ContextClientset, error) {
	return nil, nil
}

func New() k8sservice.Service {
	return &svc{}
}

func NewAsService(*any.Any, *zap.Logger, tally.Scope) (service.Service, error) {
	return New(), nil
}

func (s *svc) ListNamespaceEvents(_ context.Context, clientset, cluster, namespace string, types []k8sv1.EventType) ([]*k8sv1.Event, error) {
	return []*k8sv1.Event{
		{
			Name:               "event1",
			Reason:             "reason-1",
			Description:        "description-1",
			Cluster:            "fake-cluster-name",
			Namespace:          namespace,
			InvolvedObjectName: "pod1",
		},
	}, nil
}
