package featureflag

import (
	"context"
	"errors"
	"sync"

	"go.uber.org/zap"
)

// Errors
var (
	ErrFlagNotFound      = errors.New("feature flag not found")
	ErrFlagAlreadyExists = errors.New("feature flag already exists")
	ErrNoFlagsLoaded     = errors.New("no feature flags loaded")
)

// Service represents the feature flag service
type Service struct {
	logger *zap.Logger
	flags  map[string]Flag
	mu     sync.RWMutex
}

// Flag represents a feature flag
type Flag struct {
	ID          string      `json:"id"`
	Name        string      `json:"name"`
	Description string      `json:"description"`
	Enabled     bool        `json:"enabled"`
	Value       interface{} `json:"value,omitempty"`
	Type        string      `json:"type"`
	CreatedAt   string      `json:"created_at"`
	UpdatedAt   string      `json:"updated_at"`
}

// New creates a new feature flag service
func New(logger *zap.Logger) *Service {
	service := &Service{
		logger: logger,
		flags:  make(map[string]Flag),
	}

	// Initialize default feature flags
	service.initializeDefaultFlags()

	return service
}

// initializeDefaultFlags sets up default feature flags for CAINuro
func (s *Service) initializeDefaultFlags() {
	defaultFlags := []Flag{
		{
			ID:          "enhanced_search",
			Name:        "Enhanced Search",
			Description: "Enable advanced search with autocomplete and filters",
			Enabled:     true,
			Type:        "boolean",
			CreatedAt:   "2024-01-01T00:00:00Z",
			UpdatedAt:   "2024-01-01T00:00:00Z",
		},
		{
			ID:          "chaos_engineering",
			Name:        "Chaos Engineering",
			Description: "Enable chaos engineering experiments",
			Enabled:     false,
			Type:        "boolean",
			CreatedAt:   "2024-01-01T00:00:00Z",
			UpdatedAt:   "2024-01-01T00:00:00Z",
		},
		{
			ID:          "real_time_metrics",
			Name:        "Real-time Metrics",
			Description: "Enable real-time metrics dashboard",
			Enabled:     true,
			Type:        "boolean",
			CreatedAt:   "2024-01-01T00:00:00Z",
			UpdatedAt:   "2024-01-01T00:00:00Z",
		},
		{
			ID:          "feedback_system",
			Name:        "Feedback System",
			Description: "Enable in-app feedback collection",
			Enabled:     true,
			Type:        "boolean",
			CreatedAt:   "2024-01-01T00:00:00Z",
			UpdatedAt:   "2024-01-01T00:00:00Z",
		},
		{
			ID:          "bot_integration",
			Name:        "Bot Integration",
			Description: "Enable Slack/Teams bot integration",
			Enabled:     false,
			Type:        "boolean",
			CreatedAt:   "2024-01-01T00:00:00Z",
			UpdatedAt:   "2024-01-01T00:00:00Z",
		},
		{
			ID:          "advanced_k8s",
			Name:        "Advanced Kubernetes",
			Description: "Enable advanced Kubernetes resource management",
			Enabled:     true,
			Type:        "boolean",
			CreatedAt:   "2024-01-01T00:00:00Z",
			UpdatedAt:   "2024-01-01T00:00:00Z",
		},
		{
			ID:          "max_search_results",
			Name:        "Max Search Results",
			Description: "Maximum number of search results to return",
			Enabled:     true,
			Value:       100,
			Type:        "number",
			CreatedAt:   "2024-01-01T00:00:00Z",
			UpdatedAt:   "2024-01-01T00:00:00Z",
		},
		{
			ID:          "ui_theme",
			Name:        "UI Theme",
			Description: "Default UI theme for new users",
			Enabled:     true,
			Value:       "dark",
			Type:        "string",
			CreatedAt:   "2024-01-01T00:00:00Z",
			UpdatedAt:   "2024-01-01T00:00:00Z",
		},
	}

	s.mu.Lock()
	defer s.mu.Unlock()

	for _, flag := range defaultFlags {
		s.flags[flag.ID] = flag
	}

	s.logger.Info("Initialized feature flags", zap.Int("count", len(defaultFlags)))
}

// GetFlags returns all feature flags
func (s *Service) GetFlags(ctx context.Context) (map[string]Flag, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// Create a copy to avoid race conditions
	flags := make(map[string]Flag)
	for id, flag := range s.flags {
		flags[id] = flag
	}

	s.logger.Debug("Retrieved feature flags", zap.Int("count", len(flags)))
	return flags, nil
}

// GetFlag returns a specific feature flag
func (s *Service) GetFlag(ctx context.Context, flagID string) (*Flag, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	flag, exists := s.flags[flagID]
	if !exists {
		return nil, ErrFlagNotFound
	}

	return &flag, nil
}

// IsEnabled checks if a feature flag is enabled
func (s *Service) IsEnabled(ctx context.Context, flagID string) bool {
	s.mu.RLock()
	defer s.mu.RUnlock()

	flag, exists := s.flags[flagID]
	if !exists {
		s.logger.Warn("Feature flag not found", zap.String("flag_id", flagID))
		return false
	}

	return flag.Enabled
}

// UpdateFlag updates a feature flag
func (s *Service) UpdateFlag(ctx context.Context, flagID string, updates Flag) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	existing, exists := s.flags[flagID]
	if !exists {
		return ErrFlagNotFound
	}

	// Update only provided fields
	if updates.Name != "" {
		existing.Name = updates.Name
	}
	if updates.Description != "" {
		existing.Description = updates.Description
	}
	existing.Enabled = updates.Enabled
	if updates.Value != nil {
		existing.Value = updates.Value
	}
	existing.UpdatedAt = "2024-01-01T00:00:00Z" // In real implementation, use time.Now()

	s.flags[flagID] = existing
	s.logger.Info("Updated feature flag", zap.String("flag_id", flagID))

	return nil
}

// CreateFlag creates a new feature flag
func (s *Service) CreateFlag(ctx context.Context, flag Flag) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if _, exists := s.flags[flag.ID]; exists {
		return ErrFlagAlreadyExists
	}

	flag.CreatedAt = "2024-01-01T00:00:00Z" // In real implementation, use time.Now()
	flag.UpdatedAt = "2024-01-01T00:00:00Z"

	s.flags[flag.ID] = flag
	s.logger.Info("Created feature flag", zap.String("flag_id", flag.ID))

	return nil
}

// DeleteFlag deletes a feature flag
func (s *Service) DeleteFlag(ctx context.Context, flagID string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if _, exists := s.flags[flagID]; !exists {
		return ErrFlagNotFound
	}

	delete(s.flags, flagID)
	s.logger.Info("Deleted feature flag", zap.String("flag_id", flagID))

	return nil
}

// Health checks the health of the feature flag service
func (s *Service) Health(ctx context.Context) error {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// Simple health check - ensure we have flags loaded
	if len(s.flags) == 0 {
		return ErrNoFlagsLoaded
	}

	return nil
}
