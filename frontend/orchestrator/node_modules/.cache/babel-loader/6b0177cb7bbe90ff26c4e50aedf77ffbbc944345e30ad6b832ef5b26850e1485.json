{"ast": null, "code": "import verifyPlainObject from '../utils/verifyPlainObject';\nexport function wrapMapToPropsConstant(\n// * Note:\n//  It seems that the dispatch argument\n//  could be a dispatch function in some cases (ex: whenMapDispatchToPropsIsMissing)\n//  and a state object in some others (ex: whenMapStateToPropsIsMissing)\n// eslint-disable-next-line no-unused-vars\ngetConstant) {\n  return function initConstantSelector(dispatch) {\n    const constant = getConstant(dispatch);\n    function constantSelector() {\n      return constant;\n    }\n    constantSelector.dependsOnOwnProps = false;\n    return constantSelector;\n  };\n} // dependsOnOwnProps is used by createMapToPropsProxy to determine whether to pass props as args\n// to the mapToProps function being wrapped. It is also used by makePurePropsSelector to determine\n// whether mapToProps needs to be invoked when props have changed.\n//\n// A length of one signals that mapToProps does not depend on props from the parent component.\n// A length of zero is assumed to mean mapToProps is getting args via arguments or ...args and\n// therefore not reporting its length accurately..\n// TODO Can this get pulled out so that we can subscribe directly to the store if we don't need ownProps?\n\nexport function getDependsOnOwnProps(mapToProps) {\n  return mapToProps.dependsOnOwnProps ? Boolean(mapToProps.dependsOnOwnProps) : mapToProps.length !== 1;\n} // Used by whenMapStateToPropsIsFunction and whenMapDispatchToPropsIsFunction,\n// this function wraps mapToProps in a proxy function which does several things:\n//\n//  * Detects whether the mapToProps function being called depends on props, which\n//    is used by selectorFactory to decide if it should reinvoke on props changes.\n//\n//  * On first call, handles mapToProps if returns another function, and treats that\n//    new function as the true mapToProps for subsequent calls.\n//\n//  * On first call, verifies the first result is a plain object, in order to warn\n//    the developer that their mapToProps function is not returning a valid result.\n//\n\nexport function wrapMapToPropsFunc(mapToProps, methodName) {\n  return function initProxySelector(dispatch, _ref) {\n    let {\n      displayName\n    } = _ref;\n    const proxy = function mapToPropsProxy(stateOrDispatch, ownProps) {\n      return proxy.dependsOnOwnProps ? proxy.mapToProps(stateOrDispatch, ownProps) : proxy.mapToProps(stateOrDispatch, undefined);\n    }; // allow detectFactoryAndVerify to get ownProps\n\n    proxy.dependsOnOwnProps = true;\n    proxy.mapToProps = function detectFactoryAndVerify(stateOrDispatch, ownProps) {\n      proxy.mapToProps = mapToProps;\n      proxy.dependsOnOwnProps = getDependsOnOwnProps(mapToProps);\n      let props = proxy(stateOrDispatch, ownProps);\n      if (typeof props === 'function') {\n        proxy.mapToProps = props;\n        proxy.dependsOnOwnProps = getDependsOnOwnProps(props);\n        props = proxy(stateOrDispatch, ownProps);\n      }\n      if (process.env.NODE_ENV !== 'production') verifyPlainObject(props, displayName, methodName);\n      return props;\n    };\n    return proxy;\n  };\n}", "map": {"version": 3, "names": ["verifyPlainObject", "wrapMapToPropsConstant", "getConstant", "initConstantSelector", "dispatch", "constant", "constantSelector", "dependsOnOwnProps", "getDependsOnOwnProps", "mapToProps", "Boolean", "length", "wrapMapToPropsFunc", "methodName", "initProxySelector", "_ref", "displayName", "proxy", "mapToPropsProxy", "stateOrDispatch", "ownProps", "undefined", "detectFactoryAndVerify", "props", "process", "env", "NODE_ENV"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/node_modules/react-redux/es/connect/wrapMapToProps.js"], "sourcesContent": ["import verifyPlainObject from '../utils/verifyPlainObject';\nexport function wrapMapToPropsConstant( // * Note:\n//  It seems that the dispatch argument\n//  could be a dispatch function in some cases (ex: whenMapDispatchToPropsIsMissing)\n//  and a state object in some others (ex: whenMapStateToPropsIsMissing)\n// eslint-disable-next-line no-unused-vars\ngetConstant) {\n  return function initConstantSelector(dispatch) {\n    const constant = getConstant(dispatch);\n\n    function constantSelector() {\n      return constant;\n    }\n\n    constantSelector.dependsOnOwnProps = false;\n    return constantSelector;\n  };\n} // dependsOnOwnProps is used by createMapToPropsProxy to determine whether to pass props as args\n// to the mapToProps function being wrapped. It is also used by makePurePropsSelector to determine\n// whether mapToProps needs to be invoked when props have changed.\n//\n// A length of one signals that mapToProps does not depend on props from the parent component.\n// A length of zero is assumed to mean mapToProps is getting args via arguments or ...args and\n// therefore not reporting its length accurately..\n// TODO Can this get pulled out so that we can subscribe directly to the store if we don't need ownProps?\n\nexport function getDependsOnOwnProps(mapToProps) {\n  return mapToProps.dependsOnOwnProps ? Boolean(mapToProps.dependsOnOwnProps) : mapToProps.length !== 1;\n} // Used by whenMapStateToPropsIsFunction and whenMapDispatchToPropsIsFunction,\n// this function wraps mapToProps in a proxy function which does several things:\n//\n//  * Detects whether the mapToProps function being called depends on props, which\n//    is used by selectorFactory to decide if it should reinvoke on props changes.\n//\n//  * On first call, handles mapToProps if returns another function, and treats that\n//    new function as the true mapToProps for subsequent calls.\n//\n//  * On first call, verifies the first result is a plain object, in order to warn\n//    the developer that their mapToProps function is not returning a valid result.\n//\n\nexport function wrapMapToPropsFunc(mapToProps, methodName) {\n  return function initProxySelector(dispatch, {\n    displayName\n  }) {\n    const proxy = function mapToPropsProxy(stateOrDispatch, ownProps) {\n      return proxy.dependsOnOwnProps ? proxy.mapToProps(stateOrDispatch, ownProps) : proxy.mapToProps(stateOrDispatch, undefined);\n    }; // allow detectFactoryAndVerify to get ownProps\n\n\n    proxy.dependsOnOwnProps = true;\n\n    proxy.mapToProps = function detectFactoryAndVerify(stateOrDispatch, ownProps) {\n      proxy.mapToProps = mapToProps;\n      proxy.dependsOnOwnProps = getDependsOnOwnProps(mapToProps);\n      let props = proxy(stateOrDispatch, ownProps);\n\n      if (typeof props === 'function') {\n        proxy.mapToProps = props;\n        proxy.dependsOnOwnProps = getDependsOnOwnProps(props);\n        props = proxy(stateOrDispatch, ownProps);\n      }\n\n      if (process.env.NODE_ENV !== 'production') verifyPlainObject(props, displayName, methodName);\n      return props;\n    };\n\n    return proxy;\n  };\n}"], "mappings": "AAAA,OAAOA,iBAAiB,MAAM,4BAA4B;AAC1D,OAAO,SAASC,sBAAsBA;AAAE;AACxC;AACA;AACA;AACA;AACAC,WAAW,EAAE;EACX,OAAO,SAASC,oBAAoBA,CAACC,QAAQ,EAAE;IAC7C,MAAMC,QAAQ,GAAGH,WAAW,CAACE,QAAQ,CAAC;IAEtC,SAASE,gBAAgBA,CAAA,EAAG;MAC1B,OAAOD,QAAQ;IACjB;IAEAC,gBAAgB,CAACC,iBAAiB,GAAG,KAAK;IAC1C,OAAOD,gBAAgB;EACzB,CAAC;AACH,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASE,oBAAoBA,CAACC,UAAU,EAAE;EAC/C,OAAOA,UAAU,CAACF,iBAAiB,GAAGG,OAAO,CAACD,UAAU,CAACF,iBAAiB,CAAC,GAAGE,UAAU,CAACE,MAAM,KAAK,CAAC;AACvG,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,kBAAkBA,CAACH,UAAU,EAAEI,UAAU,EAAE;EACzD,OAAO,SAASC,iBAAiBA,CAACV,QAAQ,EAAAW,IAAA,EAEvC;IAAA,IAFyC;MAC1CC;IACF,CAAC,GAAAD,IAAA;IACC,MAAME,KAAK,GAAG,SAASC,eAAeA,CAACC,eAAe,EAAEC,QAAQ,EAAE;MAChE,OAAOH,KAAK,CAACV,iBAAiB,GAAGU,KAAK,CAACR,UAAU,CAACU,eAAe,EAAEC,QAAQ,CAAC,GAAGH,KAAK,CAACR,UAAU,CAACU,eAAe,EAAEE,SAAS,CAAC;IAC7H,CAAC,CAAC,CAAC;;IAGHJ,KAAK,CAACV,iBAAiB,GAAG,IAAI;IAE9BU,KAAK,CAACR,UAAU,GAAG,SAASa,sBAAsBA,CAACH,eAAe,EAAEC,QAAQ,EAAE;MAC5EH,KAAK,CAACR,UAAU,GAAGA,UAAU;MAC7BQ,KAAK,CAACV,iBAAiB,GAAGC,oBAAoB,CAACC,UAAU,CAAC;MAC1D,IAAIc,KAAK,GAAGN,KAAK,CAACE,eAAe,EAAEC,QAAQ,CAAC;MAE5C,IAAI,OAAOG,KAAK,KAAK,UAAU,EAAE;QAC/BN,KAAK,CAACR,UAAU,GAAGc,KAAK;QACxBN,KAAK,CAACV,iBAAiB,GAAGC,oBAAoB,CAACe,KAAK,CAAC;QACrDA,KAAK,GAAGN,KAAK,CAACE,eAAe,EAAEC,QAAQ,CAAC;MAC1C;MAEA,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE1B,iBAAiB,CAACuB,KAAK,EAAEP,WAAW,EAAEH,UAAU,CAAC;MAC5F,OAAOU,KAAK;IACd,CAAC;IAED,OAAON,KAAK;EACd,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}