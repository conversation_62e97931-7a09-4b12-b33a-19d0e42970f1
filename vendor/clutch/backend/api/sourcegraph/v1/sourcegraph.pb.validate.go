// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: sourcegraph/v1/sourcegraph.proto

package sourcegraphv1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CompareCommitsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CompareCommitsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CompareCommitsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CompareCommitsRequestMultiError, or nil if none found.
func (m *CompareCommitsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CompareCommitsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetRepository()) < 1 {
		err := CompareCommitsRequestValidationError{
			field:  "Repository",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetBase()) < 1 {
		err := CompareCommitsRequestValidationError{
			field:  "Base",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetHead()) < 1 {
		err := CompareCommitsRequestValidationError{
			field:  "Head",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CompareCommitsRequestMultiError(errors)
	}

	return nil
}

// CompareCommitsRequestMultiError is an error wrapping multiple validation
// errors returned by CompareCommitsRequest.ValidateAll() if the designated
// constraints aren't met.
type CompareCommitsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CompareCommitsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CompareCommitsRequestMultiError) AllErrors() []error { return m }

// CompareCommitsRequestValidationError is the validation error returned by
// CompareCommitsRequest.Validate if the designated constraints aren't met.
type CompareCommitsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CompareCommitsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CompareCommitsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CompareCommitsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CompareCommitsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CompareCommitsRequestValidationError) ErrorName() string {
	return "CompareCommitsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CompareCommitsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCompareCommitsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CompareCommitsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CompareCommitsRequestValidationError{}

// Validate checks the field values on CompareCommitsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CompareCommitsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CompareCommitsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CompareCommitsResponseMultiError, or nil if none found.
func (m *CompareCommitsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CompareCommitsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetCommits() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CompareCommitsResponseValidationError{
						field:  fmt.Sprintf("Commits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CompareCommitsResponseValidationError{
						field:  fmt.Sprintf("Commits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CompareCommitsResponseValidationError{
					field:  fmt.Sprintf("Commits[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CompareCommitsResponseMultiError(errors)
	}

	return nil
}

// CompareCommitsResponseMultiError is an error wrapping multiple validation
// errors returned by CompareCommitsResponse.ValidateAll() if the designated
// constraints aren't met.
type CompareCommitsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CompareCommitsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CompareCommitsResponseMultiError) AllErrors() []error { return m }

// CompareCommitsResponseValidationError is the validation error returned by
// CompareCommitsResponse.Validate if the designated constraints aren't met.
type CompareCommitsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CompareCommitsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CompareCommitsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CompareCommitsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CompareCommitsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CompareCommitsResponseValidationError) ErrorName() string {
	return "CompareCommitsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CompareCommitsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCompareCommitsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CompareCommitsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CompareCommitsResponseValidationError{}

// Validate checks the field values on Commit with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Commit) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Commit with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in CommitMultiError, or nil if none found.
func (m *Commit) ValidateAll() error {
	return m.validate(true)
}

func (m *Commit) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Oid

	// no validation rules for Email

	// no validation rules for Message

	// no validation rules for DisplayName

	if len(errors) > 0 {
		return CommitMultiError(errors)
	}

	return nil
}

// CommitMultiError is an error wrapping multiple validation errors returned by
// Commit.ValidateAll() if the designated constraints aren't met.
type CommitMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CommitMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CommitMultiError) AllErrors() []error { return m }

// CommitValidationError is the validation error returned by Commit.Validate if
// the designated constraints aren't met.
type CommitValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CommitValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CommitValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CommitValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CommitValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CommitValidationError) ErrorName() string { return "CommitValidationError" }

// Error satisfies the builtin error interface
func (e CommitValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCommit.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CommitValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CommitValidationError{}

// Validate checks the field values on GetQueryResultsCountRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetQueryResultsCountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetQueryResultsCountRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetQueryResultsCountRequestMultiError, or nil if none found.
func (m *GetQueryResultsCountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetQueryResultsCountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetQuery() != "" {

		if len(m.GetQuery()) < 1 {
			err := GetQueryResultsCountRequestValidationError{
				field:  "Query",
				reason: "value length must be at least 1 bytes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return GetQueryResultsCountRequestMultiError(errors)
	}

	return nil
}

// GetQueryResultsCountRequestMultiError is an error wrapping multiple
// validation errors returned by GetQueryResultsCountRequest.ValidateAll() if
// the designated constraints aren't met.
type GetQueryResultsCountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetQueryResultsCountRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetQueryResultsCountRequestMultiError) AllErrors() []error { return m }

// GetQueryResultsCountRequestValidationError is the validation error returned
// by GetQueryResultsCountRequest.Validate if the designated constraints
// aren't met.
type GetQueryResultsCountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetQueryResultsCountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetQueryResultsCountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetQueryResultsCountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetQueryResultsCountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetQueryResultsCountRequestValidationError) ErrorName() string {
	return "GetQueryResultsCountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetQueryResultsCountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetQueryResultsCountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetQueryResultsCountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetQueryResultsCountRequestValidationError{}

// Validate checks the field values on GetQueryResultsCountResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetQueryResultsCountResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetQueryResultsCountResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetQueryResultsCountResponseMultiError, or nil if none found.
func (m *GetQueryResultsCountResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetQueryResultsCountResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Count

	if len(errors) > 0 {
		return GetQueryResultsCountResponseMultiError(errors)
	}

	return nil
}

// GetQueryResultsCountResponseMultiError is an error wrapping multiple
// validation errors returned by GetQueryResultsCountResponse.ValidateAll() if
// the designated constraints aren't met.
type GetQueryResultsCountResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetQueryResultsCountResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetQueryResultsCountResponseMultiError) AllErrors() []error { return m }

// GetQueryResultsCountResponseValidationError is the validation error returned
// by GetQueryResultsCountResponse.Validate if the designated constraints
// aren't met.
type GetQueryResultsCountResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetQueryResultsCountResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetQueryResultsCountResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetQueryResultsCountResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetQueryResultsCountResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetQueryResultsCountResponseValidationError) ErrorName() string {
	return "GetQueryResultsCountResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetQueryResultsCountResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetQueryResultsCountResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetQueryResultsCountResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetQueryResultsCountResponseValidationError{}
