package db

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/chaisql/chai"
)

// Store defines the database interface
type Store interface {
	Exec(ctx context.Context, query string, args ...interface{}) error
	Query(ctx context.Context, query string, args ...interface{}) (Result, error)
	Insert(ctx context.Context, table string, data interface{}) error
	Update(ctx context.Context, table string, data interface{}, where string, args ...interface{}) error
	Delete(ctx context.Context, table string, where string, args ...interface{}) error
	Close() error
}

// Result defines the query result interface
type Result interface {
	Next() bool
	Scan(dest interface{}) error
	Close() error
}

// CachedResource represents a cached cloud resource
type CachedResource struct {
	ID         int64     `json:"id" db:"id"`
	Provider   string    `json:"provider" db:"provider"`
	Type       string    `json:"type" db:"type"`
	Name       string    `json:"name" db:"name"`
	ResourceID string    `json:"resource_id" db:"resource_id"`
	Region     string    `json:"region" db:"region"`
	Tags       string    `json:"tags" db:"tags"`
	LastSeen   time.Time `json:"last_seen" db:"last_seen"`
}

// WorkflowExecution represents a workflow execution record
type WorkflowExecution struct {
	ID         string     `json:"id" db:"id"`
	WorkflowID string     `json:"workflow_id" db:"workflow_id"`
	Status     string     `json:"status" db:"status"`
	Input      string     `json:"input" db:"input"`
	Output     string     `json:"output" db:"output"`
	StartTime  time.Time  `json:"start_time" db:"start_time"`
	EndTime    *time.Time `json:"end_time" db:"end_time"`
	Error      string     `json:"error" db:"error"`
}

// EnvoyConfig represents an Envoy configuration
type EnvoyConfig struct {
	ID          string    `json:"id" db:"id"`
	NodeID      string    `json:"node_id" db:"node_id"`
	ClusterName string    `json:"cluster_name" db:"cluster_name"`
	Config      string    `json:"config" db:"config"`
	Version     string    `json:"version" db:"version"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

// GenjiStore implements Store using ChaiSQL database
type GenjiStore struct {
	db *chai.DB
}

// NewGenjiStore creates a new ChaiSQL database store
func NewGenjiStore(dbPath string) (*GenjiStore, error) {
	db, err := chai.Open(dbPath)
	if err != nil {
		return nil, fmt.Errorf("failed to open ChaiSQL database: %w", err)
	}

	store := &GenjiStore{db: db}

	// Initialize database schema
	if err := store.initSchema(); err != nil {
		return nil, fmt.Errorf("failed to initialize schema: %w", err)
	}

	return store, nil
}

// initSchema creates the necessary tables
func (s *GenjiStore) initSchema() error {
	// Create resource_cache table
	err := s.db.Exec(`
		CREATE TABLE IF NOT EXISTS resource_cache (
			id INT PRIMARY KEY,
			provider TEXT NOT NULL,
			type TEXT NOT NULL,
			name TEXT NOT NULL,
			resource_id TEXT NOT NULL,
			region TEXT,
			tags TEXT,
			last_seen TEXT
		)
	`)
	if err != nil {
		return fmt.Errorf("failed to create resource_cache table: %w", err)
	}

	// Create workflow_executions table
	err = s.db.Exec(`
		CREATE TABLE IF NOT EXISTS workflow_executions (
			id TEXT PRIMARY KEY,
			workflow_id TEXT NOT NULL,
			status TEXT NOT NULL,
			input TEXT,
			output TEXT,
			start_time TEXT,
			end_time TEXT,
			error TEXT
		)
	`)
	if err != nil {
		return fmt.Errorf("failed to create workflow_executions table: %w", err)
	}

	// Create envoy_configs table
	err = s.db.Exec(`
		CREATE TABLE IF NOT EXISTS envoy_configs (
			id TEXT PRIMARY KEY,
			node_id TEXT NOT NULL,
			cluster_name TEXT NOT NULL,
			config TEXT NOT NULL,
			version TEXT NOT NULL,
			created_at TEXT,
			updated_at TEXT
		)
	`)
	if err != nil {
		return fmt.Errorf("failed to create envoy_configs table: %w", err)
	}

	// Create settings table
	err = s.db.Exec(`
		CREATE TABLE IF NOT EXISTS settings (
			setting_key TEXT PRIMARY KEY,
			setting_value TEXT NOT NULL,
			updated_at TEXT
		)
	`)
	if err != nil {
		return fmt.Errorf("failed to create settings table: %w", err)
	}

	// Create users table
	err = s.db.Exec(`
		CREATE TABLE IF NOT EXISTS users (
			id TEXT PRIMARY KEY,
			username TEXT NOT NULL,
			email TEXT NOT NULL,
			name TEXT NOT NULL,
			password_hash TEXT NOT NULL,
			roles TEXT,
			created_at TEXT,
			updated_at TEXT
		)
	`)
	if err != nil {
		return fmt.Errorf("failed to create users table: %w", err)
	}

	// Create workflows table
	err = s.db.Exec(`
		CREATE TABLE IF NOT EXISTS workflows (
			id TEXT PRIMARY KEY,
			name TEXT NOT NULL,
			description TEXT,
			definition TEXT NOT NULL,
			created_at TEXT,
			updated_at TEXT
		)
	`)
	if err != nil {
		return fmt.Errorf("failed to create workflows table: %w", err)
	}

	// Create indexes for better performance
	s.db.Exec("CREATE INDEX IF NOT EXISTS idx_resource_provider ON resource_cache(provider)")
	s.db.Exec("CREATE INDEX IF NOT EXISTS idx_resource_type ON resource_cache(type)")
	s.db.Exec("CREATE INDEX IF NOT EXISTS idx_workflow_status ON workflow_executions(status)")
	s.db.Exec("CREATE INDEX IF NOT EXISTS idx_envoy_node ON envoy_configs(node_id)")
	s.db.Exec("CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)")
	s.db.Exec("CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)")

	return nil
}

// Exec executes a query without returning results
func (s *GenjiStore) Exec(ctx context.Context, query string, args ...interface{}) error {
	err := s.db.Exec(query, args...)
	if err != nil {
		return fmt.Errorf("exec failed: %w", err)
	}
	return nil
}

// Query executes a query and returns results
func (s *GenjiStore) Query(ctx context.Context, query string, args ...interface{}) (Result, error) {
	result, err := s.db.Query(query, args...)
	if err != nil {
		return nil, fmt.Errorf("query failed: %w", err)
	}
	return &GenjiResult{result: result}, nil
}

// Insert inserts data into a table
func (s *GenjiStore) Insert(ctx context.Context, table string, data interface{}) error {
	// ChaiSQL supports direct struct insertion
	query := fmt.Sprintf("INSERT INTO %s VALUES ?", table)
	err := s.db.Exec(query, data)
	if err != nil {
		return fmt.Errorf("insert failed: %w", err)
	}
	return nil
}

// Update updates data in a table
func (s *GenjiStore) Update(ctx context.Context, table string, data interface{}, where string, args ...interface{}) error {
	// Convert data to map for field-by-field updates
	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("failed to marshal data: %w", err)
	}

	var fields map[string]interface{}
	if err := json.Unmarshal(jsonData, &fields); err != nil {
		return fmt.Errorf("failed to unmarshal data: %w", err)
	}

	// Build SET clause dynamically
	var setParts []string
	var setArgs []interface{}
	for key, value := range fields {
		setParts = append(setParts, fmt.Sprintf("%s = ?", key))
		setArgs = append(setArgs, value)
	}

	if len(setParts) == 0 {
		return fmt.Errorf("no fields to update")
	}

	setClause := strings.Join(setParts, ", ")
	query := fmt.Sprintf("UPDATE %s SET %s WHERE %s", table, setClause, where)

	allArgs := append(setArgs, args...)
	err = s.db.Exec(query, allArgs...)
	if err != nil {
		return fmt.Errorf("update failed: %w", err)
	}
	return nil
}

// Delete deletes data from a table
func (s *GenjiStore) Delete(ctx context.Context, table string, where string, args ...interface{}) error {
	query := fmt.Sprintf("DELETE FROM %s WHERE %s", table, where)
	err := s.db.Exec(query, args...)
	if err != nil {
		return fmt.Errorf("delete failed: %w", err)
	}
	return nil
}

// Close closes the database connection
func (s *GenjiStore) Close() error {
	return s.db.Close()
}

// GenjiResult implements Result for ChaiSQL query results
type GenjiResult struct {
	result *chai.Result
	rows   []*chai.Row
	index  int
	loaded bool
}

// Next advances to the next result
func (r *GenjiResult) Next() bool {
	// If rows haven't been loaded yet, load them
	if !r.loaded {
		r.result.Iterate(func(row *chai.Row) error {
			r.rows = append(r.rows, row)
			return nil
		})
		r.index = -1
		r.loaded = true
	}

	r.index++
	return r.index < len(r.rows)
}

// Scan scans the current result into dest
func (r *GenjiResult) Scan(dest interface{}) error {
	if !r.loaded || r.index < 0 || r.index >= len(r.rows) {
		return fmt.Errorf("no row available")
	}

	// Try different scan methods based on dest type
	switch v := dest.(type) {
	case *map[string]interface{}:
		// Convert to map[string]any for ChaiSQL
		mapAny := make(map[string]any)
		err := r.rows[r.index].MapScan(mapAny)
		if err != nil {
			return err
		}
		// Convert back to map[string]interface{}
		for k, val := range mapAny {
			(*v)[k] = val
		}
		return nil
	default:
		return r.rows[r.index].StructScan(dest)
	}
}

// Close closes the result
func (r *GenjiResult) Close() error {
	return r.result.Close()
}
