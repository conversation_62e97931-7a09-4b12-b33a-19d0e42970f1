{
  "name": "@clutch-sh/{{ .PackageName }}",
  "version": "1.0.0",
  "private": true,
  "description": " {{ .Description }}",
  "license": "Apache-2.0",
  "author": "{{ .DeveloperEmail }}",
  "main": "dist/index.js",
  "scripts": {
    "clean": "rm -rf ./dist && rm -f tsconfig.tsbuildinfo",
    "compile": "tsc -b",
    "compile:dev": "esbuild --target=es2019 --outdir=dist --sourcemap src/*.tsx",
    "compile:watch": "yarn compile:dev --watch=forever",
    "lint": "yarn run package:lint",
    "lint:fix": "yarn run lint --fix",
    "test": "yarn run package:test",
    "test:coverage": "yarn run test --collect-coverage",
    "test:watch": "yarn run test --watch"
  },
  "dependencies": {
    "@clutch-sh/core": "^4.0.0-beta",
    {{- if .IsWizardTemplate}}
    "@clutch-sh/wizard": "^4.0.0-beta",
    {{- end}}
    "esbuild": "^0.18.0",
    "eslint": "^8.3.0",
    "jest": "^27.0.0",
    "react": "^17.0.2",
    "react-dom": "^17.0.2",
    "typescript": "^4.2.3"
  },
  "resolutions": {
    "@types/node": "18.19.23",
    "@types/react": "17.0.50",
    "@types/react-dom": "17.0.17",
    "@mui/material": "5.8.5",
    "@mui/lab": "5.0.0-alpha.87",
    "@mui/styles": "5.8.4",
    "@mui/system": "5.8.5",
    "esbuild": "0.18.13",
    "eslint": "8.16.0",
    "jest": "27.5.1",
    "react": "17.0.2",
    "react-dom": "17.0.2",
    "react-hook-form": "7.25.3",
    "react-router": "6.0.0",
    "react-router-dom": "6.0.0",
    "typescript": "4.2.3"
  },
  "devDependencies": {
    "@clutch-sh/tools": "^4.0.0-beta"
  },
  "engines": {
    "node": ">=18",
    "yarn": "^4.0.0"
  }
}
