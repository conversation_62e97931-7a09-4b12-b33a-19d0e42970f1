{"ast": null, "code": "import React,{useEffect,useState}from'react';import{useAuth}from'../auth/AuthContext';import{ShieldCheckIcon,UserGroupIcon,KeyIcon,PlusIcon,PencilIcon,TrashIcon,CheckCircleIcon,XCircleIcon,MagnifyingGlassIcon,ExclamationTriangleIcon}from'@heroicons/react/24/outline';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const RBACAdmin=()=>{const{user}=useAuth();const[activeTab,setActiveTab]=useState('roles');const[roles,setRoles]=useState([]);const[permissions,setPermissions]=useState([]);const[policies,setPolicies]=useState([]);const[loading,setLoading]=useState(true);const[searchTerm,setSearchTerm]=useState('');const[showCreateModal,setShowCreateModal]=useState(false);const[showEditModal,setShowEditModal]=useState(false);const[showDeleteModal,setShowDeleteModal]=useState(false);const[selectedItem,setSelectedItem]=useState(null);useEffect(()=>{fetchData();},[activeTab]);const fetchData=async()=>{setLoading(true);try{switch(activeTab){case'roles':await fetchRoles();break;case'permissions':await fetchPermissions();break;case'policies':await fetchPolicies();break;}}catch(error){console.error(\"Failed to fetch \".concat(activeTab,\":\"),error);}finally{setLoading(false);}};const fetchRoles=async()=>{try{const response=await fetch('/v1/rbac/roles',{credentials:'include'});if(response.ok){const data=await response.json();setRoles(data.roles||[]);}}catch(error){console.error('Failed to fetch roles:',error);}};const fetchPermissions=async()=>{try{const response=await fetch('/v1/rbac/permissions',{credentials:'include'});if(response.ok){const data=await response.json();setPermissions(data.permissions||[]);}}catch(error){console.error('Failed to fetch permissions:',error);}};const fetchPolicies=async()=>{try{const response=await fetch('/v1/rbac/policies',{credentials:'include'});if(response.ok){const data=await response.json();setPolicies(data.policies||[]);}}catch(error){console.error('Failed to fetch policies:',error);}};const handleCreate=async data=>{try{const response=await fetch(\"/v1/rbac/\".concat(activeTab),{method:'POST',headers:{'Content-Type':'application/json'},credentials:'include',body:JSON.stringify(data)});if(response.ok){setShowCreateModal(false);fetchData();}}catch(error){console.error(\"Failed to create \".concat(activeTab.slice(0,-1),\":\"),error);}};const handleUpdate=async(id,data)=>{try{const response=await fetch(\"/v1/rbac/\".concat(activeTab,\"/\").concat(id),{method:'PUT',headers:{'Content-Type':'application/json'},credentials:'include',body:JSON.stringify(data)});if(response.ok){setShowEditModal(false);setSelectedItem(null);fetchData();}}catch(error){console.error(\"Failed to update \".concat(activeTab.slice(0,-1),\":\"),error);}};const handleDelete=async()=>{if(!selectedItem)return;try{const response=await fetch(\"/v1/rbac/\".concat(activeTab,\"/\").concat(selectedItem.id),{method:'DELETE',credentials:'include'});if(response.ok){setShowDeleteModal(false);setSelectedItem(null);fetchData();}}catch(error){console.error(\"Failed to delete \".concat(activeTab.slice(0,-1),\":\"),error);}};const getFilteredData=()=>{let data=[];switch(activeTab){case'roles':data=roles;break;case'permissions':data=permissions;break;case'policies':data=policies;break;}return data.filter(item=>item.name.toLowerCase().includes(searchTerm.toLowerCase())||item.description.toLowerCase().includes(searchTerm.toLowerCase()));};const tabs=[{id:'roles',name:'Roles',icon:UserGroupIcon},{id:'permissions',name:'Permissions',icon:KeyIcon},{id:'policies',name:'Policies',icon:ShieldCheckIcon}];if(loading){return/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center h-64\",children:/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500\"})});}return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white shadow rounded-lg p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-900\",children:\"RBAC Management\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Manage roles, permissions, and access policies\"})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setShowCreateModal(true),className:\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700\",children:[/*#__PURE__*/_jsx(PlusIcon,{className:\"h-4 w-4 mr-2\"}),\"Create \",activeTab.slice(0,-1)]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white shadow rounded-lg\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"border-b border-gray-200\",children:/*#__PURE__*/_jsx(\"nav\",{className:\"-mb-px flex space-x-8 px-6\",children:tabs.map(tab=>/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setActiveTab(tab.id),className:\"py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 \".concat(activeTab===tab.id?'border-blue-500 text-blue-600':'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),children:[/*#__PURE__*/_jsx(tab.icon,{className:\"h-5 w-5\"}),/*#__PURE__*/_jsx(\"span\",{children:tab.name})]},tab.id))})}),/*#__PURE__*/_jsx(\"div\",{className:\"p-6 border-b border-gray-200\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(MagnifyingGlassIcon,{className:\"h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:\"Search \".concat(activeTab,\"...\"),value:searchTerm,onChange:e=>setSearchTerm(e.target.value),className:\"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-6\",children:[activeTab==='roles'&&/*#__PURE__*/_jsx(RolesTable,{roles:getFilteredData(),onEdit:role=>{setSelectedItem(role);setShowEditModal(true);},onDelete:role=>{setSelectedItem(role);setShowDeleteModal(true);}}),activeTab==='permissions'&&/*#__PURE__*/_jsx(PermissionsTable,{permissions:getFilteredData(),onEdit:permission=>{setSelectedItem(permission);setShowEditModal(true);},onDelete:permission=>{setSelectedItem(permission);setShowDeleteModal(true);}}),activeTab==='policies'&&/*#__PURE__*/_jsx(PoliciesTable,{policies:getFilteredData(),onEdit:policy=>{setSelectedItem(policy);setShowEditModal(true);},onDelete:policy=>{setSelectedItem(policy);setShowDeleteModal(true);}})]})]}),showDeleteModal&&selectedItem&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",children:/*#__PURE__*/_jsx(\"div\",{className:\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"mt-3 text-center\",children:[/*#__PURE__*/_jsx(ExclamationTriangleIcon,{className:\"mx-auto h-12 w-12 text-red-600\"}),/*#__PURE__*/_jsxs(\"h3\",{className:\"text-lg font-medium text-gray-900 mt-2\",children:[\"Delete \",activeTab.slice(0,-1)]}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-gray-500 mt-2\",children:[\"Are you sure you want to delete \\\"\",selectedItem.name,\"\\\"? This action cannot be undone.\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-center space-x-3 mt-4\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setShowDeleteModal(false),className:\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\",children:\"Cancel\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handleDelete,className:\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700\",children:\"Delete\"})]})]})})})]});};// Component for displaying roles table\nconst RolesTable=_ref=>{let{roles,onEdit,onDelete}=_ref;return/*#__PURE__*/_jsx(\"div\",{className:\"overflow-x-auto\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"min-w-full divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(\"thead\",{className:\"bg-gray-50\",children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Role\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Permissions\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Users\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Updated\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{className:\"bg-white divide-y divide-gray-200\",children:roles.map(role=>/*#__PURE__*/_jsxs(\"tr\",{className:\"hover:bg-gray-50\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-sm font-medium text-gray-900\",children:role.name}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-500\",children:role.description})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsxs(\"span\",{className:\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",children:[role.permissions.length,\" permissions\"]})}),/*#__PURE__*/_jsxs(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",children:[role.user_count||0,\" users\"]}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",children:new Date(role.updated_at).toLocaleDateString()}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-end space-x-2\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>onEdit(role),className:\"text-blue-600 hover:text-blue-900\",children:/*#__PURE__*/_jsx(PencilIcon,{className:\"h-4 w-4\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>onDelete(role),className:\"text-red-600 hover:text-red-900\",children:/*#__PURE__*/_jsx(TrashIcon,{className:\"h-4 w-4\"})})]})})]},role.id))})]})});};// Component for displaying permissions table\nconst PermissionsTable=_ref2=>{let{permissions,onEdit,onDelete}=_ref2;return/*#__PURE__*/_jsx(\"div\",{className:\"overflow-x-auto\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"min-w-full divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(\"thead\",{className:\"bg-gray-50\",children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Permission\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Resource\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Action\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Category\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{className:\"bg-white divide-y divide-gray-200\",children:permissions.map(permission=>/*#__PURE__*/_jsxs(\"tr\",{className:\"hover:bg-gray-50\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-sm font-medium text-gray-900\",children:permission.name}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-500\",children:permission.description})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsx(\"span\",{className:\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\",children:permission.resource})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsx(\"span\",{className:\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800\",children:permission.action})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",children:permission.category}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-end space-x-2\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>onEdit(permission),className:\"text-blue-600 hover:text-blue-900\",children:/*#__PURE__*/_jsx(PencilIcon,{className:\"h-4 w-4\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>onDelete(permission),className:\"text-red-600 hover:text-red-900\",children:/*#__PURE__*/_jsx(TrashIcon,{className:\"h-4 w-4\"})})]})})]},permission.id))})]})});};// Component for displaying policies table\nconst PoliciesTable=_ref3=>{let{policies,onEdit,onDelete}=_ref3;return/*#__PURE__*/_jsx(\"div\",{className:\"overflow-x-auto\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"min-w-full divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(\"thead\",{className:\"bg-gray-50\",children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Policy\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Effect\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Status\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Resources\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{className:\"bg-white divide-y divide-gray-200\",children:policies.map(policy=>/*#__PURE__*/_jsxs(\"tr\",{className:\"hover:bg-gray-50\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-sm font-medium text-gray-900\",children:policy.name}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-500\",children:policy.description})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsx(\"span\",{className:\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(policy.effect==='allow'?'bg-green-100 text-green-800':'bg-red-100 text-red-800'),children:policy.effect})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsxs(\"span\",{className:\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(policy.enabled?'bg-green-100 text-green-800':'bg-gray-100 text-gray-800'),children:[policy.enabled?/*#__PURE__*/_jsx(CheckCircleIcon,{className:\"h-3 w-3 mr-1\"}):/*#__PURE__*/_jsx(XCircleIcon,{className:\"h-3 w-3 mr-1\"}),policy.enabled?'Enabled':'Disabled']})}),/*#__PURE__*/_jsxs(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",children:[policy.resources.length,\" resources\"]}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-end space-x-2\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>onEdit(policy),className:\"text-blue-600 hover:text-blue-900\",children:/*#__PURE__*/_jsx(PencilIcon,{className:\"h-4 w-4\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>onDelete(policy),className:\"text-red-600 hover:text-red-900\",children:/*#__PURE__*/_jsx(TrashIcon,{className:\"h-4 w-4\"})})]})})]},policy.id))})]})});};export default RBACAdmin;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useAuth", "ShieldCheckIcon", "UserGroupIcon", "KeyIcon", "PlusIcon", "PencilIcon", "TrashIcon", "CheckCircleIcon", "XCircleIcon", "MagnifyingGlassIcon", "ExclamationTriangleIcon", "jsx", "_jsx", "jsxs", "_jsxs", "RBACAdmin", "user", "activeTab", "setActiveTab", "roles", "setRoles", "permissions", "setPermissions", "policies", "setPolicies", "loading", "setLoading", "searchTerm", "setSearchTerm", "showCreateModal", "setShowCreateModal", "showEditModal", "setShowEditModal", "showDeleteModal", "setShowDeleteModal", "selectedItem", "setSelectedItem", "fetchData", "fetchRoles", "fetchPermissions", "fetchPolicies", "error", "console", "concat", "response", "fetch", "credentials", "ok", "data", "json", "handleCreate", "method", "headers", "body", "JSON", "stringify", "slice", "handleUpdate", "id", "handleDelete", "getFilteredData", "filter", "item", "name", "toLowerCase", "includes", "description", "tabs", "icon", "className", "children", "onClick", "map", "tab", "type", "placeholder", "value", "onChange", "e", "target", "RolesTable", "onEdit", "role", "onDelete", "PermissionsTable", "permission", "PoliciesTable", "policy", "_ref", "length", "user_count", "Date", "updated_at", "toLocaleDateString", "_ref2", "resource", "action", "category", "_ref3", "effect", "enabled", "resources"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/RBACAdmin.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useAuth } from '../auth/AuthContext';\nimport {\n  ShieldCheckIcon,\n  UserGroupIcon,\n  KeyIcon,\n  PlusIcon,\n  PencilIcon,\n  TrashIcon,\n  EyeIcon,\n  CheckCircleIcon,\n  XCircleIcon,\n  MagnifyingGlassIcon,\n  ExclamationTriangleIcon,\n} from '@heroicons/react/24/outline';\n\ninterface Role {\n  id: string;\n  name: string;\n  description: string;\n  permissions: string[];\n  created_at: string;\n  updated_at: string;\n  user_count?: number;\n}\n\ninterface Permission {\n  id: string;\n  name: string;\n  description: string;\n  resource: string;\n  action: string;\n  category: string;\n}\n\ninterface PolicyRule {\n  id: string;\n  name: string;\n  description: string;\n  effect: 'allow' | 'deny';\n  subjects: string[];\n  resources: string[];\n  actions: string[];\n  conditions?: any;\n  enabled: boolean;\n}\n\nconst RBACAdmin: React.FC = () => {\n  const { user } = useAuth();\n  const [activeTab, setActiveTab] = useState<'roles' | 'permissions' | 'policies'>('roles');\n  const [roles, setRoles] = useState<Role[]>([]);\n  const [permissions, setPermissions] = useState<Permission[]>([]);\n  const [policies, setPolicies] = useState<PolicyRule[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showCreateModal, setShowCreateModal] = useState(false);\n  const [showEditModal, setShowEditModal] = useState(false);\n  const [showDeleteModal, setShowDeleteModal] = useState(false);\n  const [selectedItem, setSelectedItem] = useState<any>(null);\n\n  useEffect(() => {\n    fetchData();\n  }, [activeTab]);\n\n  const fetchData = async () => {\n    setLoading(true);\n    try {\n      switch (activeTab) {\n        case 'roles':\n          await fetchRoles();\n          break;\n        case 'permissions':\n          await fetchPermissions();\n          break;\n        case 'policies':\n          await fetchPolicies();\n          break;\n      }\n    } catch (error) {\n      console.error(`Failed to fetch ${activeTab}:`, error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchRoles = async () => {\n    try {\n      const response = await fetch('/v1/rbac/roles', {\n        credentials: 'include',\n      });\n      if (response.ok) {\n        const data = await response.json();\n        setRoles(data.roles || []);\n      }\n    } catch (error) {\n      console.error('Failed to fetch roles:', error);\n    }\n  };\n\n  const fetchPermissions = async () => {\n    try {\n      const response = await fetch('/v1/rbac/permissions', {\n        credentials: 'include',\n      });\n      if (response.ok) {\n        const data = await response.json();\n        setPermissions(data.permissions || []);\n      }\n    } catch (error) {\n      console.error('Failed to fetch permissions:', error);\n    }\n  };\n\n  const fetchPolicies = async () => {\n    try {\n      const response = await fetch('/v1/rbac/policies', {\n        credentials: 'include',\n      });\n      if (response.ok) {\n        const data = await response.json();\n        setPolicies(data.policies || []);\n      }\n    } catch (error) {\n      console.error('Failed to fetch policies:', error);\n    }\n  };\n\n  const handleCreate = async (data: any) => {\n    try {\n      const response = await fetch(`/v1/rbac/${activeTab}`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include',\n        body: JSON.stringify(data),\n      });\n\n      if (response.ok) {\n        setShowCreateModal(false);\n        fetchData();\n      }\n    } catch (error) {\n      console.error(`Failed to create ${activeTab.slice(0, -1)}:`, error);\n    }\n  };\n\n  const handleUpdate = async (id: string, data: any) => {\n    try {\n      const response = await fetch(`/v1/rbac/${activeTab}/${id}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include',\n        body: JSON.stringify(data),\n      });\n\n      if (response.ok) {\n        setShowEditModal(false);\n        setSelectedItem(null);\n        fetchData();\n      }\n    } catch (error) {\n      console.error(`Failed to update ${activeTab.slice(0, -1)}:`, error);\n    }\n  };\n\n  const handleDelete = async () => {\n    if (!selectedItem) return;\n\n    try {\n      const response = await fetch(`/v1/rbac/${activeTab}/${selectedItem.id}`, {\n        method: 'DELETE',\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        setShowDeleteModal(false);\n        setSelectedItem(null);\n        fetchData();\n      }\n    } catch (error) {\n      console.error(`Failed to delete ${activeTab.slice(0, -1)}:`, error);\n    }\n  };\n\n  const getFilteredData = () => {\n    let data: any[] = [];\n    switch (activeTab) {\n      case 'roles':\n        data = roles;\n        break;\n      case 'permissions':\n        data = permissions;\n        break;\n      case 'policies':\n        data = policies;\n        break;\n    }\n\n    return data.filter(item =>\n      item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      item.description.toLowerCase().includes(searchTerm.toLowerCase())\n    );\n  };\n\n  const tabs = [\n    { id: 'roles', name: 'Roles', icon: UserGroupIcon },\n    { id: 'permissions', name: 'Permissions', icon: KeyIcon },\n    { id: 'policies', name: 'Policies', icon: ShieldCheckIcon },\n  ];\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">RBAC Management</h1>\n            <p className=\"text-gray-600\">Manage roles, permissions, and access policies</p>\n          </div>\n          <button\n            onClick={() => setShowCreateModal(true)}\n            className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700\"\n          >\n            <PlusIcon className=\"h-4 w-4 mr-2\" />\n            Create {activeTab.slice(0, -1)}\n          </button>\n        </div>\n      </div>\n\n      {/* Tabs */}\n      <div className=\"bg-white shadow rounded-lg\">\n        <div className=\"border-b border-gray-200\">\n          <nav className=\"-mb-px flex space-x-8 px-6\">\n            {tabs.map((tab) => (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id as any)}\n                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${\n                  activeTab === tab.id\n                    ? 'border-blue-500 text-blue-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                <tab.icon className=\"h-5 w-5\" />\n                <span>{tab.name}</span>\n              </button>\n            ))}\n          </nav>\n        </div>\n\n        {/* Search */}\n        <div className=\"p-6 border-b border-gray-200\">\n          <div className=\"relative\">\n            <MagnifyingGlassIcon className=\"h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\" />\n            <input\n              type=\"text\"\n              placeholder={`Search ${activeTab}...`}\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n            />\n          </div>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-6\">\n          {activeTab === 'roles' && (\n            <RolesTable\n              roles={getFilteredData()}\n              onEdit={(role) => {\n                setSelectedItem(role);\n                setShowEditModal(true);\n              }}\n              onDelete={(role) => {\n                setSelectedItem(role);\n                setShowDeleteModal(true);\n              }}\n            />\n          )}\n\n          {activeTab === 'permissions' && (\n            <PermissionsTable\n              permissions={getFilteredData()}\n              onEdit={(permission) => {\n                setSelectedItem(permission);\n                setShowEditModal(true);\n              }}\n              onDelete={(permission) => {\n                setSelectedItem(permission);\n                setShowDeleteModal(true);\n              }}\n            />\n          )}\n\n          {activeTab === 'policies' && (\n            <PoliciesTable\n              policies={getFilteredData()}\n              onEdit={(policy) => {\n                setSelectedItem(policy);\n                setShowEditModal(true);\n              }}\n              onDelete={(policy) => {\n                setSelectedItem(policy);\n                setShowDeleteModal(true);\n              }}\n            />\n          )}\n        </div>\n      </div>\n\n      {/* Delete Confirmation Modal */}\n      {showDeleteModal && selectedItem && (\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n          <div className=\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\">\n            <div className=\"mt-3 text-center\">\n              <ExclamationTriangleIcon className=\"mx-auto h-12 w-12 text-red-600\" />\n              <h3 className=\"text-lg font-medium text-gray-900 mt-2\">\n                Delete {activeTab.slice(0, -1)}\n              </h3>\n              <p className=\"text-sm text-gray-500 mt-2\">\n                Are you sure you want to delete \"{selectedItem.name}\"? This action cannot be undone.\n              </p>\n              <div className=\"flex justify-center space-x-3 mt-4\">\n                <button\n                  onClick={() => setShowDeleteModal(false)}\n                  className=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\"\n                >\n                  Cancel\n                </button>\n                <button\n                  onClick={handleDelete}\n                  className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700\"\n                >\n                  Delete\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\n// Component for displaying roles table\nconst RolesTable: React.FC<{\n  roles: Role[];\n  onEdit: (role: Role) => void;\n  onDelete: (role: Role) => void;\n}> = ({ roles, onEdit, onDelete }) => (\n  <div className=\"overflow-x-auto\">\n    <table className=\"min-w-full divide-y divide-gray-200\">\n      <thead className=\"bg-gray-50\">\n        <tr>\n          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n            Role\n          </th>\n          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n            Permissions\n          </th>\n          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n            Users\n          </th>\n          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n            Updated\n          </th>\n          <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n            Actions\n          </th>\n        </tr>\n      </thead>\n      <tbody className=\"bg-white divide-y divide-gray-200\">\n        {roles.map((role) => (\n          <tr key={role.id} className=\"hover:bg-gray-50\">\n            <td className=\"px-6 py-4 whitespace-nowrap\">\n              <div>\n                <div className=\"text-sm font-medium text-gray-900\">{role.name}</div>\n                <div className=\"text-sm text-gray-500\">{role.description}</div>\n              </div>\n            </td>\n            <td className=\"px-6 py-4 whitespace-nowrap\">\n              <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n                {role.permissions.length} permissions\n              </span>\n            </td>\n            <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n              {role.user_count || 0} users\n            </td>\n            <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n              {new Date(role.updated_at).toLocaleDateString()}\n            </td>\n            <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n              <div className=\"flex items-center justify-end space-x-2\">\n                <button\n                  onClick={() => onEdit(role)}\n                  className=\"text-blue-600 hover:text-blue-900\"\n                >\n                  <PencilIcon className=\"h-4 w-4\" />\n                </button>\n                <button\n                  onClick={() => onDelete(role)}\n                  className=\"text-red-600 hover:text-red-900\"\n                >\n                  <TrashIcon className=\"h-4 w-4\" />\n                </button>\n              </div>\n            </td>\n          </tr>\n        ))}\n      </tbody>\n    </table>\n  </div>\n);\n\n// Component for displaying permissions table\nconst PermissionsTable: React.FC<{\n  permissions: Permission[];\n  onEdit: (permission: Permission) => void;\n  onDelete: (permission: Permission) => void;\n}> = ({ permissions, onEdit, onDelete }) => (\n  <div className=\"overflow-x-auto\">\n    <table className=\"min-w-full divide-y divide-gray-200\">\n      <thead className=\"bg-gray-50\">\n        <tr>\n          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n            Permission\n          </th>\n          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n            Resource\n          </th>\n          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n            Action\n          </th>\n          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n            Category\n          </th>\n          <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n            Actions\n          </th>\n        </tr>\n      </thead>\n      <tbody className=\"bg-white divide-y divide-gray-200\">\n        {permissions.map((permission) => (\n          <tr key={permission.id} className=\"hover:bg-gray-50\">\n            <td className=\"px-6 py-4 whitespace-nowrap\">\n              <div>\n                <div className=\"text-sm font-medium text-gray-900\">{permission.name}</div>\n                <div className=\"text-sm text-gray-500\">{permission.description}</div>\n              </div>\n            </td>\n            <td className=\"px-6 py-4 whitespace-nowrap\">\n              <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n                {permission.resource}\n              </span>\n            </td>\n            <td className=\"px-6 py-4 whitespace-nowrap\">\n              <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800\">\n                {permission.action}\n              </span>\n            </td>\n            <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n              {permission.category}\n            </td>\n            <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n              <div className=\"flex items-center justify-end space-x-2\">\n                <button\n                  onClick={() => onEdit(permission)}\n                  className=\"text-blue-600 hover:text-blue-900\"\n                >\n                  <PencilIcon className=\"h-4 w-4\" />\n                </button>\n                <button\n                  onClick={() => onDelete(permission)}\n                  className=\"text-red-600 hover:text-red-900\"\n                >\n                  <TrashIcon className=\"h-4 w-4\" />\n                </button>\n              </div>\n            </td>\n          </tr>\n        ))}\n      </tbody>\n    </table>\n  </div>\n);\n\n// Component for displaying policies table\nconst PoliciesTable: React.FC<{\n  policies: PolicyRule[];\n  onEdit: (policy: PolicyRule) => void;\n  onDelete: (policy: PolicyRule) => void;\n}> = ({ policies, onEdit, onDelete }) => (\n  <div className=\"overflow-x-auto\">\n    <table className=\"min-w-full divide-y divide-gray-200\">\n      <thead className=\"bg-gray-50\">\n        <tr>\n          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n            Policy\n          </th>\n          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n            Effect\n          </th>\n          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n            Status\n          </th>\n          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n            Resources\n          </th>\n          <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n            Actions\n          </th>\n        </tr>\n      </thead>\n      <tbody className=\"bg-white divide-y divide-gray-200\">\n        {policies.map((policy) => (\n          <tr key={policy.id} className=\"hover:bg-gray-50\">\n            <td className=\"px-6 py-4 whitespace-nowrap\">\n              <div>\n                <div className=\"text-sm font-medium text-gray-900\">{policy.name}</div>\n                <div className=\"text-sm text-gray-500\">{policy.description}</div>\n              </div>\n            </td>\n            <td className=\"px-6 py-4 whitespace-nowrap\">\n              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                policy.effect === 'allow' \n                  ? 'bg-green-100 text-green-800' \n                  : 'bg-red-100 text-red-800'\n              }`}>\n                {policy.effect}\n              </span>\n            </td>\n            <td className=\"px-6 py-4 whitespace-nowrap\">\n              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                policy.enabled \n                  ? 'bg-green-100 text-green-800' \n                  : 'bg-gray-100 text-gray-800'\n              }`}>\n                {policy.enabled ? (\n                  <CheckCircleIcon className=\"h-3 w-3 mr-1\" />\n                ) : (\n                  <XCircleIcon className=\"h-3 w-3 mr-1\" />\n                )}\n                {policy.enabled ? 'Enabled' : 'Disabled'}\n              </span>\n            </td>\n            <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n              {policy.resources.length} resources\n            </td>\n            <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n              <div className=\"flex items-center justify-end space-x-2\">\n                <button\n                  onClick={() => onEdit(policy)}\n                  className=\"text-blue-600 hover:text-blue-900\"\n                >\n                  <PencilIcon className=\"h-4 w-4\" />\n                </button>\n                <button\n                  onClick={() => onDelete(policy)}\n                  className=\"text-red-600 hover:text-red-900\"\n                >\n                  <TrashIcon className=\"h-4 w-4\" />\n                </button>\n              </div>\n            </td>\n          </tr>\n        ))}\n      </tbody>\n    </table>\n  </div>\n);\n\nexport default RBACAdmin;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,OAAO,KAAQ,qBAAqB,CAC7C,OACEC,eAAe,CACfC,aAAa,CACbC,OAAO,CACPC,QAAQ,CACRC,UAAU,CACVC,SAAS,CAETC,eAAe,CACfC,WAAW,CACXC,mBAAmB,CACnBC,uBAAuB,KAClB,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAiCrC,KAAM,CAAAC,SAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAAEC,IAAK,CAAC,CAAGhB,OAAO,CAAC,CAAC,CAC1B,KAAM,CAACiB,SAAS,CAAEC,YAAY,CAAC,CAAGnB,QAAQ,CAAuC,OAAO,CAAC,CACzF,KAAM,CAACoB,KAAK,CAAEC,QAAQ,CAAC,CAAGrB,QAAQ,CAAS,EAAE,CAAC,CAC9C,KAAM,CAACsB,WAAW,CAAEC,cAAc,CAAC,CAAGvB,QAAQ,CAAe,EAAE,CAAC,CAChE,KAAM,CAACwB,QAAQ,CAAEC,WAAW,CAAC,CAAGzB,QAAQ,CAAe,EAAE,CAAC,CAC1D,KAAM,CAAC0B,OAAO,CAAEC,UAAU,CAAC,CAAG3B,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC4B,UAAU,CAAEC,aAAa,CAAC,CAAG7B,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAAC8B,eAAe,CAAEC,kBAAkB,CAAC,CAAG/B,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAACgC,aAAa,CAAEC,gBAAgB,CAAC,CAAGjC,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACkC,eAAe,CAAEC,kBAAkB,CAAC,CAAGnC,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAACoC,YAAY,CAAEC,eAAe,CAAC,CAAGrC,QAAQ,CAAM,IAAI,CAAC,CAE3DD,SAAS,CAAC,IAAM,CACduC,SAAS,CAAC,CAAC,CACb,CAAC,CAAE,CAACpB,SAAS,CAAC,CAAC,CAEf,KAAM,CAAAoB,SAAS,CAAG,KAAAA,CAAA,GAAY,CAC5BX,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,OAAQT,SAAS,EACf,IAAK,OAAO,CACV,KAAM,CAAAqB,UAAU,CAAC,CAAC,CAClB,MACF,IAAK,aAAa,CAChB,KAAM,CAAAC,gBAAgB,CAAC,CAAC,CACxB,MACF,IAAK,UAAU,CACb,KAAM,CAAAC,aAAa,CAAC,CAAC,CACrB,MACJ,CACF,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,oBAAAE,MAAA,CAAoB1B,SAAS,MAAKwB,KAAK,CAAC,CACvD,CAAC,OAAS,CACRf,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAY,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B,GAAI,CACF,KAAM,CAAAM,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,gBAAgB,CAAE,CAC7CC,WAAW,CAAE,SACf,CAAC,CAAC,CACF,GAAIF,QAAQ,CAACG,EAAE,CAAE,CACf,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAJ,QAAQ,CAACK,IAAI,CAAC,CAAC,CAClC7B,QAAQ,CAAC4B,IAAI,CAAC7B,KAAK,EAAI,EAAE,CAAC,CAC5B,CACF,CAAE,MAAOsB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAChD,CACF,CAAC,CAED,KAAM,CAAAF,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAI,CACF,KAAM,CAAAK,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,sBAAsB,CAAE,CACnDC,WAAW,CAAE,SACf,CAAC,CAAC,CACF,GAAIF,QAAQ,CAACG,EAAE,CAAE,CACf,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAJ,QAAQ,CAACK,IAAI,CAAC,CAAC,CAClC3B,cAAc,CAAC0B,IAAI,CAAC3B,WAAW,EAAI,EAAE,CAAC,CACxC,CACF,CAAE,MAAOoB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACtD,CACF,CAAC,CAED,KAAM,CAAAD,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACF,KAAM,CAAAI,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,mBAAmB,CAAE,CAChDC,WAAW,CAAE,SACf,CAAC,CAAC,CACF,GAAIF,QAAQ,CAACG,EAAE,CAAE,CACf,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAJ,QAAQ,CAACK,IAAI,CAAC,CAAC,CAClCzB,WAAW,CAACwB,IAAI,CAACzB,QAAQ,EAAI,EAAE,CAAC,CAClC,CACF,CAAE,MAAOkB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACnD,CACF,CAAC,CAED,KAAM,CAAAS,YAAY,CAAG,KAAO,CAAAF,IAAS,EAAK,CACxC,GAAI,CACF,KAAM,CAAAJ,QAAQ,CAAG,KAAM,CAAAC,KAAK,aAAAF,MAAA,CAAa1B,SAAS,EAAI,CACpDkC,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDN,WAAW,CAAE,SAAS,CACtBO,IAAI,CAAEC,IAAI,CAACC,SAAS,CAACP,IAAI,CAC3B,CAAC,CAAC,CAEF,GAAIJ,QAAQ,CAACG,EAAE,CAAE,CACfjB,kBAAkB,CAAC,KAAK,CAAC,CACzBO,SAAS,CAAC,CAAC,CACb,CACF,CAAE,MAAOI,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,qBAAAE,MAAA,CAAqB1B,SAAS,CAACuC,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,MAAKf,KAAK,CAAC,CACrE,CACF,CAAC,CAED,KAAM,CAAAgB,YAAY,CAAG,KAAAA,CAAOC,EAAU,CAAEV,IAAS,GAAK,CACpD,GAAI,CACF,KAAM,CAAAJ,QAAQ,CAAG,KAAM,CAAAC,KAAK,aAAAF,MAAA,CAAa1B,SAAS,MAAA0B,MAAA,CAAIe,EAAE,EAAI,CAC1DP,MAAM,CAAE,KAAK,CACbC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDN,WAAW,CAAE,SAAS,CACtBO,IAAI,CAAEC,IAAI,CAACC,SAAS,CAACP,IAAI,CAC3B,CAAC,CAAC,CAEF,GAAIJ,QAAQ,CAACG,EAAE,CAAE,CACff,gBAAgB,CAAC,KAAK,CAAC,CACvBI,eAAe,CAAC,IAAI,CAAC,CACrBC,SAAS,CAAC,CAAC,CACb,CACF,CAAE,MAAOI,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,qBAAAE,MAAA,CAAqB1B,SAAS,CAACuC,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,MAAKf,KAAK,CAAC,CACrE,CACF,CAAC,CAED,KAAM,CAAAkB,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CAACxB,YAAY,CAAE,OAEnB,GAAI,CACF,KAAM,CAAAS,QAAQ,CAAG,KAAM,CAAAC,KAAK,aAAAF,MAAA,CAAa1B,SAAS,MAAA0B,MAAA,CAAIR,YAAY,CAACuB,EAAE,EAAI,CACvEP,MAAM,CAAE,QAAQ,CAChBL,WAAW,CAAE,SACf,CAAC,CAAC,CAEF,GAAIF,QAAQ,CAACG,EAAE,CAAE,CACfb,kBAAkB,CAAC,KAAK,CAAC,CACzBE,eAAe,CAAC,IAAI,CAAC,CACrBC,SAAS,CAAC,CAAC,CACb,CACF,CAAE,MAAOI,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,qBAAAE,MAAA,CAAqB1B,SAAS,CAACuC,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,MAAKf,KAAK,CAAC,CACrE,CACF,CAAC,CAED,KAAM,CAAAmB,eAAe,CAAGA,CAAA,GAAM,CAC5B,GAAI,CAAAZ,IAAW,CAAG,EAAE,CACpB,OAAQ/B,SAAS,EACf,IAAK,OAAO,CACV+B,IAAI,CAAG7B,KAAK,CACZ,MACF,IAAK,aAAa,CAChB6B,IAAI,CAAG3B,WAAW,CAClB,MACF,IAAK,UAAU,CACb2B,IAAI,CAAGzB,QAAQ,CACf,MACJ,CAEA,MAAO,CAAAyB,IAAI,CAACa,MAAM,CAACC,IAAI,EACrBA,IAAI,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACtC,UAAU,CAACqC,WAAW,CAAC,CAAC,CAAC,EAC1DF,IAAI,CAACI,WAAW,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACtC,UAAU,CAACqC,WAAW,CAAC,CAAC,CAClE,CAAC,CACH,CAAC,CAED,KAAM,CAAAG,IAAI,CAAG,CACX,CAAET,EAAE,CAAE,OAAO,CAAEK,IAAI,CAAE,OAAO,CAAEK,IAAI,CAAElE,aAAc,CAAC,CACnD,CAAEwD,EAAE,CAAE,aAAa,CAAEK,IAAI,CAAE,aAAa,CAAEK,IAAI,CAAEjE,OAAQ,CAAC,CACzD,CAAEuD,EAAE,CAAE,UAAU,CAAEK,IAAI,CAAE,UAAU,CAAEK,IAAI,CAAEnE,eAAgB,CAAC,CAC5D,CAED,GAAIwB,OAAO,CAAE,CACX,mBACEb,IAAA,QAAKyD,SAAS,CAAC,uCAAuC,CAAAC,QAAA,cACpD1D,IAAA,QAAKyD,SAAS,CAAC,gEAAgE,CAAM,CAAC,CACnF,CAAC,CAEV,CAEA,mBACEvD,KAAA,QAAKuD,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExB1D,IAAA,QAAKyD,SAAS,CAAC,gCAAgC,CAAAC,QAAA,cAC7CxD,KAAA,QAAKuD,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDxD,KAAA,QAAAwD,QAAA,eACE1D,IAAA,OAAIyD,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,cACrE1D,IAAA,MAAGyD,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,gDAA8C,CAAG,CAAC,EAC5E,CAAC,cACNxD,KAAA,WACEyD,OAAO,CAAEA,CAAA,GAAMzC,kBAAkB,CAAC,IAAI,CAAE,CACxCuC,SAAS,CAAC,gJAAgJ,CAAAC,QAAA,eAE1J1D,IAAA,CAACR,QAAQ,EAACiE,SAAS,CAAC,cAAc,CAAE,CAAC,UAC9B,CAACpD,SAAS,CAACuC,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,EACxB,CAAC,EACN,CAAC,CACH,CAAC,cAGN1C,KAAA,QAAKuD,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzC1D,IAAA,QAAKyD,SAAS,CAAC,0BAA0B,CAAAC,QAAA,cACvC1D,IAAA,QAAKyD,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CACxCH,IAAI,CAACK,GAAG,CAAEC,GAAG,eACZ3D,KAAA,WAEEyD,OAAO,CAAEA,CAAA,GAAMrD,YAAY,CAACuD,GAAG,CAACf,EAAS,CAAE,CAC3CW,SAAS,yEAAA1B,MAAA,CACP1B,SAAS,GAAKwD,GAAG,CAACf,EAAE,CAChB,+BAA+B,CAC/B,4EAA4E,CAC/E,CAAAY,QAAA,eAEH1D,IAAA,CAAC6D,GAAG,CAACL,IAAI,EAACC,SAAS,CAAC,SAAS,CAAE,CAAC,cAChCzD,IAAA,SAAA0D,QAAA,CAAOG,GAAG,CAACV,IAAI,CAAO,CAAC,GATlBU,GAAG,CAACf,EAUH,CACT,CAAC,CACC,CAAC,CACH,CAAC,cAGN9C,IAAA,QAAKyD,SAAS,CAAC,8BAA8B,CAAAC,QAAA,cAC3CxD,KAAA,QAAKuD,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvB1D,IAAA,CAACH,mBAAmB,EAAC4D,SAAS,CAAC,0EAA0E,CAAE,CAAC,cAC5GzD,IAAA,UACE8D,IAAI,CAAC,MAAM,CACXC,WAAW,WAAAhC,MAAA,CAAY1B,SAAS,OAAM,CACtC2D,KAAK,CAAEjD,UAAW,CAClBkD,QAAQ,CAAGC,CAAC,EAAKlD,aAAa,CAACkD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC/CP,SAAS,CAAC,oGAAoG,CAC/G,CAAC,EACC,CAAC,CACH,CAAC,cAGNvD,KAAA,QAAKuD,SAAS,CAAC,KAAK,CAAAC,QAAA,EACjBrD,SAAS,GAAK,OAAO,eACpBL,IAAA,CAACoE,UAAU,EACT7D,KAAK,CAAEyC,eAAe,CAAC,CAAE,CACzBqB,MAAM,CAAGC,IAAI,EAAK,CAChB9C,eAAe,CAAC8C,IAAI,CAAC,CACrBlD,gBAAgB,CAAC,IAAI,CAAC,CACxB,CAAE,CACFmD,QAAQ,CAAGD,IAAI,EAAK,CAClB9C,eAAe,CAAC8C,IAAI,CAAC,CACrBhD,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAE,CACH,CACF,CAEAjB,SAAS,GAAK,aAAa,eAC1BL,IAAA,CAACwE,gBAAgB,EACf/D,WAAW,CAAEuC,eAAe,CAAC,CAAE,CAC/BqB,MAAM,CAAGI,UAAU,EAAK,CACtBjD,eAAe,CAACiD,UAAU,CAAC,CAC3BrD,gBAAgB,CAAC,IAAI,CAAC,CACxB,CAAE,CACFmD,QAAQ,CAAGE,UAAU,EAAK,CACxBjD,eAAe,CAACiD,UAAU,CAAC,CAC3BnD,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAE,CACH,CACF,CAEAjB,SAAS,GAAK,UAAU,eACvBL,IAAA,CAAC0E,aAAa,EACZ/D,QAAQ,CAAEqC,eAAe,CAAC,CAAE,CAC5BqB,MAAM,CAAGM,MAAM,EAAK,CAClBnD,eAAe,CAACmD,MAAM,CAAC,CACvBvD,gBAAgB,CAAC,IAAI,CAAC,CACxB,CAAE,CACFmD,QAAQ,CAAGI,MAAM,EAAK,CACpBnD,eAAe,CAACmD,MAAM,CAAC,CACvBrD,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAE,CACH,CACF,EACE,CAAC,EACH,CAAC,CAGLD,eAAe,EAAIE,YAAY,eAC9BvB,IAAA,QAAKyD,SAAS,CAAC,4EAA4E,CAAAC,QAAA,cACzF1D,IAAA,QAAKyD,SAAS,CAAC,uEAAuE,CAAAC,QAAA,cACpFxD,KAAA,QAAKuD,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/B1D,IAAA,CAACF,uBAAuB,EAAC2D,SAAS,CAAC,gCAAgC,CAAE,CAAC,cACtEvD,KAAA,OAAIuD,SAAS,CAAC,wCAAwC,CAAAC,QAAA,EAAC,SAC9C,CAACrD,SAAS,CAACuC,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,EAC5B,CAAC,cACL1C,KAAA,MAAGuD,SAAS,CAAC,4BAA4B,CAAAC,QAAA,EAAC,oCACP,CAACnC,YAAY,CAAC4B,IAAI,CAAC,mCACtD,EAAG,CAAC,cACJjD,KAAA,QAAKuD,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjD1D,IAAA,WACE2D,OAAO,CAAEA,CAAA,GAAMrC,kBAAkB,CAAC,KAAK,CAAE,CACzCmC,SAAS,CAAC,gGAAgG,CAAAC,QAAA,CAC3G,QAED,CAAQ,CAAC,cACT1D,IAAA,WACE2D,OAAO,CAAEZ,YAAa,CACtBU,SAAS,CAAC,qHAAqH,CAAAC,QAAA,CAChI,QAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACH,CAAC,CACH,CACN,EACE,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAU,UAIJ,CAAGQ,IAAA,MAAC,CAAErE,KAAK,CAAE8D,MAAM,CAAEE,QAAS,CAAC,CAAAK,IAAA,oBAC/B5E,IAAA,QAAKyD,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9BxD,KAAA,UAAOuD,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eACpD1D,IAAA,UAAOyD,SAAS,CAAC,YAAY,CAAAC,QAAA,cAC3BxD,KAAA,OAAAwD,QAAA,eACE1D,IAAA,OAAIyD,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,MAE/F,CAAI,CAAC,cACL1D,IAAA,OAAIyD,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,aAE/F,CAAI,CAAC,cACL1D,IAAA,OAAIyD,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,OAE/F,CAAI,CAAC,cACL1D,IAAA,OAAIyD,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,SAE/F,CAAI,CAAC,cACL1D,IAAA,OAAIyD,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,SAEhG,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cACR1D,IAAA,UAAOyD,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CACjDnD,KAAK,CAACqD,GAAG,CAAEU,IAAI,eACdpE,KAAA,OAAkBuD,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC5C1D,IAAA,OAAIyD,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzCxD,KAAA,QAAAwD,QAAA,eACE1D,IAAA,QAAKyD,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAEY,IAAI,CAACnB,IAAI,CAAM,CAAC,cACpEnD,IAAA,QAAKyD,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEY,IAAI,CAAChB,WAAW,CAAM,CAAC,EAC5D,CAAC,CACJ,CAAC,cACLtD,IAAA,OAAIyD,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzCxD,KAAA,SAAMuD,SAAS,CAAC,mGAAmG,CAAAC,QAAA,EAChHY,IAAI,CAAC7D,WAAW,CAACoE,MAAM,CAAC,cAC3B,EAAM,CAAC,CACL,CAAC,cACL3E,KAAA,OAAIuD,SAAS,CAAC,mDAAmD,CAAAC,QAAA,EAC9DY,IAAI,CAACQ,UAAU,EAAI,CAAC,CAAC,QACxB,EAAI,CAAC,cACL9E,IAAA,OAAIyD,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAC9D,GAAI,CAAAqB,IAAI,CAACT,IAAI,CAACU,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC,CAC7C,CAAC,cACLjF,IAAA,OAAIyD,SAAS,CAAC,4DAA4D,CAAAC,QAAA,cACxExD,KAAA,QAAKuD,SAAS,CAAC,yCAAyC,CAAAC,QAAA,eACtD1D,IAAA,WACE2D,OAAO,CAAEA,CAAA,GAAMU,MAAM,CAACC,IAAI,CAAE,CAC5Bb,SAAS,CAAC,mCAAmC,CAAAC,QAAA,cAE7C1D,IAAA,CAACP,UAAU,EAACgE,SAAS,CAAC,SAAS,CAAE,CAAC,CAC5B,CAAC,cACTzD,IAAA,WACE2D,OAAO,CAAEA,CAAA,GAAMY,QAAQ,CAACD,IAAI,CAAE,CAC9Bb,SAAS,CAAC,iCAAiC,CAAAC,QAAA,cAE3C1D,IAAA,CAACN,SAAS,EAAC+D,SAAS,CAAC,SAAS,CAAE,CAAC,CAC3B,CAAC,EACN,CAAC,CACJ,CAAC,GAjCEa,IAAI,CAACxB,EAkCV,CACL,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,EACP,CAED;AACA,KAAM,CAAA0B,gBAIJ,CAAGU,KAAA,MAAC,CAAEzE,WAAW,CAAE4D,MAAM,CAAEE,QAAS,CAAC,CAAAW,KAAA,oBACrClF,IAAA,QAAKyD,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9BxD,KAAA,UAAOuD,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eACpD1D,IAAA,UAAOyD,SAAS,CAAC,YAAY,CAAAC,QAAA,cAC3BxD,KAAA,OAAAwD,QAAA,eACE1D,IAAA,OAAIyD,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,YAE/F,CAAI,CAAC,cACL1D,IAAA,OAAIyD,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,UAE/F,CAAI,CAAC,cACL1D,IAAA,OAAIyD,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,QAE/F,CAAI,CAAC,cACL1D,IAAA,OAAIyD,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,UAE/F,CAAI,CAAC,cACL1D,IAAA,OAAIyD,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,SAEhG,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cACR1D,IAAA,UAAOyD,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CACjDjD,WAAW,CAACmD,GAAG,CAAEa,UAAU,eAC1BvE,KAAA,OAAwBuD,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAClD1D,IAAA,OAAIyD,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzCxD,KAAA,QAAAwD,QAAA,eACE1D,IAAA,QAAKyD,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAEe,UAAU,CAACtB,IAAI,CAAM,CAAC,cAC1EnD,IAAA,QAAKyD,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEe,UAAU,CAACnB,WAAW,CAAM,CAAC,EAClE,CAAC,CACJ,CAAC,cACLtD,IAAA,OAAIyD,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzC1D,IAAA,SAAMyD,SAAS,CAAC,qGAAqG,CAAAC,QAAA,CAClHe,UAAU,CAACU,QAAQ,CAChB,CAAC,CACL,CAAC,cACLnF,IAAA,OAAIyD,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzC1D,IAAA,SAAMyD,SAAS,CAAC,uGAAuG,CAAAC,QAAA,CACpHe,UAAU,CAACW,MAAM,CACd,CAAC,CACL,CAAC,cACLpF,IAAA,OAAIyD,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAC9De,UAAU,CAACY,QAAQ,CAClB,CAAC,cACLrF,IAAA,OAAIyD,SAAS,CAAC,4DAA4D,CAAAC,QAAA,cACxExD,KAAA,QAAKuD,SAAS,CAAC,yCAAyC,CAAAC,QAAA,eACtD1D,IAAA,WACE2D,OAAO,CAAEA,CAAA,GAAMU,MAAM,CAACI,UAAU,CAAE,CAClChB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,cAE7C1D,IAAA,CAACP,UAAU,EAACgE,SAAS,CAAC,SAAS,CAAE,CAAC,CAC5B,CAAC,cACTzD,IAAA,WACE2D,OAAO,CAAEA,CAAA,GAAMY,QAAQ,CAACE,UAAU,CAAE,CACpChB,SAAS,CAAC,iCAAiC,CAAAC,QAAA,cAE3C1D,IAAA,CAACN,SAAS,EAAC+D,SAAS,CAAC,SAAS,CAAE,CAAC,CAC3B,CAAC,EACN,CAAC,CACJ,CAAC,GAnCEgB,UAAU,CAAC3B,EAoChB,CACL,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,EACP,CAED;AACA,KAAM,CAAA4B,aAIJ,CAAGY,KAAA,MAAC,CAAE3E,QAAQ,CAAE0D,MAAM,CAAEE,QAAS,CAAC,CAAAe,KAAA,oBAClCtF,IAAA,QAAKyD,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9BxD,KAAA,UAAOuD,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eACpD1D,IAAA,UAAOyD,SAAS,CAAC,YAAY,CAAAC,QAAA,cAC3BxD,KAAA,OAAAwD,QAAA,eACE1D,IAAA,OAAIyD,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,QAE/F,CAAI,CAAC,cACL1D,IAAA,OAAIyD,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,QAE/F,CAAI,CAAC,cACL1D,IAAA,OAAIyD,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,QAE/F,CAAI,CAAC,cACL1D,IAAA,OAAIyD,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,WAE/F,CAAI,CAAC,cACL1D,IAAA,OAAIyD,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,SAEhG,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cACR1D,IAAA,UAAOyD,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CACjD/C,QAAQ,CAACiD,GAAG,CAAEe,MAAM,eACnBzE,KAAA,OAAoBuD,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC9C1D,IAAA,OAAIyD,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzCxD,KAAA,QAAAwD,QAAA,eACE1D,IAAA,QAAKyD,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAEiB,MAAM,CAACxB,IAAI,CAAM,CAAC,cACtEnD,IAAA,QAAKyD,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEiB,MAAM,CAACrB,WAAW,CAAM,CAAC,EAC9D,CAAC,CACJ,CAAC,cACLtD,IAAA,OAAIyD,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzC1D,IAAA,SAAMyD,SAAS,4EAAA1B,MAAA,CACb4C,MAAM,CAACY,MAAM,GAAK,OAAO,CACrB,6BAA6B,CAC7B,yBAAyB,CAC5B,CAAA7B,QAAA,CACAiB,MAAM,CAACY,MAAM,CACV,CAAC,CACL,CAAC,cACLvF,IAAA,OAAIyD,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzCxD,KAAA,SAAMuD,SAAS,4EAAA1B,MAAA,CACb4C,MAAM,CAACa,OAAO,CACV,6BAA6B,CAC7B,2BAA2B,CAC9B,CAAA9B,QAAA,EACAiB,MAAM,CAACa,OAAO,cACbxF,IAAA,CAACL,eAAe,EAAC8D,SAAS,CAAC,cAAc,CAAE,CAAC,cAE5CzD,IAAA,CAACJ,WAAW,EAAC6D,SAAS,CAAC,cAAc,CAAE,CACxC,CACAkB,MAAM,CAACa,OAAO,CAAG,SAAS,CAAG,UAAU,EACpC,CAAC,CACL,CAAC,cACLtF,KAAA,OAAIuD,SAAS,CAAC,mDAAmD,CAAAC,QAAA,EAC9DiB,MAAM,CAACc,SAAS,CAACZ,MAAM,CAAC,YAC3B,EAAI,CAAC,cACL7E,IAAA,OAAIyD,SAAS,CAAC,4DAA4D,CAAAC,QAAA,cACxExD,KAAA,QAAKuD,SAAS,CAAC,yCAAyC,CAAAC,QAAA,eACtD1D,IAAA,WACE2D,OAAO,CAAEA,CAAA,GAAMU,MAAM,CAACM,MAAM,CAAE,CAC9BlB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,cAE7C1D,IAAA,CAACP,UAAU,EAACgE,SAAS,CAAC,SAAS,CAAE,CAAC,CAC5B,CAAC,cACTzD,IAAA,WACE2D,OAAO,CAAEA,CAAA,GAAMY,QAAQ,CAACI,MAAM,CAAE,CAChClB,SAAS,CAAC,iCAAiC,CAAAC,QAAA,cAE3C1D,IAAA,CAACN,SAAS,EAAC+D,SAAS,CAAC,SAAS,CAAE,CAAC,CAC3B,CAAC,EACN,CAAC,CACJ,CAAC,GAhDEkB,MAAM,CAAC7B,EAiDZ,CACL,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,EACP,CAED,cAAe,CAAA3C,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}