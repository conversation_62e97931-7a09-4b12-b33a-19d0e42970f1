package audit

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"time"

	"github.com/codenotary/immudb/pkg/client"
	"github.com/codenotary/immudb/pkg/stdlib"
	"go.uber.org/zap"
)

// ImmuSink implements tamper-proof audit logging using ImmuDB
type ImmuSink struct {
	client client.ImmuClient
	logger *zap.Logger
	dbName string
}

// ImmuConfig holds ImmuDB configuration
type ImmuConfig struct {
	Host     string
	Port     int
	Username string
	Password string
	Database string
	DataDir  string
}

// AuditEntry represents an audit log entry
type AuditEntry struct {
	ID        string                 `json:"id"`
	UserID    string                 `json:"user_id"`
	Action    string                 `json:"action"`
	Resource  string                 `json:"resource"`
	Timestamp time.Time              `json:"timestamp"`
	Metadata  map[string]interface{} `json:"metadata"`
	ProofHash string                 `json:"proof_hash"`
}

// NewImmuSink creates a new ImmuDB audit sink
func NewImmuSink(config ImmuConfig, logger *zap.Logger) (*ImmuSink, error) {
	opts := client.DefaultOptions()
	opts.Address = fmt.Sprintf("%s:%d", config.Host, config.Port)
	opts.Username = config.Username
	opts.Password = config.Password
	opts.Database = config.Database

	if config.DataDir != "" {
		opts.Dir = config.DataDir
	}

	immuClient := client.NewClient()
	err := immuClient.OpenSession(context.Background(), []byte(config.Username), []byte(config.Password), config.Database)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to ImmuDB: %w", err)
	}

	sink := &ImmuSink{
		client: immuClient,
		logger: logger,
		dbName: config.Database,
	}

	// Initialize audit table
	if err := sink.initSchema(); err != nil {
		return nil, fmt.Errorf("failed to initialize audit schema: %w", err)
	}

	return sink, nil
}

// Close closes the ImmuDB connection
func (i *ImmuSink) Close() error {
	return i.client.CloseSession(context.Background())
}

// WriteAuditEntry writes an audit entry to ImmuDB
func (i *ImmuSink) WriteAuditEntry(ctx context.Context, entry AuditEntry) error {
	// Generate a unique ID if not provided
	if entry.ID == "" {
		entry.ID = generateEntryID(entry)
	}

	// Set timestamp if not provided
	if entry.Timestamp.IsZero() {
		entry.Timestamp = time.Now().UTC()
	}

	// Serialize the entry
	entryData, err := json.Marshal(entry)
	if err != nil {
		return fmt.Errorf("failed to marshal audit entry: %w", err)
	}

	// Calculate proof hash
	entry.ProofHash = calculateProofHash(entryData)

	// Store in ImmuDB
	key := []byte(fmt.Sprintf("audit:%s", entry.ID))
	finalData, _ := json.Marshal(entry)

	_, err = i.client.Set(ctx, key, finalData)
	if err != nil {
		return fmt.Errorf("failed to store audit entry: %w", err)
	}

	i.logger.Info("Audit entry stored",
		zap.String("id", entry.ID),
		zap.String("action", entry.Action),
		zap.String("user_id", entry.UserID),
		zap.String("resource", entry.Resource))

	return nil
}

// GetAuditEntry retrieves an audit entry by ID
func (i *ImmuSink) GetAuditEntry(ctx context.Context, entryID string) (*AuditEntry, error) {
	key := []byte(fmt.Sprintf("audit:%s", entryID))

	item, err := i.client.Get(ctx, key)
	if err != nil {
		return nil, fmt.Errorf("failed to get audit entry: %w", err)
	}

	var entry AuditEntry
	if err := json.Unmarshal(item.Value, &entry); err != nil {
		return nil, fmt.Errorf("failed to unmarshal audit entry: %w", err)
	}

	return &entry, nil
}

// VerifyAuditEntry verifies the integrity of an audit entry
func (i *ImmuSink) VerifyAuditEntry(ctx context.Context, entryID string) (*VerificationResult, error) {
	key := []byte(fmt.Sprintf("audit:%s", entryID))

	// Get the entry with proof
	item, err := i.client.VerifiedGet(ctx, key)
	if err != nil {
		return nil, fmt.Errorf("failed to get verified audit entry: %w", err)
	}

	var entry AuditEntry
	if err := json.Unmarshal(item.Value, &entry); err != nil {
		return nil, fmt.Errorf("failed to unmarshal audit entry: %w", err)
	}

	// Verify the proof hash
	entryWithoutProof := entry
	entryWithoutProof.ProofHash = ""
	originalData, _ := json.Marshal(entryWithoutProof)
	expectedHash := calculateProofHash(originalData)

	verified := entry.ProofHash == expectedHash

	return &VerificationResult{
		EntryID:      entryID,
		Verified:     verified,
		ProofHash:    entry.ProofHash,
		ExpectedHash: expectedHash,
		Revision:     item.Revision,
		Timestamp:    entry.Timestamp,
	}, nil
}

// ListAuditEntries lists audit entries with optional filters
func (i *ImmuSink) ListAuditEntries(ctx context.Context, filter AuditFilter) ([]*AuditEntry, error) {
	// For now, return mock data until ImmuDB client is properly configured
	// TODO: Implement proper ImmuDB SQL queries

	query := "SELECT value FROM audit_entries WHERE 1=1"
	args := []interface{}{}

	if filter.UserID != "" {
		query += " AND JSON_EXTRACT(value, '$.user_id') = ?"
		args = append(args, filter.UserID)
	}

	if filter.Action != "" {
		query += " AND JSON_EXTRACT(value, '$.action') = ?"
		args = append(args, filter.Action)
	}

	if !filter.StartTime.IsZero() {
		query += " AND JSON_EXTRACT(value, '$.timestamp') >= ?"
		args = append(args, filter.StartTime.Format(time.RFC3339))
	}

	if !filter.EndTime.IsZero() {
		query += " AND JSON_EXTRACT(value, '$.timestamp') <= ?"
		args = append(args, filter.EndTime.Format(time.RFC3339))
	}

	if filter.Limit > 0 {
		query += " LIMIT ?"
		args = append(args, filter.Limit)
	}

	rows, err := db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to query audit entries: %w", err)
	}
	defer rows.Close()

	var entries []*AuditEntry
	for rows.Next() {
		var valueJSON string
		if err := rows.Scan(&valueJSON); err != nil {
			continue
		}

		var entry AuditEntry
		if err := json.Unmarshal([]byte(valueJSON), &entry); err != nil {
			continue
		}

		entries = append(entries, &entry)
	}

	return entries, nil
}

// initSchema creates the audit table structure
func (i *ImmuSink) initSchema() error {
	db := stdlib.OpenDB(i.client)
	defer db.Close()

	// Create audit entries table
	_, err := db.Exec(`
		CREATE TABLE IF NOT EXISTS audit_entries (
			id VARCHAR[256] PRIMARY KEY,
			value JSON,
			created_at TIMESTAMP DEFAULT NOW()
		)
	`)
	if err != nil {
		return fmt.Errorf("failed to create audit_entries table: %w", err)
	}

	// Create indexes for common queries
	_, err = db.Exec(`CREATE INDEX IF NOT EXISTS idx_audit_user_id ON audit_entries(JSON_EXTRACT(value, '$.user_id'))`)
	if err != nil {
		i.logger.Warn("Failed to create user_id index", zap.Error(err))
	}

	_, err = db.Exec(`CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_entries(JSON_EXTRACT(value, '$.action'))`)
	if err != nil {
		i.logger.Warn("Failed to create action index", zap.Error(err))
	}

	_, err = db.Exec(`CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_entries(JSON_EXTRACT(value, '$.timestamp'))`)
	if err != nil {
		i.logger.Warn("Failed to create timestamp index", zap.Error(err))
	}

	return nil
}

// Helper functions

func generateEntryID(entry AuditEntry) string {
	data := fmt.Sprintf("%s:%s:%s:%d", entry.UserID, entry.Action, entry.Resource, entry.Timestamp.UnixNano())
	hash := sha256.Sum256([]byte(data))
	return hex.EncodeToString(hash[:])[:16] // Use first 16 chars
}

func calculateProofHash(data []byte) string {
	hash := sha256.Sum256(data)
	return hex.EncodeToString(hash[:])
}

// Data structures

type AuditFilter struct {
	UserID    string
	Action    string
	Resource  string
	StartTime time.Time
	EndTime   time.Time
	Limit     int
}

type VerificationResult struct {
	EntryID      string    `json:"entry_id"`
	Verified     bool      `json:"verified"`
	ProofHash    string    `json:"proof_hash"`
	ExpectedHash string    `json:"expected_hash"`
	Revision     uint64    `json:"revision"`
	Timestamp    time.Time `json:"timestamp"`
}
