{"ast": null, "code": "import _objectSpread from\"/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useEffect,useState}from'react';import{useAuth}from'../auth/AuthContext';import{PlusIcon,PencilIcon,TrashIcon,EyeIcon,CheckCircleIcon,XCircleIcon,MagnifyingGlassIcon}from'@heroicons/react/24/outline';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const UserManagement=()=>{const{user:currentUser}=useAuth();const[users,setUsers]=useState([]);const[loading,setLoading]=useState(true);const[searchTerm,setSearchTerm]=useState('');const[showCreateModal,setShowCreateModal]=useState(false);const[selectedUser,setSelectedUser]=useState(null);const[createForm,setCreateForm]=useState({username:'',email:'',name:'',password:'',roles:['viewer'],groups:['cainuro-users']});useEffect(()=>{fetchUsers();},[]);const fetchUsers=async()=>{try{const response=await fetch('/v1/users',{credentials:'include'});if(response.ok){const data=await response.json();setUsers(data.users||[]);}}catch(error){console.error('Failed to fetch users:',error);}finally{setLoading(false);}};const handleCreateUser=async e=>{e.preventDefault();try{const response=await fetch('/v1/users',{method:'POST',headers:{'Content-Type':'application/json'},credentials:'include',body:JSON.stringify(createForm)});if(response.ok){setShowCreateModal(false);setCreateForm({username:'',email:'',name:'',password:'',roles:['viewer'],groups:['cainuro-users']});fetchUsers();}}catch(error){console.error('Failed to create user:',error);}};const filteredUsers=users.filter(user=>user.username.toLowerCase().includes(searchTerm.toLowerCase())||user.email.toLowerCase().includes(searchTerm.toLowerCase())||user.name.toLowerCase().includes(searchTerm.toLowerCase()));const getRoleBadgeColor=role=>{switch(role){case'admin':return'bg-red-100 text-red-800';case'operator':return'bg-blue-100 text-blue-800';case'viewer':return'bg-green-100 text-green-800';default:return'bg-gray-100 text-gray-800';}};if(loading){return/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center h-64\",children:/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"})});}return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white shadow rounded-lg p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-900\",children:\"User Management\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Manage system users and their permissions\"})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setShowCreateModal(true),className:\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700\",children:[/*#__PURE__*/_jsx(PlusIcon,{className:\"h-4 w-4 mr-2\"}),\"Create User\"]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white shadow rounded-lg p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 relative\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",children:/*#__PURE__*/_jsx(MagnifyingGlassIcon,{className:\"h-5 w-5 text-gray-400\"})}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:\"Search users...\",value:searchTerm,onChange:e=>setSearchTerm(e.target.value),className:\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm text-gray-500\",children:[filteredUsers.length,\" of \",users.length,\" users\"]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white shadow rounded-lg overflow-hidden\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"px-6 py-4 border-b border-gray-200\",children:/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900\",children:\"Users\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"overflow-x-auto\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"min-w-full divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(\"thead\",{className:\"bg-gray-50\",children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"User\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Roles\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Status\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Last Login\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{className:\"bg-white divide-y divide-gray-200\",children:filteredUsers.map(user=>/*#__PURE__*/_jsxs(\"tr\",{className:\"hover:bg-gray-50\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0 h-10 w-10\",children:/*#__PURE__*/_jsx(\"div\",{className:\"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-gray-700\",children:user.name.split(' ').map(n=>n[0]).join('').toUpperCase()})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"ml-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-sm font-medium text-gray-900\",children:user.name}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-500\",children:user.email}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-xs text-gray-400\",children:[\"@\",user.username]})]})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap gap-1\",children:user.roles.map(role=>/*#__PURE__*/_jsx(\"span\",{className:\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(getRoleBadgeColor(role)),children:role},role))})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsx(\"span\",{className:\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(user.active?'bg-green-100 text-green-800':'bg-red-100 text-red-800'),children:user.active?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(CheckCircleIcon,{className:\"h-3 w-3 mr-1\"}),\"Active\"]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(XCircleIcon,{className:\"h-3 w-3 mr-1\"}),\"Inactive\"]})})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",children:user.last_login?new Date(user.last_login).toLocaleDateString():'Never'}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm font-medium\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-2\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setSelectedUser(user),className:\"text-blue-600 hover:text-blue-900\",children:/*#__PURE__*/_jsx(EyeIcon,{className:\"h-4 w-4\"})}),/*#__PURE__*/_jsx(\"button\",{className:\"text-yellow-600 hover:text-yellow-900\",children:/*#__PURE__*/_jsx(PencilIcon,{className:\"h-4 w-4\"})}),user.username!==(currentUser===null||currentUser===void 0?void 0:currentUser.username)&&user.id!==(currentUser===null||currentUser===void 0?void 0:currentUser.id)&&/*#__PURE__*/_jsx(\"button\",{className:\"text-red-600 hover:text-red-900\",children:/*#__PURE__*/_jsx(TrashIcon,{className:\"h-4 w-4\"})})]})})]},user.id))})]})})]}),showCreateModal&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",children:/*#__PURE__*/_jsx(\"div\",{className:\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"mt-3\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-4\",children:\"Create New User\"}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleCreateUser,className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Username\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",required:true,value:createForm.username,onChange:e=>setCreateForm(_objectSpread(_objectSpread({},createForm),{},{username:e.target.value})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Email\"}),/*#__PURE__*/_jsx(\"input\",{type:\"email\",required:true,value:createForm.email,onChange:e=>setCreateForm(_objectSpread(_objectSpread({},createForm),{},{email:e.target.value})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Full Name\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",required:true,value:createForm.name,onChange:e=>setCreateForm(_objectSpread(_objectSpread({},createForm),{},{name:e.target.value})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Password\"}),/*#__PURE__*/_jsx(\"input\",{type:\"password\",required:true,value:createForm.password,onChange:e=>setCreateForm(_objectSpread(_objectSpread({},createForm),{},{password:e.target.value})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-end space-x-3 pt-4\",children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:()=>setShowCreateModal(false),className:\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\",children:\"Cancel\"}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700\",children:\"Create User\"})]})]})]})})})]});};export default UserManagement;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useAuth", "PlusIcon", "PencilIcon", "TrashIcon", "EyeIcon", "CheckCircleIcon", "XCircleIcon", "MagnifyingGlassIcon", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "UserManagement", "user", "currentUser", "users", "setUsers", "loading", "setLoading", "searchTerm", "setSearchTerm", "showCreateModal", "setShowCreateModal", "selected<PERSON>ser", "setSelectedUser", "createForm", "setCreateForm", "username", "email", "name", "password", "roles", "groups", "fetchUsers", "response", "fetch", "credentials", "ok", "data", "json", "error", "console", "handleCreateUser", "e", "preventDefault", "method", "headers", "body", "JSON", "stringify", "filteredUsers", "filter", "toLowerCase", "includes", "getRoleBadgeColor", "role", "className", "children", "onClick", "type", "placeholder", "value", "onChange", "target", "length", "map", "split", "n", "join", "toUpperCase", "concat", "active", "last_login", "Date", "toLocaleDateString", "id", "onSubmit", "required", "_objectSpread"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/UserManagement.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useAuth } from '../auth/AuthContext';\nimport {\n  UsersIcon,\n  PlusIcon,\n  PencilIcon,\n  TrashIcon,\n  EyeIcon,\n  CheckCircleIcon,\n  XCircleIcon,\n  MagnifyingGlassIcon,\n} from '@heroicons/react/24/outline';\n\ninterface User {\n  id: string;\n  username: string;\n  email: string;\n  name: string;\n  roles: string[];\n  groups: string[];\n  active: boolean;\n  created_at: string;\n  last_login?: string;\n}\n\ninterface CreateUserForm {\n  username: string;\n  email: string;\n  name: string;\n  password: string;\n  roles: string[];\n  groups: string[];\n}\n\nconst UserManagement: React.FC = () => {\n  const { user: currentUser } = useAuth();\n  const [users, setUsers] = useState<User[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showCreateModal, setShowCreateModal] = useState(false);\n  const [selectedUser, setSelectedUser] = useState<User | null>(null);\n  const [createForm, setCreateForm] = useState<CreateUserForm>({\n    username: '',\n    email: '',\n    name: '',\n    password: '',\n    roles: ['viewer'],\n    groups: ['cainuro-users'],\n  });\n\n  useEffect(() => {\n    fetchUsers();\n  }, []);\n\n  const fetchUsers = async () => {\n    try {\n      const response = await fetch('/v1/users', {\n        credentials: 'include',\n      });\n      \n      if (response.ok) {\n        const data = await response.json();\n        setUsers(data.users || []);\n      }\n    } catch (error) {\n      console.error('Failed to fetch users:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreateUser = async (e: React.FormEvent) => {\n    e.preventDefault();\n    try {\n      const response = await fetch('/v1/users', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include',\n        body: JSON.stringify(createForm),\n      });\n\n      if (response.ok) {\n        setShowCreateModal(false);\n        setCreateForm({\n          username: '',\n          email: '',\n          name: '',\n          password: '',\n          roles: ['viewer'],\n          groups: ['cainuro-users'],\n        });\n        fetchUsers();\n      }\n    } catch (error) {\n      console.error('Failed to create user:', error);\n    }\n  };\n\n  const filteredUsers = users.filter(user =>\n    user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    user.name.toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  const getRoleBadgeColor = (role: string) => {\n    switch (role) {\n      case 'admin':\n        return 'bg-red-100 text-red-800';\n      case 'operator':\n        return 'bg-blue-100 text-blue-800';\n      case 'viewer':\n        return 'bg-green-100 text-green-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">User Management</h1>\n            <p className=\"text-gray-600\">Manage system users and their permissions</p>\n          </div>\n          <button\n            onClick={() => setShowCreateModal(true)}\n            className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700\"\n          >\n            <PlusIcon className=\"h-4 w-4 mr-2\" />\n            Create User\n          </button>\n        </div>\n      </div>\n\n      {/* Search and Filters */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <div className=\"flex items-center space-x-4\">\n          <div className=\"flex-1 relative\">\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <input\n              type=\"text\"\n              placeholder=\"Search users...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\n            />\n          </div>\n          <div className=\"text-sm text-gray-500\">\n            {filteredUsers.length} of {users.length} users\n          </div>\n        </div>\n      </div>\n\n      {/* Users Table */}\n      <div className=\"bg-white shadow rounded-lg overflow-hidden\">\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <h3 className=\"text-lg font-medium text-gray-900\">Users</h3>\n        </div>\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full divide-y divide-gray-200\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  User\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Roles\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Status\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Last Login\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Actions\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {filteredUsers.map((user) => (\n                <tr key={user.id} className=\"hover:bg-gray-50\">\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"flex items-center\">\n                      <div className=\"flex-shrink-0 h-10 w-10\">\n                        <div className=\"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center\">\n                          <span className=\"text-sm font-medium text-gray-700\">\n                            {user.name.split(' ').map(n => n[0]).join('').toUpperCase()}\n                          </span>\n                        </div>\n                      </div>\n                      <div className=\"ml-4\">\n                        <div className=\"text-sm font-medium text-gray-900\">{user.name}</div>\n                        <div className=\"text-sm text-gray-500\">{user.email}</div>\n                        <div className=\"text-xs text-gray-400\">@{user.username}</div>\n                      </div>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"flex flex-wrap gap-1\">\n                      {user.roles.map((role) => (\n                        <span\n                          key={role}\n                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleBadgeColor(role)}`}\n                        >\n                          {role}\n                        </span>\n                      ))}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                      user.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'\n                    }`}>\n                      {user.active ? (\n                        <>\n                          <CheckCircleIcon className=\"h-3 w-3 mr-1\" />\n                          Active\n                        </>\n                      ) : (\n                        <>\n                          <XCircleIcon className=\"h-3 w-3 mr-1\" />\n                          Inactive\n                        </>\n                      )}\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                    {user.last_login ? new Date(user.last_login).toLocaleDateString() : 'Never'}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                    <div className=\"flex space-x-2\">\n                      <button\n                        onClick={() => setSelectedUser(user)}\n                        className=\"text-blue-600 hover:text-blue-900\"\n                      >\n                        <EyeIcon className=\"h-4 w-4\" />\n                      </button>\n                      <button className=\"text-yellow-600 hover:text-yellow-900\">\n                        <PencilIcon className=\"h-4 w-4\" />\n                      </button>\n                      {user.username !== currentUser?.username && user.id !== currentUser?.id && (\n                        <button className=\"text-red-600 hover:text-red-900\">\n                          <TrashIcon className=\"h-4 w-4\" />\n                        </button>\n                      )}\n                    </div>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n\n      {/* Create User Modal */}\n      {showCreateModal && (\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n          <div className=\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\">\n            <div className=\"mt-3\">\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Create New User</h3>\n              <form onSubmit={handleCreateUser} className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">Username</label>\n                  <input\n                    type=\"text\"\n                    required\n                    value={createForm.username}\n                    onChange={(e) => setCreateForm({...createForm, username: e.target.value})}\n                    className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">Email</label>\n                  <input\n                    type=\"email\"\n                    required\n                    value={createForm.email}\n                    onChange={(e) => setCreateForm({...createForm, email: e.target.value})}\n                    className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">Full Name</label>\n                  <input\n                    type=\"text\"\n                    required\n                    value={createForm.name}\n                    onChange={(e) => setCreateForm({...createForm, name: e.target.value})}\n                    className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">Password</label>\n                  <input\n                    type=\"password\"\n                    required\n                    value={createForm.password}\n                    onChange={(e) => setCreateForm({...createForm, password: e.target.value})}\n                    className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                  />\n                </div>\n                <div className=\"flex justify-end space-x-3 pt-4\">\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowCreateModal(false)}\n                    className=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\"\n                  >\n                    Cancel\n                  </button>\n                  <button\n                    type=\"submit\"\n                    className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700\"\n                  >\n                    Create User\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default UserManagement;\n"], "mappings": "yJAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,OAAO,KAAQ,qBAAqB,CAC7C,OAEEC,QAAQ,CACRC,UAAU,CACVC,SAAS,CACTC,OAAO,CACPC,eAAe,CACfC,WAAW,CACXC,mBAAmB,KACd,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAuBrC,KAAM,CAAAC,cAAwB,CAAGA,CAAA,GAAM,CACrC,KAAM,CAAEC,IAAI,CAAEC,WAAY,CAAC,CAAGhB,OAAO,CAAC,CAAC,CACvC,KAAM,CAACiB,KAAK,CAAEC,QAAQ,CAAC,CAAGnB,QAAQ,CAAS,EAAE,CAAC,CAC9C,KAAM,CAACoB,OAAO,CAAEC,UAAU,CAAC,CAAGrB,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACsB,UAAU,CAAEC,aAAa,CAAC,CAAGvB,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACwB,eAAe,CAAEC,kBAAkB,CAAC,CAAGzB,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAAC0B,YAAY,CAAEC,eAAe,CAAC,CAAG3B,QAAQ,CAAc,IAAI,CAAC,CACnE,KAAM,CAAC4B,UAAU,CAAEC,aAAa,CAAC,CAAG7B,QAAQ,CAAiB,CAC3D8B,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,EAAE,CACTC,IAAI,CAAE,EAAE,CACRC,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,CAAC,QAAQ,CAAC,CACjBC,MAAM,CAAE,CAAC,eAAe,CAC1B,CAAC,CAAC,CAEFpC,SAAS,CAAC,IAAM,CACdqC,UAAU,CAAC,CAAC,CACd,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAA,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,WAAW,CAAE,CACxCC,WAAW,CAAE,SACf,CAAC,CAAC,CAEF,GAAIF,QAAQ,CAACG,EAAE,CAAE,CACf,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAJ,QAAQ,CAACK,IAAI,CAAC,CAAC,CAClCvB,QAAQ,CAACsB,IAAI,CAACvB,KAAK,EAAI,EAAE,CAAC,CAC5B,CACF,CAAE,MAAOyB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAChD,CAAC,OAAS,CACRtB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAwB,gBAAgB,CAAG,KAAO,CAAAC,CAAkB,EAAK,CACrDA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClB,GAAI,CACF,KAAM,CAAAV,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,WAAW,CAAE,CACxCU,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDV,WAAW,CAAE,SAAS,CACtBW,IAAI,CAAEC,IAAI,CAACC,SAAS,CAACxB,UAAU,CACjC,CAAC,CAAC,CAEF,GAAIS,QAAQ,CAACG,EAAE,CAAE,CACff,kBAAkB,CAAC,KAAK,CAAC,CACzBI,aAAa,CAAC,CACZC,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,EAAE,CACTC,IAAI,CAAE,EAAE,CACRC,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,CAAC,QAAQ,CAAC,CACjBC,MAAM,CAAE,CAAC,eAAe,CAC1B,CAAC,CAAC,CACFC,UAAU,CAAC,CAAC,CACd,CACF,CAAE,MAAOO,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAChD,CACF,CAAC,CAED,KAAM,CAAAU,aAAa,CAAGnC,KAAK,CAACoC,MAAM,CAACtC,IAAI,EACrCA,IAAI,CAACc,QAAQ,CAACyB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAClC,UAAU,CAACiC,WAAW,CAAC,CAAC,CAAC,EAC9DvC,IAAI,CAACe,KAAK,CAACwB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAClC,UAAU,CAACiC,WAAW,CAAC,CAAC,CAAC,EAC3DvC,IAAI,CAACgB,IAAI,CAACuB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAClC,UAAU,CAACiC,WAAW,CAAC,CAAC,CAC3D,CAAC,CAED,KAAM,CAAAE,iBAAiB,CAAIC,IAAY,EAAK,CAC1C,OAAQA,IAAI,EACV,IAAK,OAAO,CACV,MAAO,yBAAyB,CAClC,IAAK,UAAU,CACb,MAAO,2BAA2B,CACpC,IAAK,QAAQ,CACX,MAAO,6BAA6B,CACtC,QACE,MAAO,2BAA2B,CACtC,CACF,CAAC,CAED,GAAItC,OAAO,CAAE,CACX,mBACEV,IAAA,QAAKiD,SAAS,CAAC,uCAAuC,CAAAC,QAAA,cACpDlD,IAAA,QAAKiD,SAAS,CAAC,gEAAgE,CAAM,CAAC,CACnF,CAAC,CAEV,CAEA,mBACE/C,KAAA,QAAK+C,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExBlD,IAAA,QAAKiD,SAAS,CAAC,gCAAgC,CAAAC,QAAA,cAC7ChD,KAAA,QAAK+C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDhD,KAAA,QAAAgD,QAAA,eACElD,IAAA,OAAIiD,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,cACrElD,IAAA,MAAGiD,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,2CAAyC,CAAG,CAAC,EACvE,CAAC,cACNhD,KAAA,WACEiD,OAAO,CAAEA,CAAA,GAAMpC,kBAAkB,CAAC,IAAI,CAAE,CACxCkC,SAAS,CAAC,gJAAgJ,CAAAC,QAAA,eAE1JlD,IAAA,CAACR,QAAQ,EAACyD,SAAS,CAAC,cAAc,CAAE,CAAC,cAEvC,EAAQ,CAAC,EACN,CAAC,CACH,CAAC,cAGNjD,IAAA,QAAKiD,SAAS,CAAC,gCAAgC,CAAAC,QAAA,cAC7ChD,KAAA,QAAK+C,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1ChD,KAAA,QAAK+C,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BlD,IAAA,QAAKiD,SAAS,CAAC,sEAAsE,CAAAC,QAAA,cACnFlD,IAAA,CAACF,mBAAmB,EAACmD,SAAS,CAAC,uBAAuB,CAAE,CAAC,CACtD,CAAC,cACNjD,IAAA,UACEoD,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,iBAAiB,CAC7BC,KAAK,CAAE1C,UAAW,CAClB2C,QAAQ,CAAGnB,CAAC,EAAKvB,aAAa,CAACuB,CAAC,CAACoB,MAAM,CAACF,KAAK,CAAE,CAC/CL,SAAS,CAAC,6MAA6M,CACxN,CAAC,EACC,CAAC,cACN/C,KAAA,QAAK+C,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EACnCP,aAAa,CAACc,MAAM,CAAC,MAAI,CAACjD,KAAK,CAACiD,MAAM,CAAC,QAC1C,EAAK,CAAC,EACH,CAAC,CACH,CAAC,cAGNvD,KAAA,QAAK+C,SAAS,CAAC,4CAA4C,CAAAC,QAAA,eACzDlD,IAAA,QAAKiD,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjDlD,IAAA,OAAIiD,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,OAAK,CAAI,CAAC,CACzD,CAAC,cACNlD,IAAA,QAAKiD,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9BhD,KAAA,UAAO+C,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eACpDlD,IAAA,UAAOiD,SAAS,CAAC,YAAY,CAAAC,QAAA,cAC3BhD,KAAA,OAAAgD,QAAA,eACElD,IAAA,OAAIiD,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,MAE/F,CAAI,CAAC,cACLlD,IAAA,OAAIiD,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,OAE/F,CAAI,CAAC,cACLlD,IAAA,OAAIiD,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,QAE/F,CAAI,CAAC,cACLlD,IAAA,OAAIiD,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,YAE/F,CAAI,CAAC,cACLlD,IAAA,OAAIiD,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,SAE/F,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cACRlD,IAAA,UAAOiD,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CACjDP,aAAa,CAACe,GAAG,CAAEpD,IAAI,eACtBJ,KAAA,OAAkB+C,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC5ClD,IAAA,OAAIiD,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzChD,KAAA,QAAK+C,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChClD,IAAA,QAAKiD,SAAS,CAAC,yBAAyB,CAAAC,QAAA,cACtClD,IAAA,QAAKiD,SAAS,CAAC,qEAAqE,CAAAC,QAAA,cAClFlD,IAAA,SAAMiD,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAChD5C,IAAI,CAACgB,IAAI,CAACqC,KAAK,CAAC,GAAG,CAAC,CAACD,GAAG,CAACE,CAAC,EAAIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC,CACvD,CAAC,CACJ,CAAC,CACH,CAAC,cACN5D,KAAA,QAAK+C,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBlD,IAAA,QAAKiD,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAE5C,IAAI,CAACgB,IAAI,CAAM,CAAC,cACpEtB,IAAA,QAAKiD,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAE5C,IAAI,CAACe,KAAK,CAAM,CAAC,cACzDnB,KAAA,QAAK+C,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAC,GAAC,CAAC5C,IAAI,CAACc,QAAQ,EAAM,CAAC,EAC1D,CAAC,EACH,CAAC,CACJ,CAAC,cACLpB,IAAA,OAAIiD,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzClD,IAAA,QAAKiD,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAClC5C,IAAI,CAACkB,KAAK,CAACkC,GAAG,CAAEV,IAAI,eACnBhD,IAAA,SAEEiD,SAAS,4EAAAc,MAAA,CAA6EhB,iBAAiB,CAACC,IAAI,CAAC,CAAG,CAAAE,QAAA,CAE/GF,IAAI,EAHAA,IAID,CACP,CAAC,CACC,CAAC,CACJ,CAAC,cACLhD,IAAA,OAAIiD,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzClD,IAAA,SAAMiD,SAAS,4EAAAc,MAAA,CACbzD,IAAI,CAAC0D,MAAM,CAAG,6BAA6B,CAAG,yBAAyB,CACtE,CAAAd,QAAA,CACA5C,IAAI,CAAC0D,MAAM,cACV9D,KAAA,CAAAE,SAAA,EAAA8C,QAAA,eACElD,IAAA,CAACJ,eAAe,EAACqD,SAAS,CAAC,cAAc,CAAE,CAAC,SAE9C,EAAE,CAAC,cAEH/C,KAAA,CAAAE,SAAA,EAAA8C,QAAA,eACElD,IAAA,CAACH,WAAW,EAACoD,SAAS,CAAC,cAAc,CAAE,CAAC,WAE1C,EAAE,CACH,CACG,CAAC,CACL,CAAC,cACLjD,IAAA,OAAIiD,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAC9D5C,IAAI,CAAC2D,UAAU,CAAG,GAAI,CAAAC,IAAI,CAAC5D,IAAI,CAAC2D,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC,CAAG,OAAO,CACzE,CAAC,cACLnE,IAAA,OAAIiD,SAAS,CAAC,iDAAiD,CAAAC,QAAA,cAC7DhD,KAAA,QAAK+C,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BlD,IAAA,WACEmD,OAAO,CAAEA,CAAA,GAAMlC,eAAe,CAACX,IAAI,CAAE,CACrC2C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,cAE7ClD,IAAA,CAACL,OAAO,EAACsD,SAAS,CAAC,SAAS,CAAE,CAAC,CACzB,CAAC,cACTjD,IAAA,WAAQiD,SAAS,CAAC,uCAAuC,CAAAC,QAAA,cACvDlD,IAAA,CAACP,UAAU,EAACwD,SAAS,CAAC,SAAS,CAAE,CAAC,CAC5B,CAAC,CACR3C,IAAI,CAACc,QAAQ,IAAKb,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEa,QAAQ,GAAId,IAAI,CAAC8D,EAAE,IAAK7D,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAE6D,EAAE,gBACrEpE,IAAA,WAAQiD,SAAS,CAAC,iCAAiC,CAAAC,QAAA,cACjDlD,IAAA,CAACN,SAAS,EAACuD,SAAS,CAAC,SAAS,CAAE,CAAC,CAC3B,CACT,EACE,CAAC,CACJ,CAAC,GAlEE3C,IAAI,CAAC8D,EAmEV,CACL,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,EACH,CAAC,CAGLtD,eAAe,eACdd,IAAA,QAAKiD,SAAS,CAAC,4EAA4E,CAAAC,QAAA,cACzFlD,IAAA,QAAKiD,SAAS,CAAC,uEAAuE,CAAAC,QAAA,cACpFhD,KAAA,QAAK+C,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBlD,IAAA,OAAIiD,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,cAC3EhD,KAAA,SAAMmE,QAAQ,CAAElC,gBAAiB,CAACc,SAAS,CAAC,WAAW,CAAAC,QAAA,eACrDhD,KAAA,QAAAgD,QAAA,eACElD,IAAA,UAAOiD,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,UAAQ,CAAO,CAAC,cAC3ElD,IAAA,UACEoD,IAAI,CAAC,MAAM,CACXkB,QAAQ,MACRhB,KAAK,CAAEpC,UAAU,CAACE,QAAS,CAC3BmC,QAAQ,CAAGnB,CAAC,EAAKjB,aAAa,CAAAoD,aAAA,CAAAA,aAAA,IAAKrD,UAAU,MAAEE,QAAQ,CAAEgB,CAAC,CAACoB,MAAM,CAACF,KAAK,EAAC,CAAE,CAC1EL,SAAS,CAAC,4HAA4H,CACvI,CAAC,EACC,CAAC,cACN/C,KAAA,QAAAgD,QAAA,eACElD,IAAA,UAAOiD,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,OAAK,CAAO,CAAC,cACxElD,IAAA,UACEoD,IAAI,CAAC,OAAO,CACZkB,QAAQ,MACRhB,KAAK,CAAEpC,UAAU,CAACG,KAAM,CACxBkC,QAAQ,CAAGnB,CAAC,EAAKjB,aAAa,CAAAoD,aAAA,CAAAA,aAAA,IAAKrD,UAAU,MAAEG,KAAK,CAAEe,CAAC,CAACoB,MAAM,CAACF,KAAK,EAAC,CAAE,CACvEL,SAAS,CAAC,4HAA4H,CACvI,CAAC,EACC,CAAC,cACN/C,KAAA,QAAAgD,QAAA,eACElD,IAAA,UAAOiD,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,WAAS,CAAO,CAAC,cAC5ElD,IAAA,UACEoD,IAAI,CAAC,MAAM,CACXkB,QAAQ,MACRhB,KAAK,CAAEpC,UAAU,CAACI,IAAK,CACvBiC,QAAQ,CAAGnB,CAAC,EAAKjB,aAAa,CAAAoD,aAAA,CAAAA,aAAA,IAAKrD,UAAU,MAAEI,IAAI,CAAEc,CAAC,CAACoB,MAAM,CAACF,KAAK,EAAC,CAAE,CACtEL,SAAS,CAAC,4HAA4H,CACvI,CAAC,EACC,CAAC,cACN/C,KAAA,QAAAgD,QAAA,eACElD,IAAA,UAAOiD,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,UAAQ,CAAO,CAAC,cAC3ElD,IAAA,UACEoD,IAAI,CAAC,UAAU,CACfkB,QAAQ,MACRhB,KAAK,CAAEpC,UAAU,CAACK,QAAS,CAC3BgC,QAAQ,CAAGnB,CAAC,EAAKjB,aAAa,CAAAoD,aAAA,CAAAA,aAAA,IAAKrD,UAAU,MAAEK,QAAQ,CAAEa,CAAC,CAACoB,MAAM,CAACF,KAAK,EAAC,CAAE,CAC1EL,SAAS,CAAC,4HAA4H,CACvI,CAAC,EACC,CAAC,cACN/C,KAAA,QAAK+C,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC9ClD,IAAA,WACEoD,IAAI,CAAC,QAAQ,CACbD,OAAO,CAAEA,CAAA,GAAMpC,kBAAkB,CAAC,KAAK,CAAE,CACzCkC,SAAS,CAAC,gGAAgG,CAAAC,QAAA,CAC3G,QAED,CAAQ,CAAC,cACTlD,IAAA,WACEoD,IAAI,CAAC,QAAQ,CACbH,SAAS,CAAC,uHAAuH,CAAAC,QAAA,CAClI,aAED,CAAQ,CAAC,EACN,CAAC,EACF,CAAC,EACJ,CAAC,CACH,CAAC,CACH,CACN,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAA7C,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}