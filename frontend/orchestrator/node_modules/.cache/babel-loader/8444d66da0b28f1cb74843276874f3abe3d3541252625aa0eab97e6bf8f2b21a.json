{"ast": null, "code": "import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\nimport axios from 'axios';\nconst initialState = {\n  status: null,\n  events: [],\n  loading: false,\n  error: null,\n  metrics: {\n    cpu_usage: [],\n    memory_usage: [],\n    replica_count: []\n  }\n};\n\n// Async thunks\nexport const fetchAutoscalerStatus = createAsyncThunk('autoscaler/fetchStatus', async () => {\n  const response = await axios.get('/v1/autoscaler/status');\n  return response.data;\n});\nexport const updateAutoscalerConfig = createAsyncThunk('autoscaler/updateConfig', async config => {\n  const response = await axios.put('/v1/autoscaler/config', config);\n  return response.data;\n});\nexport const fetchScalingEvents = createAsyncThunk('autoscaler/fetchEvents', async () => {\n  const response = await axios.get('/v1/autoscaler/events');\n  return response.data.events;\n});\nexport const fetchMetrics = createAsyncThunk('autoscaler/fetchMetrics', async (timeRange = '1h') => {\n  const response = await axios.get(`/v1/autoscaler/metrics?range=${timeRange}`);\n  return response.data;\n});\nconst autoscalerSlice = createSlice({\n  name: 'autoscaler',\n  initialState,\n  reducers: {\n    clearEvents: state => {\n      state.events = [];\n    },\n    addEvent: (state, action) => {\n      state.events.unshift(action.payload);\n      // Keep only the last 100 events\n      if (state.events.length > 100) {\n        state.events = state.events.slice(0, 100);\n      }\n    }\n  },\n  extraReducers: builder => {\n    builder.addCase(fetchAutoscalerStatus.pending, state => {\n      state.loading = true;\n      state.error = null;\n    }).addCase(fetchAutoscalerStatus.fulfilled, (state, action) => {\n      state.loading = false;\n      state.status = action.payload;\n    }).addCase(fetchAutoscalerStatus.rejected, (state, action) => {\n      state.loading = false;\n      state.error = action.error.message || 'Failed to fetch autoscaler status';\n    }).addCase(updateAutoscalerConfig.pending, state => {\n      state.loading = true;\n      state.error = null;\n    }).addCase(updateAutoscalerConfig.fulfilled, (state, action) => {\n      state.loading = false;\n      state.status = {\n        ...state.status,\n        ...action.payload\n      };\n    }).addCase(updateAutoscalerConfig.rejected, (state, action) => {\n      state.loading = false;\n      state.error = action.error.message || 'Failed to update autoscaler config';\n    }).addCase(fetchScalingEvents.fulfilled, (state, action) => {\n      state.events = action.payload;\n    }).addCase(fetchMetrics.fulfilled, (state, action) => {\n      state.metrics = action.payload;\n    });\n  }\n});\nexport const {\n  clearEvents,\n  addEvent\n} = autoscalerSlice.actions;\nexport default autoscalerSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "axios", "initialState", "status", "events", "loading", "error", "metrics", "cpu_usage", "memory_usage", "replica_count", "fetchAutoscalerStatus", "response", "get", "data", "updateAutoscalerConfig", "config", "put", "fetchScalingEvents", "fetchMetrics", "timeRange", "autoscalerSlice", "name", "reducers", "clearEvents", "state", "addEvent", "action", "unshift", "payload", "length", "slice", "extraReducers", "builder", "addCase", "pending", "fulfilled", "rejected", "message", "actions", "reducer"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/autoscalerSlice.ts"], "sourcesContent": ["import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';\nimport axios from 'axios';\n\nexport interface AutoscalerStatus {\n  enabled: boolean;\n  current_replicas: number;\n  desired_replicas: number;\n  min_replicas: number;\n  max_replicas: number;\n  target_cpu_utilization: number;\n  current_cpu_utilization: number;\n  last_scale_time?: string;\n}\n\nexport interface ScalingEvent {\n  timestamp: string;\n  from_replicas: number;\n  to_replicas: number;\n  reason: string;\n  message: string;\n}\n\nexport interface AutoscalerState {\n  status: AutoscalerStatus | null;\n  events: ScalingEvent[];\n  loading: boolean;\n  error: string | null;\n  metrics: {\n    cpu_usage: Array<{ timestamp: string; value: number }>;\n    memory_usage: Array<{ timestamp: string; value: number }>;\n    replica_count: Array<{ timestamp: string; value: number }>;\n  };\n}\n\nconst initialState: AutoscalerState = {\n  status: null,\n  events: [],\n  loading: false,\n  error: null,\n  metrics: {\n    cpu_usage: [],\n    memory_usage: [],\n    replica_count: [],\n  },\n};\n\n// Async thunks\nexport const fetchAutoscalerStatus = createAsyncThunk(\n  'autoscaler/fetchStatus',\n  async () => {\n    const response = await axios.get('/v1/autoscaler/status');\n    return response.data;\n  }\n);\n\nexport const updateAutoscalerConfig = createAsyncThunk(\n  'autoscaler/updateConfig',\n  async (config: Partial<AutoscalerStatus>) => {\n    const response = await axios.put('/v1/autoscaler/config', config);\n    return response.data;\n  }\n);\n\nexport const fetchScalingEvents = createAsyncThunk(\n  'autoscaler/fetchEvents',\n  async () => {\n    const response = await axios.get('/v1/autoscaler/events');\n    return response.data.events;\n  }\n);\n\nexport const fetchMetrics = createAsyncThunk(\n  'autoscaler/fetchMetrics',\n  async (timeRange: string = '1h') => {\n    const response = await axios.get(`/v1/autoscaler/metrics?range=${timeRange}`);\n    return response.data;\n  }\n);\n\nconst autoscalerSlice = createSlice({\n  name: 'autoscaler',\n  initialState,\n  reducers: {\n    clearEvents: (state) => {\n      state.events = [];\n    },\n    addEvent: (state, action: PayloadAction<ScalingEvent>) => {\n      state.events.unshift(action.payload);\n      // Keep only the last 100 events\n      if (state.events.length > 100) {\n        state.events = state.events.slice(0, 100);\n      }\n    },\n  },\n  extraReducers: (builder) => {\n    builder\n      .addCase(fetchAutoscalerStatus.pending, (state) => {\n        state.loading = true;\n        state.error = null;\n      })\n      .addCase(fetchAutoscalerStatus.fulfilled, (state, action) => {\n        state.loading = false;\n        state.status = action.payload;\n      })\n      .addCase(fetchAutoscalerStatus.rejected, (state, action) => {\n        state.loading = false;\n        state.error = action.error.message || 'Failed to fetch autoscaler status';\n      })\n      .addCase(updateAutoscalerConfig.pending, (state) => {\n        state.loading = true;\n        state.error = null;\n      })\n      .addCase(updateAutoscalerConfig.fulfilled, (state, action) => {\n        state.loading = false;\n        state.status = { ...state.status, ...action.payload };\n      })\n      .addCase(updateAutoscalerConfig.rejected, (state, action) => {\n        state.loading = false;\n        state.error = action.error.message || 'Failed to update autoscaler config';\n      })\n      .addCase(fetchScalingEvents.fulfilled, (state, action) => {\n        state.events = action.payload;\n      })\n      .addCase(fetchMetrics.fulfilled, (state, action) => {\n        state.metrics = action.payload;\n      });\n  },\n});\n\nexport const { clearEvents, addEvent } = autoscalerSlice.actions;\nexport default autoscalerSlice.reducer;\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,gBAAgB,QAAuB,kBAAkB;AAC/E,OAAOC,KAAK,MAAM,OAAO;AAiCzB,MAAMC,YAA6B,GAAG;EACpCC,MAAM,EAAE,IAAI;EACZC,MAAM,EAAE,EAAE;EACVC,OAAO,EAAE,KAAK;EACdC,KAAK,EAAE,IAAI;EACXC,OAAO,EAAE;IACPC,SAAS,EAAE,EAAE;IACbC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE;EACjB;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,qBAAqB,GAAGX,gBAAgB,CACnD,wBAAwB,EACxB,YAAY;EACV,MAAMY,QAAQ,GAAG,MAAMX,KAAK,CAACY,GAAG,CAAC,uBAAuB,CAAC;EACzD,OAAOD,QAAQ,CAACE,IAAI;AACtB,CACF,CAAC;AAED,OAAO,MAAMC,sBAAsB,GAAGf,gBAAgB,CACpD,yBAAyB,EACzB,MAAOgB,MAAiC,IAAK;EAC3C,MAAMJ,QAAQ,GAAG,MAAMX,KAAK,CAACgB,GAAG,CAAC,uBAAuB,EAAED,MAAM,CAAC;EACjE,OAAOJ,QAAQ,CAACE,IAAI;AACtB,CACF,CAAC;AAED,OAAO,MAAMI,kBAAkB,GAAGlB,gBAAgB,CAChD,wBAAwB,EACxB,YAAY;EACV,MAAMY,QAAQ,GAAG,MAAMX,KAAK,CAACY,GAAG,CAAC,uBAAuB,CAAC;EACzD,OAAOD,QAAQ,CAACE,IAAI,CAACV,MAAM;AAC7B,CACF,CAAC;AAED,OAAO,MAAMe,YAAY,GAAGnB,gBAAgB,CAC1C,yBAAyB,EACzB,OAAOoB,SAAiB,GAAG,IAAI,KAAK;EAClC,MAAMR,QAAQ,GAAG,MAAMX,KAAK,CAACY,GAAG,CAAC,gCAAgCO,SAAS,EAAE,CAAC;EAC7E,OAAOR,QAAQ,CAACE,IAAI;AACtB,CACF,CAAC;AAED,MAAMO,eAAe,GAAGtB,WAAW,CAAC;EAClCuB,IAAI,EAAE,YAAY;EAClBpB,YAAY;EACZqB,QAAQ,EAAE;IACRC,WAAW,EAAGC,KAAK,IAAK;MACtBA,KAAK,CAACrB,MAAM,GAAG,EAAE;IACnB,CAAC;IACDsB,QAAQ,EAAEA,CAACD,KAAK,EAAEE,MAAmC,KAAK;MACxDF,KAAK,CAACrB,MAAM,CAACwB,OAAO,CAACD,MAAM,CAACE,OAAO,CAAC;MACpC;MACA,IAAIJ,KAAK,CAACrB,MAAM,CAAC0B,MAAM,GAAG,GAAG,EAAE;QAC7BL,KAAK,CAACrB,MAAM,GAAGqB,KAAK,CAACrB,MAAM,CAAC2B,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC;MAC3C;IACF;EACF,CAAC;EACDC,aAAa,EAAGC,OAAO,IAAK;IAC1BA,OAAO,CACJC,OAAO,CAACvB,qBAAqB,CAACwB,OAAO,EAAGV,KAAK,IAAK;MACjDA,KAAK,CAACpB,OAAO,GAAG,IAAI;MACpBoB,KAAK,CAACnB,KAAK,GAAG,IAAI;IACpB,CAAC,CAAC,CACD4B,OAAO,CAACvB,qBAAqB,CAACyB,SAAS,EAAE,CAACX,KAAK,EAAEE,MAAM,KAAK;MAC3DF,KAAK,CAACpB,OAAO,GAAG,KAAK;MACrBoB,KAAK,CAACtB,MAAM,GAAGwB,MAAM,CAACE,OAAO;IAC/B,CAAC,CAAC,CACDK,OAAO,CAACvB,qBAAqB,CAAC0B,QAAQ,EAAE,CAACZ,KAAK,EAAEE,MAAM,KAAK;MAC1DF,KAAK,CAACpB,OAAO,GAAG,KAAK;MACrBoB,KAAK,CAACnB,KAAK,GAAGqB,MAAM,CAACrB,KAAK,CAACgC,OAAO,IAAI,mCAAmC;IAC3E,CAAC,CAAC,CACDJ,OAAO,CAACnB,sBAAsB,CAACoB,OAAO,EAAGV,KAAK,IAAK;MAClDA,KAAK,CAACpB,OAAO,GAAG,IAAI;MACpBoB,KAAK,CAACnB,KAAK,GAAG,IAAI;IACpB,CAAC,CAAC,CACD4B,OAAO,CAACnB,sBAAsB,CAACqB,SAAS,EAAE,CAACX,KAAK,EAAEE,MAAM,KAAK;MAC5DF,KAAK,CAACpB,OAAO,GAAG,KAAK;MACrBoB,KAAK,CAACtB,MAAM,GAAG;QAAE,GAAGsB,KAAK,CAACtB,MAAM;QAAE,GAAGwB,MAAM,CAACE;MAAQ,CAAC;IACvD,CAAC,CAAC,CACDK,OAAO,CAACnB,sBAAsB,CAACsB,QAAQ,EAAE,CAACZ,KAAK,EAAEE,MAAM,KAAK;MAC3DF,KAAK,CAACpB,OAAO,GAAG,KAAK;MACrBoB,KAAK,CAACnB,KAAK,GAAGqB,MAAM,CAACrB,KAAK,CAACgC,OAAO,IAAI,oCAAoC;IAC5E,CAAC,CAAC,CACDJ,OAAO,CAAChB,kBAAkB,CAACkB,SAAS,EAAE,CAACX,KAAK,EAAEE,MAAM,KAAK;MACxDF,KAAK,CAACrB,MAAM,GAAGuB,MAAM,CAACE,OAAO;IAC/B,CAAC,CAAC,CACDK,OAAO,CAACf,YAAY,CAACiB,SAAS,EAAE,CAACX,KAAK,EAAEE,MAAM,KAAK;MAClDF,KAAK,CAAClB,OAAO,GAAGoB,MAAM,CAACE,OAAO;IAChC,CAAC,CAAC;EACN;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEL,WAAW;EAAEE;AAAS,CAAC,GAAGL,eAAe,CAACkB,OAAO;AAChE,eAAelB,eAAe,CAACmB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}