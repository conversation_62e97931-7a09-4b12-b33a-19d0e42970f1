{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/WorkflowExecutor.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { fetchWorkflows, executeWorkflow, setSelectedWorkflow, getWorkflowStatus } from '../store/slices/workflowSlice';\nimport { PlayIcon, ClockIcon, CheckCircleIcon, XCircleIcon, CogIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WorkflowExecutor = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    workflows,\n    executions,\n    loading,\n    error,\n    selectedWorkflow\n  } = useSelector(state => state.workflow);\n  const [inputs, setInputs] = useState({});\n  useEffect(() => {\n    dispatch(fetchWorkflows());\n  }, [dispatch]);\n  const handleExecute = () => {\n    if (selectedWorkflow) {\n      dispatch(executeWorkflow({\n        workflowId: selectedWorkflow.id,\n        inputs\n      }));\n      setInputs({});\n    }\n  };\n  const getStatusIcon = status => {\n    switch (status) {\n      case 'running':\n        return /*#__PURE__*/_jsxDEV(ClockIcon, {\n          className: \"h-5 w-5 text-yellow-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 16\n        }, this);\n      case 'completed':\n        return /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n          className: \"h-5 w-5 text-green-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 16\n        }, this);\n      case 'failed':\n        return /*#__PURE__*/_jsxDEV(XCircleIcon, {\n          className: \"h-5 w-5 text-red-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(CogIcon, {\n          className: \"h-5 w-5 text-gray-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'running':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'completed':\n        return 'bg-green-100 text-green-800';\n      case 'failed':\n        return 'bg-red-100 text-red-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold text-white\",\n        children: \"Workflow Executor\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-1 text-sm text-gray-400\",\n        children: \"Execute and monitor automated workflows across your infrastructure\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 shadow rounded-lg\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-4 py-5 sm:p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg leading-6 font-medium text-white mb-4\",\n            children: \"Available Workflows\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center h-32\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [workflows.map(workflow => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `border rounded-lg p-4 cursor-pointer transition-colors ${(selectedWorkflow === null || selectedWorkflow === void 0 ? void 0 : selectedWorkflow.id) === workflow.id ? 'border-cyan-500 bg-cyan-500/10' : 'border-gray-600 hover:bg-gray-700'}`,\n              onClick: () => dispatch(setSelectedWorkflow(workflow)),\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-lg font-medium text-white\",\n                    children: workflow.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 102,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mt-1 text-sm text-gray-400\",\n                    children: workflow.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 103,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mt-1 text-xs text-gray-500\",\n                    children: [\"Created: \", new Date(workflow.created_at).toLocaleDateString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 104,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 101,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(CogIcon, {\n                  className: \"h-6 w-6 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 108,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 21\n              }, this)\n            }, workflow.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 19\n            }, this)), workflows.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-8\",\n              children: [/*#__PURE__*/_jsxDEV(CogIcon, {\n                className: \"mx-auto h-12 w-12 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"mt-2 text-sm font-medium text-gray-300\",\n                children: \"No workflows available\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-gray-400\",\n                children: \"Create a workflow to get started.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 shadow rounded-lg\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-4 py-5 sm:p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg leading-6 font-medium text-white mb-4\",\n            children: \"Execute Workflow\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this), selectedWorkflow ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-md font-medium text-white\",\n                children: selectedWorkflow.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-400\",\n                children: selectedWorkflow.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                children: \"Input Parameters (JSON)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                className: \"block w-full h-32 px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent\",\n                placeholder: \"{\\\"key\\\": \\\"value\\\"}\",\n                value: JSON.stringify(inputs, null, 2),\n                onChange: e => {\n                  try {\n                    setInputs(JSON.parse(e.target.value || '{}'));\n                  } catch {\n                    // Invalid JSON, keep the text as is\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleExecute,\n              disabled: loading,\n              className: \"w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 disabled:opacity-50\",\n              children: [/*#__PURE__*/_jsxDEV(PlayIcon, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 19\n              }, this), loading ? 'Executing...' : 'Execute Workflow']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-8\",\n            children: [/*#__PURE__*/_jsxDEV(PlayIcon, {\n              className: \"mx-auto h-12 w-12 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"mt-2 text-sm font-medium text-gray-300\",\n              children: \"Select a workflow\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-gray-400\",\n              children: \"Choose a workflow from the list to execute it.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 15\n          }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4 bg-red-50 border border-red-200 rounded-md p-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-red-700\",\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 shadow rounded-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-4 py-5 sm:p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg leading-6 font-medium text-white mb-4\",\n          children: \"Execution History\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this), executions.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-8\",\n          children: [/*#__PURE__*/_jsxDEV(ClockIcon, {\n            className: \"mx-auto h-12 w-12 text-gray-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"mt-2 text-sm font-medium text-gray-300\",\n            children: \"No executions yet\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-1 text-sm text-gray-400\",\n            children: \"Execute a workflow to see the history here.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: executions.map(execution => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border border-gray-600 rounded-lg p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [getStatusIcon(execution.status), /*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-lg font-medium text-white\",\n                    children: execution.workflow_id\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 214,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(execution.status)}`,\n                    children: execution.status\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 217,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mt-1 text-sm text-gray-400\",\n                  children: [\"Execution ID: \", execution.execution_id]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 23\n                }, this), execution.started_at && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mt-1 text-xs text-gray-500\",\n                  children: [\"Started: \", new Date(execution.started_at).toLocaleString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 25\n                }, this), execution.completed_at && /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mt-1 text-xs text-gray-500\",\n                  children: [\"Completed: \", new Date(execution.completed_at).toLocaleString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => dispatch(getWorkflowStatus(execution.execution_id)),\n                className: \"text-cyan-400 hover:text-cyan-300 text-sm\",\n                children: \"Refresh\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 19\n            }, this), execution.outputs && Object.keys(execution.outputs).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"text-sm font-medium text-gray-300\",\n                children: \"Outputs:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n                className: \"mt-1 text-xs text-gray-400 bg-gray-700 p-2 rounded overflow-x-auto\",\n                children: JSON.stringify(execution.outputs, null, 2)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 21\n            }, this)]\n          }, execution.execution_id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 189,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 67,\n    columnNumber: 5\n  }, this);\n};\n_s(WorkflowExecutor, \"8W9B8DsFzlZh3mzlg7zSRDKB4tg=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = WorkflowExecutor;\nexport default WorkflowExecutor;\nvar _c;\n$RefreshReg$(_c, \"WorkflowExecutor\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useDispatch", "useSelector", "fetchWorkflows", "executeWorkflow", "setSelectedWorkflow", "getWorkflowStatus", "PlayIcon", "ClockIcon", "CheckCircleIcon", "XCircleIcon", "CogIcon", "jsxDEV", "_jsxDEV", "WorkflowExecutor", "_s", "dispatch", "workflows", "executions", "loading", "error", "selectedWorkflow", "state", "workflow", "inputs", "setInputs", "handleExecute", "workflowId", "id", "getStatusIcon", "status", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getStatusColor", "children", "map", "onClick", "name", "description", "Date", "created_at", "toLocaleDateString", "length", "placeholder", "value", "JSON", "stringify", "onChange", "e", "parse", "target", "disabled", "execution", "workflow_id", "execution_id", "started_at", "toLocaleString", "completed_at", "outputs", "Object", "keys", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/WorkflowExecutor.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { RootState, AppDispatch } from '../store/store';\nimport {\n  fetchWorkflows,\n  executeWorkflow,\n  setSelectedWorkflow,\n  getWorkflowStatus,\n} from '../store/slices/workflowSlice';\nimport {\n  PlayIcon,\n  ClockIcon,\n  CheckCircleIcon,\n  XCircleIcon,\n  CogIcon,\n} from '@heroicons/react/24/outline';\n\nconst WorkflowExecutor: React.FC = () => {\n  const dispatch = useDispatch<AppDispatch>();\n  const { workflows, executions, loading, error, selectedWorkflow } = useSelector(\n    (state: RootState) => state.workflow\n  );\n\n  const [inputs, setInputs] = useState<Record<string, any>>({});\n\n  useEffect(() => {\n    dispatch(fetchWorkflows());\n  }, [dispatch]);\n\n  const handleExecute = () => {\n    if (selectedWorkflow) {\n      dispatch(executeWorkflow({\n        workflowId: selectedWorkflow.id,\n        inputs,\n      }));\n      setInputs({});\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'running':\n        return <ClockIcon className=\"h-5 w-5 text-yellow-400\" />;\n      case 'completed':\n        return <CheckCircleIcon className=\"h-5 w-5 text-green-400\" />;\n      case 'failed':\n        return <XCircleIcon className=\"h-5 w-5 text-red-400\" />;\n      default:\n        return <CogIcon className=\"h-5 w-5 text-gray-400\" />;\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'running':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'completed':\n        return 'bg-green-100 text-green-800';\n      case 'failed':\n        return 'bg-red-100 text-red-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div>\n        <h1 className=\"text-2xl font-bold text-white\">Workflow Executor</h1>\n        <p className=\"mt-1 text-sm text-gray-400\">\n          Execute and monitor automated workflows across your infrastructure\n        </p>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Workflow Selection */}\n        <div className=\"bg-gray-800 shadow rounded-lg\">\n          <div className=\"px-4 py-5 sm:p-6\">\n            <h3 className=\"text-lg leading-6 font-medium text-white mb-4\">\n              Available Workflows\n            </h3>\n\n            {loading ? (\n              <div className=\"flex items-center justify-center h-32\">\n                <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500\"></div>\n              </div>\n            ) : (\n              <div className=\"space-y-3\">\n                {workflows.map((workflow) => (\n                  <div\n                    key={workflow.id}\n                    className={`border rounded-lg p-4 cursor-pointer transition-colors ${\n                      selectedWorkflow?.id === workflow.id\n                        ? 'border-cyan-500 bg-cyan-500/10'\n                        : 'border-gray-600 hover:bg-gray-700'\n                    }`}\n                    onClick={() => dispatch(setSelectedWorkflow(workflow))}\n                  >\n                    <div className=\"flex items-start justify-between\">\n                      <div>\n                        <h4 className=\"text-lg font-medium text-white\">{workflow.name}</h4>\n                        <p className=\"mt-1 text-sm text-gray-400\">{workflow.description}</p>\n                        <p className=\"mt-1 text-xs text-gray-500\">\n                          Created: {new Date(workflow.created_at).toLocaleDateString()}\n                        </p>\n                      </div>\n                      <CogIcon className=\"h-6 w-6 text-gray-400\" />\n                    </div>\n                  </div>\n                ))}\n\n                {workflows.length === 0 && (\n                  <div className=\"text-center py-8\">\n                    <CogIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n                    <h3 className=\"mt-2 text-sm font-medium text-gray-300\">No workflows available</h3>\n                    <p className=\"mt-1 text-sm text-gray-400\">\n                      Create a workflow to get started.\n                    </p>\n                  </div>\n                )}\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Workflow Execution */}\n        <div className=\"bg-gray-800 shadow rounded-lg\">\n          <div className=\"px-4 py-5 sm:p-6\">\n            <h3 className=\"text-lg leading-6 font-medium text-white mb-4\">\n              Execute Workflow\n            </h3>\n\n            {selectedWorkflow ? (\n              <div className=\"space-y-4\">\n                <div>\n                  <h4 className=\"text-md font-medium text-white\">{selectedWorkflow.name}</h4>\n                  <p className=\"text-sm text-gray-400\">{selectedWorkflow.description}</p>\n                </div>\n\n                {/* Input Parameters */}\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                    Input Parameters (JSON)\n                  </label>\n                  <textarea\n                    className=\"block w-full h-32 px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent\"\n                    placeholder='{\"key\": \"value\"}'\n                    value={JSON.stringify(inputs, null, 2)}\n                    onChange={(e) => {\n                      try {\n                        setInputs(JSON.parse(e.target.value || '{}'));\n                      } catch {\n                        // Invalid JSON, keep the text as is\n                      }\n                    }}\n                  />\n                </div>\n\n                <button\n                  onClick={handleExecute}\n                  disabled={loading}\n                  className=\"w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 disabled:opacity-50\"\n                >\n                  <PlayIcon className=\"h-4 w-4 mr-2\" />\n                  {loading ? 'Executing...' : 'Execute Workflow'}\n                </button>\n              </div>\n            ) : (\n              <div className=\"text-center py-8\">\n                <PlayIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n                <h3 className=\"mt-2 text-sm font-medium text-gray-300\">Select a workflow</h3>\n                <p className=\"mt-1 text-sm text-gray-400\">\n                  Choose a workflow from the list to execute it.\n                </p>\n              </div>\n            )}\n\n            {error && (\n              <div className=\"mt-4 bg-red-50 border border-red-200 rounded-md p-4\">\n                <div className=\"text-sm text-red-700\">{error}</div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Execution History */}\n      <div className=\"bg-gray-800 shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <h3 className=\"text-lg leading-6 font-medium text-white mb-4\">\n            Execution History\n          </h3>\n\n          {executions.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <ClockIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n              <h3 className=\"mt-2 text-sm font-medium text-gray-300\">No executions yet</h3>\n              <p className=\"mt-1 text-sm text-gray-400\">\n                Execute a workflow to see the history here.\n              </p>\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              {executions.map((execution) => (\n                <div\n                  key={execution.execution_id}\n                  className=\"border border-gray-600 rounded-lg p-4\"\n                >\n                  <div className=\"flex items-start justify-between\">\n                    <div className=\"flex-1\">\n                      <div className=\"flex items-center space-x-2\">\n                        {getStatusIcon(execution.status)}\n                        <h4 className=\"text-lg font-medium text-white\">\n                          {execution.workflow_id}\n                        </h4>\n                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(execution.status)}`}>\n                          {execution.status}\n                        </span>\n                      </div>\n                      <p className=\"mt-1 text-sm text-gray-400\">\n                        Execution ID: {execution.execution_id}\n                      </p>\n                      {execution.started_at && (\n                        <p className=\"mt-1 text-xs text-gray-500\">\n                          Started: {new Date(execution.started_at).toLocaleString()}\n                        </p>\n                      )}\n                      {execution.completed_at && (\n                        <p className=\"mt-1 text-xs text-gray-500\">\n                          Completed: {new Date(execution.completed_at).toLocaleString()}\n                        </p>\n                      )}\n                    </div>\n                    <button\n                      onClick={() => dispatch(getWorkflowStatus(execution.execution_id))}\n                      className=\"text-cyan-400 hover:text-cyan-300 text-sm\"\n                    >\n                      Refresh\n                    </button>\n                  </div>\n\n                  {execution.outputs && Object.keys(execution.outputs).length > 0 && (\n                    <div className=\"mt-3\">\n                      <h5 className=\"text-sm font-medium text-gray-300\">Outputs:</h5>\n                      <pre className=\"mt-1 text-xs text-gray-400 bg-gray-700 p-2 rounded overflow-x-auto\">\n                        {JSON.stringify(execution.outputs, null, 2)}\n                      </pre>\n                    </div>\n                  )}\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default WorkflowExecutor;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,SACEC,cAAc,EACdC,eAAe,EACfC,mBAAmB,EACnBC,iBAAiB,QACZ,+BAA+B;AACtC,SACEC,QAAQ,EACRC,SAAS,EACTC,eAAe,EACfC,WAAW,EACXC,OAAO,QACF,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAMC,QAAQ,GAAGf,WAAW,CAAc,CAAC;EAC3C,MAAM;IAAEgB,SAAS;IAAEC,UAAU;IAAEC,OAAO;IAAEC,KAAK;IAAEC;EAAiB,CAAC,GAAGnB,WAAW,CAC5EoB,KAAgB,IAAKA,KAAK,CAACC,QAC9B,CAAC;EAED,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG1B,QAAQ,CAAsB,CAAC,CAAC,CAAC;EAE7DC,SAAS,CAAC,MAAM;IACdgB,QAAQ,CAACb,cAAc,CAAC,CAAC,CAAC;EAC5B,CAAC,EAAE,CAACa,QAAQ,CAAC,CAAC;EAEd,MAAMU,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIL,gBAAgB,EAAE;MACpBL,QAAQ,CAACZ,eAAe,CAAC;QACvBuB,UAAU,EAAEN,gBAAgB,CAACO,EAAE;QAC/BJ;MACF,CAAC,CAAC,CAAC;MACHC,SAAS,CAAC,CAAC,CAAC,CAAC;IACf;EACF,CAAC;EAED,MAAMI,aAAa,GAAIC,MAAc,IAAK;IACxC,QAAQA,MAAM;MACZ,KAAK,SAAS;QACZ,oBAAOjB,OAAA,CAACL,SAAS;UAACuB,SAAS,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC1D,KAAK,WAAW;QACd,oBAAOtB,OAAA,CAACJ,eAAe;UAACsB,SAAS,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC/D,KAAK,QAAQ;QACX,oBAAOtB,OAAA,CAACH,WAAW;UAACqB,SAAS,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzD;QACE,oBAAOtB,OAAA,CAACF,OAAO;UAACoB,SAAS,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACxD;EACF,CAAC;EAED,MAAMC,cAAc,GAAIN,MAAc,IAAK;IACzC,QAAQA,MAAM;MACZ,KAAK,SAAS;QACZ,OAAO,+BAA+B;MACxC,KAAK,WAAW;QACd,OAAO,6BAA6B;MACtC,KAAK,QAAQ;QACX,OAAO,yBAAyB;MAClC;QACE,OAAO,2BAA2B;IACtC;EACF,CAAC;EAED,oBACEjB,OAAA;IAAKkB,SAAS,EAAC,WAAW;IAAAM,QAAA,gBAExBxB,OAAA;MAAAwB,QAAA,gBACExB,OAAA;QAAIkB,SAAS,EAAC,+BAA+B;QAAAM,QAAA,EAAC;MAAiB;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpEtB,OAAA;QAAGkB,SAAS,EAAC,4BAA4B;QAAAM,QAAA,EAAC;MAE1C;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAENtB,OAAA;MAAKkB,SAAS,EAAC,uCAAuC;MAAAM,QAAA,gBAEpDxB,OAAA;QAAKkB,SAAS,EAAC,+BAA+B;QAAAM,QAAA,eAC5CxB,OAAA;UAAKkB,SAAS,EAAC,kBAAkB;UAAAM,QAAA,gBAC/BxB,OAAA;YAAIkB,SAAS,EAAC,+CAA+C;YAAAM,QAAA,EAAC;UAE9D;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAEJhB,OAAO,gBACNN,OAAA;YAAKkB,SAAS,EAAC,uCAAuC;YAAAM,QAAA,eACpDxB,OAAA;cAAKkB,SAAS,EAAC;YAA8D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjF,CAAC,gBAENtB,OAAA;YAAKkB,SAAS,EAAC,WAAW;YAAAM,QAAA,GACvBpB,SAAS,CAACqB,GAAG,CAAEf,QAAQ,iBACtBV,OAAA;cAEEkB,SAAS,EAAE,0DACT,CAAAV,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEO,EAAE,MAAKL,QAAQ,CAACK,EAAE,GAChC,gCAAgC,GAChC,mCAAmC,EACtC;cACHW,OAAO,EAAEA,CAAA,KAAMvB,QAAQ,CAACX,mBAAmB,CAACkB,QAAQ,CAAC,CAAE;cAAAc,QAAA,eAEvDxB,OAAA;gBAAKkB,SAAS,EAAC,kCAAkC;gBAAAM,QAAA,gBAC/CxB,OAAA;kBAAAwB,QAAA,gBACExB,OAAA;oBAAIkB,SAAS,EAAC,gCAAgC;oBAAAM,QAAA,EAAEd,QAAQ,CAACiB;kBAAI;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnEtB,OAAA;oBAAGkB,SAAS,EAAC,4BAA4B;oBAAAM,QAAA,EAAEd,QAAQ,CAACkB;kBAAW;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpEtB,OAAA;oBAAGkB,SAAS,EAAC,4BAA4B;oBAAAM,QAAA,GAAC,WAC/B,EAAC,IAAIK,IAAI,CAACnB,QAAQ,CAACoB,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC;kBAAA;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACNtB,OAAA,CAACF,OAAO;kBAACoB,SAAS,EAAC;gBAAuB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C;YAAC,GAjBDZ,QAAQ,CAACK,EAAE;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkBb,CACN,CAAC,EAEDlB,SAAS,CAAC4B,MAAM,KAAK,CAAC,iBACrBhC,OAAA;cAAKkB,SAAS,EAAC,kBAAkB;cAAAM,QAAA,gBAC/BxB,OAAA,CAACF,OAAO;gBAACoB,SAAS,EAAC;cAAiC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvDtB,OAAA;gBAAIkB,SAAS,EAAC,wCAAwC;gBAAAM,QAAA,EAAC;cAAsB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClFtB,OAAA;gBAAGkB,SAAS,EAAC,4BAA4B;gBAAAM,QAAA,EAAC;cAE1C;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtB,OAAA;QAAKkB,SAAS,EAAC,+BAA+B;QAAAM,QAAA,eAC5CxB,OAAA;UAAKkB,SAAS,EAAC,kBAAkB;UAAAM,QAAA,gBAC/BxB,OAAA;YAAIkB,SAAS,EAAC,+CAA+C;YAAAM,QAAA,EAAC;UAE9D;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAEJd,gBAAgB,gBACfR,OAAA;YAAKkB,SAAS,EAAC,WAAW;YAAAM,QAAA,gBACxBxB,OAAA;cAAAwB,QAAA,gBACExB,OAAA;gBAAIkB,SAAS,EAAC,gCAAgC;gBAAAM,QAAA,EAAEhB,gBAAgB,CAACmB;cAAI;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3EtB,OAAA;gBAAGkB,SAAS,EAAC,uBAAuB;gBAAAM,QAAA,EAAEhB,gBAAgB,CAACoB;cAAW;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC,eAGNtB,OAAA;cAAAwB,QAAA,gBACExB,OAAA;gBAAOkB,SAAS,EAAC,8CAA8C;gBAAAM,QAAA,EAAC;cAEhE;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRtB,OAAA;gBACEkB,SAAS,EAAC,wLAAwL;gBAClMe,WAAW,EAAC,sBAAkB;gBAC9BC,KAAK,EAAEC,IAAI,CAACC,SAAS,CAACzB,MAAM,EAAE,IAAI,EAAE,CAAC,CAAE;gBACvC0B,QAAQ,EAAGC,CAAC,IAAK;kBACf,IAAI;oBACF1B,SAAS,CAACuB,IAAI,CAACI,KAAK,CAACD,CAAC,CAACE,MAAM,CAACN,KAAK,IAAI,IAAI,CAAC,CAAC;kBAC/C,CAAC,CAAC,MAAM;oBACN;kBAAA;gBAEJ;cAAE;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENtB,OAAA;cACE0B,OAAO,EAAEb,aAAc;cACvB4B,QAAQ,EAAEnC,OAAQ;cAClBY,SAAS,EAAC,2PAA2P;cAAAM,QAAA,gBAErQxB,OAAA,CAACN,QAAQ;gBAACwB,SAAS,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EACpChB,OAAO,GAAG,cAAc,GAAG,kBAAkB;YAAA;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,gBAENtB,OAAA;YAAKkB,SAAS,EAAC,kBAAkB;YAAAM,QAAA,gBAC/BxB,OAAA,CAACN,QAAQ;cAACwB,SAAS,EAAC;YAAiC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxDtB,OAAA;cAAIkB,SAAS,EAAC,wCAAwC;cAAAM,QAAA,EAAC;YAAiB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7EtB,OAAA;cAAGkB,SAAS,EAAC,4BAA4B;cAAAM,QAAA,EAAC;YAE1C;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACN,EAEAf,KAAK,iBACJP,OAAA;YAAKkB,SAAS,EAAC,qDAAqD;YAAAM,QAAA,eAClExB,OAAA;cAAKkB,SAAS,EAAC,sBAAsB;cAAAM,QAAA,EAAEjB;YAAK;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtB,OAAA;MAAKkB,SAAS,EAAC,+BAA+B;MAAAM,QAAA,eAC5CxB,OAAA;QAAKkB,SAAS,EAAC,kBAAkB;QAAAM,QAAA,gBAC/BxB,OAAA;UAAIkB,SAAS,EAAC,+CAA+C;UAAAM,QAAA,EAAC;QAE9D;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAEJjB,UAAU,CAAC2B,MAAM,KAAK,CAAC,gBACtBhC,OAAA;UAAKkB,SAAS,EAAC,kBAAkB;UAAAM,QAAA,gBAC/BxB,OAAA,CAACL,SAAS;YAACuB,SAAS,EAAC;UAAiC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzDtB,OAAA;YAAIkB,SAAS,EAAC,wCAAwC;YAAAM,QAAA,EAAC;UAAiB;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7EtB,OAAA;YAAGkB,SAAS,EAAC,4BAA4B;YAAAM,QAAA,EAAC;UAE1C;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,gBAENtB,OAAA;UAAKkB,SAAS,EAAC,WAAW;UAAAM,QAAA,EACvBnB,UAAU,CAACoB,GAAG,CAAEiB,SAAS,iBACxB1C,OAAA;YAEEkB,SAAS,EAAC,uCAAuC;YAAAM,QAAA,gBAEjDxB,OAAA;cAAKkB,SAAS,EAAC,kCAAkC;cAAAM,QAAA,gBAC/CxB,OAAA;gBAAKkB,SAAS,EAAC,QAAQ;gBAAAM,QAAA,gBACrBxB,OAAA;kBAAKkB,SAAS,EAAC,6BAA6B;kBAAAM,QAAA,GACzCR,aAAa,CAAC0B,SAAS,CAACzB,MAAM,CAAC,eAChCjB,OAAA;oBAAIkB,SAAS,EAAC,gCAAgC;oBAAAM,QAAA,EAC3CkB,SAAS,CAACC;kBAAW;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC,eACLtB,OAAA;oBAAMkB,SAAS,EAAE,2EAA2EK,cAAc,CAACmB,SAAS,CAACzB,MAAM,CAAC,EAAG;oBAAAO,QAAA,EAC5HkB,SAAS,CAACzB;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNtB,OAAA;kBAAGkB,SAAS,EAAC,4BAA4B;kBAAAM,QAAA,GAAC,gBAC1B,EAACkB,SAAS,CAACE,YAAY;gBAAA;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC,EACHoB,SAAS,CAACG,UAAU,iBACnB7C,OAAA;kBAAGkB,SAAS,EAAC,4BAA4B;kBAAAM,QAAA,GAAC,WAC/B,EAAC,IAAIK,IAAI,CAACa,SAAS,CAACG,UAAU,CAAC,CAACC,cAAc,CAAC,CAAC;gBAAA;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CACJ,EACAoB,SAAS,CAACK,YAAY,iBACrB/C,OAAA;kBAAGkB,SAAS,EAAC,4BAA4B;kBAAAM,QAAA,GAAC,aAC7B,EAAC,IAAIK,IAAI,CAACa,SAAS,CAACK,YAAY,CAAC,CAACD,cAAc,CAAC,CAAC;gBAAA;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D,CACJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNtB,OAAA;gBACE0B,OAAO,EAAEA,CAAA,KAAMvB,QAAQ,CAACV,iBAAiB,CAACiD,SAAS,CAACE,YAAY,CAAC,CAAE;gBACnE1B,SAAS,EAAC,2CAA2C;gBAAAM,QAAA,EACtD;cAED;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAELoB,SAAS,CAACM,OAAO,IAAIC,MAAM,CAACC,IAAI,CAACR,SAAS,CAACM,OAAO,CAAC,CAAChB,MAAM,GAAG,CAAC,iBAC7DhC,OAAA;cAAKkB,SAAS,EAAC,MAAM;cAAAM,QAAA,gBACnBxB,OAAA;gBAAIkB,SAAS,EAAC,mCAAmC;gBAAAM,QAAA,EAAC;cAAQ;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/DtB,OAAA;gBAAKkB,SAAS,EAAC,oEAAoE;gBAAAM,QAAA,EAChFW,IAAI,CAACC,SAAS,CAACM,SAAS,CAACM,OAAO,EAAE,IAAI,EAAE,CAAC;cAAC;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA,GA3CIoB,SAAS,CAACE,YAAY;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA4CxB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpB,EAAA,CAjPID,gBAA0B;EAAA,QACbb,WAAW,EACwCC,WAAW;AAAA;AAAA8D,EAAA,GAF3ElD,gBAA0B;AAmPhC,eAAeA,gBAAgB;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}