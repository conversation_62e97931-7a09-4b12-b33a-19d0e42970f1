{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/AuditViewer.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { fetchAuditEvents, setQuery } from '../store/slices/auditSlice';\nimport { ShieldCheckIcon, MagnifyingGlassIcon, CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuditViewer = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    events,\n    loading,\n    error,\n    totalCount,\n    query\n  } = useSelector(state => state.audit);\n  const [filters, setFilters] = useState({\n    user_id: '',\n    action: '',\n    resource: '',\n    start_time: '',\n    end_time: ''\n  });\n  useEffect(() => {\n    dispatch(fetchAuditEvents({\n      limit: 50\n    }));\n  }, [dispatch]);\n  const handleSearch = () => {\n    const searchQuery = {\n      ...filters,\n      limit: 50\n    };\n    dispatch(setQuery(searchQuery));\n    dispatch(fetchAuditEvents(searchQuery));\n  };\n  const getActionColor = action => {\n    if (action.includes('create') || action.includes('add')) return 'text-green-400';\n    if (action.includes('delete') || action.includes('remove')) return 'text-red-400';\n    if (action.includes('update') || action.includes('modify')) return 'text-yellow-400';\n    return 'text-blue-400';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold text-white\",\n        children: \"Audit Trail\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-1 text-sm text-gray-400\",\n        children: \"Tamper-proof audit logs powered by ImmuDB\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 shadow rounded-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-4 py-5 sm:p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg leading-6 font-medium text-white mb-4\",\n          children: \"Filters\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 gap-4 sm:grid-cols-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-300\",\n              children: \"User ID\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              className: \"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\",\n              value: filters.user_id,\n              onChange: e => setFilters({\n                ...filters,\n                user_id: e.target.value\n              }),\n              placeholder: \"Filter by user...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-300\",\n              children: \"Action\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              className: \"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\",\n              value: filters.action,\n              onChange: e => setFilters({\n                ...filters,\n                action: e.target.value\n              }),\n              placeholder: \"Filter by action...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-300\",\n              children: \"Resource\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              className: \"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\",\n              value: filters.resource,\n              onChange: e => setFilters({\n                ...filters,\n                resource: e.target.value\n              }),\n              placeholder: \"Filter by resource...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-300\",\n              children: \"Start Date\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"datetime-local\",\n              className: \"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\",\n              value: filters.start_time,\n              onChange: e => setFilters({\n                ...filters,\n                start_time: e.target.value\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-300\",\n              children: \"End Date\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"datetime-local\",\n              className: \"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\",\n              value: filters.end_time,\n              onChange: e => setFilters({\n                ...filters,\n                end_time: e.target.value\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-end\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleSearch,\n              disabled: loading,\n              className: \"w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 disabled:opacity-50\",\n              children: [/*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 17\n              }, this), \"Search\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 shadow rounded-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-4 py-5 sm:p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg leading-6 font-medium text-white\",\n            children: \"Audit Events\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-400\",\n            children: [totalCount, \" events found\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4 bg-red-50 border border-red-200 rounded-md p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-red-700\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 13\n        }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center h-32\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: events.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-12\",\n            children: [/*#__PURE__*/_jsxDEV(ShieldCheckIcon, {\n              className: \"mx-auto h-12 w-12 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"mt-2 text-sm font-medium text-gray-300\",\n              children: \"No audit events found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-gray-400\",\n              children: \"Try adjusting your search criteria.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 17\n          }, this) : events.map(event => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border border-gray-600 rounded-lg p-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [event.success ? /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                    className: \"h-5 w-5 text-green-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 164,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(XCircleIcon, {\n                    className: \"h-5 w-5 text-red-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 166,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `font-medium ${getActionColor(event.action)}`,\n                    children: event.action\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 168,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-400\",\n                    children: \"on\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 171,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white\",\n                    children: event.resource\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 172,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-2 text-sm text-gray-400\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [\"User: \", event.user_id]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 176,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [\"Resource ID: \", event.resource_id]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 177,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [\"IP: \", event.ip_address]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 178,\n                    columnNumber: 27\n                  }, this), event.error_msg && /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-red-400\",\n                    children: [\"Error: \", event.error_msg]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 180,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 25\n                }, this), Object.keys(event.details).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"details\", {\n                    className: \"group\",\n                    children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n                      className: \"cursor-pointer text-sm text-cyan-400 hover:text-cyan-300\",\n                      children: \"View Details\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 187,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n                      className: \"mt-2 text-xs text-gray-400 bg-gray-700 p-2 rounded overflow-x-auto\",\n                      children: JSON.stringify(event.details, null, 2)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 190,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 186,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-right text-sm text-gray-400\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  children: new Date(event.timestamp).toLocaleDateString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: new Date(event.timestamp).toLocaleTimeString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 21\n            }, this)\n          }, event.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 5\n  }, this);\n};\n_s(AuditViewer, \"g46YWMWJU/jW2BUM605iKWD7FzY=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = AuditViewer;\nexport default AuditViewer;\nvar _c;\n$RefreshReg$(_c, \"AuditViewer\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "fetchAuditEvents", "<PERSON><PERSON><PERSON><PERSON>", "ShieldCheckIcon", "MagnifyingGlassIcon", "CheckCircleIcon", "XCircleIcon", "jsxDEV", "_jsxDEV", "AuditViewer", "_s", "dispatch", "events", "loading", "error", "totalCount", "query", "state", "audit", "filters", "setFilters", "user_id", "action", "resource", "start_time", "end_time", "limit", "handleSearch", "searchQuery", "getActionColor", "includes", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "value", "onChange", "e", "target", "placeholder", "onClick", "disabled", "length", "map", "event", "success", "resource_id", "ip_address", "error_msg", "Object", "keys", "details", "JSON", "stringify", "Date", "timestamp", "toLocaleDateString", "toLocaleTimeString", "id", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/AuditViewer.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { RootState, AppDispatch } from '../store/store';\nimport { fetchAuditEvents, setQuery } from '../store/slices/auditSlice';\nimport {\n  ShieldCheckIcon,\n  MagnifyingGlassIcon,\n  CheckCircleIcon,\n  XCircleIcon,\n} from '@heroicons/react/24/outline';\n\nconst AuditViewer: React.FC = () => {\n  const dispatch = useDispatch<AppDispatch>();\n  const { events, loading, error, totalCount, query } = useSelector(\n    (state: RootState) => state.audit\n  );\n\n  const [filters, setFilters] = useState({\n    user_id: '',\n    action: '',\n    resource: '',\n    start_time: '',\n    end_time: '',\n  });\n\n  useEffect(() => {\n    dispatch(fetchAuditEvents({ limit: 50 }));\n  }, [dispatch]);\n\n  const handleSearch = () => {\n    const searchQuery = { ...filters, limit: 50 };\n    dispatch(setQuery(searchQuery));\n    dispatch(fetchAuditEvents(searchQuery));\n  };\n\n  const getActionColor = (action: string) => {\n    if (action.includes('create') || action.includes('add')) return 'text-green-400';\n    if (action.includes('delete') || action.includes('remove')) return 'text-red-400';\n    if (action.includes('update') || action.includes('modify')) return 'text-yellow-400';\n    return 'text-blue-400';\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div>\n        <h1 className=\"text-2xl font-bold text-white\">Audit Trail</h1>\n        <p className=\"mt-1 text-sm text-gray-400\">\n          Tamper-proof audit logs powered by ImmuDB\n        </p>\n      </div>\n\n      {/* Filters */}\n      <div className=\"bg-gray-800 shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <h3 className=\"text-lg leading-6 font-medium text-white mb-4\">Filters</h3>\n          \n          <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-3\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300\">User ID</label>\n              <input\n                type=\"text\"\n                className=\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\"\n                value={filters.user_id}\n                onChange={(e) => setFilters({ ...filters, user_id: e.target.value })}\n                placeholder=\"Filter by user...\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300\">Action</label>\n              <input\n                type=\"text\"\n                className=\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\"\n                value={filters.action}\n                onChange={(e) => setFilters({ ...filters, action: e.target.value })}\n                placeholder=\"Filter by action...\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300\">Resource</label>\n              <input\n                type=\"text\"\n                className=\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\"\n                value={filters.resource}\n                onChange={(e) => setFilters({ ...filters, resource: e.target.value })}\n                placeholder=\"Filter by resource...\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300\">Start Date</label>\n              <input\n                type=\"datetime-local\"\n                className=\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\"\n                value={filters.start_time}\n                onChange={(e) => setFilters({ ...filters, start_time: e.target.value })}\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300\">End Date</label>\n              <input\n                type=\"datetime-local\"\n                className=\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\"\n                value={filters.end_time}\n                onChange={(e) => setFilters({ ...filters, end_time: e.target.value })}\n              />\n            </div>\n\n            <div className=\"flex items-end\">\n              <button\n                onClick={handleSearch}\n                disabled={loading}\n                className=\"w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 disabled:opacity-50\"\n              >\n                <MagnifyingGlassIcon className=\"h-4 w-4 mr-2\" />\n                Search\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Results */}\n      <div className=\"bg-gray-800 shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h3 className=\"text-lg leading-6 font-medium text-white\">Audit Events</h3>\n            <span className=\"text-sm text-gray-400\">{totalCount} events found</span>\n          </div>\n\n          {error && (\n            <div className=\"mb-4 bg-red-50 border border-red-200 rounded-md p-4\">\n              <div className=\"text-sm text-red-700\">{error}</div>\n            </div>\n          )}\n\n          {loading ? (\n            <div className=\"flex items-center justify-center h-32\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500\"></div>\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              {events.length === 0 ? (\n                <div className=\"text-center py-12\">\n                  <ShieldCheckIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n                  <h3 className=\"mt-2 text-sm font-medium text-gray-300\">No audit events found</h3>\n                  <p className=\"mt-1 text-sm text-gray-400\">\n                    Try adjusting your search criteria.\n                  </p>\n                </div>\n              ) : (\n                events.map((event) => (\n                  <div\n                    key={event.id}\n                    className=\"border border-gray-600 rounded-lg p-4\"\n                  >\n                    <div className=\"flex items-start justify-between\">\n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center space-x-2\">\n                          {event.success ? (\n                            <CheckCircleIcon className=\"h-5 w-5 text-green-400\" />\n                          ) : (\n                            <XCircleIcon className=\"h-5 w-5 text-red-400\" />\n                          )}\n                          <span className={`font-medium ${getActionColor(event.action)}`}>\n                            {event.action}\n                          </span>\n                          <span className=\"text-gray-400\">on</span>\n                          <span className=\"text-white\">{event.resource}</span>\n                        </div>\n                        \n                        <div className=\"mt-2 text-sm text-gray-400\">\n                          <p>User: {event.user_id}</p>\n                          <p>Resource ID: {event.resource_id}</p>\n                          <p>IP: {event.ip_address}</p>\n                          {event.error_msg && (\n                            <p className=\"text-red-400\">Error: {event.error_msg}</p>\n                          )}\n                        </div>\n\n                        {Object.keys(event.details).length > 0 && (\n                          <div className=\"mt-3\">\n                            <details className=\"group\">\n                              <summary className=\"cursor-pointer text-sm text-cyan-400 hover:text-cyan-300\">\n                                View Details\n                              </summary>\n                              <pre className=\"mt-2 text-xs text-gray-400 bg-gray-700 p-2 rounded overflow-x-auto\">\n                                {JSON.stringify(event.details, null, 2)}\n                              </pre>\n                            </details>\n                          </div>\n                        )}\n                      </div>\n                      \n                      <div className=\"text-right text-sm text-gray-400\">\n                        <p>{new Date(event.timestamp).toLocaleDateString()}</p>\n                        <p>{new Date(event.timestamp).toLocaleTimeString()}</p>\n                      </div>\n                    </div>\n                  </div>\n                ))\n              )}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AuditViewer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,SAASC,gBAAgB,EAAEC,QAAQ,QAAQ,4BAA4B;AACvE,SACEC,eAAe,EACfC,mBAAmB,EACnBC,eAAe,EACfC,WAAW,QACN,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAMC,QAAQ,GAAGZ,WAAW,CAAc,CAAC;EAC3C,MAAM;IAAEa,MAAM;IAAEC,OAAO;IAAEC,KAAK;IAAEC,UAAU;IAAEC;EAAM,CAAC,GAAGhB,WAAW,CAC9DiB,KAAgB,IAAKA,KAAK,CAACC,KAC9B,CAAC;EAED,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC;IACrCuB,OAAO,EAAE,EAAE;IACXC,MAAM,EAAE,EAAE;IACVC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF5B,SAAS,CAAC,MAAM;IACdc,QAAQ,CAACV,gBAAgB,CAAC;MAAEyB,KAAK,EAAE;IAAG,CAAC,CAAC,CAAC;EAC3C,CAAC,EAAE,CAACf,QAAQ,CAAC,CAAC;EAEd,MAAMgB,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,WAAW,GAAG;MAAE,GAAGT,OAAO;MAAEO,KAAK,EAAE;IAAG,CAAC;IAC7Cf,QAAQ,CAACT,QAAQ,CAAC0B,WAAW,CAAC,CAAC;IAC/BjB,QAAQ,CAACV,gBAAgB,CAAC2B,WAAW,CAAC,CAAC;EACzC,CAAC;EAED,MAAMC,cAAc,GAAIP,MAAc,IAAK;IACzC,IAAIA,MAAM,CAACQ,QAAQ,CAAC,QAAQ,CAAC,IAAIR,MAAM,CAACQ,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,gBAAgB;IAChF,IAAIR,MAAM,CAACQ,QAAQ,CAAC,QAAQ,CAAC,IAAIR,MAAM,CAACQ,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,cAAc;IACjF,IAAIR,MAAM,CAACQ,QAAQ,CAAC,QAAQ,CAAC,IAAIR,MAAM,CAACQ,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,iBAAiB;IACpF,OAAO,eAAe;EACxB,CAAC;EAED,oBACEtB,OAAA;IAAKuB,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBxB,OAAA;MAAAwB,QAAA,gBACExB,OAAA;QAAIuB,SAAS,EAAC,+BAA+B;QAAAC,QAAA,EAAC;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9D5B,OAAA;QAAGuB,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAE1C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGN5B,OAAA;MAAKuB,SAAS,EAAC,+BAA+B;MAAAC,QAAA,eAC5CxB,OAAA;QAAKuB,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BxB,OAAA;UAAIuB,SAAS,EAAC,+CAA+C;UAAAC,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAE1E5B,OAAA;UAAKuB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDxB,OAAA;YAAAwB,QAAA,gBACExB,OAAA;cAAOuB,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1E5B,OAAA;cACE6B,IAAI,EAAC,MAAM;cACXN,SAAS,EAAC,0IAA0I;cACpJO,KAAK,EAAEnB,OAAO,CAACE,OAAQ;cACvBkB,QAAQ,EAAGC,CAAC,IAAKpB,UAAU,CAAC;gBAAE,GAAGD,OAAO;gBAAEE,OAAO,EAAEmB,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cACrEI,WAAW,EAAC;YAAmB;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN5B,OAAA;YAAAwB,QAAA,gBACExB,OAAA;cAAOuB,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzE5B,OAAA;cACE6B,IAAI,EAAC,MAAM;cACXN,SAAS,EAAC,0IAA0I;cACpJO,KAAK,EAAEnB,OAAO,CAACG,MAAO;cACtBiB,QAAQ,EAAGC,CAAC,IAAKpB,UAAU,CAAC;gBAAE,GAAGD,OAAO;gBAAEG,MAAM,EAAEkB,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cACpEI,WAAW,EAAC;YAAqB;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN5B,OAAA;YAAAwB,QAAA,gBACExB,OAAA;cAAOuB,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3E5B,OAAA;cACE6B,IAAI,EAAC,MAAM;cACXN,SAAS,EAAC,0IAA0I;cACpJO,KAAK,EAAEnB,OAAO,CAACI,QAAS;cACxBgB,QAAQ,EAAGC,CAAC,IAAKpB,UAAU,CAAC;gBAAE,GAAGD,OAAO;gBAAEI,QAAQ,EAAEiB,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cACtEI,WAAW,EAAC;YAAuB;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN5B,OAAA;YAAAwB,QAAA,gBACExB,OAAA;cAAOuB,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7E5B,OAAA;cACE6B,IAAI,EAAC,gBAAgB;cACrBN,SAAS,EAAC,0IAA0I;cACpJO,KAAK,EAAEnB,OAAO,CAACK,UAAW;cAC1Be,QAAQ,EAAGC,CAAC,IAAKpB,UAAU,CAAC;gBAAE,GAAGD,OAAO;gBAAEK,UAAU,EAAEgB,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN5B,OAAA;YAAAwB,QAAA,gBACExB,OAAA;cAAOuB,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3E5B,OAAA;cACE6B,IAAI,EAAC,gBAAgB;cACrBN,SAAS,EAAC,0IAA0I;cACpJO,KAAK,EAAEnB,OAAO,CAACM,QAAS;cACxBc,QAAQ,EAAGC,CAAC,IAAKpB,UAAU,CAAC;gBAAE,GAAGD,OAAO;gBAAEM,QAAQ,EAAEe,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN5B,OAAA;YAAKuB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7BxB,OAAA;cACEmC,OAAO,EAAEhB,YAAa;cACtBiB,QAAQ,EAAE/B,OAAQ;cAClBkB,SAAS,EAAC,2PAA2P;cAAAC,QAAA,gBAErQxB,OAAA,CAACJ,mBAAmB;gBAAC2B,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,UAElD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5B,OAAA;MAAKuB,SAAS,EAAC,+BAA+B;MAAAC,QAAA,eAC5CxB,OAAA;QAAKuB,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BxB,OAAA;UAAKuB,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDxB,OAAA;YAAIuB,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1E5B,OAAA;YAAMuB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAEjB,UAAU,EAAC,eAAa;UAAA;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC,EAELtB,KAAK,iBACJN,OAAA;UAAKuB,SAAS,EAAC,qDAAqD;UAAAC,QAAA,eAClExB,OAAA;YAAKuB,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAElB;UAAK;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CACN,EAEAvB,OAAO,gBACNL,OAAA;UAAKuB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,eACpDxB,OAAA;YAAKuB,SAAS,EAAC;UAA8D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CAAC,gBAEN5B,OAAA;UAAKuB,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBpB,MAAM,CAACiC,MAAM,KAAK,CAAC,gBAClBrC,OAAA;YAAKuB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCxB,OAAA,CAACL,eAAe;cAAC4B,SAAS,EAAC;YAAiC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/D5B,OAAA;cAAIuB,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjF5B,OAAA;cAAGuB,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE1C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,GAENxB,MAAM,CAACkC,GAAG,CAAEC,KAAK,iBACfvC,OAAA;YAEEuB,SAAS,EAAC,uCAAuC;YAAAC,QAAA,eAEjDxB,OAAA;cAAKuB,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/CxB,OAAA;gBAAKuB,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrBxB,OAAA;kBAAKuB,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,GACzCe,KAAK,CAACC,OAAO,gBACZxC,OAAA,CAACH,eAAe;oBAAC0B,SAAS,EAAC;kBAAwB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAEtD5B,OAAA,CAACF,WAAW;oBAACyB,SAAS,EAAC;kBAAsB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAChD,eACD5B,OAAA;oBAAMuB,SAAS,EAAE,eAAeF,cAAc,CAACkB,KAAK,CAACzB,MAAM,CAAC,EAAG;oBAAAU,QAAA,EAC5De,KAAK,CAACzB;kBAAM;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACP5B,OAAA;oBAAMuB,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzC5B,OAAA;oBAAMuB,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAEe,KAAK,CAACxB;kBAAQ;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC,eAEN5B,OAAA;kBAAKuB,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzCxB,OAAA;oBAAAwB,QAAA,GAAG,QAAM,EAACe,KAAK,CAAC1B,OAAO;kBAAA;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5B5B,OAAA;oBAAAwB,QAAA,GAAG,eAAa,EAACe,KAAK,CAACE,WAAW;kBAAA;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvC5B,OAAA;oBAAAwB,QAAA,GAAG,MAAI,EAACe,KAAK,CAACG,UAAU;kBAAA;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAC5BW,KAAK,CAACI,SAAS,iBACd3C,OAAA;oBAAGuB,SAAS,EAAC,cAAc;oBAAAC,QAAA,GAAC,SAAO,EAACe,KAAK,CAACI,SAAS;kBAAA;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CACxD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,EAELgB,MAAM,CAACC,IAAI,CAACN,KAAK,CAACO,OAAO,CAAC,CAACT,MAAM,GAAG,CAAC,iBACpCrC,OAAA;kBAAKuB,SAAS,EAAC,MAAM;kBAAAC,QAAA,eACnBxB,OAAA;oBAASuB,SAAS,EAAC,OAAO;oBAAAC,QAAA,gBACxBxB,OAAA;sBAASuB,SAAS,EAAC,0DAA0D;sBAAAC,QAAA,EAAC;oBAE9E;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC,eACV5B,OAAA;sBAAKuB,SAAS,EAAC,oEAAoE;sBAAAC,QAAA,EAChFuB,IAAI,CAACC,SAAS,CAACT,KAAK,CAACO,OAAO,EAAE,IAAI,EAAE,CAAC;oBAAC;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN5B,OAAA;gBAAKuB,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/CxB,OAAA;kBAAAwB,QAAA,EAAI,IAAIyB,IAAI,CAACV,KAAK,CAACW,SAAS,CAAC,CAACC,kBAAkB,CAAC;gBAAC;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvD5B,OAAA;kBAAAwB,QAAA,EAAI,IAAIyB,IAAI,CAACV,KAAK,CAACW,SAAS,CAAC,CAACE,kBAAkB,CAAC;gBAAC;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GA7CDW,KAAK,CAACc,EAAE;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA8CV,CACN;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1B,EAAA,CAxMID,WAAqB;EAAA,QACRV,WAAW,EAC0BC,WAAW;AAAA;AAAA8D,EAAA,GAF7DrD,WAAqB;AA0M3B,eAAeA,WAAW;AAAC,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}