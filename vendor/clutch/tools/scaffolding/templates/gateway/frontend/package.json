{"name": "@{{ .RepoName }}/frontend", "private": true, "version": "0.0.0", "scripts": {"build": "yarn compile && yarn register-workflows && react-scripts build", "clean": "rm -rf build", "compile": "yarn workspaces foreach -Ai run compile", "compile:watch": "yarn workspaces foreach -Aip -j unlimited run compile:watch", "eject": "react-scripts eject", "lint": "eslint --ext .js,.jsx,.ts,.tsx .", "lint:fix": "yarn run lint --fix", "register-workflows": "npm explore @clutch-sh/tools -- yarn registerWorkflows $PWD/src", "start": "yarn clean && yarn compile:watch & yarn register-workflows && DANGEROUSLY_DISABLE_HOST_CHECK=true FORCE_COLOR=true react-scripts start | cat", "test": "yarn workspaces foreach -Ap -j unlimited run test --silent", "test:coverage": "yarn workspaces foreach -Ap -j unlimited run test:coverage --silent", "test:watch": "yarn workspaces foreach -Aip -j unlimited run test:watch", "upgrade": "yarn upgrade"}, "dependencies": {"protobufjs": "6.11.3"}, "devDependencies": {"@clutch-sh/ec2": "^4.0.0-beta", "@{{ .RepoName }}/echo": "^0.0.0", "@clutch-sh/core": "^4.0.0-beta", "@clutch-sh/tools": "^4.0.0-beta", "history": "^5.0.0", "react": "^17.0.2", "react-dom": "^17.0.2", "react-scripts": "^5.0.1"}, "resolutions": {"@types/react": "17.0.50", "react-hook-form": "7.25.3"}, "workspaces": ["workflows/*"], "proxy": "http://localhost:8080", "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"node": ">=18 <19", "yarn": "^4.5.0"}, "packageManager": "yarn@4.5.0"}