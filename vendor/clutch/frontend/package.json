{"name": "@clutch-sh/clutch", "version": "4.0.0-beta", "private": true, "description": "Interface to handle ad-hoc infrastructure modifications", "keywords": ["lyft", "clutch", "infrastructure", "infra"], "homepage": "https://clutch.sh", "bugs": {"url": "https://github.com/lyft/clutch/issues"}, "repository": {"type": "git", "url": "git+https://github.com/lyft/clutch.git"}, "license": "Apache-2.0", "author": "<EMAIL>", "workspaces": ["api", "{packages,workflows}/*"], "scripts": {"build": "yarn compile && yarn workspace @clutch-sh/app build", "build:dev": "yarn compile:dev && yarn run register-workflows", "buildStart": "DANGEROUSLY_DISABLE_HOST_CHECK=true FORCE_COLOR=true yarn workspace @clutch-sh/app run start | cat", "buildWatch": "WORKSPACES=\"$(yarn workspaces list --no-private --json)\" yarn run workspace:compile:watch compile:watch buildStart $INIT_CWD", "clean": "yarn workspaces foreach -Aip -j unlimited run clean", "compile": "yarn clean && yarn workspaces foreach -Ai run compile", "compile:dev": "yarn workspaces foreach -Ai run compile:dev", "compile:watch": "yarn workspaces foreach -Aip -j unlimited run compile:watch", "lint": "yarn workspaces foreach -<PERSON>p run lint", "lint:files": "eslint --ext .js,.jsx,.ts,.tsx", "lint:fix": "yarn run lint --fix", "lint:packages": "sort-package-json package.json api/package.json {packages,workflows}/*/package.json --check", "lint:packages:fix": "sort-package-json package.json api/package.json {packages,workflows}/*/package.json", "nuke": "PROJECT_CWD=$PWD npm run --silent node_modules:nuke --workspace=@clutch-sh/tools", "package": "func() { yarn workspace @clutch-sh/\"$@\"; }; func", "publishBeta": "yarn compile && yarn workspaces foreach --no-private -Ai run publishBeta", "register-workflows": "yarn workspace @clutch-sh/app run register-workflows", "start": "yarn run clean && yarn run register-workflows && yarn run buildWatch", "storybook": "rm -rf node_modules/.cache/storybook/ && storybook dev --disable-telemetry -p 6006 -h localhost", "storybook:build": "NODE_OPTIONS=--max_old_space_size=4096 storybook build -o netlify/storybook-static", "test": "yarn workspaces foreach -<PERSON>p run test --silent", "test:coverage": "yarn workspaces foreach -Aip run test:coverage --silent", "test:e2e": "yarn workspaces foreach -Aip run test:e2e", "test:licenses": "yarn node license-linter.js", "test:update": "yarn run test -u", "test:watch": "yarn workspaces foreach -Aip -j unlimited run test:watch"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "resolutions": {"@date-io/core": "2.14.0", "@emotion/react": "11.0.0", "@emotion/styled": "11.0.0", "@mui/icons-material": "5.8.4", "@mui/lab": "5.0.0-alpha.87", "@mui/material": "5.8.5", "@mui/styles": "5.8.4", "@mui/system": "5.8.5", "@mui/x-date-pickers": "5.0.5", "@types/enzyme": "3.10.8", "@types/jest": "29.1.2", "@types/mocha": "10.0.0", "@types/node": "18.19.23", "@types/react": "17.0.50", "@types/react-dom": "17.0.17", "csstype": "3.0.3", "esbuild": "0.18.13", "eslint": "8.16.0", "jest": "27.5.1", "react": "17.0.2", "react-dom": "17.0.2", "react-router": "6.0.0", "react-router-dom": "6.0.0", "recharts": "2.1.9", "typescript": "4.2.3"}, "dependencies": {"eslint": "^8.3.0", "jest": "^27.0.0", "react": "^17.0.2", "react-dom": "^17.0.2", "tsc-alias": "^1.8.8", "typescript": "^4.2.3"}, "devDependencies": {"@storybook/addon-a11y": "^8.3.3", "@storybook/addon-actions": "^8.3.3", "@storybook/addon-essentials": "^8.3.3", "@storybook/addon-links": "^8.3.3", "@storybook/addon-themes": "^8.3.3", "@storybook/manager-api": "^8.3.3", "@storybook/node-logger": "^8.3.3", "@storybook/preset-create-react-app": "^8.3.3", "@storybook/react-webpack5": "^8.3.3", "@storybook/theming": "^8.3.3", "@types/eslint": "^8", "jest": "^27.0.0", "license-checker": "^25.0.1", "pre-commit": "^1.2.2", "react": "^17.0.2", "react-dom": "^17.0.2", "sort-package-json": "^1.48.1", "storybook": "^8.3.3", "typescript": "^4.2.3"}, "engines": {"node": ">=18 <19", "yarn": "^4.5.0"}, "packageManager": "yarn@4.5.0", "stableVersion": "3.0.0-beta"}