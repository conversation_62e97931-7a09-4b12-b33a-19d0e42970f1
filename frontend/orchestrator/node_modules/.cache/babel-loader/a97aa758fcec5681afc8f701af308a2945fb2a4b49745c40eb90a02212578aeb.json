{"ast": null, "code": "import _objectSpread from\"/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import{useAuth}from'../auth/AuthContext';import{CogIcon,ShieldCheckIcon,ServerIcon,BellIcon,KeyIcon,ExclamationTriangleIcon,CheckCircleIcon}from'@heroicons/react/24/outline';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Settings=()=>{const{user}=useAuth();const[activeTab,setActiveTab]=useState('authentication');const[settings,setSettings]=useState({authentication:{sessionTimeout:24,maxLoginAttempts:5,passwordMinLength:4,requireMFA:false},security:{enableAuditLogging:true,enableRateLimiting:true,allowedOrigins:['http://localhost:3000','http://localhost:8080'],encryptionEnabled:true},notifications:{emailEnabled:false,slackEnabled:false,webhookUrl:''},system:{logLevel:'info',maxConnections:1000,backupEnabled:true,backupInterval:'daily'}});const[loading,setLoading]=useState(false);const[message,setMessage]=useState(null);useEffect(()=>{fetchSettings();},[]);const fetchSettings=async()=>{try{const response=await fetch('/v1/admin/settings',{credentials:'include'});if(response.ok){const data=await response.json();setSettings(data.settings||settings);}}catch(error){console.error('Failed to fetch settings:',error);}};const handleSaveSettings=async()=>{setLoading(true);try{const response=await fetch('/v1/admin/settings',{method:'PUT',headers:{'Content-Type':'application/json'},credentials:'include',body:JSON.stringify({settings})});if(response.ok){setMessage({type:'success',text:'Settings saved successfully'});}else{setMessage({type:'error',text:'Failed to save settings'});}}catch(error){setMessage({type:'error',text:'Failed to save settings'});}finally{setLoading(false);}};const tabs=[{id:'authentication',name:'Authentication',icon:ShieldCheckIcon},{id:'security',name:'Security',icon:KeyIcon},{id:'notifications',name:'Notifications',icon:BellIcon},{id:'system',name:'System',icon:ServerIcon}];return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white shadow rounded-lg p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-900\",children:\"System Settings\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Configure system-wide settings and preferences\"})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:handleSaveSettings,disabled:loading,className:\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50\",children:[/*#__PURE__*/_jsx(CogIcon,{className:\"h-4 w-4 mr-2\"}),loading?'Saving...':'Save Settings']})]})}),message&&/*#__PURE__*/_jsx(\"div\",{className:\"p-4 rounded-md \".concat(message.type==='success'?'bg-green-50 text-green-800':'bg-red-50 text-red-800'),children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex\",children:[message.type==='success'?/*#__PURE__*/_jsx(CheckCircleIcon,{className:\"h-5 w-5 mr-2\"}):/*#__PURE__*/_jsx(ExclamationTriangleIcon,{className:\"h-5 w-5 mr-2\"}),message.text]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white shadow rounded-lg\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"border-b border-gray-200\",children:/*#__PURE__*/_jsx(\"nav\",{className:\"-mb-px flex space-x-8 px-6\",children:tabs.map(tab=>{const Icon=tab.icon;return/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setActiveTab(tab.id),className:\"py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 \".concat(activeTab===tab.id?'border-blue-500 text-blue-600':'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),children:[/*#__PURE__*/_jsx(Icon,{className:\"h-4 w-4\"}),/*#__PURE__*/_jsx(\"span\",{children:tab.name})]},tab.id);})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-6\",children:[activeTab==='authentication'&&/*#__PURE__*/_jsx(\"div\",{className:\"space-y-6\",children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-4\",children:\"Authentication Settings\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-6\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Session Timeout (hours)\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",value:settings.authentication.sessionTimeout,onChange:e=>setSettings(_objectSpread(_objectSpread({},settings),{},{authentication:_objectSpread(_objectSpread({},settings.authentication),{},{sessionTimeout:parseInt(e.target.value)})})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Max Login Attempts\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",value:settings.authentication.maxLoginAttempts,onChange:e=>setSettings(_objectSpread(_objectSpread({},settings),{},{authentication:_objectSpread(_objectSpread({},settings.authentication),{},{maxLoginAttempts:parseInt(e.target.value)})})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Password Min Length\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",value:settings.authentication.passwordMinLength,onChange:e=>setSettings(_objectSpread(_objectSpread({},settings),{},{authentication:_objectSpread(_objectSpread({},settings.authentication),{},{passwordMinLength:parseInt(e.target.value)})})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:settings.authentication.requireMFA,onChange:e=>setSettings(_objectSpread(_objectSpread({},settings),{},{authentication:_objectSpread(_objectSpread({},settings.authentication),{},{requireMFA:e.target.checked})})),className:\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"}),/*#__PURE__*/_jsx(\"label\",{className:\"ml-2 block text-sm text-gray-900\",children:\"Require Multi-Factor Authentication\"})]})]})]})}),activeTab==='security'&&/*#__PURE__*/_jsx(\"div\",{className:\"space-y-6\",children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-4\",children:\"Security Settings\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:settings.security.enableAuditLogging,onChange:e=>setSettings(_objectSpread(_objectSpread({},settings),{},{security:_objectSpread(_objectSpread({},settings.security),{},{enableAuditLogging:e.target.checked})})),className:\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"}),/*#__PURE__*/_jsx(\"label\",{className:\"ml-2 block text-sm text-gray-900\",children:\"Enable Audit Logging\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:settings.security.enableRateLimiting,onChange:e=>setSettings(_objectSpread(_objectSpread({},settings),{},{security:_objectSpread(_objectSpread({},settings.security),{},{enableRateLimiting:e.target.checked})})),className:\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"}),/*#__PURE__*/_jsx(\"label\",{className:\"ml-2 block text-sm text-gray-900\",children:\"Enable Rate Limiting\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:settings.security.encryptionEnabled,onChange:e=>setSettings(_objectSpread(_objectSpread({},settings),{},{security:_objectSpread(_objectSpread({},settings.security),{},{encryptionEnabled:e.target.checked})})),className:\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"}),/*#__PURE__*/_jsx(\"label\",{className:\"ml-2 block text-sm text-gray-900\",children:\"Enable Data Encryption\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Allowed Origins (one per line)\"}),/*#__PURE__*/_jsx(\"textarea\",{value:settings.security.allowedOrigins.join('\\n'),onChange:e=>setSettings(_objectSpread(_objectSpread({},settings),{},{security:_objectSpread(_objectSpread({},settings.security),{},{allowedOrigins:e.target.value.split('\\n').filter(origin=>origin.trim())})})),rows:4,className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500\"})]})]})]})}),activeTab==='notifications'&&/*#__PURE__*/_jsx(\"div\",{className:\"space-y-6\",children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-4\",children:\"Notification Settings\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:settings.notifications.emailEnabled,onChange:e=>setSettings(_objectSpread(_objectSpread({},settings),{},{notifications:_objectSpread(_objectSpread({},settings.notifications),{},{emailEnabled:e.target.checked})})),className:\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"}),/*#__PURE__*/_jsx(\"label\",{className:\"ml-2 block text-sm text-gray-900\",children:\"Enable Email Notifications\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:settings.notifications.slackEnabled,onChange:e=>setSettings(_objectSpread(_objectSpread({},settings),{},{notifications:_objectSpread(_objectSpread({},settings.notifications),{},{slackEnabled:e.target.checked})})),className:\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"}),/*#__PURE__*/_jsx(\"label\",{className:\"ml-2 block text-sm text-gray-900\",children:\"Enable Slack Notifications\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Webhook URL\"}),/*#__PURE__*/_jsx(\"input\",{type:\"url\",value:settings.notifications.webhookUrl,onChange:e=>setSettings(_objectSpread(_objectSpread({},settings),{},{notifications:_objectSpread(_objectSpread({},settings.notifications),{},{webhookUrl:e.target.value})})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500\",placeholder:\"https://hooks.slack.com/services/...\"})]})]})]})}),activeTab==='system'&&/*#__PURE__*/_jsx(\"div\",{className:\"space-y-6\",children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-4\",children:\"System Settings\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-6\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Log Level\"}),/*#__PURE__*/_jsxs(\"select\",{value:settings.system.logLevel,onChange:e=>setSettings(_objectSpread(_objectSpread({},settings),{},{system:_objectSpread(_objectSpread({},settings.system),{},{logLevel:e.target.value})})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"debug\",children:\"Debug\"}),/*#__PURE__*/_jsx(\"option\",{value:\"info\",children:\"Info\"}),/*#__PURE__*/_jsx(\"option\",{value:\"warn\",children:\"Warning\"}),/*#__PURE__*/_jsx(\"option\",{value:\"error\",children:\"Error\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Max Connections\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",value:settings.system.maxConnections,onChange:e=>setSettings(_objectSpread(_objectSpread({},settings),{},{system:_objectSpread(_objectSpread({},settings.system),{},{maxConnections:parseInt(e.target.value)})})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Backup Interval\"}),/*#__PURE__*/_jsxs(\"select\",{value:settings.system.backupInterval,onChange:e=>setSettings(_objectSpread(_objectSpread({},settings),{},{system:_objectSpread(_objectSpread({},settings.system),{},{backupInterval:e.target.value})})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"hourly\",children:\"Hourly\"}),/*#__PURE__*/_jsx(\"option\",{value:\"daily\",children:\"Daily\"}),/*#__PURE__*/_jsx(\"option\",{value:\"weekly\",children:\"Weekly\"}),/*#__PURE__*/_jsx(\"option\",{value:\"monthly\",children:\"Monthly\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:settings.system.backupEnabled,onChange:e=>setSettings(_objectSpread(_objectSpread({},settings),{},{system:_objectSpread(_objectSpread({},settings.system),{},{backupEnabled:e.target.checked})})),className:\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"}),/*#__PURE__*/_jsx(\"label\",{className:\"ml-2 block text-sm text-gray-900\",children:\"Enable Automatic Backups\"})]})]})]})})]})]})]});};export default Settings;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "CogIcon", "ShieldCheckIcon", "ServerIcon", "BellIcon", "KeyIcon", "ExclamationTriangleIcon", "CheckCircleIcon", "jsx", "_jsx", "jsxs", "_jsxs", "Settings", "user", "activeTab", "setActiveTab", "settings", "setSettings", "authentication", "sessionTimeout", "maxLogin<PERSON><PERSON><PERSON>s", "<PERSON><PERSON>in<PERSON><PERSON><PERSON>", "requireMFA", "security", "enableAuditLogging", "enableRateLimiting", "<PERSON><PERSON><PERSON><PERSON>", "encryptionEnabled", "notifications", "emailEnabled", "slackEnabled", "webhookUrl", "system", "logLevel", "maxConnections", "backup<PERSON><PERSON>bled", "backupInterval", "loading", "setLoading", "message", "setMessage", "fetchSettings", "response", "fetch", "credentials", "ok", "data", "json", "error", "console", "handleSaveSettings", "method", "headers", "body", "JSON", "stringify", "type", "text", "tabs", "id", "name", "icon", "className", "children", "onClick", "disabled", "concat", "map", "tab", "Icon", "value", "onChange", "e", "_objectSpread", "parseInt", "target", "checked", "join", "split", "filter", "origin", "trim", "rows", "placeholder"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/Settings.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../auth/AuthContext';\nimport {\n  CogIcon,\n  ShieldCheckIcon,\n  ServerIcon,\n  BellIcon,\n  CircleStackIcon,\n  CloudIcon,\n  KeyIcon,\n  ExclamationTriangleIcon,\n  CheckCircleIcon,\n} from '@heroicons/react/24/outline';\n\ninterface SystemSettings {\n  authentication: {\n    sessionTimeout: number;\n    maxLoginAttempts: number;\n    passwordMinLength: number;\n    requireMFA: boolean;\n  };\n  security: {\n    enableAuditLogging: boolean;\n    enableRateLimiting: boolean;\n    allowedOrigins: string[];\n    encryptionEnabled: boolean;\n  };\n  notifications: {\n    emailEnabled: boolean;\n    slackEnabled: boolean;\n    webhookUrl: string;\n  };\n  system: {\n    logLevel: string;\n    maxConnections: number;\n    backupEnabled: boolean;\n    backupInterval: string;\n  };\n}\n\nconst Settings: React.FC = () => {\n  const { user } = useAuth();\n  const [activeTab, setActiveTab] = useState('authentication');\n  const [settings, setSettings] = useState<SystemSettings>({\n    authentication: {\n      sessionTimeout: 24,\n      maxLoginAttempts: 5,\n      passwordMinLength: 4,\n      requireMFA: false,\n    },\n    security: {\n      enableAuditLogging: true,\n      enableRateLimiting: true,\n      allowedOrigins: ['http://localhost:3000', 'http://localhost:8080'],\n      encryptionEnabled: true,\n    },\n    notifications: {\n      emailEnabled: false,\n      slackEnabled: false,\n      webhookUrl: '',\n    },\n    system: {\n      logLevel: 'info',\n      maxConnections: 1000,\n      backupEnabled: true,\n      backupInterval: 'daily',\n    },\n  });\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);\n\n  useEffect(() => {\n    fetchSettings();\n  }, []);\n\n  const fetchSettings = async () => {\n    try {\n      const response = await fetch('/v1/admin/settings', {\n        credentials: 'include',\n      });\n      \n      if (response.ok) {\n        const data = await response.json();\n        setSettings(data.settings || settings);\n      }\n    } catch (error) {\n      console.error('Failed to fetch settings:', error);\n    }\n  };\n\n  const handleSaveSettings = async () => {\n    setLoading(true);\n    try {\n      const response = await fetch('/v1/admin/settings', {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include',\n        body: JSON.stringify({ settings }),\n      });\n\n      if (response.ok) {\n        setMessage({ type: 'success', text: 'Settings saved successfully' });\n      } else {\n        setMessage({ type: 'error', text: 'Failed to save settings' });\n      }\n    } catch (error) {\n      setMessage({ type: 'error', text: 'Failed to save settings' });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const tabs = [\n    { id: 'authentication', name: 'Authentication', icon: ShieldCheckIcon },\n    { id: 'security', name: 'Security', icon: KeyIcon },\n    { id: 'notifications', name: 'Notifications', icon: BellIcon },\n    { id: 'system', name: 'System', icon: ServerIcon },\n  ];\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">System Settings</h1>\n            <p className=\"text-gray-600\">Configure system-wide settings and preferences</p>\n          </div>\n          <button\n            onClick={handleSaveSettings}\n            disabled={loading}\n            className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50\"\n          >\n            <CogIcon className=\"h-4 w-4 mr-2\" />\n            {loading ? 'Saving...' : 'Save Settings'}\n          </button>\n        </div>\n      </div>\n\n      {/* Message */}\n      {message && (\n        <div className={`p-4 rounded-md ${\n          message.type === 'success' ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'\n        }`}>\n          <div className=\"flex\">\n            {message.type === 'success' ? (\n              <CheckCircleIcon className=\"h-5 w-5 mr-2\" />\n            ) : (\n              <ExclamationTriangleIcon className=\"h-5 w-5 mr-2\" />\n            )}\n            {message.text}\n          </div>\n        </div>\n      )}\n\n      {/* Settings Tabs */}\n      <div className=\"bg-white shadow rounded-lg\">\n        <div className=\"border-b border-gray-200\">\n          <nav className=\"-mb-px flex space-x-8 px-6\">\n            {tabs.map((tab) => {\n              const Icon = tab.icon;\n              return (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id)}\n                  className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${\n                    activeTab === tab.id\n                      ? 'border-blue-500 text-blue-600'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }`}\n                >\n                  <Icon className=\"h-4 w-4\" />\n                  <span>{tab.name}</span>\n                </button>\n              );\n            })}\n          </nav>\n        </div>\n\n        <div className=\"p-6\">\n          {/* Authentication Tab */}\n          {activeTab === 'authentication' && (\n            <div className=\"space-y-6\">\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Authentication Settings</h3>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Session Timeout (hours)</label>\n                    <input\n                      type=\"number\"\n                      value={settings.authentication.sessionTimeout}\n                      onChange={(e) => setSettings({\n                        ...settings,\n                        authentication: {\n                          ...settings.authentication,\n                          sessionTimeout: parseInt(e.target.value)\n                        }\n                      })}\n                      className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Max Login Attempts</label>\n                    <input\n                      type=\"number\"\n                      value={settings.authentication.maxLoginAttempts}\n                      onChange={(e) => setSettings({\n                        ...settings,\n                        authentication: {\n                          ...settings.authentication,\n                          maxLoginAttempts: parseInt(e.target.value)\n                        }\n                      })}\n                      className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Password Min Length</label>\n                    <input\n                      type=\"number\"\n                      value={settings.authentication.passwordMinLength}\n                      onChange={(e) => setSettings({\n                        ...settings,\n                        authentication: {\n                          ...settings.authentication,\n                          passwordMinLength: parseInt(e.target.value)\n                        }\n                      })}\n                      className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                    />\n                  </div>\n                  <div className=\"flex items-center\">\n                    <input\n                      type=\"checkbox\"\n                      checked={settings.authentication.requireMFA}\n                      onChange={(e) => setSettings({\n                        ...settings,\n                        authentication: {\n                          ...settings.authentication,\n                          requireMFA: e.target.checked\n                        }\n                      })}\n                      className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                    />\n                    <label className=\"ml-2 block text-sm text-gray-900\">Require Multi-Factor Authentication</label>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Security Tab */}\n          {activeTab === 'security' && (\n            <div className=\"space-y-6\">\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Security Settings</h3>\n                <div className=\"space-y-4\">\n                  <div className=\"flex items-center\">\n                    <input\n                      type=\"checkbox\"\n                      checked={settings.security.enableAuditLogging}\n                      onChange={(e) => setSettings({\n                        ...settings,\n                        security: {\n                          ...settings.security,\n                          enableAuditLogging: e.target.checked\n                        }\n                      })}\n                      className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                    />\n                    <label className=\"ml-2 block text-sm text-gray-900\">Enable Audit Logging</label>\n                  </div>\n                  <div className=\"flex items-center\">\n                    <input\n                      type=\"checkbox\"\n                      checked={settings.security.enableRateLimiting}\n                      onChange={(e) => setSettings({\n                        ...settings,\n                        security: {\n                          ...settings.security,\n                          enableRateLimiting: e.target.checked\n                        }\n                      })}\n                      className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                    />\n                    <label className=\"ml-2 block text-sm text-gray-900\">Enable Rate Limiting</label>\n                  </div>\n                  <div className=\"flex items-center\">\n                    <input\n                      type=\"checkbox\"\n                      checked={settings.security.encryptionEnabled}\n                      onChange={(e) => setSettings({\n                        ...settings,\n                        security: {\n                          ...settings.security,\n                          encryptionEnabled: e.target.checked\n                        }\n                      })}\n                      className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                    />\n                    <label className=\"ml-2 block text-sm text-gray-900\">Enable Data Encryption</label>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Allowed Origins (one per line)</label>\n                    <textarea\n                      value={settings.security.allowedOrigins.join('\\n')}\n                      onChange={(e) => setSettings({\n                        ...settings,\n                        security: {\n                          ...settings.security,\n                          allowedOrigins: e.target.value.split('\\n').filter(origin => origin.trim())\n                        }\n                      })}\n                      rows={4}\n                      className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                    />\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Notifications Tab */}\n          {activeTab === 'notifications' && (\n            <div className=\"space-y-6\">\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Notification Settings</h3>\n                <div className=\"space-y-4\">\n                  <div className=\"flex items-center\">\n                    <input\n                      type=\"checkbox\"\n                      checked={settings.notifications.emailEnabled}\n                      onChange={(e) => setSettings({\n                        ...settings,\n                        notifications: {\n                          ...settings.notifications,\n                          emailEnabled: e.target.checked\n                        }\n                      })}\n                      className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                    />\n                    <label className=\"ml-2 block text-sm text-gray-900\">Enable Email Notifications</label>\n                  </div>\n                  <div className=\"flex items-center\">\n                    <input\n                      type=\"checkbox\"\n                      checked={settings.notifications.slackEnabled}\n                      onChange={(e) => setSettings({\n                        ...settings,\n                        notifications: {\n                          ...settings.notifications,\n                          slackEnabled: e.target.checked\n                        }\n                      })}\n                      className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                    />\n                    <label className=\"ml-2 block text-sm text-gray-900\">Enable Slack Notifications</label>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Webhook URL</label>\n                    <input\n                      type=\"url\"\n                      value={settings.notifications.webhookUrl}\n                      onChange={(e) => setSettings({\n                        ...settings,\n                        notifications: {\n                          ...settings.notifications,\n                          webhookUrl: e.target.value\n                        }\n                      })}\n                      className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                      placeholder=\"https://hooks.slack.com/services/...\"\n                    />\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* System Tab */}\n          {activeTab === 'system' && (\n            <div className=\"space-y-6\">\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">System Settings</h3>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Log Level</label>\n                    <select\n                      value={settings.system.logLevel}\n                      onChange={(e) => setSettings({\n                        ...settings,\n                        system: {\n                          ...settings.system,\n                          logLevel: e.target.value\n                        }\n                      })}\n                      className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                    >\n                      <option value=\"debug\">Debug</option>\n                      <option value=\"info\">Info</option>\n                      <option value=\"warn\">Warning</option>\n                      <option value=\"error\">Error</option>\n                    </select>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Max Connections</label>\n                    <input\n                      type=\"number\"\n                      value={settings.system.maxConnections}\n                      onChange={(e) => setSettings({\n                        ...settings,\n                        system: {\n                          ...settings.system,\n                          maxConnections: parseInt(e.target.value)\n                        }\n                      })}\n                      className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Backup Interval</label>\n                    <select\n                      value={settings.system.backupInterval}\n                      onChange={(e) => setSettings({\n                        ...settings,\n                        system: {\n                          ...settings.system,\n                          backupInterval: e.target.value\n                        }\n                      })}\n                      className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                    >\n                      <option value=\"hourly\">Hourly</option>\n                      <option value=\"daily\">Daily</option>\n                      <option value=\"weekly\">Weekly</option>\n                      <option value=\"monthly\">Monthly</option>\n                    </select>\n                  </div>\n                  <div className=\"flex items-center\">\n                    <input\n                      type=\"checkbox\"\n                      checked={settings.system.backupEnabled}\n                      onChange={(e) => setSettings({\n                        ...settings,\n                        system: {\n                          ...settings.system,\n                          backupEnabled: e.target.checked\n                        }\n                      })}\n                      className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                    />\n                    <label className=\"ml-2 block text-sm text-gray-900\">Enable Automatic Backups</label>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Settings;\n"], "mappings": "yJAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,OAAO,KAAQ,qBAAqB,CAC7C,OACEC,OAAO,CACPC,eAAe,CACfC,UAAU,CACVC,QAAQ,CAGRC,OAAO,CACPC,uBAAuB,CACvBC,eAAe,KACV,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBA4BrC,KAAM,CAAAC,QAAkB,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAAEC,IAAK,CAAC,CAAGb,OAAO,CAAC,CAAC,CAC1B,KAAM,CAACc,SAAS,CAAEC,YAAY,CAAC,CAAGjB,QAAQ,CAAC,gBAAgB,CAAC,CAC5D,KAAM,CAACkB,QAAQ,CAAEC,WAAW,CAAC,CAAGnB,QAAQ,CAAiB,CACvDoB,cAAc,CAAE,CACdC,cAAc,CAAE,EAAE,CAClBC,gBAAgB,CAAE,CAAC,CACnBC,iBAAiB,CAAE,CAAC,CACpBC,UAAU,CAAE,KACd,CAAC,CACDC,QAAQ,CAAE,CACRC,kBAAkB,CAAE,IAAI,CACxBC,kBAAkB,CAAE,IAAI,CACxBC,cAAc,CAAE,CAAC,uBAAuB,CAAE,uBAAuB,CAAC,CAClEC,iBAAiB,CAAE,IACrB,CAAC,CACDC,aAAa,CAAE,CACbC,YAAY,CAAE,KAAK,CACnBC,YAAY,CAAE,KAAK,CACnBC,UAAU,CAAE,EACd,CAAC,CACDC,MAAM,CAAE,CACNC,QAAQ,CAAE,MAAM,CAChBC,cAAc,CAAE,IAAI,CACpBC,aAAa,CAAE,IAAI,CACnBC,cAAc,CAAE,OAClB,CACF,CAAC,CAAC,CACF,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGxC,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACyC,OAAO,CAAEC,UAAU,CAAC,CAAG1C,QAAQ,CAAqD,IAAI,CAAC,CAEhGC,SAAS,CAAC,IAAM,CACd0C,aAAa,CAAC,CAAC,CACjB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAA,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,oBAAoB,CAAE,CACjDC,WAAW,CAAE,SACf,CAAC,CAAC,CAEF,GAAIF,QAAQ,CAACG,EAAE,CAAE,CACf,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAJ,QAAQ,CAACK,IAAI,CAAC,CAAC,CAClC9B,WAAW,CAAC6B,IAAI,CAAC9B,QAAQ,EAAIA,QAAQ,CAAC,CACxC,CACF,CAAE,MAAOgC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACnD,CACF,CAAC,CAED,KAAM,CAAAE,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACrCZ,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAI,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,oBAAoB,CAAE,CACjDQ,MAAM,CAAE,KAAK,CACbC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDR,WAAW,CAAE,SAAS,CACtBS,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CAAEvC,QAAS,CAAC,CACnC,CAAC,CAAC,CAEF,GAAI0B,QAAQ,CAACG,EAAE,CAAE,CACfL,UAAU,CAAC,CAAEgB,IAAI,CAAE,SAAS,CAAEC,IAAI,CAAE,6BAA8B,CAAC,CAAC,CACtE,CAAC,IAAM,CACLjB,UAAU,CAAC,CAAEgB,IAAI,CAAE,OAAO,CAAEC,IAAI,CAAE,yBAA0B,CAAC,CAAC,CAChE,CACF,CAAE,MAAOT,KAAK,CAAE,CACdR,UAAU,CAAC,CAAEgB,IAAI,CAAE,OAAO,CAAEC,IAAI,CAAE,yBAA0B,CAAC,CAAC,CAChE,CAAC,OAAS,CACRnB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAoB,IAAI,CAAG,CACX,CAAEC,EAAE,CAAE,gBAAgB,CAAEC,IAAI,CAAE,gBAAgB,CAAEC,IAAI,CAAE3D,eAAgB,CAAC,CACvE,CAAEyD,EAAE,CAAE,UAAU,CAAEC,IAAI,CAAE,UAAU,CAAEC,IAAI,CAAExD,OAAQ,CAAC,CACnD,CAAEsD,EAAE,CAAE,eAAe,CAAEC,IAAI,CAAE,eAAe,CAAEC,IAAI,CAAEzD,QAAS,CAAC,CAC9D,CAAEuD,EAAE,CAAE,QAAQ,CAAEC,IAAI,CAAE,QAAQ,CAAEC,IAAI,CAAE1D,UAAW,CAAC,CACnD,CAED,mBACEQ,KAAA,QAAKmD,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExBtD,IAAA,QAAKqD,SAAS,CAAC,gCAAgC,CAAAC,QAAA,cAC7CpD,KAAA,QAAKmD,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDpD,KAAA,QAAAoD,QAAA,eACEtD,IAAA,OAAIqD,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,cACrEtD,IAAA,MAAGqD,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,gDAA8C,CAAG,CAAC,EAC5E,CAAC,cACNpD,KAAA,WACEqD,OAAO,CAAEd,kBAAmB,CAC5Be,QAAQ,CAAE5B,OAAQ,CAClByB,SAAS,CAAC,oKAAoK,CAAAC,QAAA,eAE9KtD,IAAA,CAACR,OAAO,EAAC6D,SAAS,CAAC,cAAc,CAAE,CAAC,CACnCzB,OAAO,CAAG,WAAW,CAAG,eAAe,EAClC,CAAC,EACN,CAAC,CACH,CAAC,CAGLE,OAAO,eACN9B,IAAA,QAAKqD,SAAS,mBAAAI,MAAA,CACZ3B,OAAO,CAACiB,IAAI,GAAK,SAAS,CAAG,4BAA4B,CAAG,wBAAwB,CACnF,CAAAO,QAAA,cACDpD,KAAA,QAAKmD,SAAS,CAAC,MAAM,CAAAC,QAAA,EAClBxB,OAAO,CAACiB,IAAI,GAAK,SAAS,cACzB/C,IAAA,CAACF,eAAe,EAACuD,SAAS,CAAC,cAAc,CAAE,CAAC,cAE5CrD,IAAA,CAACH,uBAAuB,EAACwD,SAAS,CAAC,cAAc,CAAE,CACpD,CACAvB,OAAO,CAACkB,IAAI,EACV,CAAC,CACH,CACN,cAGD9C,KAAA,QAAKmD,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzCtD,IAAA,QAAKqD,SAAS,CAAC,0BAA0B,CAAAC,QAAA,cACvCtD,IAAA,QAAKqD,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CACxCL,IAAI,CAACS,GAAG,CAAEC,GAAG,EAAK,CACjB,KAAM,CAAAC,IAAI,CAAGD,GAAG,CAACP,IAAI,CACrB,mBACElD,KAAA,WAEEqD,OAAO,CAAEA,CAAA,GAAMjD,YAAY,CAACqD,GAAG,CAACT,EAAE,CAAE,CACpCG,SAAS,yEAAAI,MAAA,CACPpD,SAAS,GAAKsD,GAAG,CAACT,EAAE,CAChB,+BAA+B,CAC/B,4EAA4E,CAC/E,CAAAI,QAAA,eAEHtD,IAAA,CAAC4D,IAAI,EAACP,SAAS,CAAC,SAAS,CAAE,CAAC,cAC5BrD,IAAA,SAAAsD,QAAA,CAAOK,GAAG,CAACR,IAAI,CAAO,CAAC,GATlBQ,GAAG,CAACT,EAUH,CAAC,CAEb,CAAC,CAAC,CACC,CAAC,CACH,CAAC,cAENhD,KAAA,QAAKmD,SAAS,CAAC,KAAK,CAAAC,QAAA,EAEjBjD,SAAS,GAAK,gBAAgB,eAC7BL,IAAA,QAAKqD,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxBpD,KAAA,QAAAoD,QAAA,eACEtD,IAAA,OAAIqD,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,yBAAuB,CAAI,CAAC,cACnFpD,KAAA,QAAKmD,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDpD,KAAA,QAAAoD,QAAA,eACEtD,IAAA,UAAOqD,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,yBAAuB,CAAO,CAAC,cAC1FtD,IAAA,UACE+C,IAAI,CAAC,QAAQ,CACbc,KAAK,CAAEtD,QAAQ,CAACE,cAAc,CAACC,cAAe,CAC9CoD,QAAQ,CAAGC,CAAC,EAAKvD,WAAW,CAAAwD,aAAA,CAAAA,aAAA,IACvBzD,QAAQ,MACXE,cAAc,CAAAuD,aAAA,CAAAA,aAAA,IACTzD,QAAQ,CAACE,cAAc,MAC1BC,cAAc,CAAEuD,QAAQ,CAACF,CAAC,CAACG,MAAM,CAACL,KAAK,CAAC,EACzC,EACF,CAAE,CACHR,SAAS,CAAC,mJAAmJ,CAC9J,CAAC,EACC,CAAC,cACNnD,KAAA,QAAAoD,QAAA,eACEtD,IAAA,UAAOqD,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,oBAAkB,CAAO,CAAC,cACrFtD,IAAA,UACE+C,IAAI,CAAC,QAAQ,CACbc,KAAK,CAAEtD,QAAQ,CAACE,cAAc,CAACE,gBAAiB,CAChDmD,QAAQ,CAAGC,CAAC,EAAKvD,WAAW,CAAAwD,aAAA,CAAAA,aAAA,IACvBzD,QAAQ,MACXE,cAAc,CAAAuD,aAAA,CAAAA,aAAA,IACTzD,QAAQ,CAACE,cAAc,MAC1BE,gBAAgB,CAAEsD,QAAQ,CAACF,CAAC,CAACG,MAAM,CAACL,KAAK,CAAC,EAC3C,EACF,CAAE,CACHR,SAAS,CAAC,mJAAmJ,CAC9J,CAAC,EACC,CAAC,cACNnD,KAAA,QAAAoD,QAAA,eACEtD,IAAA,UAAOqD,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,qBAAmB,CAAO,CAAC,cACtFtD,IAAA,UACE+C,IAAI,CAAC,QAAQ,CACbc,KAAK,CAAEtD,QAAQ,CAACE,cAAc,CAACG,iBAAkB,CACjDkD,QAAQ,CAAGC,CAAC,EAAKvD,WAAW,CAAAwD,aAAA,CAAAA,aAAA,IACvBzD,QAAQ,MACXE,cAAc,CAAAuD,aAAA,CAAAA,aAAA,IACTzD,QAAQ,CAACE,cAAc,MAC1BG,iBAAiB,CAAEqD,QAAQ,CAACF,CAAC,CAACG,MAAM,CAACL,KAAK,CAAC,EAC5C,EACF,CAAE,CACHR,SAAS,CAAC,mJAAmJ,CAC9J,CAAC,EACC,CAAC,cACNnD,KAAA,QAAKmD,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCtD,IAAA,UACE+C,IAAI,CAAC,UAAU,CACfoB,OAAO,CAAE5D,QAAQ,CAACE,cAAc,CAACI,UAAW,CAC5CiD,QAAQ,CAAGC,CAAC,EAAKvD,WAAW,CAAAwD,aAAA,CAAAA,aAAA,IACvBzD,QAAQ,MACXE,cAAc,CAAAuD,aAAA,CAAAA,aAAA,IACTzD,QAAQ,CAACE,cAAc,MAC1BI,UAAU,CAAEkD,CAAC,CAACG,MAAM,CAACC,OAAO,EAC7B,EACF,CAAE,CACHd,SAAS,CAAC,mEAAmE,CAC9E,CAAC,cACFrD,IAAA,UAAOqD,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,qCAAmC,CAAO,CAAC,EAC5F,CAAC,EACH,CAAC,EACH,CAAC,CACH,CACN,CAGAjD,SAAS,GAAK,UAAU,eACvBL,IAAA,QAAKqD,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxBpD,KAAA,QAAAoD,QAAA,eACEtD,IAAA,OAAIqD,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,mBAAiB,CAAI,CAAC,cAC7EpD,KAAA,QAAKmD,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBpD,KAAA,QAAKmD,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCtD,IAAA,UACE+C,IAAI,CAAC,UAAU,CACfoB,OAAO,CAAE5D,QAAQ,CAACO,QAAQ,CAACC,kBAAmB,CAC9C+C,QAAQ,CAAGC,CAAC,EAAKvD,WAAW,CAAAwD,aAAA,CAAAA,aAAA,IACvBzD,QAAQ,MACXO,QAAQ,CAAAkD,aAAA,CAAAA,aAAA,IACHzD,QAAQ,CAACO,QAAQ,MACpBC,kBAAkB,CAAEgD,CAAC,CAACG,MAAM,CAACC,OAAO,EACrC,EACF,CAAE,CACHd,SAAS,CAAC,mEAAmE,CAC9E,CAAC,cACFrD,IAAA,UAAOqD,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,sBAAoB,CAAO,CAAC,EAC7E,CAAC,cACNpD,KAAA,QAAKmD,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCtD,IAAA,UACE+C,IAAI,CAAC,UAAU,CACfoB,OAAO,CAAE5D,QAAQ,CAACO,QAAQ,CAACE,kBAAmB,CAC9C8C,QAAQ,CAAGC,CAAC,EAAKvD,WAAW,CAAAwD,aAAA,CAAAA,aAAA,IACvBzD,QAAQ,MACXO,QAAQ,CAAAkD,aAAA,CAAAA,aAAA,IACHzD,QAAQ,CAACO,QAAQ,MACpBE,kBAAkB,CAAE+C,CAAC,CAACG,MAAM,CAACC,OAAO,EACrC,EACF,CAAE,CACHd,SAAS,CAAC,mEAAmE,CAC9E,CAAC,cACFrD,IAAA,UAAOqD,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,sBAAoB,CAAO,CAAC,EAC7E,CAAC,cACNpD,KAAA,QAAKmD,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCtD,IAAA,UACE+C,IAAI,CAAC,UAAU,CACfoB,OAAO,CAAE5D,QAAQ,CAACO,QAAQ,CAACI,iBAAkB,CAC7C4C,QAAQ,CAAGC,CAAC,EAAKvD,WAAW,CAAAwD,aAAA,CAAAA,aAAA,IACvBzD,QAAQ,MACXO,QAAQ,CAAAkD,aAAA,CAAAA,aAAA,IACHzD,QAAQ,CAACO,QAAQ,MACpBI,iBAAiB,CAAE6C,CAAC,CAACG,MAAM,CAACC,OAAO,EACpC,EACF,CAAE,CACHd,SAAS,CAAC,mEAAmE,CAC9E,CAAC,cACFrD,IAAA,UAAOqD,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,wBAAsB,CAAO,CAAC,EAC/E,CAAC,cACNpD,KAAA,QAAAoD,QAAA,eACEtD,IAAA,UAAOqD,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,gCAA8B,CAAO,CAAC,cACjGtD,IAAA,aACE6D,KAAK,CAAEtD,QAAQ,CAACO,QAAQ,CAACG,cAAc,CAACmD,IAAI,CAAC,IAAI,CAAE,CACnDN,QAAQ,CAAGC,CAAC,EAAKvD,WAAW,CAAAwD,aAAA,CAAAA,aAAA,IACvBzD,QAAQ,MACXO,QAAQ,CAAAkD,aAAA,CAAAA,aAAA,IACHzD,QAAQ,CAACO,QAAQ,MACpBG,cAAc,CAAE8C,CAAC,CAACG,MAAM,CAACL,KAAK,CAACQ,KAAK,CAAC,IAAI,CAAC,CAACC,MAAM,CAACC,MAAM,EAAIA,MAAM,CAACC,IAAI,CAAC,CAAC,CAAC,EAC3E,EACF,CAAE,CACHC,IAAI,CAAE,CAAE,CACRpB,SAAS,CAAC,mJAAmJ,CAC9J,CAAC,EACC,CAAC,EACH,CAAC,EACH,CAAC,CACH,CACN,CAGAhD,SAAS,GAAK,eAAe,eAC5BL,IAAA,QAAKqD,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxBpD,KAAA,QAAAoD,QAAA,eACEtD,IAAA,OAAIqD,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,uBAAqB,CAAI,CAAC,cACjFpD,KAAA,QAAKmD,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBpD,KAAA,QAAKmD,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCtD,IAAA,UACE+C,IAAI,CAAC,UAAU,CACfoB,OAAO,CAAE5D,QAAQ,CAACY,aAAa,CAACC,YAAa,CAC7C0C,QAAQ,CAAGC,CAAC,EAAKvD,WAAW,CAAAwD,aAAA,CAAAA,aAAA,IACvBzD,QAAQ,MACXY,aAAa,CAAA6C,aAAA,CAAAA,aAAA,IACRzD,QAAQ,CAACY,aAAa,MACzBC,YAAY,CAAE2C,CAAC,CAACG,MAAM,CAACC,OAAO,EAC/B,EACF,CAAE,CACHd,SAAS,CAAC,mEAAmE,CAC9E,CAAC,cACFrD,IAAA,UAAOqD,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,4BAA0B,CAAO,CAAC,EACnF,CAAC,cACNpD,KAAA,QAAKmD,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCtD,IAAA,UACE+C,IAAI,CAAC,UAAU,CACfoB,OAAO,CAAE5D,QAAQ,CAACY,aAAa,CAACE,YAAa,CAC7CyC,QAAQ,CAAGC,CAAC,EAAKvD,WAAW,CAAAwD,aAAA,CAAAA,aAAA,IACvBzD,QAAQ,MACXY,aAAa,CAAA6C,aAAA,CAAAA,aAAA,IACRzD,QAAQ,CAACY,aAAa,MACzBE,YAAY,CAAE0C,CAAC,CAACG,MAAM,CAACC,OAAO,EAC/B,EACF,CAAE,CACHd,SAAS,CAAC,mEAAmE,CAC9E,CAAC,cACFrD,IAAA,UAAOqD,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,4BAA0B,CAAO,CAAC,EACnF,CAAC,cACNpD,KAAA,QAAAoD,QAAA,eACEtD,IAAA,UAAOqD,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,aAAW,CAAO,CAAC,cAC9EtD,IAAA,UACE+C,IAAI,CAAC,KAAK,CACVc,KAAK,CAAEtD,QAAQ,CAACY,aAAa,CAACG,UAAW,CACzCwC,QAAQ,CAAGC,CAAC,EAAKvD,WAAW,CAAAwD,aAAA,CAAAA,aAAA,IACvBzD,QAAQ,MACXY,aAAa,CAAA6C,aAAA,CAAAA,aAAA,IACRzD,QAAQ,CAACY,aAAa,MACzBG,UAAU,CAAEyC,CAAC,CAACG,MAAM,CAACL,KAAK,EAC3B,EACF,CAAE,CACHR,SAAS,CAAC,mJAAmJ,CAC7JqB,WAAW,CAAC,sCAAsC,CACnD,CAAC,EACC,CAAC,EACH,CAAC,EACH,CAAC,CACH,CACN,CAGArE,SAAS,GAAK,QAAQ,eACrBL,IAAA,QAAKqD,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxBpD,KAAA,QAAAoD,QAAA,eACEtD,IAAA,OAAIqD,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,cAC3EpD,KAAA,QAAKmD,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDpD,KAAA,QAAAoD,QAAA,eACEtD,IAAA,UAAOqD,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,WAAS,CAAO,CAAC,cAC5EpD,KAAA,WACE2D,KAAK,CAAEtD,QAAQ,CAACgB,MAAM,CAACC,QAAS,CAChCsC,QAAQ,CAAGC,CAAC,EAAKvD,WAAW,CAAAwD,aAAA,CAAAA,aAAA,IACvBzD,QAAQ,MACXgB,MAAM,CAAAyC,aAAA,CAAAA,aAAA,IACDzD,QAAQ,CAACgB,MAAM,MAClBC,QAAQ,CAAEuC,CAAC,CAACG,MAAM,CAACL,KAAK,EACzB,EACF,CAAE,CACHR,SAAS,CAAC,mJAAmJ,CAAAC,QAAA,eAE7JtD,IAAA,WAAQ6D,KAAK,CAAC,OAAO,CAAAP,QAAA,CAAC,OAAK,CAAQ,CAAC,cACpCtD,IAAA,WAAQ6D,KAAK,CAAC,MAAM,CAAAP,QAAA,CAAC,MAAI,CAAQ,CAAC,cAClCtD,IAAA,WAAQ6D,KAAK,CAAC,MAAM,CAAAP,QAAA,CAAC,SAAO,CAAQ,CAAC,cACrCtD,IAAA,WAAQ6D,KAAK,CAAC,OAAO,CAAAP,QAAA,CAAC,OAAK,CAAQ,CAAC,EAC9B,CAAC,EACN,CAAC,cACNpD,KAAA,QAAAoD,QAAA,eACEtD,IAAA,UAAOqD,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,iBAAe,CAAO,CAAC,cAClFtD,IAAA,UACE+C,IAAI,CAAC,QAAQ,CACbc,KAAK,CAAEtD,QAAQ,CAACgB,MAAM,CAACE,cAAe,CACtCqC,QAAQ,CAAGC,CAAC,EAAKvD,WAAW,CAAAwD,aAAA,CAAAA,aAAA,IACvBzD,QAAQ,MACXgB,MAAM,CAAAyC,aAAA,CAAAA,aAAA,IACDzD,QAAQ,CAACgB,MAAM,MAClBE,cAAc,CAAEwC,QAAQ,CAACF,CAAC,CAACG,MAAM,CAACL,KAAK,CAAC,EACzC,EACF,CAAE,CACHR,SAAS,CAAC,mJAAmJ,CAC9J,CAAC,EACC,CAAC,cACNnD,KAAA,QAAAoD,QAAA,eACEtD,IAAA,UAAOqD,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,iBAAe,CAAO,CAAC,cAClFpD,KAAA,WACE2D,KAAK,CAAEtD,QAAQ,CAACgB,MAAM,CAACI,cAAe,CACtCmC,QAAQ,CAAGC,CAAC,EAAKvD,WAAW,CAAAwD,aAAA,CAAAA,aAAA,IACvBzD,QAAQ,MACXgB,MAAM,CAAAyC,aAAA,CAAAA,aAAA,IACDzD,QAAQ,CAACgB,MAAM,MAClBI,cAAc,CAAEoC,CAAC,CAACG,MAAM,CAACL,KAAK,EAC/B,EACF,CAAE,CACHR,SAAS,CAAC,mJAAmJ,CAAAC,QAAA,eAE7JtD,IAAA,WAAQ6D,KAAK,CAAC,QAAQ,CAAAP,QAAA,CAAC,QAAM,CAAQ,CAAC,cACtCtD,IAAA,WAAQ6D,KAAK,CAAC,OAAO,CAAAP,QAAA,CAAC,OAAK,CAAQ,CAAC,cACpCtD,IAAA,WAAQ6D,KAAK,CAAC,QAAQ,CAAAP,QAAA,CAAC,QAAM,CAAQ,CAAC,cACtCtD,IAAA,WAAQ6D,KAAK,CAAC,SAAS,CAAAP,QAAA,CAAC,SAAO,CAAQ,CAAC,EAClC,CAAC,EACN,CAAC,cACNpD,KAAA,QAAKmD,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCtD,IAAA,UACE+C,IAAI,CAAC,UAAU,CACfoB,OAAO,CAAE5D,QAAQ,CAACgB,MAAM,CAACG,aAAc,CACvCoC,QAAQ,CAAGC,CAAC,EAAKvD,WAAW,CAAAwD,aAAA,CAAAA,aAAA,IACvBzD,QAAQ,MACXgB,MAAM,CAAAyC,aAAA,CAAAA,aAAA,IACDzD,QAAQ,CAACgB,MAAM,MAClBG,aAAa,CAAEqC,CAAC,CAACG,MAAM,CAACC,OAAO,EAChC,EACF,CAAE,CACHd,SAAS,CAAC,mEAAmE,CAC9E,CAAC,cACFrD,IAAA,UAAOqD,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,0BAAwB,CAAO,CAAC,EACjF,CAAC,EACH,CAAC,EACH,CAAC,CACH,CACN,EACE,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAnD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}