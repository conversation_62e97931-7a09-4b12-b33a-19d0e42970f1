[{"/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/index.tsx": "1", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/reportWebVitals.ts": "2", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/store.ts": "3", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/App.tsx": "4", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/searchSlice.ts": "5", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/workflowSlice.ts": "6", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/envoySlice.ts": "7", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/auditSlice.ts": "8", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/autoscalerSlice.ts": "9", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/databaseSlice.ts": "10", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/components/Layout.tsx": "11", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/SearchDashboard.tsx": "12", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/Dashboard.tsx": "13", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/WorkflowExecutor.tsx": "14", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/AutoscalerDashboard.tsx": "15", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/DiscoveryWizard.tsx": "16", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/EnvoyConfigEditor.tsx": "17", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/AuditViewer.tsx": "18", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/DatabaseAdmin.tsx": "19", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/auth/AuthContext.tsx": "20", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/auth/LoginPage.tsx": "21"}, {"size": 778, "mtime": 1749058599021, "results": "22", "hashOfConfig": "23"}, {"size": 370, "mtime": 1749058755887, "results": "24", "hashOfConfig": "23"}, {"size": 863, "mtime": 1749063617349, "results": "25", "hashOfConfig": "23"}, {"size": 3385, "mtime": 1749086348344, "results": "26", "hashOfConfig": "23"}, {"size": 2725, "mtime": 1749063531345, "results": "27", "hashOfConfig": "23"}, {"size": 3481, "mtime": 1749063548264, "results": "28", "hashOfConfig": "23"}, {"size": 2849, "mtime": 1749063582338, "results": "29", "hashOfConfig": "23"}, {"size": 1957, "mtime": 1749063595368, "results": "30", "hashOfConfig": "23"}, {"size": 3681, "mtime": 1749063567934, "results": "31", "hashOfConfig": "23"}, {"size": 2204, "mtime": 1749063606689, "results": "32", "hashOfConfig": "23"}, {"size": 8604, "mtime": 1749087112194, "results": "33", "hashOfConfig": "23"}, {"size": 8594, "mtime": 1749063749914, "results": "34", "hashOfConfig": "23"}, {"size": 8985, "mtime": 1749058679704, "results": "35", "hashOfConfig": "23"}, {"size": 10204, "mtime": 1749063787790, "results": "36", "hashOfConfig": "23"}, {"size": 9190, "mtime": 1749063823453, "results": "37", "hashOfConfig": "23"}, {"size": 17002, "mtime": 1749064442452, "results": "38", "hashOfConfig": "23"}, {"size": 11841, "mtime": 1749064586885, "results": "39", "hashOfConfig": "23"}, {"size": 9026, "mtime": 1749063897136, "results": "40", "hashOfConfig": "23"}, {"size": 8828, "mtime": 1749063931077, "results": "41", "hashOfConfig": "23"}, {"size": 8213, "mtime": 1749087594986, "results": "42", "hashOfConfig": "23"}, {"size": 6612, "mtime": 1749087527402, "results": "43", "hashOfConfig": "23"}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ojadns", {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/index.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/reportWebVitals.ts", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/store.ts", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/App.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/searchSlice.ts", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/workflowSlice.ts", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/envoySlice.ts", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/auditSlice.ts", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/autoscalerSlice.ts", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/databaseSlice.ts", ["107"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/components/Layout.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/SearchDashboard.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/Dashboard.tsx", ["108"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/WorkflowExecutor.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/AutoscalerDashboard.tsx", ["109"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/DiscoveryWizard.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/EnvoyConfigEditor.tsx", ["110"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/AuditViewer.tsx", ["111"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/DatabaseAdmin.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/auth/AuthContext.tsx", ["112"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/auth/LoginPage.tsx", [], [], {"ruleId": "113", "severity": 1, "message": "114", "line": 1, "column": 41, "nodeType": "115", "messageId": "116", "endLine": 1, "endColumn": 54}, {"ruleId": "113", "severity": 1, "message": "117", "line": 3, "column": 3, "nodeType": "115", "messageId": "116", "endLine": 3, "endColumn": 15}, {"ruleId": "113", "severity": 1, "message": "118", "line": 18, "column": 43, "nodeType": "115", "messageId": "116", "endLine": 18, "endColumn": 50}, {"ruleId": "113", "severity": 1, "message": "119", "line": 19, "column": 52, "nodeType": "115", "messageId": "116", "endLine": 19, "endColumn": 57}, {"ruleId": "113", "severity": 1, "message": "120", "line": 14, "column": 47, "nodeType": "115", "messageId": "116", "endLine": 14, "endColumn": 52}, {"ruleId": "121", "severity": 1, "message": "122", "line": 60, "column": 6, "nodeType": "123", "endLine": 60, "endColumn": 8, "suggestions": "124"}, "@typescript-eslint/no-unused-vars", "'PayloadAction' is defined but never used.", "Identifier", "unusedVar", "'ChartBarIcon' is defined but never used.", "'metrics' is assigned a value but never used.", "'error' is assigned a value but never used.", "'query' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'initializeAuth'. Either include it or remove the dependency array.", "ArrayExpression", ["125"], {"desc": "126", "fix": "127"}, "Update the dependencies array to be: [initializeAuth]", {"range": "128", "text": "129"}, [1397, 1399], "[initializeAuth]"]