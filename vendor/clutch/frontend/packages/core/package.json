{"name": "@clutch-sh/core", "version": "4.0.0-beta", "description": "Clutch Core Components", "homepage": "https://clutch.sh/docs/development/frontend/overview#clutch-shcore", "license": "Apache-2.0", "author": "<EMAIL>", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "yarn clean && yarn compile", "clean": "yarn run package:clean", "compile": "yarn run package:compile", "compile:dev": "yarn run package:compile:dev", "compile:watch": "yarn run package:compile:watch", "lint": "yarn run package:lint", "lint:fix": "yarn run lint --fix", "prepublishOnly": "yarn run build", "publishBeta": "../../../tools/publish-frontend.sh core", "test": "yarn run package:test", "test:coverage": "yarn run test --collect-coverage", "test:watch": "yarn run test --watch"}, "dependencies": {"@bugsnag/js": "^7.21.0", "@bugsnag/plugin-react": "^7.19.0", "@clutch-sh/api": "workspace:^", "@date-io/core": "^1.3.6", "@emotion/jest": "^11.0.0", "@emotion/react": "^11.0.0", "@emotion/styled": "^11.0.0", "@hookform/devtools": "^4.0.2", "@hookform/resolvers": "2.8.8", "@mui/icons-material": "^5.8.4", "@mui/lab": "^5.0.0-alpha.87", "@mui/material": "^5.8.5", "@mui/styles": "^5.8.4", "@mui/system": "^5.8.4", "@mui/x-date-pickers": "~5.0.5", "@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^12.0.0", "@testing-library/react-hooks": "^8.0.1", "@testing-library/user-event": "^14.4.3", "axios": "^0.21.1", "dayjs": "^1.11.6", "history": "^5.0.0", "js-cookie": "^3.0.0", "jwt-decode": "^3.0.0", "lodash": "^4.17.0", "material-table": "^2.0.3", "react-hook-form": "^7.25.3", "react-is": "^17.0.2", "react-router-dom": "6.0.0", "react-test-renderer": "^17.0.2", "react-timeago": "^7.0.0", "recharts": "^2.1.9", "superstruct": "~0.15.0", "uuid": "^8.3.2", "yup": "^0.32.8"}, "devDependencies": {"@clutch-sh/tools": "workspace:^"}, "peerDependencies": {"react": "^17.0.2", "react-dom": "^17.0.2", "typescript": "^4.2.3"}, "engines": {"node": ">=18 <19", "yarn": "^4.5.0"}, "packageManager": "yarn@4.5.0", "stableVersion": "3.0.0-beta"}