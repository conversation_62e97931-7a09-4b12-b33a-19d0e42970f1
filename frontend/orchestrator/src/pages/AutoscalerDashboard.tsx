import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../store/store';
import {
  fetchAutoscalerStatus,
  fetchMetrics,
  updateAutoscalerConfig,
} from '../store/slices/autoscalerSlice';
import {
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  CpuChipIcon,
  ServerIcon,
} from '@heroicons/react/24/outline';

const AutoscalerDashboard: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { status, events, loading, error, metrics } = useSelector(
    (state: RootState) => state.autoscaler
  );

  useEffect(() => {
    dispatch(fetchAutoscalerStatus());
    dispatch(fetchMetrics('1h'));
  }, [dispatch]);

  const handleToggleAutoscaler = () => {
    if (status) {
      dispatch(updateAutoscalerConfig({ enabled: !status.enabled }));
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-white">Autoscaler Dashboard</h1>
        <p className="mt-1 text-sm text-gray-400">
          Monitor and configure automatic scaling for your applications
        </p>
      </div>

      {/* Status Cards */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div className="bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ServerIcon className="h-6 w-6 text-blue-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-400 truncate">Current Replicas</dt>
                  <dd className="text-lg font-medium text-white">
                    {status?.current_replicas || 0}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ArrowTrendingUpIcon className="h-6 w-6 text-green-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-400 truncate">Desired Replicas</dt>
                  <dd className="text-lg font-medium text-white">
                    {status?.desired_replicas || 0}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CpuChipIcon className="h-6 w-6 text-yellow-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-400 truncate">CPU Utilization</dt>
                  <dd className="text-lg font-medium text-white">
                    {status?.current_cpu_utilization || 0}%
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className={`w-3 h-3 rounded-full ${status?.enabled ? 'bg-green-400' : 'bg-red-400'}`} />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-400 truncate">Status</dt>
                  <dd className="text-lg font-medium text-white">
                    {status?.enabled ? 'Enabled' : 'Disabled'}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Configuration */}
      <div className="bg-gray-800 shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-white mb-4">
            Autoscaler Configuration
          </h3>

          {status && (
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label className="block text-sm font-medium text-gray-300">
                  Min Replicas
                </label>
                <div className="mt-1">
                  <input
                    type="number"
                    className="block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500"
                    value={status.min_replicas}
                    readOnly
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300">
                  Max Replicas
                </label>
                <div className="mt-1">
                  <input
                    type="number"
                    className="block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500"
                    value={status.max_replicas}
                    readOnly
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300">
                  Target CPU Utilization (%)
                </label>
                <div className="mt-1">
                  <input
                    type="number"
                    className="block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500"
                    value={status.target_cpu_utilization}
                    readOnly
                  />
                </div>
              </div>

              <div className="flex items-end">
                <button
                  onClick={handleToggleAutoscaler}
                  disabled={loading}
                  className={`w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 ${
                    status.enabled
                      ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500'
                      : 'bg-green-600 hover:bg-green-700 focus:ring-green-500'
                  }`}
                >
                  {status.enabled ? 'Disable' : 'Enable'} Autoscaler
                </button>
              </div>
            </div>
          )}

          {error && (
            <div className="mt-4 bg-red-50 border border-red-200 rounded-md p-4">
              <div className="text-sm text-red-700">{error}</div>
            </div>
          )}
        </div>
      </div>

      {/* Scaling Events */}
      <div className="bg-gray-800 shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-white mb-4">
            Recent Scaling Events
          </h3>

          {events.length === 0 ? (
            <div className="text-center py-8">
              <ArrowTrendingUpIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-300">No scaling events</h3>
              <p className="mt-1 text-sm text-gray-400">
                Scaling events will appear here when they occur.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {events.slice(0, 10).map((event, index) => (
                <div key={index} className="border border-gray-600 rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3">
                      {event.to_replicas > event.from_replicas ? (
                        <ArrowTrendingUpIcon className="h-5 w-5 text-green-400" />
                      ) : (
                        <ArrowTrendingDownIcon className="h-5 w-5 text-red-400" />
                      )}
                      <div>
                        <p className="text-sm font-medium text-white">
                          Scaled from {event.from_replicas} to {event.to_replicas} replicas
                        </p>
                        <p className="text-sm text-gray-400">{event.reason}</p>
                        <p className="text-xs text-gray-500">{event.message}</p>
                      </div>
                    </div>
                    <span className="text-xs text-gray-400">
                      {new Date(event.timestamp).toLocaleString()}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AutoscalerDashboard;
