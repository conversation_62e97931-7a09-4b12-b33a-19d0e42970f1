package authn

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/coreos/go-oidc/v3/oidc"
	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"golang.org/x/oauth2"
	"go.uber.org/zap"
)

// Service represents the authentication service
type Service struct {
	logger        *zap.Logger
	provider      *oidc.Provider
	verifier      *oidc.IDTokenVerifier
	oauth2Config  *oauth2.Config
	httpClient    *http.Client
	sessionSecret string
	providerAlias string
	tokenStorage  Storage
}

// Config represents the authentication configuration
type Config struct {
	Issuer        string   `json:"issuer"`
	ClientID      string   `json:"client_id"`
	ClientSecret  string   `json:"client_secret"`
	RedirectURL   string   `json:"redirect_url"`
	Scopes        []string `json:"scopes"`
	SessionSecret string   `json:"session_secret"`
}

// Claims represents standardized user claims
type Claims struct {
	*jwt.RegisteredClaims
	Groups []string `json:"grp,omitempty"`
	Email  string   `json:"email,omitempty"`
}

// StateClaims represents state token claims used during OAuth exchange
type StateClaims struct {
	*jwt.RegisteredClaims
	RedirectURL string `json:"redirect"`
}

// IDClaims represents intermediate claims from ID token
type IDClaims struct {
	Email string `json:"email"`
}

// Provider interface defines authentication provider methods
type Provider interface {
	GetStateNonce(redirectURL string) (string, error)
	ValidateStateNonce(state string) (redirectURL string, err error)
	Verify(ctx context.Context, rawIDToken string) (*Claims, error)
	GetAuthCodeURL(ctx context.Context, state string) (string, error)
	Exchange(ctx context.Context, code string) (token *oauth2.Token, err error)
}

// Issuer interface defines token issuing methods
type Issuer interface {
	CreateToken(ctx context.Context, subject string, expiry *time.Duration) (token *oauth2.Token, err error)
	RefreshToken(ctx context.Context, token *oauth2.Token) (*oauth2.Token, error)
}

// TokenReader interface defines token reading methods
type TokenReader interface {
	Read(ctx context.Context, userID, provider string) (*oauth2.Token, error)
}

// TokenStorer interface defines token storing methods
type TokenStorer interface {
	Store(ctx context.Context, userID, provider string, token *oauth2.Token) error
}

// Storage interface combines token reading and storing
type Storage interface {
	TokenReader
	TokenStorer
}

// Default scopes for OIDC
var defaultScopes = []string{oidc.ScopeOpenID, oidc.ScopeOfflineAccess, "email", "profile"}

const clutchProvider = "cainuro"

// New creates a new authentication service
func New(config *Config, logger *zap.Logger, tokenStorage Storage) (*Service, error) {
	ctx := context.Background()
	
	// Set up HTTP client
	httpClient := &http.Client{
		Timeout: 30 * time.Second,
	}
	ctx = oidc.ClientContext(ctx, httpClient)

	// Parse issuer URL for provider alias
	u, err := url.Parse(config.Issuer)
	if err != nil {
		return nil, fmt.Errorf("invalid issuer URL: %w", err)
	}
	alias := u.Hostname()

	// Create OIDC provider
	provider, err := oidc.NewProvider(ctx, config.Issuer)
	if err != nil {
		return nil, fmt.Errorf("failed to create OIDC provider: %w", err)
	}

	// Create ID token verifier
	verifier := provider.Verifier(&oidc.Config{
		ClientID: config.ClientID,
	})

	// Set default scopes if none provided
	scopes := config.Scopes
	if len(scopes) == 0 {
		scopes = defaultScopes
	}

	// Create OAuth2 config
	oauth2Config := &oauth2.Config{
		ClientID:     config.ClientID,
		ClientSecret: config.ClientSecret,
		Endpoint:     provider.Endpoint(),
		RedirectURL:  config.RedirectURL,
		Scopes:       scopes,
	}

	// Verify provider supports authorization code flow
	if err := verifyProviderSupport(ctx, provider); err != nil {
		return nil, fmt.Errorf("provider verification failed: %w", err)
	}

	service := &Service{
		logger:        logger,
		provider:      provider,
		verifier:      verifier,
		oauth2Config:  oauth2Config,
		httpClient:    httpClient,
		sessionSecret: config.SessionSecret,
		providerAlias: alias,
		tokenStorage:  tokenStorage,
	}

	logger.Info("Authentication service initialized", 
		zap.String("provider", alias),
		zap.String("issuer", config.Issuer))

	return service, nil
}

// GetAuthCodeURL returns the OAuth2 authorization URL
func (s *Service) GetAuthCodeURL(ctx context.Context, state string) (string, error) {
	opts := []oauth2.AuthCodeOption{oauth2.AccessTypeOffline}
	return s.oauth2Config.AuthCodeURL(state, opts...), nil
}

// GetStateNonce creates a state nonce for OAuth2 flow
func (s *Service) GetStateNonce(redirectURL string) (string, error) {
	u, err := url.Parse(redirectURL)
	if err != nil {
		return "", fmt.Errorf("invalid redirect URL: %w", err)
	}
	
	// Only allow relative redirects for security
	if u.Scheme != "" || u.Host != "" {
		return "", errors.New("only relative redirects are supported")
	}
	
	dest := u.RequestURI()
	if !strings.HasPrefix(dest, "/") {
		dest = fmt.Sprintf("/%s", dest)
	}

	claims := &StateClaims{
		RegisteredClaims: &jwt.RegisteredClaims{
			Subject:   uuid.New().String(), // UUID serves as CSRF token
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(5 * time.Minute)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
		},
		RedirectURL: dest,
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(s.sessionSecret))
}

// ValidateStateNonce validates and extracts redirect URL from state nonce
func (s *Service) ValidateStateNonce(state string) (string, error) {
	claims := &StateClaims{}
	_, err := jwt.ParseWithClaims(state, claims, func(token *jwt.Token) (interface{}, error) {
		return []byte(s.sessionSecret), nil
	})
	if err != nil {
		return "", fmt.Errorf("invalid state token: %w", err)
	}
	
	return claims.RedirectURL, nil
}

// Exchange exchanges authorization code for tokens
func (s *Service) Exchange(ctx context.Context, code string) (*oauth2.Token, error) {
	// Set HTTP client context
	ctx = oidc.ClientContext(ctx, s.httpClient)

	// Exchange authorization code for token
	token, err := s.oauth2Config.Exchange(ctx, code, oauth2.AccessTypeOffline)
	if err != nil {
		return nil, fmt.Errorf("token exchange failed: %w", err)
	}

	// Extract ID token
	rawIDToken, ok := token.Extra("id_token").(string)
	if !ok {
		return nil, errors.New("id_token not present in OAuth token")
	}

	// Verify ID token
	idToken, err := s.verifier.Verify(ctx, rawIDToken)
	if err != nil {
		return nil, fmt.Errorf("ID token verification failed: %w", err)
	}

	// Extract claims from ID token
	claims, err := s.claimsFromOIDCToken(ctx, idToken)
	if err != nil {
		return nil, fmt.Errorf("failed to extract claims: %w", err)
	}

	// Store provider token if storage is available
	if s.tokenStorage != nil {
		if err := s.tokenStorage.Store(ctx, claims.Subject, s.providerAlias, token); err != nil {
			s.logger.Error("Failed to store provider token", zap.Error(err))
		}
	}

	// Issue and store Clutch token
	return s.issueAndStoreToken(ctx, claims, true)
}

// Verify verifies a JWT token and returns claims
func (s *Service) Verify(ctx context.Context, rawToken string) (*Claims, error) {
	claims := &Claims{}
	_, err := jwt.ParseWithClaims(rawToken, claims, func(token *jwt.Token) (interface{}, error) {
		return []byte(s.sessionSecret), nil
	})
	if err != nil {
		return nil, fmt.Errorf("token verification failed: %w", err)
	}

	// Check if token exists in storage (for revocation support)
	if s.tokenStorage != nil {
		_, err := s.tokenStorage.Read(ctx, claims.Subject, clutchProvider)
		if err != nil {
			return nil, fmt.Errorf("token not found in storage: %w", err)
		}
	}

	return claims, nil
}

// CreateToken creates a new service token
func (s *Service) CreateToken(ctx context.Context, subject string, expiry *time.Duration) (*oauth2.Token, error) {
	issuedAt := time.Now()
	var expiresAt *jwt.NumericDate
	if expiry != nil {
		expiresAt = jwt.NewNumericDate(issuedAt.Add(*expiry))
	}

	claims := &Claims{
		RegisteredClaims: &jwt.RegisteredClaims{
			ExpiresAt: expiresAt,
			IssuedAt:  jwt.NewNumericDate(issuedAt),
			Issuer:    clutchProvider,
			Subject:   "service:" + subject,
		},
	}

	return s.issueAndStoreToken(ctx, claims, false)
}

// RefreshToken refreshes an expired token
func (s *Service) RefreshToken(ctx context.Context, t *oauth2.Token) (*oauth2.Token, error) {
	// Extract claims from refresh token
	claims := &jwt.RegisteredClaims{}
	_, err := jwt.ParseWithClaims(t.RefreshToken, claims, func(token *jwt.Token) (interface{}, error) {
		return []byte(s.sessionSecret), nil
	})
	if err != nil {
		return nil, fmt.Errorf("invalid refresh token: %w", err)
	}

	// Verify refresh token exists in storage
	if s.tokenStorage != nil {
		rt, err := s.tokenStorage.Read(ctx, claims.Subject, clutchProvider)
		if err != nil {
			return nil, fmt.Errorf("refresh token not found: %w", err)
		}
		if rt.RefreshToken != t.RefreshToken {
			return nil, errors.New("refresh token mismatch")
		}
	}

	// Create new token with same subject
	newClaims := &Claims{
		RegisteredClaims: &jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			Issuer:    clutchProvider,
			Subject:   claims.Subject,
		},
	}

	return s.issueAndStoreToken(ctx, newClaims, true)
}

// Read reads a token from storage
func (s *Service) Read(ctx context.Context, userID, provider string) (*oauth2.Token, error) {
	if s.tokenStorage == nil {
		return nil, errors.New("token storage not configured")
	}

	if provider != s.providerAlias {
		return nil, fmt.Errorf("provider '%s' cannot read '%s' tokens", s.providerAlias, provider)
	}

	// Get token from storage
	token, err := s.tokenStorage.Read(ctx, userID, provider)
	if err != nil {
		return nil, err
	}

	// Return token if still valid
	if token.Valid() {
		return token, nil
	}

	// Attempt refresh if refresh token is available
	if token.RefreshToken == "" {
		return nil, errors.New("token expired and no refresh token available")
	}

	// Refresh token
	newToken, err := s.oauth2Config.TokenSource(ctx, token).Token()
	if err != nil {
		return nil, fmt.Errorf("token refresh failed: %w", err)
	}

	// Store refreshed token
	if err := s.tokenStorage.Store(ctx, userID, provider, newToken); err != nil {
		s.logger.Error("Failed to store refreshed token", zap.Error(err))
	}

	return newToken, nil
}

// issueAndStoreToken issues and stores a token
func (s *Service) issueAndStoreToken(ctx context.Context, claims *Claims, refresh bool) (*oauth2.Token, error) {
	// Sign access token
	accessToken, err := jwt.NewWithClaims(jwt.SigningMethodHS256, claims).SignedString([]byte(s.sessionSecret))
	if err != nil {
		return nil, fmt.Errorf("failed to sign access token: %w", err)
	}

	token := &oauth2.Token{
		AccessToken: accessToken,
		TokenType:   "Bearer",
	}

	if claims.ExpiresAt != nil {
		token.Expiry = claims.ExpiresAt.Time
	}

	// Create refresh token if requested and storage is available
	if s.tokenStorage != nil && refresh {
		refreshClaims := &jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(12 * time.Hour)),
			Issuer:    claims.Issuer,
			Subject:   claims.Subject,
		}

		refreshToken, err := jwt.NewWithClaims(jwt.SigningMethodHS256, refreshClaims).SignedString([]byte(s.sessionSecret))
		if err != nil {
			return nil, fmt.Errorf("failed to sign refresh token: %w", err)
		}
		token.RefreshToken = refreshToken

		// Store token
		if err := s.tokenStorage.Store(ctx, claims.Subject, clutchProvider, token); err != nil {
			return nil, fmt.Errorf("failed to store token: %w", err)
		}
	}

	return token, nil
}

// claimsFromOIDCToken extracts claims from OIDC token
func (s *Service) claimsFromOIDCToken(ctx context.Context, t *oidc.IDToken) (*Claims, error) {
	idc := &IDClaims{}
	if err := t.Claims(idc); err != nil {
		return nil, fmt.Errorf("failed to extract ID claims: %w", err)
	}
	
	if idc.Email == "" {
		return nil, errors.New("email claim not found in ID token")
	}

	claims := &Claims{
		RegisteredClaims: &jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(t.Expiry),
			IssuedAt:  jwt.NewNumericDate(t.IssuedAt),
			Issuer:    t.Issuer,
			Subject:   idc.Email,
		},
		Email:  idc.Email,
		Groups: []string{}, // TODO: Extract groups from token if available
	}

	return claims, nil
}

// verifyProviderSupport verifies the OIDC provider supports required flows
func verifyProviderSupport(ctx context.Context, provider *oidc.Provider) error {
	var claims struct {
		GrantTypesSupported []string `json:"grant_types_supported"`
	}
	
	if err := provider.Claims(&claims); err != nil {
		return fmt.Errorf("failed to get provider claims: %w", err)
	}

	// Check for authorization code grant support
	for _, grantType := range claims.GrantTypesSupported {
		if grantType == "authorization_code" {
			return nil
		}
	}

	return errors.New("provider does not support authorization_code grant type")
}

// Health checks the health of the authentication service
func (s *Service) Health(ctx context.Context) error {
	// Try to get provider configuration
	var claims struct {
		Issuer string `json:"issuer"`
	}
	
	if err := s.provider.Claims(&claims); err != nil {
		return fmt.Errorf("provider health check failed: %w", err)
	}

	return nil
}
