package search

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	asset "cloud.google.com/go/asset/apiv1"
	"cloud.google.com/go/asset/apiv1/assetpb"
	"github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/resources/armresources"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/resourcegroupstaggingapi"
	"github.com/aws/aws-sdk-go-v2/service/s3/types"
	appconfig "github.com/cainuro/orchestrator/internal/config"
	"github.com/cainuro/orchestrator/internal/db"
	"github.com/rogpeppe/go-internal/cache"
	"go.uber.org/zap"
	"google.golang.org/api/option"
)

// Resource represents a cloud resource
type Resource struct {
	Provider string            `json:"provider"`
	Type     string            `json:"type"`
	Name     string            `json:"name"`
	ID       string            `json:"id"`
	Tags     map[string]string `json:"tags"`
}

// SearchRequest represents a search request
type SearchRequest struct {
	Provider string `json:"provider"`
	Query    string `json:"query"`
	Limit    int32  `json:"limit"`
}

// SearchResponse represents a search response
type SearchResponse struct {
	Resources  []*Resource `json:"resources"`
	TotalCount int32       `json:"total_count"`
}

// Service defines the search/discovery service interface
type Service interface {
	SearchResources(ctx context.Context, req *SearchRequest) (*SearchResponse, error)
	GetResource(ctx context.Context, resourceID string) (*Resource, error)
	DiscoverAWSResources(ctx context.Context, regions []string) error
	DiscoverGCPResources(ctx context.Context, projects []string) error
	DiscoverAzureResources(ctx context.Context, subscriptions []string) error
}

// service implements the Service interface
type service struct {
	config *appconfig.Config
	logger *zap.Logger
	cache  cache.Cache
	store  db.Store

	// Cloud provider clients
	awsClient   AWSClient
	gcpClient   GCPClient
	azureClient AzureClient
}

// Cloud provider client interfaces for mocking
type AWSClient interface {
	GetResources(ctx context.Context, params *resourcegroupstaggingapi.GetResourcesInput, optFns ...func(*resourcegroupstaggingapi.Options)) (*resourcegroupstaggingapi.GetResourcesOutput, error)
}

type GCPClient interface {
	SearchAllResources(ctx context.Context, req *assetpb.SearchAllResourcesRequest, opts ...option.ClientOption) (*assetpb.SearchAllResourcesResponse, error)
}

type AzureClient interface {
	NewListPager(options *armresources.ClientListOptions) *armresources.ClientListPager
}

// NewService creates a new search service
func NewService(
	config *appconfig.Config,
	logger *zap.Logger,
	cacheInstance cache.Cache,
	store db.Store,
) (Service, error) {
	s := &service{
		config: config,
		logger: logger,
		cache:  cacheInstance,
		store:  store,
	}

	// Initialize cloud provider clients
	if err := s.initCloudClients(); err != nil {
		return nil, fmt.Errorf("failed to initialize cloud clients: %w", err)
	}

	return s, nil
}

// SearchResources searches for resources across cloud providers
func (s *service) SearchResources(ctx context.Context, req *SearchRequest) (*SearchResponse, error) {
	s.logger.Info("Searching resources",
		zap.String("provider", req.Provider),
		zap.String("query", req.Query))

	// Check cache first
	cacheKey := fmt.Sprintf("search:%s:%s", req.Provider, req.Query)
	var cachedResponse SearchResponse
	if found, err := s.cache.Get(ctx, cacheKey, &cachedResponse); err == nil && found {
		s.logger.Debug("Returning cached search results", zap.String("cache_key", cacheKey))
		return &cachedResponse, nil
	}

	var resources []*Resource
	var err error

	switch strings.ToLower(req.Provider) {
	case "aws":
		resources, err = s.searchAWSResources(ctx, req)
	case "gcp":
		resources, err = s.searchGCPResources(ctx, req)
	case "azure":
		resources, err = s.searchAzureResources(ctx, req)
	case "":
		// Search all providers
		resources, err = s.searchAllProviders(ctx, req)
	default:
		return nil, fmt.Errorf("unsupported provider: %s", req.Provider)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to search resources: %w", err)
	}

	// Apply limit
	if req.Limit > 0 && int32(len(resources)) > req.Limit {
		resources = resources[:req.Limit]
	}

	response := &SearchResponse{
		Resources:  resources,
		TotalCount: int32(len(resources)),
	}

	// Cache the response for 5 minutes
	s.cache.SetWithTTL(ctx, cacheKey, response, 5*time.Minute)

	return response, nil
}

// GetResource retrieves a specific resource by ID
func (s *service) GetResource(ctx context.Context, resourceID string) (*Resource, error) {
	s.logger.Info("Getting resource", zap.String("resource_id", resourceID))

	// Check cache first
	cacheKey := fmt.Sprintf("resource:%s", resourceID)
	var cachedResource Resource
	if found, err := s.cache.Get(ctx, cacheKey, &cachedResource); err == nil && found {
		return &cachedResource, nil
	}

	// Query database
	result, err := s.store.Query(ctx, "SELECT * FROM resource_cache WHERE id = ?", resourceID)
	if err != nil {
		return nil, fmt.Errorf("failed to query resource: %w", err)
	}
	defer result.Close()

	var cachedRes db.CachedResource
	if result.Next() {
		if err := result.Scan(&cachedRes); err != nil {
			return nil, fmt.Errorf("failed to scan resource: %w", err)
		}
	} else {
		return nil, fmt.Errorf("resource not found: %s", resourceID)
	}

	// Convert to Resource
	var tags map[string]string
	json.Unmarshal([]byte(cachedRes.Tags), &tags)

	resource := &Resource{
		Provider: cachedRes.Provider,
		Type:     cachedRes.Type,
		Name:     cachedRes.Name,
		ID:       cachedRes.ResourceID,
		Tags:     tags,
	}

	// Cache the resource
	s.cache.SetWithTTL(ctx, cacheKey, resource, 10*time.Minute)

	return resource, nil
}

// DiscoverAWSResources discovers AWS resources and caches them
func (s *service) DiscoverAWSResources(ctx context.Context, regions []string) error {
	s.logger.Info("Starting AWS resource discovery", zap.Strings("regions", regions))

	for _, region := range regions {
		s.logger.Info("Discovering AWS resources", zap.String("region", region))

		// Get all resources in the region
		input := &resourcegroupstaggingapi.GetResourcesInput{
			ResourcesPerPage: aws.Int32(100),
		}

		output, err := s.awsClient.GetResources(ctx, input)
		if err != nil {
			s.logger.Error("Failed to get AWS resources", zap.String("region", region), zap.Error(err))
			continue
		}

		// Process and cache resources
		for _, resource := range output.ResourceTagMappingList {
			cachedResource := db.CachedResource{
				Provider:   "aws",
				Type:       s.extractAWSResourceType(*resource.ResourceARN),
				Name:       s.extractAWSResourceName(*resource.ResourceARN),
				ResourceID: *resource.ResourceARN,
				Region:     region,
				Tags:       s.convertAWSTagsToJSON(resource.Tags),
				LastSeen:   time.Now(),
			}

			if err := s.store.Insert(ctx, "resource_cache", cachedResource); err != nil {
				s.logger.Error("Failed to cache AWS resource", zap.Error(err))
			}
		}
	}

	s.logger.Info("AWS resource discovery completed")
	return nil
}

// DiscoverGCPResources discovers GCP resources and caches them
func (s *service) DiscoverGCPResources(ctx context.Context, projects []string) error {
	s.logger.Info("Starting GCP resource discovery", zap.Strings("projects", projects))

	for _, project := range projects {
		s.logger.Info("Discovering GCP resources", zap.String("project", project))

		req := &assetpb.SearchAllResourcesRequest{
			Scope: fmt.Sprintf("projects/%s", project),
		}

		resp, err := s.gcpClient.SearchAllResources(ctx, req)
		if err != nil {
			s.logger.Error("Failed to get GCP resources", zap.String("project", project), zap.Error(err))
			continue
		}

		// Process and cache resources
		for _, resource := range resp.Results {
			cachedResource := db.CachedResource{
				Provider:   "gcp",
				Type:       resource.AssetType,
				Name:       resource.DisplayName,
				ResourceID: resource.Name,
				Region:     s.extractGCPRegion(resource.Name),
				Tags:       s.convertGCPLabelsToJSON(resource.Labels),
				LastSeen:   time.Now(),
			}

			if err := s.store.Insert(ctx, "resource_cache", cachedResource); err != nil {
				s.logger.Error("Failed to cache GCP resource", zap.Error(err))
			}
		}
	}

	s.logger.Info("GCP resource discovery completed")
	return nil
}

// DiscoverAzureResources discovers Azure resources and caches them
func (s *service) DiscoverAzureResources(ctx context.Context, subscriptions []string) error {
	s.logger.Info("Starting Azure resource discovery", zap.Strings("subscriptions", subscriptions))

	for _, subscription := range subscriptions {
		s.logger.Info("Discovering Azure resources", zap.String("subscription", subscription))

		pager := s.azureClient.NewListPager(&armresources.ClientListOptions{})
		for pager.More() {
			page, err := pager.NextPage(ctx)
			if err != nil {
				s.logger.Error("Failed to get Azure resources", zap.String("subscription", subscription), zap.Error(err))
				break
			}

			// Process and cache resources
			for _, resource := range page.Value {
				cachedResource := db.CachedResource{
					Provider:   "azure",
					Type:       *resource.Type,
					Name:       *resource.Name,
					ResourceID: *resource.ID,
					Region:     *resource.Location,
					Tags:       s.convertAzureTagsToJSON(resource.Tags),
					LastSeen:   time.Now(),
				}

				if err := s.store.Insert(ctx, "resource_cache", cachedResource); err != nil {
					s.logger.Error("Failed to cache Azure resource", zap.Error(err))
				}
			}
		}
	}

	s.logger.Info("Azure resource discovery completed")
	return nil
}

// initCloudClients initializes cloud provider clients
func (s *service) initCloudClients() error {
	// Initialize AWS client
	cfg, err := config.LoadDefaultConfig(context.Background())
	if err != nil {
		return fmt.Errorf("failed to load AWS config: %w", err)
	}
	s.awsClient = resourcegroupstaggingapi.NewFromConfig(cfg)

	// Initialize GCP client
	gcpClient, err := asset.NewClient(context.Background())
	if err != nil {
		return fmt.Errorf("failed to create GCP client: %w", err)
	}
	s.gcpClient = gcpClient

	// Initialize Azure client
	cred, err := azcore.NewDefaultAzureCredential(nil)
	if err != nil {
		return fmt.Errorf("failed to create Azure credentials: %w", err)
	}
	azureClient, err := armresources.NewClient("subscription-id", cred, nil)
	if err != nil {
		return fmt.Errorf("failed to create Azure client: %w", err)
	}
	s.azureClient = azureClient

	return nil
}

// searchAWSResources searches AWS resources
func (s *service) searchAWSResources(ctx context.Context, req *SearchRequest) ([]*Resource, error) {
	// Query cached resources from database
	query := "SELECT * FROM resource_cache WHERE provider = 'aws'"
	if req.Query != "" {
		query += " AND (name LIKE ? OR type LIKE ?)"
	}

	var args []interface{}
	if req.Query != "" {
		searchPattern := "%" + req.Query + "%"
		args = append(args, searchPattern, searchPattern)
	}

	result, err := s.store.Query(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to query AWS resources: %w", err)
	}
	defer result.Close()

	var resources []*Resource
	for result.Next() {
		var cachedRes db.CachedResource
		if err := result.Scan(&cachedRes); err != nil {
			continue
		}

		var tags map[string]string
		json.Unmarshal([]byte(cachedRes.Tags), &tags)

		resources = append(resources, &Resource{
			Provider: cachedRes.Provider,
			Type:     cachedRes.Type,
			Name:     cachedRes.Name,
			ID:       cachedRes.ResourceID,
			Tags:     tags,
		})
	}

	return resources, nil
}

// searchGCPResources searches GCP resources
func (s *service) searchGCPResources(ctx context.Context, req *SearchRequest) ([]*Resource, error) {
	// Query cached resources from database
	query := "SELECT * FROM resource_cache WHERE provider = 'gcp'"
	if req.Query != "" {
		query += " AND (name LIKE ? OR type LIKE ?)"
	}

	var args []interface{}
	if req.Query != "" {
		searchPattern := "%" + req.Query + "%"
		args = append(args, searchPattern, searchPattern)
	}

	result, err := s.store.Query(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to query GCP resources: %w", err)
	}
	defer result.Close()

	var resources []*Resource
	for result.Next() {
		var cachedRes db.CachedResource
		if err := result.Scan(&cachedRes); err != nil {
			continue
		}

		var tags map[string]string
		json.Unmarshal([]byte(cachedRes.Tags), &tags)

		resources = append(resources, &Resource{
			Provider: cachedRes.Provider,
			Type:     cachedRes.Type,
			Name:     cachedRes.Name,
			ID:       cachedRes.ResourceID,
			Tags:     tags,
		})
	}

	return resources, nil
}

// searchAzureResources searches Azure resources
func (s *service) searchAzureResources(ctx context.Context, req *SearchRequest) ([]*Resource, error) {
	// Query cached resources from database
	query := "SELECT * FROM resource_cache WHERE provider = 'azure'"
	if req.Query != "" {
		query += " AND (name LIKE ? OR type LIKE ?)"
	}

	var args []interface{}
	if req.Query != "" {
		searchPattern := "%" + req.Query + "%"
		args = append(args, searchPattern, searchPattern)
	}

	result, err := s.store.Query(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to query Azure resources: %w", err)
	}
	defer result.Close()

	var resources []*Resource
	for result.Next() {
		var cachedRes db.CachedResource
		if err := result.Scan(&cachedRes); err != nil {
			continue
		}

		var tags map[string]string
		json.Unmarshal([]byte(cachedRes.Tags), &tags)

		resources = append(resources, &Resource{
			Provider: cachedRes.Provider,
			Type:     cachedRes.Type,
			Name:     cachedRes.Name,
			ID:       cachedRes.ResourceID,
			Tags:     tags,
		})
	}

	return resources, nil
}

// searchAllProviders searches across all cloud providers
func (s *service) searchAllProviders(ctx context.Context, req *SearchRequest) ([]*Resource, error) {
	var allResources []*Resource

	// Search AWS
	awsReq := &SearchRequest{Provider: "aws", Query: req.Query, Limit: req.Limit}
	awsResources, err := s.searchAWSResources(ctx, awsReq)
	if err == nil {
		allResources = append(allResources, awsResources...)
	}

	// Search GCP
	gcpReq := &SearchRequest{Provider: "gcp", Query: req.Query, Limit: req.Limit}
	gcpResources, err := s.searchGCPResources(ctx, gcpReq)
	if err == nil {
		allResources = append(allResources, gcpResources...)
	}

	// Search Azure
	azureReq := &SearchRequest{Provider: "azure", Query: req.Query, Limit: req.Limit}
	azureResources, err := s.searchAzureResources(ctx, azureReq)
	if err == nil {
		allResources = append(allResources, azureResources...)
	}

	return allResources, nil
}

// Helper methods for resource parsing and conversion

// extractAWSResourceType extracts resource type from AWS ARN
func (s *service) extractAWSResourceType(arn string) string {
	parts := strings.Split(arn, ":")
	if len(parts) >= 3 {
		return parts[2]
	}
	return "unknown"
}

// extractAWSResourceName extracts resource name from AWS ARN
func (s *service) extractAWSResourceName(arn string) string {
	parts := strings.Split(arn, "/")
	if len(parts) > 0 {
		return parts[len(parts)-1]
	}
	return arn
}

// convertAWSTagsToJSON converts AWS tags to JSON string
func (s *service) convertAWSTagsToJSON(tags []types.Tag) string {
	tagMap := make(map[string]string)
	for _, tag := range tags {
		if tag.Key != nil && tag.Value != nil {
			tagMap[*tag.Key] = *tag.Value
		}
	}
	jsonBytes, _ := json.Marshal(tagMap)
	return string(jsonBytes)
}

// extractGCPRegion extracts region from GCP resource name
func (s *service) extractGCPRegion(resourceName string) string {
	// Extract region from resource name like projects/project/zones/us-central1-a/instances/instance-name
	parts := strings.Split(resourceName, "/")
	for i, part := range parts {
		if part == "zones" || part == "regions" {
			if i+1 < len(parts) {
				return parts[i+1]
			}
		}
	}
	return "global"
}

// convertGCPLabelsToJSON converts GCP labels to JSON string
func (s *service) convertGCPLabelsToJSON(labels map[string]string) string {
	if labels == nil {
		labels = make(map[string]string)
	}
	jsonBytes, _ := json.Marshal(labels)
	return string(jsonBytes)
}

// convertAzureTagsToJSON converts Azure tags to JSON string
func (s *service) convertAzureTagsToJSON(tags map[string]*string) string {
	tagMap := make(map[string]string)
	for key, value := range tags {
		if value != nil {
			tagMap[key] = *value
		}
	}
	jsonBytes, _ := json.Marshal(tagMap)
	return string(jsonBytes)
}
