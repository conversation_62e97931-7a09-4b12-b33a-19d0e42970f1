syntax = "proto3";

package orchestrator.v1;

import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";

option go_package = "github.com/cainuro/orchestrator/proto/orchestrator/v1;orchestratorv1";

// AutoScaler Service
service AutoScalerService {
  rpc GetAutoscalerStatus(GetAutoscalerStatusRequest) returns (GetAutoscalerStatusResponse);
  rpc UpdateAutoscalerConfig(UpdateAutoscalerConfigRequest) returns (UpdateAutoscalerConfigResponse);
}

// Workflow Service
service WorkflowService {
  rpc ExecuteWorkflow(ExecuteWorkflowRequest) returns (ExecuteWorkflowResponse);
  rpc GetWorkflowStatus(GetWorkflowStatusRequest) returns (GetWorkflowStatusResponse);
  rpc ListWorkflows(ListWorkflowsRequest) returns (ListWorkflowsResponse);
}

// Discovery Service
service DiscoveryService {
  rpc SearchResources(SearchResourcesRequest) returns (SearchResourcesResponse);
  rpc GetResource(GetResourceRequest) returns (GetResourceResponse);
}

// Envoy Control Plane Service
service EnvoyControlPlaneService {
  rpc PushSnapshot(PushSnapshotRequest) returns (PushSnapshotResponse);
  rpc GetSnapshot(GetSnapshotRequest) returns (GetSnapshotResponse);
}

// DB Admin Service
service DBAdminService {
  rpc GetDBStatus(GetDBStatusRequest) returns (GetDBStatusResponse);
  rpc ExecuteQuery(ExecuteQueryRequest) returns (ExecuteQueryResponse);
}

// Audit Service
service AuditService {
  rpc GetAuditLogs(GetAuditLogsRequest) returns (GetAuditLogsResponse);
  rpc VerifyAuditEntry(VerifyAuditEntryRequest) returns (VerifyAuditEntryResponse);
}

// Common Resource message for discovery
message Resource {
  string provider = 1; // aws|gcp|azure
  string type = 2;
  string name = 3;
  string id = 4;
  map<string, string> tags = 5;
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp updated_at = 7;
}

// AutoScaler messages
message GetAutoscalerStatusRequest {}

message GetAutoscalerStatusResponse {
  bool enabled = 1;
  int32 current_replicas = 2;
  int32 desired_replicas = 3;
  string status = 4;
}

message UpdateAutoscalerConfigRequest {
  bool enabled = 1;
  int32 min_replicas = 2;
  int32 max_replicas = 3;
  int32 target_cpu_percent = 4;
}

message UpdateAutoscalerConfigResponse {
  bool success = 1;
  string message = 2;
}

// Workflow messages
message ExecuteWorkflowRequest {
  string workflow_id = 1;
  map<string, string> inputs = 2;
}

message ExecuteWorkflowResponse {
  string execution_id = 1;
  string status = 2;
}

message GetWorkflowStatusRequest {
  string workflow_id = 1;
}

message GetWorkflowStatusResponse {
  string workflow_id = 1;
  string status = 2;
  google.protobuf.Timestamp started_at = 3;
  google.protobuf.Timestamp completed_at = 4;
  map<string, string> outputs = 5;
}

message ListWorkflowsRequest {
  int32 page_size = 1;
  string page_token = 2;
}

message ListWorkflowsResponse {
  repeated WorkflowInfo workflows = 1;
  string next_page_token = 2;
}

message WorkflowInfo {
  string id = 1;
  string name = 2;
  string description = 3;
  google.protobuf.Timestamp created_at = 4;
}

// Discovery messages
message SearchResourcesRequest {
  string provider = 1; // aws|gcp|azure
  string query = 2;
  map<string, string> filters = 3;
  int32 limit = 4;
}

message SearchResourcesResponse {
  repeated Resource resources = 1;
  string next_token = 2;
  int32 total_count = 3;
}

message GetResourceRequest {
  string resource_id = 1;
}

message GetResourceResponse {
  Resource resource = 1;
}

// Envoy messages
message PushSnapshotRequest {
  string tenant = 1;
  string clusters_json = 2;
  string routes_json = 3;
  string listeners_json = 4;
}

message PushSnapshotResponse {
  bool success = 1;
  string message = 2;
  string snapshot_version = 3;
}

message GetSnapshotRequest {
  string tenant = 1;
}

message GetSnapshotResponse {
  string tenant = 1;
  string clusters_json = 2;
  string routes_json = 3;
  string listeners_json = 4;
  string version = 5;
  google.protobuf.Timestamp created_at = 6;
}

// DB Admin messages
message GetDBStatusRequest {}

message GetDBStatusResponse {
  bool connected = 1;
  string version = 2;
  int64 total_records = 3;
  string status = 4;
}

message ExecuteQueryRequest {
  string query = 1;
  repeated string parameters = 2;
}

message QueryResult {
  map<string, string> row = 1;
}

message ExecuteQueryResponse {
  repeated QueryResult results = 1;
  int32 rows_affected = 2;
}

// Audit messages
message GetAuditLogsRequest {
  google.protobuf.Timestamp start_time = 1;
  google.protobuf.Timestamp end_time = 2;
  string user_id = 3;
  string action = 4;
  int32 limit = 5;
}

message GetAuditLogsResponse {
  repeated AuditEntry entries = 1;
  string next_token = 2;
}

message VerifyAuditEntryRequest {
  string entry_id = 1;
}

message VerifyAuditEntryResponse {
  bool verified = 1;
  string proof = 2;
  string message = 3;
}

message AuditEntry {
  string id = 1;
  string user_id = 2;
  string action = 3;
  string resource = 4;
  google.protobuf.Timestamp timestamp = 5;
  map<string, string> metadata = 6;
  string proof_hash = 7;
}
