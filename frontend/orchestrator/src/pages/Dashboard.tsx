import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../auth/AuthContext';
import {
  ChartBarIcon,
  CogIcon,
  MagnifyingGlassIcon,
  GlobeAltIcon,
  ArrowTrendingUpIcon,
  ShieldCheckIcon,
  CircleStackIcon,
  UsersIcon,
  ServerIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  PlayIcon,
  EyeIcon,
} from '@heroicons/react/24/outline';

interface HealthStatus {
  mode: string;
  services: Record<string, string>;
  status: string;
  version: string;
}

interface DashboardStats {
  totalUsers: number;
  activeConnections: number;
  runningWorkflows: number;
  systemUptime: string;
}

interface QuickAction {
  name: string;
  description: string;
  icon: any;
  href: string;
  color: string;
}

const serviceIcons: Record<string, any> = {
  audit: ShieldCheckIcon,
  autoscaler: ArrowTrendingUpIcon,
  db_admin: CircleStackIcon,
  discovery: MagnifyingGlassIcon,
  envoy_control_plane: GlobeAltIcon,
  workflow: CogIcon,
};

const quickActions: QuickAction[] = [
  {
    name: 'Discovery Wizard',
    description: 'Search across AWS, GCP, and Azure resources',
    icon: MagnifyingGlassIcon,
    href: '/discovery',
    color: 'bg-blue-500',
  },
  {
    name: 'Run Workflow',
    description: 'Execute automation workflows',
    icon: PlayIcon,
    href: '/workflows',
    color: 'bg-green-500',
  },
  {
    name: 'Search Resources',
    description: 'Advanced resource search and filtering',
    icon: EyeIcon,
    href: '/search',
    color: 'bg-purple-500',
  },
  {
    name: 'Envoy Config',
    description: 'Manage Envoy proxy configurations',
    icon: GlobeAltIcon,
    href: '/envoy',
    color: 'bg-orange-500',
  },
  {
    name: 'Audit Logs',
    description: 'View tamper-proof audit trail',
    icon: ShieldCheckIcon,
    href: '/audit',
    color: 'bg-red-500',
  },
  {
    name: 'Database Admin',
    description: 'Manage database connections and queries',
    icon: CircleStackIcon,
    href: '/database',
    color: 'bg-indigo-500',
  },
];

export function Dashboard() {
  const { user } = useAuth();
  const [health, setHealth] = useState<HealthStatus | null>(null);
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch health status
        const healthResponse = await fetch('/health');
        const healthData = await healthResponse.json();
        setHealth(healthData);

        // Fetch dashboard stats
        const statsResponse = await fetch('/v1/admin/stats', {
          credentials: 'include',
        });
        if (statsResponse.ok) {
          const statsData = await statsResponse.json();
          setStats({
            totalUsers: statsData.totalUsers || 0,
            activeConnections: statsData.activeConnections || 0,
            runningWorkflows: statsData.runningWorkflows || 0,
            systemUptime: statsData.systemUptime || 'Unknown',
          });
        }
      } catch (error) {
        console.error('Failed to fetch dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
    const interval = setInterval(fetchData, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">CAINuro Orchestrator</h1>
            <p className="text-gray-600">Welcome back, {user?.name}</p>
          </div>
          <div className="flex items-center space-x-2">
            {health?.status === 'healthy' ? (
              <>
                <CheckCircleIcon className="h-8 w-8 text-green-500" />
                <span className="text-sm font-medium text-green-600">All Systems Operational</span>
              </>
            ) : (
              <>
                <ExclamationTriangleIcon className="h-8 w-8 text-red-500" />
                <span className="text-sm font-medium text-red-600">System Issues Detected</span>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <UsersIcon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total Users</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats?.totalUsers || 0}</dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-5 py-3">
            <div className="text-sm">
              <span className="text-green-600 font-medium">Active</span>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ServerIcon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Connections</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats?.activeConnections || 0}</dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-5 py-3">
            <div className="text-sm">
              <span className="text-blue-600 font-medium">Active</span>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CogIcon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Workflows</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats?.runningWorkflows || 0}</dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-5 py-3">
            <div className="text-sm">
              <span className="text-purple-600 font-medium">Running</span>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ClockIcon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Uptime</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats?.systemUptime || 'Unknown'}</dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-5 py-3">
            <div className="text-sm">
              <span className="text-green-600 font-medium">{health?.mode} Mode</span>
            </div>
          </div>
        </div>
      </div>

      {/* Services Status */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">System Services</h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
            {health?.services && Object.entries(health.services).map(([serviceName, status]) => {
              const IconComponent = serviceIcons[serviceName] || CogIcon;
              const isActive = status === 'active';

              return (
                <div key={serviceName} className="flex items-center p-4 border border-gray-200 rounded-lg">
                  <div className="flex-shrink-0">
                    <IconComponent className={`h-6 w-6 ${isActive ? 'text-green-500' : 'text-red-500'}`} />
                  </div>
                  <div className="ml-4 flex-1">
                    <h4 className="text-sm font-medium text-gray-900">
                      {serviceName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </h4>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      isActive
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {status}
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Quick Actions</h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
            {quickActions.map((action) => {
              const IconComponent = action.icon;
              return (
                <Link
                  key={action.name}
                  to={action.href}
                  className="relative group bg-white p-6 border border-gray-200 rounded-lg hover:border-gray-300 hover:shadow-md transition-all duration-200"
                >
                  <div>
                    <span className={`rounded-lg inline-flex p-3 ${action.color} text-white`}>
                      <IconComponent className="h-6 w-6" />
                    </span>
                  </div>
                  <div className="mt-4">
                    <h3 className="text-lg font-medium text-gray-900 group-hover:text-blue-600">
                      {action.name}
                    </h3>
                    <p className="mt-2 text-sm text-gray-500">
                      {action.description}
                    </p>
                  </div>
                </Link>
              );
            })}
          </div>
        </div>
      </div>

      {/* System Information */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">System Information</h3>
        </div>
        <div className="p-6">
          <dl className="grid grid-cols-1 gap-6 sm:grid-cols-3">
            <div>
              <dt className="text-sm font-medium text-gray-500">Version</dt>
              <dd className="mt-1 text-lg font-semibold text-gray-900">{health?.version}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Mode</dt>
              <dd className="mt-1 text-lg font-semibold text-gray-900">
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                  {health?.mode}
                </span>
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Active Services</dt>
              <dd className="mt-1 text-lg font-semibold text-gray-900">
                {health?.services ? Object.keys(health.services).length : 0} / {health?.services ? Object.keys(health.services).length : 0}
              </dd>
            </div>
          </dl>
        </div>
      </div>
    </div>
  );
}
