const childProcess = require("child_process");
const fs = require("fs");

const srcDir = process.argv[2];
const configFile = process.argv[3] || "clutch.config.js";

const config = require(`${srcDir}/${configFile}`); // eslint-disable-line import/no-dynamic-require
const rootFrontendDir = `${srcDir}/..`;

const WORKFLOW_MODULE_PATH = `${srcDir}/workflows.jsx`;

const addImport = workflow => {
  const wkflwName = workflow.replace("@", "");
  let moduleNameParts = [wkflwName];
  if (wkflwName.includes("/")) {
    moduleNameParts = wkflwName.split(/[/-]+/);
  }
  const moduleName = moduleNameParts
    .map((part, idx) => {
      if (idx === 0) {
        return part;
      }
      return part.replace(/^\w/, c => c.toUpperCase());
    })
    .join("");
  fs.appendFileSync(
    WORKFLOW_MODULE_PATH,
    `import { default as ${moduleName}} from "${workflow}";\n`
  );
  console.log(`Registered ${workflow} workflow...`); // eslint-disable-line no-console
  return moduleName;
};

const discoverWorkflows = () => {
  return new Promise(resolve => {
    fs.appendFileSync(WORKFLOW_MODULE_PATH, `/* eslint-disable */\n`);
    fs.appendFileSync(
      WORKFLOW_MODULE_PATH,
      `/*\n * This file is autogenerated. DO NOT MODIFY BY HAND!\n`
    );
    fs.appendFileSync(
      WORKFLOW_MODULE_PATH,
      ` * Run the @clutch-sh/tools registerWorkflows target instead\n*/\n`
    );
    const packagePattern = Object.keys(config);
    childProcess.exec(
      `yarn workspaces list --json`,
      {
        cwd: rootFrontendDir,
      },
      (err, stdout) => {
        if (err) {
          throw err;
        }

        const modules = {};

        (stdout || "")
          .split("\n")
          .filter(item => item.length)
          .map(item => (JSON.parse(item) || {})?.name)
          .filter(name => packagePattern.includes(name))
          .forEach(p => {
            const packageName = `@${p.split("@")[1]}`;
            modules[packageName] = addImport(packageName);
          });

        return resolve(modules);
      }
    );
  });
};

try {
  fs.unlinkSync(WORKFLOW_MODULE_PATH);
} catch (err) {}

(() => {
  return discoverWorkflows().then(modules => {
    fs.appendFileSync(WORKFLOW_MODULE_PATH, `\nconst registeredWorkflows = {\n`);
    Object.keys(modules).forEach(moduleKey => {
      const value = modules[moduleKey];
      fs.appendFileSync(WORKFLOW_MODULE_PATH, `  "${moduleKey}": ${value},\n`);
    });
    fs.appendFileSync(WORKFLOW_MODULE_PATH, `};\nexport default registeredWorkflows;`);
    console.log("Generated Workflow imports!"); // eslint-disable-line no-console
  });
})();
