{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/auth/AuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { createContext, useContext, useEffect, useState } from 'react';\n\n// Authentication types based on Clutch implementation\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n// Create auth context\nconst AuthContext = /*#__PURE__*/createContext(undefined);\n\n// Auth provider component\n\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [authState, setAuthState] = useState({\n    isAuthenticated: false,\n    isLoading: true,\n    user: null,\n    token: null,\n    error: null\n  });\n\n  // Initialize authentication on mount\n  useEffect(() => {\n    initializeAuth();\n  }, []);\n\n  // Initialize authentication\n  const initializeAuth = async () => {\n    try {\n      setAuthState(prev => ({\n        ...prev,\n        isLoading: true,\n        error: null\n      }));\n\n      // Check if user is already authenticated\n      const response = await fetch('/v1/auth/user', {\n        credentials: 'include'\n      });\n      if (response.ok) {\n        const user = await response.json();\n        setAuthState(prev => ({\n          ...prev,\n          isAuthenticated: true,\n          user,\n          isLoading: false\n        }));\n      } else {\n        // Try to refresh token\n        await refreshToken();\n      }\n    } catch (error) {\n      console.error('Auth initialization failed:', error);\n      setAuthState(prev => ({\n        ...prev,\n        isAuthenticated: false,\n        isLoading: false,\n        error: 'Authentication initialization failed'\n      }));\n    }\n  };\n\n  // Login function with demo OAuth simulation\n  const login = async (redirectUrl = '/') => {\n    try {\n      setAuthState(prev => ({\n        ...prev,\n        isLoading: true,\n        error: null\n      }));\n\n      // Get auth URL from backend\n      const response = await fetch(`/v1/auth/login?redirect_url=${encodeURIComponent(redirectUrl)}`, {\n        credentials: 'include'\n      });\n      if (!response.ok) {\n        throw new Error('Failed to initiate login');\n      }\n      const data = await response.json();\n      if (data.token) {\n        // User was already authenticated, use existing token\n        const userResponse = await fetch('/v1/auth/user', {\n          credentials: 'include'\n        });\n        if (userResponse.ok) {\n          const user = await userResponse.json();\n          setAuthState(prev => ({\n            ...prev,\n            isAuthenticated: true,\n            user,\n            token: data.token,\n            isLoading: false\n          }));\n          return;\n        }\n      }\n\n      // For demo purposes, simulate OAuth callback instead of redirecting\n      if (data.auth_url && data.state) {\n        // In production, this would redirect to: window.location.href = data.auth_url;\n        // For demo, simulate successful authentication by creating a demo user\n        const demoUser = {\n          sub: 'demo_user_' + Date.now(),\n          email: '<EMAIL>',\n          name: 'CAINuro Demo User',\n          groups: ['cainuro-users', 'cainuro-operators'],\n          roles: ['viewer', 'discovery_user', 'workflow_user'],\n          permissions: ['discovery:read', 'workflows:read', 'workflows:execute', 'audit:read', 'metrics:read', 'auth:read'],\n          preferences: {\n            theme: 'dark',\n            timezone: 'UTC',\n            language: 'en'\n          }\n        };\n\n        // Simulate successful authentication\n        setAuthState(prev => ({\n          ...prev,\n          isAuthenticated: true,\n          user: demoUser,\n          token: {\n            access_token: 'demo_token_' + Date.now(),\n            token_type: 'Bearer',\n            expires_in: 3600\n          },\n          isLoading: false\n        }));\n      }\n    } catch (error) {\n      console.error('Login failed:', error);\n      setAuthState(prev => ({\n        ...prev,\n        isLoading: false,\n        error: error instanceof Error ? error.message : 'Login failed'\n      }));\n    }\n  };\n\n  // Logout function\n  const logout = async () => {\n    try {\n      setAuthState(prev => ({\n        ...prev,\n        isLoading: true\n      }));\n      const response = await fetch('/v1/auth/logout', {\n        method: 'POST',\n        credentials: 'include'\n      });\n      setAuthState({\n        isAuthenticated: false,\n        isLoading: false,\n        user: null,\n        token: null,\n        error: null\n      });\n\n      // Optionally redirect to logout URL if provided\n      if (response.ok) {\n        const data = await response.json();\n        if (data.logout_url && data.logout_url !== window.location.origin) {\n          window.location.href = data.logout_url;\n          return;\n        }\n      }\n\n      // Reload page to clear any cached state\n      window.location.reload();\n    } catch (error) {\n      console.error('Logout failed:', error);\n      setAuthState(prev => ({\n        ...prev,\n        isLoading: false,\n        error: 'Logout failed'\n      }));\n    }\n  };\n\n  // Check permission function\n  const checkPermission = async (action, resource) => {\n    try {\n      var _data$decision;\n      const response = await fetch('/v1/auth/check', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        credentials: 'include',\n        body: JSON.stringify({\n          action,\n          resource\n        })\n      });\n      if (!response.ok) {\n        return false;\n      }\n      const data = await response.json();\n      return ((_data$decision = data.decision) === null || _data$decision === void 0 ? void 0 : _data$decision.allowed) || false;\n    } catch (error) {\n      console.error('Permission check failed:', error);\n      return false;\n    }\n  };\n\n  // Refresh token function\n  const refreshToken = async () => {\n    try {\n      const response = await fetch('/v1/auth/refresh', {\n        method: 'POST',\n        credentials: 'include'\n      });\n      if (response.ok) {\n        const tokenData = await response.json();\n\n        // Get updated user info\n        const userResponse = await fetch('/v1/auth/user', {\n          credentials: 'include'\n        });\n        if (userResponse.ok) {\n          const user = await userResponse.json();\n          setAuthState(prev => ({\n            ...prev,\n            isAuthenticated: true,\n            user,\n            token: tokenData,\n            isLoading: false,\n            error: null\n          }));\n        }\n      } else {\n        throw new Error('Token refresh failed');\n      }\n    } catch (error) {\n      console.error('Token refresh failed:', error);\n      setAuthState(prev => ({\n        ...prev,\n        isAuthenticated: false,\n        isLoading: false,\n        user: null,\n        token: null,\n        error: 'Session expired'\n      }));\n    }\n  };\n\n  // Auto-refresh token before expiry\n  useEffect(() => {\n    if (authState.token && authState.isAuthenticated) {\n      const refreshInterval = setInterval(() => {\n        refreshToken();\n      }, 50 * 60 * 1000); // Refresh every 50 minutes (token expires in 60 minutes)\n\n      return () => clearInterval(refreshInterval);\n    }\n  }, [authState.token, authState.isAuthenticated]);\n  const contextValue = {\n    ...authState,\n    login,\n    logout,\n    checkPermission,\n    refreshToken\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: contextValue,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 304,\n    columnNumber: 5\n  }, this);\n};\n\n// Hook to use auth context\n_s(AuthProvider, \"EtjL6AJ9I1MVsglub8zGDHBrPgE=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\n// Higher-order component for protected routes\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const ProtectedRoute = ({\n  children,\n  requiredPermission,\n  fallback = /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-4 text-red-600\",\n    children: \"Access Denied\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 329,\n    columnNumber: 14\n  }, this)\n}) => {\n  _s3();\n  const {\n    isAuthenticated,\n    isLoading,\n    checkPermission\n  } = useAuth();\n  const [hasPermission, setHasPermission] = useState(null);\n  useEffect(() => {\n    if (isAuthenticated && requiredPermission) {\n      checkPermission(requiredPermission.action, requiredPermission.resource).then(setHasPermission);\n    } else if (isAuthenticated) {\n      setHasPermission(true);\n    }\n  }, [isAuthenticated, requiredPermission, checkPermission]);\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4\",\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 344,\n      columnNumber: 12\n    }, this);\n  }\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4\",\n      children: \"Please log in to access this page.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 12\n    }, this);\n  }\n  if (requiredPermission && hasPermission === false) {\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: fallback\n    }, void 0, false);\n  }\n  if (requiredPermission && hasPermission === null) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4\",\n      children: \"Checking permissions...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 356,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: children\n  }, void 0, false);\n};\n_s3(ProtectedRoute, \"NlR+6+MpaGDn/Vwdy9L5LnCliPY=\", false, function () {\n  return [useAuth];\n});\n_c2 = ProtectedRoute;\nvar _c, _c2;\n$RefreshReg$(_c, \"AuthProvider\");\n$RefreshReg$(_c2, \"ProtectedRoute\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AuthContext", "undefined", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "authState", "setAuthState", "isAuthenticated", "isLoading", "user", "token", "error", "initializeAuth", "prev", "response", "fetch", "credentials", "ok", "json", "refreshToken", "console", "login", "redirectUrl", "encodeURIComponent", "Error", "data", "userResponse", "auth_url", "state", "demoUser", "sub", "Date", "now", "email", "name", "groups", "roles", "permissions", "preferences", "theme", "timezone", "language", "access_token", "token_type", "expires_in", "message", "logout", "method", "logout_url", "window", "location", "origin", "href", "reload", "checkPermission", "action", "resource", "_data$decision", "headers", "body", "JSON", "stringify", "decision", "allowed", "tokenData", "refreshInterval", "setInterval", "clearInterval", "contextValue", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "context", "ProtectedRoute", "requiredPermission", "fallback", "className", "_s3", "hasPermission", "setHasPermission", "then", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/auth/AuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';\n\n// Authentication types based on Clutch implementation\ninterface User {\n  sub: string;\n  email: string;\n  name: string;\n  groups: string[];\n  roles: string[];\n  permissions: string[];\n  preferences: {\n    theme: string;\n    timezone: string;\n    language: string;\n  };\n}\n\ninterface AuthToken {\n  access_token: string;\n  refresh_token?: string;\n  token_type: string;\n  expires_in: number;\n}\n\ninterface AuthState {\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  user: User | null;\n  token: AuthToken | null;\n  error: string | null;\n}\n\ninterface AuthContextType extends AuthState {\n  login: (redirectUrl?: string) => Promise<void>;\n  logout: () => Promise<void>;\n  checkPermission: (action: string, resource: string) => Promise<boolean>;\n  refreshToken: () => Promise<void>;\n}\n\n// Create auth context\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\n// Auth provider component\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [authState, setAuthState] = useState<AuthState>({\n    isAuthenticated: false,\n    isLoading: true,\n    user: null,\n    token: null,\n    error: null,\n  });\n\n  // Initialize authentication on mount\n  useEffect(() => {\n    initializeAuth();\n  }, []);\n\n  // Initialize authentication\n  const initializeAuth = async () => {\n    try {\n      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));\n\n      // Check if user is already authenticated\n      const response = await fetch('/v1/auth/user', {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const user = await response.json();\n        setAuthState(prev => ({\n          ...prev,\n          isAuthenticated: true,\n          user,\n          isLoading: false,\n        }));\n      } else {\n        // Try to refresh token\n        await refreshToken();\n      }\n    } catch (error) {\n      console.error('Auth initialization failed:', error);\n      setAuthState(prev => ({\n        ...prev,\n        isAuthenticated: false,\n        isLoading: false,\n        error: 'Authentication initialization failed',\n      }));\n    }\n  };\n\n  // Login function with demo OAuth simulation\n  const login = async (redirectUrl: string = '/') => {\n    try {\n      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));\n\n      // Get auth URL from backend\n      const response = await fetch(`/v1/auth/login?redirect_url=${encodeURIComponent(redirectUrl)}`, {\n        credentials: 'include',\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to initiate login');\n      }\n\n      const data = await response.json();\n\n      if (data.token) {\n        // User was already authenticated, use existing token\n        const userResponse = await fetch('/v1/auth/user', {\n          credentials: 'include',\n        });\n\n        if (userResponse.ok) {\n          const user = await userResponse.json();\n          setAuthState(prev => ({\n            ...prev,\n            isAuthenticated: true,\n            user,\n            token: data.token,\n            isLoading: false,\n          }));\n          return;\n        }\n      }\n\n      // For demo purposes, simulate OAuth callback instead of redirecting\n      if (data.auth_url && data.state) {\n        // In production, this would redirect to: window.location.href = data.auth_url;\n        // For demo, simulate successful authentication by creating a demo user\n        const demoUser = {\n          sub: 'demo_user_' + Date.now(),\n          email: '<EMAIL>',\n          name: 'CAINuro Demo User',\n          groups: ['cainuro-users', 'cainuro-operators'],\n          roles: ['viewer', 'discovery_user', 'workflow_user'],\n          permissions: [\n            'discovery:read',\n            'workflows:read',\n            'workflows:execute',\n            'audit:read',\n            'metrics:read',\n            'auth:read'\n          ],\n          preferences: {\n            theme: 'dark',\n            timezone: 'UTC',\n            language: 'en'\n          }\n        };\n\n        // Simulate successful authentication\n        setAuthState(prev => ({\n          ...prev,\n          isAuthenticated: true,\n          user: demoUser,\n          token: {\n            access_token: 'demo_token_' + Date.now(),\n            token_type: 'Bearer',\n            expires_in: 3600\n          },\n          isLoading: false,\n        }));\n      }\n    } catch (error) {\n      console.error('Login failed:', error);\n      setAuthState(prev => ({\n        ...prev,\n        isLoading: false,\n        error: error instanceof Error ? error.message : 'Login failed',\n      }));\n    }\n  };\n\n  // Logout function\n  const logout = async () => {\n    try {\n      setAuthState(prev => ({ ...prev, isLoading: true }));\n\n      const response = await fetch('/v1/auth/logout', {\n        method: 'POST',\n        credentials: 'include',\n      });\n\n      setAuthState({\n        isAuthenticated: false,\n        isLoading: false,\n        user: null,\n        token: null,\n        error: null,\n      });\n\n      // Optionally redirect to logout URL if provided\n      if (response.ok) {\n        const data = await response.json();\n        if (data.logout_url && data.logout_url !== window.location.origin) {\n          window.location.href = data.logout_url;\n          return;\n        }\n      }\n      \n      // Reload page to clear any cached state\n      window.location.reload();\n    } catch (error) {\n      console.error('Logout failed:', error);\n      setAuthState(prev => ({\n        ...prev,\n        isLoading: false,\n        error: 'Logout failed',\n      }));\n    }\n  };\n\n  // Check permission function\n  const checkPermission = async (action: string, resource: string): Promise<boolean> => {\n    try {\n      const response = await fetch('/v1/auth/check', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include',\n        body: JSON.stringify({ action, resource }),\n      });\n\n      if (!response.ok) {\n        return false;\n      }\n\n      const data = await response.json();\n      return data.decision?.allowed || false;\n    } catch (error) {\n      console.error('Permission check failed:', error);\n      return false;\n    }\n  };\n\n  // Refresh token function\n  const refreshToken = async () => {\n    try {\n      const response = await fetch('/v1/auth/refresh', {\n        method: 'POST',\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const tokenData = await response.json();\n        \n        // Get updated user info\n        const userResponse = await fetch('/v1/auth/user', {\n          credentials: 'include',\n        });\n\n        if (userResponse.ok) {\n          const user = await userResponse.json();\n          setAuthState(prev => ({\n            ...prev,\n            isAuthenticated: true,\n            user,\n            token: tokenData,\n            isLoading: false,\n            error: null,\n          }));\n        }\n      } else {\n        throw new Error('Token refresh failed');\n      }\n    } catch (error) {\n      console.error('Token refresh failed:', error);\n      setAuthState(prev => ({\n        ...prev,\n        isAuthenticated: false,\n        isLoading: false,\n        user: null,\n        token: null,\n        error: 'Session expired',\n      }));\n    }\n  };\n\n  // Auto-refresh token before expiry\n  useEffect(() => {\n    if (authState.token && authState.isAuthenticated) {\n      const refreshInterval = setInterval(() => {\n        refreshToken();\n      }, 50 * 60 * 1000); // Refresh every 50 minutes (token expires in 60 minutes)\n\n      return () => clearInterval(refreshInterval);\n    }\n  }, [authState.token, authState.isAuthenticated]);\n\n  const contextValue: AuthContextType = {\n    ...authState,\n    login,\n    logout,\n    checkPermission,\n    refreshToken,\n  };\n\n  return (\n    <AuthContext.Provider value={contextValue}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\n// Hook to use auth context\nexport const useAuth = (): AuthContextType => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\n// Higher-order component for protected routes\ninterface ProtectedRouteProps {\n  children: ReactNode;\n  requiredPermission?: { action: string; resource: string };\n  fallback?: ReactNode;\n}\n\nexport const ProtectedRoute: React.FC<ProtectedRouteProps> = ({\n  children,\n  requiredPermission,\n  fallback = <div className=\"p-4 text-red-600\">Access Denied</div>,\n}) => {\n  const { isAuthenticated, isLoading, checkPermission } = useAuth();\n  const [hasPermission, setHasPermission] = useState<boolean | null>(null);\n\n  useEffect(() => {\n    if (isAuthenticated && requiredPermission) {\n      checkPermission(requiredPermission.action, requiredPermission.resource)\n        .then(setHasPermission);\n    } else if (isAuthenticated) {\n      setHasPermission(true);\n    }\n  }, [isAuthenticated, requiredPermission, checkPermission]);\n\n  if (isLoading) {\n    return <div className=\"p-4\">Loading...</div>;\n  }\n\n  if (!isAuthenticated) {\n    return <div className=\"p-4\">Please log in to access this page.</div>;\n  }\n\n  if (requiredPermission && hasPermission === false) {\n    return <>{fallback}</>;\n  }\n\n  if (requiredPermission && hasPermission === null) {\n    return <div className=\"p-4\">Checking permissions...</div>;\n  }\n\n  return <>{children}</>;\n};\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAmB,OAAO;;AAExF;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAqCA;AACA,MAAMC,WAAW,gBAAGR,aAAa,CAA8BS,SAAS,CAAC;;AAEzE;;AAKA,OAAO,MAAMC,YAAyC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACzE,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGX,QAAQ,CAAY;IACpDY,eAAe,EAAE,KAAK;IACtBC,SAAS,EAAE,IAAI;IACfC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,IAAI;IACXC,KAAK,EAAE;EACT,CAAC,CAAC;;EAEF;EACAjB,SAAS,CAAC,MAAM;IACdkB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMA,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFN,YAAY,CAACO,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEL,SAAS,EAAE,IAAI;QAAEG,KAAK,EAAE;MAAK,CAAC,CAAC,CAAC;;MAEjE;MACA,MAAMG,QAAQ,GAAG,MAAMC,KAAK,CAAC,eAAe,EAAE;QAC5CC,WAAW,EAAE;MACf,CAAC,CAAC;MAEF,IAAIF,QAAQ,CAACG,EAAE,EAAE;QACf,MAAMR,IAAI,GAAG,MAAMK,QAAQ,CAACI,IAAI,CAAC,CAAC;QAClCZ,YAAY,CAACO,IAAI,KAAK;UACpB,GAAGA,IAAI;UACPN,eAAe,EAAE,IAAI;UACrBE,IAAI;UACJD,SAAS,EAAE;QACb,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACL;QACA,MAAMW,YAAY,CAAC,CAAC;MACtB;IACF,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdS,OAAO,CAACT,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDL,YAAY,CAACO,IAAI,KAAK;QACpB,GAAGA,IAAI;QACPN,eAAe,EAAE,KAAK;QACtBC,SAAS,EAAE,KAAK;QAChBG,KAAK,EAAE;MACT,CAAC,CAAC,CAAC;IACL;EACF,CAAC;;EAED;EACA,MAAMU,KAAK,GAAG,MAAAA,CAAOC,WAAmB,GAAG,GAAG,KAAK;IACjD,IAAI;MACFhB,YAAY,CAACO,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEL,SAAS,EAAE,IAAI;QAAEG,KAAK,EAAE;MAAK,CAAC,CAAC,CAAC;;MAEjE;MACA,MAAMG,QAAQ,GAAG,MAAMC,KAAK,CAAC,+BAA+BQ,kBAAkB,CAACD,WAAW,CAAC,EAAE,EAAE;QAC7FN,WAAW,EAAE;MACf,CAAC,CAAC;MAEF,IAAI,CAACF,QAAQ,CAACG,EAAE,EAAE;QAChB,MAAM,IAAIO,KAAK,CAAC,0BAA0B,CAAC;MAC7C;MAEA,MAAMC,IAAI,GAAG,MAAMX,QAAQ,CAACI,IAAI,CAAC,CAAC;MAElC,IAAIO,IAAI,CAACf,KAAK,EAAE;QACd;QACA,MAAMgB,YAAY,GAAG,MAAMX,KAAK,CAAC,eAAe,EAAE;UAChDC,WAAW,EAAE;QACf,CAAC,CAAC;QAEF,IAAIU,YAAY,CAACT,EAAE,EAAE;UACnB,MAAMR,IAAI,GAAG,MAAMiB,YAAY,CAACR,IAAI,CAAC,CAAC;UACtCZ,YAAY,CAACO,IAAI,KAAK;YACpB,GAAGA,IAAI;YACPN,eAAe,EAAE,IAAI;YACrBE,IAAI;YACJC,KAAK,EAAEe,IAAI,CAACf,KAAK;YACjBF,SAAS,EAAE;UACb,CAAC,CAAC,CAAC;UACH;QACF;MACF;;MAEA;MACA,IAAIiB,IAAI,CAACE,QAAQ,IAAIF,IAAI,CAACG,KAAK,EAAE;QAC/B;QACA;QACA,MAAMC,QAAQ,GAAG;UACfC,GAAG,EAAE,YAAY,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;UAC9BC,KAAK,EAAE,kBAAkB;UACzBC,IAAI,EAAE,mBAAmB;UACzBC,MAAM,EAAE,CAAC,eAAe,EAAE,mBAAmB,CAAC;UAC9CC,KAAK,EAAE,CAAC,QAAQ,EAAE,gBAAgB,EAAE,eAAe,CAAC;UACpDC,WAAW,EAAE,CACX,gBAAgB,EAChB,gBAAgB,EAChB,mBAAmB,EACnB,YAAY,EACZ,cAAc,EACd,WAAW,CACZ;UACDC,WAAW,EAAE;YACXC,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,KAAK;YACfC,QAAQ,EAAE;UACZ;QACF,CAAC;;QAED;QACAnC,YAAY,CAACO,IAAI,KAAK;UACpB,GAAGA,IAAI;UACPN,eAAe,EAAE,IAAI;UACrBE,IAAI,EAAEoB,QAAQ;UACdnB,KAAK,EAAE;YACLgC,YAAY,EAAE,aAAa,GAAGX,IAAI,CAACC,GAAG,CAAC,CAAC;YACxCW,UAAU,EAAE,QAAQ;YACpBC,UAAU,EAAE;UACd,CAAC;UACDpC,SAAS,EAAE;QACb,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdS,OAAO,CAACT,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCL,YAAY,CAACO,IAAI,KAAK;QACpB,GAAGA,IAAI;QACPL,SAAS,EAAE,KAAK;QAChBG,KAAK,EAAEA,KAAK,YAAYa,KAAK,GAAGb,KAAK,CAACkC,OAAO,GAAG;MAClD,CAAC,CAAC,CAAC;IACL;EACF,CAAC;;EAED;EACA,MAAMC,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI;MACFxC,YAAY,CAACO,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEL,SAAS,EAAE;MAAK,CAAC,CAAC,CAAC;MAEpD,MAAMM,QAAQ,GAAG,MAAMC,KAAK,CAAC,iBAAiB,EAAE;QAC9CgC,MAAM,EAAE,MAAM;QACd/B,WAAW,EAAE;MACf,CAAC,CAAC;MAEFV,YAAY,CAAC;QACXC,eAAe,EAAE,KAAK;QACtBC,SAAS,EAAE,KAAK;QAChBC,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,IAAI;QACXC,KAAK,EAAE;MACT,CAAC,CAAC;;MAEF;MACA,IAAIG,QAAQ,CAACG,EAAE,EAAE;QACf,MAAMQ,IAAI,GAAG,MAAMX,QAAQ,CAACI,IAAI,CAAC,CAAC;QAClC,IAAIO,IAAI,CAACuB,UAAU,IAAIvB,IAAI,CAACuB,UAAU,KAAKC,MAAM,CAACC,QAAQ,CAACC,MAAM,EAAE;UACjEF,MAAM,CAACC,QAAQ,CAACE,IAAI,GAAG3B,IAAI,CAACuB,UAAU;UACtC;QACF;MACF;;MAEA;MACAC,MAAM,CAACC,QAAQ,CAACG,MAAM,CAAC,CAAC;IAC1B,CAAC,CAAC,OAAO1C,KAAK,EAAE;MACdS,OAAO,CAACT,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtCL,YAAY,CAACO,IAAI,KAAK;QACpB,GAAGA,IAAI;QACPL,SAAS,EAAE,KAAK;QAChBG,KAAK,EAAE;MACT,CAAC,CAAC,CAAC;IACL;EACF,CAAC;;EAED;EACA,MAAM2C,eAAe,GAAG,MAAAA,CAAOC,MAAc,EAAEC,QAAgB,KAAuB;IACpF,IAAI;MAAA,IAAAC,cAAA;MACF,MAAM3C,QAAQ,GAAG,MAAMC,KAAK,CAAC,gBAAgB,EAAE;QAC7CgC,MAAM,EAAE,MAAM;QACdW,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACD1C,WAAW,EAAE,SAAS;QACtB2C,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEN,MAAM;UAAEC;QAAS,CAAC;MAC3C,CAAC,CAAC;MAEF,IAAI,CAAC1C,QAAQ,CAACG,EAAE,EAAE;QAChB,OAAO,KAAK;MACd;MAEA,MAAMQ,IAAI,GAAG,MAAMX,QAAQ,CAACI,IAAI,CAAC,CAAC;MAClC,OAAO,EAAAuC,cAAA,GAAAhC,IAAI,CAACqC,QAAQ,cAAAL,cAAA,uBAAbA,cAAA,CAAeM,OAAO,KAAI,KAAK;IACxC,CAAC,CAAC,OAAOpD,KAAK,EAAE;MACdS,OAAO,CAACT,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAO,KAAK;IACd;EACF,CAAC;;EAED;EACA,MAAMQ,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAML,QAAQ,GAAG,MAAMC,KAAK,CAAC,kBAAkB,EAAE;QAC/CgC,MAAM,EAAE,MAAM;QACd/B,WAAW,EAAE;MACf,CAAC,CAAC;MAEF,IAAIF,QAAQ,CAACG,EAAE,EAAE;QACf,MAAM+C,SAAS,GAAG,MAAMlD,QAAQ,CAACI,IAAI,CAAC,CAAC;;QAEvC;QACA,MAAMQ,YAAY,GAAG,MAAMX,KAAK,CAAC,eAAe,EAAE;UAChDC,WAAW,EAAE;QACf,CAAC,CAAC;QAEF,IAAIU,YAAY,CAACT,EAAE,EAAE;UACnB,MAAMR,IAAI,GAAG,MAAMiB,YAAY,CAACR,IAAI,CAAC,CAAC;UACtCZ,YAAY,CAACO,IAAI,KAAK;YACpB,GAAGA,IAAI;YACPN,eAAe,EAAE,IAAI;YACrBE,IAAI;YACJC,KAAK,EAAEsD,SAAS;YAChBxD,SAAS,EAAE,KAAK;YAChBG,KAAK,EAAE;UACT,CAAC,CAAC,CAAC;QACL;MACF,CAAC,MAAM;QACL,MAAM,IAAIa,KAAK,CAAC,sBAAsB,CAAC;MACzC;IACF,CAAC,CAAC,OAAOb,KAAK,EAAE;MACdS,OAAO,CAACT,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CL,YAAY,CAACO,IAAI,KAAK;QACpB,GAAGA,IAAI;QACPN,eAAe,EAAE,KAAK;QACtBC,SAAS,EAAE,KAAK;QAChBC,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,IAAI;QACXC,KAAK,EAAE;MACT,CAAC,CAAC,CAAC;IACL;EACF,CAAC;;EAED;EACAjB,SAAS,CAAC,MAAM;IACd,IAAIW,SAAS,CAACK,KAAK,IAAIL,SAAS,CAACE,eAAe,EAAE;MAChD,MAAM0D,eAAe,GAAGC,WAAW,CAAC,MAAM;QACxC/C,YAAY,CAAC,CAAC;MAChB,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;;MAEpB,OAAO,MAAMgD,aAAa,CAACF,eAAe,CAAC;IAC7C;EACF,CAAC,EAAE,CAAC5D,SAAS,CAACK,KAAK,EAAEL,SAAS,CAACE,eAAe,CAAC,CAAC;EAEhD,MAAM6D,YAA6B,GAAG;IACpC,GAAG/D,SAAS;IACZgB,KAAK;IACLyB,MAAM;IACNQ,eAAe;IACfnC;EACF,CAAC;EAED,oBACEtB,OAAA,CAACG,WAAW,CAACqE,QAAQ;IAACC,KAAK,EAAEF,YAAa;IAAAjE,QAAA,EACvCA;EAAQ;IAAAoE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;;AAED;AAAAtE,EAAA,CAtQaF,YAAyC;AAAAyE,EAAA,GAAzCzE,YAAyC;AAuQtD,OAAO,MAAM0E,OAAO,GAAGA,CAAA,KAAuB;EAAAC,GAAA;EAC5C,MAAMC,OAAO,GAAGrF,UAAU,CAACO,WAAW,CAAC;EACvC,IAAI8E,OAAO,KAAK7E,SAAS,EAAE;IACzB,MAAM,IAAIuB,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOsD,OAAO;AAChB,CAAC;;AAED;AAAAD,GAAA,CARaD,OAAO;AAepB,OAAO,MAAMG,cAA6C,GAAGA,CAAC;EAC5D5E,QAAQ;EACR6E,kBAAkB;EAClBC,QAAQ,gBAAGpF,OAAA;IAAKqF,SAAS,EAAC,kBAAkB;IAAA/E,QAAA,EAAC;EAAa;IAAAoE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK;AACjE,CAAC,KAAK;EAAAS,GAAA;EACJ,MAAM;IAAE5E,eAAe;IAAEC,SAAS;IAAE8C;EAAgB,CAAC,GAAGsB,OAAO,CAAC,CAAC;EACjE,MAAM,CAACQ,aAAa,EAAEC,gBAAgB,CAAC,GAAG1F,QAAQ,CAAiB,IAAI,CAAC;EAExED,SAAS,CAAC,MAAM;IACd,IAAIa,eAAe,IAAIyE,kBAAkB,EAAE;MACzC1B,eAAe,CAAC0B,kBAAkB,CAACzB,MAAM,EAAEyB,kBAAkB,CAACxB,QAAQ,CAAC,CACpE8B,IAAI,CAACD,gBAAgB,CAAC;IAC3B,CAAC,MAAM,IAAI9E,eAAe,EAAE;MAC1B8E,gBAAgB,CAAC,IAAI,CAAC;IACxB;EACF,CAAC,EAAE,CAAC9E,eAAe,EAAEyE,kBAAkB,EAAE1B,eAAe,CAAC,CAAC;EAE1D,IAAI9C,SAAS,EAAE;IACb,oBAAOX,OAAA;MAAKqF,SAAS,EAAC,KAAK;MAAA/E,QAAA,EAAC;IAAU;MAAAoE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAC9C;EAEA,IAAI,CAACnE,eAAe,EAAE;IACpB,oBAAOV,OAAA;MAAKqF,SAAS,EAAC,KAAK;MAAA/E,QAAA,EAAC;IAAkC;MAAAoE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACtE;EAEA,IAAIM,kBAAkB,IAAII,aAAa,KAAK,KAAK,EAAE;IACjD,oBAAOvF,OAAA,CAAAE,SAAA;MAAAI,QAAA,EAAG8E;IAAQ,gBAAG,CAAC;EACxB;EAEA,IAAID,kBAAkB,IAAII,aAAa,KAAK,IAAI,EAAE;IAChD,oBAAOvF,OAAA;MAAKqF,SAAS,EAAC,KAAK;MAAA/E,QAAA,EAAC;IAAuB;MAAAoE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAC3D;EAEA,oBAAO7E,OAAA,CAAAE,SAAA;IAAAI,QAAA,EAAGA;EAAQ,gBAAG,CAAC;AACxB,CAAC;AAACgF,GAAA,CAlCWJ,cAA6C;EAAA,QAKAH,OAAO;AAAA;AAAAW,GAAA,GALpDR,cAA6C;AAAA,IAAAJ,EAAA,EAAAY,GAAA;AAAAC,YAAA,CAAAb,EAAA;AAAAa,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}