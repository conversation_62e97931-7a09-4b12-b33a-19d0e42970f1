import type { WorkflowConfiguration } from "@clutch-sh/core";

import { StartExperiment } from "./start-experiment";

const register = (): WorkflowConfiguration => {
  return {
    developer: {
      name: "Lyft",
      contactUrl: "mailto:<EMAIL>",
    },
    path: "server-experimentation",
    group: "Chaos Experimentation",
    displayName: "Server Fault Injection",
    defaultLayoutProps: {
      variant: "standard",
    },
    routes: {
      startExperiment: {
        path: "start",
        displayName: "Start Experiment",
        description: "Start Server Experiment.",
        component: StartExperiment,
      },
    },
  };
};

export default register;
