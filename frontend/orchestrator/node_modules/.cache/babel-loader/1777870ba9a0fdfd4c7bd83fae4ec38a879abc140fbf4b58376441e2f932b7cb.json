{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/DiscoveryWizard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useDispatch } from 'react-redux';\nimport { searchResources } from '../store/slices/searchSlice';\nimport { CloudIcon, KeyIcon, MagnifyingGlassIcon, CheckCircleIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst DiscoveryWizard = () => {\n  _s();\n  const dispatch = useDispatch();\n  const [currentStep, setCurrentStep] = useState(1);\n  const [credentials, setCredentials] = useState({\n    provider: 'aws'\n  });\n  const [isValidating, setIsValidating] = useState(false);\n  const [validationResult, setValidationResult] = useState(null);\n  const steps = [{\n    id: 1,\n    name: 'Choose Provider',\n    description: 'Select your cloud provider'\n  }, {\n    id: 2,\n    name: 'Add Credentials',\n    description: 'Configure authentication'\n  }, {\n    id: 3,\n    name: 'Configure Discovery',\n    description: 'Set discovery parameters'\n  }, {\n    id: 4,\n    name: 'Launch Discovery',\n    description: 'Start resource discovery'\n  }];\n  const providers = [{\n    id: 'aws',\n    name: 'Amazon Web Services',\n    icon: '🟠',\n    description: 'Discover EC2, S3, RDS, and other AWS resources',\n    fields: ['accessKey', 'secretKey', 'region']\n  }, {\n    id: 'gcp',\n    name: 'Google Cloud Platform',\n    icon: '🔵',\n    description: 'Discover Compute Engine, Cloud Storage, and GCP resources',\n    fields: ['projectId']\n  }, {\n    id: 'azure',\n    name: 'Microsoft Azure',\n    icon: '🔷',\n    description: 'Discover Virtual Machines, Storage, and Azure resources',\n    fields: ['subscriptionId', 'tenantId']\n  }];\n  const handleProviderSelect = providerId => {\n    setCredentials({\n      provider: providerId\n    });\n    setCurrentStep(2);\n  };\n  const handleCredentialChange = (field, value) => {\n    setCredentials(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const validateCredentials = async () => {\n    setIsValidating(true);\n    setValidationResult(null);\n\n    // Simulate credential validation\n    setTimeout(() => {\n      setValidationResult('success');\n      setIsValidating(false);\n    }, 2000);\n  };\n  const launchDiscovery = () => {\n    // Launch discovery workflow\n    dispatch(searchResources({\n      provider: credentials.provider,\n      limit: 100\n    }));\n    setCurrentStep(4);\n  };\n  const renderStepContent = () => {\n    switch (currentStep) {\n      case 1:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(CloudIcon, {\n              className: \"mx-auto h-12 w-12 text-cyan-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"mt-2 text-lg font-medium text-white\",\n              children: \"Choose Cloud Provider\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-gray-400\",\n              children: \"Select the cloud provider you want to discover resources from\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 gap-4 sm:grid-cols-3\",\n            children: providers.map(provider => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleProviderSelect(provider.id),\n              className: \"relative rounded-lg border border-gray-600 bg-gray-800 p-6 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-cyan-500 transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-3xl mb-3\",\n                  children: provider.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 113,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-lg font-medium text-white\",\n                  children: provider.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 114,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mt-2 text-sm text-gray-400\",\n                  children: provider.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 115,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 19\n              }, this)\n            }, provider.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this);\n      case 2:\n        const selectedProvider = providers.find(p => p.id === credentials.provider);\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(KeyIcon, {\n              className: \"mx-auto h-12 w-12 text-cyan-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"mt-2 text-lg font-medium text-white\",\n              children: [\"Configure \", selectedProvider === null || selectedProvider === void 0 ? void 0 : selectedProvider.name, \" Credentials\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-gray-400\",\n              children: [\"Enter your authentication credentials for \", selectedProvider === null || selectedProvider === void 0 ? void 0 : selectedProvider.name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [credentials.provider === 'aws' && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-300\",\n                  children: \"Access Key ID\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 141,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  className: \"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\",\n                  value: credentials.accessKey || '',\n                  onChange: e => handleCredentialChange('accessKey', e.target.value),\n                  placeholder: \"AKIA...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 142,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-300\",\n                  children: \"Secret Access Key\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 151,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"password\",\n                  className: \"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\",\n                  value: credentials.secretKey || '',\n                  onChange: e => handleCredentialChange('secretKey', e.target.value),\n                  placeholder: \"\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-300\",\n                  children: \"Region\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  className: \"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\",\n                  value: credentials.region || '',\n                  onChange: e => handleCredentialChange('region', e.target.value),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select a region\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 167,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"us-east-1\",\n                    children: \"US East (N. Virginia)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 168,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"us-west-2\",\n                    children: \"US West (Oregon)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 169,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"eu-west-1\",\n                    children: \"Europe (Ireland)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 170,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"ap-southeast-1\",\n                    children: \"Asia Pacific (Singapore)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 171,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true), credentials.provider === 'gcp' && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-300\",\n                children: \"Project ID\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\",\n                value: credentials.projectId || '',\n                onChange: e => handleCredentialChange('projectId', e.target.value),\n                placeholder: \"my-gcp-project\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 17\n            }, this), credentials.provider === 'azure' && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-300\",\n                  children: \"Subscription ID\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  className: \"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\",\n                  value: credentials.subscriptionId || '',\n                  onChange: e => handleCredentialChange('subscriptionId', e.target.value),\n                  placeholder: \"xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-300\",\n                  children: \"Tenant ID\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  className: \"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\",\n                  value: credentials.tenantId || '',\n                  onChange: e => handleCredentialChange('tenantId', e.target.value),\n                  placeholder: \"xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setCurrentStep(1),\n              className: \"px-4 py-2 border border-gray-600 rounded-md text-gray-300 hover:bg-gray-700\",\n              children: \"Back\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: validateCredentials,\n              disabled: isValidating,\n              className: \"px-4 py-2 bg-cyan-600 text-white rounded-md hover:bg-cyan-700 disabled:opacity-50\",\n              children: isValidating ? 'Validating...' : 'Validate & Continue'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this), validationResult === 'success' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 text-green-400\",\n            children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n              className: \"h-5 w-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Credentials validated successfully!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setCurrentStep(3),\n              className: \"ml-4 px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700\",\n              children: \"Continue\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 15\n          }, this), validationResult === 'error' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 text-red-400\",\n            children: [/*#__PURE__*/_jsxDEV(ExclamationTriangleIcon, {\n              className: \"h-5 w-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Credential validation failed. Please check your credentials.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this);\n      case 3:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n              className: \"mx-auto h-12 w-12 text-cyan-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"mt-2 text-lg font-medium text-white\",\n              children: \"Configure Discovery\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-gray-400\",\n              children: \"Set parameters for resource discovery\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-300\",\n                children: \"Discovery Scope\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  children: \"All Resources\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  children: \"Compute Only\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  children: \"Storage Only\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  children: \"Network Only\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-300\",\n                children: \"Resource Tags Filter\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\",\n                placeholder: \"Environment=production,Team=platform\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                className: \"h-4 w-4 text-cyan-600 focus:ring-cyan-500 border-gray-600 rounded bg-gray-700\",\n                defaultChecked: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"ml-2 block text-sm text-gray-300\",\n                children: \"Cache results for faster subsequent searches\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setCurrentStep(2),\n              className: \"px-4 py-2 border border-gray-600 rounded-md text-gray-300 hover:bg-gray-700\",\n              children: \"Back\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: launchDiscovery,\n              className: \"px-4 py-2 bg-cyan-600 text-white rounded-md hover:bg-cyan-700\",\n              children: \"Launch Discovery\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this);\n      case 4:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n              className: \"mx-auto h-12 w-12 text-green-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"mt-2 text-lg font-medium text-white\",\n              children: \"Discovery Launched!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-gray-400\",\n              children: \"Resource discovery is now running in the background\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-700 rounded-lg p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-md font-medium text-white mb-2\",\n              children: \"Discovery Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-300\",\n                  children: \"Provider:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white\",\n                  children: credentials.provider.toUpperCase()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-300\",\n                  children: \"Status:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-green-400\",\n                  children: \"Running\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-300\",\n                  children: \"Resources Found:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white\",\n                  children: \"42\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => window.location.href = '/search',\n              className: \"px-6 py-2 bg-cyan-600 text-white rounded-md hover:bg-cyan-700\",\n              children: \"View Results\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this);\n      default:\n        return null;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold text-white\",\n        children: \"Discovery Wizard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 363,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-1 text-sm text-gray-400\",\n        children: \"Multi-step wizard to add credentials and launch discovery workflows\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 362,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 shadow rounded-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-4 py-5 sm:p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"nav\", {\n          \"aria-label\": \"Progress\",\n          children: /*#__PURE__*/_jsxDEV(\"ol\", {\n            className: \"flex items-center\",\n            children: steps.map((step, stepIdx) => /*#__PURE__*/_jsxDEV(\"li\", {\n              className: `${stepIdx !== steps.length - 1 ? 'pr-8 sm:pr-20' : ''} relative`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `relative flex h-8 w-8 items-center justify-center rounded-full ${step.id <= currentStep ? 'bg-cyan-600 text-white' : 'border-2 border-gray-600 bg-gray-800 text-gray-400'}`,\n                  children: step.id < currentStep ? /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 385,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium\",\n                    children: step.id\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 387,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ml-4 min-w-0 flex-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: `text-sm font-medium ${step.id <= currentStep ? 'text-white' : 'text-gray-400'}`,\n                    children: step.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 391,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-400\",\n                    children: step.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 394,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 390,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 19\n              }, this), stepIdx !== steps.length - 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute top-4 left-4 -ml-px mt-0.5 h-full w-0.5 bg-gray-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 21\n              }, this)]\n            }, step.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 371,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 370,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 shadow rounded-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-4 py-5 sm:p-6\",\n        children: renderStepContent()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 409,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 408,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 360,\n    columnNumber: 5\n  }, this);\n};\n_s(DiscoveryWizard, \"UwLn39iFgDiqHQw2V8oWQFKww0E=\", false, function () {\n  return [useDispatch];\n});\n_c = DiscoveryWizard;\nexport default DiscoveryWizard;\nvar _c;\n$RefreshReg$(_c, \"DiscoveryWizard\");", "map": {"version": 3, "names": ["React", "useState", "useDispatch", "searchResources", "CloudIcon", "KeyIcon", "MagnifyingGlassIcon", "CheckCircleIcon", "ExclamationTriangleIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "DiscoveryWizard", "_s", "dispatch", "currentStep", "setCurrentStep", "credentials", "setCredentials", "provider", "isValidating", "setIsValidating", "validationResult", "setValidationResult", "steps", "id", "name", "description", "providers", "icon", "fields", "handleProviderSelect", "providerId", "handleCredentialChange", "field", "value", "prev", "validateCredentials", "setTimeout", "launchDiscovery", "limit", "renderStepContent", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "onClick", "<PERSON><PERSON><PERSON><PERSON>", "find", "p", "type", "accessKey", "onChange", "e", "target", "placeholder", "secret<PERSON>ey", "region", "projectId", "subscriptionId", "tenantId", "disabled", "defaultChecked", "toUpperCase", "window", "location", "href", "step", "stepIdx", "length", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/DiscoveryWizard.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useDispatch } from 'react-redux';\nimport { AppDispatch } from '../store/store';\nimport { searchResources } from '../store/slices/searchSlice';\nimport {\n  CloudIcon,\n  KeyIcon,\n  MagnifyingGlassIcon,\n  CheckCircleIcon,\n  ExclamationTriangleIcon,\n} from '@heroicons/react/24/outline';\n\ninterface CredentialForm {\n  provider: 'aws' | 'gcp' | 'azure';\n  accessKey?: string;\n  secretKey?: string;\n  region?: string;\n  projectId?: string;\n  subscriptionId?: string;\n  tenantId?: string;\n}\n\nconst DiscoveryWizard: React.FC = () => {\n  const dispatch = useDispatch<AppDispatch>();\n  const [currentStep, setCurrentStep] = useState(1);\n  const [credentials, setCredentials] = useState<CredentialForm>({\n    provider: 'aws',\n  });\n  const [isValidating, setIsValidating] = useState(false);\n  const [validationResult, setValidationResult] = useState<'success' | 'error' | null>(null);\n\n  const steps = [\n    { id: 1, name: 'Choose Provider', description: 'Select your cloud provider' },\n    { id: 2, name: 'Add Credentials', description: 'Configure authentication' },\n    { id: 3, name: 'Configure Discovery', description: 'Set discovery parameters' },\n    { id: 4, name: 'Launch Discovery', description: 'Start resource discovery' },\n  ];\n\n  const providers = [\n    {\n      id: 'aws',\n      name: 'Amazon Web Services',\n      icon: '🟠',\n      description: 'Discover EC2, S3, RDS, and other AWS resources',\n      fields: ['accessKey', 'secretKey', 'region'],\n    },\n    {\n      id: 'gcp',\n      name: 'Google Cloud Platform',\n      icon: '🔵',\n      description: 'Discover Compute Engine, Cloud Storage, and GCP resources',\n      fields: ['projectId'],\n    },\n    {\n      id: 'azure',\n      name: 'Microsoft Azure',\n      icon: '🔷',\n      description: 'Discover Virtual Machines, Storage, and Azure resources',\n      fields: ['subscriptionId', 'tenantId'],\n    },\n  ];\n\n  const handleProviderSelect = (providerId: 'aws' | 'gcp' | 'azure') => {\n    setCredentials({ provider: providerId });\n    setCurrentStep(2);\n  };\n\n  const handleCredentialChange = (field: string, value: string) => {\n    setCredentials(prev => ({ ...prev, [field]: value }));\n  };\n\n  const validateCredentials = async () => {\n    setIsValidating(true);\n    setValidationResult(null);\n\n    // Simulate credential validation\n    setTimeout(() => {\n      setValidationResult('success');\n      setIsValidating(false);\n    }, 2000);\n  };\n\n  const launchDiscovery = () => {\n    // Launch discovery workflow\n    dispatch(searchResources({\n      provider: credentials.provider,\n      limit: 100,\n    }));\n    setCurrentStep(4);\n  };\n\n  const renderStepContent = () => {\n    switch (currentStep) {\n      case 1:\n        return (\n          <div className=\"space-y-6\">\n            <div className=\"text-center\">\n              <CloudIcon className=\"mx-auto h-12 w-12 text-cyan-400\" />\n              <h3 className=\"mt-2 text-lg font-medium text-white\">Choose Cloud Provider</h3>\n              <p className=\"mt-1 text-sm text-gray-400\">\n                Select the cloud provider you want to discover resources from\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-3\">\n              {providers.map((provider) => (\n                <button\n                  key={provider.id}\n                  onClick={() => handleProviderSelect(provider.id as any)}\n                  className=\"relative rounded-lg border border-gray-600 bg-gray-800 p-6 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-cyan-500 transition-colors\"\n                >\n                  <div className=\"text-center\">\n                    <div className=\"text-3xl mb-3\">{provider.icon}</div>\n                    <h4 className=\"text-lg font-medium text-white\">{provider.name}</h4>\n                    <p className=\"mt-2 text-sm text-gray-400\">{provider.description}</p>\n                  </div>\n                </button>\n              ))}\n            </div>\n          </div>\n        );\n\n      case 2:\n        const selectedProvider = providers.find(p => p.id === credentials.provider);\n        return (\n          <div className=\"space-y-6\">\n            <div className=\"text-center\">\n              <KeyIcon className=\"mx-auto h-12 w-12 text-cyan-400\" />\n              <h3 className=\"mt-2 text-lg font-medium text-white\">\n                Configure {selectedProvider?.name} Credentials\n              </h3>\n              <p className=\"mt-1 text-sm text-gray-400\">\n                Enter your authentication credentials for {selectedProvider?.name}\n              </p>\n            </div>\n\n            <div className=\"space-y-4\">\n              {credentials.provider === 'aws' && (\n                <>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-300\">Access Key ID</label>\n                    <input\n                      type=\"text\"\n                      className=\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\"\n                      value={credentials.accessKey || ''}\n                      onChange={(e) => handleCredentialChange('accessKey', e.target.value)}\n                      placeholder=\"AKIA...\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-300\">Secret Access Key</label>\n                    <input\n                      type=\"password\"\n                      className=\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\"\n                      value={credentials.secretKey || ''}\n                      onChange={(e) => handleCredentialChange('secretKey', e.target.value)}\n                      placeholder=\"••••••••••••••••••••••••••••••••••••••••\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-300\">Region</label>\n                    <select\n                      className=\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\"\n                      value={credentials.region || ''}\n                      onChange={(e) => handleCredentialChange('region', e.target.value)}\n                    >\n                      <option value=\"\">Select a region</option>\n                      <option value=\"us-east-1\">US East (N. Virginia)</option>\n                      <option value=\"us-west-2\">US West (Oregon)</option>\n                      <option value=\"eu-west-1\">Europe (Ireland)</option>\n                      <option value=\"ap-southeast-1\">Asia Pacific (Singapore)</option>\n                    </select>\n                  </div>\n                </>\n              )}\n\n              {credentials.provider === 'gcp' && (\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-300\">Project ID</label>\n                  <input\n                    type=\"text\"\n                    className=\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\"\n                    value={credentials.projectId || ''}\n                    onChange={(e) => handleCredentialChange('projectId', e.target.value)}\n                    placeholder=\"my-gcp-project\"\n                  />\n                </div>\n              )}\n\n              {credentials.provider === 'azure' && (\n                <>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-300\">Subscription ID</label>\n                    <input\n                      type=\"text\"\n                      className=\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\"\n                      value={credentials.subscriptionId || ''}\n                      onChange={(e) => handleCredentialChange('subscriptionId', e.target.value)}\n                      placeholder=\"xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-300\">Tenant ID</label>\n                    <input\n                      type=\"text\"\n                      className=\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\"\n                      value={credentials.tenantId || ''}\n                      onChange={(e) => handleCredentialChange('tenantId', e.target.value)}\n                      placeholder=\"xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx\"\n                    />\n                  </div>\n                </>\n              )}\n            </div>\n\n            <div className=\"flex justify-between\">\n              <button\n                onClick={() => setCurrentStep(1)}\n                className=\"px-4 py-2 border border-gray-600 rounded-md text-gray-300 hover:bg-gray-700\"\n              >\n                Back\n              </button>\n              <button\n                onClick={validateCredentials}\n                disabled={isValidating}\n                className=\"px-4 py-2 bg-cyan-600 text-white rounded-md hover:bg-cyan-700 disabled:opacity-50\"\n              >\n                {isValidating ? 'Validating...' : 'Validate & Continue'}\n              </button>\n            </div>\n\n            {validationResult === 'success' && (\n              <div className=\"flex items-center space-x-2 text-green-400\">\n                <CheckCircleIcon className=\"h-5 w-5\" />\n                <span>Credentials validated successfully!</span>\n                <button\n                  onClick={() => setCurrentStep(3)}\n                  className=\"ml-4 px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700\"\n                >\n                  Continue\n                </button>\n              </div>\n            )}\n\n            {validationResult === 'error' && (\n              <div className=\"flex items-center space-x-2 text-red-400\">\n                <ExclamationTriangleIcon className=\"h-5 w-5\" />\n                <span>Credential validation failed. Please check your credentials.</span>\n              </div>\n            )}\n          </div>\n        );\n\n      case 3:\n        return (\n          <div className=\"space-y-6\">\n            <div className=\"text-center\">\n              <MagnifyingGlassIcon className=\"mx-auto h-12 w-12 text-cyan-400\" />\n              <h3 className=\"mt-2 text-lg font-medium text-white\">Configure Discovery</h3>\n              <p className=\"mt-1 text-sm text-gray-400\">\n                Set parameters for resource discovery\n              </p>\n            </div>\n\n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300\">Discovery Scope</label>\n                <select className=\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\">\n                  <option>All Resources</option>\n                  <option>Compute Only</option>\n                  <option>Storage Only</option>\n                  <option>Network Only</option>\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300\">Resource Tags Filter</label>\n                <input\n                  type=\"text\"\n                  className=\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\"\n                  placeholder=\"Environment=production,Team=platform\"\n                />\n              </div>\n\n              <div className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  className=\"h-4 w-4 text-cyan-600 focus:ring-cyan-500 border-gray-600 rounded bg-gray-700\"\n                  defaultChecked\n                />\n                <label className=\"ml-2 block text-sm text-gray-300\">\n                  Cache results for faster subsequent searches\n                </label>\n              </div>\n            </div>\n\n            <div className=\"flex justify-between\">\n              <button\n                onClick={() => setCurrentStep(2)}\n                className=\"px-4 py-2 border border-gray-600 rounded-md text-gray-300 hover:bg-gray-700\"\n              >\n                Back\n              </button>\n              <button\n                onClick={launchDiscovery}\n                className=\"px-4 py-2 bg-cyan-600 text-white rounded-md hover:bg-cyan-700\"\n              >\n                Launch Discovery\n              </button>\n            </div>\n          </div>\n        );\n\n      case 4:\n        return (\n          <div className=\"space-y-6\">\n            <div className=\"text-center\">\n              <CheckCircleIcon className=\"mx-auto h-12 w-12 text-green-400\" />\n              <h3 className=\"mt-2 text-lg font-medium text-white\">Discovery Launched!</h3>\n              <p className=\"mt-1 text-sm text-gray-400\">\n                Resource discovery is now running in the background\n              </p>\n            </div>\n\n            <div className=\"bg-gray-700 rounded-lg p-4\">\n              <h4 className=\"text-md font-medium text-white mb-2\">Discovery Status</h4>\n              <div className=\"space-y-2\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-300\">Provider:</span>\n                  <span className=\"text-white\">{credentials.provider.toUpperCase()}</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-300\">Status:</span>\n                  <span className=\"text-green-400\">Running</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-300\">Resources Found:</span>\n                  <span className=\"text-white\">42</span>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex justify-center\">\n              <button\n                onClick={() => window.location.href = '/search'}\n                className=\"px-6 py-2 bg-cyan-600 text-white rounded-md hover:bg-cyan-700\"\n              >\n                View Results\n              </button>\n            </div>\n          </div>\n        );\n\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div>\n        <h1 className=\"text-2xl font-bold text-white\">Discovery Wizard</h1>\n        <p className=\"mt-1 text-sm text-gray-400\">\n          Multi-step wizard to add credentials and launch discovery workflows\n        </p>\n      </div>\n\n      {/* Progress Steps */}\n      <div className=\"bg-gray-800 shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <nav aria-label=\"Progress\">\n            <ol className=\"flex items-center\">\n              {steps.map((step, stepIdx) => (\n                <li key={step.id} className={`${stepIdx !== steps.length - 1 ? 'pr-8 sm:pr-20' : ''} relative`}>\n                  <div className=\"flex items-center\">\n                    <div\n                      className={`relative flex h-8 w-8 items-center justify-center rounded-full ${\n                        step.id <= currentStep\n                          ? 'bg-cyan-600 text-white'\n                          : 'border-2 border-gray-600 bg-gray-800 text-gray-400'\n                      }`}\n                    >\n                      {step.id < currentStep ? (\n                        <CheckCircleIcon className=\"h-5 w-5\" />\n                      ) : (\n                        <span className=\"text-sm font-medium\">{step.id}</span>\n                      )}\n                    </div>\n                    <div className=\"ml-4 min-w-0 flex-1\">\n                      <p className={`text-sm font-medium ${step.id <= currentStep ? 'text-white' : 'text-gray-400'}`}>\n                        {step.name}\n                      </p>\n                      <p className=\"text-sm text-gray-400\">{step.description}</p>\n                    </div>\n                  </div>\n                  {stepIdx !== steps.length - 1 && (\n                    <div className=\"absolute top-4 left-4 -ml-px mt-0.5 h-full w-0.5 bg-gray-600\" />\n                  )}\n                </li>\n              ))}\n            </ol>\n          </nav>\n        </div>\n      </div>\n\n      {/* Step Content */}\n      <div className=\"bg-gray-800 shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          {renderStepContent()}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default DiscoveryWizard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,aAAa;AAEzC,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SACEC,SAAS,EACTC,OAAO,EACPC,mBAAmB,EACnBC,eAAe,EACfC,uBAAuB,QAClB,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAYrC,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAMC,QAAQ,GAAGb,WAAW,CAAc,CAAC;EAC3C,MAAM,CAACc,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAiB;IAC7DmB,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACsB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvB,QAAQ,CAA6B,IAAI,CAAC;EAE1F,MAAMwB,KAAK,GAAG,CACZ;IAAEC,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,iBAAiB;IAAEC,WAAW,EAAE;EAA6B,CAAC,EAC7E;IAAEF,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,iBAAiB;IAAEC,WAAW,EAAE;EAA2B,CAAC,EAC3E;IAAEF,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,qBAAqB;IAAEC,WAAW,EAAE;EAA2B,CAAC,EAC/E;IAAEF,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,kBAAkB;IAAEC,WAAW,EAAE;EAA2B,CAAC,CAC7E;EAED,MAAMC,SAAS,GAAG,CAChB;IACEH,EAAE,EAAE,KAAK;IACTC,IAAI,EAAE,qBAAqB;IAC3BG,IAAI,EAAE,IAAI;IACVF,WAAW,EAAE,gDAAgD;IAC7DG,MAAM,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,QAAQ;EAC7C,CAAC,EACD;IACEL,EAAE,EAAE,KAAK;IACTC,IAAI,EAAE,uBAAuB;IAC7BG,IAAI,EAAE,IAAI;IACVF,WAAW,EAAE,2DAA2D;IACxEG,MAAM,EAAE,CAAC,WAAW;EACtB,CAAC,EACD;IACEL,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,iBAAiB;IACvBG,IAAI,EAAE,IAAI;IACVF,WAAW,EAAE,yDAAyD;IACtEG,MAAM,EAAE,CAAC,gBAAgB,EAAE,UAAU;EACvC,CAAC,CACF;EAED,MAAMC,oBAAoB,GAAIC,UAAmC,IAAK;IACpEd,cAAc,CAAC;MAAEC,QAAQ,EAAEa;IAAW,CAAC,CAAC;IACxChB,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC;EAED,MAAMiB,sBAAsB,GAAGA,CAACC,KAAa,EAAEC,KAAa,KAAK;IAC/DjB,cAAc,CAACkB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;EACvD,CAAC;EAED,MAAME,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtChB,eAAe,CAAC,IAAI,CAAC;IACrBE,mBAAmB,CAAC,IAAI,CAAC;;IAEzB;IACAe,UAAU,CAAC,MAAM;MACff,mBAAmB,CAAC,SAAS,CAAC;MAC9BF,eAAe,CAAC,KAAK,CAAC;IACxB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMkB,eAAe,GAAGA,CAAA,KAAM;IAC5B;IACAzB,QAAQ,CAACZ,eAAe,CAAC;MACvBiB,QAAQ,EAAEF,WAAW,CAACE,QAAQ;MAC9BqB,KAAK,EAAE;IACT,CAAC,CAAC,CAAC;IACHxB,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC;EAED,MAAMyB,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,QAAQ1B,WAAW;MACjB,KAAK,CAAC;QACJ,oBACEN,OAAA;UAAKiC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBlC,OAAA;YAAKiC,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BlC,OAAA,CAACN,SAAS;cAACuC,SAAS,EAAC;YAAiC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzDtC,OAAA;cAAIiC,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9EtC,OAAA;cAAGiC,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE1C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENtC,OAAA;YAAKiC,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EACnDf,SAAS,CAACoB,GAAG,CAAE7B,QAAQ,iBACtBV,OAAA;cAEEwC,OAAO,EAAEA,CAAA,KAAMlB,oBAAoB,CAACZ,QAAQ,CAACM,EAAS,CAAE;cACxDiB,SAAS,EAAC,oJAAoJ;cAAAC,QAAA,eAE9JlC,OAAA;gBAAKiC,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BlC,OAAA;kBAAKiC,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAExB,QAAQ,CAACU;gBAAI;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpDtC,OAAA;kBAAIiC,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAAExB,QAAQ,CAACO;gBAAI;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnEtC,OAAA;kBAAGiC,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAExB,QAAQ,CAACQ;gBAAW;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE;YAAC,GARD5B,QAAQ,CAACM,EAAE;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASV,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,CAAC;QACJ,MAAMG,gBAAgB,GAAGtB,SAAS,CAACuB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC3B,EAAE,KAAKR,WAAW,CAACE,QAAQ,CAAC;QAC3E,oBACEV,OAAA;UAAKiC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBlC,OAAA;YAAKiC,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BlC,OAAA,CAACL,OAAO;cAACsC,SAAS,EAAC;YAAiC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvDtC,OAAA;cAAIiC,SAAS,EAAC,qCAAqC;cAAAC,QAAA,GAAC,YACxC,EAACO,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAExB,IAAI,EAAC,cACpC;YAAA;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLtC,OAAA;cAAGiC,SAAS,EAAC,4BAA4B;cAAAC,QAAA,GAAC,4CACE,EAACO,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAExB,IAAI;YAAA;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENtC,OAAA;YAAKiC,SAAS,EAAC,WAAW;YAAAC,QAAA,GACvB1B,WAAW,CAACE,QAAQ,KAAK,KAAK,iBAC7BV,OAAA,CAAAE,SAAA;cAAAgC,QAAA,gBACElC,OAAA;gBAAAkC,QAAA,gBACElC,OAAA;kBAAOiC,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAChFtC,OAAA;kBACE4C,IAAI,EAAC,MAAM;kBACXX,SAAS,EAAC,0IAA0I;kBACpJP,KAAK,EAAElB,WAAW,CAACqC,SAAS,IAAI,EAAG;kBACnCC,QAAQ,EAAGC,CAAC,IAAKvB,sBAAsB,CAAC,WAAW,EAAEuB,CAAC,CAACC,MAAM,CAACtB,KAAK,CAAE;kBACrEuB,WAAW,EAAC;gBAAS;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtC,OAAA;gBAAAkC,QAAA,gBACElC,OAAA;kBAAOiC,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACpFtC,OAAA;kBACE4C,IAAI,EAAC,UAAU;kBACfX,SAAS,EAAC,0IAA0I;kBACpJP,KAAK,EAAElB,WAAW,CAAC0C,SAAS,IAAI,EAAG;kBACnCJ,QAAQ,EAAGC,CAAC,IAAKvB,sBAAsB,CAAC,WAAW,EAAEuB,CAAC,CAACC,MAAM,CAACtB,KAAK,CAAE;kBACrEuB,WAAW,EAAC;gBAA0C;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtC,OAAA;gBAAAkC,QAAA,gBACElC,OAAA;kBAAOiC,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzEtC,OAAA;kBACEiC,SAAS,EAAC,0IAA0I;kBACpJP,KAAK,EAAElB,WAAW,CAAC2C,MAAM,IAAI,EAAG;kBAChCL,QAAQ,EAAGC,CAAC,IAAKvB,sBAAsB,CAAC,QAAQ,EAAEuB,CAAC,CAACC,MAAM,CAACtB,KAAK,CAAE;kBAAAQ,QAAA,gBAElElC,OAAA;oBAAQ0B,KAAK,EAAC,EAAE;oBAAAQ,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACzCtC,OAAA;oBAAQ0B,KAAK,EAAC,WAAW;oBAAAQ,QAAA,EAAC;kBAAqB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACxDtC,OAAA;oBAAQ0B,KAAK,EAAC,WAAW;oBAAAQ,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACnDtC,OAAA;oBAAQ0B,KAAK,EAAC,WAAW;oBAAAQ,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACnDtC,OAAA;oBAAQ0B,KAAK,EAAC,gBAAgB;oBAAAQ,QAAA,EAAC;kBAAwB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA,eACN,CACH,EAEA9B,WAAW,CAACE,QAAQ,KAAK,KAAK,iBAC7BV,OAAA;cAAAkC,QAAA,gBACElC,OAAA;gBAAOiC,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7EtC,OAAA;gBACE4C,IAAI,EAAC,MAAM;gBACXX,SAAS,EAAC,0IAA0I;gBACpJP,KAAK,EAAElB,WAAW,CAAC4C,SAAS,IAAI,EAAG;gBACnCN,QAAQ,EAAGC,CAAC,IAAKvB,sBAAsB,CAAC,WAAW,EAAEuB,CAAC,CAACC,MAAM,CAACtB,KAAK,CAAE;gBACrEuB,WAAW,EAAC;cAAgB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN,EAEA9B,WAAW,CAACE,QAAQ,KAAK,OAAO,iBAC/BV,OAAA,CAAAE,SAAA;cAAAgC,QAAA,gBACElC,OAAA;gBAAAkC,QAAA,gBACElC,OAAA;kBAAOiC,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClFtC,OAAA;kBACE4C,IAAI,EAAC,MAAM;kBACXX,SAAS,EAAC,0IAA0I;kBACpJP,KAAK,EAAElB,WAAW,CAAC6C,cAAc,IAAI,EAAG;kBACxCP,QAAQ,EAAGC,CAAC,IAAKvB,sBAAsB,CAAC,gBAAgB,EAAEuB,CAAC,CAACC,MAAM,CAACtB,KAAK,CAAE;kBAC1EuB,WAAW,EAAC;gBAAsC;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtC,OAAA;gBAAAkC,QAAA,gBACElC,OAAA;kBAAOiC,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5EtC,OAAA;kBACE4C,IAAI,EAAC,MAAM;kBACXX,SAAS,EAAC,0IAA0I;kBACpJP,KAAK,EAAElB,WAAW,CAAC8C,QAAQ,IAAI,EAAG;kBAClCR,QAAQ,EAAGC,CAAC,IAAKvB,sBAAsB,CAAC,UAAU,EAAEuB,CAAC,CAACC,MAAM,CAACtB,KAAK,CAAE;kBACpEuB,WAAW,EAAC;gBAAsC;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA,eACN,CACH;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENtC,OAAA;YAAKiC,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnClC,OAAA;cACEwC,OAAO,EAAEA,CAAA,KAAMjC,cAAc,CAAC,CAAC,CAAE;cACjC0B,SAAS,EAAC,6EAA6E;cAAAC,QAAA,EACxF;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTtC,OAAA;cACEwC,OAAO,EAAEZ,mBAAoB;cAC7B2B,QAAQ,EAAE5C,YAAa;cACvBsB,SAAS,EAAC,mFAAmF;cAAAC,QAAA,EAE5FvB,YAAY,GAAG,eAAe,GAAG;YAAqB;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAELzB,gBAAgB,KAAK,SAAS,iBAC7Bb,OAAA;YAAKiC,SAAS,EAAC,4CAA4C;YAAAC,QAAA,gBACzDlC,OAAA,CAACH,eAAe;cAACoC,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvCtC,OAAA;cAAAkC,QAAA,EAAM;YAAmC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChDtC,OAAA;cACEwC,OAAO,EAAEA,CAAA,KAAMjC,cAAc,CAAC,CAAC,CAAE;cACjC0B,SAAS,EAAC,2EAA2E;cAAAC,QAAA,EACtF;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,EAEAzB,gBAAgB,KAAK,OAAO,iBAC3Bb,OAAA;YAAKiC,SAAS,EAAC,0CAA0C;YAAAC,QAAA,gBACvDlC,OAAA,CAACF,uBAAuB;cAACmC,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/CtC,OAAA;cAAAkC,QAAA,EAAM;YAA4D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAGV,KAAK,CAAC;QACJ,oBACEtC,OAAA;UAAKiC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBlC,OAAA;YAAKiC,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BlC,OAAA,CAACJ,mBAAmB;cAACqC,SAAS,EAAC;YAAiC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnEtC,OAAA;cAAIiC,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5EtC,OAAA;cAAGiC,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE1C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENtC,OAAA;YAAKiC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBlC,OAAA;cAAAkC,QAAA,gBACElC,OAAA;gBAAOiC,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAClFtC,OAAA;gBAAQiC,SAAS,EAAC,0IAA0I;gBAAAC,QAAA,gBAC1JlC,OAAA;kBAAAkC,QAAA,EAAQ;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9BtC,OAAA;kBAAAkC,QAAA,EAAQ;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC7BtC,OAAA;kBAAAkC,QAAA,EAAQ;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC7BtC,OAAA;kBAAAkC,QAAA,EAAQ;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENtC,OAAA;cAAAkC,QAAA,gBACElC,OAAA;gBAAOiC,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAC;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvFtC,OAAA;gBACE4C,IAAI,EAAC,MAAM;gBACXX,SAAS,EAAC,0IAA0I;gBACpJgB,WAAW,EAAC;cAAsC;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENtC,OAAA;cAAKiC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChClC,OAAA;gBACE4C,IAAI,EAAC,UAAU;gBACfX,SAAS,EAAC,+EAA+E;gBACzFuB,cAAc;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eACFtC,OAAA;gBAAOiC,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAEpD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtC,OAAA;YAAKiC,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnClC,OAAA;cACEwC,OAAO,EAAEA,CAAA,KAAMjC,cAAc,CAAC,CAAC,CAAE;cACjC0B,SAAS,EAAC,6EAA6E;cAAAC,QAAA,EACxF;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTtC,OAAA;cACEwC,OAAO,EAAEV,eAAgB;cACzBG,SAAS,EAAC,+DAA+D;cAAAC,QAAA,EAC1E;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,CAAC;QACJ,oBACEtC,OAAA;UAAKiC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBlC,OAAA;YAAKiC,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BlC,OAAA,CAACH,eAAe;cAACoC,SAAS,EAAC;YAAkC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChEtC,OAAA;cAAIiC,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5EtC,OAAA;cAAGiC,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE1C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENtC,OAAA;YAAKiC,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzClC,OAAA;cAAIiC,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzEtC,OAAA;cAAKiC,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBlC,OAAA;gBAAKiC,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBACnClC,OAAA;kBAAMiC,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAChDtC,OAAA;kBAAMiC,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAE1B,WAAW,CAACE,QAAQ,CAAC+C,WAAW,CAAC;gBAAC;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC,eACNtC,OAAA;gBAAKiC,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBACnClC,OAAA;kBAAMiC,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9CtC,OAAA;kBAAMiC,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC,eACNtC,OAAA;gBAAKiC,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBACnClC,OAAA;kBAAMiC,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvDtC,OAAA;kBAAMiC,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtC,OAAA;YAAKiC,SAAS,EAAC,qBAAqB;YAAAC,QAAA,eAClClC,OAAA;cACEwC,OAAO,EAAEA,CAAA,KAAMkB,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,SAAU;cAChD3B,SAAS,EAAC,+DAA+D;cAAAC,QAAA,EAC1E;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,oBACEtC,OAAA;IAAKiC,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBlC,OAAA;MAAAkC,QAAA,gBACElC,OAAA;QAAIiC,SAAS,EAAC,+BAA+B;QAAAC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnEtC,OAAA;QAAGiC,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAE1C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNtC,OAAA;MAAKiC,SAAS,EAAC,+BAA+B;MAAAC,QAAA,eAC5ClC,OAAA;QAAKiC,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BlC,OAAA;UAAK,cAAW,UAAU;UAAAkC,QAAA,eACxBlC,OAAA;YAAIiC,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAC9BnB,KAAK,CAACwB,GAAG,CAAC,CAACsB,IAAI,EAAEC,OAAO,kBACvB9D,OAAA;cAAkBiC,SAAS,EAAE,GAAG6B,OAAO,KAAK/C,KAAK,CAACgD,MAAM,GAAG,CAAC,GAAG,eAAe,GAAG,EAAE,WAAY;cAAA7B,QAAA,gBAC7FlC,OAAA;gBAAKiC,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChClC,OAAA;kBACEiC,SAAS,EAAE,kEACT4B,IAAI,CAAC7C,EAAE,IAAIV,WAAW,GAClB,wBAAwB,GACxB,oDAAoD,EACvD;kBAAA4B,QAAA,EAEF2B,IAAI,CAAC7C,EAAE,GAAGV,WAAW,gBACpBN,OAAA,CAACH,eAAe;oBAACoC,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAEvCtC,OAAA;oBAAMiC,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,EAAE2B,IAAI,CAAC7C;kBAAE;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBACtD;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACNtC,OAAA;kBAAKiC,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,gBAClClC,OAAA;oBAAGiC,SAAS,EAAE,uBAAuB4B,IAAI,CAAC7C,EAAE,IAAIV,WAAW,GAAG,YAAY,GAAG,eAAe,EAAG;oBAAA4B,QAAA,EAC5F2B,IAAI,CAAC5C;kBAAI;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACJtC,OAAA;oBAAGiC,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAE2B,IAAI,CAAC3C;kBAAW;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACLwB,OAAO,KAAK/C,KAAK,CAACgD,MAAM,GAAG,CAAC,iBAC3B/D,OAAA;gBAAKiC,SAAS,EAAC;cAA8D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAChF;YAAA,GAxBMuB,IAAI,CAAC7C,EAAE;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyBZ,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtC,OAAA;MAAKiC,SAAS,EAAC,+BAA+B;MAAAC,QAAA,eAC5ClC,OAAA;QAAKiC,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAC9BF,iBAAiB,CAAC;MAAC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClC,EAAA,CAxYID,eAAyB;EAAA,QACZX,WAAW;AAAA;AAAAwE,EAAA,GADxB7D,eAAyB;AA0Y/B,eAAeA,eAAe;AAAC,IAAA6D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}