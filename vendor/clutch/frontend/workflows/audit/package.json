{"name": "@clutch-sh/audit", "version": "4.0.0-beta", "description": " Clutch Audit Workflows", "license": "Apache-2.0", "author": "<EMAIL>", "main": "dist/index.js", "scripts": {"build": "yarn clean && yarn compile", "clean": "yarn run package:clean", "compile": "yarn run package:compile", "compile:dev": "yarn run package:compile:dev", "compile:watch": "yarn run package:compile:watch", "lint": "yarn run package:lint", "lint:fix": "yarn run lint --fix", "publishBeta": "../../../tools/publish-frontend.sh audit", "test": "yarn run package:test", "test:coverage": "yarn run test --collect-coverage", "test:watch": "yarn run test --watch"}, "dependencies": {"@clutch-sh/api": "workspace:^", "@clutch-sh/core": "workspace:^", "file-saver": "^2.0.5", "react-is": "^17.0.2", "react-json-view": "^1.21.3"}, "devDependencies": {"@clutch-sh/tools": "workspace:^"}, "peerDependencies": {"@mui/icons-material": "^5.11.0", "@mui/material": "^5.11.0", "react": "^17.0.2", "react-dom": "^17.0.2", "typescript": "^4.2.3"}, "engines": {"node": ">=18 <19", "yarn": "^4.5.0"}, "packageManager": "yarn@4.5.0", "stableVersion": "3.0.0-beta"}