package aws

import (
	"context"
	"fmt"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/ec2"
	"github.com/aws/aws-sdk-go-v2/service/ec2/types"
	"go.uber.org/zap"
)

// Service represents the AWS service
type Service struct {
	logger   *zap.Logger
	ec2Client *ec2.Client
}

// Resource represents a cloud resource
type Resource struct {
	Provider string            `json:"provider"`
	Type     string            `json:"type"`
	ID       string            `json:"id"`
	Name     string            `json:"name"`
	Region   string            `json:"region"`
	Tags     map[string]string `json:"tags"`
	State    string            `json:"state"`
}

// New creates a new AWS service
func New(logger *zap.Logger) (*Service, error) {
	// Load AWS configuration
	cfg, err := config.LoadDefaultConfig(context.TODO())
	if err != nil {
		return nil, fmt.Errorf("failed to load AWS config: %w", err)
	}

	return &Service{
		logger:   logger,
		ec2Client: ec2.NewFromConfig(cfg),
	}, nil
}

// DiscoverEC2Instances discovers EC2 instances
func (s *Service) DiscoverEC2Instances(ctx context.Context) ([]Resource, error) {
	s.logger.Info("Discovering EC2 instances")

	// Describe instances
	result, err := s.ec2Client.DescribeInstances(ctx, &ec2.DescribeInstancesInput{})
	if err != nil {
		s.logger.Error("Failed to describe EC2 instances", zap.Error(err))
		return nil, fmt.Errorf("failed to describe instances: %w", err)
	}

	var resources []Resource
	for _, reservation := range result.Reservations {
		for _, instance := range reservation.Instances {
			resource := Resource{
				Provider: "aws",
				Type:     "ec2.instance",
				ID:       aws.ToString(instance.InstanceId),
				Region:   aws.ToString(instance.Placement.AvailabilityZone),
				State:    string(instance.State.Name),
				Tags:     make(map[string]string),
			}

			// Extract name from tags
			for _, tag := range instance.Tags {
				if aws.ToString(tag.Key) == "Name" {
					resource.Name = aws.ToString(tag.Value)
				}
				resource.Tags[aws.ToString(tag.Key)] = aws.ToString(tag.Value)
			}

			// Use instance ID as name if no Name tag
			if resource.Name == "" {
				resource.Name = resource.ID
			}

			resources = append(resources, resource)
		}
	}

	s.logger.Info("Discovered EC2 instances", zap.Int("count", len(resources)))
	return resources, nil
}

// DiscoverVPCs discovers VPCs
func (s *Service) DiscoverVPCs(ctx context.Context) ([]Resource, error) {
	s.logger.Info("Discovering VPCs")

	result, err := s.ec2Client.DescribeVpcs(ctx, &ec2.DescribeVpcsInput{})
	if err != nil {
		s.logger.Error("Failed to describe VPCs", zap.Error(err))
		return nil, fmt.Errorf("failed to describe VPCs: %w", err)
	}

	var resources []Resource
	for _, vpc := range result.Vpcs {
		resource := Resource{
			Provider: "aws",
			Type:     "ec2.vpc",
			ID:       aws.ToString(vpc.VpcId),
			State:    string(vpc.State),
			Tags:     make(map[string]string),
		}

		// Extract name from tags
		for _, tag := range vpc.Tags {
			if aws.ToString(tag.Key) == "Name" {
				resource.Name = aws.ToString(tag.Value)
			}
			resource.Tags[aws.ToString(tag.Key)] = aws.ToString(tag.Value)
		}

		// Use VPC ID as name if no Name tag
		if resource.Name == "" {
			resource.Name = resource.ID
		}

		resources = append(resources, resource)
	}

	s.logger.Info("Discovered VPCs", zap.Int("count", len(resources)))
	return resources, nil
}

// DiscoverSecurityGroups discovers security groups
func (s *Service) DiscoverSecurityGroups(ctx context.Context) ([]Resource, error) {
	s.logger.Info("Discovering Security Groups")

	result, err := s.ec2Client.DescribeSecurityGroups(ctx, &ec2.DescribeSecurityGroupsInput{})
	if err != nil {
		s.logger.Error("Failed to describe security groups", zap.Error(err))
		return nil, fmt.Errorf("failed to describe security groups: %w", err)
	}

	var resources []Resource
	for _, sg := range result.SecurityGroups {
		resource := Resource{
			Provider: "aws",
			Type:     "ec2.security_group",
			ID:       aws.ToString(sg.GroupId),
			Name:     aws.ToString(sg.GroupName),
			Tags:     make(map[string]string),
		}

		// Extract tags
		for _, tag := range sg.Tags {
			resource.Tags[aws.ToString(tag.Key)] = aws.ToString(tag.Value)
		}

		resources = append(resources, resource)
	}

	s.logger.Info("Discovered Security Groups", zap.Int("count", len(resources)))
	return resources, nil
}

// SearchResources searches for resources based on criteria
func (s *Service) SearchResources(ctx context.Context, resourceType string, filters map[string]string) ([]Resource, error) {
	s.logger.Info("Searching AWS resources", 
		zap.String("type", resourceType), 
		zap.Any("filters", filters))

	var resources []Resource
	var err error

	switch resourceType {
	case "ec2.instance", "instance", "":
		resources, err = s.DiscoverEC2Instances(ctx)
	case "ec2.vpc", "vpc":
		resources, err = s.DiscoverVPCs(ctx)
	case "ec2.security_group", "security_group":
		resources, err = s.DiscoverSecurityGroups(ctx)
	default:
		// If no specific type, discover all
		instances, err1 := s.DiscoverEC2Instances(ctx)
		if err1 != nil {
			s.logger.Warn("Failed to discover EC2 instances", zap.Error(err1))
		} else {
			resources = append(resources, instances...)
		}

		vpcs, err2 := s.DiscoverVPCs(ctx)
		if err2 != nil {
			s.logger.Warn("Failed to discover VPCs", zap.Error(err2))
		} else {
			resources = append(resources, vpcs...)
		}

		sgs, err3 := s.DiscoverSecurityGroups(ctx)
		if err3 != nil {
			s.logger.Warn("Failed to discover security groups", zap.Error(err3))
		} else {
			resources = append(resources, sgs...)
		}
	}

	if err != nil {
		return nil, err
	}

	// Apply filters
	if len(filters) > 0 {
		filtered := make([]Resource, 0)
		for _, resource := range resources {
			matches := true
			for key, value := range filters {
				if tagValue, exists := resource.Tags[key]; !exists || tagValue != value {
					matches = false
					break
				}
			}
			if matches {
				filtered = append(filtered, resource)
			}
		}
		resources = filtered
	}

	s.logger.Info("Search completed", zap.Int("results", len(resources)))
	return resources, nil
}

// GetResource gets a specific resource by ID
func (s *Service) GetResource(ctx context.Context, resourceID string) (*Resource, error) {
	s.logger.Info("Getting AWS resource", zap.String("id", resourceID))

	// Try to find the resource by searching all types
	resources, err := s.SearchResources(ctx, "", nil)
	if err != nil {
		return nil, err
	}

	for _, resource := range resources {
		if resource.ID == resourceID {
			return &resource, nil
		}
	}

	return nil, fmt.Errorf("resource not found: %s", resourceID)
}

// Health checks the health of the AWS service
func (s *Service) Health(ctx context.Context) error {
	// Try to make a simple API call to verify connectivity
	_, err := s.ec2Client.DescribeRegions(ctx, &ec2.DescribeRegionsInput{})
	if err != nil {
		return fmt.Errorf("AWS health check failed: %w", err)
	}
	return nil
}
