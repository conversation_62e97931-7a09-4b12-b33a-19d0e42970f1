gateway:
  logger:
    pretty: true
    level: DEBUG
  stats:
    flush_interval: 1s
  middleware:
    - name: clutch.middleware.stats
    - name: clutch.middleware.validate
    - name: clutch.middleware.audit
  listener:
    tcp:
      address: 0.0.0.0
      port: 8080
      secure: false
modules:
  - name: clutch.module.healthcheck
  - name: clutch.module.resolver
  - name: clutch.module.aws
  - name: clutch.module.audit
services:
  - name: clutch.service.aws
    typed_config:
      "@type": types.google.com/clutch.config.service.aws.v1.Config
      regions:
        - us-east-1
        - us-west-2
  - name: clutch.service.db.postgres
    typed_config:
      "@type": types.google.com/clutch.config.service.db.postgres.v1.Config
      connection:
        host: localhost
        port: 5432
        user: clutch
        ssl_mode: DISABLE
        dbname: clutch
        password: clutch
  - name: clutch.service.audit.sink.logger
    typed_config:
      "@type": types.google.com/clutch.config.service.audit.v1.SinkConfig
  - name: clutch.service.audit
    typed_config:
      "@type": types.google.com/clutch.config.service.audit.v1.Config
      db_provider: clutch.service.db.postgres
      sinks:
        - clutch.service.audit.sink.logger
      filter:
        denylist: True
        rules:
            - field: METHOD
              text: Healthcheck
resolvers:
  - name: clutch.resolver.aws
