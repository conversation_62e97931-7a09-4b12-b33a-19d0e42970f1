{"name": "@clutch-sh/amiibo", "version": "0.1.0", "private": true, "description": " Lookup all Amiibo by name", "license": "Apache-2.0", "author": "<EMAIL>", "main": "dist/index.js", "scripts": {"clean": "rm -rf ./dist && rm -f tsconfig.tsbuildinfo", "compile": "tsc -b", "compile:dev": "esbuild --target=es2019 --outdir=dist --sourcemap src/*.tsx", "compile:watch": "yarn compile:dev --watch", "lint": "eslint --ext .js,.jsx,.ts,.tsx .", "lint:fix": "yarn run lint --fix", "test": "jest --passWithNoTests", "test:coverage": "yarn run test --collect-coverage", "test:watch": "yarn run test --watch"}, "dependencies": {"@clutch-sh/core": "4.0.0-beta", "@clutch-sh/data-layout": "4.0.0-beta", "@clutch-sh/wizard": "4.0.0-beta", "lodash": "^4.17.0", "react-dom": "^17.0.2", "react": "^17.0.2", "typescript": "^4.2.3"}, "devDependencies": {"@clutch-sh/tools": "4.0.0-beta", "eslint": "^8.3.0", "jest": "^27.0.0"}, "engines": {"node": ">=18 <19", "yarn": "^4.5.0"}}