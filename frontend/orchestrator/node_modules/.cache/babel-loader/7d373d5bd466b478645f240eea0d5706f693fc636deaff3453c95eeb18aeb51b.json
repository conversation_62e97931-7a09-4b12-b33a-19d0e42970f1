{"ast": null, "code": "import _objectSpread from\"/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useEffect,useState}from'react';import{useDispatch,useSelector}from'react-redux';import{fetchEnvoyConfigs,fetchEnvoyNodes,createEnvoyConfig,setSelectedConfig}from'../store/slices/envoySlice';import{GlobeAltIcon,PlusIcon,DocumentTextIcon}from'@heroicons/react/24/outline';import Editor from'@monaco-editor/react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const EnvoyConfigEditor=()=>{const dispatch=useDispatch();const{configs,nodes,selectedConfig,loading,error}=useSelector(state=>state.envoy);const[showCreateForm,setShowCreateForm]=useState(false);const[newConfig,setNewConfig]=useState({node_id:'',cluster_name:'',config:'',version:'1.0.0'});useEffect(()=>{dispatch(fetchEnvoyConfigs());dispatch(fetchEnvoyNodes());},[dispatch]);const handleCreateConfig=()=>{dispatch(createEnvoyConfig(newConfig));setNewConfig({node_id:'',cluster_name:'',config:'',version:'1.0.0'});setShowCreateForm(false);};return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-white\",children:\"Envoy Configuration\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-gray-400\",children:\"Manage Envoy proxy configurations and deployments\"})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setShowCreateForm(true),className:\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500\",children:[/*#__PURE__*/_jsx(PlusIcon,{className:\"h-4 w-4 mr-2\"}),\"New Config\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 lg:grid-cols-2 gap-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-gray-800 shadow rounded-lg\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"px-4 py-5 sm:p-6\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg leading-6 font-medium text-white mb-4\",children:\"Configurations\"}),loading?/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center h-32\",children:/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500\"})}):/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-3\",children:[configs.map(config=>/*#__PURE__*/_jsx(\"div\",{className:\"border rounded-lg p-4 cursor-pointer transition-colors \".concat((selectedConfig===null||selectedConfig===void 0?void 0:selectedConfig.id)===config.id?'border-cyan-500 bg-cyan-500/10':'border-gray-600 hover:bg-gray-700'),onClick:()=>dispatch(setSelectedConfig(config)),children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{className:\"text-lg font-medium text-white\",children:config.cluster_name}),/*#__PURE__*/_jsxs(\"p\",{className:\"mt-1 text-sm text-gray-400\",children:[\"Node: \",config.node_id]}),/*#__PURE__*/_jsxs(\"p\",{className:\"mt-1 text-xs text-gray-500\",children:[\"Version: \",config.version]})]}),/*#__PURE__*/_jsx(DocumentTextIcon,{className:\"h-6 w-6 text-gray-400\"})]})},config.id)),configs.length===0&&/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-8\",children:[/*#__PURE__*/_jsx(GlobeAltIcon,{className:\"mx-auto h-12 w-12 text-gray-400\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"mt-2 text-sm font-medium text-gray-300\",children:\"No configurations\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-gray-400\",children:\"Create your first Envoy configuration.\"})]})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-gray-800 shadow rounded-lg\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"px-4 py-5 sm:p-6\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg leading-6 font-medium text-white mb-4\",children:\"Configuration Editor\"}),selectedConfig?/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-300\",children:\"Cluster Name\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",className:\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\",value:selectedConfig.cluster_name,readOnly:true})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-300\",children:\"Node ID\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",className:\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\",value:selectedConfig.node_id,readOnly:true})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-300\",children:\"Configuration (YAML)\"}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-1 border border-gray-600 rounded-md overflow-hidden\",children:/*#__PURE__*/_jsx(Editor,{height:\"300px\",defaultLanguage:\"yaml\",value:selectedConfig.config,theme:\"vs-dark\",options:{readOnly:true,minimap:{enabled:false},scrollBeyondLastLine:false,fontSize:14,fontFamily:'JetBrains Mono, monospace',lineNumbers:'on',folding:true,wordWrap:'on'}})})]})]}):/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-8\",children:[/*#__PURE__*/_jsx(DocumentTextIcon,{className:\"mx-auto h-12 w-12 text-gray-400\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"mt-2 text-sm font-medium text-gray-300\",children:\"Select a configuration\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-gray-400\",children:\"Choose a configuration from the list to view and edit it.\"})]})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-gray-800 shadow rounded-lg\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"px-4 py-5 sm:p-6\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg leading-6 font-medium text-white mb-4\",children:\"Connected Envoy Nodes\"}),nodes.length===0?/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-8\",children:[/*#__PURE__*/_jsx(GlobeAltIcon,{className:\"mx-auto h-12 w-12 text-gray-400\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"mt-2 text-sm font-medium text-gray-300\",children:\"No nodes connected\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-gray-400\",children:\"Envoy nodes will appear here when they connect to the control plane.\"})]}):/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3\",children:nodes.map(node=>/*#__PURE__*/_jsxs(\"div\",{className:\"border border-gray-600 rounded-lg p-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{className:\"text-lg font-medium text-white\",children:node.id}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-gray-400\",children:[\"Cluster: \",node.cluster]}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-xs text-gray-500\",children:[\"Version: \",node.version]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"w-3 h-3 rounded-full \".concat(node.status==='connected'?'bg-green-400':'bg-red-400')})]}),/*#__PURE__*/_jsxs(\"p\",{className:\"mt-2 text-xs text-gray-500\",children:[\"Last seen: \",new Date(node.last_seen).toLocaleString()]})]},node.id))})]})}),showCreateForm&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",children:/*#__PURE__*/_jsx(\"div\",{className:\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-gray-800\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"mt-3\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-white mb-4\",children:\"Create New Configuration\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-300\",children:\"Node ID\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",className:\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\",value:newConfig.node_id,onChange:e=>setNewConfig(_objectSpread(_objectSpread({},newConfig),{},{node_id:e.target.value}))})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-300\",children:\"Cluster Name\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",className:\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\",value:newConfig.cluster_name,onChange:e=>setNewConfig(_objectSpread(_objectSpread({},newConfig),{},{cluster_name:e.target.value}))})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-300\",children:\"Configuration\"}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-1 border border-gray-600 rounded-md overflow-hidden\",children:/*#__PURE__*/_jsx(Editor,{height:\"200px\",defaultLanguage:\"yaml\",value:newConfig.config,onChange:value=>setNewConfig(_objectSpread(_objectSpread({},newConfig),{},{config:value||''})),theme:\"vs-dark\",options:{minimap:{enabled:false},scrollBeyondLastLine:false,fontSize:14,fontFamily:'JetBrains Mono, monospace',lineNumbers:'on',folding:true,wordWrap:'on'}})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-end space-x-3 mt-6\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setShowCreateForm(false),className:\"px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700\",children:\"Cancel\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handleCreateConfig,className:\"px-4 py-2 bg-cyan-600 text-white rounded-md hover:bg-cyan-700\",children:\"Create\"})]})]})})})]});};export default EnvoyConfigEditor;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "fetchEnvoyConfigs", "fetchEnvoyNodes", "createEnvoyConfig", "setSelectedConfig", "GlobeAltIcon", "PlusIcon", "DocumentTextIcon", "Editor", "jsx", "_jsx", "jsxs", "_jsxs", "EnvoyConfigEditor", "dispatch", "configs", "nodes", "selectedConfig", "loading", "error", "state", "envoy", "showCreateForm", "setShowCreateForm", "newConfig", "setNewConfig", "node_id", "cluster_name", "config", "version", "handleCreateConfig", "className", "children", "onClick", "map", "concat", "id", "length", "type", "value", "readOnly", "height", "defaultLanguage", "theme", "options", "minimap", "enabled", "scrollBeyondLastLine", "fontSize", "fontFamily", "lineNumbers", "folding", "wordWrap", "node", "cluster", "status", "Date", "last_seen", "toLocaleString", "onChange", "e", "_objectSpread", "target"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/EnvoyConfigEditor.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { RootState, AppDispatch } from '../store/store';\nimport {\n  fetchEnvoyConfigs,\n  fetchEnvoyNodes,\n  createEnvoyConfig,\n  setSelectedConfig,\n} from '../store/slices/envoySlice';\nimport {\n  GlobeAltIcon,\n  PlusIcon,\n  DocumentTextIcon,\n} from '@heroicons/react/24/outline';\nimport Editor from '@monaco-editor/react';\n\nconst EnvoyConfigEditor: React.FC = () => {\n  const dispatch = useDispatch<AppDispatch>();\n  const { configs, nodes, selectedConfig, loading, error } = useSelector(\n    (state: RootState) => state.envoy\n  );\n\n  const [showCreateForm, setShowCreateForm] = useState(false);\n  const [newConfig, setNewConfig] = useState({\n    node_id: '',\n    cluster_name: '',\n    config: '',\n    version: '1.0.0',\n  });\n\n  useEffect(() => {\n    dispatch(fetchEnvoyConfigs());\n    dispatch(fetchEnvoyNodes());\n  }, [dispatch]);\n\n  const handleCreateConfig = () => {\n    dispatch(createEnvoyConfig(newConfig));\n    setNewConfig({ node_id: '', cluster_name: '', config: '', version: '1.0.0' });\n    setShowCreateForm(false);\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-white\">Envoy Configuration</h1>\n          <p className=\"mt-1 text-sm text-gray-400\">\n            Manage Envoy proxy configurations and deployments\n          </p>\n        </div>\n        <button\n          onClick={() => setShowCreateForm(true)}\n          className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500\"\n        >\n          <PlusIcon className=\"h-4 w-4 mr-2\" />\n          New Config\n        </button>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Configurations List */}\n        <div className=\"bg-gray-800 shadow rounded-lg\">\n          <div className=\"px-4 py-5 sm:p-6\">\n            <h3 className=\"text-lg leading-6 font-medium text-white mb-4\">\n              Configurations\n            </h3>\n\n            {loading ? (\n              <div className=\"flex items-center justify-center h-32\">\n                <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500\"></div>\n              </div>\n            ) : (\n              <div className=\"space-y-3\">\n                {configs.map((config) => (\n                  <div\n                    key={config.id}\n                    className={`border rounded-lg p-4 cursor-pointer transition-colors ${\n                      selectedConfig?.id === config.id\n                        ? 'border-cyan-500 bg-cyan-500/10'\n                        : 'border-gray-600 hover:bg-gray-700'\n                    }`}\n                    onClick={() => dispatch(setSelectedConfig(config))}\n                  >\n                    <div className=\"flex items-start justify-between\">\n                      <div>\n                        <h4 className=\"text-lg font-medium text-white\">{config.cluster_name}</h4>\n                        <p className=\"mt-1 text-sm text-gray-400\">Node: {config.node_id}</p>\n                        <p className=\"mt-1 text-xs text-gray-500\">Version: {config.version}</p>\n                      </div>\n                      <DocumentTextIcon className=\"h-6 w-6 text-gray-400\" />\n                    </div>\n                  </div>\n                ))}\n\n                {configs.length === 0 && (\n                  <div className=\"text-center py-8\">\n                    <GlobeAltIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n                    <h3 className=\"mt-2 text-sm font-medium text-gray-300\">No configurations</h3>\n                    <p className=\"mt-1 text-sm text-gray-400\">\n                      Create your first Envoy configuration.\n                    </p>\n                  </div>\n                )}\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Config Editor */}\n        <div className=\"bg-gray-800 shadow rounded-lg\">\n          <div className=\"px-4 py-5 sm:p-6\">\n            <h3 className=\"text-lg leading-6 font-medium text-white mb-4\">\n              Configuration Editor\n            </h3>\n\n            {selectedConfig ? (\n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-300\">\n                    Cluster Name\n                  </label>\n                  <input\n                    type=\"text\"\n                    className=\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\"\n                    value={selectedConfig.cluster_name}\n                    readOnly\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-300\">\n                    Node ID\n                  </label>\n                  <input\n                    type=\"text\"\n                    className=\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\"\n                    value={selectedConfig.node_id}\n                    readOnly\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-300\">\n                    Configuration (YAML)\n                  </label>\n                  <div className=\"mt-1 border border-gray-600 rounded-md overflow-hidden\">\n                    <Editor\n                      height=\"300px\"\n                      defaultLanguage=\"yaml\"\n                      value={selectedConfig.config}\n                      theme=\"vs-dark\"\n                      options={{\n                        readOnly: true,\n                        minimap: { enabled: false },\n                        scrollBeyondLastLine: false,\n                        fontSize: 14,\n                        fontFamily: 'JetBrains Mono, monospace',\n                        lineNumbers: 'on',\n                        folding: true,\n                        wordWrap: 'on',\n                      }}\n                    />\n                  </div>\n                </div>\n              </div>\n            ) : (\n              <div className=\"text-center py-8\">\n                <DocumentTextIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n                <h3 className=\"mt-2 text-sm font-medium text-gray-300\">Select a configuration</h3>\n                <p className=\"mt-1 text-sm text-gray-400\">\n                  Choose a configuration from the list to view and edit it.\n                </p>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Connected Nodes */}\n      <div className=\"bg-gray-800 shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <h3 className=\"text-lg leading-6 font-medium text-white mb-4\">\n            Connected Envoy Nodes\n          </h3>\n\n          {nodes.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <GlobeAltIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n              <h3 className=\"mt-2 text-sm font-medium text-gray-300\">No nodes connected</h3>\n              <p className=\"mt-1 text-sm text-gray-400\">\n                Envoy nodes will appear here when they connect to the control plane.\n              </p>\n            </div>\n          ) : (\n            <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3\">\n              {nodes.map((node) => (\n                <div key={node.id} className=\"border border-gray-600 rounded-lg p-4\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <h4 className=\"text-lg font-medium text-white\">{node.id}</h4>\n                      <p className=\"text-sm text-gray-400\">Cluster: {node.cluster}</p>\n                      <p className=\"text-xs text-gray-500\">Version: {node.version}</p>\n                    </div>\n                    <div className={`w-3 h-3 rounded-full ${\n                      node.status === 'connected' ? 'bg-green-400' : 'bg-red-400'\n                    }`} />\n                  </div>\n                  <p className=\"mt-2 text-xs text-gray-500\">\n                    Last seen: {new Date(node.last_seen).toLocaleString()}\n                  </p>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Create Config Modal */}\n      {showCreateForm && (\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n          <div className=\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-gray-800\">\n            <div className=\"mt-3\">\n              <h3 className=\"text-lg font-medium text-white mb-4\">Create New Configuration</h3>\n              \n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-300\">Node ID</label>\n                  <input\n                    type=\"text\"\n                    className=\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\"\n                    value={newConfig.node_id}\n                    onChange={(e) => setNewConfig({ ...newConfig, node_id: e.target.value })}\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-300\">Cluster Name</label>\n                  <input\n                    type=\"text\"\n                    className=\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\"\n                    value={newConfig.cluster_name}\n                    onChange={(e) => setNewConfig({ ...newConfig, cluster_name: e.target.value })}\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-300\">Configuration</label>\n                  <div className=\"mt-1 border border-gray-600 rounded-md overflow-hidden\">\n                    <Editor\n                      height=\"200px\"\n                      defaultLanguage=\"yaml\"\n                      value={newConfig.config}\n                      onChange={(value) => setNewConfig({ ...newConfig, config: value || '' })}\n                      theme=\"vs-dark\"\n                      options={{\n                        minimap: { enabled: false },\n                        scrollBeyondLastLine: false,\n                        fontSize: 14,\n                        fontFamily: 'JetBrains Mono, monospace',\n                        lineNumbers: 'on',\n                        folding: true,\n                        wordWrap: 'on',\n                      }}\n                    />\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"flex justify-end space-x-3 mt-6\">\n                <button\n                  onClick={() => setShowCreateForm(false)}\n                  className=\"px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700\"\n                >\n                  Cancel\n                </button>\n                <button\n                  onClick={handleCreateConfig}\n                  className=\"px-4 py-2 bg-cyan-600 text-white rounded-md hover:bg-cyan-700\"\n                >\n                  Create\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default EnvoyConfigEditor;\n"], "mappings": "yJAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CAEtD,OACEC,iBAAiB,CACjBC,eAAe,CACfC,iBAAiB,CACjBC,iBAAiB,KACZ,4BAA4B,CACnC,OACEC,YAAY,CACZC,QAAQ,CACRC,gBAAgB,KACX,6BAA6B,CACpC,MAAO,CAAAC,MAAM,KAAM,sBAAsB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1C,KAAM,CAAAC,iBAA2B,CAAGA,CAAA,GAAM,CACxC,KAAM,CAAAC,QAAQ,CAAGf,WAAW,CAAc,CAAC,CAC3C,KAAM,CAAEgB,OAAO,CAAEC,KAAK,CAAEC,cAAc,CAAEC,OAAO,CAAEC,KAAM,CAAC,CAAGnB,WAAW,CACnEoB,KAAgB,EAAKA,KAAK,CAACC,KAC9B,CAAC,CAED,KAAM,CAACC,cAAc,CAAEC,iBAAiB,CAAC,CAAGzB,QAAQ,CAAC,KAAK,CAAC,CAC3D,KAAM,CAAC0B,SAAS,CAAEC,YAAY,CAAC,CAAG3B,QAAQ,CAAC,CACzC4B,OAAO,CAAE,EAAE,CACXC,YAAY,CAAE,EAAE,CAChBC,MAAM,CAAE,EAAE,CACVC,OAAO,CAAE,OACX,CAAC,CAAC,CAEFhC,SAAS,CAAC,IAAM,CACdiB,QAAQ,CAACb,iBAAiB,CAAC,CAAC,CAAC,CAC7Ba,QAAQ,CAACZ,eAAe,CAAC,CAAC,CAAC,CAC7B,CAAC,CAAE,CAACY,QAAQ,CAAC,CAAC,CAEd,KAAM,CAAAgB,kBAAkB,CAAGA,CAAA,GAAM,CAC/BhB,QAAQ,CAACX,iBAAiB,CAACqB,SAAS,CAAC,CAAC,CACtCC,YAAY,CAAC,CAAEC,OAAO,CAAE,EAAE,CAAEC,YAAY,CAAE,EAAE,CAAEC,MAAM,CAAE,EAAE,CAAEC,OAAO,CAAE,OAAQ,CAAC,CAAC,CAC7EN,iBAAiB,CAAC,KAAK,CAAC,CAC1B,CAAC,CAED,mBACEX,KAAA,QAAKmB,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExBpB,KAAA,QAAKmB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDpB,KAAA,QAAAoB,QAAA,eACEtB,IAAA,OAAIqB,SAAS,CAAC,+BAA+B,CAAAC,QAAA,CAAC,qBAAmB,CAAI,CAAC,cACtEtB,IAAA,MAAGqB,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,mDAE1C,CAAG,CAAC,EACD,CAAC,cACNpB,KAAA,WACEqB,OAAO,CAAEA,CAAA,GAAMV,iBAAiB,CAAC,IAAI,CAAE,CACvCQ,SAAS,CAAC,wNAAwN,CAAAC,QAAA,eAElOtB,IAAA,CAACJ,QAAQ,EAACyB,SAAS,CAAC,cAAc,CAAE,CAAC,aAEvC,EAAQ,CAAC,EACN,CAAC,cAENnB,KAAA,QAAKmB,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eAEpDtB,IAAA,QAAKqB,SAAS,CAAC,+BAA+B,CAAAC,QAAA,cAC5CpB,KAAA,QAAKmB,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BtB,IAAA,OAAIqB,SAAS,CAAC,+CAA+C,CAAAC,QAAA,CAAC,gBAE9D,CAAI,CAAC,CAEJd,OAAO,cACNR,IAAA,QAAKqB,SAAS,CAAC,uCAAuC,CAAAC,QAAA,cACpDtB,IAAA,QAAKqB,SAAS,CAAC,8DAA8D,CAAM,CAAC,CACjF,CAAC,cAENnB,KAAA,QAAKmB,SAAS,CAAC,WAAW,CAAAC,QAAA,EACvBjB,OAAO,CAACmB,GAAG,CAAEN,MAAM,eAClBlB,IAAA,QAEEqB,SAAS,2DAAAI,MAAA,CACP,CAAAlB,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAEmB,EAAE,IAAKR,MAAM,CAACQ,EAAE,CAC5B,gCAAgC,CAChC,mCAAmC,CACtC,CACHH,OAAO,CAAEA,CAAA,GAAMnB,QAAQ,CAACV,iBAAiB,CAACwB,MAAM,CAAC,CAAE,CAAAI,QAAA,cAEnDpB,KAAA,QAAKmB,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/CpB,KAAA,QAAAoB,QAAA,eACEtB,IAAA,OAAIqB,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAEJ,MAAM,CAACD,YAAY,CAAK,CAAC,cACzEf,KAAA,MAAGmB,SAAS,CAAC,4BAA4B,CAAAC,QAAA,EAAC,QAAM,CAACJ,MAAM,CAACF,OAAO,EAAI,CAAC,cACpEd,KAAA,MAAGmB,SAAS,CAAC,4BAA4B,CAAAC,QAAA,EAAC,WAAS,CAACJ,MAAM,CAACC,OAAO,EAAI,CAAC,EACpE,CAAC,cACNnB,IAAA,CAACH,gBAAgB,EAACwB,SAAS,CAAC,uBAAuB,CAAE,CAAC,EACnD,CAAC,EAfDH,MAAM,CAACQ,EAgBT,CACN,CAAC,CAEDrB,OAAO,CAACsB,MAAM,GAAK,CAAC,eACnBzB,KAAA,QAAKmB,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BtB,IAAA,CAACL,YAAY,EAAC0B,SAAS,CAAC,iCAAiC,CAAE,CAAC,cAC5DrB,IAAA,OAAIqB,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,mBAAiB,CAAI,CAAC,cAC7EtB,IAAA,MAAGqB,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,wCAE1C,CAAG,CAAC,EACD,CACN,EACE,CACN,EACE,CAAC,CACH,CAAC,cAGNtB,IAAA,QAAKqB,SAAS,CAAC,+BAA+B,CAAAC,QAAA,cAC5CpB,KAAA,QAAKmB,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BtB,IAAA,OAAIqB,SAAS,CAAC,+CAA+C,CAAAC,QAAA,CAAC,sBAE9D,CAAI,CAAC,CAEJf,cAAc,cACbL,KAAA,QAAKmB,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBpB,KAAA,QAAAoB,QAAA,eACEtB,IAAA,UAAOqB,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,cAE3D,CAAO,CAAC,cACRtB,IAAA,UACE4B,IAAI,CAAC,MAAM,CACXP,SAAS,CAAC,0IAA0I,CACpJQ,KAAK,CAAEtB,cAAc,CAACU,YAAa,CACnCa,QAAQ,MACT,CAAC,EACC,CAAC,cAEN5B,KAAA,QAAAoB,QAAA,eACEtB,IAAA,UAAOqB,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,SAE3D,CAAO,CAAC,cACRtB,IAAA,UACE4B,IAAI,CAAC,MAAM,CACXP,SAAS,CAAC,0IAA0I,CACpJQ,KAAK,CAAEtB,cAAc,CAACS,OAAQ,CAC9Bc,QAAQ,MACT,CAAC,EACC,CAAC,cAEN5B,KAAA,QAAAoB,QAAA,eACEtB,IAAA,UAAOqB,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,sBAE3D,CAAO,CAAC,cACRtB,IAAA,QAAKqB,SAAS,CAAC,wDAAwD,CAAAC,QAAA,cACrEtB,IAAA,CAACF,MAAM,EACLiC,MAAM,CAAC,OAAO,CACdC,eAAe,CAAC,MAAM,CACtBH,KAAK,CAAEtB,cAAc,CAACW,MAAO,CAC7Be,KAAK,CAAC,SAAS,CACfC,OAAO,CAAE,CACPJ,QAAQ,CAAE,IAAI,CACdK,OAAO,CAAE,CAAEC,OAAO,CAAE,KAAM,CAAC,CAC3BC,oBAAoB,CAAE,KAAK,CAC3BC,QAAQ,CAAE,EAAE,CACZC,UAAU,CAAE,2BAA2B,CACvCC,WAAW,CAAE,IAAI,CACjBC,OAAO,CAAE,IAAI,CACbC,QAAQ,CAAE,IACZ,CAAE,CACH,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,cAENxC,KAAA,QAAKmB,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BtB,IAAA,CAACH,gBAAgB,EAACwB,SAAS,CAAC,iCAAiC,CAAE,CAAC,cAChErB,IAAA,OAAIqB,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,wBAAsB,CAAI,CAAC,cAClFtB,IAAA,MAAGqB,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,2DAE1C,CAAG,CAAC,EACD,CACN,EACE,CAAC,CACH,CAAC,EACH,CAAC,cAGNtB,IAAA,QAAKqB,SAAS,CAAC,+BAA+B,CAAAC,QAAA,cAC5CpB,KAAA,QAAKmB,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BtB,IAAA,OAAIqB,SAAS,CAAC,+CAA+C,CAAAC,QAAA,CAAC,uBAE9D,CAAI,CAAC,CAEJhB,KAAK,CAACqB,MAAM,GAAK,CAAC,cACjBzB,KAAA,QAAKmB,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BtB,IAAA,CAACL,YAAY,EAAC0B,SAAS,CAAC,iCAAiC,CAAE,CAAC,cAC5DrB,IAAA,OAAIqB,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,oBAAkB,CAAI,CAAC,cAC9EtB,IAAA,MAAGqB,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,sEAE1C,CAAG,CAAC,EACD,CAAC,cAENtB,IAAA,QAAKqB,SAAS,CAAC,sDAAsD,CAAAC,QAAA,CAClEhB,KAAK,CAACkB,GAAG,CAAEmB,IAAI,eACdzC,KAAA,QAAmBmB,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eAClEpB,KAAA,QAAKmB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDpB,KAAA,QAAAoB,QAAA,eACEtB,IAAA,OAAIqB,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAEqB,IAAI,CAACjB,EAAE,CAAK,CAAC,cAC7DxB,KAAA,MAAGmB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAC,WAAS,CAACqB,IAAI,CAACC,OAAO,EAAI,CAAC,cAChE1C,KAAA,MAAGmB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAC,WAAS,CAACqB,IAAI,CAACxB,OAAO,EAAI,CAAC,EAC7D,CAAC,cACNnB,IAAA,QAAKqB,SAAS,yBAAAI,MAAA,CACZkB,IAAI,CAACE,MAAM,GAAK,WAAW,CAAG,cAAc,CAAG,YAAY,CAC1D,CAAE,CAAC,EACH,CAAC,cACN3C,KAAA,MAAGmB,SAAS,CAAC,4BAA4B,CAAAC,QAAA,EAAC,aAC7B,CAAC,GAAI,CAAAwB,IAAI,CAACH,IAAI,CAACI,SAAS,CAAC,CAACC,cAAc,CAAC,CAAC,EACpD,CAAC,GAbIL,IAAI,CAACjB,EAcV,CACN,CAAC,CACC,CACN,EACE,CAAC,CACH,CAAC,CAGLd,cAAc,eACbZ,IAAA,QAAKqB,SAAS,CAAC,4EAA4E,CAAAC,QAAA,cACzFtB,IAAA,QAAKqB,SAAS,CAAC,0EAA0E,CAAAC,QAAA,cACvFpB,KAAA,QAAKmB,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBtB,IAAA,OAAIqB,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,0BAAwB,CAAI,CAAC,cAEjFpB,KAAA,QAAKmB,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBpB,KAAA,QAAAoB,QAAA,eACEtB,IAAA,UAAOqB,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,SAAO,CAAO,CAAC,cAC1EtB,IAAA,UACE4B,IAAI,CAAC,MAAM,CACXP,SAAS,CAAC,0IAA0I,CACpJQ,KAAK,CAAEf,SAAS,CAACE,OAAQ,CACzBiC,QAAQ,CAAGC,CAAC,EAAKnC,YAAY,CAAAoC,aAAA,CAAAA,aAAA,IAAMrC,SAAS,MAAEE,OAAO,CAAEkC,CAAC,CAACE,MAAM,CAACvB,KAAK,EAAE,CAAE,CAC1E,CAAC,EACC,CAAC,cAEN3B,KAAA,QAAAoB,QAAA,eACEtB,IAAA,UAAOqB,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,cAAY,CAAO,CAAC,cAC/EtB,IAAA,UACE4B,IAAI,CAAC,MAAM,CACXP,SAAS,CAAC,0IAA0I,CACpJQ,KAAK,CAAEf,SAAS,CAACG,YAAa,CAC9BgC,QAAQ,CAAGC,CAAC,EAAKnC,YAAY,CAAAoC,aAAA,CAAAA,aAAA,IAAMrC,SAAS,MAAEG,YAAY,CAAEiC,CAAC,CAACE,MAAM,CAACvB,KAAK,EAAE,CAAE,CAC/E,CAAC,EACC,CAAC,cAEN3B,KAAA,QAAAoB,QAAA,eACEtB,IAAA,UAAOqB,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,eAAa,CAAO,CAAC,cAChFtB,IAAA,QAAKqB,SAAS,CAAC,wDAAwD,CAAAC,QAAA,cACrEtB,IAAA,CAACF,MAAM,EACLiC,MAAM,CAAC,OAAO,CACdC,eAAe,CAAC,MAAM,CACtBH,KAAK,CAAEf,SAAS,CAACI,MAAO,CACxB+B,QAAQ,CAAGpB,KAAK,EAAKd,YAAY,CAAAoC,aAAA,CAAAA,aAAA,IAAMrC,SAAS,MAAEI,MAAM,CAAEW,KAAK,EAAI,EAAE,EAAE,CAAE,CACzEI,KAAK,CAAC,SAAS,CACfC,OAAO,CAAE,CACPC,OAAO,CAAE,CAAEC,OAAO,CAAE,KAAM,CAAC,CAC3BC,oBAAoB,CAAE,KAAK,CAC3BC,QAAQ,CAAE,EAAE,CACZC,UAAU,CAAE,2BAA2B,CACvCC,WAAW,CAAE,IAAI,CACjBC,OAAO,CAAE,IAAI,CACbC,QAAQ,CAAE,IACZ,CAAE,CACH,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,cAENxC,KAAA,QAAKmB,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC9CtB,IAAA,WACEuB,OAAO,CAAEA,CAAA,GAAMV,iBAAiB,CAAC,KAAK,CAAE,CACxCQ,SAAS,CAAC,+DAA+D,CAAAC,QAAA,CAC1E,QAED,CAAQ,CAAC,cACTtB,IAAA,WACEuB,OAAO,CAAEH,kBAAmB,CAC5BC,SAAS,CAAC,+DAA+D,CAAAC,QAAA,CAC1E,QAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACH,CAAC,CACH,CACN,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAAnB,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}