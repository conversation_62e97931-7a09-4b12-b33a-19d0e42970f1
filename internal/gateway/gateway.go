package gateway

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/recover"
	"go.uber.org/zap"

	"github.com/cainuro/orchestrator/internal/config"
)

// Gateway represents the main CAINuro Orchestrator gateway
type Gateway struct {
	config   *config.Config
	logger   *zap.Logger
	fiberApp *fiber.App
}

// ComponentFactory holds all available service factories
type ComponentFactory struct {
	Services map[string]ServiceFactory
	Modules  map[string]ModuleFactory
}

// ServiceFactory creates a service instance
type ServiceFactory func(cfg interface{}, logger *zap.Logger) (Service, error)

// ModuleFactory creates a module instance
type ModuleFactory func(cfg interface{}, logger *zap.Logger) (Module, error)

// Service interface for all CAINuro services
type Service interface {
	Name() string
	Start(ctx context.Context) error
	Stop(ctx context.Context) error
}

// Module interface for all CAINuro modules
type Module interface {
	Name() string
	Register(app *fiber.App) error
}

// New creates a new Gateway instance
func New(cfg *config.Config, logger *zap.Logger) (*Gateway, error) {
	// Initialize Fiber app with CAINuro configuration
	fiberApp := fiber.New(fiber.Config{
		AppName:      "cai-orchestrator",
		ServerHeader: fmt.Sprintf("CAINuro-Orchestrator/%s", cfg.Version),
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  120 * time.Second,
	})

	// Add middleware
	fiberApp.Use(recover.New())
	fiberApp.Use(cors.New(cors.Config{
		AllowOrigins: "*",
		AllowMethods: "GET,POST,PUT,DELETE,OPTIONS",
		AllowHeaders: "Origin,Content-Type,Accept,Authorization",
	}))

	return &Gateway{
		config:   cfg,
		logger:   logger,
		fiberApp: fiberApp,
	}, nil
}

// RegisterServices registers all configured services
func (g *Gateway) RegisterServices(factory *ComponentFactory) error {
	g.logger.Info("Registering services...")

	// Register core services based on configuration
	for serviceName, serviceConfig := range g.config.Services {
		g.logger.Info("Registering service", zap.String("service", serviceName))

		serviceFactory, exists := factory.Services[serviceName]
		if !exists {
			return fmt.Errorf("service factory not found: %s", serviceName)
		}

		service, err := serviceFactory(serviceConfig, g.logger.With(zap.String("service", serviceName)))
		if err != nil {
			return fmt.Errorf("failed to create service %s: %w", serviceName, err)
		}

		// Start the service
		ctx := context.Background()
		if err := service.Start(ctx); err != nil {
			return fmt.Errorf("failed to start service %s: %w", serviceName, err)
		}

		g.logger.Info("Service registered successfully", zap.String("service", serviceName))
	}

	return nil
}

// RegisterModules registers all configured modules
func (g *Gateway) RegisterModules(factory *ComponentFactory) error {
	g.logger.Info("Registering modules...")

	// Register core modules based on configuration
	for moduleName, moduleConfig := range g.config.Modules {
		g.logger.Info("Registering module", zap.String("module", moduleName))

		moduleFactory, exists := factory.Modules[moduleName]
		if !exists {
			return fmt.Errorf("module factory not found: %s", moduleName)
		}

		module, err := moduleFactory(moduleConfig, g.logger.With(zap.String("module", moduleName)))
		if err != nil {
			return fmt.Errorf("failed to create module %s: %w", moduleName, err)
		}

		// Register the module with Fiber
		if err := module.Register(g.fiberApp); err != nil {
			return fmt.Errorf("failed to register module %s: %w", moduleName, err)
		}

		g.logger.Info("Module registered successfully", zap.String("module", moduleName))
	}

	return nil
}

// SetupRoutes configures the core API routes
func (g *Gateway) SetupRoutes() {
	// Health check endpoint
	g.fiberApp.Get("/health", g.healthCheck)

	// API v1 group
	_ = g.fiberApp.Group("/v1")

	// Core API endpoints will be registered by modules
	g.logger.Info("Core routes configured")
}

// healthCheck returns the health status of the gateway
func (g *Gateway) healthCheck(c *fiber.Ctx) error {
	return c.JSON(fiber.Map{
		"status":    "healthy",
		"version":   g.config.Version,
		"mode":      "YOLO",
		"timestamp": time.Now().UTC(),
	})
}

// Run starts the gateway server
func (g *Gateway) Run() error {
	// Setup routes
	g.SetupRoutes()

	// Start HTTP server
	addr := fmt.Sprintf(":%d", g.config.Server.HTTPPort)
	g.logger.Info("🚀 CAINuro Orchestrator starting",
		zap.String("version", g.config.Version),
		zap.String("http_addr", addr))

	// Start HTTP server in a goroutine
	go func() {
		if err := g.fiberApp.Listen(addr); err != nil {
			g.logger.Fatal("Failed to start HTTP server", zap.Error(err))
		}
	}()

	// Wait for shutdown signal
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	<-sigChan

	g.logger.Info("🛑 Shutting down gracefully...")

	// Shutdown with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := g.fiberApp.ShutdownWithContext(ctx); err != nil {
		g.logger.Error("Server shutdown error", zap.Error(err))
		return err
	}

	g.logger.Info("✅ Server stopped gracefully")
	return nil
}

// GetLogger returns the logger
func (g *Gateway) GetLogger() *zap.Logger {
	return g.logger
}
