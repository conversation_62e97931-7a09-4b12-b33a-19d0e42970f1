# 🚀 CAINuro Orchestrator

**YOLO Mode Engaged!** - A complete transformation of Lyft Clutch into a modern, cloud-native orchestration platform.

## 🎯 What We Built

CAINuro Orchestrator is a comprehensive cloud infrastructure management platform that provides:

- **🔍 Multi-Cloud Discovery**: Search and discover resources across AWS, GCP, and Azure
- **⚡ Workflow Automation**: Execute complex automation workflows across your infrastructure  
- **🌐 Envoy Control Plane**: Manage Envoy proxy configurations and deployments
- **📈 Auto Scaling**: Intelligent application scaling based on metrics
- **🛡️ Tamper-Proof Audit**: Immutable audit trail powered by ImmuDB
- **💾 Embedded Database**: SQLite-based storage with Ristretto caching

## 🏗️ Architecture

### Backend (Go)
- **Framework**: Fiber v2 (high-performance HTTP framework)
- **Database**: SQLite with custom ORM layer
- **Cache**: Ristretto (in-memory cache)
- **Audit**: ImmuDB integration for tamper-proof logs
- **DI**: Google Wire for dependency injection
- **Config**: Viper for configuration management

### Frontend (React + TypeScript)
- **Framework**: React 18 with TypeScript
- **Styling**: Tailwind CSS v3 + Headless UI
- **State**: Redux Toolkit with RTK Query
- **Routing**: React Router v6
- **Icons**: Heroicons v2

### Key Transformations from Clutch
- ✅ Postgres → SQLite (embedded)
- ✅ Redis → Ristretto (in-memory)
- ✅ Material-UI → Tailwind CSS + Headless UI
- ✅ Uber fx → Google Wire
- ✅ Added ImmuDB audit trail
- ✅ Added Envoy xDS control plane
- ✅ Added multi-cloud discovery
- ✅ Single static binary deployment

## 🚀 Quick Start

### Prerequisites
- Go 1.21+
- Node.js 18+
- npm or yarn

### Backend Setup
```bash
# Clone and build
git clone <repository>
cd CAINuro_Orchestrator

# Install Go dependencies
go mod tidy

# Build the application
go build -o cai-orchestrator ./cmd

# Run the server
./cai-orchestrator --port 8080
```

### Frontend Setup (Optional)
```bash
# Navigate to frontend
cd frontend/orchestrator

# Install dependencies
npm install

# Build for production
npm run build

# Or run in development mode
npm start
```

## 📡 API Endpoints

### Core Services
- `GET /health` - System health check
- `GET /v1/autoscaler/status` - Autoscaler status
- `POST /v1/autoscaler/config` - Update autoscaler config

### Workflow Management
- `GET /v1/workflows` - List available workflows
- `POST /v1/workflows/execute` - Execute a workflow
- `GET /v1/workflows/:id/status` - Get workflow status

### Resource Discovery
- `POST /v1/discovery/search` - Search cloud resources
- `GET /v1/discovery/resources/:id` - Get resource details

### Envoy Control Plane
- `POST /v1/envoy/snapshot` - Push Envoy snapshot
- `GET /v1/envoy/snapshots/:tenant` - Get Envoy snapshot

### Database Administration
- `GET /v1/db/status` - Database status
- `POST /v1/db/query` - Execute SQL query

### Audit Trail
- `GET /v1/audit/logs` - Get audit logs
- `POST /v1/audit/verify` - Verify audit entry

## 🎛️ Configuration

The application uses YAML configuration with sensible defaults:

```yaml
server:
  host: "0.0.0.0"
  grpc_port: 8081
  http_port: 8080

discovery:
  aws:
    regions: ["us-east-1", "us-west-2"]
  gcp:
    projects: []
  azure:
    subscriptions: []

envoy:
  xds_port: 18000
  snapshot_cache_ttl: "5m"

database:
  path: "./data/cainuro.db"

audit:
  path: "./data/immu"

cache:
  size: 134217728  # 128 MiB
```

## 🏃‍♂️ Running the Application

### Development Mode
```bash
# Terminal 1: Backend
./cai-orchestrator --port 8080

# Terminal 2: Frontend (optional)
cd frontend/orchestrator
npm start
```

### Production Mode
```bash
# Build everything
go build -o cai-orchestrator ./cmd
cd frontend/orchestrator && npm run build && cd ../..

# Run single binary
./cai-orchestrator --port 8080
```

### Docker (Future)
```bash
# Build container
docker build -t cainuro-orchestrator .

# Run container
docker run -p 8080:8080 cainuro-orchestrator
```

## 🧪 Testing the API

```bash
# Health check
curl http://localhost:8080/health

# Autoscaler status
curl http://localhost:8080/v1/autoscaler/status

# Search resources
curl -X POST http://localhost:8080/v1/discovery/search \
  -H "Content-Type: application/json" \
  -d '{"query": "web-server", "provider": "aws"}'

# Execute workflow
curl -X POST http://localhost:8080/v1/workflows/execute \
  -H "Content-Type: application/json" \
  -d '{"workflow_id": "cloud-search", "inputs": {"region": "us-east-1"}}'
```

## 📁 Project Structure

```
CAINuro_Orchestrator/
├── cmd/                    # Application entrypoint
├── internal/              # Internal packages
│   ├── audit/            # Audit trail (ImmuDB)
│   ├── cache/            # Ristretto cache
│   ├── config/           # Configuration management
│   ├── db/               # Database layer (SQLite)
│   ├── gateway/          # HTTP gateway
│   └── wire/             # Dependency injection
├── backend/service/       # Business logic services
│   ├── autoscaler/       # Auto-scaling service
│   ├── audit/            # Audit service
│   ├── dbadmin/          # Database admin
│   ├── envoycontrolplane/ # Envoy xDS control plane
│   ├── search/           # Multi-cloud discovery
│   └── workflow/         # Workflow execution
├── frontend/orchestrator/ # React frontend
│   ├── src/
│   │   ├── components/   # Reusable components
│   │   ├── pages/        # Page components
│   │   ├── store/        # Redux store & slices
│   │   └── utils/        # Utility functions
│   └── build/            # Production build
├── vendor/clutch/         # Original Clutch codebase
├── go.mod                # Go dependencies
└── README.md             # This file
```

## 🎯 Features Implemented

### ✅ Core Infrastructure
- [x] Fiber HTTP server with middleware
- [x] SQLite database with custom ORM
- [x] Ristretto in-memory cache
- [x] Configuration management with Viper
- [x] Structured logging with Zap
- [x] Google Wire dependency injection

### ✅ Services
- [x] Multi-cloud resource discovery (AWS/GCP/Azure)
- [x] Workflow execution engine
- [x] Envoy xDS control plane
- [x] Auto-scaling service
- [x] Database administration
- [x] Audit trail with ImmuDB

### ✅ Frontend
- [x] React 18 with TypeScript
- [x] Tailwind CSS + Headless UI
- [x] Redux Toolkit state management
- [x] Responsive dark theme
- [x] Real-time dashboard
- [x] Interactive service pages

### ✅ API Integration
- [x] RESTful API design
- [x] JSON request/response
- [x] Error handling
- [x] CORS support
- [x] Health checks

## 🚧 Future Enhancements

- [ ] gRPC API support
- [ ] Kubernetes integration
- [ ] Prometheus metrics
- [ ] OpenTelemetry tracing
- [ ] WebSocket real-time updates
- [ ] Plugin system
- [ ] Multi-tenancy
- [ ] RBAC authentication
- [ ] Helm charts
- [ ] CI/CD pipelines

## 🤝 Contributing

This is a demonstration project showcasing the transformation of Lyft Clutch. The codebase is designed to be:

- **Modular**: Clear separation of concerns
- **Testable**: Dependency injection and interfaces
- **Scalable**: Microservice-ready architecture
- **Observable**: Structured logging and metrics
- **Maintainable**: Clean code and documentation

## 📄 License

This project is a transformation/port of Lyft Clutch and maintains compatibility with its Apache 2.0 license.

---

**🚀 YOLO Mode Complete!** - From Clutch to CAINuro in record time!
