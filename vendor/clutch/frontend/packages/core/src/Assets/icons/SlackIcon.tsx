import * as React from "react";

import type { SVGProps } from "../global";
import { StyledSVG } from "../global";

const SlackIcon = ({ size, ...props }: SVGProps) => (
  <StyledSVG
    size={size}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.2663 4.71377C8.26565 3.76812 8.98138 3.0007 9.86575 3C10.7508 3.0014 11.4672 3.76882 11.4665 4.71447V6.42754H9.86641C8.98203 6.42684 8.26565 5.65942 8.2663 4.71377ZM5.60011 7.57099H9.86575C10.7501 7.57168 11.4665 8.33911 11.4659 9.28546C11.4665 10.2311 10.7501 10.9985 9.86575 10.9992H5.59945C4.71508 10.9978 3.99869 10.2304 4 9.28476C3.99935 8.33911 4.71574 7.57168 5.60011 7.57099ZM18.3992 7.57099C19.2836 7.57168 20 8.33911 19.9993 9.28476C20 10.2311 19.2836 10.9985 18.3992 10.9992H16.7991V9.28476C16.7984 8.33911 17.5148 7.57168 18.3992 7.57099ZM15.733 4.71377V9.28476C15.7337 10.2311 15.0173 10.9985 14.1329 10.9992C13.2485 10.9978 12.5321 10.2304 12.5334 9.28476V4.71377C12.5328 3.76812 13.2492 3.0007 14.1335 3C15.0179 3.0014 15.7337 3.76882 15.733 4.71377ZM14.1327 20.1414C15.0171 20.1407 15.7335 19.3732 15.7328 18.4276C15.7335 17.4819 15.0171 16.7145 14.1327 16.7138H12.5326V18.4276C12.532 19.3725 13.2484 20.14 14.1327 20.1414ZM14.1327 15.5697H18.399C19.2834 15.569 19.9998 14.8015 19.9991 13.8559C20.0004 12.9102 19.2841 12.1428 18.3997 12.1414H14.1334C13.249 12.1421 12.5326 12.9095 12.5333 13.8552C12.5326 14.8015 13.2484 15.569 14.1327 15.5697ZM5.60119 15.5697C4.71682 15.569 4.00043 14.8016 4.00108 13.8559C4.00043 12.9103 4.71682 12.1428 5.60119 12.1422H7.2013V13.8559C7.20195 14.8016 6.48556 15.569 5.60119 15.5697ZM8.26738 18.4269V13.8559C8.26673 12.9103 8.98246 12.1428 9.86749 12.1428C10.7519 12.1442 11.4682 12.9117 11.4669 13.8573V18.4276C11.4676 19.3733 10.7512 20.1407 9.86683 20.1414C8.98246 20.14 8.26607 19.3726 8.26738 18.4269Z"
      fill="#0D1030"
    />
  </StyledSVG>
);

export default SlackIcon;
