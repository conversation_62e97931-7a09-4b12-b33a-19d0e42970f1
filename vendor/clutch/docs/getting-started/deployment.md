---
title: Deployment
{{ .EditURL }}
---

The recommend way to deploy Clutch is as a single binary with backend and frontend code combined. This is the default build that occurs. See [Build Guides](/docs/getting-started/build-guides) for more information.

The backend configuration also needs to be distributed alongside the binary for a successful deployment.

Standardized deployment instructions will be published here at a later date for deployment to various cloud environments and Kubernetes.

:::info
The project is also considering a move to server-side rendering for the frontend to better host feature-rich deployments.
:::
