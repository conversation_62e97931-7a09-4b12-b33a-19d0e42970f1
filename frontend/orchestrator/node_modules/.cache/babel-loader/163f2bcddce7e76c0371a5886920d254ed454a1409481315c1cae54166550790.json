{"ast": null, "code": "import React,{useState}from'react';import{Link,useLocation}from'react-router-dom';import{useAuth}from'../auth/AuthContext';import SearchField from'./SearchField';import{ChartBarIcon,CogIcon,MagnifyingGlassIcon,GlobeAltIcon,ArrowTrendingUpIcon,ShieldCheckIcon,CircleStackIcon,Bars3Icon,XMarkIcon,UserCircleIcon,ArrowRightOnRectangleIcon,UsersIcon,FlagIcon}from'@heroicons/react/24/outline';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";// Base navigation items\nconst baseNavigation=[{name:'Dashboard',href:'/dashboard',icon:ChartBarIcon},{name:'Search',href:'/search',icon:MagnifyingGlassIcon},{name:'Discovery Wizard',href:'/discovery',icon:MagnifyingGlassIcon},{name:'Workflows',href:'/workflows',icon:CogIcon},{name:'Envoy Config',href:'/envoy',icon:GlobeAltIcon},{name:'Autoscaler',href:'/autoscaler',icon:ArrowTrendingUpIcon},{name:'Audit',href:'/audit',icon:ShieldCheckIcon},{name:'Database',href:'/database',icon:CircleStackIcon}];// Admin navigation items\nconst adminNavigation=[{name:'Admin Dashboard',href:'/admin',icon:ChartBarIcon},{name:'User Management',href:'/admin/users',icon:UsersIcon},{name:'RBAC Management',href:'/admin/rbac',icon:ShieldCheckIcon},{name:'Feature Flags',href:'/admin/featureflags',icon:FlagIcon},{name:'Settings',href:'/admin/settings',icon:CogIcon}];// User navigation items\nconst userNavigation=[{name:'Profile',href:'/profile',icon:UserCircleIcon}];export function Layout(_ref){var _user$roles,_user$roles2,_user$roles3;let{children}=_ref;const[sidebarOpen,setSidebarOpen]=useState(false);const[userMenuOpen,setUserMenuOpen]=useState(false);const location=useLocation();const{user,logout}=useAuth();// Combine navigation based on user role\nconst navigation=[...baseNavigation,...(user!==null&&user!==void 0&&(_user$roles=user.roles)!==null&&_user$roles!==void 0&&_user$roles.includes('admin')?adminNavigation:[]),...userNavigation];return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-gray-900\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"fixed inset-0 z-50 lg:hidden \".concat(sidebarOpen?'block':'hidden'),children:[/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 bg-gray-600 bg-opacity-75\",onClick:()=>setSidebarOpen(false)}),/*#__PURE__*/_jsxs(\"div\",{className:\"fixed inset-y-0 left-0 flex w-64 flex-col bg-gray-800\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex h-16 items-center justify-between px-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-xl font-bold text-cyan-400\",children:\"\\uD83D\\uDE80 CAINuro\"})}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",className:\"text-gray-300 hover:text-white\",onClick:()=>setSidebarOpen(false),children:/*#__PURE__*/_jsx(XMarkIcon,{className:\"h-6 w-6\"})})]}),/*#__PURE__*/_jsx(\"nav\",{className:\"flex-1 space-y-1 px-2 py-4\",children:navigation.map(item=>{const isActive=location.pathname===item.href;return/*#__PURE__*/_jsxs(Link,{to:item.href,className:\"group flex items-center px-2 py-2 text-sm font-medium rounded-md \".concat(isActive?'bg-cyan-600 text-white':'text-gray-300 hover:bg-gray-700 hover:text-white'),onClick:()=>setSidebarOpen(false),children:[/*#__PURE__*/_jsx(item.icon,{className:\"mr-3 h-6 w-6 flex-shrink-0\"}),item.name]},item.name);})})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col flex-grow bg-gray-800 pt-5 pb-4 overflow-y-auto\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center flex-shrink-0 px-4\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-xl font-bold text-cyan-400\",children:\"\\uD83D\\uDE80 CAINuro Orchestrator\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-5 flex-1 flex flex-col\",children:/*#__PURE__*/_jsx(\"nav\",{className:\"flex-1 px-2 space-y-1\",children:navigation.map(item=>{const isActive=location.pathname===item.href;return/*#__PURE__*/_jsxs(Link,{to:item.href,className:\"group flex items-center px-2 py-2 text-sm font-medium rounded-md \".concat(isActive?'bg-cyan-600 text-white':'text-gray-300 hover:bg-gray-700 hover:text-white'),children:[/*#__PURE__*/_jsx(item.icon,{className:\"mr-3 h-6 w-6 flex-shrink-0\"}),item.name]},item.name);})})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0 flex border-t border-gray-700 p-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center w-full\",children:[/*#__PURE__*/_jsx(UserCircleIcon,{className:\"h-8 w-8 text-gray-400\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"ml-3 flex-1\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-gray-300\",children:(user===null||user===void 0?void 0:user.name)||(user===null||user===void 0?void 0:user.email)||'User'}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-400\",children:(user===null||user===void 0?void 0:(_user$roles2=user.roles)===null||_user$roles2===void 0?void 0:_user$roles2.join(', '))||'Loading...'})]}),/*#__PURE__*/_jsx(\"button\",{onClick:logout,className:\"ml-2 p-1 text-gray-400 hover:text-white\",title:\"Logout\",children:/*#__PURE__*/_jsx(ArrowRightOnRectangleIcon,{className:\"h-5 w-5\"})})]})})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"lg:pl-64 flex flex-col flex-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"sticky top-0 z-10 flex-shrink-0 flex h-16 bg-gray-800 shadow\",children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",className:\"px-4 border-r border-gray-700 text-gray-300 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-cyan-500 lg:hidden\",onClick:()=>setSidebarOpen(true),children:/*#__PURE__*/_jsx(Bars3Icon,{className:\"h-6 w-6\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-4 flex justify-between\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 flex\",children:/*#__PURE__*/_jsx(\"div\",{className:\"w-full flex md:ml-0\",children:/*#__PURE__*/_jsx(SearchField,{})})}),/*#__PURE__*/_jsx(\"div\",{className:\"ml-4 flex items-center md:ml-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\",children:\"All Systems Operational\"}),/*#__PURE__*/_jsx(\"div\",{className:\"w-3 h-3 bg-green-400 rounded-full animate-pulse\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setUserMenuOpen(!userMenuOpen),className:\"flex items-center space-x-2 text-gray-300 hover:text-white p-2 rounded-md\",children:[/*#__PURE__*/_jsx(UserCircleIcon,{className:\"h-6 w-6\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium\",children:(user===null||user===void 0?void 0:user.name)||(user===null||user===void 0?void 0:user.email)||'User'})]}),userMenuOpen&&/*#__PURE__*/_jsxs(\"div\",{className:\"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"px-4 py-2 border-b border-gray-200\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-gray-900\",children:(user===null||user===void 0?void 0:user.name)||'User'}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500\",children:user===null||user===void 0?void 0:user.email}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-400\",children:user===null||user===void 0?void 0:(_user$roles3=user.roles)===null||_user$roles3===void 0?void 0:_user$roles3.join(', ')})]}),/*#__PURE__*/_jsxs(Link,{to:\"/profile\",onClick:()=>setUserMenuOpen(false),className:\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",children:[/*#__PURE__*/_jsx(UserCircleIcon,{className:\"inline h-4 w-4 mr-2\"}),\"Profile\"]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{setUserMenuOpen(false);logout();},className:\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",children:[/*#__PURE__*/_jsx(ArrowRightOnRectangleIcon,{className:\"inline h-4 w-4 mr-2\"}),\"Sign out\"]})]})]})]})})]})]}),/*#__PURE__*/_jsx(\"main\",{className:\"flex-1\",children:/*#__PURE__*/_jsx(\"div\",{className:\"py-6\",children:/*#__PURE__*/_jsx(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 md:px-8\",children:children})})})]})]});}", "map": {"version": 3, "names": ["React", "useState", "Link", "useLocation", "useAuth", "SearchField", "ChartBarIcon", "CogIcon", "MagnifyingGlassIcon", "GlobeAltIcon", "ArrowTrendingUpIcon", "ShieldCheckIcon", "CircleStackIcon", "Bars3Icon", "XMarkIcon", "UserCircleIcon", "ArrowRightOnRectangleIcon", "UsersIcon", "FlagIcon", "jsx", "_jsx", "jsxs", "_jsxs", "baseNavigation", "name", "href", "icon", "adminNavigation", "userNavigation", "Layout", "_ref", "_user$roles", "_user$roles2", "_user$roles3", "children", "sidebarOpen", "setSidebarOpen", "userMenuOpen", "setUserMenuOpen", "location", "user", "logout", "navigation", "roles", "includes", "className", "concat", "onClick", "type", "map", "item", "isActive", "pathname", "to", "email", "join", "title"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/components/Layout.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { useAuth } from '../auth/AuthContext';\nimport SearchField from './SearchField';\nimport {\n  ChartBarIcon,\n  CogIcon,\n  MagnifyingGlassIcon,\n  GlobeAltIcon,\n  ArrowTrendingUpIcon,\n  ShieldCheckIcon,\n  CircleStackIcon,\n  Bars3Icon,\n  XMarkIcon,\n  UserCircleIcon,\n  ArrowRightOnRectangleIcon,\n  UsersIcon,\n  FlagIcon,\n} from '@heroicons/react/24/outline';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\n// Base navigation items\nconst baseNavigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: ChartBarIcon },\n  { name: 'Search', href: '/search', icon: MagnifyingGlassIcon },\n  { name: 'Discovery Wizard', href: '/discovery', icon: MagnifyingGlassIcon },\n  { name: 'Workflows', href: '/workflows', icon: CogIcon },\n  { name: 'Envoy Config', href: '/envoy', icon: GlobeAltIcon },\n  { name: 'Autoscaler', href: '/autoscaler', icon: ArrowTrendingUpIcon },\n  { name: '<PERSON><PERSON>', href: '/audit', icon: ShieldCheckIcon },\n  { name: 'Database', href: '/database', icon: CircleStackIcon },\n];\n\n// Admin navigation items\nconst adminNavigation = [\n  { name: 'Admin Dashboard', href: '/admin', icon: ChartBarIcon },\n  { name: 'User Management', href: '/admin/users', icon: UsersIcon },\n  { name: 'RBAC Management', href: '/admin/rbac', icon: ShieldCheckIcon },\n  { name: 'Feature Flags', href: '/admin/featureflags', icon: FlagIcon },\n  { name: 'Settings', href: '/admin/settings', icon: CogIcon },\n];\n\n// User navigation items\nconst userNavigation = [\n  { name: 'Profile', href: '/profile', icon: UserCircleIcon },\n];\n\nexport function Layout({ children }: LayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [userMenuOpen, setUserMenuOpen] = useState(false);\n  const location = useLocation();\n  const { user, logout } = useAuth();\n\n  // Combine navigation based on user role\n  const navigation = [\n    ...baseNavigation,\n    ...(user?.roles?.includes('admin') ? adminNavigation : []),\n    ...userNavigation,\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-900\">\n      {/* Mobile sidebar */}\n      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"fixed inset-y-0 left-0 flex w-64 flex-col bg-gray-800\">\n          <div className=\"flex h-16 items-center justify-between px-4\">\n            <div className=\"flex items-center\">\n              <span className=\"text-xl font-bold text-cyan-400\">🚀 CAINuro</span>\n            </div>\n            <button\n              type=\"button\"\n              className=\"text-gray-300 hover:text-white\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <XMarkIcon className=\"h-6 w-6\" />\n            </button>\n          </div>\n          <nav className=\"flex-1 space-y-1 px-2 py-4\">\n            {navigation.map((item) => {\n              const isActive = location.pathname === item.href;\n              return (\n                <Link\n                  key={item.name}\n                  to={item.href}\n                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${\n                    isActive\n                      ? 'bg-cyan-600 text-white'\n                      : 'text-gray-300 hover:bg-gray-700 hover:text-white'\n                  }`}\n                  onClick={() => setSidebarOpen(false)}\n                >\n                  <item.icon className=\"mr-3 h-6 w-6 flex-shrink-0\" />\n                  {item.name}\n                </Link>\n              );\n            })}\n          </nav>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex flex-col flex-grow bg-gray-800 pt-5 pb-4 overflow-y-auto\">\n          <div className=\"flex items-center flex-shrink-0 px-4\">\n            <span className=\"text-xl font-bold text-cyan-400\">🚀 CAINuro Orchestrator</span>\n          </div>\n          <div className=\"mt-5 flex-1 flex flex-col\">\n            <nav className=\"flex-1 px-2 space-y-1\">\n              {navigation.map((item) => {\n                const isActive = location.pathname === item.href;\n                return (\n                  <Link\n                    key={item.name}\n                    to={item.href}\n                    className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${\n                      isActive\n                        ? 'bg-cyan-600 text-white'\n                        : 'text-gray-300 hover:bg-gray-700 hover:text-white'\n                    }`}\n                  >\n                    <item.icon className=\"mr-3 h-6 w-6 flex-shrink-0\" />\n                    {item.name}\n                  </Link>\n                );\n              })}\n            </nav>\n          </div>\n          <div className=\"flex-shrink-0 flex border-t border-gray-700 p-4\">\n            <div className=\"flex items-center w-full\">\n              <UserCircleIcon className=\"h-8 w-8 text-gray-400\" />\n              <div className=\"ml-3 flex-1\">\n                <p className=\"text-sm font-medium text-gray-300\">{user?.name || user?.email || 'User'}</p>\n                <p className=\"text-xs text-gray-400\">{user?.roles?.join(', ') || 'Loading...'}</p>\n              </div>\n              <button\n                onClick={logout}\n                className=\"ml-2 p-1 text-gray-400 hover:text-white\"\n                title=\"Logout\"\n              >\n                <ArrowRightOnRectangleIcon className=\"h-5 w-5\" />\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64 flex flex-col flex-1\">\n        {/* Top bar */}\n        <div className=\"sticky top-0 z-10 flex-shrink-0 flex h-16 bg-gray-800 shadow\">\n          <button\n            type=\"button\"\n            className=\"px-4 border-r border-gray-700 text-gray-300 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-cyan-500 lg:hidden\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <Bars3Icon className=\"h-6 w-6\" />\n          </button>\n          <div className=\"flex-1 px-4 flex justify-between\">\n            <div className=\"flex-1 flex\">\n              <div className=\"w-full flex md:ml-0\">\n                <SearchField />\n              </div>\n            </div>\n            <div className=\"ml-4 flex items-center md:ml-6\">\n              <div className=\"flex items-center space-x-4\">\n                <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n                  All Systems Operational\n                </span>\n                <div className=\"w-3 h-3 bg-green-400 rounded-full animate-pulse\"></div>\n\n                {/* User menu */}\n                <div className=\"relative\">\n                  <button\n                    onClick={() => setUserMenuOpen(!userMenuOpen)}\n                    className=\"flex items-center space-x-2 text-gray-300 hover:text-white p-2 rounded-md\"\n                  >\n                    <UserCircleIcon className=\"h-6 w-6\" />\n                    <span className=\"text-sm font-medium\">{user?.name || user?.email || 'User'}</span>\n                  </button>\n\n                  {userMenuOpen && (\n                    <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50\">\n                      <div className=\"px-4 py-2 border-b border-gray-200\">\n                        <p className=\"text-sm font-medium text-gray-900\">{user?.name || 'User'}</p>\n                        <p className=\"text-sm text-gray-500\">{user?.email}</p>\n                        <p className=\"text-xs text-gray-400\">{user?.roles?.join(', ')}</p>\n                      </div>\n                      <Link\n                        to=\"/profile\"\n                        onClick={() => setUserMenuOpen(false)}\n                        className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                      >\n                        <UserCircleIcon className=\"inline h-4 w-4 mr-2\" />\n                        Profile\n                      </Link>\n                      <button\n                        onClick={() => {\n                          setUserMenuOpen(false);\n                          logout();\n                        }}\n                        className=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                      >\n                        <ArrowRightOnRectangleIcon className=\"inline h-4 w-4 mr-2\" />\n                        Sign out\n                      </button>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1\">\n          <div className=\"py-6\">\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 md:px-8\">\n              {children}\n            </div>\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,IAAI,CAAEC,WAAW,KAAQ,kBAAkB,CACpD,OAASC,OAAO,KAAQ,qBAAqB,CAC7C,MAAO,CAAAC,WAAW,KAAM,eAAe,CACvC,OACEC,YAAY,CACZC,OAAO,CACPC,mBAAmB,CACnBC,YAAY,CACZC,mBAAmB,CACnBC,eAAe,CACfC,eAAe,CACfC,SAAS,CACTC,SAAS,CACTC,cAAc,CACdC,yBAAyB,CACzBC,SAAS,CACTC,QAAQ,KACH,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAMrC;AACA,KAAM,CAAAC,cAAc,CAAG,CACrB,CAAEC,IAAI,CAAE,WAAW,CAAEC,IAAI,CAAE,YAAY,CAAEC,IAAI,CAAEpB,YAAa,CAAC,CAC7D,CAAEkB,IAAI,CAAE,QAAQ,CAAEC,IAAI,CAAE,SAAS,CAAEC,IAAI,CAAElB,mBAAoB,CAAC,CAC9D,CAAEgB,IAAI,CAAE,kBAAkB,CAAEC,IAAI,CAAE,YAAY,CAAEC,IAAI,CAAElB,mBAAoB,CAAC,CAC3E,CAAEgB,IAAI,CAAE,WAAW,CAAEC,IAAI,CAAE,YAAY,CAAEC,IAAI,CAAEnB,OAAQ,CAAC,CACxD,CAAEiB,IAAI,CAAE,cAAc,CAAEC,IAAI,CAAE,QAAQ,CAAEC,IAAI,CAAEjB,YAAa,CAAC,CAC5D,CAAEe,IAAI,CAAE,YAAY,CAAEC,IAAI,CAAE,aAAa,CAAEC,IAAI,CAAEhB,mBAAoB,CAAC,CACtE,CAAEc,IAAI,CAAE,OAAO,CAAEC,IAAI,CAAE,QAAQ,CAAEC,IAAI,CAAEf,eAAgB,CAAC,CACxD,CAAEa,IAAI,CAAE,UAAU,CAAEC,IAAI,CAAE,WAAW,CAAEC,IAAI,CAAEd,eAAgB,CAAC,CAC/D,CAED;AACA,KAAM,CAAAe,eAAe,CAAG,CACtB,CAAEH,IAAI,CAAE,iBAAiB,CAAEC,IAAI,CAAE,QAAQ,CAAEC,IAAI,CAAEpB,YAAa,CAAC,CAC/D,CAAEkB,IAAI,CAAE,iBAAiB,CAAEC,IAAI,CAAE,cAAc,CAAEC,IAAI,CAAET,SAAU,CAAC,CAClE,CAAEO,IAAI,CAAE,iBAAiB,CAAEC,IAAI,CAAE,aAAa,CAAEC,IAAI,CAAEf,eAAgB,CAAC,CACvE,CAAEa,IAAI,CAAE,eAAe,CAAEC,IAAI,CAAE,qBAAqB,CAAEC,IAAI,CAAER,QAAS,CAAC,CACtE,CAAEM,IAAI,CAAE,UAAU,CAAEC,IAAI,CAAE,iBAAiB,CAAEC,IAAI,CAAEnB,OAAQ,CAAC,CAC7D,CAED;AACA,KAAM,CAAAqB,cAAc,CAAG,CACrB,CAAEJ,IAAI,CAAE,SAAS,CAAEC,IAAI,CAAE,UAAU,CAAEC,IAAI,CAAEX,cAAe,CAAC,CAC5D,CAED,MAAO,SAAS,CAAAc,MAAMA,CAAAC,IAAA,CAA4B,KAAAC,WAAA,CAAAC,YAAA,CAAAC,YAAA,IAA3B,CAAEC,QAAsB,CAAC,CAAAJ,IAAA,CAC9C,KAAM,CAACK,WAAW,CAAEC,cAAc,CAAC,CAAGnC,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACoC,YAAY,CAAEC,eAAe,CAAC,CAAGrC,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAAsC,QAAQ,CAAGpC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAEqC,IAAI,CAAEC,MAAO,CAAC,CAAGrC,OAAO,CAAC,CAAC,CAElC;AACA,KAAM,CAAAsC,UAAU,CAAG,CACjB,GAAGnB,cAAc,CACjB,IAAIiB,IAAI,SAAJA,IAAI,YAAAT,WAAA,CAAJS,IAAI,CAAEG,KAAK,UAAAZ,WAAA,WAAXA,WAAA,CAAaa,QAAQ,CAAC,OAAO,CAAC,CAAGjB,eAAe,CAAG,EAAE,CAAC,CAC1D,GAAGC,cAAc,CAClB,CAED,mBACEN,KAAA,QAAKuB,SAAS,CAAC,0BAA0B,CAAAX,QAAA,eAEvCZ,KAAA,QAAKuB,SAAS,iCAAAC,MAAA,CAAkCX,WAAW,CAAG,OAAO,CAAG,QAAQ,CAAG,CAAAD,QAAA,eACjFd,IAAA,QAAKyB,SAAS,CAAC,yCAAyC,CAACE,OAAO,CAAEA,CAAA,GAAMX,cAAc,CAAC,KAAK,CAAE,CAAE,CAAC,cACjGd,KAAA,QAAKuB,SAAS,CAAC,uDAAuD,CAAAX,QAAA,eACpEZ,KAAA,QAAKuB,SAAS,CAAC,6CAA6C,CAAAX,QAAA,eAC1Dd,IAAA,QAAKyB,SAAS,CAAC,mBAAmB,CAAAX,QAAA,cAChCd,IAAA,SAAMyB,SAAS,CAAC,iCAAiC,CAAAX,QAAA,CAAC,sBAAU,CAAM,CAAC,CAChE,CAAC,cACNd,IAAA,WACE4B,IAAI,CAAC,QAAQ,CACbH,SAAS,CAAC,gCAAgC,CAC1CE,OAAO,CAAEA,CAAA,GAAMX,cAAc,CAAC,KAAK,CAAE,CAAAF,QAAA,cAErCd,IAAA,CAACN,SAAS,EAAC+B,SAAS,CAAC,SAAS,CAAE,CAAC,CAC3B,CAAC,EACN,CAAC,cACNzB,IAAA,QAAKyB,SAAS,CAAC,4BAA4B,CAAAX,QAAA,CACxCQ,UAAU,CAACO,GAAG,CAAEC,IAAI,EAAK,CACxB,KAAM,CAAAC,QAAQ,CAAGZ,QAAQ,CAACa,QAAQ,GAAKF,IAAI,CAACzB,IAAI,CAChD,mBACEH,KAAA,CAACpB,IAAI,EAEHmD,EAAE,CAAEH,IAAI,CAACzB,IAAK,CACdoB,SAAS,qEAAAC,MAAA,CACPK,QAAQ,CACJ,wBAAwB,CACxB,kDAAkD,CACrD,CACHJ,OAAO,CAAEA,CAAA,GAAMX,cAAc,CAAC,KAAK,CAAE,CAAAF,QAAA,eAErCd,IAAA,CAAC8B,IAAI,CAACxB,IAAI,EAACmB,SAAS,CAAC,4BAA4B,CAAE,CAAC,CACnDK,IAAI,CAAC1B,IAAI,GAVL0B,IAAI,CAAC1B,IAWN,CAAC,CAEX,CAAC,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,cAGNJ,IAAA,QAAKyB,SAAS,CAAC,0DAA0D,CAAAX,QAAA,cACvEZ,KAAA,QAAKuB,SAAS,CAAC,+DAA+D,CAAAX,QAAA,eAC5Ed,IAAA,QAAKyB,SAAS,CAAC,sCAAsC,CAAAX,QAAA,cACnDd,IAAA,SAAMyB,SAAS,CAAC,iCAAiC,CAAAX,QAAA,CAAC,mCAAuB,CAAM,CAAC,CAC7E,CAAC,cACNd,IAAA,QAAKyB,SAAS,CAAC,2BAA2B,CAAAX,QAAA,cACxCd,IAAA,QAAKyB,SAAS,CAAC,uBAAuB,CAAAX,QAAA,CACnCQ,UAAU,CAACO,GAAG,CAAEC,IAAI,EAAK,CACxB,KAAM,CAAAC,QAAQ,CAAGZ,QAAQ,CAACa,QAAQ,GAAKF,IAAI,CAACzB,IAAI,CAChD,mBACEH,KAAA,CAACpB,IAAI,EAEHmD,EAAE,CAAEH,IAAI,CAACzB,IAAK,CACdoB,SAAS,qEAAAC,MAAA,CACPK,QAAQ,CACJ,wBAAwB,CACxB,kDAAkD,CACrD,CAAAjB,QAAA,eAEHd,IAAA,CAAC8B,IAAI,CAACxB,IAAI,EAACmB,SAAS,CAAC,4BAA4B,CAAE,CAAC,CACnDK,IAAI,CAAC1B,IAAI,GATL0B,IAAI,CAAC1B,IAUN,CAAC,CAEX,CAAC,CAAC,CACC,CAAC,CACH,CAAC,cACNJ,IAAA,QAAKyB,SAAS,CAAC,iDAAiD,CAAAX,QAAA,cAC9DZ,KAAA,QAAKuB,SAAS,CAAC,0BAA0B,CAAAX,QAAA,eACvCd,IAAA,CAACL,cAAc,EAAC8B,SAAS,CAAC,uBAAuB,CAAE,CAAC,cACpDvB,KAAA,QAAKuB,SAAS,CAAC,aAAa,CAAAX,QAAA,eAC1Bd,IAAA,MAAGyB,SAAS,CAAC,mCAAmC,CAAAX,QAAA,CAAE,CAAAM,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEhB,IAAI,IAAIgB,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEc,KAAK,GAAI,MAAM,CAAI,CAAC,cAC1FlC,IAAA,MAAGyB,SAAS,CAAC,uBAAuB,CAAAX,QAAA,CAAE,CAAAM,IAAI,SAAJA,IAAI,kBAAAR,YAAA,CAAJQ,IAAI,CAAEG,KAAK,UAAAX,YAAA,iBAAXA,YAAA,CAAauB,IAAI,CAAC,IAAI,CAAC,GAAI,YAAY,CAAI,CAAC,EAC/E,CAAC,cACNnC,IAAA,WACE2B,OAAO,CAAEN,MAAO,CAChBI,SAAS,CAAC,yCAAyC,CACnDW,KAAK,CAAC,QAAQ,CAAAtB,QAAA,cAEdd,IAAA,CAACJ,yBAAyB,EAAC6B,SAAS,CAAC,SAAS,CAAE,CAAC,CAC3C,CAAC,EACN,CAAC,CACH,CAAC,EACH,CAAC,CACH,CAAC,cAGNvB,KAAA,QAAKuB,SAAS,CAAC,+BAA+B,CAAAX,QAAA,eAE5CZ,KAAA,QAAKuB,SAAS,CAAC,8DAA8D,CAAAX,QAAA,eAC3Ed,IAAA,WACE4B,IAAI,CAAC,QAAQ,CACbH,SAAS,CAAC,4HAA4H,CACtIE,OAAO,CAAEA,CAAA,GAAMX,cAAc,CAAC,IAAI,CAAE,CAAAF,QAAA,cAEpCd,IAAA,CAACP,SAAS,EAACgC,SAAS,CAAC,SAAS,CAAE,CAAC,CAC3B,CAAC,cACTvB,KAAA,QAAKuB,SAAS,CAAC,kCAAkC,CAAAX,QAAA,eAC/Cd,IAAA,QAAKyB,SAAS,CAAC,aAAa,CAAAX,QAAA,cAC1Bd,IAAA,QAAKyB,SAAS,CAAC,qBAAqB,CAAAX,QAAA,cAClCd,IAAA,CAACf,WAAW,GAAE,CAAC,CACZ,CAAC,CACH,CAAC,cACNe,IAAA,QAAKyB,SAAS,CAAC,gCAAgC,CAAAX,QAAA,cAC7CZ,KAAA,QAAKuB,SAAS,CAAC,6BAA6B,CAAAX,QAAA,eAC1Cd,IAAA,SAAMyB,SAAS,CAAC,qGAAqG,CAAAX,QAAA,CAAC,yBAEtH,CAAM,CAAC,cACPd,IAAA,QAAKyB,SAAS,CAAC,iDAAiD,CAAM,CAAC,cAGvEvB,KAAA,QAAKuB,SAAS,CAAC,UAAU,CAAAX,QAAA,eACvBZ,KAAA,WACEyB,OAAO,CAAEA,CAAA,GAAMT,eAAe,CAAC,CAACD,YAAY,CAAE,CAC9CQ,SAAS,CAAC,2EAA2E,CAAAX,QAAA,eAErFd,IAAA,CAACL,cAAc,EAAC8B,SAAS,CAAC,SAAS,CAAE,CAAC,cACtCzB,IAAA,SAAMyB,SAAS,CAAC,qBAAqB,CAAAX,QAAA,CAAE,CAAAM,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEhB,IAAI,IAAIgB,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEc,KAAK,GAAI,MAAM,CAAO,CAAC,EAC5E,CAAC,CAERjB,YAAY,eACXf,KAAA,QAAKuB,SAAS,CAAC,oEAAoE,CAAAX,QAAA,eACjFZ,KAAA,QAAKuB,SAAS,CAAC,oCAAoC,CAAAX,QAAA,eACjDd,IAAA,MAAGyB,SAAS,CAAC,mCAAmC,CAAAX,QAAA,CAAE,CAAAM,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEhB,IAAI,GAAI,MAAM,CAAI,CAAC,cAC3EJ,IAAA,MAAGyB,SAAS,CAAC,uBAAuB,CAAAX,QAAA,CAAEM,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEc,KAAK,CAAI,CAAC,cACtDlC,IAAA,MAAGyB,SAAS,CAAC,uBAAuB,CAAAX,QAAA,CAAEM,IAAI,SAAJA,IAAI,kBAAAP,YAAA,CAAJO,IAAI,CAAEG,KAAK,UAAAV,YAAA,iBAAXA,YAAA,CAAasB,IAAI,CAAC,IAAI,CAAC,CAAI,CAAC,EAC/D,CAAC,cACNjC,KAAA,CAACpB,IAAI,EACHmD,EAAE,CAAC,UAAU,CACbN,OAAO,CAAEA,CAAA,GAAMT,eAAe,CAAC,KAAK,CAAE,CACtCO,SAAS,CAAC,yDAAyD,CAAAX,QAAA,eAEnEd,IAAA,CAACL,cAAc,EAAC8B,SAAS,CAAC,qBAAqB,CAAE,CAAC,UAEpD,EAAM,CAAC,cACPvB,KAAA,WACEyB,OAAO,CAAEA,CAAA,GAAM,CACbT,eAAe,CAAC,KAAK,CAAC,CACtBG,MAAM,CAAC,CAAC,CACV,CAAE,CACFI,SAAS,CAAC,0EAA0E,CAAAX,QAAA,eAEpFd,IAAA,CAACJ,yBAAyB,EAAC6B,SAAS,CAAC,qBAAqB,CAAE,CAAC,WAE/D,EAAQ,CAAC,EACN,CACN,EACE,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,EACH,CAAC,cAGNzB,IAAA,SAAMyB,SAAS,CAAC,QAAQ,CAAAX,QAAA,cACtBd,IAAA,QAAKyB,SAAS,CAAC,MAAM,CAAAX,QAAA,cACnBd,IAAA,QAAKyB,SAAS,CAAC,wCAAwC,CAAAX,QAAA,CACpDA,QAAQ,CACN,CAAC,CACH,CAAC,CACF,CAAC,EACJ,CAAC,EACH,CAAC,CAEV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}