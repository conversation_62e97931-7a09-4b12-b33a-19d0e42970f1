package resolver

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"time"

	"go.uber.org/zap"
)

// Service represents the enhanced resolver service
type Service struct {
	logger *zap.Logger
}

// Resource represents a cloud resource
type Resource struct {
	ID          string            `json:"id"`
	Name        string            `json:"name"`
	Type        string            `json:"type"`
	Provider    string            `json:"provider"`
	Region      string            `json:"region"`
	Status      string            `json:"status"`
	Tags        map[string]string `json:"tags"`
	Metadata    map[string]interface{} `json:"metadata"`
	CreatedAt   time.Time         `json:"created_at"`
	UpdatedAt   time.Time         `json:"updated_at"`
}

// AutocompleteResult represents an autocomplete suggestion
type AutocompleteResult struct {
	ID    string `json:"id"`
	Label string `json:"label"`
	Type  string `json:"type"`
	Score float64 `json:"score"`
}

// SearchRequest represents a search request
type SearchRequest struct {
	Query         string            `json:"query"`
	ResourceType  string            `json:"resource_type,omitempty"`
	Provider      string            `json:"provider,omitempty"`
	Region        string            `json:"region,omitempty"`
	Tags          map[string]string `json:"tags,omitempty"`
	Limit         int               `json:"limit,omitempty"`
	CaseSensitive bool              `json:"case_sensitive,omitempty"`
}

// SearchResponse represents a search response
type SearchResponse struct {
	Resources    []Resource `json:"resources"`
	TotalCount   int        `json:"total_count"`
	HasMore      bool       `json:"has_more"`
	SearchTime   string     `json:"search_time"`
}

// AutocompleteRequest represents an autocomplete request
type AutocompleteRequest struct {
	Query         string `json:"query"`
	ResourceType  string `json:"resource_type,omitempty"`
	Limit         int    `json:"limit,omitempty"`
	CaseSensitive bool   `json:"case_sensitive,omitempty"`
}

// New creates a new resolver service
func New(logger *zap.Logger) *Service {
	return &Service{
		logger: logger,
	}
}

// Search performs an enhanced search across resources
func (s *Service) Search(ctx context.Context, req SearchRequest) (*SearchResponse, error) {
	startTime := time.Now()
	
	s.logger.Info("Performing enhanced search", 
		zap.String("query", req.Query),
		zap.String("type", req.ResourceType),
		zap.String("provider", req.Provider))

	// Get mock resources (in real implementation, this would query actual data sources)
	allResources := s.getMockResources()
	
	// Apply filters
	filteredResources := s.applyFilters(allResources, req)
	
	// Apply search query
	searchResults := s.applySearchQuery(filteredResources, req)
	
	// Sort by relevance
	sort.Slice(searchResults, func(i, j int) bool {
		return s.calculateRelevanceScore(searchResults[i], req.Query) > 
			   s.calculateRelevanceScore(searchResults[j], req.Query)
	})
	
	// Apply limit
	limit := req.Limit
	if limit == 0 {
		limit = 50 // Default limit
	}
	
	hasMore := len(searchResults) > limit
	if len(searchResults) > limit {
		searchResults = searchResults[:limit]
	}
	
	searchTime := time.Since(startTime)
	
	response := &SearchResponse{
		Resources:  searchResults,
		TotalCount: len(searchResults),
		HasMore:    hasMore,
		SearchTime: searchTime.String(),
	}
	
	s.logger.Info("Search completed", 
		zap.Int("results", len(searchResults)),
		zap.Duration("duration", searchTime))
	
	return response, nil
}

// Autocomplete provides search suggestions
func (s *Service) Autocomplete(ctx context.Context, req AutocompleteRequest) ([]AutocompleteResult, error) {
	s.logger.Debug("Performing autocomplete", zap.String("query", req.Query))
	
	if len(req.Query) < 2 {
		return []AutocompleteResult{}, nil
	}
	
	// Get mock resources
	allResources := s.getMockResources()
	
	var suggestions []AutocompleteResult
	query := req.Query
	if !req.CaseSensitive {
		query = strings.ToLower(query)
	}
	
	// Generate suggestions from resource names, IDs, and tags
	for _, resource := range allResources {
		score := s.calculateAutocompleteScore(resource, query, req.CaseSensitive)
		if score > 0 {
			label := fmt.Sprintf("%s (%s in %s)", resource.Name, resource.Type, resource.Region)
			suggestions = append(suggestions, AutocompleteResult{
				ID:    resource.ID,
				Label: label,
				Type:  resource.Type,
				Score: score,
			})
		}
	}
	
	// Sort by score
	sort.Slice(suggestions, func(i, j int) bool {
		return suggestions[i].Score > suggestions[j].Score
	})
	
	// Apply limit
	limit := req.Limit
	if limit == 0 {
		limit = 10 // Default autocomplete limit
	}
	
	if len(suggestions) > limit {
		suggestions = suggestions[:limit]
	}
	
	s.logger.Debug("Autocomplete completed", zap.Int("suggestions", len(suggestions)))
	return suggestions, nil
}

// GetResourceSchemas returns available resource schemas
func (s *Service) GetResourceSchemas(ctx context.Context, resourceType string) (map[string]interface{}, error) {
	schemas := map[string]interface{}{
		"aws.ec2.instance": map[string]interface{}{
			"properties": map[string]interface{}{
				"instance_id":    map[string]string{"type": "string", "description": "EC2 instance ID"},
				"instance_type":  map[string]string{"type": "string", "description": "Instance type (e.g., t3.micro)"},
				"state":          map[string]string{"type": "string", "description": "Instance state"},
				"public_ip":      map[string]string{"type": "string", "description": "Public IP address"},
				"private_ip":     map[string]string{"type": "string", "description": "Private IP address"},
				"vpc_id":         map[string]string{"type": "string", "description": "VPC ID"},
				"subnet_id":      map[string]string{"type": "string", "description": "Subnet ID"},
				"security_groups": map[string]string{"type": "array", "description": "Security groups"},
			},
		},
		"gcp.compute.instance": map[string]interface{}{
			"properties": map[string]interface{}{
				"name":         map[string]string{"type": "string", "description": "Instance name"},
				"machine_type": map[string]string{"type": "string", "description": "Machine type"},
				"status":       map[string]string{"type": "string", "description": "Instance status"},
				"zone":         map[string]string{"type": "string", "description": "Zone"},
				"network":      map[string]string{"type": "string", "description": "Network"},
			},
		},
		"k8s.pod": map[string]interface{}{
			"properties": map[string]interface{}{
				"name":      map[string]string{"type": "string", "description": "Pod name"},
				"namespace": map[string]string{"type": "string", "description": "Namespace"},
				"phase":     map[string]string{"type": "string", "description": "Pod phase"},
				"node":      map[string]string{"type": "string", "description": "Node name"},
				"containers": map[string]string{"type": "array", "description": "Containers"},
			},
		},
		"k8s.service": map[string]interface{}{
			"properties": map[string]interface{}{
				"name":      map[string]string{"type": "string", "description": "Service name"},
				"namespace": map[string]string{"type": "string", "description": "Namespace"},
				"type":      map[string]string{"type": "string", "description": "Service type"},
				"ports":     map[string]string{"type": "array", "description": "Service ports"},
				"selector":  map[string]string{"type": "object", "description": "Label selector"},
			},
		},
	}
	
	if resourceType != "" {
		if schema, exists := schemas[resourceType]; exists {
			return map[string]interface{}{resourceType: schema}, nil
		}
		return map[string]interface{}{}, nil
	}
	
	return schemas, nil
}

// getMockResources returns mock resources for demonstration
func (s *Service) getMockResources() []Resource {
	now := time.Now()
	
	return []Resource{
		{
			ID:       "i-1234567890abcdef0",
			Name:     "web-server-1",
			Type:     "aws.ec2.instance",
			Provider: "aws",
			Region:   "us-east-1",
			Status:   "running",
			Tags: map[string]string{
				"Environment": "production",
				"Team":        "platform",
				"Service":     "web",
			},
			Metadata: map[string]interface{}{
				"instance_type": "t3.medium",
				"public_ip":     "************",
				"private_ip":    "*********",
			},
			CreatedAt: now.Add(-24 * time.Hour),
			UpdatedAt: now.Add(-1 * time.Hour),
		},
		{
			ID:       "i-0987654321fedcba0",
			Name:     "api-server-1",
			Type:     "aws.ec2.instance",
			Provider: "aws",
			Region:   "us-west-2",
			Status:   "running",
			Tags: map[string]string{
				"Environment": "production",
				"Team":        "backend",
				"Service":     "api",
			},
			Metadata: map[string]interface{}{
				"instance_type": "t3.large",
				"public_ip":     "***********",
				"private_ip":    "*********",
			},
			CreatedAt: now.Add(-48 * time.Hour),
			UpdatedAt: now.Add(-2 * time.Hour),
		},
		{
			ID:       "gcp-instance-1",
			Name:     "database-server",
			Type:     "gcp.compute.instance",
			Provider: "gcp",
			Region:   "us-central1",
			Status:   "running",
			Tags: map[string]string{
				"Environment": "production",
				"Team":        "data",
				"Service":     "database",
			},
			Metadata: map[string]interface{}{
				"machine_type": "n1-standard-4",
				"zone":         "us-central1-a",
			},
			CreatedAt: now.Add(-72 * time.Hour),
			UpdatedAt: now.Add(-30 * time.Minute),
		},
		{
			ID:       "nginx-pod-1",
			Name:     "nginx-deployment-abc123",
			Type:     "k8s.pod",
			Provider: "kubernetes",
			Region:   "default",
			Status:   "running",
			Tags: map[string]string{
				"app":     "nginx",
				"version": "1.21",
			},
			Metadata: map[string]interface{}{
				"namespace": "default",
				"node":      "worker-1",
				"containers": []string{"nginx"},
			},
			CreatedAt: now.Add(-12 * time.Hour),
			UpdatedAt: now.Add(-15 * time.Minute),
		},
		{
			ID:       "web-service-1",
			Name:     "web-service",
			Type:     "k8s.service",
			Provider: "kubernetes",
			Region:   "default",
			Status:   "active",
			Tags: map[string]string{
				"app":  "web",
				"tier": "frontend",
			},
			Metadata: map[string]interface{}{
				"namespace": "default",
				"type":      "LoadBalancer",
				"ports":     []int{80, 443},
			},
			CreatedAt: now.Add(-36 * time.Hour),
			UpdatedAt: now.Add(-45 * time.Minute),
		},
	}
}

// applyFilters applies filters to resources
func (s *Service) applyFilters(resources []Resource, req SearchRequest) []Resource {
	var filtered []Resource
	
	for _, resource := range resources {
		// Filter by resource type
		if req.ResourceType != "" && resource.Type != req.ResourceType {
			continue
		}
		
		// Filter by provider
		if req.Provider != "" && resource.Provider != req.Provider {
			continue
		}
		
		// Filter by region
		if req.Region != "" && resource.Region != req.Region {
			continue
		}
		
		// Filter by tags
		if len(req.Tags) > 0 {
			matches := true
			for key, value := range req.Tags {
				if resourceValue, exists := resource.Tags[key]; !exists || resourceValue != value {
					matches = false
					break
				}
			}
			if !matches {
				continue
			}
		}
		
		filtered = append(filtered, resource)
	}
	
	return filtered
}

// applySearchQuery applies text search to resources
func (s *Service) applySearchQuery(resources []Resource, req SearchRequest) []Resource {
	if req.Query == "" {
		return resources
	}
	
	var results []Resource
	query := req.Query
	if !req.CaseSensitive {
		query = strings.ToLower(query)
	}
	
	for _, resource := range resources {
		if s.matchesQuery(resource, query, req.CaseSensitive) {
			results = append(results, resource)
		}
	}
	
	return results
}

// matchesQuery checks if a resource matches the search query
func (s *Service) matchesQuery(resource Resource, query string, caseSensitive bool) bool {
	searchFields := []string{
		resource.ID,
		resource.Name,
		resource.Type,
		resource.Provider,
		resource.Region,
		resource.Status,
	}
	
	// Add tag values to search fields
	for _, value := range resource.Tags {
		searchFields = append(searchFields, value)
	}
	
	for _, field := range searchFields {
		if !caseSensitive {
			field = strings.ToLower(field)
		}
		if strings.Contains(field, query) {
			return true
		}
	}
	
	return false
}

// calculateRelevanceScore calculates relevance score for search results
func (s *Service) calculateRelevanceScore(resource Resource, query string) float64 {
	score := 0.0
	query = strings.ToLower(query)
	
	// Exact matches get highest score
	if strings.ToLower(resource.Name) == query {
		score += 100
	} else if strings.Contains(strings.ToLower(resource.Name), query) {
		score += 50
	}
	
	if strings.ToLower(resource.ID) == query {
		score += 90
	} else if strings.Contains(strings.ToLower(resource.ID), query) {
		score += 40
	}
	
	// Type and provider matches
	if strings.Contains(strings.ToLower(resource.Type), query) {
		score += 30
	}
	if strings.Contains(strings.ToLower(resource.Provider), query) {
		score += 20
	}
	
	// Tag matches
	for _, value := range resource.Tags {
		if strings.Contains(strings.ToLower(value), query) {
			score += 10
		}
	}
	
	return score
}

// calculateAutocompleteScore calculates score for autocomplete suggestions
func (s *Service) calculateAutocompleteScore(resource Resource, query string, caseSensitive bool) float64 {
	score := 0.0
	
	searchFields := map[string]float64{
		resource.Name: 10.0,
		resource.ID:   8.0,
		resource.Type: 5.0,
	}
	
	for field, weight := range searchFields {
		if !caseSensitive {
			field = strings.ToLower(field)
		}
		
		if strings.HasPrefix(field, query) {
			score += weight * 2 // Prefix matches get double score
		} else if strings.Contains(field, query) {
			score += weight
		}
	}
	
	return score
}

// Health checks the health of the resolver service
func (s *Service) Health(ctx context.Context) error {
	// Simple health check - try to get mock resources
	resources := s.getMockResources()
	if len(resources) == 0 {
		return fmt.Errorf("no resources available")
	}
	return nil
}
