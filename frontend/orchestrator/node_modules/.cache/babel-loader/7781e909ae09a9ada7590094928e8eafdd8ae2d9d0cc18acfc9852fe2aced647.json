{"ast": null, "code": "import React from'react';import{Routes,Route}from'react-router-dom';import{Layout}from'./components/Layout';import{Dashboard}from'./pages/Dashboard';import SearchDashboard from'./pages/SearchDashboard';import DiscoveryWizard from'./pages/DiscoveryWizard';import WorkflowExecutor from'./pages/WorkflowExecutor';import EnvoyConfigEditor from'./pages/EnvoyConfigEditor';import AutoscalerDashboard from'./pages/AutoscalerDashboard';import AuditViewer from'./pages/AuditViewer';import DatabaseAdmin from'./pages/DatabaseAdmin';import'./index.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-theme-bg-container\",children:/*#__PURE__*/_jsx(Layout,{children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(Dashboard,{})}),/*#__PURE__*/_jsx(Route,{path:\"/dashboard\",element:/*#__PURE__*/_jsx(Dashboard,{})}),/*#__PURE__*/_jsx(Route,{path:\"/search\",element:/*#__PURE__*/_jsx(SearchDashboard,{})}),/*#__PURE__*/_jsx(Route,{path:\"/discovery\",element:/*#__PURE__*/_jsx(DiscoveryWizard,{})}),/*#__PURE__*/_jsx(Route,{path:\"/workflows\",element:/*#__PURE__*/_jsx(WorkflowExecutor,{})}),/*#__PURE__*/_jsx(Route,{path:\"/envoy\",element:/*#__PURE__*/_jsx(EnvoyConfigEditor,{})}),/*#__PURE__*/_jsx(Route,{path:\"/autoscaler\",element:/*#__PURE__*/_jsx(AutoscalerDashboard,{})}),/*#__PURE__*/_jsx(Route,{path:\"/audit\",element:/*#__PURE__*/_jsx(AuditViewer,{})}),/*#__PURE__*/_jsx(Route,{path:\"/database\",element:/*#__PURE__*/_jsx(DatabaseAdmin,{})})]})})});}export default App;", "map": {"version": 3, "names": ["React", "Routes", "Route", "Layout", "Dashboard", "SearchDashboard", "DiscoveryWizard", "WorkflowExecutor", "EnvoyConfigEditor", "AutoscalerDashboard", "AuditViewer", "DatabaseAdmin", "jsx", "_jsx", "jsxs", "_jsxs", "App", "className", "children", "path", "element"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { Routes, Route } from 'react-router-dom';\nimport { Layout } from './components/Layout';\nimport { Dashboard } from './pages/Dashboard';\nimport SearchDashboard from './pages/SearchDashboard';\nimport DiscoveryWizard from './pages/DiscoveryWizard';\nimport WorkflowExecutor from './pages/WorkflowExecutor';\nimport EnvoyConfigEditor from './pages/EnvoyConfigEditor';\nimport AutoscalerDashboard from './pages/AutoscalerDashboard';\nimport AuditViewer from './pages/AuditViewer';\nimport DatabaseAdmin from './pages/DatabaseAdmin';\nimport './index.css';\n\nfunction App() {\n  return (\n    <div className=\"min-h-screen bg-theme-bg-container\">\n      <Layout>\n        <Routes>\n          <Route path=\"/\" element={<Dashboard />} />\n          <Route path=\"/dashboard\" element={<Dashboard />} />\n          <Route path=\"/search\" element={<SearchDashboard />} />\n          <Route path=\"/discovery\" element={<DiscoveryWizard />} />\n          <Route path=\"/workflows\" element={<WorkflowExecutor />} />\n          <Route path=\"/envoy\" element={<EnvoyConfigEditor />} />\n          <Route path=\"/autoscaler\" element={<AutoscalerDashboard />} />\n          <Route path=\"/audit\" element={<AuditViewer />} />\n          <Route path=\"/database\" element={<DatabaseAdmin />} />\n        </Routes>\n      </Layout>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,MAAM,CAAEC,KAAK,KAAQ,kBAAkB,CAChD,OAASC,MAAM,KAAQ,qBAAqB,CAC5C,OAASC,SAAS,KAAQ,mBAAmB,CAC7C,MAAO,CAAAC,eAAe,KAAM,yBAAyB,CACrD,MAAO,CAAAC,eAAe,KAAM,yBAAyB,CACrD,MAAO,CAAAC,gBAAgB,KAAM,0BAA0B,CACvD,MAAO,CAAAC,iBAAiB,KAAM,2BAA2B,CACzD,MAAO,CAAAC,mBAAmB,KAAM,6BAA6B,CAC7D,MAAO,CAAAC,WAAW,KAAM,qBAAqB,CAC7C,MAAO,CAAAC,aAAa,KAAM,uBAAuB,CACjD,MAAO,aAAa,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAErB,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,mBACEH,IAAA,QAAKI,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjDL,IAAA,CAACV,MAAM,EAAAe,QAAA,cACLH,KAAA,CAACd,MAAM,EAAAiB,QAAA,eACLL,IAAA,CAACX,KAAK,EAACiB,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEP,IAAA,CAACT,SAAS,GAAE,CAAE,CAAE,CAAC,cAC1CS,IAAA,CAACX,KAAK,EAACiB,IAAI,CAAC,YAAY,CAACC,OAAO,cAAEP,IAAA,CAACT,SAAS,GAAE,CAAE,CAAE,CAAC,cACnDS,IAAA,CAACX,KAAK,EAACiB,IAAI,CAAC,SAAS,CAACC,OAAO,cAAEP,IAAA,CAACR,eAAe,GAAE,CAAE,CAAE,CAAC,cACtDQ,IAAA,CAACX,KAAK,EAACiB,IAAI,CAAC,YAAY,CAACC,OAAO,cAAEP,IAAA,CAACP,eAAe,GAAE,CAAE,CAAE,CAAC,cACzDO,IAAA,CAACX,KAAK,EAACiB,IAAI,CAAC,YAAY,CAACC,OAAO,cAAEP,IAAA,CAACN,gBAAgB,GAAE,CAAE,CAAE,CAAC,cAC1DM,IAAA,CAACX,KAAK,EAACiB,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAEP,IAAA,CAACL,iBAAiB,GAAE,CAAE,CAAE,CAAC,cACvDK,IAAA,CAACX,KAAK,EAACiB,IAAI,CAAC,aAAa,CAACC,OAAO,cAAEP,IAAA,CAACJ,mBAAmB,GAAE,CAAE,CAAE,CAAC,cAC9DI,IAAA,CAACX,KAAK,EAACiB,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAEP,IAAA,CAACH,WAAW,GAAE,CAAE,CAAE,CAAC,cACjDG,IAAA,CAACX,KAAK,EAACiB,IAAI,CAAC,WAAW,CAACC,OAAO,cAAEP,IAAA,CAACF,aAAa,GAAE,CAAE,CAAE,CAAC,EAChD,CAAC,CACH,CAAC,CACN,CAAC,CAEV,CAEA,cAAe,CAAAK,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}