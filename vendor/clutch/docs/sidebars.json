{"docs": [{"type": "category", "label": "About", "items": ["about/what-is-clutch", "about/roadmap", "about/architecture", "about/lyft-case-study", "about/comparison"]}, {"type": "category", "label": "Getting Started", "items": ["getting-started/build-guides", "getting-started/docker", "getting-started/local-build", "getting-started/mock-gateway", "getting-started/deployment"]}, {"type": "category", "label": "Development", "items": ["development/guide", "development/custom-gateway", "development/feature", "development/feature-flags", "development/api", {"type": "category", "label": "Frontend", "items": ["development/frontend/overview", "development/frontend/storybook", "development/frontend/bugsnag", {"type": "link", "label": "<PERSON><PERSON><PERSON>'s Storybook", "href": "https://storybook.clutch.sh"}]}, "development/backend"]}, {"type": "category", "label": "Advanced Features", "items": ["advanced/auth", "advanced/chaos-experimentation", "advanced/security-auditing", "advanced/topology", "advanced/workflow-feedback-framework"]}, "configuration", "components", "community"]}