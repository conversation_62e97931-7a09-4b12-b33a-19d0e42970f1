{"ast": null, "code": "import React from'react';import{Routes,Route}from'react-router-dom';import{AuthProvider,useAuth,ProtectedRoute}from'./auth/AuthContext';import{LoginPage}from'./auth/LoginPage';import{Layout}from'./components/Layout';import{Dashboard}from'./pages/Dashboard';import SearchDashboard from'./pages/SearchDashboard';import DiscoveryWizard from'./pages/DiscoveryWizard';import WorkflowExecutor from'./pages/WorkflowExecutor';import EnvoyConfigEditor from'./pages/EnvoyConfigEditor';import AutoscalerDashboard from'./pages/AutoscalerDashboard';import AuditViewer from'./pages/AuditViewer';import DatabaseAdmin from'./pages/DatabaseAdmin';import AdminDashboard from'./pages/AdminDashboard';import UserManagement from'./pages/UserManagement';import UserProfile from'./pages/UserProfile';import Settings from'./pages/Settings';import'./index.css';// App Router Component (inside AuthProvider)\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function AppRouter(){const{isAuthenticated,isLoading}=useAuth();if(isLoading){return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-16 w-16 border-b-2 border-white mx-auto mb-4\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-white text-lg\",children:\"Loading CAINuro Orchestrator...\"})]})});}if(!isAuthenticated){return/*#__PURE__*/_jsx(LoginPage,{});}return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-theme-bg-container\",children:/*#__PURE__*/_jsx(Layout,{children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(Dashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/dashboard\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(Dashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/search\",element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredPermission:{action:\"READ\",resource:\"discovery\"},children:/*#__PURE__*/_jsx(SearchDashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/discovery\",element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredPermission:{action:\"READ\",resource:\"discovery\"},children:/*#__PURE__*/_jsx(DiscoveryWizard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/workflows\",element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredPermission:{action:\"EXECUTE\",resource:\"workflows\"},children:/*#__PURE__*/_jsx(WorkflowExecutor,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/envoy\",element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredPermission:{action:\"READ\",resource:\"envoy\"},children:/*#__PURE__*/_jsx(EnvoyConfigEditor,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/autoscaler\",element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredPermission:{action:\"READ\",resource:\"autoscaler\"},children:/*#__PURE__*/_jsx(AutoscalerDashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/audit\",element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredPermission:{action:\"READ\",resource:\"audit\"},children:/*#__PURE__*/_jsx(AuditViewer,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/database\",element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredPermission:{action:\"READ\",resource:\"database\"},children:/*#__PURE__*/_jsx(DatabaseAdmin,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin\",element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredPermission:{action:\"READ\",resource:\"admin\"},children:/*#__PURE__*/_jsx(AdminDashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/users\",element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredPermission:{action:\"READ\",resource:\"admin\"},children:/*#__PURE__*/_jsx(UserManagement,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/settings\",element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredPermission:{action:\"READ\",resource:\"admin\"},children:/*#__PURE__*/_jsx(Settings,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/profile\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(UserProfile,{})})})]})})});}// Main App Component\nfunction App(){return/*#__PURE__*/_jsx(AuthProvider,{children:/*#__PURE__*/_jsx(AppRouter,{})});}export default App;", "map": {"version": 3, "names": ["React", "Routes", "Route", "<PERSON>th<PERSON><PERSON><PERSON>", "useAuth", "ProtectedRoute", "LoginPage", "Layout", "Dashboard", "SearchDashboard", "DiscoveryWizard", "WorkflowExecutor", "EnvoyConfigEditor", "AutoscalerDashboard", "AuditViewer", "DatabaseAdmin", "AdminDashboard", "UserManagement", "UserProfile", "Settings", "jsx", "_jsx", "jsxs", "_jsxs", "AppRouter", "isAuthenticated", "isLoading", "className", "children", "path", "element", "requiredPermission", "action", "resource", "App"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { Routes, Route } from 'react-router-dom';\nimport { AuthProvider, useAuth, ProtectedRoute } from './auth/AuthContext';\nimport { LoginPage } from './auth/LoginPage';\nimport { Layout } from './components/Layout';\nimport { Dashboard } from './pages/Dashboard';\nimport SearchDashboard from './pages/SearchDashboard';\nimport DiscoveryWizard from './pages/DiscoveryWizard';\nimport WorkflowExecutor from './pages/WorkflowExecutor';\nimport EnvoyConfigEditor from './pages/EnvoyConfigEditor';\nimport AutoscalerDashboard from './pages/AutoscalerDashboard';\nimport AuditViewer from './pages/AuditViewer';\nimport DatabaseAdmin from './pages/DatabaseAdmin';\nimport AdminDashboard from './pages/AdminDashboard';\nimport UserManagement from './pages/UserManagement';\nimport UserProfile from './pages/UserProfile';\nimport Settings from './pages/Settings';\nimport './index.css';\n\n// App Router Component (inside AuthProvider)\nfunction AppRouter() {\n  const { isAuthenticated, isLoading } = useAuth();\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-16 w-16 border-b-2 border-white mx-auto mb-4\"></div>\n          <div className=\"text-white text-lg\">Loading CAINuro Orchestrator...</div>\n        </div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return <LoginPage />;\n  }\n\n  return (\n    <div className=\"min-h-screen bg-theme-bg-container\">\n      <Layout>\n        <Routes>\n          <Route path=\"/\" element={\n            <ProtectedRoute>\n              <Dashboard />\n            </ProtectedRoute>\n          } />\n          <Route path=\"/dashboard\" element={\n            <ProtectedRoute>\n              <Dashboard />\n            </ProtectedRoute>\n          } />\n          <Route path=\"/search\" element={\n            <ProtectedRoute requiredPermission={{ action: \"READ\", resource: \"discovery\" }}>\n              <SearchDashboard />\n            </ProtectedRoute>\n          } />\n          <Route path=\"/discovery\" element={\n            <ProtectedRoute requiredPermission={{ action: \"READ\", resource: \"discovery\" }}>\n              <DiscoveryWizard />\n            </ProtectedRoute>\n          } />\n          <Route path=\"/workflows\" element={\n            <ProtectedRoute requiredPermission={{ action: \"EXECUTE\", resource: \"workflows\" }}>\n              <WorkflowExecutor />\n            </ProtectedRoute>\n          } />\n          <Route path=\"/envoy\" element={\n            <ProtectedRoute requiredPermission={{ action: \"READ\", resource: \"envoy\" }}>\n              <EnvoyConfigEditor />\n            </ProtectedRoute>\n          } />\n          <Route path=\"/autoscaler\" element={\n            <ProtectedRoute requiredPermission={{ action: \"READ\", resource: \"autoscaler\" }}>\n              <AutoscalerDashboard />\n            </ProtectedRoute>\n          } />\n          <Route path=\"/audit\" element={\n            <ProtectedRoute requiredPermission={{ action: \"READ\", resource: \"audit\" }}>\n              <AuditViewer />\n            </ProtectedRoute>\n          } />\n          <Route path=\"/database\" element={\n            <ProtectedRoute requiredPermission={{ action: \"READ\", resource: \"database\" }}>\n              <DatabaseAdmin />\n            </ProtectedRoute>\n          } />\n\n          {/* Admin Routes */}\n          <Route path=\"/admin\" element={\n            <ProtectedRoute requiredPermission={{ action: \"READ\", resource: \"admin\" }}>\n              <AdminDashboard />\n            </ProtectedRoute>\n          } />\n          <Route path=\"/admin/users\" element={\n            <ProtectedRoute requiredPermission={{ action: \"READ\", resource: \"admin\" }}>\n              <UserManagement />\n            </ProtectedRoute>\n          } />\n          <Route path=\"/admin/settings\" element={\n            <ProtectedRoute requiredPermission={{ action: \"READ\", resource: \"admin\" }}>\n              <Settings />\n            </ProtectedRoute>\n          } />\n\n          {/* User Routes */}\n          <Route path=\"/profile\" element={\n            <ProtectedRoute>\n              <UserProfile />\n            </ProtectedRoute>\n          } />\n        </Routes>\n      </Layout>\n    </div>\n  );\n}\n\n// Main App Component\nfunction App() {\n  return (\n    <AuthProvider>\n      <AppRouter />\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,MAAM,CAAEC,KAAK,KAAQ,kBAAkB,CAChD,OAASC,YAAY,CAAEC,OAAO,CAAEC,cAAc,KAAQ,oBAAoB,CAC1E,OAASC,SAAS,KAAQ,kBAAkB,CAC5C,OAASC,MAAM,KAAQ,qBAAqB,CAC5C,OAASC,SAAS,KAAQ,mBAAmB,CAC7C,MAAO,CAAAC,eAAe,KAAM,yBAAyB,CACrD,MAAO,CAAAC,eAAe,KAAM,yBAAyB,CACrD,MAAO,CAAAC,gBAAgB,KAAM,0BAA0B,CACvD,MAAO,CAAAC,iBAAiB,KAAM,2BAA2B,CACzD,MAAO,CAAAC,mBAAmB,KAAM,6BAA6B,CAC7D,MAAO,CAAAC,WAAW,KAAM,qBAAqB,CAC7C,MAAO,CAAAC,aAAa,KAAM,uBAAuB,CACjD,MAAO,CAAAC,cAAc,KAAM,wBAAwB,CACnD,MAAO,CAAAC,cAAc,KAAM,wBAAwB,CACnD,MAAO,CAAAC,WAAW,KAAM,qBAAqB,CAC7C,MAAO,CAAAC,QAAQ,KAAM,kBAAkB,CACvC,MAAO,aAAa,CAEpB;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,QAAS,CAAAC,SAASA,CAAA,CAAG,CACnB,KAAM,CAAEC,eAAe,CAAEC,SAAU,CAAC,CAAGtB,OAAO,CAAC,CAAC,CAEhD,GAAIsB,SAAS,CAAE,CACb,mBACEL,IAAA,QAAKM,SAAS,CAAC,4GAA4G,CAAAC,QAAA,cACzHL,KAAA,QAAKI,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BP,IAAA,QAAKM,SAAS,CAAC,0EAA0E,CAAM,CAAC,cAChGN,IAAA,QAAKM,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,iCAA+B,CAAK,CAAC,EACtE,CAAC,CACH,CAAC,CAEV,CAEA,GAAI,CAACH,eAAe,CAAE,CACpB,mBAAOJ,IAAA,CAACf,SAAS,GAAE,CAAC,CACtB,CAEA,mBACEe,IAAA,QAAKM,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjDP,IAAA,CAACd,MAAM,EAAAqB,QAAA,cACLL,KAAA,CAACtB,MAAM,EAAA2B,QAAA,eACLP,IAAA,CAACnB,KAAK,EAAC2B,IAAI,CAAC,GAAG,CAACC,OAAO,cACrBT,IAAA,CAAChB,cAAc,EAAAuB,QAAA,cACbP,IAAA,CAACb,SAAS,GAAE,CAAC,CACC,CACjB,CAAE,CAAC,cACJa,IAAA,CAACnB,KAAK,EAAC2B,IAAI,CAAC,YAAY,CAACC,OAAO,cAC9BT,IAAA,CAAChB,cAAc,EAAAuB,QAAA,cACbP,IAAA,CAACb,SAAS,GAAE,CAAC,CACC,CACjB,CAAE,CAAC,cACJa,IAAA,CAACnB,KAAK,EAAC2B,IAAI,CAAC,SAAS,CAACC,OAAO,cAC3BT,IAAA,CAAChB,cAAc,EAAC0B,kBAAkB,CAAE,CAAEC,MAAM,CAAE,MAAM,CAAEC,QAAQ,CAAE,WAAY,CAAE,CAAAL,QAAA,cAC5EP,IAAA,CAACZ,eAAe,GAAE,CAAC,CACL,CACjB,CAAE,CAAC,cACJY,IAAA,CAACnB,KAAK,EAAC2B,IAAI,CAAC,YAAY,CAACC,OAAO,cAC9BT,IAAA,CAAChB,cAAc,EAAC0B,kBAAkB,CAAE,CAAEC,MAAM,CAAE,MAAM,CAAEC,QAAQ,CAAE,WAAY,CAAE,CAAAL,QAAA,cAC5EP,IAAA,CAACX,eAAe,GAAE,CAAC,CACL,CACjB,CAAE,CAAC,cACJW,IAAA,CAACnB,KAAK,EAAC2B,IAAI,CAAC,YAAY,CAACC,OAAO,cAC9BT,IAAA,CAAChB,cAAc,EAAC0B,kBAAkB,CAAE,CAAEC,MAAM,CAAE,SAAS,CAAEC,QAAQ,CAAE,WAAY,CAAE,CAAAL,QAAA,cAC/EP,IAAA,CAACV,gBAAgB,GAAE,CAAC,CACN,CACjB,CAAE,CAAC,cACJU,IAAA,CAACnB,KAAK,EAAC2B,IAAI,CAAC,QAAQ,CAACC,OAAO,cAC1BT,IAAA,CAAChB,cAAc,EAAC0B,kBAAkB,CAAE,CAAEC,MAAM,CAAE,MAAM,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAAAL,QAAA,cACxEP,IAAA,CAACT,iBAAiB,GAAE,CAAC,CACP,CACjB,CAAE,CAAC,cACJS,IAAA,CAACnB,KAAK,EAAC2B,IAAI,CAAC,aAAa,CAACC,OAAO,cAC/BT,IAAA,CAAChB,cAAc,EAAC0B,kBAAkB,CAAE,CAAEC,MAAM,CAAE,MAAM,CAAEC,QAAQ,CAAE,YAAa,CAAE,CAAAL,QAAA,cAC7EP,IAAA,CAACR,mBAAmB,GAAE,CAAC,CACT,CACjB,CAAE,CAAC,cACJQ,IAAA,CAACnB,KAAK,EAAC2B,IAAI,CAAC,QAAQ,CAACC,OAAO,cAC1BT,IAAA,CAAChB,cAAc,EAAC0B,kBAAkB,CAAE,CAAEC,MAAM,CAAE,MAAM,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAAAL,QAAA,cACxEP,IAAA,CAACP,WAAW,GAAE,CAAC,CACD,CACjB,CAAE,CAAC,cACJO,IAAA,CAACnB,KAAK,EAAC2B,IAAI,CAAC,WAAW,CAACC,OAAO,cAC7BT,IAAA,CAAChB,cAAc,EAAC0B,kBAAkB,CAAE,CAAEC,MAAM,CAAE,MAAM,CAAEC,QAAQ,CAAE,UAAW,CAAE,CAAAL,QAAA,cAC3EP,IAAA,CAACN,aAAa,GAAE,CAAC,CACH,CACjB,CAAE,CAAC,cAGJM,IAAA,CAACnB,KAAK,EAAC2B,IAAI,CAAC,QAAQ,CAACC,OAAO,cAC1BT,IAAA,CAAChB,cAAc,EAAC0B,kBAAkB,CAAE,CAAEC,MAAM,CAAE,MAAM,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAAAL,QAAA,cACxEP,IAAA,CAACL,cAAc,GAAE,CAAC,CACJ,CACjB,CAAE,CAAC,cACJK,IAAA,CAACnB,KAAK,EAAC2B,IAAI,CAAC,cAAc,CAACC,OAAO,cAChCT,IAAA,CAAChB,cAAc,EAAC0B,kBAAkB,CAAE,CAAEC,MAAM,CAAE,MAAM,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAAAL,QAAA,cACxEP,IAAA,CAACJ,cAAc,GAAE,CAAC,CACJ,CACjB,CAAE,CAAC,cACJI,IAAA,CAACnB,KAAK,EAAC2B,IAAI,CAAC,iBAAiB,CAACC,OAAO,cACnCT,IAAA,CAAChB,cAAc,EAAC0B,kBAAkB,CAAE,CAAEC,MAAM,CAAE,MAAM,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAAAL,QAAA,cACxEP,IAAA,CAACF,QAAQ,GAAE,CAAC,CACE,CACjB,CAAE,CAAC,cAGJE,IAAA,CAACnB,KAAK,EAAC2B,IAAI,CAAC,UAAU,CAACC,OAAO,cAC5BT,IAAA,CAAChB,cAAc,EAAAuB,QAAA,cACbP,IAAA,CAACH,WAAW,GAAE,CAAC,CACD,CACjB,CAAE,CAAC,EACE,CAAC,CACH,CAAC,CACN,CAAC,CAEV,CAEA;AACA,QAAS,CAAAgB,GAAGA,CAAA,CAAG,CACb,mBACEb,IAAA,CAAClB,YAAY,EAAAyB,QAAA,cACXP,IAAA,CAACG,SAAS,GAAE,CAAC,CACD,CAAC,CAEnB,CAEA,cAAe,CAAAU,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}