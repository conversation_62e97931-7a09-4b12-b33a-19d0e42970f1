import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../store/store';
import { searchResources, setQuery, setProvider, clearResults } from '../store/slices/searchSlice';
import {
  MagnifyingGlassIcon,
  CloudIcon,
  TagIcon,
  ServerIcon,
} from '@heroicons/react/24/outline';

const SearchDashboard: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { resources, loading, error, totalCount, query, provider } = useSelector(
    (state: RootState) => state.search
  );

  const [localQuery, setLocalQuery] = useState(query);
  const [localProvider, setLocalProvider] = useState(provider);

  const providers = [
    { value: '', label: 'All Providers' },
    { value: 'aws', label: 'AWS' },
    { value: 'gcp', label: 'GCP' },
    { value: 'azure', label: 'Azure' },
  ];

  const handleSearch = () => {
    dispatch(setQuery(localQuery));
    dispatch(setProvider(localProvider));
    dispatch(searchResources({
      query: localQuery,
      provider: localProvider,
      limit: 50,
    }));
  };

  const handleClear = () => {
    setLocalQuery('');
    setLocalProvider('');
    dispatch(clearResults());
  };

  useEffect(() => {
    // Load initial data
    dispatch(searchResources({ limit: 20 }));
  }, [dispatch]);

  const getProviderColor = (provider: string) => {
    switch (provider.toLowerCase()) {
      case 'aws':
        return 'bg-orange-100 text-orange-800';
      case 'gcp':
        return 'bg-blue-100 text-blue-800';
      case 'azure':
        return 'bg-cyan-100 text-cyan-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-white">Resource Discovery</h1>
        <p className="mt-1 text-sm text-gray-400">
          Search and discover resources across all your cloud providers
        </p>
      </div>

      {/* Search Form */}
      <div className="bg-gray-800 shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-4">
            <div className="sm:col-span-2">
              <label htmlFor="search" className="block text-sm font-medium text-gray-300">
                Search Query
              </label>
              <div className="mt-1 relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  id="search"
                  className="block w-full pl-10 pr-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                  placeholder="Search by name, type, or tags..."
                  value={localQuery}
                  onChange={(e) => setLocalQuery(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
            </div>

            <div>
              <label htmlFor="provider" className="block text-sm font-medium text-gray-300">
                Provider
              </label>
              <select
                id="provider"
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-600 bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent rounded-md"
                value={localProvider}
                onChange={(e) => setLocalProvider(e.target.value)}
              >
                {providers.map((p) => (
                  <option key={p.value} value={p.value}>
                    {p.label}
                  </option>
                ))}
              </select>
            </div>

            <div className="flex items-end space-x-2">
              <button
                type="button"
                onClick={handleSearch}
                disabled={loading}
                className="flex-1 bg-cyan-600 border border-transparent rounded-md py-2 px-4 inline-flex justify-center text-sm font-medium text-white hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 disabled:opacity-50"
              >
                {loading ? 'Searching...' : 'Search'}
              </button>
              <button
                type="button"
                onClick={handleClear}
                className="bg-gray-600 border border-transparent rounded-md py-2 px-4 inline-flex justify-center text-sm font-medium text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                Clear
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Results */}
      <div className="bg-gray-800 shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg leading-6 font-medium text-white">
              Search Results
            </h3>
            <span className="text-sm text-gray-400">
              {totalCount} resources found
            </span>
          </div>

          {error && (
            <div className="mb-4 bg-red-50 border border-red-200 rounded-md p-4">
              <div className="text-sm text-red-700">{error}</div>
            </div>
          )}

          {loading ? (
            <div className="flex items-center justify-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500"></div>
            </div>
          ) : (
            <div className="space-y-4">
              {resources.length === 0 ? (
                <div className="text-center py-12">
                  <CloudIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-300">No resources found</h3>
                  <p className="mt-1 text-sm text-gray-400">
                    Try adjusting your search criteria or run a discovery scan.
                  </p>
                </div>
              ) : (
                resources.map((resource) => (
                  <div
                    key={resource.id}
                    className="border border-gray-600 rounded-lg p-4 hover:bg-gray-700 transition-colors"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <ServerIcon className="h-5 w-5 text-gray-400" />
                          <h4 className="text-lg font-medium text-white">{resource.name}</h4>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getProviderColor(resource.provider)}`}>
                            {resource.provider.toUpperCase()}
                          </span>
                        </div>
                        <p className="mt-1 text-sm text-gray-400">{resource.type}</p>
                        <p className="mt-1 text-xs text-gray-500 font-mono">{resource.id}</p>
                      </div>
                    </div>

                    {Object.keys(resource.tags).length > 0 && (
                      <div className="mt-3">
                        <div className="flex items-center space-x-1 mb-2">
                          <TagIcon className="h-4 w-4 text-gray-400" />
                          <span className="text-sm text-gray-400">Tags</span>
                        </div>
                        <div className="flex flex-wrap gap-1">
                          {Object.entries(resource.tags).map(([key, value]) => (
                            <span
                              key={key}
                              className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-600 text-gray-200"
                            >
                              {key}: {value}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SearchDashboard;
