[{"/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/index.tsx": "1", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/reportWebVitals.ts": "2", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/store.ts": "3", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/App.tsx": "4", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/searchSlice.ts": "5", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/workflowSlice.ts": "6", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/envoySlice.ts": "7", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/auditSlice.ts": "8", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/autoscalerSlice.ts": "9", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/databaseSlice.ts": "10", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/components/Layout.tsx": "11", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/SearchDashboard.tsx": "12", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/Dashboard.tsx": "13", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/WorkflowExecutor.tsx": "14", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/AutoscalerDashboard.tsx": "15", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/DiscoveryWizard.tsx": "16", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/EnvoyConfigEditor.tsx": "17", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/AuditViewer.tsx": "18", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/DatabaseAdmin.tsx": "19"}, {"size": 778, "mtime": 1749058599021, "results": "20", "hashOfConfig": "21"}, {"size": 370, "mtime": 1749058755887, "results": "22", "hashOfConfig": "21"}, {"size": 863, "mtime": 1749063617349, "results": "23", "hashOfConfig": "21"}, {"size": 1591, "mtime": 1749064381709, "results": "24", "hashOfConfig": "21"}, {"size": 2725, "mtime": 1749063531345, "results": "25", "hashOfConfig": "21"}, {"size": 3481, "mtime": 1749063548264, "results": "26", "hashOfConfig": "21"}, {"size": 2849, "mtime": 1749063582338, "results": "27", "hashOfConfig": "21"}, {"size": 1957, "mtime": 1749063595368, "results": "28", "hashOfConfig": "21"}, {"size": 3681, "mtime": 1749063567934, "results": "29", "hashOfConfig": "21"}, {"size": 2204, "mtime": 1749063606689, "results": "30", "hashOfConfig": "21"}, {"size": 6466, "mtime": 1749064454108, "results": "31", "hashOfConfig": "21"}, {"size": 8594, "mtime": 1749063749914, "results": "32", "hashOfConfig": "21"}, {"size": 8985, "mtime": 1749058679704, "results": "33", "hashOfConfig": "21"}, {"size": 10204, "mtime": 1749063787790, "results": "34", "hashOfConfig": "21"}, {"size": 9190, "mtime": 1749063823453, "results": "35", "hashOfConfig": "21"}, {"size": 17002, "mtime": 1749064442452, "results": "36", "hashOfConfig": "21"}, {"size": 11841, "mtime": 1749064586885, "results": "37", "hashOfConfig": "21"}, {"size": 9026, "mtime": 1749063897136, "results": "38", "hashOfConfig": "21"}, {"size": 8828, "mtime": 1749063931077, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ojadns", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/index.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/reportWebVitals.ts", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/store.ts", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/App.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/searchSlice.ts", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/workflowSlice.ts", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/envoySlice.ts", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/auditSlice.ts", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/autoscalerSlice.ts", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/databaseSlice.ts", ["97"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/components/Layout.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/SearchDashboard.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/Dashboard.tsx", ["98"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/WorkflowExecutor.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/AutoscalerDashboard.tsx", ["99"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/DiscoveryWizard.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/EnvoyConfigEditor.tsx", ["100"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/AuditViewer.tsx", ["101"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/DatabaseAdmin.tsx", [], [], {"ruleId": "102", "severity": 1, "message": "103", "line": 1, "column": 41, "nodeType": "104", "messageId": "105", "endLine": 1, "endColumn": 54}, {"ruleId": "102", "severity": 1, "message": "106", "line": 3, "column": 3, "nodeType": "104", "messageId": "105", "endLine": 3, "endColumn": 15}, {"ruleId": "102", "severity": 1, "message": "107", "line": 18, "column": 43, "nodeType": "104", "messageId": "105", "endLine": 18, "endColumn": 50}, {"ruleId": "102", "severity": 1, "message": "108", "line": 19, "column": 52, "nodeType": "104", "messageId": "105", "endLine": 19, "endColumn": 57}, {"ruleId": "102", "severity": 1, "message": "109", "line": 14, "column": 47, "nodeType": "104", "messageId": "105", "endLine": 14, "endColumn": 52}, "@typescript-eslint/no-unused-vars", "'PayloadAction' is defined but never used.", "Identifier", "unusedVar", "'ChartBarIcon' is defined but never used.", "'metrics' is assigned a value but never used.", "'error' is assigned a value but never used.", "'query' is assigned a value but never used."]