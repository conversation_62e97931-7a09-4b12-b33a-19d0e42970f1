{"ast": null, "code": "import { defaultMemoize, defaultEqualityCheck } from './defaultMemoize';\nexport { defaultMemoize, defaultEqualityCheck };\nfunction getDependencies(funcs) {\n  var dependencies = Array.isArray(funcs[0]) ? funcs[0] : funcs;\n  if (!dependencies.every(function (dep) {\n    return typeof dep === 'function';\n  })) {\n    var dependencyTypes = dependencies.map(function (dep) {\n      return typeof dep === 'function' ? \"function \" + (dep.name || 'unnamed') + \"()\" : typeof dep;\n    }).join(', ');\n    throw new Error(\"createSelector expects all input-selectors to be functions, but received the following types: [\" + dependencyTypes + \"]\");\n  }\n  return dependencies;\n}\nexport function createSelectorCreator(memoize) {\n  for (var _len = arguments.length, memoizeOptionsFromArgs = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    memoizeOptionsFromArgs[_key - 1] = arguments[_key];\n  }\n  var createSelector = function createSelector() {\n    for (var _len2 = arguments.length, funcs = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      funcs[_key2] = arguments[_key2];\n    }\n    var _recomputations = 0;\n    var _lastResult; // Due to the intricacies of rest params, we can't do an optional arg after `...funcs`.\n    // So, start by declaring the default value here.\n    // (And yes, the words 'memoize' and 'options' appear too many times in this next sequence.)\n\n    var directlyPassedOptions = {\n      memoizeOptions: undefined\n    }; // Normally, the result func or \"output selector\" is the last arg\n\n    var resultFunc = funcs.pop(); // If the result func is actually an _object_, assume it's our options object\n\n    if (typeof resultFunc === 'object') {\n      directlyPassedOptions = resultFunc; // and pop the real result func off\n\n      resultFunc = funcs.pop();\n    }\n    if (typeof resultFunc !== 'function') {\n      throw new Error(\"createSelector expects an output function after the inputs, but received: [\" + typeof resultFunc + \"]\");\n    } // Determine which set of options we're using. Prefer options passed directly,\n    // but fall back to options given to createSelectorCreator.\n\n    var _directlyPassedOption = directlyPassedOptions,\n      _directlyPassedOption2 = _directlyPassedOption.memoizeOptions,\n      memoizeOptions = _directlyPassedOption2 === void 0 ? memoizeOptionsFromArgs : _directlyPassedOption2; // Simplifying assumption: it's unlikely that the first options arg of the provided memoizer\n    // is an array. In most libs I've looked at, it's an equality function or options object.\n    // Based on that, if `memoizeOptions` _is_ an array, we assume it's a full\n    // user-provided array of options. Otherwise, it must be just the _first_ arg, and so\n    // we wrap it in an array so we can apply it.\n\n    var finalMemoizeOptions = Array.isArray(memoizeOptions) ? memoizeOptions : [memoizeOptions];\n    var dependencies = getDependencies(funcs);\n    var memoizedResultFunc = memoize.apply(void 0, [function recomputationWrapper() {\n      _recomputations++; // apply arguments instead of spreading for performance.\n\n      return resultFunc.apply(null, arguments);\n    }].concat(finalMemoizeOptions)); // If a selector is called with the exact same arguments we don't need to traverse our dependencies again.\n\n    var selector = memoize(function dependenciesChecker() {\n      var params = [];\n      var length = dependencies.length;\n      for (var i = 0; i < length; i++) {\n        // apply arguments instead of spreading and mutate a local list of params for performance.\n        // @ts-ignore\n        params.push(dependencies[i].apply(null, arguments));\n      } // apply arguments instead of spreading for performance.\n\n      _lastResult = memoizedResultFunc.apply(null, params);\n      return _lastResult;\n    });\n    Object.assign(selector, {\n      resultFunc: resultFunc,\n      memoizedResultFunc: memoizedResultFunc,\n      dependencies: dependencies,\n      lastResult: function lastResult() {\n        return _lastResult;\n      },\n      recomputations: function recomputations() {\n        return _recomputations;\n      },\n      resetRecomputations: function resetRecomputations() {\n        return _recomputations = 0;\n      }\n    });\n    return selector;\n  }; // @ts-ignore\n\n  return createSelector;\n}\nexport var createSelector = /* #__PURE__ */createSelectorCreator(defaultMemoize);\n// Manual definition of state and output arguments\nexport var createStructuredSelector = function createStructuredSelector(selectors, selectorCreator) {\n  if (selectorCreator === void 0) {\n    selectorCreator = createSelector;\n  }\n  if (typeof selectors !== 'object') {\n    throw new Error('createStructuredSelector expects first argument to be an object ' + (\"where each property is a selector, instead received a \" + typeof selectors));\n  }\n  var objectKeys = Object.keys(selectors);\n  var resultSelector = selectorCreator(\n  // @ts-ignore\n  objectKeys.map(function (key) {\n    return selectors[key];\n  }), function () {\n    for (var _len3 = arguments.length, values = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n      values[_key3] = arguments[_key3];\n    }\n    return values.reduce(function (composition, value, index) {\n      composition[objectKeys[index]] = value;\n      return composition;\n    }, {});\n  });\n  return resultSelector;\n};", "map": {"version": 3, "names": ["defaultMemoize", "defaultEqualityCheck", "getDependencies", "funcs", "dependencies", "Array", "isArray", "every", "dep", "dependencyTypes", "map", "name", "join", "Error", "createSelectorCreator", "memoize", "_len", "arguments", "length", "memoizeOptionsFromArgs", "_key", "createSelector", "_len2", "_key2", "_recomputations", "_lastResult", "directlyPassedOptions", "memoizeOptions", "undefined", "resultFunc", "pop", "_directlyPassedOption", "_directlyPassedOption2", "finalMemoizeOptions", "memoizedResultFunc", "apply", "recomputationWrapper", "concat", "selector", "dependenciesChecker", "params", "i", "push", "Object", "assign", "lastResult", "recomputations", "resetRecomputations", "createStructuredSelector", "selectors", "selectorCreator", "objectKeys", "keys", "resultSelector", "key", "_len3", "values", "_key3", "reduce", "composition", "value", "index"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/node_modules/reselect/es/index.js"], "sourcesContent": ["import { defaultMemoize, defaultEqualityCheck } from './defaultMemoize';\nexport { defaultMemoize, defaultEqualityCheck };\n\nfunction getDependencies(funcs) {\n  var dependencies = Array.isArray(funcs[0]) ? funcs[0] : funcs;\n\n  if (!dependencies.every(function (dep) {\n    return typeof dep === 'function';\n  })) {\n    var dependencyTypes = dependencies.map(function (dep) {\n      return typeof dep === 'function' ? \"function \" + (dep.name || 'unnamed') + \"()\" : typeof dep;\n    }).join(', ');\n    throw new Error(\"createSelector expects all input-selectors to be functions, but received the following types: [\" + dependencyTypes + \"]\");\n  }\n\n  return dependencies;\n}\n\nexport function createSelectorCreator(memoize) {\n  for (var _len = arguments.length, memoizeOptionsFromArgs = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    memoizeOptionsFromArgs[_key - 1] = arguments[_key];\n  }\n\n  var createSelector = function createSelector() {\n    for (var _len2 = arguments.length, funcs = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      funcs[_key2] = arguments[_key2];\n    }\n\n    var _recomputations = 0;\n\n    var _lastResult; // Due to the intricacies of rest params, we can't do an optional arg after `...funcs`.\n    // So, start by declaring the default value here.\n    // (And yes, the words 'memoize' and 'options' appear too many times in this next sequence.)\n\n\n    var directlyPassedOptions = {\n      memoizeOptions: undefined\n    }; // Normally, the result func or \"output selector\" is the last arg\n\n    var resultFunc = funcs.pop(); // If the result func is actually an _object_, assume it's our options object\n\n    if (typeof resultFunc === 'object') {\n      directlyPassedOptions = resultFunc; // and pop the real result func off\n\n      resultFunc = funcs.pop();\n    }\n\n    if (typeof resultFunc !== 'function') {\n      throw new Error(\"createSelector expects an output function after the inputs, but received: [\" + typeof resultFunc + \"]\");\n    } // Determine which set of options we're using. Prefer options passed directly,\n    // but fall back to options given to createSelectorCreator.\n\n\n    var _directlyPassedOption = directlyPassedOptions,\n        _directlyPassedOption2 = _directlyPassedOption.memoizeOptions,\n        memoizeOptions = _directlyPassedOption2 === void 0 ? memoizeOptionsFromArgs : _directlyPassedOption2; // Simplifying assumption: it's unlikely that the first options arg of the provided memoizer\n    // is an array. In most libs I've looked at, it's an equality function or options object.\n    // Based on that, if `memoizeOptions` _is_ an array, we assume it's a full\n    // user-provided array of options. Otherwise, it must be just the _first_ arg, and so\n    // we wrap it in an array so we can apply it.\n\n    var finalMemoizeOptions = Array.isArray(memoizeOptions) ? memoizeOptions : [memoizeOptions];\n    var dependencies = getDependencies(funcs);\n    var memoizedResultFunc = memoize.apply(void 0, [function recomputationWrapper() {\n      _recomputations++; // apply arguments instead of spreading for performance.\n\n      return resultFunc.apply(null, arguments);\n    }].concat(finalMemoizeOptions)); // If a selector is called with the exact same arguments we don't need to traverse our dependencies again.\n\n    var selector = memoize(function dependenciesChecker() {\n      var params = [];\n      var length = dependencies.length;\n\n      for (var i = 0; i < length; i++) {\n        // apply arguments instead of spreading and mutate a local list of params for performance.\n        // @ts-ignore\n        params.push(dependencies[i].apply(null, arguments));\n      } // apply arguments instead of spreading for performance.\n\n\n      _lastResult = memoizedResultFunc.apply(null, params);\n      return _lastResult;\n    });\n    Object.assign(selector, {\n      resultFunc: resultFunc,\n      memoizedResultFunc: memoizedResultFunc,\n      dependencies: dependencies,\n      lastResult: function lastResult() {\n        return _lastResult;\n      },\n      recomputations: function recomputations() {\n        return _recomputations;\n      },\n      resetRecomputations: function resetRecomputations() {\n        return _recomputations = 0;\n      }\n    });\n    return selector;\n  }; // @ts-ignore\n\n\n  return createSelector;\n}\nexport var createSelector = /* #__PURE__ */createSelectorCreator(defaultMemoize);\n// Manual definition of state and output arguments\nexport var createStructuredSelector = function createStructuredSelector(selectors, selectorCreator) {\n  if (selectorCreator === void 0) {\n    selectorCreator = createSelector;\n  }\n\n  if (typeof selectors !== 'object') {\n    throw new Error('createStructuredSelector expects first argument to be an object ' + (\"where each property is a selector, instead received a \" + typeof selectors));\n  }\n\n  var objectKeys = Object.keys(selectors);\n  var resultSelector = selectorCreator( // @ts-ignore\n  objectKeys.map(function (key) {\n    return selectors[key];\n  }), function () {\n    for (var _len3 = arguments.length, values = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n      values[_key3] = arguments[_key3];\n    }\n\n    return values.reduce(function (composition, value, index) {\n      composition[objectKeys[index]] = value;\n      return composition;\n    }, {});\n  });\n  return resultSelector;\n};"], "mappings": "AAAA,SAASA,cAAc,EAAEC,oBAAoB,QAAQ,kBAAkB;AACvE,SAASD,cAAc,EAAEC,oBAAoB;AAE7C,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC9B,IAAIC,YAAY,GAAGC,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK;EAE7D,IAAI,CAACC,YAAY,CAACG,KAAK,CAAC,UAAUC,GAAG,EAAE;IACrC,OAAO,OAAOA,GAAG,KAAK,UAAU;EAClC,CAAC,CAAC,EAAE;IACF,IAAIC,eAAe,GAAGL,YAAY,CAACM,GAAG,CAAC,UAAUF,GAAG,EAAE;MACpD,OAAO,OAAOA,GAAG,KAAK,UAAU,GAAG,WAAW,IAAIA,GAAG,CAACG,IAAI,IAAI,SAAS,CAAC,GAAG,IAAI,GAAG,OAAOH,GAAG;IAC9F,CAAC,CAAC,CAACI,IAAI,CAAC,IAAI,CAAC;IACb,MAAM,IAAIC,KAAK,CAAC,iGAAiG,GAAGJ,eAAe,GAAG,GAAG,CAAC;EAC5I;EAEA,OAAOL,YAAY;AACrB;AAEA,OAAO,SAASU,qBAAqBA,CAACC,OAAO,EAAE;EAC7C,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,sBAAsB,GAAG,IAAId,KAAK,CAACW,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEI,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGJ,IAAI,EAAEI,IAAI,EAAE,EAAE;IAC5HD,sBAAsB,CAACC,IAAI,GAAG,CAAC,CAAC,GAAGH,SAAS,CAACG,IAAI,CAAC;EACpD;EAEA,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAG;IAC7C,KAAK,IAAIC,KAAK,GAAGL,SAAS,CAACC,MAAM,EAAEf,KAAK,GAAG,IAAIE,KAAK,CAACiB,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;MAC9FpB,KAAK,CAACoB,KAAK,CAAC,GAAGN,SAAS,CAACM,KAAK,CAAC;IACjC;IAEA,IAAIC,eAAe,GAAG,CAAC;IAEvB,IAAIC,WAAW,CAAC,CAAC;IACjB;IACA;;IAGA,IAAIC,qBAAqB,GAAG;MAC1BC,cAAc,EAAEC;IAClB,CAAC,CAAC,CAAC;;IAEH,IAAIC,UAAU,GAAG1B,KAAK,CAAC2B,GAAG,CAAC,CAAC,CAAC,CAAC;;IAE9B,IAAI,OAAOD,UAAU,KAAK,QAAQ,EAAE;MAClCH,qBAAqB,GAAGG,UAAU,CAAC,CAAC;;MAEpCA,UAAU,GAAG1B,KAAK,CAAC2B,GAAG,CAAC,CAAC;IAC1B;IAEA,IAAI,OAAOD,UAAU,KAAK,UAAU,EAAE;MACpC,MAAM,IAAIhB,KAAK,CAAC,6EAA6E,GAAG,OAAOgB,UAAU,GAAG,GAAG,CAAC;IAC1H,CAAC,CAAC;IACF;;IAGA,IAAIE,qBAAqB,GAAGL,qBAAqB;MAC7CM,sBAAsB,GAAGD,qBAAqB,CAACJ,cAAc;MAC7DA,cAAc,GAAGK,sBAAsB,KAAK,KAAK,CAAC,GAAGb,sBAAsB,GAAGa,sBAAsB,CAAC,CAAC;IAC1G;IACA;IACA;IACA;;IAEA,IAAIC,mBAAmB,GAAG5B,KAAK,CAACC,OAAO,CAACqB,cAAc,CAAC,GAAGA,cAAc,GAAG,CAACA,cAAc,CAAC;IAC3F,IAAIvB,YAAY,GAAGF,eAAe,CAACC,KAAK,CAAC;IACzC,IAAI+B,kBAAkB,GAAGnB,OAAO,CAACoB,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,SAASC,oBAAoBA,CAAA,EAAG;MAC9EZ,eAAe,EAAE,CAAC,CAAC;;MAEnB,OAAOK,UAAU,CAACM,KAAK,CAAC,IAAI,EAAElB,SAAS,CAAC;IAC1C,CAAC,CAAC,CAACoB,MAAM,CAACJ,mBAAmB,CAAC,CAAC,CAAC,CAAC;;IAEjC,IAAIK,QAAQ,GAAGvB,OAAO,CAAC,SAASwB,mBAAmBA,CAAA,EAAG;MACpD,IAAIC,MAAM,GAAG,EAAE;MACf,IAAItB,MAAM,GAAGd,YAAY,CAACc,MAAM;MAEhC,KAAK,IAAIuB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,MAAM,EAAEuB,CAAC,EAAE,EAAE;QAC/B;QACA;QACAD,MAAM,CAACE,IAAI,CAACtC,YAAY,CAACqC,CAAC,CAAC,CAACN,KAAK,CAAC,IAAI,EAAElB,SAAS,CAAC,CAAC;MACrD,CAAC,CAAC;;MAGFQ,WAAW,GAAGS,kBAAkB,CAACC,KAAK,CAAC,IAAI,EAAEK,MAAM,CAAC;MACpD,OAAOf,WAAW;IACpB,CAAC,CAAC;IACFkB,MAAM,CAACC,MAAM,CAACN,QAAQ,EAAE;MACtBT,UAAU,EAAEA,UAAU;MACtBK,kBAAkB,EAAEA,kBAAkB;MACtC9B,YAAY,EAAEA,YAAY;MAC1ByC,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;QAChC,OAAOpB,WAAW;MACpB,CAAC;MACDqB,cAAc,EAAE,SAASA,cAAcA,CAAA,EAAG;QACxC,OAAOtB,eAAe;MACxB,CAAC;MACDuB,mBAAmB,EAAE,SAASA,mBAAmBA,CAAA,EAAG;QAClD,OAAOvB,eAAe,GAAG,CAAC;MAC5B;IACF,CAAC,CAAC;IACF,OAAOc,QAAQ;EACjB,CAAC,CAAC,CAAC;;EAGH,OAAOjB,cAAc;AACvB;AACA,OAAO,IAAIA,cAAc,GAAG,eAAeP,qBAAqB,CAACd,cAAc,CAAC;AAChF;AACA,OAAO,IAAIgD,wBAAwB,GAAG,SAASA,wBAAwBA,CAACC,SAAS,EAAEC,eAAe,EAAE;EAClG,IAAIA,eAAe,KAAK,KAAK,CAAC,EAAE;IAC9BA,eAAe,GAAG7B,cAAc;EAClC;EAEA,IAAI,OAAO4B,SAAS,KAAK,QAAQ,EAAE;IACjC,MAAM,IAAIpC,KAAK,CAAC,kEAAkE,IAAI,wDAAwD,GAAG,OAAOoC,SAAS,CAAC,CAAC;EACrK;EAEA,IAAIE,UAAU,GAAGR,MAAM,CAACS,IAAI,CAACH,SAAS,CAAC;EACvC,IAAII,cAAc,GAAGH,eAAe;EAAE;EACtCC,UAAU,CAACzC,GAAG,CAAC,UAAU4C,GAAG,EAAE;IAC5B,OAAOL,SAAS,CAACK,GAAG,CAAC;EACvB,CAAC,CAAC,EAAE,YAAY;IACd,KAAK,IAAIC,KAAK,GAAGtC,SAAS,CAACC,MAAM,EAAEsC,MAAM,GAAG,IAAInD,KAAK,CAACkD,KAAK,CAAC,EAAEE,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGF,KAAK,EAAEE,KAAK,EAAE,EAAE;MAC/FD,MAAM,CAACC,KAAK,CAAC,GAAGxC,SAAS,CAACwC,KAAK,CAAC;IAClC;IAEA,OAAOD,MAAM,CAACE,MAAM,CAAC,UAAUC,WAAW,EAAEC,KAAK,EAAEC,KAAK,EAAE;MACxDF,WAAW,CAACR,UAAU,CAACU,KAAK,CAAC,CAAC,GAAGD,KAAK;MACtC,OAAOD,WAAW;IACpB,CAAC,EAAE,CAAC,CAAC,CAAC;EACR,CAAC,CAAC;EACF,OAAON,cAAc;AACvB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}