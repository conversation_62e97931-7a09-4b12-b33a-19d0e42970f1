apiVersion: v1
kind: Namespace
metadata:
  name: clutch

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: clutch
  namespace: clutch

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
 name: lyft:clutch
rules:
- apiGroups: [""]
  resources: ["pods", "pods/status"]
  verbs: ["get", "list", "watch", "update"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["delete"]
- apiGroups: ["autoscaling"]
  resources: ["horizontalpodautoscalers", "horizontalpodautoscalers/status"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["autoscaling"]
  resources: ["horizontalpodautoscalers"]
  verbs: ["patch", "update"]
- apiGroups: ["extensions", "apps"]
  resources: ["deployments", "deployments/scale", "deployments/status"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["extensions", "apps"]
  resources: ["deployments/scale"]
  verbs: ["patch", "update"]
- apiGroups: ["extensions", "apps"]
  resources: ["deployments"]
  verbs: ["update"]

---
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
 name: lyft:clutch
roleRef:
 apiGroup: rbac.authorization.k8s.io
 kind: ClusterRole
 name: lyft:clutch
subjects:
- kind: ServiceAccount
  name: clutch
  namespace: clutch

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: clutch-config
  namespace: clutch
data:
  clutch-config.yaml: |
    gateway:
      logger:
        pretty: true
        level: DEBUG
      stats:
        flush_interval: 1s
        log_reporter: {}
      timeouts:
        default: 15s
      middleware:
        - name: clutch.middleware.stats
        - name: clutch.middleware.validate
      listener:
        tcp:
          address: 0.0.0.0
          port: 8080
          secure: false
    modules:
      - name: clutch.module.assets
      - name: clutch.module.healthcheck
      - name: clutch.module.resolver
      - name: clutch.module.aws
      - name: clutch.module.envoytriage
      - name: clutch.module.k8s
    services:
      - name: clutch.service.aws
        typed_config:
          "@type": types.google.com/clutch.config.service.aws.v1.Config
          regions:
            - us-east-1
            - us-west-2
      - name: clutch.service.envoyadmin
        typed_config:
          "@type": types.google.com/clutch.config.service.envoyadmin.v1.Config
          secure: false
          default_remote_port: 9876
      - name: clutch.service.k8s
        typed_config:
          "@type": types.google.com/clutch.config.service.k8s.v1.Config
    resolvers:
      - name: clutch.resolver.aws
      - name: clutch.resolver.k8s

---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: clutch
  name: clutch
  namespace: clutch
spec:
  type: ClusterIP
  ports:
  - port: 80
    protocol: TCP
    targetPort: 8080
    name: clutch2
  - port: 8080
    protocol: TCP
    targetPort: 8080
    name: clutch
  selector:
    app: clutch

---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: clutch
  name: clutch
  namespace: clutch
spec:
  replicas: 1
  selector:
    matchLabels:
      app: clutch
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: clutch
    spec:
      serviceAccount: clutch
      containers:
      - image: index.docker.io/lyft/clutch:latest
        command: ["./clutch"]
        args: ["-c", "/config/clutch-config.yaml"]
        imagePullPolicy: Always
        name: clutch
        volumeMounts:
        - name: clutch-config-volume
          mountPath: /config
      volumes:
        - name: clutch-config-volume
          configMap:
            name: clutch-config
