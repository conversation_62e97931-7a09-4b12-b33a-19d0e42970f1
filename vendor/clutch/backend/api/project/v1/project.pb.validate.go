// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: project/v1/project.proto

package projectv1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetProjectsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetProjectsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetProjectsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetProjectsRequestMultiError, or nil if none found.
func (m *GetProjectsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetProjectsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ExcludeDependencies

	if len(errors) > 0 {
		return GetProjectsRequestMultiError(errors)
	}

	return nil
}

// GetProjectsRequestMultiError is an error wrapping multiple validation errors
// returned by GetProjectsRequest.ValidateAll() if the designated constraints
// aren't met.
type GetProjectsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetProjectsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetProjectsRequestMultiError) AllErrors() []error { return m }

// GetProjectsRequestValidationError is the validation error returned by
// GetProjectsRequest.Validate if the designated constraints aren't met.
type GetProjectsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetProjectsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetProjectsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetProjectsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetProjectsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetProjectsRequestValidationError) ErrorName() string {
	return "GetProjectsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetProjectsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetProjectsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetProjectsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetProjectsRequestValidationError{}

// Validate checks the field values on ProjectResult with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProjectResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProjectResult with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ProjectResultMultiError, or
// nil if none found.
func (m *ProjectResult) ValidateAll() error {
	return m.validate(true)
}

func (m *ProjectResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFrom()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProjectResultValidationError{
					field:  "From",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProjectResultValidationError{
					field:  "From",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFrom()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProjectResultValidationError{
				field:  "From",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProject()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProjectResultValidationError{
					field:  "Project",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProjectResultValidationError{
					field:  "Project",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProject()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProjectResultValidationError{
				field:  "Project",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProjectResultMultiError(errors)
	}

	return nil
}

// ProjectResultMultiError is an error wrapping multiple validation errors
// returned by ProjectResult.ValidateAll() if the designated constraints
// aren't met.
type ProjectResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProjectResultMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProjectResultMultiError) AllErrors() []error { return m }

// ProjectResultValidationError is the validation error returned by
// ProjectResult.Validate if the designated constraints aren't met.
type ProjectResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProjectResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProjectResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProjectResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProjectResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProjectResultValidationError) ErrorName() string { return "ProjectResultValidationError" }

// Error satisfies the builtin error interface
func (e ProjectResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProjectResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProjectResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProjectResultValidationError{}

// Validate checks the field values on GetProjectsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetProjectsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetProjectsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetProjectsResponseMultiError, or nil if none found.
func (m *GetProjectsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetProjectsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	{
		sorted_keys := make([]string, len(m.GetResults()))
		i := 0
		for key := range m.GetResults() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetResults()[key]
			_ = val

			// no validation rules for Results[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, GetProjectsResponseValidationError{
							field:  fmt.Sprintf("Results[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, GetProjectsResponseValidationError{
							field:  fmt.Sprintf("Results[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return GetProjectsResponseValidationError{
						field:  fmt.Sprintf("Results[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	for idx, item := range m.GetPartialFailures() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetProjectsResponseValidationError{
						field:  fmt.Sprintf("PartialFailures[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetProjectsResponseValidationError{
						field:  fmt.Sprintf("PartialFailures[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetProjectsResponseValidationError{
					field:  fmt.Sprintf("PartialFailures[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetProjectsResponseMultiError(errors)
	}

	return nil
}

// GetProjectsResponseMultiError is an error wrapping multiple validation
// errors returned by GetProjectsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetProjectsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetProjectsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetProjectsResponseMultiError) AllErrors() []error { return m }

// GetProjectsResponseValidationError is the validation error returned by
// GetProjectsResponse.Validate if the designated constraints aren't met.
type GetProjectsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetProjectsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetProjectsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetProjectsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetProjectsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetProjectsResponseValidationError) ErrorName() string {
	return "GetProjectsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetProjectsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetProjectsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetProjectsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetProjectsResponseValidationError{}

// Validate checks the field values on ProjectResult_From with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProjectResult_From) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProjectResult_From with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProjectResult_FromMultiError, or nil if none found.
func (m *ProjectResult_From) ValidateAll() error {
	return m.validate(true)
}

func (m *ProjectResult_From) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Selected

	if len(errors) > 0 {
		return ProjectResult_FromMultiError(errors)
	}

	return nil
}

// ProjectResult_FromMultiError is an error wrapping multiple validation errors
// returned by ProjectResult_From.ValidateAll() if the designated constraints
// aren't met.
type ProjectResult_FromMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProjectResult_FromMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProjectResult_FromMultiError) AllErrors() []error { return m }

// ProjectResult_FromValidationError is the validation error returned by
// ProjectResult_From.Validate if the designated constraints aren't met.
type ProjectResult_FromValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProjectResult_FromValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProjectResult_FromValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProjectResult_FromValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProjectResult_FromValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProjectResult_FromValidationError) ErrorName() string {
	return "ProjectResult_FromValidationError"
}

// Error satisfies the builtin error interface
func (e ProjectResult_FromValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProjectResult_From.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProjectResult_FromValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProjectResult_FromValidationError{}
