// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: topology/v1/topology_api.proto

package topologyv1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetTopologyRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTopologyRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTopologyRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTopologyRequestMultiError, or nil if none found.
func (m *GetTopologyRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTopologyRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetQueries() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTopologyRequestValidationError{
						field:  fmt.Sprintf("Queries[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTopologyRequestValidationError{
						field:  fmt.Sprintf("Queries[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTopologyRequestValidationError{
					field:  fmt.Sprintf("Queries[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetTopologyRequestMultiError(errors)
	}

	return nil
}

// GetTopologyRequestMultiError is an error wrapping multiple validation errors
// returned by GetTopologyRequest.ValidateAll() if the designated constraints
// aren't met.
type GetTopologyRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTopologyRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTopologyRequestMultiError) AllErrors() []error { return m }

// GetTopologyRequestValidationError is the validation error returned by
// GetTopologyRequest.Validate if the designated constraints aren't met.
type GetTopologyRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTopologyRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTopologyRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTopologyRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTopologyRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTopologyRequestValidationError) ErrorName() string {
	return "GetTopologyRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTopologyRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTopologyRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTopologyRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTopologyRequestValidationError{}

// Validate checks the field values on GetTopologyResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTopologyResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTopologyResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTopologyResponseMultiError, or nil if none found.
func (m *GetTopologyResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTopologyResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetResults() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTopologyResponseValidationError{
						field:  fmt.Sprintf("Results[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTopologyResponseValidationError{
						field:  fmt.Sprintf("Results[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTopologyResponseValidationError{
					field:  fmt.Sprintf("Results[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetTopologyResponseMultiError(errors)
	}

	return nil
}

// GetTopologyResponseMultiError is an error wrapping multiple validation
// errors returned by GetTopologyResponse.ValidateAll() if the designated
// constraints aren't met.
type GetTopologyResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTopologyResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTopologyResponseMultiError) AllErrors() []error { return m }

// GetTopologyResponseValidationError is the validation error returned by
// GetTopologyResponse.Validate if the designated constraints aren't met.
type GetTopologyResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTopologyResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTopologyResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTopologyResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTopologyResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTopologyResponseValidationError) ErrorName() string {
	return "GetTopologyResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTopologyResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTopologyResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTopologyResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTopologyResponseValidationError{}

// Validate checks the field values on SearchRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SearchRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SearchRequestMultiError, or
// nil if none found.
func (m *SearchRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSort()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchRequestValidationError{
					field:  "Sort",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchRequestValidationError{
					field:  "Sort",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSort()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchRequestValidationError{
				field:  "Sort",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PageToken

	// no validation rules for Limit

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchRequestValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SearchRequestMultiError(errors)
	}

	return nil
}

// SearchRequestMultiError is an error wrapping multiple validation errors
// returned by SearchRequest.ValidateAll() if the designated constraints
// aren't met.
type SearchRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchRequestMultiError) AllErrors() []error { return m }

// SearchRequestValidationError is the validation error returned by
// SearchRequest.Validate if the designated constraints aren't met.
type SearchRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchRequestValidationError) ErrorName() string { return "SearchRequestValidationError" }

// Error satisfies the builtin error interface
func (e SearchRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchRequestValidationError{}

// Validate checks the field values on SearchResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SearchResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SearchResponseMultiError,
// or nil if none found.
func (m *SearchResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetResources() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchResponseValidationError{
						field:  fmt.Sprintf("Resources[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchResponseValidationError{
						field:  fmt.Sprintf("Resources[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchResponseValidationError{
					field:  fmt.Sprintf("Resources[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextPageToken

	if len(errors) > 0 {
		return SearchResponseMultiError(errors)
	}

	return nil
}

// SearchResponseMultiError is an error wrapping multiple validation errors
// returned by SearchResponse.ValidateAll() if the designated constraints
// aren't met.
type SearchResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchResponseMultiError) AllErrors() []error { return m }

// SearchResponseValidationError is the validation error returned by
// SearchResponse.Validate if the designated constraints aren't met.
type SearchResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchResponseValidationError) ErrorName() string { return "SearchResponseValidationError" }

// Error satisfies the builtin error interface
func (e SearchResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchResponseValidationError{}

// Validate checks the field values on FeatureQuery with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FeatureQuery) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FeatureQuery with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FeatureQueryMultiError, or
// nil if none found.
func (m *FeatureQuery) ValidateAll() error {
	return m.validate(true)
}

func (m *FeatureQuery) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	if len(errors) > 0 {
		return FeatureQueryMultiError(errors)
	}

	return nil
}

// FeatureQueryMultiError is an error wrapping multiple validation errors
// returned by FeatureQuery.ValidateAll() if the designated constraints aren't met.
type FeatureQueryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FeatureQueryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FeatureQueryMultiError) AllErrors() []error { return m }

// FeatureQueryValidationError is the validation error returned by
// FeatureQuery.Validate if the designated constraints aren't met.
type FeatureQueryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FeatureQueryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FeatureQueryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FeatureQueryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FeatureQueryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FeatureQueryValidationError) ErrorName() string { return "FeatureQueryValidationError" }

// Error satisfies the builtin error interface
func (e FeatureQueryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFeatureQuery.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FeatureQueryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FeatureQueryValidationError{}

// Validate checks the field values on Constraint with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Constraint) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Constraint with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ConstraintMultiError, or
// nil if none found.
func (m *Constraint) ValidateAll() error {
	return m.validate(true)
}

func (m *Constraint) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Operator

	if all {
		switch v := interface{}(m.GetValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConstraintValidationError{
					field:  "Value",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConstraintValidationError{
					field:  "Value",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConstraintValidationError{
				field:  "Value",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConstraintMultiError(errors)
	}

	return nil
}

// ConstraintMultiError is an error wrapping multiple validation errors
// returned by Constraint.ValidateAll() if the designated constraints aren't met.
type ConstraintMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConstraintMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConstraintMultiError) AllErrors() []error { return m }

// ConstraintValidationError is the validation error returned by
// Constraint.Validate if the designated constraints aren't met.
type ConstraintValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConstraintValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConstraintValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConstraintValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConstraintValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConstraintValidationError) ErrorName() string { return "ConstraintValidationError" }

// Error satisfies the builtin error interface
func (e ConstraintValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConstraint.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConstraintValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConstraintValidationError{}

// Validate checks the field values on MetadataQuery with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MetadataQuery) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MetadataQuery with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MetadataQueryMultiError, or
// nil if none found.
func (m *MetadataQuery) ValidateAll() error {
	return m.validate(true)
}

func (m *MetadataQuery) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	if all {
		switch v := interface{}(m.GetParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MetadataQueryValidationError{
					field:  "Params",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MetadataQueryValidationError{
					field:  "Params",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MetadataQueryValidationError{
				field:  "Params",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Aggregation

	for idx, item := range m.GetConstraints() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MetadataQueryValidationError{
						field:  fmt.Sprintf("Constraints[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MetadataQueryValidationError{
						field:  fmt.Sprintf("Constraints[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MetadataQueryValidationError{
					field:  fmt.Sprintf("Constraints[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return MetadataQueryMultiError(errors)
	}

	return nil
}

// MetadataQueryMultiError is an error wrapping multiple validation errors
// returned by MetadataQuery.ValidateAll() if the designated constraints
// aren't met.
type MetadataQueryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MetadataQueryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MetadataQueryMultiError) AllErrors() []error { return m }

// MetadataQueryValidationError is the validation error returned by
// MetadataQuery.Validate if the designated constraints aren't met.
type MetadataQueryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MetadataQueryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MetadataQueryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MetadataQueryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MetadataQueryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MetadataQueryValidationError) ErrorName() string { return "MetadataQueryValidationError" }

// Error satisfies the builtin error interface
func (e MetadataQueryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMetadataQuery.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MetadataQueryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MetadataQueryValidationError{}

// Validate checks the field values on Query with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Query) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Query with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in QueryMultiError, or nil if none found.
func (m *Query) ValidateAll() error {
	return m.validate(true)
}

func (m *Query) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetFeatures() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QueryValidationError{
						field:  fmt.Sprintf("Features[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QueryValidationError{
						field:  fmt.Sprintf("Features[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QueryValidationError{
					field:  fmt.Sprintf("Features[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetNodeMetadata() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QueryValidationError{
						field:  fmt.Sprintf("NodeMetadata[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QueryValidationError{
						field:  fmt.Sprintf("NodeMetadata[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QueryValidationError{
					field:  fmt.Sprintf("NodeMetadata[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetEdgeMetadata() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QueryValidationError{
						field:  fmt.Sprintf("EdgeMetadata[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QueryValidationError{
						field:  fmt.Sprintf("EdgeMetadata[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QueryValidationError{
					field:  fmt.Sprintf("EdgeMetadata[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for SourceDepth

	// no validation rules for TargetDepth

	if len(errors) > 0 {
		return QueryMultiError(errors)
	}

	return nil
}

// QueryMultiError is an error wrapping multiple validation errors returned by
// Query.ValidateAll() if the designated constraints aren't met.
type QueryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryMultiError) AllErrors() []error { return m }

// QueryValidationError is the validation error returned by Query.Validate if
// the designated constraints aren't met.
type QueryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryValidationError) ErrorName() string { return "QueryValidationError" }

// Error satisfies the builtin error interface
func (e QueryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQuery.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryValidationError{}

// Validate checks the field values on QueryResult with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *QueryResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QueryResult with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in QueryResultMultiError, or
// nil if none found.
func (m *QueryResult) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, QueryResultValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, QueryResultValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QueryResultValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetQuery()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, QueryResultValidationError{
					field:  "Query",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, QueryResultValidationError{
					field:  "Query",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetQuery()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QueryResultValidationError{
				field:  "Query",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetNodes()))
		i := 0
		for key := range m.GetNodes() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetNodes()[key]
			_ = val

			// no validation rules for Nodes[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, QueryResultValidationError{
							field:  fmt.Sprintf("Nodes[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, QueryResultValidationError{
							field:  fmt.Sprintf("Nodes[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return QueryResultValidationError{
						field:  fmt.Sprintf("Nodes[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	{
		sorted_keys := make([]string, len(m.GetEdges()))
		i := 0
		for key := range m.GetEdges() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetEdges()[key]
			_ = val

			// no validation rules for Edges[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, QueryResultValidationError{
							field:  fmt.Sprintf("Edges[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, QueryResultValidationError{
							field:  fmt.Sprintf("Edges[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return QueryResultValidationError{
						field:  fmt.Sprintf("Edges[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return QueryResultMultiError(errors)
	}

	return nil
}

// QueryResultMultiError is an error wrapping multiple validation errors
// returned by QueryResult.ValidateAll() if the designated constraints aren't met.
type QueryResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryResultMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryResultMultiError) AllErrors() []error { return m }

// QueryResultValidationError is the validation error returned by
// QueryResult.Validate if the designated constraints aren't met.
type QueryResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryResultValidationError) ErrorName() string { return "QueryResultValidationError" }

// Error satisfies the builtin error interface
func (e QueryResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryResultValidationError{}

// Validate checks the field values on Node with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Node) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Node with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in NodeMultiError, or nil if none found.
func (m *Node) ValidateAll() error {
	return m.validate(true)
}

func (m *Node) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Features

	{
		sorted_keys := make([]string, len(m.GetMetadata()))
		i := 0
		for key := range m.GetMetadata() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetMetadata()[key]
			_ = val

			// no validation rules for Metadata[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, NodeValidationError{
							field:  fmt.Sprintf("Metadata[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, NodeValidationError{
							field:  fmt.Sprintf("Metadata[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return NodeValidationError{
						field:  fmt.Sprintf("Metadata[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return NodeMultiError(errors)
	}

	return nil
}

// NodeMultiError is an error wrapping multiple validation errors returned by
// Node.ValidateAll() if the designated constraints aren't met.
type NodeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NodeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NodeMultiError) AllErrors() []error { return m }

// NodeValidationError is the validation error returned by Node.Validate if the
// designated constraints aren't met.
type NodeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NodeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NodeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NodeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NodeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NodeValidationError) ErrorName() string { return "NodeValidationError" }

// Error satisfies the builtin error interface
func (e NodeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNode.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NodeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NodeValidationError{}

// Validate checks the field values on Edge with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Edge) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Edge with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in EdgeMultiError, or nil if none found.
func (m *Edge) ValidateAll() error {
	return m.validate(true)
}

func (m *Edge) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for SourceNodeId

	// no validation rules for TargetNodeId

	{
		sorted_keys := make([]string, len(m.GetMetadata()))
		i := 0
		for key := range m.GetMetadata() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetMetadata()[key]
			_ = val

			// no validation rules for Metadata[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, EdgeValidationError{
							field:  fmt.Sprintf("Metadata[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, EdgeValidationError{
							field:  fmt.Sprintf("Metadata[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return EdgeValidationError{
						field:  fmt.Sprintf("Metadata[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return EdgeMultiError(errors)
	}

	return nil
}

// EdgeMultiError is an error wrapping multiple validation errors returned by
// Edge.ValidateAll() if the designated constraints aren't met.
type EdgeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EdgeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EdgeMultiError) AllErrors() []error { return m }

// EdgeValidationError is the validation error returned by Edge.Validate if the
// designated constraints aren't met.
type EdgeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EdgeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EdgeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EdgeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EdgeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EdgeValidationError) ErrorName() string { return "EdgeValidationError" }

// Error satisfies the builtin error interface
func (e EdgeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEdge.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EdgeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EdgeValidationError{}

// Validate checks the field values on Resource with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Resource) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Resource with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ResourceMultiError, or nil
// if none found.
func (m *Resource) ValidateAll() error {
	return m.validate(true)
}

func (m *Resource) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetPb()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ResourceValidationError{
					field:  "Pb",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ResourceValidationError{
					field:  "Pb",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPb()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ResourceValidationError{
				field:  "Pb",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetMetadata()))
		i := 0
		for key := range m.GetMetadata() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetMetadata()[key]
			_ = val

			// no validation rules for Metadata[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, ResourceValidationError{
							field:  fmt.Sprintf("Metadata[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, ResourceValidationError{
							field:  fmt.Sprintf("Metadata[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return ResourceValidationError{
						field:  fmt.Sprintf("Metadata[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return ResourceMultiError(errors)
	}

	return nil
}

// ResourceMultiError is an error wrapping multiple validation errors returned
// by Resource.ValidateAll() if the designated constraints aren't met.
type ResourceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResourceMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResourceMultiError) AllErrors() []error { return m }

// ResourceValidationError is the validation error returned by
// Resource.Validate if the designated constraints aren't met.
type ResourceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResourceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResourceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResourceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResourceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResourceValidationError) ErrorName() string { return "ResourceValidationError" }

// Error satisfies the builtin error interface
func (e ResourceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResource.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResourceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResourceValidationError{}

// Validate checks the field values on UpdateCacheRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateCacheRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateCacheRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateCacheRequestMultiError, or nil if none found.
func (m *UpdateCacheRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCacheRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateCacheRequestValidationError{
					field:  "Resource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateCacheRequestValidationError{
					field:  "Resource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateCacheRequestValidationError{
				field:  "Resource",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Action

	if len(errors) > 0 {
		return UpdateCacheRequestMultiError(errors)
	}

	return nil
}

// UpdateCacheRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateCacheRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateCacheRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCacheRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCacheRequestMultiError) AllErrors() []error { return m }

// UpdateCacheRequestValidationError is the validation error returned by
// UpdateCacheRequest.Validate if the designated constraints aren't met.
type UpdateCacheRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCacheRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCacheRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCacheRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCacheRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCacheRequestValidationError) ErrorName() string {
	return "UpdateCacheRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateCacheRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCacheRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCacheRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCacheRequestValidationError{}

// Validate checks the field values on SearchRequest_Sort with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchRequest_Sort) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchRequest_Sort with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchRequest_SortMultiError, or nil if none found.
func (m *SearchRequest_Sort) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchRequest_Sort) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Direction

	if utf8.RuneCountInString(m.GetField()) < 1 {
		err := SearchRequest_SortValidationError{
			field:  "Field",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return SearchRequest_SortMultiError(errors)
	}

	return nil
}

// SearchRequest_SortMultiError is an error wrapping multiple validation errors
// returned by SearchRequest_Sort.ValidateAll() if the designated constraints
// aren't met.
type SearchRequest_SortMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchRequest_SortMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchRequest_SortMultiError) AllErrors() []error { return m }

// SearchRequest_SortValidationError is the validation error returned by
// SearchRequest_Sort.Validate if the designated constraints aren't met.
type SearchRequest_SortValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchRequest_SortValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchRequest_SortValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchRequest_SortValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchRequest_SortValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchRequest_SortValidationError) ErrorName() string {
	return "SearchRequest_SortValidationError"
}

// Error satisfies the builtin error interface
func (e SearchRequest_SortValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchRequest_Sort.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchRequest_SortValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchRequest_SortValidationError{}

// Validate checks the field values on SearchRequest_Filter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchRequest_Filter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchRequest_Filter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchRequest_FilterMultiError, or nil if none found.
func (m *SearchRequest_Filter) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchRequest_Filter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSearch()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchRequest_FilterValidationError{
					field:  "Search",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchRequest_FilterValidationError{
					field:  "Search",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSearch()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchRequest_FilterValidationError{
				field:  "Search",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TypeUrl

	// no validation rules for Metadata

	// no validation rules for CaseSensitive

	if len(errors) > 0 {
		return SearchRequest_FilterMultiError(errors)
	}

	return nil
}

// SearchRequest_FilterMultiError is an error wrapping multiple validation
// errors returned by SearchRequest_Filter.ValidateAll() if the designated
// constraints aren't met.
type SearchRequest_FilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchRequest_FilterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchRequest_FilterMultiError) AllErrors() []error { return m }

// SearchRequest_FilterValidationError is the validation error returned by
// SearchRequest_Filter.Validate if the designated constraints aren't met.
type SearchRequest_FilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchRequest_FilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchRequest_FilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchRequest_FilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchRequest_FilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchRequest_FilterValidationError) ErrorName() string {
	return "SearchRequest_FilterValidationError"
}

// Error satisfies the builtin error interface
func (e SearchRequest_FilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchRequest_Filter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchRequest_FilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchRequest_FilterValidationError{}

// Validate checks the field values on SearchRequest_Filter_Search with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchRequest_Filter_Search) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchRequest_Filter_Search with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchRequest_Filter_SearchMultiError, or nil if none found.
func (m *SearchRequest_Filter_Search) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchRequest_Filter_Search) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetField()) < 1 {
		err := SearchRequest_Filter_SearchValidationError{
			field:  "Field",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetText()) < 1 {
		err := SearchRequest_Filter_SearchValidationError{
			field:  "Text",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return SearchRequest_Filter_SearchMultiError(errors)
	}

	return nil
}

// SearchRequest_Filter_SearchMultiError is an error wrapping multiple
// validation errors returned by SearchRequest_Filter_Search.ValidateAll() if
// the designated constraints aren't met.
type SearchRequest_Filter_SearchMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchRequest_Filter_SearchMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchRequest_Filter_SearchMultiError) AllErrors() []error { return m }

// SearchRequest_Filter_SearchValidationError is the validation error returned
// by SearchRequest_Filter_Search.Validate if the designated constraints
// aren't met.
type SearchRequest_Filter_SearchValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchRequest_Filter_SearchValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchRequest_Filter_SearchValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchRequest_Filter_SearchValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchRequest_Filter_SearchValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchRequest_Filter_SearchValidationError) ErrorName() string {
	return "SearchRequest_Filter_SearchValidationError"
}

// Error satisfies the builtin error interface
func (e SearchRequest_Filter_SearchValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchRequest_Filter_Search.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchRequest_Filter_SearchValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchRequest_Filter_SearchValidationError{}
