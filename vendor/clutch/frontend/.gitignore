# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# BE SURE TO ALSO UPDATE .dockerignore when adding ignore directives that could affect the build (probably all of them)!

# tooling
.yarn
lerna-debug.log*
npm-debug.log*
yarn-debug.log*
yarn-error.log*
*.tsbuildinfo
api/*.tsbuildinfo
package-lock.json
**/.eslintcache

# dependencies
*node_modules
/.pnp
.pnp.js

# testing
/coverage
**/cypress/screenshots
**/cypress/videos

# production
**/build
**/dist
storybook-static

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

# clutch generated files
**/workflows.jsx
