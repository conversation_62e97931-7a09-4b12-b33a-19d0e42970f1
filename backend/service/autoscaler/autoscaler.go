package autoscaler

import (
	"context"
	"fmt"
	"math"
	"sync"
	"time"

	"github.com/cainuro/orchestrator/internal/cache"
	"github.com/cainuro/orchestrator/internal/config"
	orchestratorv1 "github.com/cainuro/orchestrator/proto/orchestrator/v1"
	"go.uber.org/zap"
)

// Service defines the autoscaler service interface
type Service interface {
	GetAutoscalerStatus(ctx context.Context, req *orchestratorv1.GetAutoscalerStatusRequest) (*orchestratorv1.GetAutoscalerStatusResponse, error)
	UpdateAutoscalerConfig(ctx context.Context, req *orchestratorv1.UpdateAutoscalerConfigRequest) (*orchestratorv1.UpdateAutoscalerConfigResponse, error)
	StartAutoscaler(ctx context.Context) error
	StopAutoscaler() error
	GetScalingHistory() []ScalingEvent
}

// service implements the Service interface
type service struct {
	config *config.Config
	logger *zap.Logger
	cache  *cache.RistrettoCache

	// Autoscaler state
	enabled         bool
	currentReplicas int32
	desiredReplicas int32
	minReplicas     int32
	maxReplicas     int32
	targetCPU       int32
	status          string
	mu              sync.RWMutex

	// Scaling history
	scalingHistory []ScalingEvent
	historyMu      sync.RWMutex

	// Control loop
	stopChan chan struct{}
	running  bool
}

// ScalingEvent represents a scaling action
type ScalingEvent struct {
	Timestamp       time.Time `json:"timestamp"`
	Action          string    `json:"action"` // scale_up, scale_down, no_action
	FromReplicas    int32     `json:"from_replicas"`
	ToReplicas      int32     `json:"to_replicas"`
	Reason          string    `json:"reason"`
	CPUUtilization  float64   `json:"cpu_utilization"`
	TargetCPU       int32     `json:"target_cpu"`
	DecisionLatency string    `json:"decision_latency"`
}

// MetricsSnapshot represents current system metrics
type MetricsSnapshot struct {
	Timestamp      time.Time `json:"timestamp"`
	CPUUtilization float64   `json:"cpu_utilization"`
	MemoryUsage    float64   `json:"memory_usage"`
	RequestRate    float64   `json:"request_rate"`
	ResponseTime   float64   `json:"response_time"`
	ErrorRate      float64   `json:"error_rate"`
}

// NewService creates a new autoscaler service
func NewService(
	config *config.Config,
	logger *zap.Logger,
	cache *cache.RistrettoCache,
) (Service, error) {
	s := &service{
		config:          config,
		logger:          logger,
		cache:           cache,
		enabled:         true,
		currentReplicas: 3,
		desiredReplicas: 3,
		minReplicas:     1,
		maxReplicas:     10,
		targetCPU:       70,
		status:          "stable",
		scalingHistory:  make([]ScalingEvent, 0),
		stopChan:        make(chan struct{}),
	}

	return s, nil
}

// GetAutoscalerStatus returns the current autoscaler status
func (s *service) GetAutoscalerStatus(ctx context.Context, req *orchestratorv1.GetAutoscalerStatusRequest) (*orchestratorv1.GetAutoscalerStatusResponse, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	s.logger.Info("Getting autoscaler status")

	return &orchestratorv1.GetAutoscalerStatusResponse{
		Enabled:         s.enabled,
		CurrentReplicas: s.currentReplicas,
		DesiredReplicas: s.desiredReplicas,
		Status:          s.status,
	}, nil
}

// UpdateAutoscalerConfig updates the autoscaler configuration
func (s *service) UpdateAutoscalerConfig(ctx context.Context, req *orchestratorv1.UpdateAutoscalerConfigRequest) (*orchestratorv1.UpdateAutoscalerConfigResponse, error) {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.logger.Info("Updating autoscaler config",
		zap.Bool("enabled", req.Enabled),
		zap.Int32("min_replicas", req.MinReplicas),
		zap.Int32("max_replicas", req.MaxReplicas),
		zap.Int32("target_cpu", req.TargetCpuPercent))

	// Validate configuration
	if req.MinReplicas < 1 {
		return &orchestratorv1.UpdateAutoscalerConfigResponse{
			Success: false,
			Message: "Minimum replicas must be at least 1",
		}, nil
	}

	if req.MaxReplicas < req.MinReplicas {
		return &orchestratorv1.UpdateAutoscalerConfigResponse{
			Success: false,
			Message: "Maximum replicas must be greater than or equal to minimum replicas",
		}, nil
	}

	if req.TargetCpuPercent < 1 || req.TargetCpuPercent > 100 {
		return &orchestratorv1.UpdateAutoscalerConfigResponse{
			Success: false,
			Message: "Target CPU percentage must be between 1 and 100",
		}, nil
	}

	// Update configuration
	s.enabled = req.Enabled
	s.minReplicas = req.MinReplicas
	s.maxReplicas = req.MaxReplicas
	s.targetCPU = req.TargetCpuPercent

	// Adjust current replicas if they're outside the new bounds
	if s.currentReplicas < s.minReplicas {
		s.desiredReplicas = s.minReplicas
		s.status = "scaling_up"
	} else if s.currentReplicas > s.maxReplicas {
		s.desiredReplicas = s.maxReplicas
		s.status = "scaling_down"
	}

	// Cache the configuration
	configData := map[string]interface{}{
		"enabled":            s.enabled,
		"min_replicas":       s.minReplicas,
		"max_replicas":       s.maxReplicas,
		"target_cpu_percent": s.targetCPU,
		"updated_at":         time.Now().UTC(),
	}
	s.cache.SetWithTTL(ctx, "autoscaler_config", configData, 24*time.Hour)

	return &orchestratorv1.UpdateAutoscalerConfigResponse{
		Success: true,
		Message: "Autoscaler configuration updated successfully",
	}, nil
}

// StartAutoscaler starts the autoscaling control loop
func (s *service) StartAutoscaler(ctx context.Context) error {
	s.mu.Lock()
	if s.running {
		s.mu.Unlock()
		return fmt.Errorf("autoscaler is already running")
	}
	s.running = true
	s.mu.Unlock()

	s.logger.Info("Starting autoscaler control loop")

	go s.controlLoop(ctx)
	return nil
}

// StopAutoscaler stops the autoscaling control loop
func (s *service) StopAutoscaler() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.running {
		return fmt.Errorf("autoscaler is not running")
	}

	s.logger.Info("Stopping autoscaler control loop")
	close(s.stopChan)
	s.running = false
	s.stopChan = make(chan struct{})

	return nil
}

// GetScalingHistory returns the scaling event history
func (s *service) GetScalingHistory() []ScalingEvent {
	s.historyMu.RLock()
	defer s.historyMu.RUnlock()

	// Return a copy of the history
	history := make([]ScalingEvent, len(s.scalingHistory))
	copy(history, s.scalingHistory)
	return history
}

// controlLoop runs the main autoscaling logic
func (s *service) controlLoop(ctx context.Context) {
	ticker := time.NewTicker(30 * time.Second) // Check every 30 seconds
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			s.logger.Info("Autoscaler control loop stopped due to context cancellation")
			return
		case <-s.stopChan:
			s.logger.Info("Autoscaler control loop stopped")
			return
		case <-ticker.C:
			s.runScalingDecision(ctx)
		}
	}
}

// runScalingDecision executes the scaling decision logic
func (s *service) runScalingDecision(ctx context.Context) {
	startTime := time.Now()

	s.mu.RLock()
	enabled := s.enabled
	currentReplicas := s.currentReplicas
	minReplicas := s.minReplicas
	maxReplicas := s.maxReplicas
	targetCPU := s.targetCPU
	s.mu.RUnlock()

	if !enabled {
		return
	}

	// Get current metrics
	metrics, err := s.getCurrentMetrics(ctx)
	if err != nil {
		s.logger.Error("Failed to get current metrics", zap.Error(err))
		return
	}

	// Calculate desired replicas based on CPU utilization
	desiredReplicas := s.calculateDesiredReplicas(metrics.CPUUtilization, float64(targetCPU), currentReplicas)

	// Apply bounds
	if desiredReplicas < minReplicas {
		desiredReplicas = minReplicas
	}
	if desiredReplicas > maxReplicas {
		desiredReplicas = maxReplicas
	}

	// Determine action
	var action string
	var reason string

	if desiredReplicas > currentReplicas {
		action = "scale_up"
		reason = fmt.Sprintf("CPU utilization %.1f%% > target %d%%", metrics.CPUUtilization, targetCPU)
	} else if desiredReplicas < currentReplicas {
		action = "scale_down"
		reason = fmt.Sprintf("CPU utilization %.1f%% < target %d%%", metrics.CPUUtilization, targetCPU)
	} else {
		action = "no_action"
		reason = fmt.Sprintf("CPU utilization %.1f%% within target range", metrics.CPUUtilization)
	}

	// Record scaling event
	event := ScalingEvent{
		Timestamp:       time.Now().UTC(),
		Action:          action,
		FromReplicas:    currentReplicas,
		ToReplicas:      desiredReplicas,
		Reason:          reason,
		CPUUtilization:  metrics.CPUUtilization,
		TargetCPU:       targetCPU,
		DecisionLatency: time.Since(startTime).String(),
	}

	s.addScalingEvent(event)

	// Update state if scaling is needed
	if desiredReplicas != currentReplicas {
		s.mu.Lock()
		s.desiredReplicas = desiredReplicas
		if action == "scale_up" {
			s.status = "scaling_up"
		} else {
			s.status = "scaling_down"
		}
		s.mu.Unlock()

		s.logger.Info("Scaling decision made",
			zap.String("action", action),
			zap.Int32("from_replicas", currentReplicas),
			zap.Int32("to_replicas", desiredReplicas),
			zap.Float64("cpu_utilization", metrics.CPUUtilization),
			zap.String("reason", reason))

		// Simulate scaling action (in real implementation, this would call K8s API)
		go s.executeScaling(ctx, desiredReplicas)
	}
}

// getCurrentMetrics retrieves current system metrics
func (s *service) getCurrentMetrics(ctx context.Context) (*MetricsSnapshot, error) {
	// Check cache first
	var cachedMetrics MetricsSnapshot
	if found, err := s.cache.Get(ctx, "current_metrics", &cachedMetrics); err == nil && found {
		// Use cached metrics if they're less than 10 seconds old
		if time.Since(cachedMetrics.Timestamp) < 10*time.Second {
			return &cachedMetrics, nil
		}
	}

	// Simulate metrics collection (in real implementation, this would query Prometheus/metrics system)
	metrics := &MetricsSnapshot{
		Timestamp:      time.Now().UTC(),
		CPUUtilization: s.simulateCPUUtilization(),
		MemoryUsage:    s.simulateMemoryUsage(),
		RequestRate:    s.simulateRequestRate(),
		ResponseTime:   s.simulateResponseTime(),
		ErrorRate:      s.simulateErrorRate(),
	}

	// Cache metrics for 5 seconds
	s.cache.SetWithTTL(ctx, "current_metrics", metrics, 5*time.Second)

	return metrics, nil
}

// calculateDesiredReplicas calculates the desired number of replicas based on CPU utilization
func (s *service) calculateDesiredReplicas(currentCPU, targetCPU float64, currentReplicas int32) int32 {
	if currentCPU == 0 {
		return currentReplicas
	}

	// Use the standard HPA formula: desiredReplicas = ceil(currentReplicas * (currentCPU / targetCPU))
	ratio := currentCPU / targetCPU
	desiredReplicas := math.Ceil(float64(currentReplicas) * ratio)

	return int32(desiredReplicas)
}

// executeScaling simulates the actual scaling operation
func (s *service) executeScaling(ctx context.Context, targetReplicas int32) {
	// Simulate scaling delay
	time.Sleep(2 * time.Second)

	s.mu.Lock()
	s.currentReplicas = targetReplicas
	s.desiredReplicas = targetReplicas
	s.status = "stable"
	s.mu.Unlock()

	s.logger.Info("Scaling completed",
		zap.Int32("replicas", targetReplicas))
}

// addScalingEvent adds a scaling event to the history
func (s *service) addScalingEvent(event ScalingEvent) {
	s.historyMu.Lock()
	defer s.historyMu.Unlock()

	s.scalingHistory = append(s.scalingHistory, event)

	// Keep only the last 100 events
	if len(s.scalingHistory) > 100 {
		s.scalingHistory = s.scalingHistory[1:]
	}
}

// Simulation methods (in real implementation, these would query actual metrics)

func (s *service) simulateCPUUtilization() float64 {
	// Simulate varying CPU utilization between 30% and 90%
	base := 60.0
	variation := 20.0 * (0.5 - float64(time.Now().Unix()%2))
	return base + variation + (5.0 * (0.5 - float64(time.Now().Nanosecond()%1000)/1000.0))
}

func (s *service) simulateMemoryUsage() float64 {
	return 45.0 + (10.0 * (0.5 - float64(time.Now().Nanosecond()%1000)/1000.0))
}

func (s *service) simulateRequestRate() float64 {
	return 150.0 + (50.0 * (0.5 - float64(time.Now().Nanosecond()%1000)/1000.0))
}

func (s *service) simulateResponseTime() float64 {
	return 120.0 + (30.0 * (0.5 - float64(time.Now().Nanosecond()%1000)/1000.0))
}

func (s *service) simulateErrorRate() float64 {
	return 0.5 + (0.3 * (0.5 - float64(time.Now().Nanosecond()%1000)/1000.0))
}
