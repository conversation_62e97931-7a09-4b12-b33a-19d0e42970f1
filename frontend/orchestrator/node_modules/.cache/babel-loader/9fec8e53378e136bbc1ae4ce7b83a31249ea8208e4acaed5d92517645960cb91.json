{"ast": null, "code": "import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\nimport axios from 'axios';\nconst initialState = {\n  events: [],\n  loading: false,\n  error: null,\n  totalCount: 0,\n  query: {}\n};\nexport const fetchAuditEvents = createAsyncThunk('audit/fetchEvents', async query => {\n  const response = await axios.post('/v1/audit/query', query);\n  return response.data;\n});\nconst auditSlice = createSlice({\n  name: 'audit',\n  initialState,\n  reducers: {\n    setQuery: (state, action) => {\n      state.query = action.payload;\n    },\n    clearEvents: state => {\n      state.events = [];\n      state.totalCount = 0;\n    }\n  },\n  extraReducers: builder => {\n    builder.addCase(fetchAuditEvents.pending, state => {\n      state.loading = true;\n      state.error = null;\n    }).addCase(fetchAuditEvents.fulfilled, (state, action) => {\n      state.loading = false;\n      state.events = action.payload.events;\n      state.totalCount = action.payload.total_count;\n    }).addCase(fetchAuditEvents.rejected, (state, action) => {\n      state.loading = false;\n      state.error = action.error.message || 'Failed to fetch audit events';\n    });\n  }\n});\nexport const {\n  setQuery,\n  clearEvents\n} = auditSlice.actions;\nexport default auditSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "axios", "initialState", "events", "loading", "error", "totalCount", "query", "fetchAuditEvents", "response", "post", "data", "auditSlice", "name", "reducers", "<PERSON><PERSON><PERSON><PERSON>", "state", "action", "payload", "clearEvents", "extraReducers", "builder", "addCase", "pending", "fulfilled", "total_count", "rejected", "message", "actions", "reducer"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/auditSlice.ts"], "sourcesContent": ["import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';\nimport axios from 'axios';\n\nexport interface AuditEvent {\n  id: string;\n  timestamp: string;\n  user_id: string;\n  action: string;\n  resource: string;\n  resource_id: string;\n  details: Record<string, any>;\n  ip_address: string;\n  user_agent: string;\n  success: boolean;\n  error_msg?: string;\n}\n\nexport interface AuditQuery {\n  user_id?: string;\n  action?: string;\n  resource?: string;\n  resource_id?: string;\n  start_time?: string;\n  end_time?: string;\n  limit?: number;\n}\n\nexport interface AuditState {\n  events: AuditEvent[];\n  loading: boolean;\n  error: string | null;\n  totalCount: number;\n  query: AuditQuery;\n}\n\nconst initialState: AuditState = {\n  events: [],\n  loading: false,\n  error: null,\n  totalCount: 0,\n  query: {},\n};\n\nexport const fetchAuditEvents = createAsyncThunk(\n  'audit/fetchEvents',\n  async (query: AuditQuery) => {\n    const response = await axios.post('/v1/audit/query', query);\n    return response.data;\n  }\n);\n\nconst auditSlice = createSlice({\n  name: 'audit',\n  initialState,\n  reducers: {\n    setQuery: (state, action: PayloadAction<AuditQuery>) => {\n      state.query = action.payload;\n    },\n    clearEvents: (state) => {\n      state.events = [];\n      state.totalCount = 0;\n    },\n  },\n  extraReducers: (builder) => {\n    builder\n      .addCase(fetchAuditEvents.pending, (state) => {\n        state.loading = true;\n        state.error = null;\n      })\n      .addCase(fetchAuditEvents.fulfilled, (state, action) => {\n        state.loading = false;\n        state.events = action.payload.events;\n        state.totalCount = action.payload.total_count;\n      })\n      .addCase(fetchAuditEvents.rejected, (state, action) => {\n        state.loading = false;\n        state.error = action.error.message || 'Failed to fetch audit events';\n      });\n  },\n});\n\nexport const { setQuery, clearEvents } = auditSlice.actions;\nexport default auditSlice.reducer;\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,gBAAgB,QAAuB,kBAAkB;AAC/E,OAAOC,KAAK,MAAM,OAAO;AAkCzB,MAAMC,YAAwB,GAAG;EAC/BC,MAAM,EAAE,EAAE;EACVC,OAAO,EAAE,KAAK;EACdC,KAAK,EAAE,IAAI;EACXC,UAAU,EAAE,CAAC;EACbC,KAAK,EAAE,CAAC;AACV,CAAC;AAED,OAAO,MAAMC,gBAAgB,GAAGR,gBAAgB,CAC9C,mBAAmB,EACnB,MAAOO,KAAiB,IAAK;EAC3B,MAAME,QAAQ,GAAG,MAAMR,KAAK,CAACS,IAAI,CAAC,iBAAiB,EAAEH,KAAK,CAAC;EAC3D,OAAOE,QAAQ,CAACE,IAAI;AACtB,CACF,CAAC;AAED,MAAMC,UAAU,GAAGb,WAAW,CAAC;EAC7Bc,IAAI,EAAE,OAAO;EACbX,YAAY;EACZY,QAAQ,EAAE;IACRC,QAAQ,EAAEA,CAACC,KAAK,EAAEC,MAAiC,KAAK;MACtDD,KAAK,CAACT,KAAK,GAAGU,MAAM,CAACC,OAAO;IAC9B,CAAC;IACDC,WAAW,EAAGH,KAAK,IAAK;MACtBA,KAAK,CAACb,MAAM,GAAG,EAAE;MACjBa,KAAK,CAACV,UAAU,GAAG,CAAC;IACtB;EACF,CAAC;EACDc,aAAa,EAAGC,OAAO,IAAK;IAC1BA,OAAO,CACJC,OAAO,CAACd,gBAAgB,CAACe,OAAO,EAAGP,KAAK,IAAK;MAC5CA,KAAK,CAACZ,OAAO,GAAG,IAAI;MACpBY,KAAK,CAACX,KAAK,GAAG,IAAI;IACpB,CAAC,CAAC,CACDiB,OAAO,CAACd,gBAAgB,CAACgB,SAAS,EAAE,CAACR,KAAK,EAAEC,MAAM,KAAK;MACtDD,KAAK,CAACZ,OAAO,GAAG,KAAK;MACrBY,KAAK,CAACb,MAAM,GAAGc,MAAM,CAACC,OAAO,CAACf,MAAM;MACpCa,KAAK,CAACV,UAAU,GAAGW,MAAM,CAACC,OAAO,CAACO,WAAW;IAC/C,CAAC,CAAC,CACDH,OAAO,CAACd,gBAAgB,CAACkB,QAAQ,EAAE,CAACV,KAAK,EAAEC,MAAM,KAAK;MACrDD,KAAK,CAACZ,OAAO,GAAG,KAAK;MACrBY,KAAK,CAACX,KAAK,GAAGY,MAAM,CAACZ,KAAK,CAACsB,OAAO,IAAI,8BAA8B;IACtE,CAAC,CAAC;EACN;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEZ,QAAQ;EAAEI;AAAY,CAAC,GAAGP,UAAU,CAACgB,OAAO;AAC3D,eAAehB,UAAU,CAACiB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}