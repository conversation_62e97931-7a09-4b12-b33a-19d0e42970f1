{"version": 3, "names": ["Definition", "PatternVisitor", "OriginalPatternVisitor", "Referencer", "OriginalReferencer", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>ana<PERSON>", "require", "get<PERSON><PERSON><PERSON>", "fallback", "visitorKeysMap", "getVisitorValues", "nodeType", "client", "FLOW_FLIPPED_ALIAS_KEYS", "VISITOR_KEYS", "getTypesInfo", "flowFlippedAliasKeys", "Set", "concat", "Object", "entries", "o", "keys", "map", "k", "reduce", "acc", "key", "value", "has", "propertyTypes", "callProperties", "type", "values", "indexers", "properties", "types", "params", "argument", "elementType", "qualification", "rest", "returnType", "typeAnnotation", "typeParameters", "id", "ArrayPattern", "node", "elements", "for<PERSON>ach", "visit", "ObjectPattern", "_client", "WeakMap", "constructor", "options", "scopeManager", "_classPrivateFieldInitSpec", "_classPrivateFieldSet", "visitPattern", "callback", "_checkIdentifierOrVisit", "left", "processRightHandNodes", "visitor", "rightHandNodes", "visitClass", "_ref", "_visitArray", "decorators", "typeParamScope", "_nestTypeParamScope", "_visitTypeAnnotation", "implements", "superTypeParameters", "close", "visitFunction", "visitProperty", "_node$value", "InterfaceDeclaration", "_createScopeVariable", "extends", "body", "TypeAlias", "right", "ClassProperty", "_visitClassProperty", "ClassPrivateProperty", "AccessorProperty", "ClassAccessorProperty", "PropertyDefinition", "ClassPrivateMethod", "MethodDefinition", "DeclareModule", "_visitDeclareX", "DeclareFunction", "DeclareVariable", "DeclareClass", "OptionalMemberExpression", "MemberExpression", "computed", "__nestClassFieldInitializerScope", "__nestScope", "__currentScope", "name", "currentScope", "variableScope", "__define", "parentScope", "scope", "j", "length", "bind", "Array", "isArray", "visitorValues", "_classPrivateFieldGet", "i", "visitorValue", "propertyType", "nodeProperty", "loopPropertyNode", "l", "nodeList", "module", "exports", "analyzeScope", "ast", "parserOptions", "_parserOptions$ecmaFe", "ignoreEval", "optimistic", "directive", "nodejsScope", "sourceType", "ecmaFeatures", "globalReturn", "impliedStrict", "ecmaVersion", "childVisitor<PERSON>eys", "getVisitorKeys", "referencer"], "sources": ["../src/analyze-scope.cts"], "sourcesContent": ["import type { Client } from \"./client.cts\";\n\nconst {\n  Definition,\n  PatternVisitor: OriginalPatternVisitor,\n  Referencer: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n} = (\n  process.env.BABEL_8_BREAKING\n    ? require(\"eslint-scope\")\n    : require(\"@nicolo-ribaudo/eslint-scope-5-internals\")\n) as import(\"./types.cts\").Scope;\nconst { getKeys: fallback } = require(\"eslint-visitor-keys\");\n\nlet visitorKeysMap: Record<string, string[]>;\nfunction getVisitorValues(nodeType: string, client: Client) {\n  if (visitorKeysMap) return visitorKeysMap[nodeType];\n\n  const { FLOW_FLIPPED_ALIAS_KEYS, VISITOR_KEYS } = client.getTypesInfo();\n\n  const flowFlippedAliasKeys = new Set(\n    FLOW_FLIPPED_ALIAS_KEYS.concat([\n      \"ArrayPattern\",\n      \"ClassDeclaration\",\n      \"ClassExpression\",\n      \"FunctionDeclaration\",\n      \"FunctionExpression\",\n      \"Identifier\",\n      \"ObjectPattern\",\n      \"RestElement\",\n    ]),\n  );\n\n  visitorKeysMap = Object.entries(VISITOR_KEYS).reduce((acc, [key, value]) => {\n    if (!flowFlippedAliasKeys.has(value)) {\n      // @ts-expect-error FIXME: value is not assignable to type string[]\n      acc[key] = value;\n    }\n    return acc;\n  }, {});\n\n  return visitorKeysMap[nodeType];\n}\n\nconst propertyTypes = {\n  // loops\n  callProperties: { type: \"loop\", values: [\"value\"] },\n  indexers: { type: \"loop\", values: [\"key\", \"value\"] },\n  properties: { type: \"loop\", values: [\"argument\", \"value\"] },\n  types: { type: \"loop\" },\n  params: { type: \"loop\" },\n  // single property\n  argument: { type: \"single\" },\n  elementType: { type: \"single\" },\n  qualification: { type: \"single\" },\n  rest: { type: \"single\" },\n  returnType: { type: \"single\" },\n  // others\n  typeAnnotation: { type: \"typeAnnotation\" },\n  typeParameters: { type: \"typeParameters\" },\n  id: { type: \"id\" },\n};\n\nclass PatternVisitor extends OriginalPatternVisitor {\n  ArrayPattern(node: any) {\n    node.elements.forEach(this.visit, this);\n  }\n\n  ObjectPattern(node: any) {\n    node.properties.forEach(this.visit, this);\n  }\n}\n\nclass Referencer extends OriginalReferencer {\n  #client;\n\n  constructor(options: any, scopeManager: any, client: Client) {\n    super(options, scopeManager);\n    this.#client = client;\n  }\n\n  // inherits.\n  visitPattern(node: any, options: any, callback: any) {\n    if (!node) {\n      return;\n    }\n\n    // Visit type annotations.\n    this._checkIdentifierOrVisit(node.typeAnnotation);\n    if (node.type === \"AssignmentPattern\") {\n      this._checkIdentifierOrVisit(node.left.typeAnnotation);\n    }\n\n    // Overwrite `super.visitPattern(node, options, callback)` in order to not visit `ArrayPattern#typeAnnotation` and `ObjectPattern#typeAnnotation`.\n    if (typeof options === \"function\") {\n      callback = options;\n      options = { processRightHandNodes: false };\n    }\n\n    const visitor = new PatternVisitor(this.options, node, callback);\n    visitor.visit(node);\n\n    // Process the right hand nodes recursively.\n    if (options.processRightHandNodes) {\n      visitor.rightHandNodes.forEach(this.visit, this);\n    }\n  }\n\n  // inherits.\n  visitClass(node: any) {\n    // Decorators.\n    this._visitArray(node.decorators);\n\n    // Flow type parameters.\n    const typeParamScope = this._nestTypeParamScope(node);\n\n    // Flow super types.\n    this._visitTypeAnnotation(node.implements);\n    this._visitTypeAnnotation(\n      (process.env.BABEL_8_BREAKING\n        ? // @ts-ignore(Babel 7 vs Babel 8) Renamed\n          node.superTypeArguments\n        : // @ts-ignore(Babel 7 vs Babel 8) Renamed\n          node.superTypeParameters\n      )?.params,\n    );\n\n    // Basic.\n    super.visitClass(node);\n\n    // Close the type parameter scope.\n    if (typeParamScope) {\n      this.close(node);\n    }\n  }\n\n  // inherits.\n  visitFunction(node: any) {\n    const typeParamScope = this._nestTypeParamScope(node);\n\n    // Flow return types.\n    this._checkIdentifierOrVisit(node.returnType);\n\n    // Basic.\n    super.visitFunction(node);\n\n    // Close the type parameter scope.\n    if (typeParamScope) {\n      this.close(node);\n    }\n  }\n\n  // inherits.\n  visitProperty(node: any) {\n    if (node.value?.type === \"TypeCastExpression\") {\n      this._visitTypeAnnotation(node.value);\n    }\n    this._visitArray(node.decorators);\n    super.visitProperty(node);\n  }\n\n  InterfaceDeclaration(node: any) {\n    this._createScopeVariable(node, node.id);\n\n    const typeParamScope = this._nestTypeParamScope(node);\n\n    // TODO: Handle mixins\n    this._visitArray(node.extends);\n    this.visit(node.body);\n\n    if (typeParamScope) {\n      this.close(node);\n    }\n  }\n\n  TypeAlias(node: any) {\n    this._createScopeVariable(node, node.id);\n\n    const typeParamScope = this._nestTypeParamScope(node);\n\n    this.visit(node.right);\n\n    if (typeParamScope) {\n      this.close(node);\n    }\n  }\n\n  ClassProperty(node: any) {\n    this._visitClassProperty(node);\n  }\n\n  ClassPrivateProperty(node: any) {\n    this._visitClassProperty(node);\n  }\n\n  AccessorProperty(node: any) {\n    this._visitClassProperty(node);\n  }\n\n  ClassAccessorProperty(node: any) {\n    this._visitClassProperty(node);\n  }\n\n  PropertyDefinition(node: any) {\n    this._visitClassProperty(node);\n  }\n\n  // TODO: Update to visit type annotations when TypeScript/Flow support this syntax.\n  ClassPrivateMethod(node: any) {\n    super.MethodDefinition(node);\n  }\n\n  DeclareModule(node: any) {\n    this._visitDeclareX(node);\n  }\n\n  DeclareFunction(node: any) {\n    this._visitDeclareX(node);\n  }\n\n  DeclareVariable(node: any) {\n    this._visitDeclareX(node);\n  }\n\n  DeclareClass(node: any) {\n    this._visitDeclareX(node);\n  }\n\n  // visit OptionalMemberExpression as a MemberExpression.\n  OptionalMemberExpression(node: any) {\n    super.MemberExpression(node);\n  }\n\n  _visitClassProperty(node: any) {\n    const { computed, key, typeAnnotation, decorators, value } = node;\n\n    this._visitArray(decorators);\n    if (computed) this.visit(key);\n    this._visitTypeAnnotation(typeAnnotation);\n\n    if (value) {\n      if (this.scopeManager.__nestClassFieldInitializerScope) {\n        this.scopeManager.__nestClassFieldInitializerScope(value);\n      } else {\n        // Given that ESLint 7 didn't have a \"class field initializer\" scope,\n        // we create a plain method scope. Semantics are the same.\n        this.scopeManager.__nestScope(\n          new Scope(\n            this.scopeManager,\n            \"function\",\n            this.scopeManager.__currentScope,\n            value,\n            true,\n          ),\n        );\n      }\n      this.visit(value);\n      this.close(value);\n    }\n  }\n\n  _visitDeclareX(node: any) {\n    if (node.id) {\n      this._createScopeVariable(node, node.id);\n    }\n\n    const typeParamScope = this._nestTypeParamScope(node);\n    if (typeParamScope) {\n      this.close(node);\n    }\n  }\n\n  _createScopeVariable(node: any, name: any) {\n    this.currentScope().variableScope.__define(\n      name,\n      new Definition(\"Variable\", name, node, null, null, null),\n    );\n  }\n\n  _nestTypeParamScope(node: any) {\n    if (!node.typeParameters) {\n      return null;\n    }\n\n    const parentScope = this.scopeManager.__currentScope;\n    const scope = new Scope(\n      this.scopeManager,\n      \"type-parameters\",\n      parentScope,\n      node,\n      false,\n    );\n\n    this.scopeManager.__nestScope(scope);\n    for (let j = 0; j < node.typeParameters.params.length; j++) {\n      const name = node.typeParameters.params[j];\n      scope.__define(name, new Definition(\"TypeParameter\", name, name));\n      if (name.typeAnnotation) {\n        this._checkIdentifierOrVisit(name);\n      }\n    }\n    scope.__define = parentScope.__define.bind(parentScope);\n\n    return scope;\n  }\n\n  _visitTypeAnnotation(node: any) {\n    if (!node) {\n      return;\n    }\n    if (Array.isArray(node)) {\n      node.forEach(this._visitTypeAnnotation, this);\n      return;\n    }\n\n    // get property to check (params, id, etc...)\n    const visitorValues = getVisitorValues(node.type, this.#client);\n    if (!visitorValues) {\n      return;\n    }\n\n    // can have multiple properties\n    for (let i = 0; i < visitorValues.length; i++) {\n      const visitorValue = visitorValues[i];\n      const propertyType = (propertyTypes as Record<string, any>)[visitorValue];\n      const nodeProperty = node[visitorValue];\n      // check if property or type is defined\n      if (propertyType == null || nodeProperty == null) {\n        continue;\n      }\n      if (propertyType.type === \"loop\") {\n        for (let j = 0; j < nodeProperty.length; j++) {\n          if (Array.isArray(propertyType.values)) {\n            for (let k = 0; k < propertyType.values.length; k++) {\n              const loopPropertyNode = nodeProperty[j][propertyType.values[k]];\n              if (loopPropertyNode) {\n                this._checkIdentifierOrVisit(loopPropertyNode);\n              }\n            }\n          } else {\n            this._checkIdentifierOrVisit(nodeProperty[j]);\n          }\n        }\n      } else if (propertyType.type === \"single\") {\n        this._checkIdentifierOrVisit(nodeProperty);\n      } else if (propertyType.type === \"typeAnnotation\") {\n        this._visitTypeAnnotation(node.typeAnnotation);\n      } else if (propertyType.type === \"typeParameters\") {\n        for (let l = 0; l < node.typeParameters.params.length; l++) {\n          this._checkIdentifierOrVisit(node.typeParameters.params[l]);\n        }\n      } else if (propertyType.type === \"id\") {\n        if (node.id.type === \"Identifier\") {\n          this._checkIdentifierOrVisit(node.id);\n        } else {\n          this._visitTypeAnnotation(node.id);\n        }\n      }\n    }\n  }\n\n  _checkIdentifierOrVisit(node: any) {\n    if (node?.typeAnnotation) {\n      this._visitTypeAnnotation(node.typeAnnotation);\n    } else if (node?.type === \"Identifier\") {\n      this.visit(node);\n    } else {\n      this._visitTypeAnnotation(node);\n    }\n  }\n\n  _visitArray(nodeList: any[]) {\n    if (nodeList) {\n      for (const node of nodeList) {\n        this.visit(node);\n      }\n    }\n  }\n}\n\nexport = function analyzeScope(ast: any, parserOptions: any, client: Client) {\n  const options = {\n    ignoreEval: true,\n    optimistic: false,\n    directive: false,\n    nodejsScope:\n      ast.sourceType === \"script\" &&\n      parserOptions.ecmaFeatures?.globalReturn === true,\n    impliedStrict: false,\n    sourceType: ast.sourceType,\n    ecmaVersion: parserOptions.ecmaVersion,\n    fallback,\n    childVisitorKeys: client.getVisitorKeys(),\n  };\n\n  const scopeManager = new ScopeManager(options);\n  const referencer = new Referencer(options, scopeManager, client);\n\n  referencer.visit(ast);\n\n  return scopeManager as any;\n};\n"], "mappings": ";;;;;;;AAEA,MAAM;EACJA,UAAU;EACVC,cAAc,EAAEC,sBAAsB;EACtCC,UAAU,EAAEC,kBAAkB;EAC9BC,KAAK;EACLC;AACF,CAAC,GAGKC,OAAO,CAAC,0CAA0C,CACxB;AAChC,MAAM;EAAEC,OAAO,EAAEC;AAAS,CAAC,GAAGF,OAAO,CAAC,qBAAqB,CAAC;AAE5D,IAAIG,cAAwC;AAC5C,SAASC,gBAAgBA,CAACC,QAAgB,EAAEC,MAAc,EAAE;EAC1D,IAAIH,cAAc,EAAE,OAAOA,cAAc,CAACE,QAAQ,CAAC;EAEnD,MAAM;IAAEE,uBAAuB;IAAEC;EAAa,CAAC,GAAGF,MAAM,CAACG,YAAY,CAAC,CAAC;EAEvE,MAAMC,oBAAoB,GAAG,IAAIC,GAAG,CAClCJ,uBAAuB,CAACK,MAAM,CAAC,CAC7B,cAAc,EACd,kBAAkB,EAClB,iBAAiB,EACjB,qBAAqB,EACrB,oBAAoB,EACpB,YAAY,EACZ,eAAe,EACf,aAAa,CACd,CACH,CAAC;EAEDT,cAAc,GAAG,CAAAU,MAAA,CAAAC,OAAA,KAAAC,CAAA,IAAAF,MAAA,CAAAG,IAAA,CAAAD,CAAA,EAAAE,GAAA,CAAAC,CAAA,KAAAA,CAAA,EAAAH,CAAA,CAAAG,CAAA,MAAeV,YAAY,CAAC,CAACW,MAAM,CAAC,CAACC,GAAG,EAAE,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;IAC1E,IAAI,CAACZ,oBAAoB,CAACa,GAAG,CAACD,KAAK,CAAC,EAAE;MAEpCF,GAAG,CAACC,GAAG,CAAC,GAAGC,KAAK;IAClB;IACA,OAAOF,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EAEN,OAAOjB,cAAc,CAACE,QAAQ,CAAC;AACjC;AAEA,MAAMmB,aAAa,GAAG;EAEpBC,cAAc,EAAE;IAAEC,IAAI,EAAE,MAAM;IAAEC,MAAM,EAAE,CAAC,OAAO;EAAE,CAAC;EACnDC,QAAQ,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,MAAM,EAAE,CAAC,KAAK,EAAE,OAAO;EAAE,CAAC;EACpDE,UAAU,EAAE;IAAEH,IAAI,EAAE,MAAM;IAAEC,MAAM,EAAE,CAAC,UAAU,EAAE,OAAO;EAAE,CAAC;EAC3DG,KAAK,EAAE;IAAEJ,IAAI,EAAE;EAAO,CAAC;EACvBK,MAAM,EAAE;IAAEL,IAAI,EAAE;EAAO,CAAC;EAExBM,QAAQ,EAAE;IAAEN,IAAI,EAAE;EAAS,CAAC;EAC5BO,WAAW,EAAE;IAAEP,IAAI,EAAE;EAAS,CAAC;EAC/BQ,aAAa,EAAE;IAAER,IAAI,EAAE;EAAS,CAAC;EACjCS,IAAI,EAAE;IAAET,IAAI,EAAE;EAAS,CAAC;EACxBU,UAAU,EAAE;IAAEV,IAAI,EAAE;EAAS,CAAC;EAE9BW,cAAc,EAAE;IAAEX,IAAI,EAAE;EAAiB,CAAC;EAC1CY,cAAc,EAAE;IAAEZ,IAAI,EAAE;EAAiB,CAAC;EAC1Ca,EAAE,EAAE;IAAEb,IAAI,EAAE;EAAK;AACnB,CAAC;AAED,MAAMhC,cAAc,SAASC,sBAAsB,CAAC;EAClD6C,YAAYA,CAACC,IAAS,EAAE;IACtBA,IAAI,CAACC,QAAQ,CAACC,OAAO,CAAC,IAAI,CAACC,KAAK,EAAE,IAAI,CAAC;EACzC;EAEAC,aAAaA,CAACJ,IAAS,EAAE;IACvBA,IAAI,CAACZ,UAAU,CAACc,OAAO,CAAC,IAAI,CAACC,KAAK,EAAE,IAAI,CAAC;EAC3C;AACF;AAAC,IAAAE,OAAA,OAAAC,OAAA;AAED,MAAMnD,UAAU,SAASC,kBAAkB,CAAC;EAG1CmD,WAAWA,CAACC,OAAY,EAAEC,YAAiB,EAAE5C,MAAc,EAAE;IAC3D,KAAK,CAAC2C,OAAO,EAAEC,YAAY,CAAC;IAH9BC,0BAAA,OAAAL,OAAO;IAILM,qBAAA,CAAKN,OAAO,EAAZ,IAAI,EAAWxC,MAAJ,CAAC;EACd;EAGA+C,YAAYA,CAACZ,IAAS,EAAEQ,OAAY,EAAEK,QAAa,EAAE;IACnD,IAAI,CAACb,IAAI,EAAE;MACT;IACF;IAGA,IAAI,CAACc,uBAAuB,CAACd,IAAI,CAACJ,cAAc,CAAC;IACjD,IAAII,IAAI,CAACf,IAAI,KAAK,mBAAmB,EAAE;MACrC,IAAI,CAAC6B,uBAAuB,CAACd,IAAI,CAACe,IAAI,CAACnB,cAAc,CAAC;IACxD;IAGA,IAAI,OAAOY,OAAO,KAAK,UAAU,EAAE;MACjCK,QAAQ,GAAGL,OAAO;MAClBA,OAAO,GAAG;QAAEQ,qBAAqB,EAAE;MAAM,CAAC;IAC5C;IAEA,MAAMC,OAAO,GAAG,IAAIhE,cAAc,CAAC,IAAI,CAACuD,OAAO,EAAER,IAAI,EAAEa,QAAQ,CAAC;IAChEI,OAAO,CAACd,KAAK,CAACH,IAAI,CAAC;IAGnB,IAAIQ,OAAO,CAACQ,qBAAqB,EAAE;MACjCC,OAAO,CAACC,cAAc,CAAChB,OAAO,CAAC,IAAI,CAACC,KAAK,EAAE,IAAI,CAAC;IAClD;EACF;EAGAgB,UAAUA,CAACnB,IAAS,EAAE;IAAA,IAAAoB,IAAA;IAEpB,IAAI,CAACC,WAAW,CAACrB,IAAI,CAACsB,UAAU,CAAC;IAGjC,MAAMC,cAAc,GAAG,IAAI,CAACC,mBAAmB,CAACxB,IAAI,CAAC;IAGrD,IAAI,CAACyB,oBAAoB,CAACzB,IAAI,CAAC0B,UAAU,CAAC;IAC1C,IAAI,CAACD,oBAAoB,EAAAL,IAAA,GAKnBpB,IAAI,CAAC2B,mBAAmB,qBAJ5BP,IAAA,CAKG9B,MACL,CAAC;IAGD,KAAK,CAAC6B,UAAU,CAACnB,IAAI,CAAC;IAGtB,IAAIuB,cAAc,EAAE;MAClB,IAAI,CAACK,KAAK,CAAC5B,IAAI,CAAC;IAClB;EACF;EAGA6B,aAAaA,CAAC7B,IAAS,EAAE;IACvB,MAAMuB,cAAc,GAAG,IAAI,CAACC,mBAAmB,CAACxB,IAAI,CAAC;IAGrD,IAAI,CAACc,uBAAuB,CAACd,IAAI,CAACL,UAAU,CAAC;IAG7C,KAAK,CAACkC,aAAa,CAAC7B,IAAI,CAAC;IAGzB,IAAIuB,cAAc,EAAE;MAClB,IAAI,CAACK,KAAK,CAAC5B,IAAI,CAAC;IAClB;EACF;EAGA8B,aAAaA,CAAC9B,IAAS,EAAE;IAAA,IAAA+B,WAAA;IACvB,IAAI,EAAAA,WAAA,GAAA/B,IAAI,CAACnB,KAAK,qBAAVkD,WAAA,CAAY9C,IAAI,MAAK,oBAAoB,EAAE;MAC7C,IAAI,CAACwC,oBAAoB,CAACzB,IAAI,CAACnB,KAAK,CAAC;IACvC;IACA,IAAI,CAACwC,WAAW,CAACrB,IAAI,CAACsB,UAAU,CAAC;IACjC,KAAK,CAACQ,aAAa,CAAC9B,IAAI,CAAC;EAC3B;EAEAgC,oBAAoBA,CAAChC,IAAS,EAAE;IAC9B,IAAI,CAACiC,oBAAoB,CAACjC,IAAI,EAAEA,IAAI,CAACF,EAAE,CAAC;IAExC,MAAMyB,cAAc,GAAG,IAAI,CAACC,mBAAmB,CAACxB,IAAI,CAAC;IAGrD,IAAI,CAACqB,WAAW,CAACrB,IAAI,CAACkC,OAAO,CAAC;IAC9B,IAAI,CAAC/B,KAAK,CAACH,IAAI,CAACmC,IAAI,CAAC;IAErB,IAAIZ,cAAc,EAAE;MAClB,IAAI,CAACK,KAAK,CAAC5B,IAAI,CAAC;IAClB;EACF;EAEAoC,SAASA,CAACpC,IAAS,EAAE;IACnB,IAAI,CAACiC,oBAAoB,CAACjC,IAAI,EAAEA,IAAI,CAACF,EAAE,CAAC;IAExC,MAAMyB,cAAc,GAAG,IAAI,CAACC,mBAAmB,CAACxB,IAAI,CAAC;IAErD,IAAI,CAACG,KAAK,CAACH,IAAI,CAACqC,KAAK,CAAC;IAEtB,IAAId,cAAc,EAAE;MAClB,IAAI,CAACK,KAAK,CAAC5B,IAAI,CAAC;IAClB;EACF;EAEAsC,aAAaA,CAACtC,IAAS,EAAE;IACvB,IAAI,CAACuC,mBAAmB,CAACvC,IAAI,CAAC;EAChC;EAEAwC,oBAAoBA,CAACxC,IAAS,EAAE;IAC9B,IAAI,CAACuC,mBAAmB,CAACvC,IAAI,CAAC;EAChC;EAEAyC,gBAAgBA,CAACzC,IAAS,EAAE;IAC1B,IAAI,CAACuC,mBAAmB,CAACvC,IAAI,CAAC;EAChC;EAEA0C,qBAAqBA,CAAC1C,IAAS,EAAE;IAC/B,IAAI,CAACuC,mBAAmB,CAACvC,IAAI,CAAC;EAChC;EAEA2C,kBAAkBA,CAAC3C,IAAS,EAAE;IAC5B,IAAI,CAACuC,mBAAmB,CAACvC,IAAI,CAAC;EAChC;EAGA4C,kBAAkBA,CAAC5C,IAAS,EAAE;IAC5B,KAAK,CAAC6C,gBAAgB,CAAC7C,IAAI,CAAC;EAC9B;EAEA8C,aAAaA,CAAC9C,IAAS,EAAE;IACvB,IAAI,CAAC+C,cAAc,CAAC/C,IAAI,CAAC;EAC3B;EAEAgD,eAAeA,CAAChD,IAAS,EAAE;IACzB,IAAI,CAAC+C,cAAc,CAAC/C,IAAI,CAAC;EAC3B;EAEAiD,eAAeA,CAACjD,IAAS,EAAE;IACzB,IAAI,CAAC+C,cAAc,CAAC/C,IAAI,CAAC;EAC3B;EAEAkD,YAAYA,CAAClD,IAAS,EAAE;IACtB,IAAI,CAAC+C,cAAc,CAAC/C,IAAI,CAAC;EAC3B;EAGAmD,wBAAwBA,CAACnD,IAAS,EAAE;IAClC,KAAK,CAACoD,gBAAgB,CAACpD,IAAI,CAAC;EAC9B;EAEAuC,mBAAmBA,CAACvC,IAAS,EAAE;IAC7B,MAAM;MAAEqD,QAAQ;MAAEzE,GAAG;MAAEgB,cAAc;MAAE0B,UAAU;MAAEzC;IAAM,CAAC,GAAGmB,IAAI;IAEjE,IAAI,CAACqB,WAAW,CAACC,UAAU,CAAC;IAC5B,IAAI+B,QAAQ,EAAE,IAAI,CAAClD,KAAK,CAACvB,GAAG,CAAC;IAC7B,IAAI,CAAC6C,oBAAoB,CAAC7B,cAAc,CAAC;IAEzC,IAAIf,KAAK,EAAE;MACT,IAAI,IAAI,CAAC4B,YAAY,CAAC6C,gCAAgC,EAAE;QACtD,IAAI,CAAC7C,YAAY,CAAC6C,gCAAgC,CAACzE,KAAK,CAAC;MAC3D,CAAC,MAAM;QAGL,IAAI,CAAC4B,YAAY,CAAC8C,WAAW,CAC3B,IAAIlG,KAAK,CACP,IAAI,CAACoD,YAAY,EACjB,UAAU,EACV,IAAI,CAACA,YAAY,CAAC+C,cAAc,EAChC3E,KAAK,EACL,IACF,CACF,CAAC;MACH;MACA,IAAI,CAACsB,KAAK,CAACtB,KAAK,CAAC;MACjB,IAAI,CAAC+C,KAAK,CAAC/C,KAAK,CAAC;IACnB;EACF;EAEAkE,cAAcA,CAAC/C,IAAS,EAAE;IACxB,IAAIA,IAAI,CAACF,EAAE,EAAE;MACX,IAAI,CAACmC,oBAAoB,CAACjC,IAAI,EAAEA,IAAI,CAACF,EAAE,CAAC;IAC1C;IAEA,MAAMyB,cAAc,GAAG,IAAI,CAACC,mBAAmB,CAACxB,IAAI,CAAC;IACrD,IAAIuB,cAAc,EAAE;MAClB,IAAI,CAACK,KAAK,CAAC5B,IAAI,CAAC;IAClB;EACF;EAEAiC,oBAAoBA,CAACjC,IAAS,EAAEyD,IAAS,EAAE;IACzC,IAAI,CAACC,YAAY,CAAC,CAAC,CAACC,aAAa,CAACC,QAAQ,CACxCH,IAAI,EACJ,IAAIzG,UAAU,CAAC,UAAU,EAAEyG,IAAI,EAAEzD,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CACzD,CAAC;EACH;EAEAwB,mBAAmBA,CAACxB,IAAS,EAAE;IAC7B,IAAI,CAACA,IAAI,CAACH,cAAc,EAAE;MACxB,OAAO,IAAI;IACb;IAEA,MAAMgE,WAAW,GAAG,IAAI,CAACpD,YAAY,CAAC+C,cAAc;IACpD,MAAMM,KAAK,GAAG,IAAIzG,KAAK,CACrB,IAAI,CAACoD,YAAY,EACjB,iBAAiB,EACjBoD,WAAW,EACX7D,IAAI,EACJ,KACF,CAAC;IAED,IAAI,CAACS,YAAY,CAAC8C,WAAW,CAACO,KAAK,CAAC;IACpC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/D,IAAI,CAACH,cAAc,CAACP,MAAM,CAAC0E,MAAM,EAAED,CAAC,EAAE,EAAE;MAC1D,MAAMN,IAAI,GAAGzD,IAAI,CAACH,cAAc,CAACP,MAAM,CAACyE,CAAC,CAAC;MAC1CD,KAAK,CAACF,QAAQ,CAACH,IAAI,EAAE,IAAIzG,UAAU,CAAC,eAAe,EAAEyG,IAAI,EAAEA,IAAI,CAAC,CAAC;MACjE,IAAIA,IAAI,CAAC7D,cAAc,EAAE;QACvB,IAAI,CAACkB,uBAAuB,CAAC2C,IAAI,CAAC;MACpC;IACF;IACAK,KAAK,CAACF,QAAQ,GAAGC,WAAW,CAACD,QAAQ,CAACK,IAAI,CAACJ,WAAW,CAAC;IAEvD,OAAOC,KAAK;EACd;EAEArC,oBAAoBA,CAACzB,IAAS,EAAE;IAC9B,IAAI,CAACA,IAAI,EAAE;MACT;IACF;IACA,IAAIkE,KAAK,CAACC,OAAO,CAACnE,IAAI,CAAC,EAAE;MACvBA,IAAI,CAACE,OAAO,CAAC,IAAI,CAACuB,oBAAoB,EAAE,IAAI,CAAC;MAC7C;IACF;IAGA,MAAM2C,aAAa,GAAGzG,gBAAgB,CAACqC,IAAI,CAACf,IAAI,EAAEoF,qBAAA,CAAKhE,OAAO,EAAZ,IAAW,CAAC,CAAC;IAC/D,IAAI,CAAC+D,aAAa,EAAE;MAClB;IACF;IAGA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,aAAa,CAACJ,MAAM,EAAEM,CAAC,EAAE,EAAE;MAC7C,MAAMC,YAAY,GAAGH,aAAa,CAACE,CAAC,CAAC;MACrC,MAAME,YAAY,GAAIzF,aAAa,CAAyBwF,YAAY,CAAC;MACzE,MAAME,YAAY,GAAGzE,IAAI,CAACuE,YAAY,CAAC;MAEvC,IAAIC,YAAY,IAAI,IAAI,IAAIC,YAAY,IAAI,IAAI,EAAE;QAChD;MACF;MACA,IAAID,YAAY,CAACvF,IAAI,KAAK,MAAM,EAAE;QAChC,KAAK,IAAI8E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,YAAY,CAACT,MAAM,EAAED,CAAC,EAAE,EAAE;UAC5C,IAAIG,KAAK,CAACC,OAAO,CAACK,YAAY,CAACtF,MAAM,CAAC,EAAE;YACtC,KAAK,IAAIT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+F,YAAY,CAACtF,MAAM,CAAC8E,MAAM,EAAEvF,CAAC,EAAE,EAAE;cACnD,MAAMiG,gBAAgB,GAAGD,YAAY,CAACV,CAAC,CAAC,CAACS,YAAY,CAACtF,MAAM,CAACT,CAAC,CAAC,CAAC;cAChE,IAAIiG,gBAAgB,EAAE;gBACpB,IAAI,CAAC5D,uBAAuB,CAAC4D,gBAAgB,CAAC;cAChD;YACF;UACF,CAAC,MAAM;YACL,IAAI,CAAC5D,uBAAuB,CAAC2D,YAAY,CAACV,CAAC,CAAC,CAAC;UAC/C;QACF;MACF,CAAC,MAAM,IAAIS,YAAY,CAACvF,IAAI,KAAK,QAAQ,EAAE;QACzC,IAAI,CAAC6B,uBAAuB,CAAC2D,YAAY,CAAC;MAC5C,CAAC,MAAM,IAAID,YAAY,CAACvF,IAAI,KAAK,gBAAgB,EAAE;QACjD,IAAI,CAACwC,oBAAoB,CAACzB,IAAI,CAACJ,cAAc,CAAC;MAChD,CAAC,MAAM,IAAI4E,YAAY,CAACvF,IAAI,KAAK,gBAAgB,EAAE;QACjD,KAAK,IAAI0F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3E,IAAI,CAACH,cAAc,CAACP,MAAM,CAAC0E,MAAM,EAAEW,CAAC,EAAE,EAAE;UAC1D,IAAI,CAAC7D,uBAAuB,CAACd,IAAI,CAACH,cAAc,CAACP,MAAM,CAACqF,CAAC,CAAC,CAAC;QAC7D;MACF,CAAC,MAAM,IAAIH,YAAY,CAACvF,IAAI,KAAK,IAAI,EAAE;QACrC,IAAIe,IAAI,CAACF,EAAE,CAACb,IAAI,KAAK,YAAY,EAAE;UACjC,IAAI,CAAC6B,uBAAuB,CAACd,IAAI,CAACF,EAAE,CAAC;QACvC,CAAC,MAAM;UACL,IAAI,CAAC2B,oBAAoB,CAACzB,IAAI,CAACF,EAAE,CAAC;QACpC;MACF;IACF;EACF;EAEAgB,uBAAuBA,CAACd,IAAS,EAAE;IACjC,IAAIA,IAAI,YAAJA,IAAI,CAAEJ,cAAc,EAAE;MACxB,IAAI,CAAC6B,oBAAoB,CAACzB,IAAI,CAACJ,cAAc,CAAC;IAChD,CAAC,MAAM,IAAI,CAAAI,IAAI,oBAAJA,IAAI,CAAEf,IAAI,MAAK,YAAY,EAAE;MACtC,IAAI,CAACkB,KAAK,CAACH,IAAI,CAAC;IAClB,CAAC,MAAM;MACL,IAAI,CAACyB,oBAAoB,CAACzB,IAAI,CAAC;IACjC;EACF;EAEAqB,WAAWA,CAACuD,QAAe,EAAE;IAC3B,IAAIA,QAAQ,EAAE;MACZ,KAAK,MAAM5E,IAAI,IAAI4E,QAAQ,EAAE;QAC3B,IAAI,CAACzE,KAAK,CAACH,IAAI,CAAC;MAClB;IACF;EACF;AACF;AAAC6E,MAAA,CAAAC,OAAA,GAEQ,SAASC,YAAYA,CAACC,GAAQ,EAAEC,aAAkB,EAAEpH,MAAc,EAAE;EAAA,IAAAqH,qBAAA;EAC3E,MAAM1E,OAAO,GAAG;IACd2E,UAAU,EAAE,IAAI;IAChBC,UAAU,EAAE,KAAK;IACjBC,SAAS,EAAE,KAAK;IAChBC,WAAW,EACTN,GAAG,CAACO,UAAU,KAAK,QAAQ,IAC3B,EAAAL,qBAAA,GAAAD,aAAa,CAACO,YAAY,qBAA1BN,qBAAA,CAA4BO,YAAY,MAAK,IAAI;IACnDC,aAAa,EAAE,KAAK;IACpBH,UAAU,EAAEP,GAAG,CAACO,UAAU;IAC1BI,WAAW,EAAEV,aAAa,CAACU,WAAW;IACtClI,QAAQ;IACRmI,gBAAgB,EAAE/H,MAAM,CAACgI,cAAc,CAAC;EAC1C,CAAC;EAED,MAAMpF,YAAY,GAAG,IAAInD,YAAY,CAACkD,OAAO,CAAC;EAC9C,MAAMsF,UAAU,GAAG,IAAI3I,UAAU,CAACqD,OAAO,EAAEC,YAAY,EAAE5C,MAAM,CAAC;EAEhEiI,UAAU,CAAC3F,KAAK,CAAC6E,GAAG,CAAC;EAErB,OAAOvE,YAAY;AACrB,CAAC", "ignoreList": []}