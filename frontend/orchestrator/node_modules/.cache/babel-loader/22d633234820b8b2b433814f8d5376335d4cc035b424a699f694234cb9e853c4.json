{"ast": null, "code": "import _objectSpread from\"/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useEffect,useState}from'react';import{useAuth}from'../auth/AuthContext';import{ShieldCheckIcon,UserGroupIcon,KeyIcon,PlusIcon,PencilIcon,TrashIcon,CheckCircleIcon,XCircleIcon,MagnifyingGlassIcon,ExclamationTriangleIcon}from'@heroicons/react/24/outline';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const RBACAdmin=()=>{const{user}=useAuth();const[activeTab,setActiveTab]=useState('roles');const[roles,setRoles]=useState([]);const[permissions,setPermissions]=useState([]);const[policies,setPolicies]=useState([]);const[loading,setLoading]=useState(true);const[searchTerm,setSearchTerm]=useState('');const[showCreateModal,setShowCreateModal]=useState(false);const[showEditModal,setShowEditModal]=useState(false);const[showDeleteModal,setShowDeleteModal]=useState(false);const[selectedItem,setSelectedItem]=useState(null);useEffect(()=>{fetchData();},[activeTab]);const fetchData=async()=>{setLoading(true);try{switch(activeTab){case'roles':await fetchRoles();break;case'permissions':await fetchPermissions();break;case'policies':await fetchPolicies();break;}}catch(error){console.error(\"Failed to fetch \".concat(activeTab,\":\"),error);}finally{setLoading(false);}};const fetchRoles=async()=>{try{const response=await fetch('/v1/rbac/roles',{credentials:'include'});if(response.ok){const data=await response.json();setRoles(data.roles||[]);}}catch(error){console.error('Failed to fetch roles:',error);}};const fetchPermissions=async()=>{try{const response=await fetch('/v1/rbac/permissions',{credentials:'include'});if(response.ok){const data=await response.json();setPermissions(data.permissions||[]);}}catch(error){console.error('Failed to fetch permissions:',error);}};const fetchPolicies=async()=>{try{const response=await fetch('/v1/rbac/policies',{credentials:'include'});if(response.ok){const data=await response.json();setPolicies(data.policies||[]);}}catch(error){console.error('Failed to fetch policies:',error);}};const handleCreate=async data=>{try{const response=await fetch(\"/v1/rbac/\".concat(activeTab),{method:'POST',headers:{'Content-Type':'application/json'},credentials:'include',body:JSON.stringify(data)});if(response.ok){setShowCreateModal(false);fetchData();alert(\"\".concat(activeTab.slice(0,-1),\" created successfully!\"));}else{const errorData=await response.json();alert(\"Failed to create \".concat(activeTab.slice(0,-1),\": \").concat(errorData.error||'Unknown error'));}}catch(error){console.error(\"Failed to create \".concat(activeTab.slice(0,-1),\":\"),error);alert(\"Failed to create \".concat(activeTab.slice(0,-1),\". Please check the console for details.\"));}};const handleUpdate=async(id,data)=>{try{const response=await fetch(\"/v1/rbac/\".concat(activeTab,\"/\").concat(id),{method:'PUT',headers:{'Content-Type':'application/json'},credentials:'include',body:JSON.stringify(data)});if(response.ok){setShowEditModal(false);setSelectedItem(null);fetchData();}}catch(error){console.error(\"Failed to update \".concat(activeTab.slice(0,-1),\":\"),error);}};const handleDelete=async()=>{if(!selectedItem)return;try{const response=await fetch(\"/v1/rbac/\".concat(activeTab,\"/\").concat(selectedItem.id),{method:'DELETE',credentials:'include'});if(response.ok){setShowDeleteModal(false);setSelectedItem(null);fetchData();}}catch(error){console.error(\"Failed to delete \".concat(activeTab.slice(0,-1),\":\"),error);}};const getFilteredData=()=>{let data=[];switch(activeTab){case'roles':data=roles;break;case'permissions':data=permissions;break;case'policies':data=policies;break;}return data.filter(item=>item.name.toLowerCase().includes(searchTerm.toLowerCase())||item.description.toLowerCase().includes(searchTerm.toLowerCase()));};const tabs=[{id:'roles',name:'Roles',icon:UserGroupIcon},{id:'permissions',name:'Permissions',icon:KeyIcon},{id:'policies',name:'Policies',icon:ShieldCheckIcon}];if(loading){return/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center h-64\",children:/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500\"})});}return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white shadow rounded-lg p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-900\",children:\"RBAC Management\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Manage roles, permissions, and access policies\"})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setShowCreateModal(true),className:\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700\",children:[/*#__PURE__*/_jsx(PlusIcon,{className:\"h-4 w-4 mr-2\"}),\"Create \",activeTab.slice(0,-1)]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white shadow rounded-lg\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"border-b border-gray-200\",children:/*#__PURE__*/_jsx(\"nav\",{className:\"-mb-px flex space-x-8 px-6\",children:tabs.map(tab=>/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setActiveTab(tab.id),className:\"py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 \".concat(activeTab===tab.id?'border-blue-500 text-blue-600':'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),children:[/*#__PURE__*/_jsx(tab.icon,{className:\"h-5 w-5\"}),/*#__PURE__*/_jsx(\"span\",{children:tab.name})]},tab.id))})}),/*#__PURE__*/_jsx(\"div\",{className:\"p-6 border-b border-gray-200\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(MagnifyingGlassIcon,{className:\"h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:\"Search \".concat(activeTab,\"...\"),value:searchTerm,onChange:e=>setSearchTerm(e.target.value),className:\"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-6\",children:[activeTab==='roles'&&/*#__PURE__*/_jsx(RolesTable,{roles:getFilteredData(),onEdit:role=>{setSelectedItem(role);setShowEditModal(true);},onDelete:role=>{setSelectedItem(role);setShowDeleteModal(true);}}),activeTab==='permissions'&&/*#__PURE__*/_jsx(PermissionsTable,{permissions:getFilteredData(),onEdit:permission=>{setSelectedItem(permission);setShowEditModal(true);},onDelete:permission=>{setSelectedItem(permission);setShowDeleteModal(true);}}),activeTab==='policies'&&/*#__PURE__*/_jsx(PoliciesTable,{policies:getFilteredData(),onEdit:policy=>{setSelectedItem(policy);setShowEditModal(true);},onDelete:policy=>{setSelectedItem(policy);setShowDeleteModal(true);}})]})]}),showCreateModal&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",children:/*#__PURE__*/_jsx(\"div\",{className:\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"mt-3\",children:[/*#__PURE__*/_jsxs(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-4\",children:[\"Create \",activeTab.slice(0,-1)]}),/*#__PURE__*/_jsx(CreateForm,{type:activeTab,onSubmit:handleCreate,onCancel:()=>setShowCreateModal(false)})]})})}),showDeleteModal&&selectedItem&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",children:/*#__PURE__*/_jsx(\"div\",{className:\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"mt-3 text-center\",children:[/*#__PURE__*/_jsx(ExclamationTriangleIcon,{className:\"mx-auto h-12 w-12 text-red-600\"}),/*#__PURE__*/_jsxs(\"h3\",{className:\"text-lg font-medium text-gray-900 mt-2\",children:[\"Delete \",activeTab.slice(0,-1)]}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-gray-500 mt-2\",children:[\"Are you sure you want to delete \\\"\",selectedItem.name,\"\\\"? This action cannot be undone.\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-center space-x-3 mt-4\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setShowDeleteModal(false),className:\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\",children:\"Cancel\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handleDelete,className:\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700\",children:\"Delete\"})]})]})})})]});};// Component for displaying roles table\nconst RolesTable=_ref=>{let{roles,onEdit,onDelete}=_ref;return/*#__PURE__*/_jsx(\"div\",{className:\"overflow-x-auto\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"min-w-full divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(\"thead\",{className:\"bg-gray-50\",children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Role\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Permissions\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Users\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Updated\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{className:\"bg-white divide-y divide-gray-200\",children:roles.map(role=>/*#__PURE__*/_jsxs(\"tr\",{className:\"hover:bg-gray-50\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-sm font-medium text-gray-900\",children:role.name}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-500\",children:role.description})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsxs(\"span\",{className:\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",children:[role.permissions.length,\" permissions\"]})}),/*#__PURE__*/_jsxs(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",children:[role.user_count||0,\" users\"]}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",children:new Date(role.updated_at).toLocaleDateString()}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-end space-x-2\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>onEdit(role),className:\"text-blue-600 hover:text-blue-900\",children:/*#__PURE__*/_jsx(PencilIcon,{className:\"h-4 w-4\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>onDelete(role),className:\"text-red-600 hover:text-red-900\",children:/*#__PURE__*/_jsx(TrashIcon,{className:\"h-4 w-4\"})})]})})]},role.id))})]})});};// Component for displaying permissions table\nconst PermissionsTable=_ref2=>{let{permissions,onEdit,onDelete}=_ref2;return/*#__PURE__*/_jsx(\"div\",{className:\"overflow-x-auto\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"min-w-full divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(\"thead\",{className:\"bg-gray-50\",children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Permission\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Resource\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Action\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Category\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{className:\"bg-white divide-y divide-gray-200\",children:permissions.map(permission=>/*#__PURE__*/_jsxs(\"tr\",{className:\"hover:bg-gray-50\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-sm font-medium text-gray-900\",children:permission.name}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-500\",children:permission.description})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsx(\"span\",{className:\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\",children:permission.resource})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsx(\"span\",{className:\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800\",children:permission.action})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",children:permission.category}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-end space-x-2\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>onEdit(permission),className:\"text-blue-600 hover:text-blue-900\",children:/*#__PURE__*/_jsx(PencilIcon,{className:\"h-4 w-4\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>onDelete(permission),className:\"text-red-600 hover:text-red-900\",children:/*#__PURE__*/_jsx(TrashIcon,{className:\"h-4 w-4\"})})]})})]},permission.id))})]})});};// Component for displaying policies table\nconst PoliciesTable=_ref3=>{let{policies,onEdit,onDelete}=_ref3;return/*#__PURE__*/_jsx(\"div\",{className:\"overflow-x-auto\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"min-w-full divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(\"thead\",{className:\"bg-gray-50\",children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Policy\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Effect\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Status\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Resources\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{className:\"bg-white divide-y divide-gray-200\",children:policies.map(policy=>/*#__PURE__*/_jsxs(\"tr\",{className:\"hover:bg-gray-50\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-sm font-medium text-gray-900\",children:policy.name}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-500\",children:policy.description})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsx(\"span\",{className:\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(policy.effect==='allow'?'bg-green-100 text-green-800':'bg-red-100 text-red-800'),children:policy.effect})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsxs(\"span\",{className:\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(policy.enabled?'bg-green-100 text-green-800':'bg-gray-100 text-gray-800'),children:[policy.enabled?/*#__PURE__*/_jsx(CheckCircleIcon,{className:\"h-3 w-3 mr-1\"}):/*#__PURE__*/_jsx(XCircleIcon,{className:\"h-3 w-3 mr-1\"}),policy.enabled?'Enabled':'Disabled']})}),/*#__PURE__*/_jsxs(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",children:[policy.resources.length,\" resources\"]}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-end space-x-2\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>onEdit(policy),className:\"text-blue-600 hover:text-blue-900\",children:/*#__PURE__*/_jsx(PencilIcon,{className:\"h-4 w-4\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>onDelete(policy),className:\"text-red-600 hover:text-red-900\",children:/*#__PURE__*/_jsx(TrashIcon,{className:\"h-4 w-4\"})})]})})]},policy.id))})]})});};// Create Form Component\nconst CreateForm=_ref4=>{let{type,onSubmit,onCancel}=_ref4;const[formData,setFormData]=useState({});const handleSubmit=e=>{e.preventDefault();onSubmit(formData);};const renderForm=()=>{switch(type){case'roles':return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Role ID\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",required:true,value:formData.id||'',onChange:e=>setFormData(_objectSpread(_objectSpread({},formData),{},{id:e.target.value})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500\",placeholder:\"role_name\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Name\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",required:true,value:formData.name||'',onChange:e=>setFormData(_objectSpread(_objectSpread({},formData),{},{name:e.target.value})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500\",placeholder:\"Role Name\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Description\"}),/*#__PURE__*/_jsx(\"textarea\",{required:true,value:formData.description||'',onChange:e=>setFormData(_objectSpread(_objectSpread({},formData),{},{description:e.target.value})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500\",rows:3,placeholder:\"Describe this role\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Permissions\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:formData.permissions||'',onChange:e=>setFormData(_objectSpread(_objectSpread({},formData),{},{permissions:e.target.value.split(',').map(p=>p.trim())})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500\",placeholder:\"permission1, permission2, permission3\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-500 mt-1\",children:\"Comma-separated list of permissions\"})]})]});case'permissions':return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Permission ID\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",required:true,value:formData.id||'',onChange:e=>setFormData(_objectSpread(_objectSpread({},formData),{},{id:e.target.value})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500\",placeholder:\"resource:action\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Name\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",required:true,value:formData.name||'',onChange:e=>setFormData(_objectSpread(_objectSpread({},formData),{},{name:e.target.value})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500\",placeholder:\"Permission Name\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Description\"}),/*#__PURE__*/_jsx(\"textarea\",{required:true,value:formData.description||'',onChange:e=>setFormData(_objectSpread(_objectSpread({},formData),{},{description:e.target.value})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500\",rows:3,placeholder:\"Describe this permission\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Resource\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",required:true,value:formData.resource||'',onChange:e=>setFormData(_objectSpread(_objectSpread({},formData),{},{resource:e.target.value})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500\",placeholder:\"workflows\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Action\"}),/*#__PURE__*/_jsxs(\"select\",{required:true,value:formData.action||'',onChange:e=>setFormData(_objectSpread(_objectSpread({},formData),{},{action:e.target.value})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select action\"}),/*#__PURE__*/_jsx(\"option\",{value:\"read\",children:\"Read\"}),/*#__PURE__*/_jsx(\"option\",{value:\"write\",children:\"Write\"}),/*#__PURE__*/_jsx(\"option\",{value:\"execute\",children:\"Execute\"}),/*#__PURE__*/_jsx(\"option\",{value:\"delete\",children:\"Delete\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Category\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",required:true,value:formData.category||'',onChange:e=>setFormData(_objectSpread(_objectSpread({},formData),{},{category:e.target.value})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500\",placeholder:\"Workflows\"})]})]});case'policies':return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Policy ID\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",required:true,value:formData.id||'',onChange:e=>setFormData(_objectSpread(_objectSpread({},formData),{},{id:e.target.value})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500\",placeholder:\"policy-name\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Name\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",required:true,value:formData.name||'',onChange:e=>setFormData(_objectSpread(_objectSpread({},formData),{},{name:e.target.value})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500\",placeholder:\"Policy Name\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Description\"}),/*#__PURE__*/_jsx(\"textarea\",{required:true,value:formData.description||'',onChange:e=>setFormData(_objectSpread(_objectSpread({},formData),{},{description:e.target.value})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500\",rows:3,placeholder:\"Describe this policy\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Effect\"}),/*#__PURE__*/_jsxs(\"select\",{required:true,value:formData.effect||'',onChange:e=>setFormData(_objectSpread(_objectSpread({},formData),{},{effect:e.target.value})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select effect\"}),/*#__PURE__*/_jsx(\"option\",{value:\"allow\",children:\"Allow\"}),/*#__PURE__*/_jsx(\"option\",{value:\"deny\",children:\"Deny\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Subjects\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:formData.subjects||'',onChange:e=>setFormData(_objectSpread(_objectSpread({},formData),{},{subjects:e.target.value.split(',').map(s=>s.trim())})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500\",placeholder:\"role:admin, user:john\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-500 mt-1\",children:\"Comma-separated list of subjects\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Resources\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:formData.resources||'',onChange:e=>setFormData(_objectSpread(_objectSpread({},formData),{},{resources:e.target.value.split(',').map(r=>r.trim())})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500\",placeholder:\"workflows, discovery\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-500 mt-1\",children:\"Comma-separated list of resources\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Actions\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:formData.actions||'',onChange:e=>setFormData(_objectSpread(_objectSpread({},formData),{},{actions:e.target.value.split(',').map(a=>a.trim())})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500\",placeholder:\"read, write, execute\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-500 mt-1\",children:\"Comma-separated list of actions\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:formData.enabled||false,onChange:e=>setFormData(_objectSpread(_objectSpread({},formData),{},{enabled:e.target.checked})),className:\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"}),/*#__PURE__*/_jsx(\"label\",{className:\"ml-2 block text-sm text-gray-900\",children:\"Enable policy\"})]})]});default:return null;}};return/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,className:\"space-y-4\",children:[renderForm(),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-end space-x-3 pt-4\",children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:onCancel,className:\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\",children:\"Cancel\"}),/*#__PURE__*/_jsxs(\"button\",{type:\"submit\",className:\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700\",children:[\"Create \",type.slice(0,-1)]})]})]});};export default RBACAdmin;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useAuth", "ShieldCheckIcon", "UserGroupIcon", "KeyIcon", "PlusIcon", "PencilIcon", "TrashIcon", "CheckCircleIcon", "XCircleIcon", "MagnifyingGlassIcon", "ExclamationTriangleIcon", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "RBACAdmin", "user", "activeTab", "setActiveTab", "roles", "setRoles", "permissions", "setPermissions", "policies", "setPolicies", "loading", "setLoading", "searchTerm", "setSearchTerm", "showCreateModal", "setShowCreateModal", "showEditModal", "setShowEditModal", "showDeleteModal", "setShowDeleteModal", "selectedItem", "setSelectedItem", "fetchData", "fetchRoles", "fetchPermissions", "fetchPolicies", "error", "console", "concat", "response", "fetch", "credentials", "ok", "data", "json", "handleCreate", "method", "headers", "body", "JSON", "stringify", "alert", "slice", "errorData", "handleUpdate", "id", "handleDelete", "getFilteredData", "filter", "item", "name", "toLowerCase", "includes", "description", "tabs", "icon", "className", "children", "onClick", "map", "tab", "type", "placeholder", "value", "onChange", "e", "target", "RolesTable", "onEdit", "role", "onDelete", "PermissionsTable", "permission", "PoliciesTable", "policy", "CreateForm", "onSubmit", "onCancel", "_ref", "length", "user_count", "Date", "updated_at", "toLocaleDateString", "_ref2", "resource", "action", "category", "_ref3", "effect", "enabled", "resources", "_ref4", "formData", "setFormData", "handleSubmit", "preventDefault", "renderForm", "required", "_objectSpread", "rows", "split", "p", "trim", "subjects", "s", "r", "actions", "a", "checked"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/RBACAdmin.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useAuth } from '../auth/AuthContext';\nimport {\n  ShieldCheckIcon,\n  UserGroupIcon,\n  KeyIcon,\n  PlusIcon,\n  PencilIcon,\n  TrashIcon,\n  EyeIcon,\n  CheckCircleIcon,\n  XCircleIcon,\n  MagnifyingGlassIcon,\n  ExclamationTriangleIcon,\n} from '@heroicons/react/24/outline';\n\ninterface Role {\n  id: string;\n  name: string;\n  description: string;\n  permissions: string[];\n  created_at: string;\n  updated_at: string;\n  user_count?: number;\n}\n\ninterface Permission {\n  id: string;\n  name: string;\n  description: string;\n  resource: string;\n  action: string;\n  category: string;\n}\n\ninterface PolicyRule {\n  id: string;\n  name: string;\n  description: string;\n  effect: 'allow' | 'deny';\n  subjects: string[];\n  resources: string[];\n  actions: string[];\n  conditions?: any;\n  enabled: boolean;\n}\n\nconst RBACAdmin: React.FC = () => {\n  const { user } = useAuth();\n  const [activeTab, setActiveTab] = useState<'roles' | 'permissions' | 'policies'>('roles');\n  const [roles, setRoles] = useState<Role[]>([]);\n  const [permissions, setPermissions] = useState<Permission[]>([]);\n  const [policies, setPolicies] = useState<PolicyRule[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showCreateModal, setShowCreateModal] = useState(false);\n  const [showEditModal, setShowEditModal] = useState(false);\n  const [showDeleteModal, setShowDeleteModal] = useState(false);\n  const [selectedItem, setSelectedItem] = useState<any>(null);\n\n  useEffect(() => {\n    fetchData();\n  }, [activeTab]);\n\n  const fetchData = async () => {\n    setLoading(true);\n    try {\n      switch (activeTab) {\n        case 'roles':\n          await fetchRoles();\n          break;\n        case 'permissions':\n          await fetchPermissions();\n          break;\n        case 'policies':\n          await fetchPolicies();\n          break;\n      }\n    } catch (error) {\n      console.error(`Failed to fetch ${activeTab}:`, error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchRoles = async () => {\n    try {\n      const response = await fetch('/v1/rbac/roles', {\n        credentials: 'include',\n      });\n      if (response.ok) {\n        const data = await response.json();\n        setRoles(data.roles || []);\n      }\n    } catch (error) {\n      console.error('Failed to fetch roles:', error);\n    }\n  };\n\n  const fetchPermissions = async () => {\n    try {\n      const response = await fetch('/v1/rbac/permissions', {\n        credentials: 'include',\n      });\n      if (response.ok) {\n        const data = await response.json();\n        setPermissions(data.permissions || []);\n      }\n    } catch (error) {\n      console.error('Failed to fetch permissions:', error);\n    }\n  };\n\n  const fetchPolicies = async () => {\n    try {\n      const response = await fetch('/v1/rbac/policies', {\n        credentials: 'include',\n      });\n      if (response.ok) {\n        const data = await response.json();\n        setPolicies(data.policies || []);\n      }\n    } catch (error) {\n      console.error('Failed to fetch policies:', error);\n    }\n  };\n\n  const handleCreate = async (data: any) => {\n    try {\n      const response = await fetch(`/v1/rbac/${activeTab}`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include',\n        body: JSON.stringify(data),\n      });\n\n      if (response.ok) {\n        setShowCreateModal(false);\n        fetchData();\n        alert(`${activeTab.slice(0, -1)} created successfully!`);\n      } else {\n        const errorData = await response.json();\n        alert(`Failed to create ${activeTab.slice(0, -1)}: ${errorData.error || 'Unknown error'}`);\n      }\n    } catch (error) {\n      console.error(`Failed to create ${activeTab.slice(0, -1)}:`, error);\n      alert(`Failed to create ${activeTab.slice(0, -1)}. Please check the console for details.`);\n    }\n  };\n\n  const handleUpdate = async (id: string, data: any) => {\n    try {\n      const response = await fetch(`/v1/rbac/${activeTab}/${id}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include',\n        body: JSON.stringify(data),\n      });\n\n      if (response.ok) {\n        setShowEditModal(false);\n        setSelectedItem(null);\n        fetchData();\n      }\n    } catch (error) {\n      console.error(`Failed to update ${activeTab.slice(0, -1)}:`, error);\n    }\n  };\n\n  const handleDelete = async () => {\n    if (!selectedItem) return;\n\n    try {\n      const response = await fetch(`/v1/rbac/${activeTab}/${selectedItem.id}`, {\n        method: 'DELETE',\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        setShowDeleteModal(false);\n        setSelectedItem(null);\n        fetchData();\n      }\n    } catch (error) {\n      console.error(`Failed to delete ${activeTab.slice(0, -1)}:`, error);\n    }\n  };\n\n  const getFilteredData = () => {\n    let data: any[] = [];\n    switch (activeTab) {\n      case 'roles':\n        data = roles;\n        break;\n      case 'permissions':\n        data = permissions;\n        break;\n      case 'policies':\n        data = policies;\n        break;\n    }\n\n    return data.filter(item =>\n      item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      item.description.toLowerCase().includes(searchTerm.toLowerCase())\n    );\n  };\n\n  const tabs = [\n    { id: 'roles', name: 'Roles', icon: UserGroupIcon },\n    { id: 'permissions', name: 'Permissions', icon: KeyIcon },\n    { id: 'policies', name: 'Policies', icon: ShieldCheckIcon },\n  ];\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">RBAC Management</h1>\n            <p className=\"text-gray-600\">Manage roles, permissions, and access policies</p>\n          </div>\n          <button\n            onClick={() => setShowCreateModal(true)}\n            className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700\"\n          >\n            <PlusIcon className=\"h-4 w-4 mr-2\" />\n            Create {activeTab.slice(0, -1)}\n          </button>\n        </div>\n      </div>\n\n      {/* Tabs */}\n      <div className=\"bg-white shadow rounded-lg\">\n        <div className=\"border-b border-gray-200\">\n          <nav className=\"-mb-px flex space-x-8 px-6\">\n            {tabs.map((tab) => (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id as any)}\n                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${\n                  activeTab === tab.id\n                    ? 'border-blue-500 text-blue-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                <tab.icon className=\"h-5 w-5\" />\n                <span>{tab.name}</span>\n              </button>\n            ))}\n          </nav>\n        </div>\n\n        {/* Search */}\n        <div className=\"p-6 border-b border-gray-200\">\n          <div className=\"relative\">\n            <MagnifyingGlassIcon className=\"h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\" />\n            <input\n              type=\"text\"\n              placeholder={`Search ${activeTab}...`}\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\n            />\n          </div>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-6\">\n          {activeTab === 'roles' && (\n            <RolesTable\n              roles={getFilteredData()}\n              onEdit={(role) => {\n                setSelectedItem(role);\n                setShowEditModal(true);\n              }}\n              onDelete={(role) => {\n                setSelectedItem(role);\n                setShowDeleteModal(true);\n              }}\n            />\n          )}\n\n          {activeTab === 'permissions' && (\n            <PermissionsTable\n              permissions={getFilteredData()}\n              onEdit={(permission) => {\n                setSelectedItem(permission);\n                setShowEditModal(true);\n              }}\n              onDelete={(permission) => {\n                setSelectedItem(permission);\n                setShowDeleteModal(true);\n              }}\n            />\n          )}\n\n          {activeTab === 'policies' && (\n            <PoliciesTable\n              policies={getFilteredData()}\n              onEdit={(policy) => {\n                setSelectedItem(policy);\n                setShowEditModal(true);\n              }}\n              onDelete={(policy) => {\n                setSelectedItem(policy);\n                setShowDeleteModal(true);\n              }}\n            />\n          )}\n        </div>\n      </div>\n\n      {/* Create Modal */}\n      {showCreateModal && (\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n          <div className=\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\">\n            <div className=\"mt-3\">\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">\n                Create {activeTab.slice(0, -1)}\n              </h3>\n              <CreateForm\n                type={activeTab}\n                onSubmit={handleCreate}\n                onCancel={() => setShowCreateModal(false)}\n              />\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Delete Confirmation Modal */}\n      {showDeleteModal && selectedItem && (\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n          <div className=\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\">\n            <div className=\"mt-3 text-center\">\n              <ExclamationTriangleIcon className=\"mx-auto h-12 w-12 text-red-600\" />\n              <h3 className=\"text-lg font-medium text-gray-900 mt-2\">\n                Delete {activeTab.slice(0, -1)}\n              </h3>\n              <p className=\"text-sm text-gray-500 mt-2\">\n                Are you sure you want to delete \"{selectedItem.name}\"? This action cannot be undone.\n              </p>\n              <div className=\"flex justify-center space-x-3 mt-4\">\n                <button\n                  onClick={() => setShowDeleteModal(false)}\n                  className=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\"\n                >\n                  Cancel\n                </button>\n                <button\n                  onClick={handleDelete}\n                  className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700\"\n                >\n                  Delete\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\n// Component for displaying roles table\nconst RolesTable: React.FC<{\n  roles: Role[];\n  onEdit: (role: Role) => void;\n  onDelete: (role: Role) => void;\n}> = ({ roles, onEdit, onDelete }) => (\n  <div className=\"overflow-x-auto\">\n    <table className=\"min-w-full divide-y divide-gray-200\">\n      <thead className=\"bg-gray-50\">\n        <tr>\n          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n            Role\n          </th>\n          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n            Permissions\n          </th>\n          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n            Users\n          </th>\n          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n            Updated\n          </th>\n          <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n            Actions\n          </th>\n        </tr>\n      </thead>\n      <tbody className=\"bg-white divide-y divide-gray-200\">\n        {roles.map((role) => (\n          <tr key={role.id} className=\"hover:bg-gray-50\">\n            <td className=\"px-6 py-4 whitespace-nowrap\">\n              <div>\n                <div className=\"text-sm font-medium text-gray-900\">{role.name}</div>\n                <div className=\"text-sm text-gray-500\">{role.description}</div>\n              </div>\n            </td>\n            <td className=\"px-6 py-4 whitespace-nowrap\">\n              <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n                {role.permissions.length} permissions\n              </span>\n            </td>\n            <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n              {role.user_count || 0} users\n            </td>\n            <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n              {new Date(role.updated_at).toLocaleDateString()}\n            </td>\n            <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n              <div className=\"flex items-center justify-end space-x-2\">\n                <button\n                  onClick={() => onEdit(role)}\n                  className=\"text-blue-600 hover:text-blue-900\"\n                >\n                  <PencilIcon className=\"h-4 w-4\" />\n                </button>\n                <button\n                  onClick={() => onDelete(role)}\n                  className=\"text-red-600 hover:text-red-900\"\n                >\n                  <TrashIcon className=\"h-4 w-4\" />\n                </button>\n              </div>\n            </td>\n          </tr>\n        ))}\n      </tbody>\n    </table>\n  </div>\n);\n\n// Component for displaying permissions table\nconst PermissionsTable: React.FC<{\n  permissions: Permission[];\n  onEdit: (permission: Permission) => void;\n  onDelete: (permission: Permission) => void;\n}> = ({ permissions, onEdit, onDelete }) => (\n  <div className=\"overflow-x-auto\">\n    <table className=\"min-w-full divide-y divide-gray-200\">\n      <thead className=\"bg-gray-50\">\n        <tr>\n          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n            Permission\n          </th>\n          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n            Resource\n          </th>\n          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n            Action\n          </th>\n          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n            Category\n          </th>\n          <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n            Actions\n          </th>\n        </tr>\n      </thead>\n      <tbody className=\"bg-white divide-y divide-gray-200\">\n        {permissions.map((permission) => (\n          <tr key={permission.id} className=\"hover:bg-gray-50\">\n            <td className=\"px-6 py-4 whitespace-nowrap\">\n              <div>\n                <div className=\"text-sm font-medium text-gray-900\">{permission.name}</div>\n                <div className=\"text-sm text-gray-500\">{permission.description}</div>\n              </div>\n            </td>\n            <td className=\"px-6 py-4 whitespace-nowrap\">\n              <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n                {permission.resource}\n              </span>\n            </td>\n            <td className=\"px-6 py-4 whitespace-nowrap\">\n              <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800\">\n                {permission.action}\n              </span>\n            </td>\n            <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n              {permission.category}\n            </td>\n            <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n              <div className=\"flex items-center justify-end space-x-2\">\n                <button\n                  onClick={() => onEdit(permission)}\n                  className=\"text-blue-600 hover:text-blue-900\"\n                >\n                  <PencilIcon className=\"h-4 w-4\" />\n                </button>\n                <button\n                  onClick={() => onDelete(permission)}\n                  className=\"text-red-600 hover:text-red-900\"\n                >\n                  <TrashIcon className=\"h-4 w-4\" />\n                </button>\n              </div>\n            </td>\n          </tr>\n        ))}\n      </tbody>\n    </table>\n  </div>\n);\n\n// Component for displaying policies table\nconst PoliciesTable: React.FC<{\n  policies: PolicyRule[];\n  onEdit: (policy: PolicyRule) => void;\n  onDelete: (policy: PolicyRule) => void;\n}> = ({ policies, onEdit, onDelete }) => (\n  <div className=\"overflow-x-auto\">\n    <table className=\"min-w-full divide-y divide-gray-200\">\n      <thead className=\"bg-gray-50\">\n        <tr>\n          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n            Policy\n          </th>\n          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n            Effect\n          </th>\n          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n            Status\n          </th>\n          <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n            Resources\n          </th>\n          <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n            Actions\n          </th>\n        </tr>\n      </thead>\n      <tbody className=\"bg-white divide-y divide-gray-200\">\n        {policies.map((policy) => (\n          <tr key={policy.id} className=\"hover:bg-gray-50\">\n            <td className=\"px-6 py-4 whitespace-nowrap\">\n              <div>\n                <div className=\"text-sm font-medium text-gray-900\">{policy.name}</div>\n                <div className=\"text-sm text-gray-500\">{policy.description}</div>\n              </div>\n            </td>\n            <td className=\"px-6 py-4 whitespace-nowrap\">\n              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                policy.effect === 'allow' \n                  ? 'bg-green-100 text-green-800' \n                  : 'bg-red-100 text-red-800'\n              }`}>\n                {policy.effect}\n              </span>\n            </td>\n            <td className=\"px-6 py-4 whitespace-nowrap\">\n              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                policy.enabled \n                  ? 'bg-green-100 text-green-800' \n                  : 'bg-gray-100 text-gray-800'\n              }`}>\n                {policy.enabled ? (\n                  <CheckCircleIcon className=\"h-3 w-3 mr-1\" />\n                ) : (\n                  <XCircleIcon className=\"h-3 w-3 mr-1\" />\n                )}\n                {policy.enabled ? 'Enabled' : 'Disabled'}\n              </span>\n            </td>\n            <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n              {policy.resources.length} resources\n            </td>\n            <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n              <div className=\"flex items-center justify-end space-x-2\">\n                <button\n                  onClick={() => onEdit(policy)}\n                  className=\"text-blue-600 hover:text-blue-900\"\n                >\n                  <PencilIcon className=\"h-4 w-4\" />\n                </button>\n                <button\n                  onClick={() => onDelete(policy)}\n                  className=\"text-red-600 hover:text-red-900\"\n                >\n                  <TrashIcon className=\"h-4 w-4\" />\n                </button>\n              </div>\n            </td>\n          </tr>\n        ))}\n      </tbody>\n    </table>\n  </div>\n);\n\n// Create Form Component\nconst CreateForm: React.FC<{\n  type: string;\n  onSubmit: (data: any) => void;\n  onCancel: () => void;\n}> = ({ type, onSubmit, onCancel }) => {\n  const [formData, setFormData] = useState<any>({});\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    onSubmit(formData);\n  };\n\n  const renderForm = () => {\n    switch (type) {\n      case 'roles':\n        return (\n          <>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700\">Role ID</label>\n              <input\n                type=\"text\"\n                required\n                value={formData.id || ''}\n                onChange={(e) => setFormData({ ...formData, id: e.target.value })}\n                className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"role_name\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700\">Name</label>\n              <input\n                type=\"text\"\n                required\n                value={formData.name || ''}\n                onChange={(e) => setFormData({ ...formData, name: e.target.value })}\n                className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"Role Name\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700\">Description</label>\n              <textarea\n                required\n                value={formData.description || ''}\n                onChange={(e) => setFormData({ ...formData, description: e.target.value })}\n                className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500\"\n                rows={3}\n                placeholder=\"Describe this role\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700\">Permissions</label>\n              <input\n                type=\"text\"\n                value={formData.permissions || ''}\n                onChange={(e) => setFormData({ ...formData, permissions: e.target.value.split(',').map((p: string) => p.trim()) })}\n                className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"permission1, permission2, permission3\"\n              />\n              <p className=\"text-xs text-gray-500 mt-1\">Comma-separated list of permissions</p>\n            </div>\n          </>\n        );\n      case 'permissions':\n        return (\n          <>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700\">Permission ID</label>\n              <input\n                type=\"text\"\n                required\n                value={formData.id || ''}\n                onChange={(e) => setFormData({ ...formData, id: e.target.value })}\n                className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"resource:action\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700\">Name</label>\n              <input\n                type=\"text\"\n                required\n                value={formData.name || ''}\n                onChange={(e) => setFormData({ ...formData, name: e.target.value })}\n                className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"Permission Name\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700\">Description</label>\n              <textarea\n                required\n                value={formData.description || ''}\n                onChange={(e) => setFormData({ ...formData, description: e.target.value })}\n                className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500\"\n                rows={3}\n                placeholder=\"Describe this permission\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700\">Resource</label>\n              <input\n                type=\"text\"\n                required\n                value={formData.resource || ''}\n                onChange={(e) => setFormData({ ...formData, resource: e.target.value })}\n                className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"workflows\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700\">Action</label>\n              <select\n                required\n                value={formData.action || ''}\n                onChange={(e) => setFormData({ ...formData, action: e.target.value })}\n                className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500\"\n              >\n                <option value=\"\">Select action</option>\n                <option value=\"read\">Read</option>\n                <option value=\"write\">Write</option>\n                <option value=\"execute\">Execute</option>\n                <option value=\"delete\">Delete</option>\n              </select>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700\">Category</label>\n              <input\n                type=\"text\"\n                required\n                value={formData.category || ''}\n                onChange={(e) => setFormData({ ...formData, category: e.target.value })}\n                className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"Workflows\"\n              />\n            </div>\n          </>\n        );\n      case 'policies':\n        return (\n          <>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700\">Policy ID</label>\n              <input\n                type=\"text\"\n                required\n                value={formData.id || ''}\n                onChange={(e) => setFormData({ ...formData, id: e.target.value })}\n                className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"policy-name\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700\">Name</label>\n              <input\n                type=\"text\"\n                required\n                value={formData.name || ''}\n                onChange={(e) => setFormData({ ...formData, name: e.target.value })}\n                className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"Policy Name\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700\">Description</label>\n              <textarea\n                required\n                value={formData.description || ''}\n                onChange={(e) => setFormData({ ...formData, description: e.target.value })}\n                className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500\"\n                rows={3}\n                placeholder=\"Describe this policy\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700\">Effect</label>\n              <select\n                required\n                value={formData.effect || ''}\n                onChange={(e) => setFormData({ ...formData, effect: e.target.value })}\n                className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500\"\n              >\n                <option value=\"\">Select effect</option>\n                <option value=\"allow\">Allow</option>\n                <option value=\"deny\">Deny</option>\n              </select>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700\">Subjects</label>\n              <input\n                type=\"text\"\n                value={formData.subjects || ''}\n                onChange={(e) => setFormData({ ...formData, subjects: e.target.value.split(',').map((s: string) => s.trim()) })}\n                className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"role:admin, user:john\"\n              />\n              <p className=\"text-xs text-gray-500 mt-1\">Comma-separated list of subjects</p>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700\">Resources</label>\n              <input\n                type=\"text\"\n                value={formData.resources || ''}\n                onChange={(e) => setFormData({ ...formData, resources: e.target.value.split(',').map((r: string) => r.trim()) })}\n                className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"workflows, discovery\"\n              />\n              <p className=\"text-xs text-gray-500 mt-1\">Comma-separated list of resources</p>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700\">Actions</label>\n              <input\n                type=\"text\"\n                value={formData.actions || ''}\n                onChange={(e) => setFormData({ ...formData, actions: e.target.value.split(',').map((a: string) => a.trim()) })}\n                className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"read, write, execute\"\n              />\n              <p className=\"text-xs text-gray-500 mt-1\">Comma-separated list of actions</p>\n            </div>\n            <div className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                checked={formData.enabled || false}\n                onChange={(e) => setFormData({ ...formData, enabled: e.target.checked })}\n                className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n              />\n              <label className=\"ml-2 block text-sm text-gray-900\">\n                Enable policy\n              </label>\n            </div>\n          </>\n        );\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-4\">\n      {renderForm()}\n      <div className=\"flex justify-end space-x-3 pt-4\">\n        <button\n          type=\"button\"\n          onClick={onCancel}\n          className=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\"\n        >\n          Cancel\n        </button>\n        <button\n          type=\"submit\"\n          className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700\"\n        >\n          Create {type.slice(0, -1)}\n        </button>\n      </div>\n    </form>\n  );\n};\n\nexport default RBACAdmin;\n"], "mappings": "yJAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,OAAO,KAAQ,qBAAqB,CAC7C,OACEC,eAAe,CACfC,aAAa,CACbC,OAAO,CACPC,QAAQ,CACRC,UAAU,CACVC,SAAS,CAETC,eAAe,CACfC,WAAW,CACXC,mBAAmB,CACnBC,uBAAuB,KAClB,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAiCrC,KAAM,CAAAC,SAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAAEC,IAAK,CAAC,CAAGlB,OAAO,CAAC,CAAC,CAC1B,KAAM,CAACmB,SAAS,CAAEC,YAAY,CAAC,CAAGrB,QAAQ,CAAuC,OAAO,CAAC,CACzF,KAAM,CAACsB,KAAK,CAAEC,QAAQ,CAAC,CAAGvB,QAAQ,CAAS,EAAE,CAAC,CAC9C,KAAM,CAACwB,WAAW,CAAEC,cAAc,CAAC,CAAGzB,QAAQ,CAAe,EAAE,CAAC,CAChE,KAAM,CAAC0B,QAAQ,CAAEC,WAAW,CAAC,CAAG3B,QAAQ,CAAe,EAAE,CAAC,CAC1D,KAAM,CAAC4B,OAAO,CAAEC,UAAU,CAAC,CAAG7B,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC8B,UAAU,CAAEC,aAAa,CAAC,CAAG/B,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACgC,eAAe,CAAEC,kBAAkB,CAAC,CAAGjC,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAACkC,aAAa,CAAEC,gBAAgB,CAAC,CAAGnC,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACoC,eAAe,CAAEC,kBAAkB,CAAC,CAAGrC,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAACsC,YAAY,CAAEC,eAAe,CAAC,CAAGvC,QAAQ,CAAM,IAAI,CAAC,CAE3DD,SAAS,CAAC,IAAM,CACdyC,SAAS,CAAC,CAAC,CACb,CAAC,CAAE,CAACpB,SAAS,CAAC,CAAC,CAEf,KAAM,CAAAoB,SAAS,CAAG,KAAAA,CAAA,GAAY,CAC5BX,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,OAAQT,SAAS,EACf,IAAK,OAAO,CACV,KAAM,CAAAqB,UAAU,CAAC,CAAC,CAClB,MACF,IAAK,aAAa,CAChB,KAAM,CAAAC,gBAAgB,CAAC,CAAC,CACxB,MACF,IAAK,UAAU,CACb,KAAM,CAAAC,aAAa,CAAC,CAAC,CACrB,MACJ,CACF,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,oBAAAE,MAAA,CAAoB1B,SAAS,MAAKwB,KAAK,CAAC,CACvD,CAAC,OAAS,CACRf,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAY,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B,GAAI,CACF,KAAM,CAAAM,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,gBAAgB,CAAE,CAC7CC,WAAW,CAAE,SACf,CAAC,CAAC,CACF,GAAIF,QAAQ,CAACG,EAAE,CAAE,CACf,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAJ,QAAQ,CAACK,IAAI,CAAC,CAAC,CAClC7B,QAAQ,CAAC4B,IAAI,CAAC7B,KAAK,EAAI,EAAE,CAAC,CAC5B,CACF,CAAE,MAAOsB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAChD,CACF,CAAC,CAED,KAAM,CAAAF,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAI,CACF,KAAM,CAAAK,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,sBAAsB,CAAE,CACnDC,WAAW,CAAE,SACf,CAAC,CAAC,CACF,GAAIF,QAAQ,CAACG,EAAE,CAAE,CACf,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAJ,QAAQ,CAACK,IAAI,CAAC,CAAC,CAClC3B,cAAc,CAAC0B,IAAI,CAAC3B,WAAW,EAAI,EAAE,CAAC,CACxC,CACF,CAAE,MAAOoB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACtD,CACF,CAAC,CAED,KAAM,CAAAD,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACF,KAAM,CAAAI,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,mBAAmB,CAAE,CAChDC,WAAW,CAAE,SACf,CAAC,CAAC,CACF,GAAIF,QAAQ,CAACG,EAAE,CAAE,CACf,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAJ,QAAQ,CAACK,IAAI,CAAC,CAAC,CAClCzB,WAAW,CAACwB,IAAI,CAACzB,QAAQ,EAAI,EAAE,CAAC,CAClC,CACF,CAAE,MAAOkB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACnD,CACF,CAAC,CAED,KAAM,CAAAS,YAAY,CAAG,KAAO,CAAAF,IAAS,EAAK,CACxC,GAAI,CACF,KAAM,CAAAJ,QAAQ,CAAG,KAAM,CAAAC,KAAK,aAAAF,MAAA,CAAa1B,SAAS,EAAI,CACpDkC,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDN,WAAW,CAAE,SAAS,CACtBO,IAAI,CAAEC,IAAI,CAACC,SAAS,CAACP,IAAI,CAC3B,CAAC,CAAC,CAEF,GAAIJ,QAAQ,CAACG,EAAE,CAAE,CACfjB,kBAAkB,CAAC,KAAK,CAAC,CACzBO,SAAS,CAAC,CAAC,CACXmB,KAAK,IAAAb,MAAA,CAAI1B,SAAS,CAACwC,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,0BAAwB,CAAC,CAC1D,CAAC,IAAM,CACL,KAAM,CAAAC,SAAS,CAAG,KAAM,CAAAd,QAAQ,CAACK,IAAI,CAAC,CAAC,CACvCO,KAAK,qBAAAb,MAAA,CAAqB1B,SAAS,CAACwC,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,OAAAd,MAAA,CAAKe,SAAS,CAACjB,KAAK,EAAI,eAAe,CAAE,CAAC,CAC5F,CACF,CAAE,MAAOA,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,qBAAAE,MAAA,CAAqB1B,SAAS,CAACwC,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,MAAKhB,KAAK,CAAC,CACnEe,KAAK,qBAAAb,MAAA,CAAqB1B,SAAS,CAACwC,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,2CAAyC,CAAC,CAC5F,CACF,CAAC,CAED,KAAM,CAAAE,YAAY,CAAG,KAAAA,CAAOC,EAAU,CAAEZ,IAAS,GAAK,CACpD,GAAI,CACF,KAAM,CAAAJ,QAAQ,CAAG,KAAM,CAAAC,KAAK,aAAAF,MAAA,CAAa1B,SAAS,MAAA0B,MAAA,CAAIiB,EAAE,EAAI,CAC1DT,MAAM,CAAE,KAAK,CACbC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDN,WAAW,CAAE,SAAS,CACtBO,IAAI,CAAEC,IAAI,CAACC,SAAS,CAACP,IAAI,CAC3B,CAAC,CAAC,CAEF,GAAIJ,QAAQ,CAACG,EAAE,CAAE,CACff,gBAAgB,CAAC,KAAK,CAAC,CACvBI,eAAe,CAAC,IAAI,CAAC,CACrBC,SAAS,CAAC,CAAC,CACb,CACF,CAAE,MAAOI,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,qBAAAE,MAAA,CAAqB1B,SAAS,CAACwC,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,MAAKhB,KAAK,CAAC,CACrE,CACF,CAAC,CAED,KAAM,CAAAoB,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CAAC1B,YAAY,CAAE,OAEnB,GAAI,CACF,KAAM,CAAAS,QAAQ,CAAG,KAAM,CAAAC,KAAK,aAAAF,MAAA,CAAa1B,SAAS,MAAA0B,MAAA,CAAIR,YAAY,CAACyB,EAAE,EAAI,CACvET,MAAM,CAAE,QAAQ,CAChBL,WAAW,CAAE,SACf,CAAC,CAAC,CAEF,GAAIF,QAAQ,CAACG,EAAE,CAAE,CACfb,kBAAkB,CAAC,KAAK,CAAC,CACzBE,eAAe,CAAC,IAAI,CAAC,CACrBC,SAAS,CAAC,CAAC,CACb,CACF,CAAE,MAAOI,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,qBAAAE,MAAA,CAAqB1B,SAAS,CAACwC,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,MAAKhB,KAAK,CAAC,CACrE,CACF,CAAC,CAED,KAAM,CAAAqB,eAAe,CAAGA,CAAA,GAAM,CAC5B,GAAI,CAAAd,IAAW,CAAG,EAAE,CACpB,OAAQ/B,SAAS,EACf,IAAK,OAAO,CACV+B,IAAI,CAAG7B,KAAK,CACZ,MACF,IAAK,aAAa,CAChB6B,IAAI,CAAG3B,WAAW,CAClB,MACF,IAAK,UAAU,CACb2B,IAAI,CAAGzB,QAAQ,CACf,MACJ,CAEA,MAAO,CAAAyB,IAAI,CAACe,MAAM,CAACC,IAAI,EACrBA,IAAI,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxC,UAAU,CAACuC,WAAW,CAAC,CAAC,CAAC,EAC1DF,IAAI,CAACI,WAAW,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxC,UAAU,CAACuC,WAAW,CAAC,CAAC,CAClE,CAAC,CACH,CAAC,CAED,KAAM,CAAAG,IAAI,CAAG,CACX,CAAET,EAAE,CAAE,OAAO,CAAEK,IAAI,CAAE,OAAO,CAAEK,IAAI,CAAEtE,aAAc,CAAC,CACnD,CAAE4D,EAAE,CAAE,aAAa,CAAEK,IAAI,CAAE,aAAa,CAAEK,IAAI,CAAErE,OAAQ,CAAC,CACzD,CAAE2D,EAAE,CAAE,UAAU,CAAEK,IAAI,CAAE,UAAU,CAAEK,IAAI,CAAEvE,eAAgB,CAAC,CAC5D,CAED,GAAI0B,OAAO,CAAE,CACX,mBACEf,IAAA,QAAK6D,SAAS,CAAC,uCAAuC,CAAAC,QAAA,cACpD9D,IAAA,QAAK6D,SAAS,CAAC,gEAAgE,CAAM,CAAC,CACnF,CAAC,CAEV,CAEA,mBACE3D,KAAA,QAAK2D,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExB9D,IAAA,QAAK6D,SAAS,CAAC,gCAAgC,CAAAC,QAAA,cAC7C5D,KAAA,QAAK2D,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChD5D,KAAA,QAAA4D,QAAA,eACE9D,IAAA,OAAI6D,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,cACrE9D,IAAA,MAAG6D,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,gDAA8C,CAAG,CAAC,EAC5E,CAAC,cACN5D,KAAA,WACE6D,OAAO,CAAEA,CAAA,GAAM3C,kBAAkB,CAAC,IAAI,CAAE,CACxCyC,SAAS,CAAC,gJAAgJ,CAAAC,QAAA,eAE1J9D,IAAA,CAACR,QAAQ,EAACqE,SAAS,CAAC,cAAc,CAAE,CAAC,UAC9B,CAACtD,SAAS,CAACwC,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,EACxB,CAAC,EACN,CAAC,CACH,CAAC,cAGN7C,KAAA,QAAK2D,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzC9D,IAAA,QAAK6D,SAAS,CAAC,0BAA0B,CAAAC,QAAA,cACvC9D,IAAA,QAAK6D,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CACxCH,IAAI,CAACK,GAAG,CAAEC,GAAG,eACZ/D,KAAA,WAEE6D,OAAO,CAAEA,CAAA,GAAMvD,YAAY,CAACyD,GAAG,CAACf,EAAS,CAAE,CAC3CW,SAAS,yEAAA5B,MAAA,CACP1B,SAAS,GAAK0D,GAAG,CAACf,EAAE,CAChB,+BAA+B,CAC/B,4EAA4E,CAC/E,CAAAY,QAAA,eAEH9D,IAAA,CAACiE,GAAG,CAACL,IAAI,EAACC,SAAS,CAAC,SAAS,CAAE,CAAC,cAChC7D,IAAA,SAAA8D,QAAA,CAAOG,GAAG,CAACV,IAAI,CAAO,CAAC,GATlBU,GAAG,CAACf,EAUH,CACT,CAAC,CACC,CAAC,CACH,CAAC,cAGNlD,IAAA,QAAK6D,SAAS,CAAC,8BAA8B,CAAAC,QAAA,cAC3C5D,KAAA,QAAK2D,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvB9D,IAAA,CAACH,mBAAmB,EAACgE,SAAS,CAAC,0EAA0E,CAAE,CAAC,cAC5G7D,IAAA,UACEkE,IAAI,CAAC,MAAM,CACXC,WAAW,WAAAlC,MAAA,CAAY1B,SAAS,OAAM,CACtC6D,KAAK,CAAEnD,UAAW,CAClBoD,QAAQ,CAAGC,CAAC,EAAKpD,aAAa,CAACoD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC/CP,SAAS,CAAC,oGAAoG,CAC/G,CAAC,EACC,CAAC,CACH,CAAC,cAGN3D,KAAA,QAAK2D,SAAS,CAAC,KAAK,CAAAC,QAAA,EACjBvD,SAAS,GAAK,OAAO,eACpBP,IAAA,CAACwE,UAAU,EACT/D,KAAK,CAAE2C,eAAe,CAAC,CAAE,CACzBqB,MAAM,CAAGC,IAAI,EAAK,CAChBhD,eAAe,CAACgD,IAAI,CAAC,CACrBpD,gBAAgB,CAAC,IAAI,CAAC,CACxB,CAAE,CACFqD,QAAQ,CAAGD,IAAI,EAAK,CAClBhD,eAAe,CAACgD,IAAI,CAAC,CACrBlD,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAE,CACH,CACF,CAEAjB,SAAS,GAAK,aAAa,eAC1BP,IAAA,CAAC4E,gBAAgB,EACfjE,WAAW,CAAEyC,eAAe,CAAC,CAAE,CAC/BqB,MAAM,CAAGI,UAAU,EAAK,CACtBnD,eAAe,CAACmD,UAAU,CAAC,CAC3BvD,gBAAgB,CAAC,IAAI,CAAC,CACxB,CAAE,CACFqD,QAAQ,CAAGE,UAAU,EAAK,CACxBnD,eAAe,CAACmD,UAAU,CAAC,CAC3BrD,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAE,CACH,CACF,CAEAjB,SAAS,GAAK,UAAU,eACvBP,IAAA,CAAC8E,aAAa,EACZjE,QAAQ,CAAEuC,eAAe,CAAC,CAAE,CAC5BqB,MAAM,CAAGM,MAAM,EAAK,CAClBrD,eAAe,CAACqD,MAAM,CAAC,CACvBzD,gBAAgB,CAAC,IAAI,CAAC,CACxB,CAAE,CACFqD,QAAQ,CAAGI,MAAM,EAAK,CACpBrD,eAAe,CAACqD,MAAM,CAAC,CACvBvD,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAE,CACH,CACF,EACE,CAAC,EACH,CAAC,CAGLL,eAAe,eACdnB,IAAA,QAAK6D,SAAS,CAAC,4EAA4E,CAAAC,QAAA,cACzF9D,IAAA,QAAK6D,SAAS,CAAC,uEAAuE,CAAAC,QAAA,cACpF5D,KAAA,QAAK2D,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB5D,KAAA,OAAI2D,SAAS,CAAC,wCAAwC,CAAAC,QAAA,EAAC,SAC9C,CAACvD,SAAS,CAACwC,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,EAC5B,CAAC,cACL/C,IAAA,CAACgF,UAAU,EACTd,IAAI,CAAE3D,SAAU,CAChB0E,QAAQ,CAAEzC,YAAa,CACvB0C,QAAQ,CAAEA,CAAA,GAAM9D,kBAAkB,CAAC,KAAK,CAAE,CAC3C,CAAC,EACC,CAAC,CACH,CAAC,CACH,CACN,CAGAG,eAAe,EAAIE,YAAY,eAC9BzB,IAAA,QAAK6D,SAAS,CAAC,4EAA4E,CAAAC,QAAA,cACzF9D,IAAA,QAAK6D,SAAS,CAAC,uEAAuE,CAAAC,QAAA,cACpF5D,KAAA,QAAK2D,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/B9D,IAAA,CAACF,uBAAuB,EAAC+D,SAAS,CAAC,gCAAgC,CAAE,CAAC,cACtE3D,KAAA,OAAI2D,SAAS,CAAC,wCAAwC,CAAAC,QAAA,EAAC,SAC9C,CAACvD,SAAS,CAACwC,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,EAC5B,CAAC,cACL7C,KAAA,MAAG2D,SAAS,CAAC,4BAA4B,CAAAC,QAAA,EAAC,oCACP,CAACrC,YAAY,CAAC8B,IAAI,CAAC,mCACtD,EAAG,CAAC,cACJrD,KAAA,QAAK2D,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjD9D,IAAA,WACE+D,OAAO,CAAEA,CAAA,GAAMvC,kBAAkB,CAAC,KAAK,CAAE,CACzCqC,SAAS,CAAC,gGAAgG,CAAAC,QAAA,CAC3G,QAED,CAAQ,CAAC,cACT9D,IAAA,WACE+D,OAAO,CAAEZ,YAAa,CACtBU,SAAS,CAAC,qHAAqH,CAAAC,QAAA,CAChI,QAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACH,CAAC,CACH,CACN,EACE,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAU,UAIJ,CAAGW,IAAA,MAAC,CAAE1E,KAAK,CAAEgE,MAAM,CAAEE,QAAS,CAAC,CAAAQ,IAAA,oBAC/BnF,IAAA,QAAK6D,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B5D,KAAA,UAAO2D,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eACpD9D,IAAA,UAAO6D,SAAS,CAAC,YAAY,CAAAC,QAAA,cAC3B5D,KAAA,OAAA4D,QAAA,eACE9D,IAAA,OAAI6D,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,MAE/F,CAAI,CAAC,cACL9D,IAAA,OAAI6D,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,aAE/F,CAAI,CAAC,cACL9D,IAAA,OAAI6D,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,OAE/F,CAAI,CAAC,cACL9D,IAAA,OAAI6D,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,SAE/F,CAAI,CAAC,cACL9D,IAAA,OAAI6D,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,SAEhG,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cACR9D,IAAA,UAAO6D,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CACjDrD,KAAK,CAACuD,GAAG,CAAEU,IAAI,eACdxE,KAAA,OAAkB2D,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC5C9D,IAAA,OAAI6D,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzC5D,KAAA,QAAA4D,QAAA,eACE9D,IAAA,QAAK6D,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAEY,IAAI,CAACnB,IAAI,CAAM,CAAC,cACpEvD,IAAA,QAAK6D,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEY,IAAI,CAAChB,WAAW,CAAM,CAAC,EAC5D,CAAC,CACJ,CAAC,cACL1D,IAAA,OAAI6D,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzC5D,KAAA,SAAM2D,SAAS,CAAC,mGAAmG,CAAAC,QAAA,EAChHY,IAAI,CAAC/D,WAAW,CAACyE,MAAM,CAAC,cAC3B,EAAM,CAAC,CACL,CAAC,cACLlF,KAAA,OAAI2D,SAAS,CAAC,mDAAmD,CAAAC,QAAA,EAC9DY,IAAI,CAACW,UAAU,EAAI,CAAC,CAAC,QACxB,EAAI,CAAC,cACLrF,IAAA,OAAI6D,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAC9D,GAAI,CAAAwB,IAAI,CAACZ,IAAI,CAACa,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC,CAC7C,CAAC,cACLxF,IAAA,OAAI6D,SAAS,CAAC,4DAA4D,CAAAC,QAAA,cACxE5D,KAAA,QAAK2D,SAAS,CAAC,yCAAyC,CAAAC,QAAA,eACtD9D,IAAA,WACE+D,OAAO,CAAEA,CAAA,GAAMU,MAAM,CAACC,IAAI,CAAE,CAC5Bb,SAAS,CAAC,mCAAmC,CAAAC,QAAA,cAE7C9D,IAAA,CAACP,UAAU,EAACoE,SAAS,CAAC,SAAS,CAAE,CAAC,CAC5B,CAAC,cACT7D,IAAA,WACE+D,OAAO,CAAEA,CAAA,GAAMY,QAAQ,CAACD,IAAI,CAAE,CAC9Bb,SAAS,CAAC,iCAAiC,CAAAC,QAAA,cAE3C9D,IAAA,CAACN,SAAS,EAACmE,SAAS,CAAC,SAAS,CAAE,CAAC,CAC3B,CAAC,EACN,CAAC,CACJ,CAAC,GAjCEa,IAAI,CAACxB,EAkCV,CACL,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,EACP,CAED;AACA,KAAM,CAAA0B,gBAIJ,CAAGa,KAAA,MAAC,CAAE9E,WAAW,CAAE8D,MAAM,CAAEE,QAAS,CAAC,CAAAc,KAAA,oBACrCzF,IAAA,QAAK6D,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B5D,KAAA,UAAO2D,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eACpD9D,IAAA,UAAO6D,SAAS,CAAC,YAAY,CAAAC,QAAA,cAC3B5D,KAAA,OAAA4D,QAAA,eACE9D,IAAA,OAAI6D,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,YAE/F,CAAI,CAAC,cACL9D,IAAA,OAAI6D,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,UAE/F,CAAI,CAAC,cACL9D,IAAA,OAAI6D,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,QAE/F,CAAI,CAAC,cACL9D,IAAA,OAAI6D,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,UAE/F,CAAI,CAAC,cACL9D,IAAA,OAAI6D,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,SAEhG,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cACR9D,IAAA,UAAO6D,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CACjDnD,WAAW,CAACqD,GAAG,CAAEa,UAAU,eAC1B3E,KAAA,OAAwB2D,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAClD9D,IAAA,OAAI6D,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzC5D,KAAA,QAAA4D,QAAA,eACE9D,IAAA,QAAK6D,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAEe,UAAU,CAACtB,IAAI,CAAM,CAAC,cAC1EvD,IAAA,QAAK6D,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEe,UAAU,CAACnB,WAAW,CAAM,CAAC,EAClE,CAAC,CACJ,CAAC,cACL1D,IAAA,OAAI6D,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzC9D,IAAA,SAAM6D,SAAS,CAAC,qGAAqG,CAAAC,QAAA,CAClHe,UAAU,CAACa,QAAQ,CAChB,CAAC,CACL,CAAC,cACL1F,IAAA,OAAI6D,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzC9D,IAAA,SAAM6D,SAAS,CAAC,uGAAuG,CAAAC,QAAA,CACpHe,UAAU,CAACc,MAAM,CACd,CAAC,CACL,CAAC,cACL3F,IAAA,OAAI6D,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAC9De,UAAU,CAACe,QAAQ,CAClB,CAAC,cACL5F,IAAA,OAAI6D,SAAS,CAAC,4DAA4D,CAAAC,QAAA,cACxE5D,KAAA,QAAK2D,SAAS,CAAC,yCAAyC,CAAAC,QAAA,eACtD9D,IAAA,WACE+D,OAAO,CAAEA,CAAA,GAAMU,MAAM,CAACI,UAAU,CAAE,CAClChB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,cAE7C9D,IAAA,CAACP,UAAU,EAACoE,SAAS,CAAC,SAAS,CAAE,CAAC,CAC5B,CAAC,cACT7D,IAAA,WACE+D,OAAO,CAAEA,CAAA,GAAMY,QAAQ,CAACE,UAAU,CAAE,CACpChB,SAAS,CAAC,iCAAiC,CAAAC,QAAA,cAE3C9D,IAAA,CAACN,SAAS,EAACmE,SAAS,CAAC,SAAS,CAAE,CAAC,CAC3B,CAAC,EACN,CAAC,CACJ,CAAC,GAnCEgB,UAAU,CAAC3B,EAoChB,CACL,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,EACP,CAED;AACA,KAAM,CAAA4B,aAIJ,CAAGe,KAAA,MAAC,CAAEhF,QAAQ,CAAE4D,MAAM,CAAEE,QAAS,CAAC,CAAAkB,KAAA,oBAClC7F,IAAA,QAAK6D,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B5D,KAAA,UAAO2D,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eACpD9D,IAAA,UAAO6D,SAAS,CAAC,YAAY,CAAAC,QAAA,cAC3B5D,KAAA,OAAA4D,QAAA,eACE9D,IAAA,OAAI6D,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,QAE/F,CAAI,CAAC,cACL9D,IAAA,OAAI6D,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,QAE/F,CAAI,CAAC,cACL9D,IAAA,OAAI6D,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,QAE/F,CAAI,CAAC,cACL9D,IAAA,OAAI6D,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,WAE/F,CAAI,CAAC,cACL9D,IAAA,OAAI6D,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,SAEhG,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cACR9D,IAAA,UAAO6D,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CACjDjD,QAAQ,CAACmD,GAAG,CAAEe,MAAM,eACnB7E,KAAA,OAAoB2D,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC9C9D,IAAA,OAAI6D,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzC5D,KAAA,QAAA4D,QAAA,eACE9D,IAAA,QAAK6D,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAEiB,MAAM,CAACxB,IAAI,CAAM,CAAC,cACtEvD,IAAA,QAAK6D,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEiB,MAAM,CAACrB,WAAW,CAAM,CAAC,EAC9D,CAAC,CACJ,CAAC,cACL1D,IAAA,OAAI6D,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzC9D,IAAA,SAAM6D,SAAS,4EAAA5B,MAAA,CACb8C,MAAM,CAACe,MAAM,GAAK,OAAO,CACrB,6BAA6B,CAC7B,yBAAyB,CAC5B,CAAAhC,QAAA,CACAiB,MAAM,CAACe,MAAM,CACV,CAAC,CACL,CAAC,cACL9F,IAAA,OAAI6D,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzC5D,KAAA,SAAM2D,SAAS,4EAAA5B,MAAA,CACb8C,MAAM,CAACgB,OAAO,CACV,6BAA6B,CAC7B,2BAA2B,CAC9B,CAAAjC,QAAA,EACAiB,MAAM,CAACgB,OAAO,cACb/F,IAAA,CAACL,eAAe,EAACkE,SAAS,CAAC,cAAc,CAAE,CAAC,cAE5C7D,IAAA,CAACJ,WAAW,EAACiE,SAAS,CAAC,cAAc,CAAE,CACxC,CACAkB,MAAM,CAACgB,OAAO,CAAG,SAAS,CAAG,UAAU,EACpC,CAAC,CACL,CAAC,cACL7F,KAAA,OAAI2D,SAAS,CAAC,mDAAmD,CAAAC,QAAA,EAC9DiB,MAAM,CAACiB,SAAS,CAACZ,MAAM,CAAC,YAC3B,EAAI,CAAC,cACLpF,IAAA,OAAI6D,SAAS,CAAC,4DAA4D,CAAAC,QAAA,cACxE5D,KAAA,QAAK2D,SAAS,CAAC,yCAAyC,CAAAC,QAAA,eACtD9D,IAAA,WACE+D,OAAO,CAAEA,CAAA,GAAMU,MAAM,CAACM,MAAM,CAAE,CAC9BlB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,cAE7C9D,IAAA,CAACP,UAAU,EAACoE,SAAS,CAAC,SAAS,CAAE,CAAC,CAC5B,CAAC,cACT7D,IAAA,WACE+D,OAAO,CAAEA,CAAA,GAAMY,QAAQ,CAACI,MAAM,CAAE,CAChClB,SAAS,CAAC,iCAAiC,CAAAC,QAAA,cAE3C9D,IAAA,CAACN,SAAS,EAACmE,SAAS,CAAC,SAAS,CAAE,CAAC,CAC3B,CAAC,EACN,CAAC,CACJ,CAAC,GAhDEkB,MAAM,CAAC7B,EAiDZ,CACL,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,EACP,CAED;AACA,KAAM,CAAA8B,UAIJ,CAAGiB,KAAA,EAAkC,IAAjC,CAAE/B,IAAI,CAAEe,QAAQ,CAAEC,QAAS,CAAC,CAAAe,KAAA,CAChC,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGhH,QAAQ,CAAM,CAAC,CAAC,CAAC,CAEjD,KAAM,CAAAiH,YAAY,CAAI9B,CAAkB,EAAK,CAC3CA,CAAC,CAAC+B,cAAc,CAAC,CAAC,CAClBpB,QAAQ,CAACiB,QAAQ,CAAC,CACpB,CAAC,CAED,KAAM,CAAAI,UAAU,CAAGA,CAAA,GAAM,CACvB,OAAQpC,IAAI,EACV,IAAK,OAAO,CACV,mBACEhE,KAAA,CAAAE,SAAA,EAAA0D,QAAA,eACE5D,KAAA,QAAA4D,QAAA,eACE9D,IAAA,UAAO6D,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,SAAO,CAAO,CAAC,cAC1E9D,IAAA,UACEkE,IAAI,CAAC,MAAM,CACXqC,QAAQ,MACRnC,KAAK,CAAE8B,QAAQ,CAAChD,EAAE,EAAI,EAAG,CACzBmB,QAAQ,CAAGC,CAAC,EAAK6B,WAAW,CAAAK,aAAA,CAAAA,aAAA,IAAMN,QAAQ,MAAEhD,EAAE,CAAEoB,CAAC,CAACC,MAAM,CAACH,KAAK,EAAE,CAAE,CAClEP,SAAS,CAAC,yGAAyG,CACnHM,WAAW,CAAC,WAAW,CACxB,CAAC,EACC,CAAC,cACNjE,KAAA,QAAA4D,QAAA,eACE9D,IAAA,UAAO6D,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,MAAI,CAAO,CAAC,cACvE9D,IAAA,UACEkE,IAAI,CAAC,MAAM,CACXqC,QAAQ,MACRnC,KAAK,CAAE8B,QAAQ,CAAC3C,IAAI,EAAI,EAAG,CAC3Bc,QAAQ,CAAGC,CAAC,EAAK6B,WAAW,CAAAK,aAAA,CAAAA,aAAA,IAAMN,QAAQ,MAAE3C,IAAI,CAAEe,CAAC,CAACC,MAAM,CAACH,KAAK,EAAE,CAAE,CACpEP,SAAS,CAAC,yGAAyG,CACnHM,WAAW,CAAC,WAAW,CACxB,CAAC,EACC,CAAC,cACNjE,KAAA,QAAA4D,QAAA,eACE9D,IAAA,UAAO6D,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,aAAW,CAAO,CAAC,cAC9E9D,IAAA,aACEuG,QAAQ,MACRnC,KAAK,CAAE8B,QAAQ,CAACxC,WAAW,EAAI,EAAG,CAClCW,QAAQ,CAAGC,CAAC,EAAK6B,WAAW,CAAAK,aAAA,CAAAA,aAAA,IAAMN,QAAQ,MAAExC,WAAW,CAAEY,CAAC,CAACC,MAAM,CAACH,KAAK,EAAE,CAAE,CAC3EP,SAAS,CAAC,yGAAyG,CACnH4C,IAAI,CAAE,CAAE,CACRtC,WAAW,CAAC,oBAAoB,CACjC,CAAC,EACC,CAAC,cACNjE,KAAA,QAAA4D,QAAA,eACE9D,IAAA,UAAO6D,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,aAAW,CAAO,CAAC,cAC9E9D,IAAA,UACEkE,IAAI,CAAC,MAAM,CACXE,KAAK,CAAE8B,QAAQ,CAACvF,WAAW,EAAI,EAAG,CAClC0D,QAAQ,CAAGC,CAAC,EAAK6B,WAAW,CAAAK,aAAA,CAAAA,aAAA,IAAMN,QAAQ,MAAEvF,WAAW,CAAE2D,CAAC,CAACC,MAAM,CAACH,KAAK,CAACsC,KAAK,CAAC,GAAG,CAAC,CAAC1C,GAAG,CAAE2C,CAAS,EAAKA,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAE,CACnH/C,SAAS,CAAC,yGAAyG,CACnHM,WAAW,CAAC,uCAAuC,CACpD,CAAC,cACFnE,IAAA,MAAG6D,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,qCAAmC,CAAG,CAAC,EAC9E,CAAC,EACN,CAAC,CAEP,IAAK,aAAa,CAChB,mBACE5D,KAAA,CAAAE,SAAA,EAAA0D,QAAA,eACE5D,KAAA,QAAA4D,QAAA,eACE9D,IAAA,UAAO6D,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,eAAa,CAAO,CAAC,cAChF9D,IAAA,UACEkE,IAAI,CAAC,MAAM,CACXqC,QAAQ,MACRnC,KAAK,CAAE8B,QAAQ,CAAChD,EAAE,EAAI,EAAG,CACzBmB,QAAQ,CAAGC,CAAC,EAAK6B,WAAW,CAAAK,aAAA,CAAAA,aAAA,IAAMN,QAAQ,MAAEhD,EAAE,CAAEoB,CAAC,CAACC,MAAM,CAACH,KAAK,EAAE,CAAE,CAClEP,SAAS,CAAC,yGAAyG,CACnHM,WAAW,CAAC,iBAAiB,CAC9B,CAAC,EACC,CAAC,cACNjE,KAAA,QAAA4D,QAAA,eACE9D,IAAA,UAAO6D,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,MAAI,CAAO,CAAC,cACvE9D,IAAA,UACEkE,IAAI,CAAC,MAAM,CACXqC,QAAQ,MACRnC,KAAK,CAAE8B,QAAQ,CAAC3C,IAAI,EAAI,EAAG,CAC3Bc,QAAQ,CAAGC,CAAC,EAAK6B,WAAW,CAAAK,aAAA,CAAAA,aAAA,IAAMN,QAAQ,MAAE3C,IAAI,CAAEe,CAAC,CAACC,MAAM,CAACH,KAAK,EAAE,CAAE,CACpEP,SAAS,CAAC,yGAAyG,CACnHM,WAAW,CAAC,iBAAiB,CAC9B,CAAC,EACC,CAAC,cACNjE,KAAA,QAAA4D,QAAA,eACE9D,IAAA,UAAO6D,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,aAAW,CAAO,CAAC,cAC9E9D,IAAA,aACEuG,QAAQ,MACRnC,KAAK,CAAE8B,QAAQ,CAACxC,WAAW,EAAI,EAAG,CAClCW,QAAQ,CAAGC,CAAC,EAAK6B,WAAW,CAAAK,aAAA,CAAAA,aAAA,IAAMN,QAAQ,MAAExC,WAAW,CAAEY,CAAC,CAACC,MAAM,CAACH,KAAK,EAAE,CAAE,CAC3EP,SAAS,CAAC,yGAAyG,CACnH4C,IAAI,CAAE,CAAE,CACRtC,WAAW,CAAC,0BAA0B,CACvC,CAAC,EACC,CAAC,cACNjE,KAAA,QAAA4D,QAAA,eACE9D,IAAA,UAAO6D,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,UAAQ,CAAO,CAAC,cAC3E9D,IAAA,UACEkE,IAAI,CAAC,MAAM,CACXqC,QAAQ,MACRnC,KAAK,CAAE8B,QAAQ,CAACR,QAAQ,EAAI,EAAG,CAC/BrB,QAAQ,CAAGC,CAAC,EAAK6B,WAAW,CAAAK,aAAA,CAAAA,aAAA,IAAMN,QAAQ,MAAER,QAAQ,CAAEpB,CAAC,CAACC,MAAM,CAACH,KAAK,EAAE,CAAE,CACxEP,SAAS,CAAC,yGAAyG,CACnHM,WAAW,CAAC,WAAW,CACxB,CAAC,EACC,CAAC,cACNjE,KAAA,QAAA4D,QAAA,eACE9D,IAAA,UAAO6D,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,QAAM,CAAO,CAAC,cACzE5D,KAAA,WACEqG,QAAQ,MACRnC,KAAK,CAAE8B,QAAQ,CAACP,MAAM,EAAI,EAAG,CAC7BtB,QAAQ,CAAGC,CAAC,EAAK6B,WAAW,CAAAK,aAAA,CAAAA,aAAA,IAAMN,QAAQ,MAAEP,MAAM,CAAErB,CAAC,CAACC,MAAM,CAACH,KAAK,EAAE,CAAE,CACtEP,SAAS,CAAC,yGAAyG,CAAAC,QAAA,eAEnH9D,IAAA,WAAQoE,KAAK,CAAC,EAAE,CAAAN,QAAA,CAAC,eAAa,CAAQ,CAAC,cACvC9D,IAAA,WAAQoE,KAAK,CAAC,MAAM,CAAAN,QAAA,CAAC,MAAI,CAAQ,CAAC,cAClC9D,IAAA,WAAQoE,KAAK,CAAC,OAAO,CAAAN,QAAA,CAAC,OAAK,CAAQ,CAAC,cACpC9D,IAAA,WAAQoE,KAAK,CAAC,SAAS,CAAAN,QAAA,CAAC,SAAO,CAAQ,CAAC,cACxC9D,IAAA,WAAQoE,KAAK,CAAC,QAAQ,CAAAN,QAAA,CAAC,QAAM,CAAQ,CAAC,EAChC,CAAC,EACN,CAAC,cACN5D,KAAA,QAAA4D,QAAA,eACE9D,IAAA,UAAO6D,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,UAAQ,CAAO,CAAC,cAC3E9D,IAAA,UACEkE,IAAI,CAAC,MAAM,CACXqC,QAAQ,MACRnC,KAAK,CAAE8B,QAAQ,CAACN,QAAQ,EAAI,EAAG,CAC/BvB,QAAQ,CAAGC,CAAC,EAAK6B,WAAW,CAAAK,aAAA,CAAAA,aAAA,IAAMN,QAAQ,MAAEN,QAAQ,CAAEtB,CAAC,CAACC,MAAM,CAACH,KAAK,EAAE,CAAE,CACxEP,SAAS,CAAC,yGAAyG,CACnHM,WAAW,CAAC,WAAW,CACxB,CAAC,EACC,CAAC,EACN,CAAC,CAEP,IAAK,UAAU,CACb,mBACEjE,KAAA,CAAAE,SAAA,EAAA0D,QAAA,eACE5D,KAAA,QAAA4D,QAAA,eACE9D,IAAA,UAAO6D,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,WAAS,CAAO,CAAC,cAC5E9D,IAAA,UACEkE,IAAI,CAAC,MAAM,CACXqC,QAAQ,MACRnC,KAAK,CAAE8B,QAAQ,CAAChD,EAAE,EAAI,EAAG,CACzBmB,QAAQ,CAAGC,CAAC,EAAK6B,WAAW,CAAAK,aAAA,CAAAA,aAAA,IAAMN,QAAQ,MAAEhD,EAAE,CAAEoB,CAAC,CAACC,MAAM,CAACH,KAAK,EAAE,CAAE,CAClEP,SAAS,CAAC,yGAAyG,CACnHM,WAAW,CAAC,aAAa,CAC1B,CAAC,EACC,CAAC,cACNjE,KAAA,QAAA4D,QAAA,eACE9D,IAAA,UAAO6D,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,MAAI,CAAO,CAAC,cACvE9D,IAAA,UACEkE,IAAI,CAAC,MAAM,CACXqC,QAAQ,MACRnC,KAAK,CAAE8B,QAAQ,CAAC3C,IAAI,EAAI,EAAG,CAC3Bc,QAAQ,CAAGC,CAAC,EAAK6B,WAAW,CAAAK,aAAA,CAAAA,aAAA,IAAMN,QAAQ,MAAE3C,IAAI,CAAEe,CAAC,CAACC,MAAM,CAACH,KAAK,EAAE,CAAE,CACpEP,SAAS,CAAC,yGAAyG,CACnHM,WAAW,CAAC,aAAa,CAC1B,CAAC,EACC,CAAC,cACNjE,KAAA,QAAA4D,QAAA,eACE9D,IAAA,UAAO6D,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,aAAW,CAAO,CAAC,cAC9E9D,IAAA,aACEuG,QAAQ,MACRnC,KAAK,CAAE8B,QAAQ,CAACxC,WAAW,EAAI,EAAG,CAClCW,QAAQ,CAAGC,CAAC,EAAK6B,WAAW,CAAAK,aAAA,CAAAA,aAAA,IAAMN,QAAQ,MAAExC,WAAW,CAAEY,CAAC,CAACC,MAAM,CAACH,KAAK,EAAE,CAAE,CAC3EP,SAAS,CAAC,yGAAyG,CACnH4C,IAAI,CAAE,CAAE,CACRtC,WAAW,CAAC,sBAAsB,CACnC,CAAC,EACC,CAAC,cACNjE,KAAA,QAAA4D,QAAA,eACE9D,IAAA,UAAO6D,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,QAAM,CAAO,CAAC,cACzE5D,KAAA,WACEqG,QAAQ,MACRnC,KAAK,CAAE8B,QAAQ,CAACJ,MAAM,EAAI,EAAG,CAC7BzB,QAAQ,CAAGC,CAAC,EAAK6B,WAAW,CAAAK,aAAA,CAAAA,aAAA,IAAMN,QAAQ,MAAEJ,MAAM,CAAExB,CAAC,CAACC,MAAM,CAACH,KAAK,EAAE,CAAE,CACtEP,SAAS,CAAC,yGAAyG,CAAAC,QAAA,eAEnH9D,IAAA,WAAQoE,KAAK,CAAC,EAAE,CAAAN,QAAA,CAAC,eAAa,CAAQ,CAAC,cACvC9D,IAAA,WAAQoE,KAAK,CAAC,OAAO,CAAAN,QAAA,CAAC,OAAK,CAAQ,CAAC,cACpC9D,IAAA,WAAQoE,KAAK,CAAC,MAAM,CAAAN,QAAA,CAAC,MAAI,CAAQ,CAAC,EAC5B,CAAC,EACN,CAAC,cACN5D,KAAA,QAAA4D,QAAA,eACE9D,IAAA,UAAO6D,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,UAAQ,CAAO,CAAC,cAC3E9D,IAAA,UACEkE,IAAI,CAAC,MAAM,CACXE,KAAK,CAAE8B,QAAQ,CAACW,QAAQ,EAAI,EAAG,CAC/BxC,QAAQ,CAAGC,CAAC,EAAK6B,WAAW,CAAAK,aAAA,CAAAA,aAAA,IAAMN,QAAQ,MAAEW,QAAQ,CAAEvC,CAAC,CAACC,MAAM,CAACH,KAAK,CAACsC,KAAK,CAAC,GAAG,CAAC,CAAC1C,GAAG,CAAE8C,CAAS,EAAKA,CAAC,CAACF,IAAI,CAAC,CAAC,CAAC,EAAE,CAAE,CAChH/C,SAAS,CAAC,yGAAyG,CACnHM,WAAW,CAAC,uBAAuB,CACpC,CAAC,cACFnE,IAAA,MAAG6D,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,kCAAgC,CAAG,CAAC,EAC3E,CAAC,cACN5D,KAAA,QAAA4D,QAAA,eACE9D,IAAA,UAAO6D,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,WAAS,CAAO,CAAC,cAC5E9D,IAAA,UACEkE,IAAI,CAAC,MAAM,CACXE,KAAK,CAAE8B,QAAQ,CAACF,SAAS,EAAI,EAAG,CAChC3B,QAAQ,CAAGC,CAAC,EAAK6B,WAAW,CAAAK,aAAA,CAAAA,aAAA,IAAMN,QAAQ,MAAEF,SAAS,CAAE1B,CAAC,CAACC,MAAM,CAACH,KAAK,CAACsC,KAAK,CAAC,GAAG,CAAC,CAAC1C,GAAG,CAAE+C,CAAS,EAAKA,CAAC,CAACH,IAAI,CAAC,CAAC,CAAC,EAAE,CAAE,CACjH/C,SAAS,CAAC,yGAAyG,CACnHM,WAAW,CAAC,sBAAsB,CACnC,CAAC,cACFnE,IAAA,MAAG6D,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,mCAAiC,CAAG,CAAC,EAC5E,CAAC,cACN5D,KAAA,QAAA4D,QAAA,eACE9D,IAAA,UAAO6D,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,SAAO,CAAO,CAAC,cAC1E9D,IAAA,UACEkE,IAAI,CAAC,MAAM,CACXE,KAAK,CAAE8B,QAAQ,CAACc,OAAO,EAAI,EAAG,CAC9B3C,QAAQ,CAAGC,CAAC,EAAK6B,WAAW,CAAAK,aAAA,CAAAA,aAAA,IAAMN,QAAQ,MAAEc,OAAO,CAAE1C,CAAC,CAACC,MAAM,CAACH,KAAK,CAACsC,KAAK,CAAC,GAAG,CAAC,CAAC1C,GAAG,CAAEiD,CAAS,EAAKA,CAAC,CAACL,IAAI,CAAC,CAAC,CAAC,EAAE,CAAE,CAC/G/C,SAAS,CAAC,yGAAyG,CACnHM,WAAW,CAAC,sBAAsB,CACnC,CAAC,cACFnE,IAAA,MAAG6D,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,iCAA+B,CAAG,CAAC,EAC1E,CAAC,cACN5D,KAAA,QAAK2D,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC9D,IAAA,UACEkE,IAAI,CAAC,UAAU,CACfgD,OAAO,CAAEhB,QAAQ,CAACH,OAAO,EAAI,KAAM,CACnC1B,QAAQ,CAAGC,CAAC,EAAK6B,WAAW,CAAAK,aAAA,CAAAA,aAAA,IAAMN,QAAQ,MAAEH,OAAO,CAAEzB,CAAC,CAACC,MAAM,CAAC2C,OAAO,EAAE,CAAE,CACzErD,SAAS,CAAC,mEAAmE,CAC9E,CAAC,cACF7D,IAAA,UAAO6D,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,eAEpD,CAAO,CAAC,EACL,CAAC,EACN,CAAC,CAEP,QACE,MAAO,KAAI,CACf,CACF,CAAC,CAED,mBACE5D,KAAA,SAAM+E,QAAQ,CAAEmB,YAAa,CAACvC,SAAS,CAAC,WAAW,CAAAC,QAAA,EAChDwC,UAAU,CAAC,CAAC,cACbpG,KAAA,QAAK2D,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC9C9D,IAAA,WACEkE,IAAI,CAAC,QAAQ,CACbH,OAAO,CAAEmB,QAAS,CAClBrB,SAAS,CAAC,gGAAgG,CAAAC,QAAA,CAC3G,QAED,CAAQ,CAAC,cACT5D,KAAA,WACEgE,IAAI,CAAC,QAAQ,CACbL,SAAS,CAAC,uHAAuH,CAAAC,QAAA,EAClI,SACQ,CAACI,IAAI,CAACnB,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,EACnB,CAAC,EACN,CAAC,EACF,CAAC,CAEX,CAAC,CAED,cAAe,CAAA1C,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}