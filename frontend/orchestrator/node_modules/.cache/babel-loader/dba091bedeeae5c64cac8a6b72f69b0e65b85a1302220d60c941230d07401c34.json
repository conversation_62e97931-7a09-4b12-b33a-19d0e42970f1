{"ast": null, "code": "import React,{useState,useEffect,useRef}from'react';import{useNavigate}from'react-router-dom';import{MagnifyingGlassIcon,XMarkIcon}from'@heroicons/react/24/outline';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const SearchField=()=>{const[query,setQuery]=useState('');const[isOpen,setIsOpen]=useState(false);const[showResults,setShowResults]=useState(false);const[results,setResults]=useState([]);const[selectedIndex,setSelectedIndex]=useState(-1);const navigate=useNavigate();const inputRef=useRef(null);const resultsRef=useRef(null);// Mock search data - in a real app, this would come from an API\nconst searchData=[{id:'1',label:'Dashboard',path:'/dashboard',category:'Navigation',description:'Main dashboard overview'},{id:'2',label:'Search Resources',path:'/search',category:'Navigation',description:'Search across all resources'},{id:'3',label:'Discovery Wizard',path:'/discovery',category:'Navigation',description:'Discover new resources'},{id:'4',label:'Workflows',path:'/workflows',category:'Navigation',description:'Manage workflows'},{id:'5',label:'Envoy Config',path:'/envoy',category:'Navigation',description:'Configure Envoy proxy'},{id:'6',label:'Autoscaler',path:'/autoscaler',category:'Navigation',description:'Autoscaling configuration'},{id:'7',label:'Audit Logs',path:'/audit',category:'Navigation',description:'View audit logs'},{id:'8',label:'Database Admin',path:'/admin/database',category:'Admin',description:'Database administration'},{id:'9',label:'User Management',path:'/admin/users',category:'Admin',description:'Manage users'},{id:'10',label:'RBAC Management',path:'/admin/rbac',category:'Admin',description:'Role-based access control'},{id:'11',label:'Feature Flags',path:'/admin/featureflags',category:'Admin',description:'Manage feature flags'},{id:'12',label:'Settings',path:'/admin/settings',category:'Admin',description:'System settings'}];const performSearch=searchQuery=>{if(!searchQuery.trim()){setResults([]);setShowResults(false);return;}const filtered=searchData.filter(item=>{var _item$description;return item.label.toLowerCase().includes(searchQuery.toLowerCase())||((_item$description=item.description)===null||_item$description===void 0?void 0:_item$description.toLowerCase().includes(searchQuery.toLowerCase()))||item.category.toLowerCase().includes(searchQuery.toLowerCase());});setResults(filtered);setShowResults(true);setSelectedIndex(-1);};const handleInputChange=e=>{const value=e.target.value;setQuery(value);performSearch(value);};const handleKeyDown=e=>{var _inputRef$current;if(!showResults||results.length===0)return;switch(e.key){case'ArrowDown':e.preventDefault();setSelectedIndex(prev=>prev<results.length-1?prev+1:0);break;case'ArrowUp':e.preventDefault();setSelectedIndex(prev=>prev>0?prev-1:results.length-1);break;case'Enter':e.preventDefault();if(selectedIndex>=0&&selectedIndex<results.length){handleResultClick(results[selectedIndex]);}else if(results.length>0){handleResultClick(results[0]);}break;case'Escape':setIsOpen(false);setShowResults(false);setQuery('');(_inputRef$current=inputRef.current)===null||_inputRef$current===void 0?void 0:_inputRef$current.blur();break;}};const handleResultClick=result=>{navigate(result.path);setIsOpen(false);setShowResults(false);setQuery('');};const handleSearchIconClick=()=>{setIsOpen(true);setTimeout(()=>{var _inputRef$current2;return(_inputRef$current2=inputRef.current)===null||_inputRef$current2===void 0?void 0:_inputRef$current2.focus();},100);};const handleClose=()=>{setIsOpen(false);setShowResults(false);setQuery('');};const handleClickOutside=e=>{if(resultsRef.current&&!resultsRef.current.contains(e.target)){setShowResults(false);}};useEffect(()=>{document.addEventListener('mousedown',handleClickOutside);return()=>document.removeEventListener('mousedown',handleClickOutside);},[]);// Global keyboard shortcut (/)\nuseEffect(()=>{const handleGlobalKeyDown=e=>{if(e.key==='/'&&e.target!==inputRef.current&&e.target.tagName!=='INPUT'){e.preventDefault();setIsOpen(true);setTimeout(()=>{var _inputRef$current3;return(_inputRef$current3=inputRef.current)===null||_inputRef$current3===void 0?void 0:_inputRef$current3.focus();},100);}};document.addEventListener('keydown',handleGlobalKeyDown);return()=>document.removeEventListener('keydown',handleGlobalKeyDown);},[]);if(!isOpen){return/*#__PURE__*/_jsx(\"button\",{onClick:handleSearchIconClick,className:\"p-2 text-gray-400 hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-md\",title:\"Search (Press / to focus)\",children:/*#__PURE__*/_jsx(MagnifyingGlassIcon,{className:\"h-5 w-5\"})});}return/*#__PURE__*/_jsxs(\"div\",{className:\"relative w-full max-w-lg\",ref:resultsRef,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none\",children:/*#__PURE__*/_jsx(MagnifyingGlassIcon,{className:\"h-5 w-5 text-gray-400\"})}),/*#__PURE__*/_jsx(\"input\",{ref:inputRef,type:\"text\",value:query,onChange:handleInputChange,onKeyDown:handleKeyDown,className:\"block w-full pl-10 pr-10 py-2 border border-gray-600 rounded-md bg-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",placeholder:\"Search pages, features...\",autoComplete:\"off\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handleClose,className:\"absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-300\",children:/*#__PURE__*/_jsx(XMarkIcon,{className:\"h-5 w-5\"})})]}),showResults&&results.length>0&&/*#__PURE__*/_jsx(\"div\",{className:\"absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-96 overflow-y-auto\",children:results.map((result,index)=>/*#__PURE__*/_jsx(\"div\",{onClick:()=>handleResultClick(result),className:\"px-4 py-3 cursor-pointer border-b border-gray-100 last:border-b-0 \".concat(index===selectedIndex?'bg-blue-50':'hover:bg-gray-50'),children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-sm font-medium text-gray-900\",children:result.label}),result.description&&/*#__PURE__*/_jsx(\"div\",{className:\"text-xs text-gray-500 mt-1\",children:result.description})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs text-gray-400 ml-2\",children:result.category})]})},result.id))}),showResults&&query&&results.length===0&&/*#__PURE__*/_jsx(\"div\",{className:\"absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"px-4 py-3 text-sm text-gray-500 text-center\",children:[\"No results found for \\\"\",query,\"\\\"\"]})})]});};export default SearchField;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useNavigate", "MagnifyingGlassIcon", "XMarkIcon", "jsx", "_jsx", "jsxs", "_jsxs", "SearchField", "query", "<PERSON><PERSON><PERSON><PERSON>", "isOpen", "setIsOpen", "showResults", "setShowResults", "results", "setResults", "selectedIndex", "setSelectedIndex", "navigate", "inputRef", "resultsRef", "searchData", "id", "label", "path", "category", "description", "performSearch", "searchQuery", "trim", "filtered", "filter", "item", "_item$description", "toLowerCase", "includes", "handleInputChange", "e", "value", "target", "handleKeyDown", "_inputRef$current", "length", "key", "preventDefault", "prev", "handleResultClick", "current", "blur", "result", "handleSearchIconClick", "setTimeout", "_inputRef$current2", "focus", "handleClose", "handleClickOutside", "contains", "document", "addEventListener", "removeEventListener", "handleGlobalKeyDown", "tagName", "_inputRef$current3", "onClick", "className", "title", "children", "ref", "type", "onChange", "onKeyDown", "placeholder", "autoComplete", "map", "index", "concat"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/components/SearchField.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { MagnifyingGlassIcon, XMarkIcon } from '@heroicons/react/24/outline';\n\ninterface SearchResult {\n  id: string;\n  label: string;\n  path: string;\n  category: string;\n  description?: string;\n}\n\nconst SearchField: React.FC = () => {\n  const [query, setQuery] = useState('');\n  const [isOpen, setIsOpen] = useState(false);\n  const [showResults, setShowResults] = useState(false);\n  const [results, setResults] = useState<SearchResult[]>([]);\n  const [selectedIndex, setSelectedIndex] = useState(-1);\n  const navigate = useNavigate();\n  const inputRef = useRef<HTMLInputElement>(null);\n  const resultsRef = useRef<HTMLDivElement>(null);\n\n  // Mock search data - in a real app, this would come from an API\n  const searchData: SearchResult[] = [\n    { id: '1', label: 'Dashboard', path: '/dashboard', category: 'Navigation', description: 'Main dashboard overview' },\n    { id: '2', label: 'Search Resources', path: '/search', category: 'Navigation', description: 'Search across all resources' },\n    { id: '3', label: 'Discovery Wizard', path: '/discovery', category: 'Navigation', description: 'Discover new resources' },\n    { id: '4', label: 'Workflows', path: '/workflows', category: 'Navigation', description: 'Manage workflows' },\n    { id: '5', label: 'Envoy Config', path: '/envoy', category: 'Navigation', description: 'Configure Envoy proxy' },\n    { id: '6', label: 'Autoscaler', path: '/autoscaler', category: 'Navigation', description: 'Autoscaling configuration' },\n    { id: '7', label: 'Audit Logs', path: '/audit', category: 'Navigation', description: 'View audit logs' },\n    { id: '8', label: 'Database Admin', path: '/admin/database', category: 'Admin', description: 'Database administration' },\n    { id: '9', label: 'User Management', path: '/admin/users', category: 'Admin', description: 'Manage users' },\n    { id: '10', label: 'RBAC Management', path: '/admin/rbac', category: 'Admin', description: 'Role-based access control' },\n    { id: '11', label: 'Feature Flags', path: '/admin/featureflags', category: 'Admin', description: 'Manage feature flags' },\n    { id: '12', label: 'Settings', path: '/admin/settings', category: 'Admin', description: 'System settings' },\n  ];\n\n  const performSearch = (searchQuery: string) => {\n    if (!searchQuery.trim()) {\n      setResults([]);\n      setShowResults(false);\n      return;\n    }\n\n    const filtered = searchData.filter(item =>\n      item.label.toLowerCase().includes(searchQuery.toLowerCase()) ||\n      item.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||\n      item.category.toLowerCase().includes(searchQuery.toLowerCase())\n    );\n\n    setResults(filtered);\n    setShowResults(true);\n    setSelectedIndex(-1);\n  };\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const value = e.target.value;\n    setQuery(value);\n    performSearch(value);\n  };\n\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (!showResults || results.length === 0) return;\n\n    switch (e.key) {\n      case 'ArrowDown':\n        e.preventDefault();\n        setSelectedIndex(prev => (prev < results.length - 1 ? prev + 1 : 0));\n        break;\n      case 'ArrowUp':\n        e.preventDefault();\n        setSelectedIndex(prev => (prev > 0 ? prev - 1 : results.length - 1));\n        break;\n      case 'Enter':\n        e.preventDefault();\n        if (selectedIndex >= 0 && selectedIndex < results.length) {\n          handleResultClick(results[selectedIndex]);\n        } else if (results.length > 0) {\n          handleResultClick(results[0]);\n        }\n        break;\n      case 'Escape':\n        setIsOpen(false);\n        setShowResults(false);\n        setQuery('');\n        inputRef.current?.blur();\n        break;\n    }\n  };\n\n  const handleResultClick = (result: SearchResult) => {\n    navigate(result.path);\n    setIsOpen(false);\n    setShowResults(false);\n    setQuery('');\n  };\n\n  const handleSearchIconClick = () => {\n    setIsOpen(true);\n    setTimeout(() => inputRef.current?.focus(), 100);\n  };\n\n  const handleClose = () => {\n    setIsOpen(false);\n    setShowResults(false);\n    setQuery('');\n  };\n\n  const handleClickOutside = (e: MouseEvent) => {\n    if (resultsRef.current && !resultsRef.current.contains(e.target as Node)) {\n      setShowResults(false);\n    }\n  };\n\n  useEffect(() => {\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  // Global keyboard shortcut (/)\n  useEffect(() => {\n    const handleGlobalKeyDown = (e: KeyboardEvent) => {\n      if (e.key === '/' && e.target !== inputRef.current && (e.target as HTMLElement).tagName !== 'INPUT') {\n        e.preventDefault();\n        setIsOpen(true);\n        setTimeout(() => inputRef.current?.focus(), 100);\n      }\n    };\n\n    document.addEventListener('keydown', handleGlobalKeyDown);\n    return () => document.removeEventListener('keydown', handleGlobalKeyDown);\n  }, []);\n\n  if (!isOpen) {\n    return (\n      <button\n        onClick={handleSearchIconClick}\n        className=\"p-2 text-gray-400 hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-md\"\n        title=\"Search (Press / to focus)\"\n      >\n        <MagnifyingGlassIcon className=\"h-5 w-5\" />\n      </button>\n    );\n  }\n\n  return (\n    <div className=\"relative w-full max-w-lg\" ref={resultsRef}>\n      <div className=\"relative\">\n        <div className=\"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none\">\n          <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n        </div>\n        <input\n          ref={inputRef}\n          type=\"text\"\n          value={query}\n          onChange={handleInputChange}\n          onKeyDown={handleKeyDown}\n          className=\"block w-full pl-10 pr-10 py-2 border border-gray-600 rounded-md bg-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          placeholder=\"Search pages, features...\"\n          autoComplete=\"off\"\n        />\n        <button\n          onClick={handleClose}\n          className=\"absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-300\"\n        >\n          <XMarkIcon className=\"h-5 w-5\" />\n        </button>\n      </div>\n\n      {/* Search Results */}\n      {showResults && results.length > 0 && (\n        <div className=\"absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-96 overflow-y-auto\">\n          {results.map((result, index) => (\n            <div\n              key={result.id}\n              onClick={() => handleResultClick(result)}\n              className={`px-4 py-3 cursor-pointer border-b border-gray-100 last:border-b-0 ${\n                index === selectedIndex ? 'bg-blue-50' : 'hover:bg-gray-50'\n              }`}\n            >\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex-1\">\n                  <div className=\"text-sm font-medium text-gray-900\">{result.label}</div>\n                  {result.description && (\n                    <div className=\"text-xs text-gray-500 mt-1\">{result.description}</div>\n                  )}\n                </div>\n                <div className=\"text-xs text-gray-400 ml-2\">{result.category}</div>\n              </div>\n            </div>\n          ))}\n        </div>\n      )}\n\n      {/* No Results */}\n      {showResults && query && results.length === 0 && (\n        <div className=\"absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg\">\n          <div className=\"px-4 py-3 text-sm text-gray-500 text-center\">\n            No results found for \"{query}\"\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default SearchField;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,MAAM,KAAQ,OAAO,CAC1D,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,mBAAmB,CAAEC,SAAS,KAAQ,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAU7E,KAAM,CAAAC,WAAqB,CAAGA,CAAA,GAAM,CAClC,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAGZ,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACa,MAAM,CAAEC,SAAS,CAAC,CAAGd,QAAQ,CAAC,KAAK,CAAC,CAC3C,KAAM,CAACe,WAAW,CAAEC,cAAc,CAAC,CAAGhB,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACiB,OAAO,CAAEC,UAAU,CAAC,CAAGlB,QAAQ,CAAiB,EAAE,CAAC,CAC1D,KAAM,CAACmB,aAAa,CAAEC,gBAAgB,CAAC,CAAGpB,QAAQ,CAAC,CAAC,CAAC,CAAC,CACtD,KAAM,CAAAqB,QAAQ,CAAGlB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAmB,QAAQ,CAAGpB,MAAM,CAAmB,IAAI,CAAC,CAC/C,KAAM,CAAAqB,UAAU,CAAGrB,MAAM,CAAiB,IAAI,CAAC,CAE/C;AACA,KAAM,CAAAsB,UAA0B,CAAG,CACjC,CAAEC,EAAE,CAAE,GAAG,CAAEC,KAAK,CAAE,WAAW,CAAEC,IAAI,CAAE,YAAY,CAAEC,QAAQ,CAAE,YAAY,CAAEC,WAAW,CAAE,yBAA0B,CAAC,CACnH,CAAEJ,EAAE,CAAE,GAAG,CAAEC,KAAK,CAAE,kBAAkB,CAAEC,IAAI,CAAE,SAAS,CAAEC,QAAQ,CAAE,YAAY,CAAEC,WAAW,CAAE,6BAA8B,CAAC,CAC3H,CAAEJ,EAAE,CAAE,GAAG,CAAEC,KAAK,CAAE,kBAAkB,CAAEC,IAAI,CAAE,YAAY,CAAEC,QAAQ,CAAE,YAAY,CAAEC,WAAW,CAAE,wBAAyB,CAAC,CACzH,CAAEJ,EAAE,CAAE,GAAG,CAAEC,KAAK,CAAE,WAAW,CAAEC,IAAI,CAAE,YAAY,CAAEC,QAAQ,CAAE,YAAY,CAAEC,WAAW,CAAE,kBAAmB,CAAC,CAC5G,CAAEJ,EAAE,CAAE,GAAG,CAAEC,KAAK,CAAE,cAAc,CAAEC,IAAI,CAAE,QAAQ,CAAEC,QAAQ,CAAE,YAAY,CAAEC,WAAW,CAAE,uBAAwB,CAAC,CAChH,CAAEJ,EAAE,CAAE,GAAG,CAAEC,KAAK,CAAE,YAAY,CAAEC,IAAI,CAAE,aAAa,CAAEC,QAAQ,CAAE,YAAY,CAAEC,WAAW,CAAE,2BAA4B,CAAC,CACvH,CAAEJ,EAAE,CAAE,GAAG,CAAEC,KAAK,CAAE,YAAY,CAAEC,IAAI,CAAE,QAAQ,CAAEC,QAAQ,CAAE,YAAY,CAAEC,WAAW,CAAE,iBAAkB,CAAC,CACxG,CAAEJ,EAAE,CAAE,GAAG,CAAEC,KAAK,CAAE,gBAAgB,CAAEC,IAAI,CAAE,iBAAiB,CAAEC,QAAQ,CAAE,OAAO,CAAEC,WAAW,CAAE,yBAA0B,CAAC,CACxH,CAAEJ,EAAE,CAAE,GAAG,CAAEC,KAAK,CAAE,iBAAiB,CAAEC,IAAI,CAAE,cAAc,CAAEC,QAAQ,CAAE,OAAO,CAAEC,WAAW,CAAE,cAAe,CAAC,CAC3G,CAAEJ,EAAE,CAAE,IAAI,CAAEC,KAAK,CAAE,iBAAiB,CAAEC,IAAI,CAAE,aAAa,CAAEC,QAAQ,CAAE,OAAO,CAAEC,WAAW,CAAE,2BAA4B,CAAC,CACxH,CAAEJ,EAAE,CAAE,IAAI,CAAEC,KAAK,CAAE,eAAe,CAAEC,IAAI,CAAE,qBAAqB,CAAEC,QAAQ,CAAE,OAAO,CAAEC,WAAW,CAAE,sBAAuB,CAAC,CACzH,CAAEJ,EAAE,CAAE,IAAI,CAAEC,KAAK,CAAE,UAAU,CAAEC,IAAI,CAAE,iBAAiB,CAAEC,QAAQ,CAAE,OAAO,CAAEC,WAAW,CAAE,iBAAkB,CAAC,CAC5G,CAED,KAAM,CAAAC,aAAa,CAAIC,WAAmB,EAAK,CAC7C,GAAI,CAACA,WAAW,CAACC,IAAI,CAAC,CAAC,CAAE,CACvBd,UAAU,CAAC,EAAE,CAAC,CACdF,cAAc,CAAC,KAAK,CAAC,CACrB,OACF,CAEA,KAAM,CAAAiB,QAAQ,CAAGT,UAAU,CAACU,MAAM,CAACC,IAAI,OAAAC,iBAAA,OACrC,CAAAD,IAAI,CAACT,KAAK,CAACW,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACP,WAAW,CAACM,WAAW,CAAC,CAAC,CAAC,IAAAD,iBAAA,CAC5DD,IAAI,CAACN,WAAW,UAAAO,iBAAA,iBAAhBA,iBAAA,CAAkBC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACP,WAAW,CAACM,WAAW,CAAC,CAAC,CAAC,GACnEF,IAAI,CAACP,QAAQ,CAACS,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACP,WAAW,CAACM,WAAW,CAAC,CAAC,CAAC,EACjE,CAAC,CAEDnB,UAAU,CAACe,QAAQ,CAAC,CACpBjB,cAAc,CAAC,IAAI,CAAC,CACpBI,gBAAgB,CAAC,CAAC,CAAC,CAAC,CACtB,CAAC,CAED,KAAM,CAAAmB,iBAAiB,CAAIC,CAAsC,EAAK,CACpE,KAAM,CAAAC,KAAK,CAAGD,CAAC,CAACE,MAAM,CAACD,KAAK,CAC5B7B,QAAQ,CAAC6B,KAAK,CAAC,CACfX,aAAa,CAACW,KAAK,CAAC,CACtB,CAAC,CAED,KAAM,CAAAE,aAAa,CAAIH,CAAsB,EAAK,KAAAI,iBAAA,CAChD,GAAI,CAAC7B,WAAW,EAAIE,OAAO,CAAC4B,MAAM,GAAK,CAAC,CAAE,OAE1C,OAAQL,CAAC,CAACM,GAAG,EACX,IAAK,WAAW,CACdN,CAAC,CAACO,cAAc,CAAC,CAAC,CAClB3B,gBAAgB,CAAC4B,IAAI,EAAKA,IAAI,CAAG/B,OAAO,CAAC4B,MAAM,CAAG,CAAC,CAAGG,IAAI,CAAG,CAAC,CAAG,CAAE,CAAC,CACpE,MACF,IAAK,SAAS,CACZR,CAAC,CAACO,cAAc,CAAC,CAAC,CAClB3B,gBAAgB,CAAC4B,IAAI,EAAKA,IAAI,CAAG,CAAC,CAAGA,IAAI,CAAG,CAAC,CAAG/B,OAAO,CAAC4B,MAAM,CAAG,CAAE,CAAC,CACpE,MACF,IAAK,OAAO,CACVL,CAAC,CAACO,cAAc,CAAC,CAAC,CAClB,GAAI5B,aAAa,EAAI,CAAC,EAAIA,aAAa,CAAGF,OAAO,CAAC4B,MAAM,CAAE,CACxDI,iBAAiB,CAAChC,OAAO,CAACE,aAAa,CAAC,CAAC,CAC3C,CAAC,IAAM,IAAIF,OAAO,CAAC4B,MAAM,CAAG,CAAC,CAAE,CAC7BI,iBAAiB,CAAChC,OAAO,CAAC,CAAC,CAAC,CAAC,CAC/B,CACA,MACF,IAAK,QAAQ,CACXH,SAAS,CAAC,KAAK,CAAC,CAChBE,cAAc,CAAC,KAAK,CAAC,CACrBJ,QAAQ,CAAC,EAAE,CAAC,CACZ,CAAAgC,iBAAA,CAAAtB,QAAQ,CAAC4B,OAAO,UAAAN,iBAAA,iBAAhBA,iBAAA,CAAkBO,IAAI,CAAC,CAAC,CACxB,MACJ,CACF,CAAC,CAED,KAAM,CAAAF,iBAAiB,CAAIG,MAAoB,EAAK,CAClD/B,QAAQ,CAAC+B,MAAM,CAACzB,IAAI,CAAC,CACrBb,SAAS,CAAC,KAAK,CAAC,CAChBE,cAAc,CAAC,KAAK,CAAC,CACrBJ,QAAQ,CAAC,EAAE,CAAC,CACd,CAAC,CAED,KAAM,CAAAyC,qBAAqB,CAAGA,CAAA,GAAM,CAClCvC,SAAS,CAAC,IAAI,CAAC,CACfwC,UAAU,CAAC,SAAAC,kBAAA,QAAAA,kBAAA,CAAMjC,QAAQ,CAAC4B,OAAO,UAAAK,kBAAA,iBAAhBA,kBAAA,CAAkBC,KAAK,CAAC,CAAC,GAAE,GAAG,CAAC,CAClD,CAAC,CAED,KAAM,CAAAC,WAAW,CAAGA,CAAA,GAAM,CACxB3C,SAAS,CAAC,KAAK,CAAC,CAChBE,cAAc,CAAC,KAAK,CAAC,CACrBJ,QAAQ,CAAC,EAAE,CAAC,CACd,CAAC,CAED,KAAM,CAAA8C,kBAAkB,CAAIlB,CAAa,EAAK,CAC5C,GAAIjB,UAAU,CAAC2B,OAAO,EAAI,CAAC3B,UAAU,CAAC2B,OAAO,CAACS,QAAQ,CAACnB,CAAC,CAACE,MAAc,CAAC,CAAE,CACxE1B,cAAc,CAAC,KAAK,CAAC,CACvB,CACF,CAAC,CAEDf,SAAS,CAAC,IAAM,CACd2D,QAAQ,CAACC,gBAAgB,CAAC,WAAW,CAAEH,kBAAkB,CAAC,CAC1D,MAAO,IAAME,QAAQ,CAACE,mBAAmB,CAAC,WAAW,CAAEJ,kBAAkB,CAAC,CAC5E,CAAC,CAAE,EAAE,CAAC,CAEN;AACAzD,SAAS,CAAC,IAAM,CACd,KAAM,CAAA8D,mBAAmB,CAAIvB,CAAgB,EAAK,CAChD,GAAIA,CAAC,CAACM,GAAG,GAAK,GAAG,EAAIN,CAAC,CAACE,MAAM,GAAKpB,QAAQ,CAAC4B,OAAO,EAAKV,CAAC,CAACE,MAAM,CAAiBsB,OAAO,GAAK,OAAO,CAAE,CACnGxB,CAAC,CAACO,cAAc,CAAC,CAAC,CAClBjC,SAAS,CAAC,IAAI,CAAC,CACfwC,UAAU,CAAC,SAAAW,kBAAA,QAAAA,kBAAA,CAAM3C,QAAQ,CAAC4B,OAAO,UAAAe,kBAAA,iBAAhBA,kBAAA,CAAkBT,KAAK,CAAC,CAAC,GAAE,GAAG,CAAC,CAClD,CACF,CAAC,CAEDI,QAAQ,CAACC,gBAAgB,CAAC,SAAS,CAAEE,mBAAmB,CAAC,CACzD,MAAO,IAAMH,QAAQ,CAACE,mBAAmB,CAAC,SAAS,CAAEC,mBAAmB,CAAC,CAC3E,CAAC,CAAE,EAAE,CAAC,CAEN,GAAI,CAAClD,MAAM,CAAE,CACX,mBACEN,IAAA,WACE2D,OAAO,CAAEb,qBAAsB,CAC/Bc,SAAS,CAAC,sGAAsG,CAChHC,KAAK,CAAC,2BAA2B,CAAAC,QAAA,cAEjC9D,IAAA,CAACH,mBAAmB,EAAC+D,SAAS,CAAC,SAAS,CAAE,CAAC,CACrC,CAAC,CAEb,CAEA,mBACE1D,KAAA,QAAK0D,SAAS,CAAC,0BAA0B,CAACG,GAAG,CAAE/C,UAAW,CAAA8C,QAAA,eACxD5D,KAAA,QAAK0D,SAAS,CAAC,UAAU,CAAAE,QAAA,eACvB9D,IAAA,QAAK4D,SAAS,CAAC,sEAAsE,CAAAE,QAAA,cACnF9D,IAAA,CAACH,mBAAmB,EAAC+D,SAAS,CAAC,uBAAuB,CAAE,CAAC,CACtD,CAAC,cACN5D,IAAA,UACE+D,GAAG,CAAEhD,QAAS,CACdiD,IAAI,CAAC,MAAM,CACX9B,KAAK,CAAE9B,KAAM,CACb6D,QAAQ,CAAEjC,iBAAkB,CAC5BkC,SAAS,CAAE9B,aAAc,CACzBwB,SAAS,CAAC,0LAA0L,CACpMO,WAAW,CAAC,2BAA2B,CACvCC,YAAY,CAAC,KAAK,CACnB,CAAC,cACFpE,IAAA,WACE2D,OAAO,CAAET,WAAY,CACrBU,SAAS,CAAC,qFAAqF,CAAAE,QAAA,cAE/F9D,IAAA,CAACF,SAAS,EAAC8D,SAAS,CAAC,SAAS,CAAE,CAAC,CAC3B,CAAC,EACN,CAAC,CAGLpD,WAAW,EAAIE,OAAO,CAAC4B,MAAM,CAAG,CAAC,eAChCtC,IAAA,QAAK4D,SAAS,CAAC,yGAAyG,CAAAE,QAAA,CACrHpD,OAAO,CAAC2D,GAAG,CAAC,CAACxB,MAAM,CAAEyB,KAAK,gBACzBtE,IAAA,QAEE2D,OAAO,CAAEA,CAAA,GAAMjB,iBAAiB,CAACG,MAAM,CAAE,CACzCe,SAAS,sEAAAW,MAAA,CACPD,KAAK,GAAK1D,aAAa,CAAG,YAAY,CAAG,kBAAkB,CAC1D,CAAAkD,QAAA,cAEH5D,KAAA,QAAK0D,SAAS,CAAC,mCAAmC,CAAAE,QAAA,eAChD5D,KAAA,QAAK0D,SAAS,CAAC,QAAQ,CAAAE,QAAA,eACrB9D,IAAA,QAAK4D,SAAS,CAAC,mCAAmC,CAAAE,QAAA,CAAEjB,MAAM,CAAC1B,KAAK,CAAM,CAAC,CACtE0B,MAAM,CAACvB,WAAW,eACjBtB,IAAA,QAAK4D,SAAS,CAAC,4BAA4B,CAAAE,QAAA,CAAEjB,MAAM,CAACvB,WAAW,CAAM,CACtE,EACE,CAAC,cACNtB,IAAA,QAAK4D,SAAS,CAAC,4BAA4B,CAAAE,QAAA,CAAEjB,MAAM,CAACxB,QAAQ,CAAM,CAAC,EAChE,CAAC,EAdDwB,MAAM,CAAC3B,EAeT,CACN,CAAC,CACC,CACN,CAGAV,WAAW,EAAIJ,KAAK,EAAIM,OAAO,CAAC4B,MAAM,GAAK,CAAC,eAC3CtC,IAAA,QAAK4D,SAAS,CAAC,gFAAgF,CAAAE,QAAA,cAC7F5D,KAAA,QAAK0D,SAAS,CAAC,6CAA6C,CAAAE,QAAA,EAAC,yBACrC,CAAC1D,KAAK,CAAC,IAC/B,EAAK,CAAC,CACH,CACN,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAAD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}