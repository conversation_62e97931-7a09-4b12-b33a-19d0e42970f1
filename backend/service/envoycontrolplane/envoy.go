package envoycontrolplane

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/cainuro/orchestrator/internal/cache"
	"github.com/cainuro/orchestrator/internal/config"
	"github.com/cainuro/orchestrator/internal/db"
	orchestratorv1 "github.com/cainuro/orchestrator/proto/orchestrator/v1"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// Service defines the Envoy control plane service interface
type Service interface {
	PushSnapshot(ctx context.Context, req *orchestratorv1.PushSnapshotRequest) (*orchestratorv1.PushSnapshotResponse, error)
	GetSnapshot(ctx context.Context, req *orchestratorv1.GetSnapshotRequest) (*orchestratorv1.GetSnapshotResponse, error)
	StartXDSServer(ctx context.Context, port int) error
	GetActiveSnapshots() map[string]*EnvoySnapshot
}

// service implements the Service interface
type service struct {
	config *config.Config
	logger *zap.Logger
	cache  *cache.RistrettoCache
	store  *db.SQLiteStore

	// Snapshot management
	snapshots map[string]*EnvoySnapshot
	mu        sync.RWMutex

	// XDS server state
	xdsServer *XDSServer
}

// EnvoySnapshot represents an Envoy configuration snapshot
type EnvoySnapshot struct {
	Tenant    string                 `json:"tenant"`
	Version   string                 `json:"version"`
	Clusters  []EnvoyCluster         `json:"clusters"`
	Routes    []EnvoyRoute           `json:"routes"`
	Listeners []EnvoyListener        `json:"listeners"`
	Endpoints []EnvoyEndpoint        `json:"endpoints"`
	CreatedAt time.Time              `json:"created_at"`
	UpdatedAt time.Time              `json:"updated_at"`
	Metadata  map[string]interface{} `json:"metadata"`
}

// EnvoyCluster represents an Envoy cluster configuration
type EnvoyCluster struct {
	Name                string               `json:"name"`
	Type                string               `json:"type"` // STATIC, STRICT_DNS, LOGICAL_DNS, EDS
	ConnectTimeout      string               `json:"connect_timeout"`
	LoadBalancingPolicy string               `json:"lb_policy"`
	HealthChecks        []EnvoyHealthCheck   `json:"health_checks,omitempty"`
	Hosts               []EnvoyHost          `json:"hosts,omitempty"`
	CircuitBreakers     *EnvoyCircuitBreaker `json:"circuit_breakers,omitempty"`
}

// EnvoyRoute represents an Envoy route configuration
type EnvoyRoute struct {
	Name     string             `json:"name"`
	Domains  []string           `json:"domains"`
	Routes   []EnvoyRouteAction `json:"routes"`
	Metadata map[string]string  `json:"metadata,omitempty"`
}

// EnvoyListener represents an Envoy listener configuration
type EnvoyListener struct {
	Name         string                 `json:"name"`
	Address      string                 `json:"address"`
	Port         int                    `json:"port"`
	FilterChains []EnvoyFilterChain     `json:"filter_chains"`
	Metadata     map[string]interface{} `json:"metadata,omitempty"`
}

// EnvoyEndpoint represents an Envoy endpoint configuration
type EnvoyEndpoint struct {
	ClusterName string      `json:"cluster_name"`
	Endpoints   []EnvoyHost `json:"endpoints"`
}

// Supporting types
type EnvoyHealthCheck struct {
	Timeout            string `json:"timeout"`
	Interval           string `json:"interval"`
	UnhealthyThreshold int    `json:"unhealthy_threshold"`
	HealthyThreshold   int    `json:"healthy_threshold"`
	Path               string `json:"path,omitempty"`
	Method             string `json:"method,omitempty"`
}

type EnvoyHost struct {
	Address string `json:"address"`
	Port    int    `json:"port"`
	Weight  int    `json:"weight,omitempty"`
}

type EnvoyCircuitBreaker struct {
	MaxConnections     int `json:"max_connections"`
	MaxPendingRequests int `json:"max_pending_requests"`
	MaxRequests        int `json:"max_requests"`
	MaxRetries         int `json:"max_retries"`
}

type EnvoyRouteAction struct {
	Match    EnvoyRouteMatch  `json:"match"`
	Route    *EnvoyRouteRoute `json:"route,omitempty"`
	Redirect *EnvoyRedirect   `json:"redirect,omitempty"`
}

type EnvoyRouteMatch struct {
	Prefix  string             `json:"prefix,omitempty"`
	Path    string             `json:"path,omitempty"`
	Regex   string             `json:"regex,omitempty"`
	Headers []EnvoyHeaderMatch `json:"headers,omitempty"`
}

type EnvoyRouteRoute struct {
	Cluster          string                 `json:"cluster,omitempty"`
	WeightedClusters []EnvoyWeightedCluster `json:"weighted_clusters,omitempty"`
	Timeout          string                 `json:"timeout,omitempty"`
	RetryPolicy      *EnvoyRetryPolicy      `json:"retry_policy,omitempty"`
}

type EnvoyRedirect struct {
	HostRedirect string `json:"host_redirect,omitempty"`
	PathRedirect string `json:"path_redirect,omitempty"`
	ResponseCode int    `json:"response_code,omitempty"`
}

type EnvoyFilterChain struct {
	Filters []EnvoyFilter    `json:"filters"`
	TLS     *EnvoyTLSContext `json:"tls,omitempty"`
}

type EnvoyFilter struct {
	Name   string                 `json:"name"`
	Config map[string]interface{} `json:"config"`
}

type EnvoyTLSContext struct {
	CertificateChain string `json:"certificate_chain"`
	PrivateKey       string `json:"private_key"`
	CACert           string `json:"ca_cert,omitempty"`
}

type EnvoyHeaderMatch struct {
	Name  string `json:"name"`
	Value string `json:"value,omitempty"`
	Regex string `json:"regex,omitempty"`
}

type EnvoyWeightedCluster struct {
	Name   string `json:"name"`
	Weight int    `json:"weight"`
}

type EnvoyRetryPolicy struct {
	RetryOn       string `json:"retry_on"`
	NumRetries    int    `json:"num_retries"`
	PerTryTimeout string `json:"per_try_timeout"`
}

// NewService creates a new Envoy control plane service
func NewService(
	config *config.Config,
	logger *zap.Logger,
	cache *cache.RistrettoCache,
	store *db.SQLiteStore,
) (Service, error) {
	s := &service{
		config:    config,
		logger:    logger,
		cache:     cache,
		store:     store,
		snapshots: make(map[string]*EnvoySnapshot),
	}

	// Load existing snapshots from database
	if err := s.loadSnapshots(); err != nil {
		logger.Warn("Failed to load existing snapshots", zap.Error(err))
	}

	return s, nil
}

// PushSnapshot pushes a new configuration snapshot for a tenant
func (s *service) PushSnapshot(ctx context.Context, req *orchestratorv1.PushSnapshotRequest) (*orchestratorv1.PushSnapshotResponse, error) {
	s.logger.Info("Pushing Envoy snapshot",
		zap.String("tenant", req.Tenant))

	// Parse JSON configurations
	var clusters []EnvoyCluster
	var routes []EnvoyRoute
	var listeners []EnvoyListener

	if req.ClustersJson != "" {
		if err := json.Unmarshal([]byte(req.ClustersJson), &clusters); err != nil {
			return nil, fmt.Errorf("invalid clusters JSON: %w", err)
		}
	}

	if req.RoutesJson != "" {
		if err := json.Unmarshal([]byte(req.RoutesJson), &routes); err != nil {
			return nil, fmt.Errorf("invalid routes JSON: %w", err)
		}
	}

	if req.ListenersJson != "" {
		if err := json.Unmarshal([]byte(req.ListenersJson), &listeners); err != nil {
			return nil, fmt.Errorf("invalid listeners JSON: %w", err)
		}
	}

	// Create new snapshot
	version := fmt.Sprintf("v%d", time.Now().Unix())
	snapshot := &EnvoySnapshot{
		Tenant:    req.Tenant,
		Version:   version,
		Clusters:  clusters,
		Routes:    routes,
		Listeners: listeners,
		CreatedAt: time.Now().UTC(),
		UpdatedAt: time.Now().UTC(),
		Metadata: map[string]interface{}{
			"pushed_by": "cainuro-orchestrator",
			"source":    "api",
		},
	}

	// Store snapshot
	s.mu.Lock()
	s.snapshots[req.Tenant] = snapshot
	s.mu.Unlock()

	// Persist to database
	if err := s.saveSnapshot(ctx, snapshot); err != nil {
		s.logger.Error("Failed to save snapshot to database", zap.Error(err))
		// Continue anyway - in-memory snapshot is still valid
	}

	// Cache the snapshot
	cacheKey := fmt.Sprintf("envoy_snapshot:%s", req.Tenant)
	s.cache.SetWithTTL(ctx, cacheKey, snapshot, s.config.Envoy.SnapshotCacheTTL)

	// Notify XDS server of update
	if s.xdsServer != nil {
		s.xdsServer.NotifySnapshotUpdate(req.Tenant, snapshot)
	}

	s.logger.Info("Envoy snapshot pushed successfully",
		zap.String("tenant", req.Tenant),
		zap.String("version", version),
		zap.Int("clusters", len(clusters)),
		zap.Int("routes", len(routes)),
		zap.Int("listeners", len(listeners)))

	return &orchestratorv1.PushSnapshotResponse{
		Success:         true,
		Message:         "Snapshot pushed successfully",
		SnapshotVersion: version,
	}, nil
}

// GetSnapshot retrieves the current snapshot for a tenant
func (s *service) GetSnapshot(ctx context.Context, req *orchestratorv1.GetSnapshotRequest) (*orchestratorv1.GetSnapshotResponse, error) {
	s.logger.Info("Getting Envoy snapshot", zap.String("tenant", req.Tenant))

	// Check cache first
	cacheKey := fmt.Sprintf("envoy_snapshot:%s", req.Tenant)
	var cachedSnapshot EnvoySnapshot
	if found, err := s.cache.Get(ctx, cacheKey, &cachedSnapshot); err == nil && found {
		return s.snapshotToResponse(&cachedSnapshot), nil
	}

	// Check in-memory snapshots
	s.mu.RLock()
	snapshot, exists := s.snapshots[req.Tenant]
	s.mu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("snapshot not found for tenant: %s", req.Tenant)
	}

	// Cache the snapshot
	s.cache.SetWithTTL(ctx, cacheKey, snapshot, s.config.Envoy.SnapshotCacheTTL)

	return s.snapshotToResponse(snapshot), nil
}

// StartXDSServer starts the Envoy xDS server
func (s *service) StartXDSServer(ctx context.Context, port int) error {
	s.logger.Info("Starting Envoy xDS server", zap.Int("port", port))

	xdsServer, err := NewXDSServer(port, s.logger)
	if err != nil {
		return fmt.Errorf("failed to create XDS server: %w", err)
	}

	s.xdsServer = xdsServer

	// Load current snapshots into XDS server
	s.mu.RLock()
	for tenant, snapshot := range s.snapshots {
		xdsServer.SetSnapshot(tenant, snapshot)
	}
	s.mu.RUnlock()

	// Start the server
	return xdsServer.Start(ctx)
}

// GetActiveSnapshots returns all active snapshots
func (s *service) GetActiveSnapshots() map[string]*EnvoySnapshot {
	s.mu.RLock()
	defer s.mu.RUnlock()

	result := make(map[string]*EnvoySnapshot)
	for tenant, snapshot := range s.snapshots {
		result[tenant] = snapshot
	}
	return result
}

// Helper methods

func (s *service) loadSnapshots() error {
	result, err := s.store.Query(context.Background(), "SELECT * FROM envoy_snapshots")
	if err != nil {
		return err
	}
	defer result.Close()

	for result.Next() {
		var row struct {
			Tenant        string `json:"tenant"`
			ClustersJSON  string `json:"clusters_json"`
			RoutesJSON    string `json:"routes_json"`
			ListenersJSON string `json:"listeners_json"`
			Version       string `json:"version"`
			CreatedAt     string `json:"created_at"`
			UpdatedAt     string `json:"updated_at"`
		}

		if err := result.Scan(&row); err != nil {
			continue
		}

		snapshot := &EnvoySnapshot{
			Tenant:  row.Tenant,
			Version: row.Version,
		}

		// Parse JSON fields
		json.Unmarshal([]byte(row.ClustersJSON), &snapshot.Clusters)
		json.Unmarshal([]byte(row.RoutesJSON), &snapshot.Routes)
		json.Unmarshal([]byte(row.ListenersJSON), &snapshot.Listeners)

		// Parse timestamps
		if createdAt, err := time.Parse(time.RFC3339, row.CreatedAt); err == nil {
			snapshot.CreatedAt = createdAt
		}
		if updatedAt, err := time.Parse(time.RFC3339, row.UpdatedAt); err == nil {
			snapshot.UpdatedAt = updatedAt
		}

		s.snapshots[row.Tenant] = snapshot
	}

	s.logger.Info("Loaded Envoy snapshots", zap.Int("count", len(s.snapshots)))
	return nil
}

func (s *service) saveSnapshot(ctx context.Context, snapshot *EnvoySnapshot) error {
	clustersJSON, _ := json.Marshal(snapshot.Clusters)
	routesJSON, _ := json.Marshal(snapshot.Routes)
	listenersJSON, _ := json.Marshal(snapshot.Listeners)

	return s.store.Exec(ctx, `
		INSERT INTO envoy_snapshots (tenant, clusters_json, routes_json, listeners_json, version, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?, ?)
		ON CONFLICT (tenant) DO UPDATE SET
			clusters_json = ?, routes_json = ?, listeners_json = ?, version = ?, updated_at = ?
	`, snapshot.Tenant, string(clustersJSON), string(routesJSON), string(listenersJSON),
		snapshot.Version, snapshot.CreatedAt.Format(time.RFC3339), snapshot.UpdatedAt.Format(time.RFC3339),
		string(clustersJSON), string(routesJSON), string(listenersJSON), snapshot.Version, snapshot.UpdatedAt.Format(time.RFC3339))
}

func (s *service) snapshotToResponse(snapshot *EnvoySnapshot) *orchestratorv1.GetSnapshotResponse {
	clustersJSON, _ := json.Marshal(snapshot.Clusters)
	routesJSON, _ := json.Marshal(snapshot.Routes)
	listenersJSON, _ := json.Marshal(snapshot.Listeners)

	return &orchestratorv1.GetSnapshotResponse{
		Tenant:        snapshot.Tenant,
		ClustersJson:  string(clustersJSON),
		RoutesJson:    string(routesJSON),
		ListenersJson: string(listenersJSON),
		Version:       snapshot.Version,
		CreatedAt:     timestampFromTime(snapshot.CreatedAt),
	}
}

func timestampFromTime(t time.Time) *timestamppb.Timestamp {
	return timestamppb.New(t)
}
