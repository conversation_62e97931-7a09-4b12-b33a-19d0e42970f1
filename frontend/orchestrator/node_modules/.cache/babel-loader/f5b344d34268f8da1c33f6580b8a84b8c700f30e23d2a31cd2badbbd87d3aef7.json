{"ast": null, "code": "import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\nimport axios from 'axios';\nconst initialState = {\n  resources: [],\n  loading: false,\n  error: null,\n  totalCount: 0,\n  query: '',\n  provider: ''\n};\n\n// Async thunks\nexport const searchResources = createAsyncThunk('search/searchResources', async params => {\n  const response = await axios.post('/v1/discovery/search', params);\n  return response.data;\n});\nexport const getResource = createAsyncThunk('search/getResource', async resourceId => {\n  const response = await axios.get(`/v1/discovery/resources/${resourceId}`);\n  return response.data.resource;\n});\nconst searchSlice = createSlice({\n  name: 'search',\n  initialState,\n  reducers: {\n    setQuery: (state, action) => {\n      state.query = action.payload;\n    },\n    setProvider: (state, action) => {\n      state.provider = action.payload;\n    },\n    clearResults: state => {\n      state.resources = [];\n      state.totalCount = 0;\n      state.error = null;\n    }\n  },\n  extraReducers: builder => {\n    builder.addCase(searchResources.pending, state => {\n      state.loading = true;\n      state.error = null;\n    }).addCase(searchResources.fulfilled, (state, action) => {\n      state.loading = false;\n      state.resources = action.payload.resources;\n      state.totalCount = action.payload.total_count;\n    }).addCase(searchResources.rejected, (state, action) => {\n      state.loading = false;\n      state.error = action.error.message || 'Search failed';\n    }).addCase(getResource.pending, state => {\n      state.loading = true;\n      state.error = null;\n    }).addCase(getResource.fulfilled, (state, action) => {\n      state.loading = false;\n      // Update the resource in the list if it exists\n      const index = state.resources.findIndex(r => r.id === action.payload.id);\n      if (index !== -1) {\n        state.resources[index] = action.payload;\n      }\n    }).addCase(getResource.rejected, (state, action) => {\n      state.loading = false;\n      state.error = action.error.message || 'Failed to get resource';\n    });\n  }\n});\nexport const {\n  setQuery,\n  setProvider,\n  clearResults\n} = searchSlice.actions;\nexport default searchSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "axios", "initialState", "resources", "loading", "error", "totalCount", "query", "provider", "searchResources", "params", "response", "post", "data", "getResource", "resourceId", "get", "resource", "searchSlice", "name", "reducers", "<PERSON><PERSON><PERSON><PERSON>", "state", "action", "payload", "<PERSON><PERSON><PERSON><PERSON>", "clearResults", "extraReducers", "builder", "addCase", "pending", "fulfilled", "total_count", "rejected", "message", "index", "findIndex", "r", "id", "actions", "reducer"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/searchSlice.ts"], "sourcesContent": ["import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';\nimport axios from 'axios';\n\nexport interface Resource {\n  provider: string;\n  type: string;\n  name: string;\n  id: string;\n  tags: Record<string, string>;\n}\n\nexport interface SearchState {\n  resources: Resource[];\n  loading: boolean;\n  error: string | null;\n  totalCount: number;\n  query: string;\n  provider: string;\n}\n\nconst initialState: SearchState = {\n  resources: [],\n  loading: false,\n  error: null,\n  totalCount: 0,\n  query: '',\n  provider: '',\n};\n\n// Async thunks\nexport const searchResources = createAsyncThunk(\n  'search/searchResources',\n  async (params: { query?: string; provider?: string; limit?: number }) => {\n    const response = await axios.post('/v1/discovery/search', params);\n    return response.data;\n  }\n);\n\nexport const getResource = createAsyncThunk(\n  'search/getResource',\n  async (resourceId: string) => {\n    const response = await axios.get(`/v1/discovery/resources/${resourceId}`);\n    return response.data.resource;\n  }\n);\n\nconst searchSlice = createSlice({\n  name: 'search',\n  initialState,\n  reducers: {\n    setQuery: (state, action: PayloadAction<string>) => {\n      state.query = action.payload;\n    },\n    setProvider: (state, action: PayloadAction<string>) => {\n      state.provider = action.payload;\n    },\n    clearResults: (state) => {\n      state.resources = [];\n      state.totalCount = 0;\n      state.error = null;\n    },\n  },\n  extraReducers: (builder) => {\n    builder\n      .addCase(searchResources.pending, (state) => {\n        state.loading = true;\n        state.error = null;\n      })\n      .addCase(searchResources.fulfilled, (state, action) => {\n        state.loading = false;\n        state.resources = action.payload.resources;\n        state.totalCount = action.payload.total_count;\n      })\n      .addCase(searchResources.rejected, (state, action) => {\n        state.loading = false;\n        state.error = action.error.message || 'Search failed';\n      })\n      .addCase(getResource.pending, (state) => {\n        state.loading = true;\n        state.error = null;\n      })\n      .addCase(getResource.fulfilled, (state, action) => {\n        state.loading = false;\n        // Update the resource in the list if it exists\n        const index = state.resources.findIndex(r => r.id === action.payload.id);\n        if (index !== -1) {\n          state.resources[index] = action.payload;\n        }\n      })\n      .addCase(getResource.rejected, (state, action) => {\n        state.loading = false;\n        state.error = action.error.message || 'Failed to get resource';\n      });\n  },\n});\n\nexport const { setQuery, setProvider, clearResults } = searchSlice.actions;\nexport default searchSlice.reducer;\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,gBAAgB,QAAuB,kBAAkB;AAC/E,OAAOC,KAAK,MAAM,OAAO;AAmBzB,MAAMC,YAAyB,GAAG;EAChCC,SAAS,EAAE,EAAE;EACbC,OAAO,EAAE,KAAK;EACdC,KAAK,EAAE,IAAI;EACXC,UAAU,EAAE,CAAC;EACbC,KAAK,EAAE,EAAE;EACTC,QAAQ,EAAE;AACZ,CAAC;;AAED;AACA,OAAO,MAAMC,eAAe,GAAGT,gBAAgB,CAC7C,wBAAwB,EACxB,MAAOU,MAA6D,IAAK;EACvE,MAAMC,QAAQ,GAAG,MAAMV,KAAK,CAACW,IAAI,CAAC,sBAAsB,EAAEF,MAAM,CAAC;EACjE,OAAOC,QAAQ,CAACE,IAAI;AACtB,CACF,CAAC;AAED,OAAO,MAAMC,WAAW,GAAGd,gBAAgB,CACzC,oBAAoB,EACpB,MAAOe,UAAkB,IAAK;EAC5B,MAAMJ,QAAQ,GAAG,MAAMV,KAAK,CAACe,GAAG,CAAC,2BAA2BD,UAAU,EAAE,CAAC;EACzE,OAAOJ,QAAQ,CAACE,IAAI,CAACI,QAAQ;AAC/B,CACF,CAAC;AAED,MAAMC,WAAW,GAAGnB,WAAW,CAAC;EAC9BoB,IAAI,EAAE,QAAQ;EACdjB,YAAY;EACZkB,QAAQ,EAAE;IACRC,QAAQ,EAAEA,CAACC,KAAK,EAAEC,MAA6B,KAAK;MAClDD,KAAK,CAACf,KAAK,GAAGgB,MAAM,CAACC,OAAO;IAC9B,CAAC;IACDC,WAAW,EAAEA,CAACH,KAAK,EAAEC,MAA6B,KAAK;MACrDD,KAAK,CAACd,QAAQ,GAAGe,MAAM,CAACC,OAAO;IACjC,CAAC;IACDE,YAAY,EAAGJ,KAAK,IAAK;MACvBA,KAAK,CAACnB,SAAS,GAAG,EAAE;MACpBmB,KAAK,CAAChB,UAAU,GAAG,CAAC;MACpBgB,KAAK,CAACjB,KAAK,GAAG,IAAI;IACpB;EACF,CAAC;EACDsB,aAAa,EAAGC,OAAO,IAAK;IAC1BA,OAAO,CACJC,OAAO,CAACpB,eAAe,CAACqB,OAAO,EAAGR,KAAK,IAAK;MAC3CA,KAAK,CAAClB,OAAO,GAAG,IAAI;MACpBkB,KAAK,CAACjB,KAAK,GAAG,IAAI;IACpB,CAAC,CAAC,CACDwB,OAAO,CAACpB,eAAe,CAACsB,SAAS,EAAE,CAACT,KAAK,EAAEC,MAAM,KAAK;MACrDD,KAAK,CAAClB,OAAO,GAAG,KAAK;MACrBkB,KAAK,CAACnB,SAAS,GAAGoB,MAAM,CAACC,OAAO,CAACrB,SAAS;MAC1CmB,KAAK,CAAChB,UAAU,GAAGiB,MAAM,CAACC,OAAO,CAACQ,WAAW;IAC/C,CAAC,CAAC,CACDH,OAAO,CAACpB,eAAe,CAACwB,QAAQ,EAAE,CAACX,KAAK,EAAEC,MAAM,KAAK;MACpDD,KAAK,CAAClB,OAAO,GAAG,KAAK;MACrBkB,KAAK,CAACjB,KAAK,GAAGkB,MAAM,CAAClB,KAAK,CAAC6B,OAAO,IAAI,eAAe;IACvD,CAAC,CAAC,CACDL,OAAO,CAACf,WAAW,CAACgB,OAAO,EAAGR,KAAK,IAAK;MACvCA,KAAK,CAAClB,OAAO,GAAG,IAAI;MACpBkB,KAAK,CAACjB,KAAK,GAAG,IAAI;IACpB,CAAC,CAAC,CACDwB,OAAO,CAACf,WAAW,CAACiB,SAAS,EAAE,CAACT,KAAK,EAAEC,MAAM,KAAK;MACjDD,KAAK,CAAClB,OAAO,GAAG,KAAK;MACrB;MACA,MAAM+B,KAAK,GAAGb,KAAK,CAACnB,SAAS,CAACiC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKf,MAAM,CAACC,OAAO,CAACc,EAAE,CAAC;MACxE,IAAIH,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBb,KAAK,CAACnB,SAAS,CAACgC,KAAK,CAAC,GAAGZ,MAAM,CAACC,OAAO;MACzC;IACF,CAAC,CAAC,CACDK,OAAO,CAACf,WAAW,CAACmB,QAAQ,EAAE,CAACX,KAAK,EAAEC,MAAM,KAAK;MAChDD,KAAK,CAAClB,OAAO,GAAG,KAAK;MACrBkB,KAAK,CAACjB,KAAK,GAAGkB,MAAM,CAAClB,KAAK,CAAC6B,OAAO,IAAI,wBAAwB;IAChE,CAAC,CAAC;EACN;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEb,QAAQ;EAAEI,WAAW;EAAEC;AAAa,CAAC,GAAGR,WAAW,CAACqB,OAAO;AAC1E,eAAerB,WAAW,CAACsB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}