// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v3.17.3
// source: topology/v1/topology_api.proto

package topologyv1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "github.com/lyft/clutch/backend/api/api/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	status "google.golang.org/genproto/googleapis/rpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	anypb "google.golang.org/protobuf/types/known/anypb"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SearchRequest_Sort_Direction int32

const (
	SearchRequest_Sort_UNSPECIFIED SearchRequest_Sort_Direction = 0
	SearchRequest_Sort_ASCENDING   SearchRequest_Sort_Direction = 1
	SearchRequest_Sort_DESCENDING  SearchRequest_Sort_Direction = 2
)

// Enum value maps for SearchRequest_Sort_Direction.
var (
	SearchRequest_Sort_Direction_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "ASCENDING",
		2: "DESCENDING",
	}
	SearchRequest_Sort_Direction_value = map[string]int32{
		"UNSPECIFIED": 0,
		"ASCENDING":   1,
		"DESCENDING":  2,
	}
)

func (x SearchRequest_Sort_Direction) Enum() *SearchRequest_Sort_Direction {
	p := new(SearchRequest_Sort_Direction)
	*p = x
	return p
}

func (x SearchRequest_Sort_Direction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SearchRequest_Sort_Direction) Descriptor() protoreflect.EnumDescriptor {
	return file_topology_v1_topology_api_proto_enumTypes[0].Descriptor()
}

func (SearchRequest_Sort_Direction) Type() protoreflect.EnumType {
	return &file_topology_v1_topology_api_proto_enumTypes[0]
}

func (x SearchRequest_Sort_Direction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SearchRequest_Sort_Direction.Descriptor instead.
func (SearchRequest_Sort_Direction) EnumDescriptor() ([]byte, []int) {
	return file_topology_v1_topology_api_proto_rawDescGZIP(), []int{2, 0, 0}
}

type Constraint_Operator int32

const (
	Constraint_UNSPECIFIED Constraint_Operator = 0
	// number operators
	Constraint_EQUAL                 Constraint_Operator = 1
	Constraint_NOT_EQUAL             Constraint_Operator = 2
	Constraint_GREATER_THAN          Constraint_Operator = 3
	Constraint_GREATER_THAN_OR_EQUAL Constraint_Operator = 4
	Constraint_LESS_THAN             Constraint_Operator = 5
	Constraint_LESS_THAN_OR_EQUAL    Constraint_Operator = 6
	// string operators
	Constraint_CONTAINS_STRING Constraint_Operator = 7
	// array operators
	Constraint_CONTAINS_VALUE Constraint_Operator = 8
	// map operators
	Constraint_CONTAINS_KEY Constraint_Operator = 9
)

// Enum value maps for Constraint_Operator.
var (
	Constraint_Operator_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "EQUAL",
		2: "NOT_EQUAL",
		3: "GREATER_THAN",
		4: "GREATER_THAN_OR_EQUAL",
		5: "LESS_THAN",
		6: "LESS_THAN_OR_EQUAL",
		7: "CONTAINS_STRING",
		8: "CONTAINS_VALUE",
		9: "CONTAINS_KEY",
	}
	Constraint_Operator_value = map[string]int32{
		"UNSPECIFIED":           0,
		"EQUAL":                 1,
		"NOT_EQUAL":             2,
		"GREATER_THAN":          3,
		"GREATER_THAN_OR_EQUAL": 4,
		"LESS_THAN":             5,
		"LESS_THAN_OR_EQUAL":    6,
		"CONTAINS_STRING":       7,
		"CONTAINS_VALUE":        8,
		"CONTAINS_KEY":          9,
	}
)

func (x Constraint_Operator) Enum() *Constraint_Operator {
	p := new(Constraint_Operator)
	*p = x
	return p
}

func (x Constraint_Operator) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Constraint_Operator) Descriptor() protoreflect.EnumDescriptor {
	return file_topology_v1_topology_api_proto_enumTypes[1].Descriptor()
}

func (Constraint_Operator) Type() protoreflect.EnumType {
	return &file_topology_v1_topology_api_proto_enumTypes[1]
}

func (x Constraint_Operator) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Constraint_Operator.Descriptor instead.
func (Constraint_Operator) EnumDescriptor() ([]byte, []int) {
	return file_topology_v1_topology_api_proto_rawDescGZIP(), []int{5, 0}
}

// if aggregate, then all timeseries or array metadata is aggregated into a
// value and returned otherwise, it will return the original array/timeseries.
// This is meant to reduce message size for larger queries.
type MetadataQuery_Aggregation int32

const (
	// default; no aggregation applied
	MetadataQuery_UNSPECIFIED MetadataQuery_Aggregation = 0
	MetadataQuery_SUM         MetadataQuery_Aggregation = 1
	MetadataQuery_AVERAGE     MetadataQuery_Aggregation = 2
	MetadataQuery_MEDIAN      MetadataQuery_Aggregation = 3
	MetadataQuery_MODE        MetadataQuery_Aggregation = 4
	MetadataQuery_MIN         MetadataQuery_Aggregation = 5
	MetadataQuery_MAX         MetadataQuery_Aggregation = 6
	MetadataQuery_COUNT       MetadataQuery_Aggregation = 7
)

// Enum value maps for MetadataQuery_Aggregation.
var (
	MetadataQuery_Aggregation_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "SUM",
		2: "AVERAGE",
		3: "MEDIAN",
		4: "MODE",
		5: "MIN",
		6: "MAX",
		7: "COUNT",
	}
	MetadataQuery_Aggregation_value = map[string]int32{
		"UNSPECIFIED": 0,
		"SUM":         1,
		"AVERAGE":     2,
		"MEDIAN":      3,
		"MODE":        4,
		"MIN":         5,
		"MAX":         6,
		"COUNT":       7,
	}
)

func (x MetadataQuery_Aggregation) Enum() *MetadataQuery_Aggregation {
	p := new(MetadataQuery_Aggregation)
	*p = x
	return p
}

func (x MetadataQuery_Aggregation) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MetadataQuery_Aggregation) Descriptor() protoreflect.EnumDescriptor {
	return file_topology_v1_topology_api_proto_enumTypes[2].Descriptor()
}

func (MetadataQuery_Aggregation) Type() protoreflect.EnumType {
	return &file_topology_v1_topology_api_proto_enumTypes[2]
}

func (x MetadataQuery_Aggregation) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MetadataQuery_Aggregation.Descriptor instead.
func (MetadataQuery_Aggregation) EnumDescriptor() ([]byte, []int) {
	return file_topology_v1_topology_api_proto_rawDescGZIP(), []int{6, 0}
}

// Action signifies to the topology service what to do with an incoming topology Resource
//
// The topology service gets a topology Resource off of the `GetTopologyObjectChannel` which is processed
// and stored in the topology_cache table.
type UpdateCacheRequest_Action int32

const (
	UpdateCacheRequest_UNSPECIFIED      UpdateCacheRequest_Action = 0
	UpdateCacheRequest_CREATE_OR_UPDATE UpdateCacheRequest_Action = 1
	UpdateCacheRequest_DELETE           UpdateCacheRequest_Action = 2
)

// Enum value maps for UpdateCacheRequest_Action.
var (
	UpdateCacheRequest_Action_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "CREATE_OR_UPDATE",
		2: "DELETE",
	}
	UpdateCacheRequest_Action_value = map[string]int32{
		"UNSPECIFIED":      0,
		"CREATE_OR_UPDATE": 1,
		"DELETE":           2,
	}
)

func (x UpdateCacheRequest_Action) Enum() *UpdateCacheRequest_Action {
	p := new(UpdateCacheRequest_Action)
	*p = x
	return p
}

func (x UpdateCacheRequest_Action) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpdateCacheRequest_Action) Descriptor() protoreflect.EnumDescriptor {
	return file_topology_v1_topology_api_proto_enumTypes[3].Descriptor()
}

func (UpdateCacheRequest_Action) Type() protoreflect.EnumType {
	return &file_topology_v1_topology_api_proto_enumTypes[3]
}

func (x UpdateCacheRequest_Action) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpdateCacheRequest_Action.Descriptor instead.
func (UpdateCacheRequest_Action) EnumDescriptor() ([]byte, []int) {
	return file_topology_v1_topology_api_proto_rawDescGZIP(), []int{12, 0}
}

type GetTopologyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Queries []*Query `protobuf:"bytes,1,rep,name=queries,proto3" json:"queries,omitempty"`
}

func (x *GetTopologyRequest) Reset() {
	*x = GetTopologyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_topology_v1_topology_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTopologyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTopologyRequest) ProtoMessage() {}

func (x *GetTopologyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_topology_v1_topology_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTopologyRequest.ProtoReflect.Descriptor instead.
func (*GetTopologyRequest) Descriptor() ([]byte, []int) {
	return file_topology_v1_topology_api_proto_rawDescGZIP(), []int{0}
}

func (x *GetTopologyRequest) GetQueries() []*Query {
	if x != nil {
		return x.Queries
	}
	return nil
}

type GetTopologyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Results []*QueryResult `protobuf:"bytes,1,rep,name=results,proto3" json:"results,omitempty"`
}

func (x *GetTopologyResponse) Reset() {
	*x = GetTopologyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_topology_v1_topology_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTopologyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTopologyResponse) ProtoMessage() {}

func (x *GetTopologyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_topology_v1_topology_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTopologyResponse.ProtoReflect.Descriptor instead.
func (*GetTopologyResponse) Descriptor() ([]byte, []int) {
	return file_topology_v1_topology_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetTopologyResponse) GetResults() []*QueryResult {
	if x != nil {
		return x.Results
	}
	return nil
}

type SearchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sort *SearchRequest_Sort `protobuf:"bytes,1,opt,name=sort,proto3" json:"sort,omitempty"`
	// Currently page_token specifies the page number you wish to request.
	// The rationale behind the naming is we might changes this to a cursor implentation
	// in the future and did not want to break existing implementations of the API.
	// https://cloud.google.com/apis/design/design_patterns#list_pagination
	PageToken string                `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	Limit     uint64                `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	Filter    *SearchRequest_Filter `protobuf:"bytes,4,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *SearchRequest) Reset() {
	*x = SearchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_topology_v1_topology_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchRequest) ProtoMessage() {}

func (x *SearchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_topology_v1_topology_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchRequest.ProtoReflect.Descriptor instead.
func (*SearchRequest) Descriptor() ([]byte, []int) {
	return file_topology_v1_topology_api_proto_rawDescGZIP(), []int{2}
}

func (x *SearchRequest) GetSort() *SearchRequest_Sort {
	if x != nil {
		return x.Sort
	}
	return nil
}

func (x *SearchRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *SearchRequest) GetLimit() uint64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *SearchRequest) GetFilter() *SearchRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

type SearchResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Resources     []*Resource `protobuf:"bytes,1,rep,name=resources,proto3" json:"resources,omitempty"`
	NextPageToken string      `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
}

func (x *SearchResponse) Reset() {
	*x = SearchResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_topology_v1_topology_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchResponse) ProtoMessage() {}

func (x *SearchResponse) ProtoReflect() protoreflect.Message {
	mi := &file_topology_v1_topology_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchResponse.ProtoReflect.Descriptor instead.
func (*SearchResponse) Descriptor() ([]byte, []int) {
	return file_topology_v1_topology_api_proto_rawDescGZIP(), []int{3}
}

func (x *SearchResponse) GetResources() []*Resource {
	if x != nil {
		return x.Resources
	}
	return nil
}

func (x *SearchResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

// A query allows the user to specify multiple dimensions and the
// corresponding desired values for the identifying features of the returned
// nodes.
type FeatureQuery struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name   string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Values []string `protobuf:"bytes,2,rep,name=values,proto3" json:"values,omitempty"`
}

func (x *FeatureQuery) Reset() {
	*x = FeatureQuery{}
	if protoimpl.UnsafeEnabled {
		mi := &file_topology_v1_topology_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeatureQuery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeatureQuery) ProtoMessage() {}

func (x *FeatureQuery) ProtoReflect() protoreflect.Message {
	mi := &file_topology_v1_topology_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeatureQuery.ProtoReflect.Descriptor instead.
func (*FeatureQuery) Descriptor() ([]byte, []int) {
	return file_topology_v1_topology_api_proto_rawDescGZIP(), []int{4}
}

func (x *FeatureQuery) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *FeatureQuery) GetValues() []string {
	if x != nil {
		return x.Values
	}
	return nil
}

// A metadata constraint with an operator and a value
type Constraint struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Operator Constraint_Operator `protobuf:"varint,1,opt,name=operator,proto3,enum=clutch.topology.v1.Constraint_Operator" json:"operator,omitempty"`
	Value    *structpb.Value     `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *Constraint) Reset() {
	*x = Constraint{}
	if protoimpl.UnsafeEnabled {
		mi := &file_topology_v1_topology_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Constraint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Constraint) ProtoMessage() {}

func (x *Constraint) ProtoReflect() protoreflect.Message {
	mi := &file_topology_v1_topology_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Constraint.ProtoReflect.Descriptor instead.
func (*Constraint) Descriptor() ([]byte, []int) {
	return file_topology_v1_topology_api_proto_rawDescGZIP(), []int{5}
}

func (x *Constraint) GetOperator() Constraint_Operator {
	if x != nil {
		return x.Operator
	}
	return Constraint_UNSPECIFIED
}

func (x *Constraint) GetValue() *structpb.Value {
	if x != nil {
		return x.Value
	}
	return nil
}

// Similar to FeatureQuery, rather than pre-traversal filtering,
// MetadataQuery specifies metadata params and potential filters
// for nodes and edges
type MetadataQuery struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name of the metadata field to populate. i.e. call_volume, service_tier,
	// etc.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// i.e. Wavefront params: google.protobuf.Struct{ ...start: <Time>, end:
	// <Time>, Timeout: <Duration>} i.e. other params for other clients TBD
	Params      *structpb.Struct          `protobuf:"bytes,2,opt,name=params,proto3" json:"params,omitempty"`
	Aggregation MetadataQuery_Aggregation `protobuf:"varint,3,opt,name=aggregation,proto3,enum=clutch.topology.v1.MetadataQuery_Aggregation" json:"aggregation,omitempty"`
	// a series of restrictive constraints for the value/values of a Type to pass
	Constraints []*Constraint `protobuf:"bytes,5,rep,name=constraints,proto3" json:"constraints,omitempty"`
}

func (x *MetadataQuery) Reset() {
	*x = MetadataQuery{}
	if protoimpl.UnsafeEnabled {
		mi := &file_topology_v1_topology_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MetadataQuery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MetadataQuery) ProtoMessage() {}

func (x *MetadataQuery) ProtoReflect() protoreflect.Message {
	mi := &file_topology_v1_topology_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MetadataQuery.ProtoReflect.Descriptor instead.
func (*MetadataQuery) Descriptor() ([]byte, []int) {
	return file_topology_v1_topology_api_proto_rawDescGZIP(), []int{6}
}

func (x *MetadataQuery) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MetadataQuery) GetParams() *structpb.Struct {
	if x != nil {
		return x.Params
	}
	return nil
}

func (x *MetadataQuery) GetAggregation() MetadataQuery_Aggregation {
	if x != nil {
		return x.Aggregation
	}
	return MetadataQuery_UNSPECIFIED
}

func (x *MetadataQuery) GetConstraints() []*Constraint {
	if x != nil {
		return x.Constraints
	}
	return nil
}

type Query struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Features represent the identifying characteristics of a node that make it
	// unique, i.e. the resulting dimensions of the nodes in the result set.
	//
	// A query can specify the set of desired values or wildcard values for each
	// feature.
	Features []*FeatureQuery `protobuf:"bytes,1,rep,name=features,proto3" json:"features,omitempty"`
	// Metadata is non-identifying characteristics of a node which can be
	// filtered upon and changed.
	NodeMetadata []*MetadataQuery `protobuf:"bytes,4,rep,name=node_metadata,json=nodeMetadata,proto3" json:"node_metadata,omitempty"`
	// Metadata query for edges.
	EdgeMetadata []*MetadataQuery `protobuf:"bytes,5,rep,name=edge_metadata,json=edgeMetadata,proto3" json:"edge_metadata,omitempty"`
	// Maximum depth to traverse in the graph upwards.
	SourceDepth uint32 `protobuf:"varint,2,opt,name=source_depth,json=sourceDepth,proto3" json:"source_depth,omitempty"`
	// Maximum depth to traverse in the graph downwards.
	TargetDepth uint32 `protobuf:"varint,3,opt,name=target_depth,json=targetDepth,proto3" json:"target_depth,omitempty"`
}

func (x *Query) Reset() {
	*x = Query{}
	if protoimpl.UnsafeEnabled {
		mi := &file_topology_v1_topology_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Query) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Query) ProtoMessage() {}

func (x *Query) ProtoReflect() protoreflect.Message {
	mi := &file_topology_v1_topology_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Query.ProtoReflect.Descriptor instead.
func (*Query) Descriptor() ([]byte, []int) {
	return file_topology_v1_topology_api_proto_rawDescGZIP(), []int{7}
}

func (x *Query) GetFeatures() []*FeatureQuery {
	if x != nil {
		return x.Features
	}
	return nil
}

func (x *Query) GetNodeMetadata() []*MetadataQuery {
	if x != nil {
		return x.NodeMetadata
	}
	return nil
}

func (x *Query) GetEdgeMetadata() []*MetadataQuery {
	if x != nil {
		return x.EdgeMetadata
	}
	return nil
}

func (x *Query) GetSourceDepth() uint32 {
	if x != nil {
		return x.SourceDepth
	}
	return 0
}

func (x *Query) GetTargetDepth() uint32 {
	if x != nil {
		return x.TargetDepth
	}
	return 0
}

type QueryResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Errors thats pertain to the issued query.
	Status *status.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Query issued in the request to yield the result.
	Query *Query `protobuf:"bytes,2,opt,name=query,proto3" json:"query,omitempty"`
	// Subset of the IDs for the query-matched nodes before any traversal
	// occurred.
	MatchedNodeIds []string `protobuf:"bytes,3,rep,name=matched_node_ids,json=matchedNodeIds,proto3" json:"matched_node_ids,omitempty"`
	// Map of node IDs to the node object.
	Nodes map[string]*Node `protobuf:"bytes,4,rep,name=nodes,proto3" json:"nodes,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Map of edge IDs to the edge object.
	Edges map[string]*Edge `protobuf:"bytes,5,rep,name=edges,proto3" json:"edges,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *QueryResult) Reset() {
	*x = QueryResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_topology_v1_topology_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryResult) ProtoMessage() {}

func (x *QueryResult) ProtoReflect() protoreflect.Message {
	mi := &file_topology_v1_topology_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryResult.ProtoReflect.Descriptor instead.
func (*QueryResult) Descriptor() ([]byte, []int) {
	return file_topology_v1_topology_api_proto_rawDescGZIP(), []int{8}
}

func (x *QueryResult) GetStatus() *status.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *QueryResult) GetQuery() *Query {
	if x != nil {
		return x.Query
	}
	return nil
}

func (x *QueryResult) GetMatchedNodeIds() []string {
	if x != nil {
		return x.MatchedNodeIds
	}
	return nil
}

func (x *QueryResult) GetNodes() map[string]*Node {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *QueryResult) GetEdges() map[string]*Edge {
	if x != nil {
		return x.Edges
	}
	return nil
}

type Node struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Each node has an opaque ID that is used to identify the node during result
	// processing and presentation.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Features represent the identifying characteristics (i.e. dimensions) of a
	// node that make it unique.
	//
	// When a node is returned from a query, the set of feature fields included on
	// the node will match those in the query.
	Features map[string]string `protobuf:"bytes,2,rep,name=features,proto3" json:"features,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Metadata maps field names to assorted state or characteristics of the node.
	Metadata map[string]*structpb.Value `protobuf:"bytes,3,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Node) Reset() {
	*x = Node{}
	if protoimpl.UnsafeEnabled {
		mi := &file_topology_v1_topology_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Node) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Node) ProtoMessage() {}

func (x *Node) ProtoReflect() protoreflect.Message {
	mi := &file_topology_v1_topology_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Node.ProtoReflect.Descriptor instead.
func (*Node) Descriptor() ([]byte, []int) {
	return file_topology_v1_topology_api_proto_rawDescGZIP(), []int{9}
}

func (x *Node) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Node) GetFeatures() map[string]string {
	if x != nil {
		return x.Features
	}
	return nil
}

func (x *Node) GetMetadata() map[string]*structpb.Value {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type Edge struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Each edge has an opaque ID that is used to identify the edge during result
	// processing and presentation.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Node ID where the edge originates.
	SourceNodeId string `protobuf:"bytes,2,opt,name=source_node_id,json=sourceNodeId,proto3" json:"source_node_id,omitempty"`
	// Node ID where the edge terminates.
	TargetNodeId string `protobuf:"bytes,3,opt,name=target_node_id,json=targetNodeId,proto3" json:"target_node_id,omitempty"`
	// Metadata maps field names to assorted state or characteristics of the
	// edge.
	Metadata map[string]*structpb.Value `protobuf:"bytes,4,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Edge) Reset() {
	*x = Edge{}
	if protoimpl.UnsafeEnabled {
		mi := &file_topology_v1_topology_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Edge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Edge) ProtoMessage() {}

func (x *Edge) ProtoReflect() protoreflect.Message {
	mi := &file_topology_v1_topology_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Edge.ProtoReflect.Descriptor instead.
func (*Edge) Descriptor() ([]byte, []int) {
	return file_topology_v1_topology_api_proto_rawDescGZIP(), []int{10}
}

func (x *Edge) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Edge) GetSourceNodeId() string {
	if x != nil {
		return x.SourceNodeId
	}
	return ""
}

func (x *Edge) GetTargetNodeId() string {
	if x != nil {
		return x.TargetNodeId
	}
	return ""
}

func (x *Edge) GetMetadata() map[string]*structpb.Value {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type Resource struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Id is the unique identifer of the Resource.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Pb is the clutch proto object.
	Pb *anypb.Any `protobuf:"bytes,2,opt,name=pb,proto3" json:"pb,omitempty"`
	// Metadata is set by the service which produces the topology Resource, for example k8s would extract
	// relevant metadata that gives the Topology API the ability to query against it.
	Metadata map[string]*structpb.Value `protobuf:"bytes,3,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Resource) Reset() {
	*x = Resource{}
	if protoimpl.UnsafeEnabled {
		mi := &file_topology_v1_topology_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Resource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Resource) ProtoMessage() {}

func (x *Resource) ProtoReflect() protoreflect.Message {
	mi := &file_topology_v1_topology_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Resource.ProtoReflect.Descriptor instead.
func (*Resource) Descriptor() ([]byte, []int) {
	return file_topology_v1_topology_api_proto_rawDescGZIP(), []int{11}
}

func (x *Resource) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Resource) GetPb() *anypb.Any {
	if x != nil {
		return x.Pb
	}
	return nil
}

func (x *Resource) GetMetadata() map[string]*structpb.Value {
	if x != nil {
		return x.Metadata
	}
	return nil
}

// A UpdateCacheRequest is used when a service such as kubernetes or aws produces objects for
// the topology API to cache.
type UpdateCacheRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Resource *Resource `protobuf:"bytes,1,opt,name=resource,proto3" json:"resource,omitempty"`
	// Action denotes what the topology service should do with this object.
	Action UpdateCacheRequest_Action `protobuf:"varint,2,opt,name=action,proto3,enum=clutch.topology.v1.UpdateCacheRequest_Action" json:"action,omitempty"`
}

func (x *UpdateCacheRequest) Reset() {
	*x = UpdateCacheRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_topology_v1_topology_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCacheRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCacheRequest) ProtoMessage() {}

func (x *UpdateCacheRequest) ProtoReflect() protoreflect.Message {
	mi := &file_topology_v1_topology_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCacheRequest.ProtoReflect.Descriptor instead.
func (*UpdateCacheRequest) Descriptor() ([]byte, []int) {
	return file_topology_v1_topology_api_proto_rawDescGZIP(), []int{12}
}

func (x *UpdateCacheRequest) GetResource() *Resource {
	if x != nil {
		return x.Resource
	}
	return nil
}

func (x *UpdateCacheRequest) GetAction() UpdateCacheRequest_Action {
	if x != nil {
		return x.Action
	}
	return UpdateCacheRequest_UNSPECIFIED
}

// The default sort is by column `id` and descending
type SearchRequest_Sort struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Direction SearchRequest_Sort_Direction `protobuf:"varint,1,opt,name=direction,proto3,enum=clutch.topology.v1.SearchRequest_Sort_Direction" json:"direction,omitempty"`
	// Using field mask selector to delinate a column or metadata.
	// if the metadata is nested specify the full path like the example shows below.
	// example for metadata 'metadata.level1.level2.level3.fieldname'
	// example for column 'column.id'
	Field string `protobuf:"bytes,2,opt,name=field,proto3" json:"field,omitempty"`
}

func (x *SearchRequest_Sort) Reset() {
	*x = SearchRequest_Sort{}
	if protoimpl.UnsafeEnabled {
		mi := &file_topology_v1_topology_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchRequest_Sort) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchRequest_Sort) ProtoMessage() {}

func (x *SearchRequest_Sort) ProtoReflect() protoreflect.Message {
	mi := &file_topology_v1_topology_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchRequest_Sort.ProtoReflect.Descriptor instead.
func (*SearchRequest_Sort) Descriptor() ([]byte, []int) {
	return file_topology_v1_topology_api_proto_rawDescGZIP(), []int{2, 0}
}

func (x *SearchRequest_Sort) GetDirection() SearchRequest_Sort_Direction {
	if x != nil {
		return x.Direction
	}
	return SearchRequest_Sort_UNSPECIFIED
}

func (x *SearchRequest_Sort) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

type SearchRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Search  *SearchRequest_Filter_Search `protobuf:"bytes,1,opt,name=search,proto3" json:"search,omitempty"`
	TypeUrl string                       `protobuf:"bytes,2,opt,name=type_url,json=typeUrl,proto3" json:"type_url,omitempty"`
	// Any valid metadata on the cache Resource object
	Metadata map[string]string `protobuf:"bytes,3,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// If case-sensitive, will use `LIKE`. Else, will use `ILIKE`
	// Notes about ILIKE from postgres docs:
	// The key word ILIKE can be used instead of LIKE to make the match case-insensitive
	// according to the active locale. This is not in the SQL standard but is a
	// PostgreSQL extension.
	CaseSensitive bool `protobuf:"varint,5,opt,name=case_sensitive,json=caseSensitive,proto3" json:"case_sensitive,omitempty"`
}

func (x *SearchRequest_Filter) Reset() {
	*x = SearchRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_topology_v1_topology_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchRequest_Filter) ProtoMessage() {}

func (x *SearchRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_topology_v1_topology_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchRequest_Filter.ProtoReflect.Descriptor instead.
func (*SearchRequest_Filter) Descriptor() ([]byte, []int) {
	return file_topology_v1_topology_api_proto_rawDescGZIP(), []int{2, 1}
}

func (x *SearchRequest_Filter) GetSearch() *SearchRequest_Filter_Search {
	if x != nil {
		return x.Search
	}
	return nil
}

func (x *SearchRequest_Filter) GetTypeUrl() string {
	if x != nil {
		return x.TypeUrl
	}
	return ""
}

func (x *SearchRequest_Filter) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *SearchRequest_Filter) GetCaseSensitive() bool {
	if x != nil {
		return x.CaseSensitive
	}
	return false
}

type SearchRequest_Filter_Search struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Using field mask selector to delinate a column or metadata.
	// if the metadata is nested specify the full path like the example shows below.
	// example for metadata 'metadata.level1.level2.level3.fieldname'
	// example for column 'column.id'
	Field string `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"`
	Text  string `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
}

func (x *SearchRequest_Filter_Search) Reset() {
	*x = SearchRequest_Filter_Search{}
	if protoimpl.UnsafeEnabled {
		mi := &file_topology_v1_topology_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchRequest_Filter_Search) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchRequest_Filter_Search) ProtoMessage() {}

func (x *SearchRequest_Filter_Search) ProtoReflect() protoreflect.Message {
	mi := &file_topology_v1_topology_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchRequest_Filter_Search.ProtoReflect.Descriptor instead.
func (*SearchRequest_Filter_Search) Descriptor() ([]byte, []int) {
	return file_topology_v1_topology_api_proto_rawDescGZIP(), []int{2, 1, 0}
}

func (x *SearchRequest_Filter_Search) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *SearchRequest_Filter_Search) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

var File_topology_v1_topology_api_proto protoreflect.FileDescriptor

var file_topology_v1_topology_api_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x74, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x6f,
	0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x12, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x74, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67,
	0x79, 0x2e, 0x76, 0x31, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x61, 0x6e, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73,
	0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x49, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x54, 0x6f,
	0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x33, 0x0a,
	0x07, 0x71, 0x75, 0x65, 0x72, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x74, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79,
	0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x07, 0x71, 0x75, 0x65, 0x72, 0x69,
	0x65, 0x73, 0x22, 0x50, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x39, 0x0a, 0x07, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x6c, 0x75,
	0x74, 0x63, 0x68, 0x2e, 0x74, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x2e, 0x76, 0x31, 0x2e,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x07, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x73, 0x22, 0xe4, 0x05, 0x0a, 0x0d, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x74, 0x6f,
	0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x52, 0x04, 0x73, 0x6f,
	0x72, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x40, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68,
	0x2e, 0x74, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0xb2, 0x01, 0x0a, 0x04, 0x53, 0x6f,
	0x72, 0x74, 0x12, 0x4e, 0x0a, 0x09, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x74,
	0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x2e, 0x44, 0x69,
	0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x05, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x22, 0x3b, 0x0a, 0x09, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0f,
	0x0a, 0x0b, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x0d, 0x0a, 0x09, 0x41, 0x53, 0x43, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x0e,
	0x0a, 0x0a, 0x44, 0x45, 0x53, 0x43, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x1a, 0xea,
	0x02, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x47, 0x0a, 0x06, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x63, 0x6c, 0x75, 0x74,
	0x63, 0x68, 0x2e, 0x74, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x06, 0x73, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x79, 0x70, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x52, 0x0a,
	0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x36, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x74, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67,
	0x79, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74,
	0x69, 0x76, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x63, 0x61, 0x73, 0x65, 0x53,
	0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x1a, 0x44, 0x0a, 0x06, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x12, 0x1d, 0x0a, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x05, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x12, 0x1b, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x1a, 0x3b,
	0x0a, 0x0d, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x74, 0x0a, 0x0e, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3a, 0x0a,
	0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x74, 0x6f, 0x70, 0x6f, 0x6c, 0x6f,
	0x67, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x09,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78,
	0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x22, 0x3a, 0x0a, 0x0c, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x22, 0xc6, 0x02,
	0x0a, 0x0a, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x12, 0x43, 0x0a, 0x08,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27,
	0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x74, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x2e, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f,
	0x72, 0x12, 0x2c, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22,
	0xc4, 0x01, 0x0a, 0x08, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x0f, 0x0a, 0x0b,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x09, 0x0a,
	0x05, 0x45, 0x51, 0x55, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f,
	0x45, 0x51, 0x55, 0x41, 0x4c, 0x10, 0x02, 0x12, 0x10, 0x0a, 0x0c, 0x47, 0x52, 0x45, 0x41, 0x54,
	0x45, 0x52, 0x5f, 0x54, 0x48, 0x41, 0x4e, 0x10, 0x03, 0x12, 0x19, 0x0a, 0x15, 0x47, 0x52, 0x45,
	0x41, 0x54, 0x45, 0x52, 0x5f, 0x54, 0x48, 0x41, 0x4e, 0x5f, 0x4f, 0x52, 0x5f, 0x45, 0x51, 0x55,
	0x41, 0x4c, 0x10, 0x04, 0x12, 0x0d, 0x0a, 0x09, 0x4c, 0x45, 0x53, 0x53, 0x5f, 0x54, 0x48, 0x41,
	0x4e, 0x10, 0x05, 0x12, 0x16, 0x0a, 0x12, 0x4c, 0x45, 0x53, 0x53, 0x5f, 0x54, 0x48, 0x41, 0x4e,
	0x5f, 0x4f, 0x52, 0x5f, 0x45, 0x51, 0x55, 0x41, 0x4c, 0x10, 0x06, 0x12, 0x13, 0x0a, 0x0f, 0x43,
	0x4f, 0x4e, 0x54, 0x41, 0x49, 0x4e, 0x53, 0x5f, 0x53, 0x54, 0x52, 0x49, 0x4e, 0x47, 0x10, 0x07,
	0x12, 0x12, 0x0a, 0x0e, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x49, 0x4e, 0x53, 0x5f, 0x56, 0x41, 0x4c,
	0x55, 0x45, 0x10, 0x08, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x49, 0x4e, 0x53,
	0x5f, 0x4b, 0x45, 0x59, 0x10, 0x09, 0x22, 0xd0, 0x02, 0x0a, 0x0d, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x06,
	0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x4f, 0x0a,
	0x0b, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x74, 0x6f, 0x70, 0x6f,
	0x6c, 0x6f, 0x67, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x2e, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x0b, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x40,
	0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x74, 0x6f, 0x70,
	0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61,
	0x69, 0x6e, 0x74, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x74, 0x73,
	0x22, 0x67, 0x0a, 0x0b, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x0f, 0x0a, 0x0b, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x07, 0x0a, 0x03, 0x53, 0x55, 0x4d, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x56, 0x45,
	0x52, 0x41, 0x47, 0x45, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x45, 0x44, 0x49, 0x41, 0x4e,
	0x10, 0x03, 0x12, 0x08, 0x0a, 0x04, 0x4d, 0x4f, 0x44, 0x45, 0x10, 0x04, 0x12, 0x07, 0x0a, 0x03,
	0x4d, 0x49, 0x4e, 0x10, 0x05, 0x12, 0x07, 0x0a, 0x03, 0x4d, 0x41, 0x58, 0x10, 0x06, 0x12, 0x09,
	0x0a, 0x05, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x07, 0x22, 0x9b, 0x02, 0x0a, 0x05, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x12, 0x3c, 0x0a, 0x08, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x74,
	0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x08, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x73, 0x12, 0x46, 0x0a, 0x0d, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63,
	0x68, 0x2e, 0x74, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x0c, 0x6e, 0x6f, 0x64,
	0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x46, 0x0a, 0x0d, 0x65, 0x64, 0x67,
	0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x74, 0x6f, 0x70, 0x6f, 0x6c, 0x6f,
	0x67, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x52, 0x0c, 0x65, 0x64, 0x67, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x70, 0x74,
	0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44,
	0x65, 0x70, 0x74, 0x68, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x64,
	0x65, 0x70, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x44, 0x65, 0x70, 0x74, 0x68, 0x22, 0xc0, 0x03, 0x0a, 0x0b, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x2a, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x2f, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x74, 0x6f, 0x70, 0x6f,
	0x6c, 0x6f, 0x67, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x05, 0x71,
	0x75, 0x65, 0x72, 0x79, 0x12, 0x28, 0x0a, 0x10, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x64, 0x5f,
	0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e,
	0x6d, 0x61, 0x74, 0x63, 0x68, 0x65, 0x64, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x73, 0x12, 0x40,
	0x0a, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x74, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x2e,
	0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x4e,
	0x6f, 0x64, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73,
	0x12, 0x40, 0x0a, 0x05, 0x65, 0x64, 0x67, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2a, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x74, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67,
	0x79, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x2e, 0x45, 0x64, 0x67, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x65, 0x64, 0x67,
	0x65, 0x73, 0x1a, 0x52, 0x0a, 0x0a, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x2e, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x74, 0x6f, 0x70, 0x6f, 0x6c,
	0x6f, 0x67, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x52, 0x0a, 0x0a, 0x45, 0x64, 0x67, 0x65, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2e, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x74,
	0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x64, 0x67, 0x65, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xb0, 0x02, 0x0a, 0x04, 0x4e,
	0x6f, 0x64, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x42, 0x0a, 0x08, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x74,
	0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x2e,
	0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x66,
	0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x6c, 0x75, 0x74,
	0x63, 0x68, 0x2e, 0x74, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4e,
	0x6f, 0x64, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x3b, 0x0a, 0x0d, 0x46,
	0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x53, 0x0a, 0x0d, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2c, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xfb, 0x01,
	0x0a, 0x04, 0x45, 0x64, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0e,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x4e, 0x6f, 0x64, 0x65,
	0x49, 0x64, 0x12, 0x42, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x74, 0x6f,
	0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x64, 0x67, 0x65, 0x2e, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x53, 0x0a, 0x0d, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2c, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xdd, 0x01, 0x0a, 0x08,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x24, 0x0a, 0x02, 0x70, 0x62, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x02, 0x70, 0x62, 0x12, 0x46,
	0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2a, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x74, 0x6f, 0x70, 0x6f, 0x6c, 0x6f,
	0x67, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x53, 0x0a, 0x0d, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2c, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xd2, 0x01, 0x0a, 0x12,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x38, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x74, 0x6f,
	0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x52, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x45, 0x0a, 0x06,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x63,
	0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x74, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x63, 0x68, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0x3b, 0x0a, 0x06, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0f, 0x0a,
	0x0b, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14,
	0x0a, 0x10, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x4f, 0x52, 0x5f, 0x55, 0x50, 0x44, 0x41,
	0x54, 0x45, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x10, 0x02,
	0x32, 0x90, 0x02, 0x0a, 0x0b, 0x54, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x41, 0x50, 0x49,
	0x12, 0x89, 0x01, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79,
	0x12, 0x26, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x74, 0x6f, 0x70, 0x6f, 0x6c, 0x6f,
	0x67, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63,
	0x68, 0x2e, 0x74, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x54, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x29, 0xaa, 0xe1, 0x1c, 0x02, 0x08, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x3a,
	0x01, 0x2a, 0x22, 0x18, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79,
	0x2f, 0x67, 0x65, 0x74, 0x54, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x12, 0x75, 0x0a, 0x06,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x12, 0x21, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e,
	0x74, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x63, 0x6c, 0x75, 0x74,
	0x63, 0x68, 0x2e, 0x74, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0xaa,
	0xe1, 0x1c, 0x02, 0x08, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18, 0x3a, 0x01, 0x2a, 0x22, 0x13,
	0x2f, 0x76, 0x31, 0x2f, 0x74, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x2f, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x42, 0x3b, 0x5a, 0x39, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x6c, 0x79, 0x66, 0x74, 0x2f, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2f, 0x62, 0x61,
	0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x6f, 0x70, 0x6f, 0x6c, 0x6f,
	0x67, 0x79, 0x2f, 0x76, 0x31, 0x3b, 0x74, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x76, 0x31,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_topology_v1_topology_api_proto_rawDescOnce sync.Once
	file_topology_v1_topology_api_proto_rawDescData = file_topology_v1_topology_api_proto_rawDesc
)

func file_topology_v1_topology_api_proto_rawDescGZIP() []byte {
	file_topology_v1_topology_api_proto_rawDescOnce.Do(func() {
		file_topology_v1_topology_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_topology_v1_topology_api_proto_rawDescData)
	})
	return file_topology_v1_topology_api_proto_rawDescData
}

var file_topology_v1_topology_api_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_topology_v1_topology_api_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_topology_v1_topology_api_proto_goTypes = []interface{}{
	(SearchRequest_Sort_Direction)(0),   // 0: clutch.topology.v1.SearchRequest.Sort.Direction
	(Constraint_Operator)(0),            // 1: clutch.topology.v1.Constraint.Operator
	(MetadataQuery_Aggregation)(0),      // 2: clutch.topology.v1.MetadataQuery.Aggregation
	(UpdateCacheRequest_Action)(0),      // 3: clutch.topology.v1.UpdateCacheRequest.Action
	(*GetTopologyRequest)(nil),          // 4: clutch.topology.v1.GetTopologyRequest
	(*GetTopologyResponse)(nil),         // 5: clutch.topology.v1.GetTopologyResponse
	(*SearchRequest)(nil),               // 6: clutch.topology.v1.SearchRequest
	(*SearchResponse)(nil),              // 7: clutch.topology.v1.SearchResponse
	(*FeatureQuery)(nil),                // 8: clutch.topology.v1.FeatureQuery
	(*Constraint)(nil),                  // 9: clutch.topology.v1.Constraint
	(*MetadataQuery)(nil),               // 10: clutch.topology.v1.MetadataQuery
	(*Query)(nil),                       // 11: clutch.topology.v1.Query
	(*QueryResult)(nil),                 // 12: clutch.topology.v1.QueryResult
	(*Node)(nil),                        // 13: clutch.topology.v1.Node
	(*Edge)(nil),                        // 14: clutch.topology.v1.Edge
	(*Resource)(nil),                    // 15: clutch.topology.v1.Resource
	(*UpdateCacheRequest)(nil),          // 16: clutch.topology.v1.UpdateCacheRequest
	(*SearchRequest_Sort)(nil),          // 17: clutch.topology.v1.SearchRequest.Sort
	(*SearchRequest_Filter)(nil),        // 18: clutch.topology.v1.SearchRequest.Filter
	(*SearchRequest_Filter_Search)(nil), // 19: clutch.topology.v1.SearchRequest.Filter.Search
	nil,                                 // 20: clutch.topology.v1.SearchRequest.Filter.MetadataEntry
	nil,                                 // 21: clutch.topology.v1.QueryResult.NodesEntry
	nil,                                 // 22: clutch.topology.v1.QueryResult.EdgesEntry
	nil,                                 // 23: clutch.topology.v1.Node.FeaturesEntry
	nil,                                 // 24: clutch.topology.v1.Node.MetadataEntry
	nil,                                 // 25: clutch.topology.v1.Edge.MetadataEntry
	nil,                                 // 26: clutch.topology.v1.Resource.MetadataEntry
	(*structpb.Value)(nil),              // 27: google.protobuf.Value
	(*structpb.Struct)(nil),             // 28: google.protobuf.Struct
	(*status.Status)(nil),               // 29: google.rpc.Status
	(*anypb.Any)(nil),                   // 30: google.protobuf.Any
}
var file_topology_v1_topology_api_proto_depIdxs = []int32{
	11, // 0: clutch.topology.v1.GetTopologyRequest.queries:type_name -> clutch.topology.v1.Query
	12, // 1: clutch.topology.v1.GetTopologyResponse.results:type_name -> clutch.topology.v1.QueryResult
	17, // 2: clutch.topology.v1.SearchRequest.sort:type_name -> clutch.topology.v1.SearchRequest.Sort
	18, // 3: clutch.topology.v1.SearchRequest.filter:type_name -> clutch.topology.v1.SearchRequest.Filter
	15, // 4: clutch.topology.v1.SearchResponse.resources:type_name -> clutch.topology.v1.Resource
	1,  // 5: clutch.topology.v1.Constraint.operator:type_name -> clutch.topology.v1.Constraint.Operator
	27, // 6: clutch.topology.v1.Constraint.value:type_name -> google.protobuf.Value
	28, // 7: clutch.topology.v1.MetadataQuery.params:type_name -> google.protobuf.Struct
	2,  // 8: clutch.topology.v1.MetadataQuery.aggregation:type_name -> clutch.topology.v1.MetadataQuery.Aggregation
	9,  // 9: clutch.topology.v1.MetadataQuery.constraints:type_name -> clutch.topology.v1.Constraint
	8,  // 10: clutch.topology.v1.Query.features:type_name -> clutch.topology.v1.FeatureQuery
	10, // 11: clutch.topology.v1.Query.node_metadata:type_name -> clutch.topology.v1.MetadataQuery
	10, // 12: clutch.topology.v1.Query.edge_metadata:type_name -> clutch.topology.v1.MetadataQuery
	29, // 13: clutch.topology.v1.QueryResult.status:type_name -> google.rpc.Status
	11, // 14: clutch.topology.v1.QueryResult.query:type_name -> clutch.topology.v1.Query
	21, // 15: clutch.topology.v1.QueryResult.nodes:type_name -> clutch.topology.v1.QueryResult.NodesEntry
	22, // 16: clutch.topology.v1.QueryResult.edges:type_name -> clutch.topology.v1.QueryResult.EdgesEntry
	23, // 17: clutch.topology.v1.Node.features:type_name -> clutch.topology.v1.Node.FeaturesEntry
	24, // 18: clutch.topology.v1.Node.metadata:type_name -> clutch.topology.v1.Node.MetadataEntry
	25, // 19: clutch.topology.v1.Edge.metadata:type_name -> clutch.topology.v1.Edge.MetadataEntry
	30, // 20: clutch.topology.v1.Resource.pb:type_name -> google.protobuf.Any
	26, // 21: clutch.topology.v1.Resource.metadata:type_name -> clutch.topology.v1.Resource.MetadataEntry
	15, // 22: clutch.topology.v1.UpdateCacheRequest.resource:type_name -> clutch.topology.v1.Resource
	3,  // 23: clutch.topology.v1.UpdateCacheRequest.action:type_name -> clutch.topology.v1.UpdateCacheRequest.Action
	0,  // 24: clutch.topology.v1.SearchRequest.Sort.direction:type_name -> clutch.topology.v1.SearchRequest.Sort.Direction
	19, // 25: clutch.topology.v1.SearchRequest.Filter.search:type_name -> clutch.topology.v1.SearchRequest.Filter.Search
	20, // 26: clutch.topology.v1.SearchRequest.Filter.metadata:type_name -> clutch.topology.v1.SearchRequest.Filter.MetadataEntry
	13, // 27: clutch.topology.v1.QueryResult.NodesEntry.value:type_name -> clutch.topology.v1.Node
	14, // 28: clutch.topology.v1.QueryResult.EdgesEntry.value:type_name -> clutch.topology.v1.Edge
	27, // 29: clutch.topology.v1.Node.MetadataEntry.value:type_name -> google.protobuf.Value
	27, // 30: clutch.topology.v1.Edge.MetadataEntry.value:type_name -> google.protobuf.Value
	27, // 31: clutch.topology.v1.Resource.MetadataEntry.value:type_name -> google.protobuf.Value
	4,  // 32: clutch.topology.v1.TopologyAPI.GetTopology:input_type -> clutch.topology.v1.GetTopologyRequest
	6,  // 33: clutch.topology.v1.TopologyAPI.Search:input_type -> clutch.topology.v1.SearchRequest
	5,  // 34: clutch.topology.v1.TopologyAPI.GetTopology:output_type -> clutch.topology.v1.GetTopologyResponse
	7,  // 35: clutch.topology.v1.TopologyAPI.Search:output_type -> clutch.topology.v1.SearchResponse
	34, // [34:36] is the sub-list for method output_type
	32, // [32:34] is the sub-list for method input_type
	32, // [32:32] is the sub-list for extension type_name
	32, // [32:32] is the sub-list for extension extendee
	0,  // [0:32] is the sub-list for field type_name
}

func init() { file_topology_v1_topology_api_proto_init() }
func file_topology_v1_topology_api_proto_init() {
	if File_topology_v1_topology_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_topology_v1_topology_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTopologyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_topology_v1_topology_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTopologyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_topology_v1_topology_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_topology_v1_topology_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_topology_v1_topology_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeatureQuery); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_topology_v1_topology_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Constraint); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_topology_v1_topology_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MetadataQuery); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_topology_v1_topology_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Query); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_topology_v1_topology_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_topology_v1_topology_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Node); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_topology_v1_topology_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Edge); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_topology_v1_topology_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Resource); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_topology_v1_topology_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCacheRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_topology_v1_topology_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchRequest_Sort); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_topology_v1_topology_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_topology_v1_topology_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchRequest_Filter_Search); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_topology_v1_topology_api_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_topology_v1_topology_api_proto_goTypes,
		DependencyIndexes: file_topology_v1_topology_api_proto_depIdxs,
		EnumInfos:         file_topology_v1_topology_api_proto_enumTypes,
		MessageInfos:      file_topology_v1_topology_api_proto_msgTypes,
	}.Build()
	File_topology_v1_topology_api_proto = out.File
	file_topology_v1_topology_api_proto_rawDesc = nil
	file_topology_v1_topology_api_proto_goTypes = nil
	file_topology_v1_topology_api_proto_depIdxs = nil
}
