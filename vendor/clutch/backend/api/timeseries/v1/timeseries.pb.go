// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v3.17.3
// source: timeseries/v1/timeseries.proto

package timeseriesv1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	anypb "google.golang.org/protobuf/types/known/anypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TimeRange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartMillis int64 `protobuf:"varint,1,opt,name=start_millis,json=startMillis,proto3" json:"start_millis,omitempty"`
	EndMillis   int64 `protobuf:"varint,2,opt,name=end_millis,json=endMillis,proto3" json:"end_millis,omitempty"`
}

func (x *TimeRange) Reset() {
	*x = TimeRange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_timeseries_v1_timeseries_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeRange) ProtoMessage() {}

func (x *TimeRange) ProtoReflect() protoreflect.Message {
	mi := &file_timeseries_v1_timeseries_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeRange.ProtoReflect.Descriptor instead.
func (*TimeRange) Descriptor() ([]byte, []int) {
	return file_timeseries_v1_timeseries_proto_rawDescGZIP(), []int{0}
}

func (x *TimeRange) GetStartMillis() int64 {
	if x != nil {
		return x.StartMillis
	}
	return 0
}

func (x *TimeRange) GetEndMillis() int64 {
	if x != nil {
		return x.EndMillis
	}
	return 0
}

// A timeseries Point message is useful for organizing events
// to be displayed in a timeline view.  Users can transform data
// into a timeseries format and be able to organize them.
type Point struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Timestamp:
	//
	//	*Point_Range
	//	*Point_Millis
	Timestamp   isPoint_Timestamp `protobuf_oneof:"timestamp"`
	Pb          *anypb.Any        `protobuf:"bytes,3,opt,name=pb,proto3" json:"pb,omitempty"`
	Description string            `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	Href        string            `protobuf:"bytes,5,opt,name=href,proto3" json:"href,omitempty"`
}

func (x *Point) Reset() {
	*x = Point{}
	if protoimpl.UnsafeEnabled {
		mi := &file_timeseries_v1_timeseries_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Point) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Point) ProtoMessage() {}

func (x *Point) ProtoReflect() protoreflect.Message {
	mi := &file_timeseries_v1_timeseries_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Point.ProtoReflect.Descriptor instead.
func (*Point) Descriptor() ([]byte, []int) {
	return file_timeseries_v1_timeseries_proto_rawDescGZIP(), []int{1}
}

func (m *Point) GetTimestamp() isPoint_Timestamp {
	if m != nil {
		return m.Timestamp
	}
	return nil
}

func (x *Point) GetRange() *TimeRange {
	if x, ok := x.GetTimestamp().(*Point_Range); ok {
		return x.Range
	}
	return nil
}

func (x *Point) GetMillis() int64 {
	if x, ok := x.GetTimestamp().(*Point_Millis); ok {
		return x.Millis
	}
	return 0
}

func (x *Point) GetPb() *anypb.Any {
	if x != nil {
		return x.Pb
	}
	return nil
}

func (x *Point) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Point) GetHref() string {
	if x != nil {
		return x.Href
	}
	return ""
}

type isPoint_Timestamp interface {
	isPoint_Timestamp()
}

type Point_Range struct {
	Range *TimeRange `protobuf:"bytes,1,opt,name=range,proto3,oneof"`
}

type Point_Millis struct {
	Millis int64 `protobuf:"varint,2,opt,name=millis,proto3,oneof"`
}

func (*Point_Range) isPoint_Timestamp() {}

func (*Point_Millis) isPoint_Timestamp() {}

var File_timeseries_v1_timeseries_proto protoreflect.FileDescriptor

var file_timeseries_v1_timeseries_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2f, 0x76, 0x31, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x14, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x65, 0x72,
	0x69, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x61, 0x6e, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5f, 0x0a, 0x09, 0x54, 0x69,
	0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x2a, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x6d, 0x69, 0x6c, 0x6c, 0x69, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x72, 0x74, 0x4d, 0x69, 0x6c,
	0x6c, 0x69, 0x73, 0x12, 0x26, 0x0a, 0x0a, 0x65, 0x6e, 0x64, 0x5f, 0x6d, 0x69, 0x6c, 0x6c, 0x69,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x09, 0x65, 0x6e, 0x64, 0x4d, 0x69, 0x6c, 0x6c, 0x69, 0x73, 0x22, 0xc8, 0x01, 0x0a, 0x05,
	0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x37, 0x0a, 0x05, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x00, 0x52, 0x05, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x18,
	0x0a, 0x06, 0x6d, 0x69, 0x6c, 0x6c, 0x69, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00,
	0x52, 0x06, 0x6d, 0x69, 0x6c, 0x6c, 0x69, 0x73, 0x12, 0x24, 0x0a, 0x02, 0x70, 0x62, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x41, 0x6e, 0x79, 0x52, 0x02, 0x70, 0x62, 0x12, 0x20,
	0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x12, 0x0a, 0x04, 0x68, 0x72, 0x65, 0x66, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x68, 0x72, 0x65, 0x66, 0x42, 0x10, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x42, 0x3f, 0x5a, 0x3d, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6c, 0x79, 0x66, 0x74, 0x2f, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68,
	0x2f, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x65, 0x72, 0x69, 0x65, 0x73, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_timeseries_v1_timeseries_proto_rawDescOnce sync.Once
	file_timeseries_v1_timeseries_proto_rawDescData = file_timeseries_v1_timeseries_proto_rawDesc
)

func file_timeseries_v1_timeseries_proto_rawDescGZIP() []byte {
	file_timeseries_v1_timeseries_proto_rawDescOnce.Do(func() {
		file_timeseries_v1_timeseries_proto_rawDescData = protoimpl.X.CompressGZIP(file_timeseries_v1_timeseries_proto_rawDescData)
	})
	return file_timeseries_v1_timeseries_proto_rawDescData
}

var file_timeseries_v1_timeseries_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_timeseries_v1_timeseries_proto_goTypes = []interface{}{
	(*TimeRange)(nil), // 0: clutch.timeseries.v1.TimeRange
	(*Point)(nil),     // 1: clutch.timeseries.v1.Point
	(*anypb.Any)(nil), // 2: google.protobuf.Any
}
var file_timeseries_v1_timeseries_proto_depIdxs = []int32{
	0, // 0: clutch.timeseries.v1.Point.range:type_name -> clutch.timeseries.v1.TimeRange
	2, // 1: clutch.timeseries.v1.Point.pb:type_name -> google.protobuf.Any
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_timeseries_v1_timeseries_proto_init() }
func file_timeseries_v1_timeseries_proto_init() {
	if File_timeseries_v1_timeseries_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_timeseries_v1_timeseries_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeRange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_timeseries_v1_timeseries_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Point); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_timeseries_v1_timeseries_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*Point_Range)(nil),
		(*Point_Millis)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_timeseries_v1_timeseries_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_timeseries_v1_timeseries_proto_goTypes,
		DependencyIndexes: file_timeseries_v1_timeseries_proto_depIdxs,
		MessageInfos:      file_timeseries_v1_timeseries_proto_msgTypes,
	}.Build()
	File_timeseries_v1_timeseries_proto = out.File
	file_timeseries_v1_timeseries_proto_rawDesc = nil
	file_timeseries_v1_timeseries_proto_goTypes = nil
	file_timeseries_v1_timeseries_proto_depIdxs = nil
}
