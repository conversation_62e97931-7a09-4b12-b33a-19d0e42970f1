{"name": "@clutch-sh/data-layout", "version": "4.0.0-beta", "description": "Data Layout manager for clutch", "homepage": "https://clutch.sh/docs/development/frontend/overview#clutch-shdata-layout", "license": "Apache-2.0", "author": "<EMAIL>", "main": "dist/index.js", "types": "dist/index", "files": ["dist"], "scripts": {"build": "yarn clean && yarn compile", "clean": "yarn run package:clean", "compile": "yarn run package:compile", "compile:dev": "yarn run package:compile:dev", "compile:watch": "yarn run package:compile:watch", "lint": "yarn run package:lint", "lint:fix": "yarn run lint --fix", "prepublishOnly": "yarn run build", "publishBeta": "../../../tools/publish-frontend.sh data-layout", "test": "yarn run package:test", "test:coverage": "yarn run test --collect-coverage", "test:watch": "yarn run test --watch"}, "dependencies": {"@clutch-sh/core": "workspace:^", "lodash": "^4.17.0", "react-hook-thunk-reducer": "^0.2.1"}, "devDependencies": {"@clutch-sh/tools": "workspace:^", "react-test-renderer": "^17.0.2"}, "peerDependencies": {"react": "^17.0.2", "react-dom": "^17.0.2", "typescript": "^4.2.3"}, "engines": {"node": ">=18 <19", "yarn": "^4.5.0"}, "packageManager": "yarn@4.5.0", "stableVersion": "3.0.0-beta"}