{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/AutoscalerDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { fetchAutoscalerStatus, fetchMetrics, updateAutoscalerConfig } from '../store/slices/autoscalerSlice';\nimport { ArrowTrendingUpIcon, ArrowTrendingDownIcon, CpuChipIcon, ServerIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AutoscalerDashboard = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    status,\n    events,\n    loading,\n    error,\n    metrics\n  } = useSelector(state => state.autoscaler);\n  useEffect(() => {\n    dispatch(fetchAutoscalerStatus());\n    dispatch(fetchMetrics('1h'));\n  }, [dispatch]);\n  const handleToggleAutoscaler = () => {\n    if (status) {\n      dispatch(updateAutoscalerConfig({\n        enabled: !status.enabled\n      }));\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold text-white\",\n        children: \"Autoscaler Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-1 text-sm text-gray-400\",\n        children: \"Monitor and configure automatic scaling for your applications\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 overflow-hidden shadow rounded-lg\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-5\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(ServerIcon, {\n                className: \"h-6 w-6 text-blue-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 49,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-5 w-0 flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                  className: \"text-sm font-medium text-gray-400 truncate\",\n                  children: \"Current Replicas\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 53,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                  className: \"text-lg font-medium text-white\",\n                  children: (status === null || status === void 0 ? void 0 : status.current_replicas) || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 54,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 52,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 overflow-hidden shadow rounded-lg\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-5\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(ArrowTrendingUpIcon, {\n                className: \"h-6 w-6 text-green-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 67,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-5 w-0 flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                  className: \"text-sm font-medium text-gray-400 truncate\",\n                  children: \"Desired Replicas\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 71,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                  className: \"text-lg font-medium text-white\",\n                  children: (status === null || status === void 0 ? void 0 : status.desired_replicas) || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 72,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 70,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 overflow-hidden shadow rounded-lg\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-5\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(CpuChipIcon, {\n                className: \"h-6 w-6 text-yellow-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-5 w-0 flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                  className: \"text-sm font-medium text-gray-400 truncate\",\n                  children: \"CPU Utilization\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 89,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                  className: \"text-lg font-medium text-white\",\n                  children: [(status === null || status === void 0 ? void 0 : status.current_cpu_utilization) || 0, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 90,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 88,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 overflow-hidden shadow rounded-lg\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-5\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-3 h-3 rounded-full ${status !== null && status !== void 0 && status.enabled ? 'bg-green-400' : 'bg-red-400'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-5 w-0 flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                  className: \"text-sm font-medium text-gray-400 truncate\",\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 107,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                  className: \"text-lg font-medium text-white\",\n                  children: status !== null && status !== void 0 && status.enabled ? 'Enabled' : 'Disabled'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 108,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 shadow rounded-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-4 py-5 sm:p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg leading-6 font-medium text-white mb-4\",\n          children: \"Autoscaler Configuration\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this), status && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 gap-6 sm:grid-cols-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-300\",\n              children: \"Min Replicas\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-1\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                className: \"block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\",\n                value: status.min_replicas,\n                readOnly: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-300\",\n              children: \"Max Replicas\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-1\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                className: \"block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\",\n                value: status.max_replicas,\n                readOnly: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-300\",\n              children: \"Target CPU Utilization (%)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-1\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                className: \"block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\",\n                value: status.target_cpu_utilization,\n                readOnly: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-end\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleToggleAutoscaler,\n              disabled: loading,\n              className: `w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 ${status.enabled ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500' : 'bg-green-600 hover:bg-green-700 focus:ring-green-500'}`,\n              children: [status.enabled ? 'Disable' : 'Enable', \" Autoscaler\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 13\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 bg-red-50 border border-red-200 rounded-md p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-red-700\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 shadow rounded-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-4 py-5 sm:p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg leading-6 font-medium text-white mb-4\",\n          children: \"Recent Scaling Events\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this), events.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-8\",\n          children: [/*#__PURE__*/_jsxDEV(ArrowTrendingUpIcon, {\n            className: \"mx-auto h-12 w-12 text-gray-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"mt-2 text-sm font-medium text-gray-300\",\n            children: \"No scaling events\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-1 text-sm text-gray-400\",\n            children: \"Scaling events will appear here when they occur.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: events.slice(0, 10).map((event, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border border-gray-600 rounded-lg p-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [event.to_replicas > event.from_replicas ? /*#__PURE__*/_jsxDEV(ArrowTrendingUpIcon, {\n                  className: \"h-5 w-5 text-green-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 25\n                }, this) : /*#__PURE__*/_jsxDEV(ArrowTrendingDownIcon, {\n                  className: \"h-5 w-5 text-red-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-medium text-white\",\n                    children: [\"Scaled from \", event.from_replicas, \" to \", event.to_replicas, \" replicas\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 220,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-400\",\n                    children: event.reason\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 223,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500\",\n                    children: event.message\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 224,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs text-gray-400\",\n                children: new Date(event.timestamp).toLocaleString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 19\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 5\n  }, this);\n};\n_s(AutoscalerDashboard, \"R3dwKMBrlcSlLw6ialkeZapHtvA=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = AutoscalerDashboard;\nexport default AutoscalerDashboard;\nvar _c;\n$RefreshReg$(_c, \"AutoscalerDashboard\");", "map": {"version": 3, "names": ["React", "useEffect", "useDispatch", "useSelector", "fetchAutoscalerStatus", "fetchMetrics", "updateAutoscalerConfig", "ArrowTrendingUpIcon", "ArrowTrendingDownIcon", "CpuChipIcon", "ServerIcon", "jsxDEV", "_jsxDEV", "AutoscalerDashboard", "_s", "dispatch", "status", "events", "loading", "error", "metrics", "state", "autoscaler", "handleToggleAutoscaler", "enabled", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "current_replicas", "desired_replicas", "current_cpu_utilization", "type", "value", "min_replicas", "readOnly", "max_replicas", "target_cpu_utilization", "onClick", "disabled", "length", "slice", "map", "event", "index", "to_replicas", "from_replicas", "reason", "message", "Date", "timestamp", "toLocaleString", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/AutoscalerDashboard.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { RootState, AppDispatch } from '../store/store';\nimport {\n  fetchAutoscalerStatus,\n  fetchMetrics,\n  updateAutoscalerConfig,\n} from '../store/slices/autoscalerSlice';\nimport {\n  ArrowTrendingUpIcon,\n  ArrowTrendingDownIcon,\n  CpuChipIcon,\n  ServerIcon,\n} from '@heroicons/react/24/outline';\n\nconst AutoscalerDashboard: React.FC = () => {\n  const dispatch = useDispatch<AppDispatch>();\n  const { status, events, loading, error, metrics } = useSelector(\n    (state: RootState) => state.autoscaler\n  );\n\n  useEffect(() => {\n    dispatch(fetchAutoscalerStatus());\n    dispatch(fetchMetrics('1h'));\n  }, [dispatch]);\n\n  const handleToggleAutoscaler = () => {\n    if (status) {\n      dispatch(updateAutoscalerConfig({ enabled: !status.enabled }));\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div>\n        <h1 className=\"text-2xl font-bold text-white\">Autoscaler Dashboard</h1>\n        <p className=\"mt-1 text-sm text-gray-400\">\n          Monitor and configure automatic scaling for your applications\n        </p>\n      </div>\n\n      {/* Status Cards */}\n      <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\">\n        <div className=\"bg-gray-800 overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <ServerIcon className=\"h-6 w-6 text-blue-400\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-400 truncate\">Current Replicas</dt>\n                  <dd className=\"text-lg font-medium text-white\">\n                    {status?.current_replicas || 0}\n                  </dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-gray-800 overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <ArrowTrendingUpIcon className=\"h-6 w-6 text-green-400\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-400 truncate\">Desired Replicas</dt>\n                  <dd className=\"text-lg font-medium text-white\">\n                    {status?.desired_replicas || 0}\n                  </dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-gray-800 overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <CpuChipIcon className=\"h-6 w-6 text-yellow-400\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-400 truncate\">CPU Utilization</dt>\n                  <dd className=\"text-lg font-medium text-white\">\n                    {status?.current_cpu_utilization || 0}%\n                  </dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-gray-800 overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className={`w-3 h-3 rounded-full ${status?.enabled ? 'bg-green-400' : 'bg-red-400'}`} />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-400 truncate\">Status</dt>\n                  <dd className=\"text-lg font-medium text-white\">\n                    {status?.enabled ? 'Enabled' : 'Disabled'}\n                  </dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Configuration */}\n      <div className=\"bg-gray-800 shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <h3 className=\"text-lg leading-6 font-medium text-white mb-4\">\n            Autoscaler Configuration\n          </h3>\n\n          {status && (\n            <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300\">\n                  Min Replicas\n                </label>\n                <div className=\"mt-1\">\n                  <input\n                    type=\"number\"\n                    className=\"block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\"\n                    value={status.min_replicas}\n                    readOnly\n                  />\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300\">\n                  Max Replicas\n                </label>\n                <div className=\"mt-1\">\n                  <input\n                    type=\"number\"\n                    className=\"block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\"\n                    value={status.max_replicas}\n                    readOnly\n                  />\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-300\">\n                  Target CPU Utilization (%)\n                </label>\n                <div className=\"mt-1\">\n                  <input\n                    type=\"number\"\n                    className=\"block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\"\n                    value={status.target_cpu_utilization}\n                    readOnly\n                  />\n                </div>\n              </div>\n\n              <div className=\"flex items-end\">\n                <button\n                  onClick={handleToggleAutoscaler}\n                  disabled={loading}\n                  className={`w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 ${\n                    status.enabled\n                      ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500'\n                      : 'bg-green-600 hover:bg-green-700 focus:ring-green-500'\n                  }`}\n                >\n                  {status.enabled ? 'Disable' : 'Enable'} Autoscaler\n                </button>\n              </div>\n            </div>\n          )}\n\n          {error && (\n            <div className=\"mt-4 bg-red-50 border border-red-200 rounded-md p-4\">\n              <div className=\"text-sm text-red-700\">{error}</div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Scaling Events */}\n      <div className=\"bg-gray-800 shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <h3 className=\"text-lg leading-6 font-medium text-white mb-4\">\n            Recent Scaling Events\n          </h3>\n\n          {events.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <ArrowTrendingUpIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n              <h3 className=\"mt-2 text-sm font-medium text-gray-300\">No scaling events</h3>\n              <p className=\"mt-1 text-sm text-gray-400\">\n                Scaling events will appear here when they occur.\n              </p>\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              {events.slice(0, 10).map((event, index) => (\n                <div key={index} className=\"border border-gray-600 rounded-lg p-4\">\n                  <div className=\"flex items-start justify-between\">\n                    <div className=\"flex items-center space-x-3\">\n                      {event.to_replicas > event.from_replicas ? (\n                        <ArrowTrendingUpIcon className=\"h-5 w-5 text-green-400\" />\n                      ) : (\n                        <ArrowTrendingDownIcon className=\"h-5 w-5 text-red-400\" />\n                      )}\n                      <div>\n                        <p className=\"text-sm font-medium text-white\">\n                          Scaled from {event.from_replicas} to {event.to_replicas} replicas\n                        </p>\n                        <p className=\"text-sm text-gray-400\">{event.reason}</p>\n                        <p className=\"text-xs text-gray-500\">{event.message}</p>\n                      </div>\n                    </div>\n                    <span className=\"text-xs text-gray-400\">\n                      {new Date(event.timestamp).toLocaleString()}\n                    </span>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AutoscalerDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,SACEC,qBAAqB,EACrBC,YAAY,EACZC,sBAAsB,QACjB,iCAAiC;AACxC,SACEC,mBAAmB,EACnBC,qBAAqB,EACrBC,WAAW,EACXC,UAAU,QACL,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,mBAA6B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1C,MAAMC,QAAQ,GAAGb,WAAW,CAAc,CAAC;EAC3C,MAAM;IAAEc,MAAM;IAAEC,MAAM;IAAEC,OAAO;IAAEC,KAAK;IAAEC;EAAQ,CAAC,GAAGjB,WAAW,CAC5DkB,KAAgB,IAAKA,KAAK,CAACC,UAC9B,CAAC;EAEDrB,SAAS,CAAC,MAAM;IACdc,QAAQ,CAACX,qBAAqB,CAAC,CAAC,CAAC;IACjCW,QAAQ,CAACV,YAAY,CAAC,IAAI,CAAC,CAAC;EAC9B,CAAC,EAAE,CAACU,QAAQ,CAAC,CAAC;EAEd,MAAMQ,sBAAsB,GAAGA,CAAA,KAAM;IACnC,IAAIP,MAAM,EAAE;MACVD,QAAQ,CAACT,sBAAsB,CAAC;QAAEkB,OAAO,EAAE,CAACR,MAAM,CAACQ;MAAQ,CAAC,CAAC,CAAC;IAChE;EACF,CAAC;EAED,oBACEZ,OAAA;IAAKa,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBd,OAAA;MAAAc,QAAA,gBACEd,OAAA;QAAIa,SAAS,EAAC,+BAA+B;QAAAC,QAAA,EAAC;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvElB,OAAA;QAAGa,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAE1C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNlB,OAAA;MAAKa,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBACnEd,OAAA;QAAKa,SAAS,EAAC,+CAA+C;QAAAC,QAAA,eAC5Dd,OAAA;UAAKa,SAAS,EAAC,KAAK;UAAAC,QAAA,eAClBd,OAAA;YAAKa,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCd,OAAA;cAAKa,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5Bd,OAAA,CAACF,UAAU;gBAACe,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACNlB,OAAA;cAAKa,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9Bd,OAAA;gBAAAc,QAAA,gBACEd,OAAA;kBAAIa,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChFlB,OAAA;kBAAIa,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAC3C,CAAAV,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEe,gBAAgB,KAAI;gBAAC;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlB,OAAA;QAAKa,SAAS,EAAC,+CAA+C;QAAAC,QAAA,eAC5Dd,OAAA;UAAKa,SAAS,EAAC,KAAK;UAAAC,QAAA,eAClBd,OAAA;YAAKa,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCd,OAAA;cAAKa,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5Bd,OAAA,CAACL,mBAAmB;gBAACkB,SAAS,EAAC;cAAwB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eACNlB,OAAA;cAAKa,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9Bd,OAAA;gBAAAc,QAAA,gBACEd,OAAA;kBAAIa,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChFlB,OAAA;kBAAIa,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAC3C,CAAAV,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEgB,gBAAgB,KAAI;gBAAC;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlB,OAAA;QAAKa,SAAS,EAAC,+CAA+C;QAAAC,QAAA,eAC5Dd,OAAA;UAAKa,SAAS,EAAC,KAAK;UAAAC,QAAA,eAClBd,OAAA;YAAKa,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCd,OAAA;cAAKa,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5Bd,OAAA,CAACH,WAAW;gBAACgB,SAAS,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACNlB,OAAA;cAAKa,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9Bd,OAAA;gBAAAc,QAAA,gBACEd,OAAA;kBAAIa,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC/ElB,OAAA;kBAAIa,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,GAC3C,CAAAV,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEiB,uBAAuB,KAAI,CAAC,EAAC,GACxC;gBAAA;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlB,OAAA;QAAKa,SAAS,EAAC,+CAA+C;QAAAC,QAAA,eAC5Dd,OAAA;UAAKa,SAAS,EAAC,KAAK;UAAAC,QAAA,eAClBd,OAAA;YAAKa,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCd,OAAA;cAAKa,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5Bd,OAAA;gBAAKa,SAAS,EAAE,wBAAwBT,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEQ,OAAO,GAAG,cAAc,GAAG,YAAY;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1F,CAAC,eACNlB,OAAA;cAAKa,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9Bd,OAAA;gBAAAc,QAAA,gBACEd,OAAA;kBAAIa,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACtElB,OAAA;kBAAIa,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAC3CV,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEQ,OAAO,GAAG,SAAS,GAAG;gBAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlB,OAAA;MAAKa,SAAS,EAAC,+BAA+B;MAAAC,QAAA,eAC5Cd,OAAA;QAAKa,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/Bd,OAAA;UAAIa,SAAS,EAAC,+CAA+C;UAAAC,QAAA,EAAC;QAE9D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAEJd,MAAM,iBACLJ,OAAA;UAAKa,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDd,OAAA;YAAAc,QAAA,gBACEd,OAAA;cAAOa,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRlB,OAAA;cAAKa,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBd,OAAA;gBACEsB,IAAI,EAAC,QAAQ;gBACbT,SAAS,EAAC,qIAAqI;gBAC/IU,KAAK,EAAEnB,MAAM,CAACoB,YAAa;gBAC3BC,QAAQ;cAAA;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlB,OAAA;YAAAc,QAAA,gBACEd,OAAA;cAAOa,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRlB,OAAA;cAAKa,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBd,OAAA;gBACEsB,IAAI,EAAC,QAAQ;gBACbT,SAAS,EAAC,qIAAqI;gBAC/IU,KAAK,EAAEnB,MAAM,CAACsB,YAAa;gBAC3BD,QAAQ;cAAA;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlB,OAAA;YAAAc,QAAA,gBACEd,OAAA;cAAOa,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRlB,OAAA;cAAKa,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBd,OAAA;gBACEsB,IAAI,EAAC,QAAQ;gBACbT,SAAS,EAAC,qIAAqI;gBAC/IU,KAAK,EAAEnB,MAAM,CAACuB,sBAAuB;gBACrCF,QAAQ;cAAA;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlB,OAAA;YAAKa,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7Bd,OAAA;cACE4B,OAAO,EAAEjB,sBAAuB;cAChCkB,QAAQ,EAAEvB,OAAQ;cAClBO,SAAS,EAAE,8LACTT,MAAM,CAACQ,OAAO,GACV,gDAAgD,GAChD,sDAAsD,EACzD;cAAAE,QAAA,GAEFV,MAAM,CAACQ,OAAO,GAAG,SAAS,GAAG,QAAQ,EAAC,aACzC;YAAA;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAX,KAAK,iBACJP,OAAA;UAAKa,SAAS,EAAC,qDAAqD;UAAAC,QAAA,eAClEd,OAAA;YAAKa,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAEP;UAAK;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlB,OAAA;MAAKa,SAAS,EAAC,+BAA+B;MAAAC,QAAA,eAC5Cd,OAAA;QAAKa,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/Bd,OAAA;UAAIa,SAAS,EAAC,+CAA+C;UAAAC,QAAA,EAAC;QAE9D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAEJb,MAAM,CAACyB,MAAM,KAAK,CAAC,gBAClB9B,OAAA;UAAKa,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/Bd,OAAA,CAACL,mBAAmB;YAACkB,SAAS,EAAC;UAAiC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnElB,OAAA;YAAIa,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7ElB,OAAA;YAAGa,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAE1C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,gBAENlB,OAAA;UAAKa,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBT,MAAM,CAAC0B,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACC,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBACpClC,OAAA;YAAiBa,SAAS,EAAC,uCAAuC;YAAAC,QAAA,eAChEd,OAAA;cAAKa,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/Cd,OAAA;gBAAKa,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,GACzCmB,KAAK,CAACE,WAAW,GAAGF,KAAK,CAACG,aAAa,gBACtCpC,OAAA,CAACL,mBAAmB;kBAACkB,SAAS,EAAC;gBAAwB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAE1DlB,OAAA,CAACJ,qBAAqB;kBAACiB,SAAS,EAAC;gBAAsB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAC1D,eACDlB,OAAA;kBAAAc,QAAA,gBACEd,OAAA;oBAAGa,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,GAAC,cAChC,EAACmB,KAAK,CAACG,aAAa,EAAC,MAAI,EAACH,KAAK,CAACE,WAAW,EAAC,WAC1D;kBAAA;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACJlB,OAAA;oBAAGa,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAEmB,KAAK,CAACI;kBAAM;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvDlB,OAAA;oBAAGa,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAEmB,KAAK,CAACK;kBAAO;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlB,OAAA;gBAAMa,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EACpC,IAAIyB,IAAI,CAACN,KAAK,CAACO,SAAS,CAAC,CAACC,cAAc,CAAC;cAAC;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC,GAnBEgB,KAAK;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoBV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChB,EAAA,CA/NID,mBAA6B;EAAA,QAChBX,WAAW,EACwBC,WAAW;AAAA;AAAAmD,EAAA,GAF3DzC,mBAA6B;AAiOnC,eAAeA,mBAAmB;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}