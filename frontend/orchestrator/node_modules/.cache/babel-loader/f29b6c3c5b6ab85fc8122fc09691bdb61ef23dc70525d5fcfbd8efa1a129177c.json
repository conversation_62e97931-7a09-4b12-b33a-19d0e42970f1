{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/Dashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { CogIcon, MagnifyingGlassIcon, GlobeAltIcon, ArrowTrendingUpIcon, ShieldCheckIcon, CircleStackIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst serviceIcons = {\n  audit: ShieldCheckIcon,\n  autoscaler: ArrowTrendingUpIcon,\n  db_admin: CircleStackIcon,\n  discovery: MagnifyingGlassIcon,\n  envoy_control_plane: GlobeAltIcon,\n  workflow: CogIcon\n};\nexport function Dashboard() {\n  _s();\n  const [health, setHealth] = useState(null);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    const fetchHealth = async () => {\n      try {\n        const response = await fetch('/health');\n        const data = await response.json();\n        setHealth(data);\n      } catch (error) {\n        console.error('Failed to fetch health status:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchHealth();\n    const interval = setInterval(fetchHealth, 30000); // Refresh every 30 seconds\n\n    return () => clearInterval(interval);\n  }, []);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-cyan-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold text-white\",\n        children: \"CAINuro Orchestrator Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-2 text-gray-400\",\n        children: \"Real-time system status and service monitoring\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 overflow-hidden shadow rounded-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-4 py-5 sm:p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `w-8 h-8 rounded-full flex items-center justify-center ${(health === null || health === void 0 ? void 0 : health.status) === 'healthy' ? 'bg-green-100' : 'bg-red-100'}`,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-4 h-4 rounded-full ${(health === null || health === void 0 ? void 0 : health.status) === 'healthy' ? 'bg-green-500' : 'bg-red-500'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-5 w-0 flex-1\",\n            children: /*#__PURE__*/_jsxDEV(\"dl\", {\n              children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                className: \"text-sm font-medium text-gray-400 truncate\",\n                children: \"System Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                className: \"flex items-baseline\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-2xl font-semibold text-white\",\n                  children: (health === null || health === void 0 ? void 0 : health.status) === 'healthy' ? 'All Systems Operational' : 'System Issues Detected'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 86,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ml-2 flex items-baseline text-sm font-semibold text-green-400\",\n                  children: [health === null || health === void 0 ? void 0 : health.mode, \" Mode\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 89,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3\",\n      children: (health === null || health === void 0 ? void 0 : health.services) && Object.entries(health.services).map(([serviceName, status]) => {\n        const IconComponent = serviceIcons[serviceName] || CogIcon;\n        const isActive = status === 'active';\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-800 overflow-hidden shadow rounded-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-5\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(IconComponent, {\n                  className: `h-6 w-6 ${isActive ? 'text-green-400' : 'text-red-400'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 110,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-5 w-0 flex-1\",\n                children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                    className: \"text-sm font-medium text-gray-400 truncate\",\n                    children: serviceName.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase())\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 114,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                    className: \"text-lg font-medium text-white\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                      children: status\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 118,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 117,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 113,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 15\n          }, this)\n        }, serviceName, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 shadow rounded-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-4 py-5 sm:p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg leading-6 font-medium text-white\",\n          children: \"Quick Actions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-5 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"relative group bg-gray-700 p-3 focus:outline-none focus:ring-2 focus:ring-cyan-500 rounded-lg hover:bg-gray-600 transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"rounded-lg inline-flex p-3 bg-cyan-600 text-white\",\n                children: /*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n                  className: \"h-6 w-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 143,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-white\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"absolute inset-0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 19\n                }, this), \"Discover Resources\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-2 text-sm text-gray-400\",\n                children: \"Search across AWS, GCP, and Azure\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"relative group bg-gray-700 p-3 focus:outline-none focus:ring-2 focus:ring-cyan-500 rounded-lg hover:bg-gray-600 transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"rounded-lg inline-flex p-3 bg-purple-600 text-white\",\n                children: /*#__PURE__*/_jsxDEV(CogIcon, {\n                  className: \"h-6 w-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-white\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"absolute inset-0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 19\n                }, this), \"Run Workflow\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-2 text-sm text-gray-400\",\n                children: \"Execute automation workflows\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"relative group bg-gray-700 p-3 focus:outline-none focus:ring-2 focus:ring-cyan-500 rounded-lg hover:bg-gray-600 transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"rounded-lg inline-flex p-3 bg-green-600 text-white\",\n                children: /*#__PURE__*/_jsxDEV(GlobeAltIcon, {\n                  className: \"h-6 w-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-white\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"absolute inset-0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 19\n                }, this), \"Envoy Config\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-2 text-sm text-gray-400\",\n                children: \"Manage Envoy configurations\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"relative group bg-gray-700 p-3 focus:outline-none focus:ring-2 focus:ring-cyan-500 rounded-lg hover:bg-gray-600 transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"rounded-lg inline-flex p-3 bg-orange-600 text-white\",\n                children: /*#__PURE__*/_jsxDEV(ShieldCheckIcon, {\n                  className: \"h-6 w-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-white\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"absolute inset-0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 19\n                }, this), \"Audit Logs\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-2 text-sm text-gray-400\",\n                children: \"View tamper-proof audit trail\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 shadow rounded-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-4 py-5 sm:p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg leading-6 font-medium text-white\",\n          children: \"System Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"dl\", {\n          className: \"mt-5 grid grid-cols-1 gap-5 sm:grid-cols-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n              className: \"text-sm font-medium text-gray-400\",\n              children: \"Version\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n              className: \"mt-1 text-sm text-white\",\n              children: health === null || health === void 0 ? void 0 : health.version\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n              className: \"text-sm font-medium text-gray-400\",\n              children: \"Mode\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n              className: \"mt-1 text-sm text-white\",\n              children: health === null || health === void 0 ? void 0 : health.mode\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n              className: \"text-sm font-medium text-gray-400\",\n              children: \"Services\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n              className: \"mt-1 text-sm text-white\",\n              children: [health !== null && health !== void 0 && health.services ? Object.keys(health.services).length : 0, \" active\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 60,\n    columnNumber: 5\n  }, this);\n}\n_s(Dashboard, \"gVMhxzuGfgUYihkEAWnOGJCEy3Y=\");\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "CogIcon", "MagnifyingGlassIcon", "GlobeAltIcon", "ArrowTrendingUpIcon", "ShieldCheckIcon", "CircleStackIcon", "jsxDEV", "_jsxDEV", "serviceIcons", "audit", "autoscaler", "db_admin", "discovery", "envoy_control_plane", "workflow", "Dashboard", "_s", "health", "setHealth", "loading", "setLoading", "fetchHealth", "response", "fetch", "data", "json", "error", "console", "interval", "setInterval", "clearInterval", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "status", "mode", "services", "Object", "entries", "map", "serviceName", "IconComponent", "isActive", "replace", "l", "toUpperCase", "version", "keys", "length", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/Dashboard.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport {\n  ChartBarIcon,\n  CogIcon,\n  MagnifyingGlassIcon,\n  GlobeAltIcon,\n  ArrowTrendingUpIcon,\n  ShieldCheckIcon,\n  CircleStackIcon,\n} from '@heroicons/react/24/outline';\n\ninterface HealthStatus {\n  mode: string;\n  services: Record<string, string>;\n  status: string;\n  version: string;\n}\n\nconst serviceIcons: Record<string, any> = {\n  audit: ShieldCheckIcon,\n  autoscaler: ArrowTrendingUpIcon,\n  db_admin: CircleStackIcon,\n  discovery: MagnifyingGlassIcon,\n  envoy_control_plane: GlobeAltIcon,\n  workflow: CogIcon,\n};\n\nexport function Dashboard() {\n  const [health, setHealth] = useState<HealthStatus | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchHealth = async () => {\n      try {\n        const response = await fetch('/health');\n        const data = await response.json();\n        setHealth(data);\n      } catch (error) {\n        console.error('Failed to fetch health status:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchHealth();\n    const interval = setInterval(fetchHealth, 30000); // Refresh every 30 seconds\n\n    return () => clearInterval(interval);\n  }, []);\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-cyan-500\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div>\n        <h1 className=\"text-3xl font-bold text-white\">CAINuro Orchestrator Dashboard</h1>\n        <p className=\"mt-2 text-gray-400\">\n          Real-time system status and service monitoring\n        </p>\n      </div>\n\n      {/* Status Overview */}\n      <div className=\"bg-gray-800 overflow-hidden shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${\n                health?.status === 'healthy' ? 'bg-green-100' : 'bg-red-100'\n              }`}>\n                <div className={`w-4 h-4 rounded-full ${\n                  health?.status === 'healthy' ? 'bg-green-500' : 'bg-red-500'\n                }`}></div>\n              </div>\n            </div>\n            <div className=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt className=\"text-sm font-medium text-gray-400 truncate\">System Status</dt>\n                <dd className=\"flex items-baseline\">\n                  <div className=\"text-2xl font-semibold text-white\">\n                    {health?.status === 'healthy' ? 'All Systems Operational' : 'System Issues Detected'}\n                  </div>\n                  <div className=\"ml-2 flex items-baseline text-sm font-semibold text-green-400\">\n                    {health?.mode} Mode\n                  </div>\n                </dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Services Grid */}\n      <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3\">\n        {health?.services && Object.entries(health.services).map(([serviceName, status]) => {\n          const IconComponent = serviceIcons[serviceName] || CogIcon;\n          const isActive = status === 'active';\n          \n          return (\n            <div key={serviceName} className=\"bg-gray-800 overflow-hidden shadow rounded-lg\">\n              <div className=\"p-5\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <IconComponent className={`h-6 w-6 ${isActive ? 'text-green-400' : 'text-red-400'}`} />\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-400 truncate\">\n                        {serviceName.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase())}\n                      </dt>\n                      <dd className=\"text-lg font-medium text-white\">\n                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                          isActive \n                            ? 'bg-green-100 text-green-800' \n                            : 'bg-red-100 text-red-800'\n                        }`}>\n                          {status}\n                        </span>\n                      </dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n            </div>\n          );\n        })}\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"bg-gray-800 shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <h3 className=\"text-lg leading-6 font-medium text-white\">Quick Actions</h3>\n          <div className=\"mt-5 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\">\n            <button className=\"relative group bg-gray-700 p-3 focus:outline-none focus:ring-2 focus:ring-cyan-500 rounded-lg hover:bg-gray-600 transition-colors\">\n              <div>\n                <span className=\"rounded-lg inline-flex p-3 bg-cyan-600 text-white\">\n                  <MagnifyingGlassIcon className=\"h-6 w-6\" />\n                </span>\n              </div>\n              <div className=\"mt-8\">\n                <h3 className=\"text-lg font-medium text-white\">\n                  <span className=\"absolute inset-0\" />\n                  Discover Resources\n                </h3>\n                <p className=\"mt-2 text-sm text-gray-400\">\n                  Search across AWS, GCP, and Azure\n                </p>\n              </div>\n            </button>\n\n            <button className=\"relative group bg-gray-700 p-3 focus:outline-none focus:ring-2 focus:ring-cyan-500 rounded-lg hover:bg-gray-600 transition-colors\">\n              <div>\n                <span className=\"rounded-lg inline-flex p-3 bg-purple-600 text-white\">\n                  <CogIcon className=\"h-6 w-6\" />\n                </span>\n              </div>\n              <div className=\"mt-8\">\n                <h3 className=\"text-lg font-medium text-white\">\n                  <span className=\"absolute inset-0\" />\n                  Run Workflow\n                </h3>\n                <p className=\"mt-2 text-sm text-gray-400\">\n                  Execute automation workflows\n                </p>\n              </div>\n            </button>\n\n            <button className=\"relative group bg-gray-700 p-3 focus:outline-none focus:ring-2 focus:ring-cyan-500 rounded-lg hover:bg-gray-600 transition-colors\">\n              <div>\n                <span className=\"rounded-lg inline-flex p-3 bg-green-600 text-white\">\n                  <GlobeAltIcon className=\"h-6 w-6\" />\n                </span>\n              </div>\n              <div className=\"mt-8\">\n                <h3 className=\"text-lg font-medium text-white\">\n                  <span className=\"absolute inset-0\" />\n                  Envoy Config\n                </h3>\n                <p className=\"mt-2 text-sm text-gray-400\">\n                  Manage Envoy configurations\n                </p>\n              </div>\n            </button>\n\n            <button className=\"relative group bg-gray-700 p-3 focus:outline-none focus:ring-2 focus:ring-cyan-500 rounded-lg hover:bg-gray-600 transition-colors\">\n              <div>\n                <span className=\"rounded-lg inline-flex p-3 bg-orange-600 text-white\">\n                  <ShieldCheckIcon className=\"h-6 w-6\" />\n                </span>\n              </div>\n              <div className=\"mt-8\">\n                <h3 className=\"text-lg font-medium text-white\">\n                  <span className=\"absolute inset-0\" />\n                  Audit Logs\n                </h3>\n                <p className=\"mt-2 text-sm text-gray-400\">\n                  View tamper-proof audit trail\n                </p>\n              </div>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* System Info */}\n      <div className=\"bg-gray-800 shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <h3 className=\"text-lg leading-6 font-medium text-white\">System Information</h3>\n          <dl className=\"mt-5 grid grid-cols-1 gap-5 sm:grid-cols-3\">\n            <div>\n              <dt className=\"text-sm font-medium text-gray-400\">Version</dt>\n              <dd className=\"mt-1 text-sm text-white\">{health?.version}</dd>\n            </div>\n            <div>\n              <dt className=\"text-sm font-medium text-gray-400\">Mode</dt>\n              <dd className=\"mt-1 text-sm text-white\">{health?.mode}</dd>\n            </div>\n            <div>\n              <dt className=\"text-sm font-medium text-gray-400\">Services</dt>\n              <dd className=\"mt-1 text-sm text-white\">\n                {health?.services ? Object.keys(health.services).length : 0} active\n              </dd>\n            </div>\n          </dl>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAEEC,OAAO,EACPC,mBAAmB,EACnBC,YAAY,EACZC,mBAAmB,EACnBC,eAAe,EACfC,eAAe,QACV,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AASrC,MAAMC,YAAiC,GAAG;EACxCC,KAAK,EAAEL,eAAe;EACtBM,UAAU,EAAEP,mBAAmB;EAC/BQ,QAAQ,EAAEN,eAAe;EACzBO,SAAS,EAAEX,mBAAmB;EAC9BY,mBAAmB,EAAEX,YAAY;EACjCY,QAAQ,EAAEd;AACZ,CAAC;AAED,OAAO,SAASe,SAASA,CAAA,EAAG;EAAAC,EAAA;EAC1B,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGnB,QAAQ,CAAsB,IAAI,CAAC;EAC/D,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAE5CD,SAAS,CAAC,MAAM;IACd,MAAMuB,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC9B,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,SAAS,CAAC;QACvC,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;QAClCP,SAAS,CAACM,IAAI,CAAC;MACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACxD,CAAC,SAAS;QACRN,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDC,WAAW,CAAC,CAAC;IACb,MAAMO,QAAQ,GAAGC,WAAW,CAACR,WAAW,EAAE,KAAK,CAAC,CAAC,CAAC;;IAElD,OAAO,MAAMS,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIT,OAAO,EAAE;IACX,oBACEZ,OAAA;MAAKwB,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpDzB,OAAA;QAAKwB,SAAS,EAAC;MAAgE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnF,CAAC;EAEV;EAEA,oBACE7B,OAAA;IAAKwB,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBzB,OAAA;MAAAyB,QAAA,gBACEzB,OAAA;QAAIwB,SAAS,EAAC,+BAA+B;QAAAC,QAAA,EAAC;MAA8B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjF7B,OAAA;QAAGwB,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAElC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGN7B,OAAA;MAAKwB,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5DzB,OAAA;QAAKwB,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BzB,OAAA;UAAKwB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCzB,OAAA;YAAKwB,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5BzB,OAAA;cAAKwB,SAAS,EAAE,yDACd,CAAAd,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEoB,MAAM,MAAK,SAAS,GAAG,cAAc,GAAG,YAAY,EAC3D;cAAAL,QAAA,eACDzB,OAAA;gBAAKwB,SAAS,EAAE,wBACd,CAAAd,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEoB,MAAM,MAAK,SAAS,GAAG,cAAc,GAAG,YAAY;cAC3D;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN7B,OAAA;YAAKwB,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9BzB,OAAA;cAAAyB,QAAA,gBACEzB,OAAA;gBAAIwB,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7E7B,OAAA;gBAAIwB,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBACjCzB,OAAA;kBAAKwB,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAC/C,CAAAf,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEoB,MAAM,MAAK,SAAS,GAAG,yBAAyB,GAAG;gBAAwB;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF,CAAC,eACN7B,OAAA;kBAAKwB,SAAS,EAAC,+DAA+D;kBAAAC,QAAA,GAC3Ef,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEqB,IAAI,EAAC,OAChB;gBAAA;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7B,OAAA;MAAKwB,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClE,CAAAf,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEsB,QAAQ,KAAIC,MAAM,CAACC,OAAO,CAACxB,MAAM,CAACsB,QAAQ,CAAC,CAACG,GAAG,CAAC,CAAC,CAACC,WAAW,EAAEN,MAAM,CAAC,KAAK;QAClF,MAAMO,aAAa,GAAGpC,YAAY,CAACmC,WAAW,CAAC,IAAI3C,OAAO;QAC1D,MAAM6C,QAAQ,GAAGR,MAAM,KAAK,QAAQ;QAEpC,oBACE9B,OAAA;UAAuBwB,SAAS,EAAC,+CAA+C;UAAAC,QAAA,eAC9EzB,OAAA;YAAKwB,SAAS,EAAC,KAAK;YAAAC,QAAA,eAClBzB,OAAA;cAAKwB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCzB,OAAA;gBAAKwB,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5BzB,OAAA,CAACqC,aAAa;kBAACb,SAAS,EAAE,WAAWc,QAAQ,GAAG,gBAAgB,GAAG,cAAc;gBAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpF,CAAC,eACN7B,OAAA;gBAAKwB,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,eAC9BzB,OAAA;kBAAAyB,QAAA,gBACEzB,OAAA;oBAAIwB,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,EACvDW,WAAW,CAACG,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,OAAO,EAAEC,CAAC,IAAIA,CAAC,CAACC,WAAW,CAAC,CAAC;kBAAC;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE,CAAC,eACL7B,OAAA;oBAAIwB,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,eAC5CzB,OAAA;sBAAMwB,SAAS,EAAE,2EACfc,QAAQ,GACJ,6BAA6B,GAC7B,yBAAyB,EAC5B;sBAAAb,QAAA,EACAK;oBAAM;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAvBEO,WAAW;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAwBhB,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN7B,OAAA;MAAKwB,SAAS,EAAC,+BAA+B;MAAAC,QAAA,eAC5CzB,OAAA;QAAKwB,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BzB,OAAA;UAAIwB,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3E7B,OAAA;UAAKwB,SAAS,EAAC,2DAA2D;UAAAC,QAAA,gBACxEzB,OAAA;YAAQwB,SAAS,EAAC,mIAAmI;YAAAC,QAAA,gBACnJzB,OAAA;cAAAyB,QAAA,eACEzB,OAAA;gBAAMwB,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,eACjEzB,OAAA,CAACN,mBAAmB;kBAAC8B,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACN7B,OAAA;cAAKwB,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBzB,OAAA;gBAAIwB,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,gBAC5CzB,OAAA;kBAAMwB,SAAS,EAAC;gBAAkB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,sBAEvC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL7B,OAAA;gBAAGwB,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAET7B,OAAA;YAAQwB,SAAS,EAAC,mIAAmI;YAAAC,QAAA,gBACnJzB,OAAA;cAAAyB,QAAA,eACEzB,OAAA;gBAAMwB,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,eACnEzB,OAAA,CAACP,OAAO;kBAAC+B,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACN7B,OAAA;cAAKwB,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBzB,OAAA;gBAAIwB,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,gBAC5CzB,OAAA;kBAAMwB,SAAS,EAAC;gBAAkB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAEvC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL7B,OAAA;gBAAGwB,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAET7B,OAAA;YAAQwB,SAAS,EAAC,mIAAmI;YAAAC,QAAA,gBACnJzB,OAAA;cAAAyB,QAAA,eACEzB,OAAA;gBAAMwB,SAAS,EAAC,oDAAoD;gBAAAC,QAAA,eAClEzB,OAAA,CAACL,YAAY;kBAAC6B,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACN7B,OAAA;cAAKwB,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBzB,OAAA;gBAAIwB,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,gBAC5CzB,OAAA;kBAAMwB,SAAS,EAAC;gBAAkB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAEvC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL7B,OAAA;gBAAGwB,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAET7B,OAAA;YAAQwB,SAAS,EAAC,mIAAmI;YAAAC,QAAA,gBACnJzB,OAAA;cAAAyB,QAAA,eACEzB,OAAA;gBAAMwB,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,eACnEzB,OAAA,CAACH,eAAe;kBAAC2B,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACN7B,OAAA;cAAKwB,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBzB,OAAA;gBAAIwB,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,gBAC5CzB,OAAA;kBAAMwB,SAAS,EAAC;gBAAkB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,cAEvC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL7B,OAAA;gBAAGwB,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7B,OAAA;MAAKwB,SAAS,EAAC,+BAA+B;MAAAC,QAAA,eAC5CzB,OAAA;QAAKwB,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BzB,OAAA;UAAIwB,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChF7B,OAAA;UAAIwB,SAAS,EAAC,4CAA4C;UAAAC,QAAA,gBACxDzB,OAAA;YAAAyB,QAAA,gBACEzB,OAAA;cAAIwB,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9D7B,OAAA;cAAIwB,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAEf,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEgC;YAAO;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACN7B,OAAA;YAAAyB,QAAA,gBACEzB,OAAA;cAAIwB,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3D7B,OAAA;cAAIwB,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAEf,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEqB;YAAI;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eACN7B,OAAA;YAAAyB,QAAA,gBACEzB,OAAA;cAAIwB,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/D7B,OAAA;cAAIwB,SAAS,EAAC,yBAAyB;cAAAC,QAAA,GACpCf,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEsB,QAAQ,GAAGC,MAAM,CAACU,IAAI,CAACjC,MAAM,CAACsB,QAAQ,CAAC,CAACY,MAAM,GAAG,CAAC,EAAC,SAC9D;YAAA;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACpB,EAAA,CA/MeD,SAAS;AAAAqC,EAAA,GAATrC,SAAS;AAAA,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}