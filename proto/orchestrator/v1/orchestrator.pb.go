// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: proto/orchestrator/v1/orchestrator.proto

package orchestratorv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Common Resource message for discovery
type Resource struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Provider      string                 `protobuf:"bytes,1,opt,name=provider,proto3" json:"provider,omitempty"` // aws|gcp|azure
	Type          string                 `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Id            string                 `protobuf:"bytes,4,opt,name=id,proto3" json:"id,omitempty"`
	Tags          map[string]string      `protobuf:"bytes,5,rep,name=tags,proto3" json:"tags,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Resource) Reset() {
	*x = Resource{}
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Resource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Resource) ProtoMessage() {}

func (x *Resource) ProtoReflect() protoreflect.Message {
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Resource.ProtoReflect.Descriptor instead.
func (*Resource) Descriptor() ([]byte, []int) {
	return file_proto_orchestrator_v1_orchestrator_proto_rawDescGZIP(), []int{0}
}

func (x *Resource) GetProvider() string {
	if x != nil {
		return x.Provider
	}
	return ""
}

func (x *Resource) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Resource) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Resource) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Resource) GetTags() map[string]string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *Resource) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Resource) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// AutoScaler messages
type GetAutoscalerStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAutoscalerStatusRequest) Reset() {
	*x = GetAutoscalerStatusRequest{}
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAutoscalerStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAutoscalerStatusRequest) ProtoMessage() {}

func (x *GetAutoscalerStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAutoscalerStatusRequest.ProtoReflect.Descriptor instead.
func (*GetAutoscalerStatusRequest) Descriptor() ([]byte, []int) {
	return file_proto_orchestrator_v1_orchestrator_proto_rawDescGZIP(), []int{1}
}

type GetAutoscalerStatusResponse struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Enabled         bool                   `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	CurrentReplicas int32                  `protobuf:"varint,2,opt,name=current_replicas,json=currentReplicas,proto3" json:"current_replicas,omitempty"`
	DesiredReplicas int32                  `protobuf:"varint,3,opt,name=desired_replicas,json=desiredReplicas,proto3" json:"desired_replicas,omitempty"`
	Status          string                 `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *GetAutoscalerStatusResponse) Reset() {
	*x = GetAutoscalerStatusResponse{}
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAutoscalerStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAutoscalerStatusResponse) ProtoMessage() {}

func (x *GetAutoscalerStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAutoscalerStatusResponse.ProtoReflect.Descriptor instead.
func (*GetAutoscalerStatusResponse) Descriptor() ([]byte, []int) {
	return file_proto_orchestrator_v1_orchestrator_proto_rawDescGZIP(), []int{2}
}

func (x *GetAutoscalerStatusResponse) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *GetAutoscalerStatusResponse) GetCurrentReplicas() int32 {
	if x != nil {
		return x.CurrentReplicas
	}
	return 0
}

func (x *GetAutoscalerStatusResponse) GetDesiredReplicas() int32 {
	if x != nil {
		return x.DesiredReplicas
	}
	return 0
}

func (x *GetAutoscalerStatusResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type UpdateAutoscalerConfigRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Enabled          bool                   `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	MinReplicas      int32                  `protobuf:"varint,2,opt,name=min_replicas,json=minReplicas,proto3" json:"min_replicas,omitempty"`
	MaxReplicas      int32                  `protobuf:"varint,3,opt,name=max_replicas,json=maxReplicas,proto3" json:"max_replicas,omitempty"`
	TargetCpuPercent int32                  `protobuf:"varint,4,opt,name=target_cpu_percent,json=targetCpuPercent,proto3" json:"target_cpu_percent,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *UpdateAutoscalerConfigRequest) Reset() {
	*x = UpdateAutoscalerConfigRequest{}
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAutoscalerConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAutoscalerConfigRequest) ProtoMessage() {}

func (x *UpdateAutoscalerConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAutoscalerConfigRequest.ProtoReflect.Descriptor instead.
func (*UpdateAutoscalerConfigRequest) Descriptor() ([]byte, []int) {
	return file_proto_orchestrator_v1_orchestrator_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateAutoscalerConfigRequest) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *UpdateAutoscalerConfigRequest) GetMinReplicas() int32 {
	if x != nil {
		return x.MinReplicas
	}
	return 0
}

func (x *UpdateAutoscalerConfigRequest) GetMaxReplicas() int32 {
	if x != nil {
		return x.MaxReplicas
	}
	return 0
}

func (x *UpdateAutoscalerConfigRequest) GetTargetCpuPercent() int32 {
	if x != nil {
		return x.TargetCpuPercent
	}
	return 0
}

type UpdateAutoscalerConfigResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateAutoscalerConfigResponse) Reset() {
	*x = UpdateAutoscalerConfigResponse{}
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAutoscalerConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAutoscalerConfigResponse) ProtoMessage() {}

func (x *UpdateAutoscalerConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAutoscalerConfigResponse.ProtoReflect.Descriptor instead.
func (*UpdateAutoscalerConfigResponse) Descriptor() ([]byte, []int) {
	return file_proto_orchestrator_v1_orchestrator_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateAutoscalerConfigResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateAutoscalerConfigResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// Workflow messages
type ExecuteWorkflowRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	WorkflowId    string                 `protobuf:"bytes,1,opt,name=workflow_id,json=workflowId,proto3" json:"workflow_id,omitempty"`
	Inputs        map[string]string      `protobuf:"bytes,2,rep,name=inputs,proto3" json:"inputs,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExecuteWorkflowRequest) Reset() {
	*x = ExecuteWorkflowRequest{}
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExecuteWorkflowRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecuteWorkflowRequest) ProtoMessage() {}

func (x *ExecuteWorkflowRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecuteWorkflowRequest.ProtoReflect.Descriptor instead.
func (*ExecuteWorkflowRequest) Descriptor() ([]byte, []int) {
	return file_proto_orchestrator_v1_orchestrator_proto_rawDescGZIP(), []int{5}
}

func (x *ExecuteWorkflowRequest) GetWorkflowId() string {
	if x != nil {
		return x.WorkflowId
	}
	return ""
}

func (x *ExecuteWorkflowRequest) GetInputs() map[string]string {
	if x != nil {
		return x.Inputs
	}
	return nil
}

type ExecuteWorkflowResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ExecutionId   string                 `protobuf:"bytes,1,opt,name=execution_id,json=executionId,proto3" json:"execution_id,omitempty"`
	Status        string                 `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExecuteWorkflowResponse) Reset() {
	*x = ExecuteWorkflowResponse{}
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExecuteWorkflowResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecuteWorkflowResponse) ProtoMessage() {}

func (x *ExecuteWorkflowResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecuteWorkflowResponse.ProtoReflect.Descriptor instead.
func (*ExecuteWorkflowResponse) Descriptor() ([]byte, []int) {
	return file_proto_orchestrator_v1_orchestrator_proto_rawDescGZIP(), []int{6}
}

func (x *ExecuteWorkflowResponse) GetExecutionId() string {
	if x != nil {
		return x.ExecutionId
	}
	return ""
}

func (x *ExecuteWorkflowResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type GetWorkflowStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	WorkflowId    string                 `protobuf:"bytes,1,opt,name=workflow_id,json=workflowId,proto3" json:"workflow_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetWorkflowStatusRequest) Reset() {
	*x = GetWorkflowStatusRequest{}
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetWorkflowStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkflowStatusRequest) ProtoMessage() {}

func (x *GetWorkflowStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkflowStatusRequest.ProtoReflect.Descriptor instead.
func (*GetWorkflowStatusRequest) Descriptor() ([]byte, []int) {
	return file_proto_orchestrator_v1_orchestrator_proto_rawDescGZIP(), []int{7}
}

func (x *GetWorkflowStatusRequest) GetWorkflowId() string {
	if x != nil {
		return x.WorkflowId
	}
	return ""
}

type GetWorkflowStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	WorkflowId    string                 `protobuf:"bytes,1,opt,name=workflow_id,json=workflowId,proto3" json:"workflow_id,omitempty"`
	Status        string                 `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	StartedAt     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`
	CompletedAt   *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=completed_at,json=completedAt,proto3" json:"completed_at,omitempty"`
	Outputs       map[string]string      `protobuf:"bytes,5,rep,name=outputs,proto3" json:"outputs,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetWorkflowStatusResponse) Reset() {
	*x = GetWorkflowStatusResponse{}
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetWorkflowStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkflowStatusResponse) ProtoMessage() {}

func (x *GetWorkflowStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkflowStatusResponse.ProtoReflect.Descriptor instead.
func (*GetWorkflowStatusResponse) Descriptor() ([]byte, []int) {
	return file_proto_orchestrator_v1_orchestrator_proto_rawDescGZIP(), []int{8}
}

func (x *GetWorkflowStatusResponse) GetWorkflowId() string {
	if x != nil {
		return x.WorkflowId
	}
	return ""
}

func (x *GetWorkflowStatusResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *GetWorkflowStatusResponse) GetStartedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.StartedAt
	}
	return nil
}

func (x *GetWorkflowStatusResponse) GetCompletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletedAt
	}
	return nil
}

func (x *GetWorkflowStatusResponse) GetOutputs() map[string]string {
	if x != nil {
		return x.Outputs
	}
	return nil
}

type ListWorkflowsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PageSize      int32                  `protobuf:"varint,1,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	PageToken     string                 `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListWorkflowsRequest) Reset() {
	*x = ListWorkflowsRequest{}
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListWorkflowsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWorkflowsRequest) ProtoMessage() {}

func (x *ListWorkflowsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWorkflowsRequest.ProtoReflect.Descriptor instead.
func (*ListWorkflowsRequest) Descriptor() ([]byte, []int) {
	return file_proto_orchestrator_v1_orchestrator_proto_rawDescGZIP(), []int{9}
}

func (x *ListWorkflowsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListWorkflowsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

type ListWorkflowsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Workflows     []*WorkflowInfo        `protobuf:"bytes,1,rep,name=workflows,proto3" json:"workflows,omitempty"`
	NextPageToken string                 `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListWorkflowsResponse) Reset() {
	*x = ListWorkflowsResponse{}
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListWorkflowsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListWorkflowsResponse) ProtoMessage() {}

func (x *ListWorkflowsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListWorkflowsResponse.ProtoReflect.Descriptor instead.
func (*ListWorkflowsResponse) Descriptor() ([]byte, []int) {
	return file_proto_orchestrator_v1_orchestrator_proto_rawDescGZIP(), []int{10}
}

func (x *ListWorkflowsResponse) GetWorkflows() []*WorkflowInfo {
	if x != nil {
		return x.Workflows
	}
	return nil
}

func (x *ListWorkflowsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

type WorkflowInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WorkflowInfo) Reset() {
	*x = WorkflowInfo{}
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkflowInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkflowInfo) ProtoMessage() {}

func (x *WorkflowInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkflowInfo.ProtoReflect.Descriptor instead.
func (*WorkflowInfo) Descriptor() ([]byte, []int) {
	return file_proto_orchestrator_v1_orchestrator_proto_rawDescGZIP(), []int{11}
}

func (x *WorkflowInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *WorkflowInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *WorkflowInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *WorkflowInfo) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

// Discovery messages
type SearchResourcesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Provider      string                 `protobuf:"bytes,1,opt,name=provider,proto3" json:"provider,omitempty"` // aws|gcp|azure
	Query         string                 `protobuf:"bytes,2,opt,name=query,proto3" json:"query,omitempty"`
	Filters       map[string]string      `protobuf:"bytes,3,rep,name=filters,proto3" json:"filters,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Limit         int32                  `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchResourcesRequest) Reset() {
	*x = SearchResourcesRequest{}
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchResourcesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchResourcesRequest) ProtoMessage() {}

func (x *SearchResourcesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchResourcesRequest.ProtoReflect.Descriptor instead.
func (*SearchResourcesRequest) Descriptor() ([]byte, []int) {
	return file_proto_orchestrator_v1_orchestrator_proto_rawDescGZIP(), []int{12}
}

func (x *SearchResourcesRequest) GetProvider() string {
	if x != nil {
		return x.Provider
	}
	return ""
}

func (x *SearchResourcesRequest) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *SearchResourcesRequest) GetFilters() map[string]string {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *SearchResourcesRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type SearchResourcesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Resources     []*Resource            `protobuf:"bytes,1,rep,name=resources,proto3" json:"resources,omitempty"`
	NextToken     string                 `protobuf:"bytes,2,opt,name=next_token,json=nextToken,proto3" json:"next_token,omitempty"`
	TotalCount    int32                  `protobuf:"varint,3,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchResourcesResponse) Reset() {
	*x = SearchResourcesResponse{}
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchResourcesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchResourcesResponse) ProtoMessage() {}

func (x *SearchResourcesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchResourcesResponse.ProtoReflect.Descriptor instead.
func (*SearchResourcesResponse) Descriptor() ([]byte, []int) {
	return file_proto_orchestrator_v1_orchestrator_proto_rawDescGZIP(), []int{13}
}

func (x *SearchResourcesResponse) GetResources() []*Resource {
	if x != nil {
		return x.Resources
	}
	return nil
}

func (x *SearchResourcesResponse) GetNextToken() string {
	if x != nil {
		return x.NextToken
	}
	return ""
}

func (x *SearchResourcesResponse) GetTotalCount() int32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type GetResourceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceId    string                 `protobuf:"bytes,1,opt,name=resource_id,json=resourceId,proto3" json:"resource_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetResourceRequest) Reset() {
	*x = GetResourceRequest{}
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetResourceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetResourceRequest) ProtoMessage() {}

func (x *GetResourceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetResourceRequest.ProtoReflect.Descriptor instead.
func (*GetResourceRequest) Descriptor() ([]byte, []int) {
	return file_proto_orchestrator_v1_orchestrator_proto_rawDescGZIP(), []int{14}
}

func (x *GetResourceRequest) GetResourceId() string {
	if x != nil {
		return x.ResourceId
	}
	return ""
}

type GetResourceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Resource      *Resource              `protobuf:"bytes,1,opt,name=resource,proto3" json:"resource,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetResourceResponse) Reset() {
	*x = GetResourceResponse{}
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetResourceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetResourceResponse) ProtoMessage() {}

func (x *GetResourceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetResourceResponse.ProtoReflect.Descriptor instead.
func (*GetResourceResponse) Descriptor() ([]byte, []int) {
	return file_proto_orchestrator_v1_orchestrator_proto_rawDescGZIP(), []int{15}
}

func (x *GetResourceResponse) GetResource() *Resource {
	if x != nil {
		return x.Resource
	}
	return nil
}

// Envoy messages
type PushSnapshotRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Tenant        string                 `protobuf:"bytes,1,opt,name=tenant,proto3" json:"tenant,omitempty"`
	ClustersJson  string                 `protobuf:"bytes,2,opt,name=clusters_json,json=clustersJson,proto3" json:"clusters_json,omitempty"`
	RoutesJson    string                 `protobuf:"bytes,3,opt,name=routes_json,json=routesJson,proto3" json:"routes_json,omitempty"`
	ListenersJson string                 `protobuf:"bytes,4,opt,name=listeners_json,json=listenersJson,proto3" json:"listeners_json,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PushSnapshotRequest) Reset() {
	*x = PushSnapshotRequest{}
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PushSnapshotRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushSnapshotRequest) ProtoMessage() {}

func (x *PushSnapshotRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushSnapshotRequest.ProtoReflect.Descriptor instead.
func (*PushSnapshotRequest) Descriptor() ([]byte, []int) {
	return file_proto_orchestrator_v1_orchestrator_proto_rawDescGZIP(), []int{16}
}

func (x *PushSnapshotRequest) GetTenant() string {
	if x != nil {
		return x.Tenant
	}
	return ""
}

func (x *PushSnapshotRequest) GetClustersJson() string {
	if x != nil {
		return x.ClustersJson
	}
	return ""
}

func (x *PushSnapshotRequest) GetRoutesJson() string {
	if x != nil {
		return x.RoutesJson
	}
	return ""
}

func (x *PushSnapshotRequest) GetListenersJson() string {
	if x != nil {
		return x.ListenersJson
	}
	return ""
}

type PushSnapshotResponse struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Success         bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message         string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	SnapshotVersion string                 `protobuf:"bytes,3,opt,name=snapshot_version,json=snapshotVersion,proto3" json:"snapshot_version,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *PushSnapshotResponse) Reset() {
	*x = PushSnapshotResponse{}
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PushSnapshotResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushSnapshotResponse) ProtoMessage() {}

func (x *PushSnapshotResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushSnapshotResponse.ProtoReflect.Descriptor instead.
func (*PushSnapshotResponse) Descriptor() ([]byte, []int) {
	return file_proto_orchestrator_v1_orchestrator_proto_rawDescGZIP(), []int{17}
}

func (x *PushSnapshotResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *PushSnapshotResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *PushSnapshotResponse) GetSnapshotVersion() string {
	if x != nil {
		return x.SnapshotVersion
	}
	return ""
}

type GetSnapshotRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Tenant        string                 `protobuf:"bytes,1,opt,name=tenant,proto3" json:"tenant,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSnapshotRequest) Reset() {
	*x = GetSnapshotRequest{}
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSnapshotRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSnapshotRequest) ProtoMessage() {}

func (x *GetSnapshotRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSnapshotRequest.ProtoReflect.Descriptor instead.
func (*GetSnapshotRequest) Descriptor() ([]byte, []int) {
	return file_proto_orchestrator_v1_orchestrator_proto_rawDescGZIP(), []int{18}
}

func (x *GetSnapshotRequest) GetTenant() string {
	if x != nil {
		return x.Tenant
	}
	return ""
}

type GetSnapshotResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Tenant        string                 `protobuf:"bytes,1,opt,name=tenant,proto3" json:"tenant,omitempty"`
	ClustersJson  string                 `protobuf:"bytes,2,opt,name=clusters_json,json=clustersJson,proto3" json:"clusters_json,omitempty"`
	RoutesJson    string                 `protobuf:"bytes,3,opt,name=routes_json,json=routesJson,proto3" json:"routes_json,omitempty"`
	ListenersJson string                 `protobuf:"bytes,4,opt,name=listeners_json,json=listenersJson,proto3" json:"listeners_json,omitempty"`
	Version       string                 `protobuf:"bytes,5,opt,name=version,proto3" json:"version,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSnapshotResponse) Reset() {
	*x = GetSnapshotResponse{}
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSnapshotResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSnapshotResponse) ProtoMessage() {}

func (x *GetSnapshotResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSnapshotResponse.ProtoReflect.Descriptor instead.
func (*GetSnapshotResponse) Descriptor() ([]byte, []int) {
	return file_proto_orchestrator_v1_orchestrator_proto_rawDescGZIP(), []int{19}
}

func (x *GetSnapshotResponse) GetTenant() string {
	if x != nil {
		return x.Tenant
	}
	return ""
}

func (x *GetSnapshotResponse) GetClustersJson() string {
	if x != nil {
		return x.ClustersJson
	}
	return ""
}

func (x *GetSnapshotResponse) GetRoutesJson() string {
	if x != nil {
		return x.RoutesJson
	}
	return ""
}

func (x *GetSnapshotResponse) GetListenersJson() string {
	if x != nil {
		return x.ListenersJson
	}
	return ""
}

func (x *GetSnapshotResponse) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *GetSnapshotResponse) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

// DB Admin messages
type GetDBStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDBStatusRequest) Reset() {
	*x = GetDBStatusRequest{}
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDBStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDBStatusRequest) ProtoMessage() {}

func (x *GetDBStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDBStatusRequest.ProtoReflect.Descriptor instead.
func (*GetDBStatusRequest) Descriptor() ([]byte, []int) {
	return file_proto_orchestrator_v1_orchestrator_proto_rawDescGZIP(), []int{20}
}

type GetDBStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Connected     bool                   `protobuf:"varint,1,opt,name=connected,proto3" json:"connected,omitempty"`
	Version       string                 `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	TotalRecords  int64                  `protobuf:"varint,3,opt,name=total_records,json=totalRecords,proto3" json:"total_records,omitempty"`
	Status        string                 `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDBStatusResponse) Reset() {
	*x = GetDBStatusResponse{}
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDBStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDBStatusResponse) ProtoMessage() {}

func (x *GetDBStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDBStatusResponse.ProtoReflect.Descriptor instead.
func (*GetDBStatusResponse) Descriptor() ([]byte, []int) {
	return file_proto_orchestrator_v1_orchestrator_proto_rawDescGZIP(), []int{21}
}

func (x *GetDBStatusResponse) GetConnected() bool {
	if x != nil {
		return x.Connected
	}
	return false
}

func (x *GetDBStatusResponse) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *GetDBStatusResponse) GetTotalRecords() int64 {
	if x != nil {
		return x.TotalRecords
	}
	return 0
}

func (x *GetDBStatusResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type ExecuteQueryRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Query         string                 `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
	Parameters    []string               `protobuf:"bytes,2,rep,name=parameters,proto3" json:"parameters,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExecuteQueryRequest) Reset() {
	*x = ExecuteQueryRequest{}
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExecuteQueryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecuteQueryRequest) ProtoMessage() {}

func (x *ExecuteQueryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecuteQueryRequest.ProtoReflect.Descriptor instead.
func (*ExecuteQueryRequest) Descriptor() ([]byte, []int) {
	return file_proto_orchestrator_v1_orchestrator_proto_rawDescGZIP(), []int{22}
}

func (x *ExecuteQueryRequest) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *ExecuteQueryRequest) GetParameters() []string {
	if x != nil {
		return x.Parameters
	}
	return nil
}

type QueryResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Row           map[string]string      `protobuf:"bytes,1,rep,name=row,proto3" json:"row,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueryResult) Reset() {
	*x = QueryResult{}
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryResult) ProtoMessage() {}

func (x *QueryResult) ProtoReflect() protoreflect.Message {
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryResult.ProtoReflect.Descriptor instead.
func (*QueryResult) Descriptor() ([]byte, []int) {
	return file_proto_orchestrator_v1_orchestrator_proto_rawDescGZIP(), []int{23}
}

func (x *QueryResult) GetRow() map[string]string {
	if x != nil {
		return x.Row
	}
	return nil
}

type ExecuteQueryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Results       []*QueryResult         `protobuf:"bytes,1,rep,name=results,proto3" json:"results,omitempty"`
	RowsAffected  int32                  `protobuf:"varint,2,opt,name=rows_affected,json=rowsAffected,proto3" json:"rows_affected,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExecuteQueryResponse) Reset() {
	*x = ExecuteQueryResponse{}
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExecuteQueryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecuteQueryResponse) ProtoMessage() {}

func (x *ExecuteQueryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecuteQueryResponse.ProtoReflect.Descriptor instead.
func (*ExecuteQueryResponse) Descriptor() ([]byte, []int) {
	return file_proto_orchestrator_v1_orchestrator_proto_rawDescGZIP(), []int{24}
}

func (x *ExecuteQueryResponse) GetResults() []*QueryResult {
	if x != nil {
		return x.Results
	}
	return nil
}

func (x *ExecuteQueryResponse) GetRowsAffected() int32 {
	if x != nil {
		return x.RowsAffected
	}
	return 0
}

// Audit messages
type GetAuditLogsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StartTime     *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime       *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	UserId        string                 `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Action        string                 `protobuf:"bytes,4,opt,name=action,proto3" json:"action,omitempty"`
	Limit         int32                  `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAuditLogsRequest) Reset() {
	*x = GetAuditLogsRequest{}
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAuditLogsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAuditLogsRequest) ProtoMessage() {}

func (x *GetAuditLogsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAuditLogsRequest.ProtoReflect.Descriptor instead.
func (*GetAuditLogsRequest) Descriptor() ([]byte, []int) {
	return file_proto_orchestrator_v1_orchestrator_proto_rawDescGZIP(), []int{25}
}

func (x *GetAuditLogsRequest) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *GetAuditLogsRequest) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *GetAuditLogsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetAuditLogsRequest) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *GetAuditLogsRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type GetAuditLogsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Entries       []*AuditEntry          `protobuf:"bytes,1,rep,name=entries,proto3" json:"entries,omitempty"`
	NextToken     string                 `protobuf:"bytes,2,opt,name=next_token,json=nextToken,proto3" json:"next_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAuditLogsResponse) Reset() {
	*x = GetAuditLogsResponse{}
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAuditLogsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAuditLogsResponse) ProtoMessage() {}

func (x *GetAuditLogsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAuditLogsResponse.ProtoReflect.Descriptor instead.
func (*GetAuditLogsResponse) Descriptor() ([]byte, []int) {
	return file_proto_orchestrator_v1_orchestrator_proto_rawDescGZIP(), []int{26}
}

func (x *GetAuditLogsResponse) GetEntries() []*AuditEntry {
	if x != nil {
		return x.Entries
	}
	return nil
}

func (x *GetAuditLogsResponse) GetNextToken() string {
	if x != nil {
		return x.NextToken
	}
	return ""
}

type VerifyAuditEntryRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	EntryId       string                 `protobuf:"bytes,1,opt,name=entry_id,json=entryId,proto3" json:"entry_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VerifyAuditEntryRequest) Reset() {
	*x = VerifyAuditEntryRequest{}
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifyAuditEntryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyAuditEntryRequest) ProtoMessage() {}

func (x *VerifyAuditEntryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyAuditEntryRequest.ProtoReflect.Descriptor instead.
func (*VerifyAuditEntryRequest) Descriptor() ([]byte, []int) {
	return file_proto_orchestrator_v1_orchestrator_proto_rawDescGZIP(), []int{27}
}

func (x *VerifyAuditEntryRequest) GetEntryId() string {
	if x != nil {
		return x.EntryId
	}
	return ""
}

type VerifyAuditEntryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Verified      bool                   `protobuf:"varint,1,opt,name=verified,proto3" json:"verified,omitempty"`
	Proof         string                 `protobuf:"bytes,2,opt,name=proof,proto3" json:"proof,omitempty"`
	Message       string                 `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VerifyAuditEntryResponse) Reset() {
	*x = VerifyAuditEntryResponse{}
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifyAuditEntryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyAuditEntryResponse) ProtoMessage() {}

func (x *VerifyAuditEntryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyAuditEntryResponse.ProtoReflect.Descriptor instead.
func (*VerifyAuditEntryResponse) Descriptor() ([]byte, []int) {
	return file_proto_orchestrator_v1_orchestrator_proto_rawDescGZIP(), []int{28}
}

func (x *VerifyAuditEntryResponse) GetVerified() bool {
	if x != nil {
		return x.Verified
	}
	return false
}

func (x *VerifyAuditEntryResponse) GetProof() string {
	if x != nil {
		return x.Proof
	}
	return ""
}

func (x *VerifyAuditEntryResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type AuditEntry struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Action        string                 `protobuf:"bytes,3,opt,name=action,proto3" json:"action,omitempty"`
	Resource      string                 `protobuf:"bytes,4,opt,name=resource,proto3" json:"resource,omitempty"`
	Timestamp     *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Metadata      map[string]string      `protobuf:"bytes,6,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	ProofHash     string                 `protobuf:"bytes,7,opt,name=proof_hash,json=proofHash,proto3" json:"proof_hash,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AuditEntry) Reset() {
	*x = AuditEntry{}
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AuditEntry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuditEntry) ProtoMessage() {}

func (x *AuditEntry) ProtoReflect() protoreflect.Message {
	mi := &file_proto_orchestrator_v1_orchestrator_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuditEntry.ProtoReflect.Descriptor instead.
func (*AuditEntry) Descriptor() ([]byte, []int) {
	return file_proto_orchestrator_v1_orchestrator_proto_rawDescGZIP(), []int{29}
}

func (x *AuditEntry) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AuditEntry) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *AuditEntry) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *AuditEntry) GetResource() string {
	if x != nil {
		return x.Resource
	}
	return ""
}

func (x *AuditEntry) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *AuditEntry) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *AuditEntry) GetProofHash() string {
	if x != nil {
		return x.ProofHash
	}
	return ""
}

var File_proto_orchestrator_v1_orchestrator_proto protoreflect.FileDescriptor

const file_proto_orchestrator_v1_orchestrator_proto_rawDesc = "" +
	"\n" +
	"(proto/orchestrator/v1/orchestrator.proto\x12\x0forchestrator.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1cgoogle/protobuf/struct.proto\"\xc6\x02\n" +
	"\bResource\x12\x1a\n" +
	"\bprovider\x18\x01 \x01(\tR\bprovider\x12\x12\n" +
	"\x04type\x18\x02 \x01(\tR\x04type\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x0e\n" +
	"\x02id\x18\x04 \x01(\tR\x02id\x127\n" +
	"\x04tags\x18\x05 \x03(\v2#.orchestrator.v1.Resource.TagsEntryR\x04tags\x129\n" +
	"\n" +
	"created_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\x1a7\n" +
	"\tTagsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\x1c\n" +
	"\x1aGetAutoscalerStatusRequest\"\xa5\x01\n" +
	"\x1bGetAutoscalerStatusResponse\x12\x18\n" +
	"\aenabled\x18\x01 \x01(\bR\aenabled\x12)\n" +
	"\x10current_replicas\x18\x02 \x01(\x05R\x0fcurrentReplicas\x12)\n" +
	"\x10desired_replicas\x18\x03 \x01(\x05R\x0fdesiredReplicas\x12\x16\n" +
	"\x06status\x18\x04 \x01(\tR\x06status\"\xad\x01\n" +
	"\x1dUpdateAutoscalerConfigRequest\x12\x18\n" +
	"\aenabled\x18\x01 \x01(\bR\aenabled\x12!\n" +
	"\fmin_replicas\x18\x02 \x01(\x05R\vminReplicas\x12!\n" +
	"\fmax_replicas\x18\x03 \x01(\x05R\vmaxReplicas\x12,\n" +
	"\x12target_cpu_percent\x18\x04 \x01(\x05R\x10targetCpuPercent\"T\n" +
	"\x1eUpdateAutoscalerConfigResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\xc1\x01\n" +
	"\x16ExecuteWorkflowRequest\x12\x1f\n" +
	"\vworkflow_id\x18\x01 \x01(\tR\n" +
	"workflowId\x12K\n" +
	"\x06inputs\x18\x02 \x03(\v23.orchestrator.v1.ExecuteWorkflowRequest.InputsEntryR\x06inputs\x1a9\n" +
	"\vInputsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"T\n" +
	"\x17ExecuteWorkflowResponse\x12!\n" +
	"\fexecution_id\x18\x01 \x01(\tR\vexecutionId\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\";\n" +
	"\x18GetWorkflowStatusRequest\x12\x1f\n" +
	"\vworkflow_id\x18\x01 \x01(\tR\n" +
	"workflowId\"\xdd\x02\n" +
	"\x19GetWorkflowStatusResponse\x12\x1f\n" +
	"\vworkflow_id\x18\x01 \x01(\tR\n" +
	"workflowId\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\x129\n" +
	"\n" +
	"started_at\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\tstartedAt\x12=\n" +
	"\fcompleted_at\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\vcompletedAt\x12Q\n" +
	"\aoutputs\x18\x05 \x03(\v27.orchestrator.v1.GetWorkflowStatusResponse.OutputsEntryR\aoutputs\x1a:\n" +
	"\fOutputsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"R\n" +
	"\x14ListWorkflowsRequest\x12\x1b\n" +
	"\tpage_size\x18\x01 \x01(\x05R\bpageSize\x12\x1d\n" +
	"\n" +
	"page_token\x18\x02 \x01(\tR\tpageToken\"|\n" +
	"\x15ListWorkflowsResponse\x12;\n" +
	"\tworkflows\x18\x01 \x03(\v2\x1d.orchestrator.v1.WorkflowInfoR\tworkflows\x12&\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tR\rnextPageToken\"\x8f\x01\n" +
	"\fWorkflowInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x129\n" +
	"\n" +
	"created_at\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\"\xec\x01\n" +
	"\x16SearchResourcesRequest\x12\x1a\n" +
	"\bprovider\x18\x01 \x01(\tR\bprovider\x12\x14\n" +
	"\x05query\x18\x02 \x01(\tR\x05query\x12N\n" +
	"\afilters\x18\x03 \x03(\v24.orchestrator.v1.SearchResourcesRequest.FiltersEntryR\afilters\x12\x14\n" +
	"\x05limit\x18\x04 \x01(\x05R\x05limit\x1a:\n" +
	"\fFiltersEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\x92\x01\n" +
	"\x17SearchResourcesResponse\x127\n" +
	"\tresources\x18\x01 \x03(\v2\x19.orchestrator.v1.ResourceR\tresources\x12\x1d\n" +
	"\n" +
	"next_token\x18\x02 \x01(\tR\tnextToken\x12\x1f\n" +
	"\vtotal_count\x18\x03 \x01(\x05R\n" +
	"totalCount\"5\n" +
	"\x12GetResourceRequest\x12\x1f\n" +
	"\vresource_id\x18\x01 \x01(\tR\n" +
	"resourceId\"L\n" +
	"\x13GetResourceResponse\x125\n" +
	"\bresource\x18\x01 \x01(\v2\x19.orchestrator.v1.ResourceR\bresource\"\x9a\x01\n" +
	"\x13PushSnapshotRequest\x12\x16\n" +
	"\x06tenant\x18\x01 \x01(\tR\x06tenant\x12#\n" +
	"\rclusters_json\x18\x02 \x01(\tR\fclustersJson\x12\x1f\n" +
	"\vroutes_json\x18\x03 \x01(\tR\n" +
	"routesJson\x12%\n" +
	"\x0elisteners_json\x18\x04 \x01(\tR\rlistenersJson\"u\n" +
	"\x14PushSnapshotResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12)\n" +
	"\x10snapshot_version\x18\x03 \x01(\tR\x0fsnapshotVersion\",\n" +
	"\x12GetSnapshotRequest\x12\x16\n" +
	"\x06tenant\x18\x01 \x01(\tR\x06tenant\"\xef\x01\n" +
	"\x13GetSnapshotResponse\x12\x16\n" +
	"\x06tenant\x18\x01 \x01(\tR\x06tenant\x12#\n" +
	"\rclusters_json\x18\x02 \x01(\tR\fclustersJson\x12\x1f\n" +
	"\vroutes_json\x18\x03 \x01(\tR\n" +
	"routesJson\x12%\n" +
	"\x0elisteners_json\x18\x04 \x01(\tR\rlistenersJson\x12\x18\n" +
	"\aversion\x18\x05 \x01(\tR\aversion\x129\n" +
	"\n" +
	"created_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\"\x14\n" +
	"\x12GetDBStatusRequest\"\x8a\x01\n" +
	"\x13GetDBStatusResponse\x12\x1c\n" +
	"\tconnected\x18\x01 \x01(\bR\tconnected\x12\x18\n" +
	"\aversion\x18\x02 \x01(\tR\aversion\x12#\n" +
	"\rtotal_records\x18\x03 \x01(\x03R\ftotalRecords\x12\x16\n" +
	"\x06status\x18\x04 \x01(\tR\x06status\"K\n" +
	"\x13ExecuteQueryRequest\x12\x14\n" +
	"\x05query\x18\x01 \x01(\tR\x05query\x12\x1e\n" +
	"\n" +
	"parameters\x18\x02 \x03(\tR\n" +
	"parameters\"~\n" +
	"\vQueryResult\x127\n" +
	"\x03row\x18\x01 \x03(\v2%.orchestrator.v1.QueryResult.RowEntryR\x03row\x1a6\n" +
	"\bRowEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"s\n" +
	"\x14ExecuteQueryResponse\x126\n" +
	"\aresults\x18\x01 \x03(\v2\x1c.orchestrator.v1.QueryResultR\aresults\x12#\n" +
	"\rrows_affected\x18\x02 \x01(\x05R\frowsAffected\"\xce\x01\n" +
	"\x13GetAuditLogsRequest\x129\n" +
	"\n" +
	"start_time\x18\x01 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x125\n" +
	"\bend_time\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\aendTime\x12\x17\n" +
	"\auser_id\x18\x03 \x01(\tR\x06userId\x12\x16\n" +
	"\x06action\x18\x04 \x01(\tR\x06action\x12\x14\n" +
	"\x05limit\x18\x05 \x01(\x05R\x05limit\"l\n" +
	"\x14GetAuditLogsResponse\x125\n" +
	"\aentries\x18\x01 \x03(\v2\x1b.orchestrator.v1.AuditEntryR\aentries\x12\x1d\n" +
	"\n" +
	"next_token\x18\x02 \x01(\tR\tnextToken\"4\n" +
	"\x17VerifyAuditEntryRequest\x12\x19\n" +
	"\bentry_id\x18\x01 \x01(\tR\aentryId\"f\n" +
	"\x18VerifyAuditEntryResponse\x12\x1a\n" +
	"\bverified\x18\x01 \x01(\bR\bverified\x12\x14\n" +
	"\x05proof\x18\x02 \x01(\tR\x05proof\x12\x18\n" +
	"\amessage\x18\x03 \x01(\tR\amessage\"\xc6\x02\n" +
	"\n" +
	"AuditEntry\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x12\x16\n" +
	"\x06action\x18\x03 \x01(\tR\x06action\x12\x1a\n" +
	"\bresource\x18\x04 \x01(\tR\bresource\x128\n" +
	"\ttimestamp\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\ttimestamp\x12E\n" +
	"\bmetadata\x18\x06 \x03(\v2).orchestrator.v1.AuditEntry.MetadataEntryR\bmetadata\x12\x1d\n" +
	"\n" +
	"proof_hash\x18\a \x01(\tR\tproofHash\x1a;\n" +
	"\rMetadataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x012\x80\x02\n" +
	"\x11AutoScalerService\x12p\n" +
	"\x13GetAutoscalerStatus\x12+.orchestrator.v1.GetAutoscalerStatusRequest\x1a,.orchestrator.v1.GetAutoscalerStatusResponse\x12y\n" +
	"\x16UpdateAutoscalerConfig\x12..orchestrator.v1.UpdateAutoscalerConfigRequest\x1a/.orchestrator.v1.UpdateAutoscalerConfigResponse2\xc3\x02\n" +
	"\x0fWorkflowService\x12d\n" +
	"\x0fExecuteWorkflow\x12'.orchestrator.v1.ExecuteWorkflowRequest\x1a(.orchestrator.v1.ExecuteWorkflowResponse\x12j\n" +
	"\x11GetWorkflowStatus\x12).orchestrator.v1.GetWorkflowStatusRequest\x1a*.orchestrator.v1.GetWorkflowStatusResponse\x12^\n" +
	"\rListWorkflows\x12%.orchestrator.v1.ListWorkflowsRequest\x1a&.orchestrator.v1.ListWorkflowsResponse2\xd2\x01\n" +
	"\x10DiscoveryService\x12d\n" +
	"\x0fSearchResources\x12'.orchestrator.v1.SearchResourcesRequest\x1a(.orchestrator.v1.SearchResourcesResponse\x12X\n" +
	"\vGetResource\x12#.orchestrator.v1.GetResourceRequest\x1a$.orchestrator.v1.GetResourceResponse2\xd1\x01\n" +
	"\x18EnvoyControlPlaneService\x12[\n" +
	"\fPushSnapshot\x12$.orchestrator.v1.PushSnapshotRequest\x1a%.orchestrator.v1.PushSnapshotResponse\x12X\n" +
	"\vGetSnapshot\x12#.orchestrator.v1.GetSnapshotRequest\x1a$.orchestrator.v1.GetSnapshotResponse2\xc7\x01\n" +
	"\x0eDBAdminService\x12X\n" +
	"\vGetDBStatus\x12#.orchestrator.v1.GetDBStatusRequest\x1a$.orchestrator.v1.GetDBStatusResponse\x12[\n" +
	"\fExecuteQuery\x12$.orchestrator.v1.ExecuteQueryRequest\x1a%.orchestrator.v1.ExecuteQueryResponse2\xd4\x01\n" +
	"\fAuditService\x12[\n" +
	"\fGetAuditLogs\x12$.orchestrator.v1.GetAuditLogsRequest\x1a%.orchestrator.v1.GetAuditLogsResponse\x12g\n" +
	"\x10VerifyAuditEntry\x12(.orchestrator.v1.VerifyAuditEntryRequest\x1a).orchestrator.v1.VerifyAuditEntryResponseBFZDgithub.com/cainuro/orchestrator/proto/orchestrator/v1;orchestratorv1b\x06proto3"

var (
	file_proto_orchestrator_v1_orchestrator_proto_rawDescOnce sync.Once
	file_proto_orchestrator_v1_orchestrator_proto_rawDescData []byte
)

func file_proto_orchestrator_v1_orchestrator_proto_rawDescGZIP() []byte {
	file_proto_orchestrator_v1_orchestrator_proto_rawDescOnce.Do(func() {
		file_proto_orchestrator_v1_orchestrator_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_orchestrator_v1_orchestrator_proto_rawDesc), len(file_proto_orchestrator_v1_orchestrator_proto_rawDesc)))
	})
	return file_proto_orchestrator_v1_orchestrator_proto_rawDescData
}

var file_proto_orchestrator_v1_orchestrator_proto_msgTypes = make([]protoimpl.MessageInfo, 36)
var file_proto_orchestrator_v1_orchestrator_proto_goTypes = []any{
	(*Resource)(nil),                       // 0: orchestrator.v1.Resource
	(*GetAutoscalerStatusRequest)(nil),     // 1: orchestrator.v1.GetAutoscalerStatusRequest
	(*GetAutoscalerStatusResponse)(nil),    // 2: orchestrator.v1.GetAutoscalerStatusResponse
	(*UpdateAutoscalerConfigRequest)(nil),  // 3: orchestrator.v1.UpdateAutoscalerConfigRequest
	(*UpdateAutoscalerConfigResponse)(nil), // 4: orchestrator.v1.UpdateAutoscalerConfigResponse
	(*ExecuteWorkflowRequest)(nil),         // 5: orchestrator.v1.ExecuteWorkflowRequest
	(*ExecuteWorkflowResponse)(nil),        // 6: orchestrator.v1.ExecuteWorkflowResponse
	(*GetWorkflowStatusRequest)(nil),       // 7: orchestrator.v1.GetWorkflowStatusRequest
	(*GetWorkflowStatusResponse)(nil),      // 8: orchestrator.v1.GetWorkflowStatusResponse
	(*ListWorkflowsRequest)(nil),           // 9: orchestrator.v1.ListWorkflowsRequest
	(*ListWorkflowsResponse)(nil),          // 10: orchestrator.v1.ListWorkflowsResponse
	(*WorkflowInfo)(nil),                   // 11: orchestrator.v1.WorkflowInfo
	(*SearchResourcesRequest)(nil),         // 12: orchestrator.v1.SearchResourcesRequest
	(*SearchResourcesResponse)(nil),        // 13: orchestrator.v1.SearchResourcesResponse
	(*GetResourceRequest)(nil),             // 14: orchestrator.v1.GetResourceRequest
	(*GetResourceResponse)(nil),            // 15: orchestrator.v1.GetResourceResponse
	(*PushSnapshotRequest)(nil),            // 16: orchestrator.v1.PushSnapshotRequest
	(*PushSnapshotResponse)(nil),           // 17: orchestrator.v1.PushSnapshotResponse
	(*GetSnapshotRequest)(nil),             // 18: orchestrator.v1.GetSnapshotRequest
	(*GetSnapshotResponse)(nil),            // 19: orchestrator.v1.GetSnapshotResponse
	(*GetDBStatusRequest)(nil),             // 20: orchestrator.v1.GetDBStatusRequest
	(*GetDBStatusResponse)(nil),            // 21: orchestrator.v1.GetDBStatusResponse
	(*ExecuteQueryRequest)(nil),            // 22: orchestrator.v1.ExecuteQueryRequest
	(*QueryResult)(nil),                    // 23: orchestrator.v1.QueryResult
	(*ExecuteQueryResponse)(nil),           // 24: orchestrator.v1.ExecuteQueryResponse
	(*GetAuditLogsRequest)(nil),            // 25: orchestrator.v1.GetAuditLogsRequest
	(*GetAuditLogsResponse)(nil),           // 26: orchestrator.v1.GetAuditLogsResponse
	(*VerifyAuditEntryRequest)(nil),        // 27: orchestrator.v1.VerifyAuditEntryRequest
	(*VerifyAuditEntryResponse)(nil),       // 28: orchestrator.v1.VerifyAuditEntryResponse
	(*AuditEntry)(nil),                     // 29: orchestrator.v1.AuditEntry
	nil,                                    // 30: orchestrator.v1.Resource.TagsEntry
	nil,                                    // 31: orchestrator.v1.ExecuteWorkflowRequest.InputsEntry
	nil,                                    // 32: orchestrator.v1.GetWorkflowStatusResponse.OutputsEntry
	nil,                                    // 33: orchestrator.v1.SearchResourcesRequest.FiltersEntry
	nil,                                    // 34: orchestrator.v1.QueryResult.RowEntry
	nil,                                    // 35: orchestrator.v1.AuditEntry.MetadataEntry
	(*timestamppb.Timestamp)(nil),          // 36: google.protobuf.Timestamp
}
var file_proto_orchestrator_v1_orchestrator_proto_depIdxs = []int32{
	30, // 0: orchestrator.v1.Resource.tags:type_name -> orchestrator.v1.Resource.TagsEntry
	36, // 1: orchestrator.v1.Resource.created_at:type_name -> google.protobuf.Timestamp
	36, // 2: orchestrator.v1.Resource.updated_at:type_name -> google.protobuf.Timestamp
	31, // 3: orchestrator.v1.ExecuteWorkflowRequest.inputs:type_name -> orchestrator.v1.ExecuteWorkflowRequest.InputsEntry
	36, // 4: orchestrator.v1.GetWorkflowStatusResponse.started_at:type_name -> google.protobuf.Timestamp
	36, // 5: orchestrator.v1.GetWorkflowStatusResponse.completed_at:type_name -> google.protobuf.Timestamp
	32, // 6: orchestrator.v1.GetWorkflowStatusResponse.outputs:type_name -> orchestrator.v1.GetWorkflowStatusResponse.OutputsEntry
	11, // 7: orchestrator.v1.ListWorkflowsResponse.workflows:type_name -> orchestrator.v1.WorkflowInfo
	36, // 8: orchestrator.v1.WorkflowInfo.created_at:type_name -> google.protobuf.Timestamp
	33, // 9: orchestrator.v1.SearchResourcesRequest.filters:type_name -> orchestrator.v1.SearchResourcesRequest.FiltersEntry
	0,  // 10: orchestrator.v1.SearchResourcesResponse.resources:type_name -> orchestrator.v1.Resource
	0,  // 11: orchestrator.v1.GetResourceResponse.resource:type_name -> orchestrator.v1.Resource
	36, // 12: orchestrator.v1.GetSnapshotResponse.created_at:type_name -> google.protobuf.Timestamp
	34, // 13: orchestrator.v1.QueryResult.row:type_name -> orchestrator.v1.QueryResult.RowEntry
	23, // 14: orchestrator.v1.ExecuteQueryResponse.results:type_name -> orchestrator.v1.QueryResult
	36, // 15: orchestrator.v1.GetAuditLogsRequest.start_time:type_name -> google.protobuf.Timestamp
	36, // 16: orchestrator.v1.GetAuditLogsRequest.end_time:type_name -> google.protobuf.Timestamp
	29, // 17: orchestrator.v1.GetAuditLogsResponse.entries:type_name -> orchestrator.v1.AuditEntry
	36, // 18: orchestrator.v1.AuditEntry.timestamp:type_name -> google.protobuf.Timestamp
	35, // 19: orchestrator.v1.AuditEntry.metadata:type_name -> orchestrator.v1.AuditEntry.MetadataEntry
	1,  // 20: orchestrator.v1.AutoScalerService.GetAutoscalerStatus:input_type -> orchestrator.v1.GetAutoscalerStatusRequest
	3,  // 21: orchestrator.v1.AutoScalerService.UpdateAutoscalerConfig:input_type -> orchestrator.v1.UpdateAutoscalerConfigRequest
	5,  // 22: orchestrator.v1.WorkflowService.ExecuteWorkflow:input_type -> orchestrator.v1.ExecuteWorkflowRequest
	7,  // 23: orchestrator.v1.WorkflowService.GetWorkflowStatus:input_type -> orchestrator.v1.GetWorkflowStatusRequest
	9,  // 24: orchestrator.v1.WorkflowService.ListWorkflows:input_type -> orchestrator.v1.ListWorkflowsRequest
	12, // 25: orchestrator.v1.DiscoveryService.SearchResources:input_type -> orchestrator.v1.SearchResourcesRequest
	14, // 26: orchestrator.v1.DiscoveryService.GetResource:input_type -> orchestrator.v1.GetResourceRequest
	16, // 27: orchestrator.v1.EnvoyControlPlaneService.PushSnapshot:input_type -> orchestrator.v1.PushSnapshotRequest
	18, // 28: orchestrator.v1.EnvoyControlPlaneService.GetSnapshot:input_type -> orchestrator.v1.GetSnapshotRequest
	20, // 29: orchestrator.v1.DBAdminService.GetDBStatus:input_type -> orchestrator.v1.GetDBStatusRequest
	22, // 30: orchestrator.v1.DBAdminService.ExecuteQuery:input_type -> orchestrator.v1.ExecuteQueryRequest
	25, // 31: orchestrator.v1.AuditService.GetAuditLogs:input_type -> orchestrator.v1.GetAuditLogsRequest
	27, // 32: orchestrator.v1.AuditService.VerifyAuditEntry:input_type -> orchestrator.v1.VerifyAuditEntryRequest
	2,  // 33: orchestrator.v1.AutoScalerService.GetAutoscalerStatus:output_type -> orchestrator.v1.GetAutoscalerStatusResponse
	4,  // 34: orchestrator.v1.AutoScalerService.UpdateAutoscalerConfig:output_type -> orchestrator.v1.UpdateAutoscalerConfigResponse
	6,  // 35: orchestrator.v1.WorkflowService.ExecuteWorkflow:output_type -> orchestrator.v1.ExecuteWorkflowResponse
	8,  // 36: orchestrator.v1.WorkflowService.GetWorkflowStatus:output_type -> orchestrator.v1.GetWorkflowStatusResponse
	10, // 37: orchestrator.v1.WorkflowService.ListWorkflows:output_type -> orchestrator.v1.ListWorkflowsResponse
	13, // 38: orchestrator.v1.DiscoveryService.SearchResources:output_type -> orchestrator.v1.SearchResourcesResponse
	15, // 39: orchestrator.v1.DiscoveryService.GetResource:output_type -> orchestrator.v1.GetResourceResponse
	17, // 40: orchestrator.v1.EnvoyControlPlaneService.PushSnapshot:output_type -> orchestrator.v1.PushSnapshotResponse
	19, // 41: orchestrator.v1.EnvoyControlPlaneService.GetSnapshot:output_type -> orchestrator.v1.GetSnapshotResponse
	21, // 42: orchestrator.v1.DBAdminService.GetDBStatus:output_type -> orchestrator.v1.GetDBStatusResponse
	24, // 43: orchestrator.v1.DBAdminService.ExecuteQuery:output_type -> orchestrator.v1.ExecuteQueryResponse
	26, // 44: orchestrator.v1.AuditService.GetAuditLogs:output_type -> orchestrator.v1.GetAuditLogsResponse
	28, // 45: orchestrator.v1.AuditService.VerifyAuditEntry:output_type -> orchestrator.v1.VerifyAuditEntryResponse
	33, // [33:46] is the sub-list for method output_type
	20, // [20:33] is the sub-list for method input_type
	20, // [20:20] is the sub-list for extension type_name
	20, // [20:20] is the sub-list for extension extendee
	0,  // [0:20] is the sub-list for field type_name
}

func init() { file_proto_orchestrator_v1_orchestrator_proto_init() }
func file_proto_orchestrator_v1_orchestrator_proto_init() {
	if File_proto_orchestrator_v1_orchestrator_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_orchestrator_v1_orchestrator_proto_rawDesc), len(file_proto_orchestrator_v1_orchestrator_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   36,
			NumExtensions: 0,
			NumServices:   6,
		},
		GoTypes:           file_proto_orchestrator_v1_orchestrator_proto_goTypes,
		DependencyIndexes: file_proto_orchestrator_v1_orchestrator_proto_depIdxs,
		MessageInfos:      file_proto_orchestrator_v1_orchestrator_proto_msgTypes,
	}.Build()
	File_proto_orchestrator_v1_orchestrator_proto = out.File
	file_proto_orchestrator_v1_orchestrator_proto_goTypes = nil
	file_proto_orchestrator_v1_orchestrator_proto_depIdxs = nil
}
