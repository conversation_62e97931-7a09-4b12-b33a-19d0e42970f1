import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import axios from 'axios';

export interface DatabaseStats {
  total_resources: number;
  total_workflows: number;
  total_envoy_configs: number;
  database_size: string;
  cache_hit_rate: number;
  cache_size: string;
}

export interface DatabaseState {
  stats: DatabaseStats | null;
  loading: boolean;
  error: string | null;
  queryResult: any;
  queryLoading: boolean;
}

const initialState: DatabaseState = {
  stats: null,
  loading: false,
  error: null,
  queryResult: null,
  queryLoading: false,
};

export const fetchDatabaseStats = createAsyncThunk(
  'database/fetchStats',
  async () => {
    const response = await axios.get('/v1/database/stats');
    return response.data;
  }
);

export const executeQuery = createAsyncThunk(
  'database/executeQuery',
  async (query: string) => {
    const response = await axios.post('/v1/database/query', { query });
    return response.data;
  }
);

const databaseSlice = createSlice({
  name: 'database',
  initialState,
  reducers: {
    clearQueryResult: (state) => {
      state.queryResult = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchDatabaseStats.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchDatabaseStats.fulfilled, (state, action) => {
        state.loading = false;
        state.stats = action.payload;
      })
      .addCase(fetchDatabaseStats.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch database stats';
      })
      .addCase(executeQuery.pending, (state) => {
        state.queryLoading = true;
        state.error = null;
      })
      .addCase(executeQuery.fulfilled, (state, action) => {
        state.queryLoading = false;
        state.queryResult = action.payload;
      })
      .addCase(executeQuery.rejected, (state, action) => {
        state.queryLoading = false;
        state.error = action.error.message || 'Query execution failed';
      });
  },
});

export const { clearQueryResult } = databaseSlice.actions;
export default databaseSlice.reducer;
