package main

import (
	"fmt"
	"strings"

	"github.com/gofiber/fiber/v2"
)

// Authentication middleware based on Clutch implementation
func authMiddleware() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// Skip authentication for certain endpoints
		path := c.Path()

		// Always allowed endpoints (similar to Clutch's AlwaysAllowedMethods)
		alwaysAllowed := []string{
			"/health",
			"/v1/health",
			"/v1/auth/login",
			"/v1/auth/callback",
			"/v1/auth/config",
			"/static/",
			"/favicon.ico",
			"/manifest.json",
		}

		// Check if the path is in the always allowed list
		for _, allowedPath := range alwaysAllowed {
			if strings.HasPrefix(path, allowedPath) {
				return c.Next()
			}
		}

		// For the root path, allow it (serves React app)
		if path == "/" {
			return c.Next()
		}

		// Extract token from Authorization header or cookies
		token := extractToken(c)
		if token == "" {
			return c.Status(401).JSON(fiber.Map{
				"error": "Authentication required",
				"code":  "UNAUTHENTICATED",
			})
		}

		// Verify the token
		claims, err := authService.verifyToken(token)
		if err != nil {
			return c.Status(401).JSON(fiber.Map{
				"error": "Invalid or expired token",
				"code":  "UNAUTHENTICATED",
			})
		}

		// Add user information to context
		c.Locals("user", fiber.Map{
			"email":  claims.Email,
			"name":   claims.Name,
			"groups": claims.Groups,
			"sub":    claims.Subject,
		})

		return c.Next()
	}
}

// Extract token from Authorization header or cookies
func extractToken(c *fiber.Ctx) string {
	// First, try Authorization header
	authHeader := c.Get("Authorization")
	if authHeader != "" {
		// Support both "Bearer <token>" and "Token <token>" formats
		if strings.HasPrefix(authHeader, "Bearer ") {
			return strings.TrimPrefix(authHeader, "Bearer ")
		}
		if strings.HasPrefix(authHeader, "Token ") {
			return strings.TrimPrefix(authHeader, "Token ")
		}
	}

	// Fallback to cookie
	return c.Cookies("token")
}

// Enhanced user info handler
func getAuthUserEnhanced(c *fiber.Ctx) error {
	user := c.Locals("user")
	if user == nil {
		return c.Status(401).JSON(fiber.Map{"error": "Not authenticated"})
	}

	userMap := user.(fiber.Map)

	// Add additional user information
	response := fiber.Map{
		"sub":    userMap["sub"],
		"email":  userMap["email"],
		"name":   userMap["name"],
		"groups": userMap["groups"],
		"roles":  []string{"viewer", "discovery_user", "workflow_user"},
		"permissions": []string{
			"discovery:read",
			"workflows:read",
			"workflows:execute",
			"metrics:read",
			"k8s:read",
			"chaos:read",
			"feedback:read",
			"projects:read",
		},
		"preferences": fiber.Map{
			"theme":    "light",
			"timezone": "UTC",
			"language": "en",
		},
	}

	return c.JSON(response)
}

// Enhanced authorization check handler
func postAuthCheckEnhanced(c *fiber.Ctx) error {
	var req struct {
		Action   string                 `json:"action"`
		Resource string                 `json:"resource"`
		Context  map[string]interface{} `json:"context,omitempty"`
	}

	if err := c.BodyParser(&req); err != nil {
		return c.Status(400).JSON(fiber.Map{"error": "Invalid request body"})
	}

	user := c.Locals("user")
	if user == nil {
		return c.JSON(fiber.Map{
			"decision": fiber.Map{
				"allowed": false,
				"reason":  "Not authenticated",
			},
			"action":   req.Action,
			"resource": req.Resource,
		})
	}

	userMap := user.(fiber.Map)
	groups := userMap["groups"].([]string)

	// Enhanced authorization logic
	allowed := checkPermission(req.Action, req.Resource, groups)
	reason := "Allowed by policy"
	if !allowed {
		reason = "Access denied by policy"
	}

	return c.JSON(fiber.Map{
		"decision": fiber.Map{
			"allowed": allowed,
			"reason":  reason,
		},
		"action":   req.Action,
		"resource": req.Resource,
		"subject": fiber.Map{
			"user":   userMap["email"],
			"groups": groups,
		},
	})
}

// Permission checking logic
func checkPermission(action, resource string, groups []string) bool {
	// Admin group has full access
	for _, group := range groups {
		if group == "cainuro-admins" {
			return true
		}
	}

	// Operators can read and execute
	for _, group := range groups {
		if group == "cainuro-operators" {
			if action == "READ" || action == "EXECUTE" {
				return true
			}
		}
	}

	// Users can read most resources
	for _, group := range groups {
		if group == "cainuro-users" {
			if action == "READ" {
				return true
			}
		}
	}

	// Deny DELETE operations for non-admins
	if action == "DELETE" {
		return false
	}

	// Default allow for basic operations
	if action == "READ" {
		return true
	}

	return false
}

// Enhanced logout handler
func postAuthLogoutEnhanced(c *fiber.Ctx) error {
	// Clear cookies
	c.Cookie(&fiber.Cookie{
		Name:     "token",
		Value:    "",
		HTTPOnly: true,
		Secure:   false,
		SameSite: "Lax",
		MaxAge:   -1, // Expire immediately
	})

	c.Cookie(&fiber.Cookie{
		Name:     "refreshToken",
		Value:    "",
		HTTPOnly: true,
		Secure:   false,
		SameSite: "Lax",
		MaxAge:   -1, // Expire immediately
	})

	return c.JSON(fiber.Map{
		"message":    "Logged out successfully",
		"logout_url": authService.config.Issuer + "/logout",
	})
}

// Enhanced auth config handler
func getAuthConfigEnhanced(c *fiber.Ctx) error {
	config := fiber.Map{
		"provider": "oidc",
		"issuer":   authService.config.Issuer,
		"scopes":   authService.config.Scopes,
		"features": fiber.Map{
			"sso_enabled":     true,
			"mfa_enabled":     true,
			"rbac_enabled":    true,
			"audit_enabled":   true,
			"session_timeout": 3600,
			"refresh_enabled": true,
		},
		"roles": []fiber.Map{
			{
				"name":        "admin",
				"description": "Full system access",
				"permissions": []string{"*"},
			},
			{
				"name":        "operator",
				"description": "Read and execute operations",
				"permissions": []string{"*:read", "*:execute"},
			},
			{
				"name":        "viewer",
				"description": "Read-only access",
				"permissions": []string{"*:read"},
			},
		},
		"groups": []fiber.Map{
			{
				"name":        "cainuro-admins",
				"description": "System administrators",
				"roles":       []string{"admin"},
			},
			{
				"name":        "cainuro-operators",
				"description": "System operators",
				"roles":       []string{"operator"},
			},
			{
				"name":        "cainuro-users",
				"description": "Regular users",
				"roles":       []string{"viewer"},
			},
		},
		"session": fiber.Map{
			"cookie_name":     "token",
			"cookie_secure":   false, // Set to true in production
			"cookie_httponly": true,
			"cookie_samesite": "Lax",
			"max_age":         3600,
		},
	}

	return c.JSON(config)
}

// Token refresh endpoint
func postAuthRefresh(c *fiber.Ctx) error {
	refreshToken := c.Cookies("refreshToken")
	if refreshToken == "" {
		return c.Status(401).JSON(fiber.Map{"error": "No refresh token provided"})
	}

	newTokens, err := refreshTokens(refreshToken)
	if err != nil {
		return c.Status(401).JSON(fiber.Map{"error": "Invalid refresh token"})
	}

	// Set new cookies
	c.Cookie(&fiber.Cookie{
		Name:     "token",
		Value:    newTokens.AccessToken,
		HTTPOnly: true,
		Secure:   false,
		SameSite: "Lax",
		MaxAge:   3600,
	})

	if newTokens.RefreshToken != "" {
		c.Cookie(&fiber.Cookie{
			Name:     "refreshToken",
			Value:    newTokens.RefreshToken,
			HTTPOnly: true,
			Secure:   false,
			SameSite: "Lax",
			MaxAge:   86400,
		})
	}

	return c.JSON(fiber.Map{
		"access_token":  newTokens.AccessToken,
		"refresh_token": newTokens.RefreshToken,
		"token_type":    "Bearer",
		"expires_in":    3600,
	})
}

// Create service token endpoint (for programmatic access)
func postAuthCreateToken(c *fiber.Ctx) error {
	var req struct {
		Subject     string `json:"subject"`
		TokenType   string `json:"token_type"`
		ExpiryHours int    `json:"expiry_hours,omitempty"`
	}

	if err := c.BodyParser(&req); err != nil {
		return c.Status(400).JSON(fiber.Map{"error": "Invalid request body"})
	}

	// Check if user has permission to create service tokens
	user := c.Locals("user")
	if user == nil {
		return c.Status(401).JSON(fiber.Map{"error": "Not authenticated"})
	}

	userMap := user.(fiber.Map)
	groups := userMap["groups"].([]string)

	// Only admins can create service tokens
	hasPermission := false
	for _, group := range groups {
		if group == "cainuro-admins" {
			hasPermission = true
			break
		}
	}

	if !hasPermission {
		return c.Status(403).JSON(fiber.Map{"error": "Insufficient permissions to create service tokens"})
	}

	// Create service token
	subject := fmt.Sprintf("service:%s", req.Subject)
	expiryHours := req.ExpiryHours
	if expiryHours == 0 {
		expiryHours = 24 * 30 // Default 30 days
	}

	serviceToken, err := authService.createAccessToken(subject, req.Subject, []string{"service"})
	if err != nil {
		return c.Status(500).JSON(fiber.Map{"error": "Failed to create service token"})
	}

	return c.JSON(fiber.Map{
		"access_token": serviceToken,
		"token_type":   "Bearer",
		"subject":      subject,
		"expires_in":   expiryHours * 3600,
	})
}
