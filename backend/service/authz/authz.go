package authz

import (
	"context"
	"fmt"
	"strings"

	"go.uber.org/zap"
)

// Service represents the authorization service
type Service struct {
	logger           *zap.Logger
	roleToPolicy     RoleToPolicyMap
	principalToRole  PrincipalToRoleMap
}

// Config represents the authorization configuration
type Config struct {
	Roles        []Role        `json:"roles"`
	RoleBindings []RoleBinding `json:"role_bindings"`
}

// Role represents a role with policies
type Role struct {
	RoleName string   `json:"role_name"`
	Policies []Policy `json:"policies"`
}

// Policy represents an authorization policy
type Policy struct {
	ActionTypes []string `json:"action_types,omitempty"`
	Method      string   `json:"method,omitempty"`
	Resources   []string `json:"resources,omitempty"`
}

// RoleBinding binds principals to roles
type RoleBinding struct {
	Principals []Principal `json:"principals"`
	To         []string    `json:"to"`
}

// Principal represents a user or group
type Principal struct {
	Type  string `json:"type"`
	Value string `json:"value"`
}

// CheckRequest represents an authorization check request
type CheckRequest struct {
	Subject    Subject `json:"subject"`
	ActionType string  `json:"action_type"`
	Method     string  `json:"method"`
	Resource   string  `json:"resource"`
}

// Subject represents the subject of an authorization request
type Subject struct {
	User   string   `json:"user"`
	Groups []string `json:"groups"`
}

// CheckResponse represents an authorization check response
type CheckResponse struct {
	Decision Decision `json:"decision"`
	Reason   string   `json:"reason,omitempty"`
}

// Decision represents an authorization decision
type Decision string

const (
	DecisionAllow Decision = "ALLOW"
	DecisionDeny  Decision = "DENY"
)

// PrincipalKey is used for indexing principals
type PrincipalKey struct {
	Name          string
	PrincipalType PrincipalKeyType
}

// PrincipalKeyType represents the type of principal
type PrincipalKeyType int

const (
	UserPrincipal PrincipalKeyType = iota
	GroupPrincipal
)

// Type aliases for maps
type PrincipalToRoleMap map[PrincipalKey][]string
type RoleToPolicyMap map[string]*Role

// Client interface defines authorization methods
type Client interface {
	Check(ctx context.Context, request *CheckRequest) (*CheckResponse, error)
}

// New creates a new authorization service
func New(config *Config, logger *zap.Logger) (*Service, error) {
	// Build role to policy map
	roleToPolicy, err := configToRolePolicyMap(config)
	if err != nil {
		return nil, fmt.Errorf("failed to build role policy map: %w", err)
	}

	// Build principal to role map
	principalToRole := configToPrincipalRoleMap(config)

	service := &Service{
		logger:          logger,
		roleToPolicy:    roleToPolicy,
		principalToRole: principalToRole,
	}

	logger.Info("Authorization service initialized",
		zap.Int("roles", len(roleToPolicy)),
		zap.Int("principals", len(principalToRole)))

	return service, nil
}

// Check performs an authorization check
func (s *Service) Check(ctx context.Context, req *CheckRequest) (*CheckResponse, error) {
	// Gather all roles for the user and groups
	var roles []string
	
	// Add user roles
	if req.Subject.User != "" {
		userKey := PrincipalKey{
			Name:          req.Subject.User,
			PrincipalType: UserPrincipal,
		}
		if userRoles, exists := s.principalToRole[userKey]; exists {
			roles = append(roles, userRoles...)
		}
	}

	// Add group roles
	for _, group := range req.Subject.Groups {
		if group == "" {
			continue
		}
		groupKey := PrincipalKey{
			Name:          group,
			PrincipalType: GroupPrincipal,
		}
		if groupRoles, exists := s.principalToRole[groupKey]; exists {
			roles = append(roles, groupRoles...)
		}
	}

	s.logger.Debug("Authorization check",
		zap.String("user", req.Subject.User),
		zap.Strings("groups", req.Subject.Groups),
		zap.Strings("roles", roles),
		zap.String("action", req.ActionType),
		zap.String("method", req.Method),
		zap.String("resource", req.Resource))

	// Evaluate policies
	response := s.evaluate(roles, req)
	
	s.logger.Debug("Authorization result",
		zap.String("decision", string(response.Decision)),
		zap.String("reason", response.Reason))

	return response, nil
}

// evaluate evaluates policies for the given roles
func (s *Service) evaluate(roles []string, req *CheckRequest) *CheckResponse {
	if len(roles) == 0 {
		return &CheckResponse{
			Decision: DecisionDeny,
			Reason:   "no roles assigned to subject",
		}
	}

	// Check each role's policies
	for _, roleName := range roles {
		role, exists := s.roleToPolicy[roleName]
		if !exists {
			continue
		}

		for _, policy := range role.Policies {
			if s.assertPolicy(&policy, req) {
				return &CheckResponse{
					Decision: DecisionAllow,
					Reason:   fmt.Sprintf("allowed by role '%s'", roleName),
				}
			}
		}
	}

	return &CheckResponse{
		Decision: DecisionDeny,
		Reason:   "no matching policy found",
	}
}

// assertPolicy checks if a policy matches the request
func (s *Service) assertPolicy(policy *Policy, req *CheckRequest) bool {
	// Check action types
	if len(policy.ActionTypes) > 0 {
		actionMatch := false
		for _, actionType := range policy.ActionTypes {
			if req.ActionType == actionType {
				actionMatch = true
				break
			}
		}
		if !actionMatch {
			return false
		}
	}

	// Check method
	if policy.Method != "" && !s.matchMethodOrResource(policy.Method, req.Method) {
		return false
	}

	// Check resources
	if len(policy.Resources) > 0 {
		resourceMatch := false
		for _, resource := range policy.Resources {
			if s.matchMethodOrResource(resource, req.Resource) {
				resourceMatch = true
				break
			}
		}
		if !resourceMatch {
			return false
		}
	}

	return true
}

// matchMethodOrResource performs pattern matching for methods and resources
func (s *Service) matchMethodOrResource(pattern, value string) bool {
	// Simple wildcard matching
	if pattern == "*" {
		return true
	}

	// Exact match
	if pattern == value {
		return true
	}

	// Prefix match with wildcard
	if strings.HasSuffix(pattern, "*") {
		prefix := strings.TrimSuffix(pattern, "*")
		return strings.HasPrefix(value, prefix)
	}

	// Suffix match with wildcard
	if strings.HasPrefix(pattern, "*") {
		suffix := strings.TrimPrefix(pattern, "*")
		return strings.HasSuffix(value, suffix)
	}

	return false
}

// configToRolePolicyMap builds a map from role names to policies
func configToRolePolicyMap(config *Config) (RoleToPolicyMap, error) {
	roleToPolicy := make(RoleToPolicyMap, len(config.Roles))
	
	for _, role := range config.Roles {
		if _, exists := roleToPolicy[role.RoleName]; exists {
			return nil, fmt.Errorf("duplicate role '%s'", role.RoleName)
		}
		roleToPolicy[role.RoleName] = &role
	}

	// Verify all bound roles exist
	for _, binding := range config.RoleBindings {
		for _, roleName := range binding.To {
			if _, exists := roleToPolicy[roleName]; !exists {
				return nil, fmt.Errorf("attempted to bind to non-existent role '%s'", roleName)
			}
		}
	}

	return roleToPolicy, nil
}

// configToPrincipalRoleMap builds a map from principals to roles
func configToPrincipalRoleMap(config *Config) PrincipalToRoleMap {
	principalToRole := make(PrincipalToRoleMap)

	for _, binding := range config.RoleBindings {
		for _, principal := range binding.Principals {
			var key PrincipalKey
			
			switch principal.Type {
			case "user":
				key = PrincipalKey{
					Name:          principal.Value,
					PrincipalType: UserPrincipal,
				}
			case "group":
				key = PrincipalKey{
					Name:          principal.Value,
					PrincipalType: GroupPrincipal,
				}
			default:
				continue // Skip unknown principal types
			}

			principalToRole[key] = append(principalToRole[key], binding.To...)
		}
	}

	// Remove duplicates from role lists
	for key, roles := range principalToRole {
		uniqueRoles := make(map[string]struct{})
		for _, role := range roles {
			uniqueRoles[role] = struct{}{}
		}
		
		deduplicatedRoles := make([]string, 0, len(uniqueRoles))
		for role := range uniqueRoles {
			deduplicatedRoles = append(deduplicatedRoles, role)
		}
		
		principalToRole[key] = deduplicatedRoles
	}

	return principalToRole
}

// GetDefaultConfig returns a default authorization configuration
func GetDefaultConfig() *Config {
	return &Config{
		Roles: []Role{
			{
				RoleName: "admin",
				Policies: []Policy{
					{
						ActionTypes: []string{"*"},
						Method:      "*",
						Resources:   []string{"*"},
					},
				},
			},
			{
				RoleName: "operator",
				Policies: []Policy{
					{
						ActionTypes: []string{"READ", "UPDATE"},
						Method:      "/v1/*",
						Resources:   []string{"*"},
					},
				},
			},
			{
				RoleName: "viewer",
				Policies: []Policy{
					{
						ActionTypes: []string{"READ"},
						Method:      "/v1/*",
						Resources:   []string{"*"},
					},
				},
			},
			{
				RoleName: "discovery_user",
				Policies: []Policy{
					{
						ActionTypes: []string{"READ"},
						Method:      "/v1/discovery/*",
						Resources:   []string{"*"},
					},
					{
						ActionTypes: []string{"READ"},
						Method:      "/v1/resolver/*",
						Resources:   []string{"*"},
					},
				},
			},
			{
				RoleName: "workflow_user",
				Policies: []Policy{
					{
						ActionTypes: []string{"READ", "EXECUTE"},
						Method:      "/v1/workflows/*",
						Resources:   []string{"*"},
					},
				},
			},
		},
		RoleBindings: []RoleBinding{
			{
				Principals: []Principal{
					{Type: "group", Value: "cainuro-admins"},
				},
				To: []string{"admin"},
			},
			{
				Principals: []Principal{
					{Type: "group", Value: "cainuro-operators"},
				},
				To: []string{"operator"},
			},
			{
				Principals: []Principal{
					{Type: "group", Value: "cainuro-users"},
				},
				To: []string{"viewer", "discovery_user", "workflow_user"},
			},
		},
	}
}

// Health checks the health of the authorization service
func (s *Service) Health(ctx context.Context) error {
	// Simple health check - ensure we have roles and policies loaded
	if len(s.roleToPolicy) == 0 {
		return fmt.Errorf("no roles loaded")
	}
	return nil
}
