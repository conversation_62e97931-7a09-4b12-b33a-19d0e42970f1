{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/components/Layout.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { useAuth } from '../auth/AuthContext';\nimport { ChartBarIcon, CogIcon, MagnifyingGlassIcon, GlobeAltIcon, ArrowTrendingUpIcon, ShieldCheckIcon, CircleStackIcon, Bars3Icon, XMarkIcon, UserCircleIcon, ArrowRightOnRectangleIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst navigation = [{\n  name: 'Dashboard',\n  href: '/dashboard',\n  icon: ChartBarIcon\n}, {\n  name: 'Search',\n  href: '/search',\n  icon: MagnifyingGlassIcon\n}, {\n  name: 'Discovery Wizard',\n  href: '/discovery',\n  icon: MagnifyingGlassIcon\n}, {\n  name: 'Workflows',\n  href: '/workflows',\n  icon: CogIcon\n}, {\n  name: 'Envoy Config',\n  href: '/envoy',\n  icon: GlobeAltIcon\n}, {\n  name: 'Autoscaler',\n  href: '/autoscaler',\n  icon: ArrowTrendingUpIcon\n}, {\n  name: 'Audit',\n  href: '/audit',\n  icon: ShieldCheckIcon\n}, {\n  name: 'Database',\n  href: '/database',\n  icon: CircleStackIcon\n}];\nexport function Layout({\n  children\n}) {\n  _s();\n  var _user$roles, _user$roles2;\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [userMenuOpen, setUserMenuOpen] = useState(false);\n  const location = useLocation();\n  const {\n    user,\n    logout\n  } = useAuth();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-900\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-gray-600 bg-opacity-75\",\n        onClick: () => setSidebarOpen(false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-y-0 left-0 flex w-64 flex-col bg-gray-800\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex h-16 items-center justify-between px-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xl font-bold text-cyan-400\",\n              children: \"\\uD83D\\uDE80 CAINuro\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"text-gray-300 hover:text-white\",\n            onClick: () => setSidebarOpen(false),\n            children: /*#__PURE__*/_jsxDEV(XMarkIcon, {\n              className: \"h-6 w-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"flex-1 space-y-1 px-2 py-4\",\n          children: navigation.map(item => {\n            const isActive = location.pathname === item.href;\n            return /*#__PURE__*/_jsxDEV(Link, {\n              to: item.href,\n              className: `group flex items-center px-2 py-2 text-sm font-medium rounded-md ${isActive ? 'bg-cyan-600 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white'}`,\n              onClick: () => setSidebarOpen(false),\n              children: [/*#__PURE__*/_jsxDEV(item.icon, {\n                className: \"mr-3 h-6 w-6 flex-shrink-0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 19\n              }, this), item.name]\n            }, item.name, true, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col flex-grow bg-gray-800 pt-5 pb-4 overflow-y-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center flex-shrink-0 px-4\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xl font-bold text-cyan-400\",\n            children: \"\\uD83D\\uDE80 CAINuro Orchestrator\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-5 flex-1 flex flex-col\",\n          children: /*#__PURE__*/_jsxDEV(\"nav\", {\n            className: \"flex-1 px-2 space-y-1\",\n            children: navigation.map(item => {\n              const isActive = location.pathname === item.href;\n              return /*#__PURE__*/_jsxDEV(Link, {\n                to: item.href,\n                className: `group flex items-center px-2 py-2 text-sm font-medium rounded-md ${isActive ? 'bg-cyan-600 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white'}`,\n                children: [/*#__PURE__*/_jsxDEV(item.icon, {\n                  className: \"mr-3 h-6 w-6 flex-shrink-0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 100,\n                  columnNumber: 21\n                }, this), item.name]\n              }, item.name, true, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-shrink-0 flex border-t border-gray-700 p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center w-full\",\n            children: [/*#__PURE__*/_jsxDEV(UserCircleIcon, {\n              className: \"h-8 w-8 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-3 flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-300\",\n                children: (user === null || user === void 0 ? void 0 : user.name) || (user === null || user === void 0 ? void 0 : user.email) || 'User'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-400\",\n                children: (user === null || user === void 0 ? void 0 : (_user$roles = user.roles) === null || _user$roles === void 0 ? void 0 : _user$roles.join(', ')) || 'Loading...'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: logout,\n              className: \"ml-2 p-1 text-gray-400 hover:text-white\",\n              title: \"Logout\",\n              children: /*#__PURE__*/_jsxDEV(ArrowRightOnRectangleIcon, {\n                className: \"h-5 w-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"lg:pl-64 flex flex-col flex-1\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sticky top-0 z-10 flex-shrink-0 flex h-16 bg-gray-800 shadow\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          className: \"px-4 border-r border-gray-700 text-gray-300 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-cyan-500 lg:hidden\",\n          onClick: () => setSidebarOpen(true),\n          children: /*#__PURE__*/_jsxDEV(Bars3Icon, {\n            className: \"h-6 w-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 px-4 flex justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 flex\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full flex md:ml-0\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative w-full text-gray-400 focus-within:text-gray-600\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-y-0 left-0 flex items-center pointer-events-none\",\n                  children: /*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n                    className: \"h-5 w-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 142,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 141,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  className: \"block w-full h-full pl-8 pr-3 py-2 border-transparent text-gray-300 placeholder-gray-400 bg-gray-700 focus:outline-none focus:bg-gray-600 focus:border-transparent focus:ring-0 focus:text-gray-100 sm:text-sm\",\n                  placeholder: \"Search resources...\",\n                  type: \"search\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4 flex items-center md:ml-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                children: \"All Systems Operational\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-3 h-3 bg-green-400 rounded-full animate-pulse\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setUserMenuOpen(!userMenuOpen),\n                  className: \"flex items-center space-x-2 text-gray-300 hover:text-white p-2 rounded-md\",\n                  children: [/*#__PURE__*/_jsxDEV(UserCircleIcon, {\n                    className: \"h-6 w-6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 165,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm font-medium\",\n                    children: (user === null || user === void 0 ? void 0 : user.name) || (user === null || user === void 0 ? void 0 : user.email) || 'User'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 166,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 19\n                }, this), userMenuOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"px-4 py-2 border-b border-gray-200\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium text-gray-900\",\n                      children: (user === null || user === void 0 ? void 0 : user.name) || 'User'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 172,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: user === null || user === void 0 ? void 0 : user.email\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 173,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-400\",\n                      children: user === null || user === void 0 ? void 0 : (_user$roles2 = user.roles) === null || _user$roles2 === void 0 ? void 0 : _user$roles2.join(', ')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 174,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 171,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      setUserMenuOpen(false);\n                      logout();\n                    },\n                    className: \"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                    children: [/*#__PURE__*/_jsxDEV(ArrowRightOnRectangleIcon, {\n                      className: \"inline h-4 w-4 mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 183,\n                      columnNumber: 25\n                    }, this), \"Sign out\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 176,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 170,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 md:px-8\",\n            children: children\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 40,\n    columnNumber: 5\n  }, this);\n}\n_s(Layout, \"obOlMZzgLXYyI+i/rj2h8hJIHvA=\", false, function () {\n  return [useLocation, useAuth];\n});\n_c = Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useLocation", "useAuth", "ChartBarIcon", "CogIcon", "MagnifyingGlassIcon", "GlobeAltIcon", "ArrowTrendingUpIcon", "ShieldCheckIcon", "CircleStackIcon", "Bars3Icon", "XMarkIcon", "UserCircleIcon", "ArrowRightOnRectangleIcon", "jsxDEV", "_jsxDEV", "navigation", "name", "href", "icon", "Layout", "children", "_s", "_user$roles", "_user$roles2", "sidebarOpen", "setSidebarOpen", "userMenuOpen", "setUserMenuOpen", "location", "user", "logout", "className", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "map", "item", "isActive", "pathname", "to", "email", "roles", "join", "title", "placeholder", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/components/Layout.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { useAuth } from '../auth/AuthContext';\nimport {\n  ChartBarIcon,\n  CogIcon,\n  MagnifyingGlassIcon,\n  GlobeAltIcon,\n  ArrowTrendingUpIcon,\n  ShieldCheckIcon,\n  CircleStackIcon,\n  Bars3Icon,\n  XMarkIcon,\n  UserCircleIcon,\n  ArrowRightOnRectangleIcon,\n} from '@heroicons/react/24/outline';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: ChartBarIcon },\n  { name: 'Search', href: '/search', icon: MagnifyingGlassIcon },\n  { name: 'Discovery Wizard', href: '/discovery', icon: MagnifyingGlassIcon },\n  { name: 'Workflows', href: '/workflows', icon: CogIcon },\n  { name: 'Envoy Config', href: '/envoy', icon: GlobeAltIcon },\n  { name: 'Autoscaler', href: '/autoscaler', icon: ArrowTrendingUpIcon },\n  { name: '<PERSON><PERSON>', href: '/audit', icon: ShieldCheckIcon },\n  { name: 'Database', href: '/database', icon: CircleStackIcon },\n];\n\nexport function Layout({ children }: LayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [userMenuOpen, setUserMenuOpen] = useState(false);\n  const location = useLocation();\n  const { user, logout } = useAuth();\n\n  return (\n    <div className=\"min-h-screen bg-gray-900\">\n      {/* Mobile sidebar */}\n      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"fixed inset-y-0 left-0 flex w-64 flex-col bg-gray-800\">\n          <div className=\"flex h-16 items-center justify-between px-4\">\n            <div className=\"flex items-center\">\n              <span className=\"text-xl font-bold text-cyan-400\">🚀 CAINuro</span>\n            </div>\n            <button\n              type=\"button\"\n              className=\"text-gray-300 hover:text-white\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <XMarkIcon className=\"h-6 w-6\" />\n            </button>\n          </div>\n          <nav className=\"flex-1 space-y-1 px-2 py-4\">\n            {navigation.map((item) => {\n              const isActive = location.pathname === item.href;\n              return (\n                <Link\n                  key={item.name}\n                  to={item.href}\n                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${\n                    isActive\n                      ? 'bg-cyan-600 text-white'\n                      : 'text-gray-300 hover:bg-gray-700 hover:text-white'\n                  }`}\n                  onClick={() => setSidebarOpen(false)}\n                >\n                  <item.icon className=\"mr-3 h-6 w-6 flex-shrink-0\" />\n                  {item.name}\n                </Link>\n              );\n            })}\n          </nav>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex flex-col flex-grow bg-gray-800 pt-5 pb-4 overflow-y-auto\">\n          <div className=\"flex items-center flex-shrink-0 px-4\">\n            <span className=\"text-xl font-bold text-cyan-400\">🚀 CAINuro Orchestrator</span>\n          </div>\n          <div className=\"mt-5 flex-1 flex flex-col\">\n            <nav className=\"flex-1 px-2 space-y-1\">\n              {navigation.map((item) => {\n                const isActive = location.pathname === item.href;\n                return (\n                  <Link\n                    key={item.name}\n                    to={item.href}\n                    className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${\n                      isActive\n                        ? 'bg-cyan-600 text-white'\n                        : 'text-gray-300 hover:bg-gray-700 hover:text-white'\n                    }`}\n                  >\n                    <item.icon className=\"mr-3 h-6 w-6 flex-shrink-0\" />\n                    {item.name}\n                  </Link>\n                );\n              })}\n            </nav>\n          </div>\n          <div className=\"flex-shrink-0 flex border-t border-gray-700 p-4\">\n            <div className=\"flex items-center w-full\">\n              <UserCircleIcon className=\"h-8 w-8 text-gray-400\" />\n              <div className=\"ml-3 flex-1\">\n                <p className=\"text-sm font-medium text-gray-300\">{user?.name || user?.email || 'User'}</p>\n                <p className=\"text-xs text-gray-400\">{user?.roles?.join(', ') || 'Loading...'}</p>\n              </div>\n              <button\n                onClick={logout}\n                className=\"ml-2 p-1 text-gray-400 hover:text-white\"\n                title=\"Logout\"\n              >\n                <ArrowRightOnRectangleIcon className=\"h-5 w-5\" />\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64 flex flex-col flex-1\">\n        {/* Top bar */}\n        <div className=\"sticky top-0 z-10 flex-shrink-0 flex h-16 bg-gray-800 shadow\">\n          <button\n            type=\"button\"\n            className=\"px-4 border-r border-gray-700 text-gray-300 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-cyan-500 lg:hidden\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <Bars3Icon className=\"h-6 w-6\" />\n          </button>\n          <div className=\"flex-1 px-4 flex justify-between\">\n            <div className=\"flex-1 flex\">\n              <div className=\"w-full flex md:ml-0\">\n                <div className=\"relative w-full text-gray-400 focus-within:text-gray-600\">\n                  <div className=\"absolute inset-y-0 left-0 flex items-center pointer-events-none\">\n                    <MagnifyingGlassIcon className=\"h-5 w-5\" />\n                  </div>\n                  <input\n                    className=\"block w-full h-full pl-8 pr-3 py-2 border-transparent text-gray-300 placeholder-gray-400 bg-gray-700 focus:outline-none focus:bg-gray-600 focus:border-transparent focus:ring-0 focus:text-gray-100 sm:text-sm\"\n                    placeholder=\"Search resources...\"\n                    type=\"search\"\n                  />\n                </div>\n              </div>\n            </div>\n            <div className=\"ml-4 flex items-center md:ml-6\">\n              <div className=\"flex items-center space-x-4\">\n                <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n                  All Systems Operational\n                </span>\n                <div className=\"w-3 h-3 bg-green-400 rounded-full animate-pulse\"></div>\n\n                {/* User menu */}\n                <div className=\"relative\">\n                  <button\n                    onClick={() => setUserMenuOpen(!userMenuOpen)}\n                    className=\"flex items-center space-x-2 text-gray-300 hover:text-white p-2 rounded-md\"\n                  >\n                    <UserCircleIcon className=\"h-6 w-6\" />\n                    <span className=\"text-sm font-medium\">{user?.name || user?.email || 'User'}</span>\n                  </button>\n\n                  {userMenuOpen && (\n                    <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50\">\n                      <div className=\"px-4 py-2 border-b border-gray-200\">\n                        <p className=\"text-sm font-medium text-gray-900\">{user?.name || 'User'}</p>\n                        <p className=\"text-sm text-gray-500\">{user?.email}</p>\n                        <p className=\"text-xs text-gray-400\">{user?.roles?.join(', ')}</p>\n                      </div>\n                      <button\n                        onClick={() => {\n                          setUserMenuOpen(false);\n                          logout();\n                        }}\n                        className=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                      >\n                        <ArrowRightOnRectangleIcon className=\"inline h-4 w-4 mr-2\" />\n                        Sign out\n                      </button>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1\">\n          <div className=\"py-6\">\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 md:px-8\">\n              {children}\n            </div>\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,SACEC,YAAY,EACZC,OAAO,EACPC,mBAAmB,EACnBC,YAAY,EACZC,mBAAmB,EACnBC,eAAe,EACfC,eAAe,EACfC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,yBAAyB,QACpB,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMrC,MAAMC,UAAU,GAAG,CACjB;EAAEC,IAAI,EAAE,WAAW;EAAEC,IAAI,EAAE,YAAY;EAAEC,IAAI,EAAEhB;AAAa,CAAC,EAC7D;EAAEc,IAAI,EAAE,QAAQ;EAAEC,IAAI,EAAE,SAAS;EAAEC,IAAI,EAAEd;AAAoB,CAAC,EAC9D;EAAEY,IAAI,EAAE,kBAAkB;EAAEC,IAAI,EAAE,YAAY;EAAEC,IAAI,EAAEd;AAAoB,CAAC,EAC3E;EAAEY,IAAI,EAAE,WAAW;EAAEC,IAAI,EAAE,YAAY;EAAEC,IAAI,EAAEf;AAAQ,CAAC,EACxD;EAAEa,IAAI,EAAE,cAAc;EAAEC,IAAI,EAAE,QAAQ;EAAEC,IAAI,EAAEb;AAAa,CAAC,EAC5D;EAAEW,IAAI,EAAE,YAAY;EAAEC,IAAI,EAAE,aAAa;EAAEC,IAAI,EAAEZ;AAAoB,CAAC,EACtE;EAAEU,IAAI,EAAE,OAAO;EAAEC,IAAI,EAAE,QAAQ;EAAEC,IAAI,EAAEX;AAAgB,CAAC,EACxD;EAAES,IAAI,EAAE,UAAU;EAAEC,IAAI,EAAE,WAAW;EAAEC,IAAI,EAAEV;AAAgB,CAAC,CAC/D;AAED,OAAO,SAASW,MAAMA,CAAC;EAAEC;AAAsB,CAAC,EAAE;EAAAC,EAAA;EAAA,IAAAC,WAAA,EAAAC,YAAA;EAChD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC4B,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM8B,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE6B,IAAI;IAAEC;EAAO,CAAC,GAAG7B,OAAO,CAAC,CAAC;EAElC,oBACEa,OAAA;IAAKiB,SAAS,EAAC,0BAA0B;IAAAX,QAAA,gBAEvCN,OAAA;MAAKiB,SAAS,EAAE,gCAAgCP,WAAW,GAAG,OAAO,GAAG,QAAQ,EAAG;MAAAJ,QAAA,gBACjFN,OAAA;QAAKiB,SAAS,EAAC,yCAAyC;QAACC,OAAO,EAAEA,CAAA,KAAMP,cAAc,CAAC,KAAK;MAAE;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjGtB,OAAA;QAAKiB,SAAS,EAAC,uDAAuD;QAAAX,QAAA,gBACpEN,OAAA;UAAKiB,SAAS,EAAC,6CAA6C;UAAAX,QAAA,gBAC1DN,OAAA;YAAKiB,SAAS,EAAC,mBAAmB;YAAAX,QAAA,eAChCN,OAAA;cAAMiB,SAAS,EAAC,iCAAiC;cAAAX,QAAA,EAAC;YAAU;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC,eACNtB,OAAA;YACEuB,IAAI,EAAC,QAAQ;YACbN,SAAS,EAAC,gCAAgC;YAC1CC,OAAO,EAAEA,CAAA,KAAMP,cAAc,CAAC,KAAK,CAAE;YAAAL,QAAA,eAErCN,OAAA,CAACJ,SAAS;cAACqB,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNtB,OAAA;UAAKiB,SAAS,EAAC,4BAA4B;UAAAX,QAAA,EACxCL,UAAU,CAACuB,GAAG,CAAEC,IAAI,IAAK;YACxB,MAAMC,QAAQ,GAAGZ,QAAQ,CAACa,QAAQ,KAAKF,IAAI,CAACtB,IAAI;YAChD,oBACEH,OAAA,CAACf,IAAI;cAEH2C,EAAE,EAAEH,IAAI,CAACtB,IAAK;cACdc,SAAS,EAAE,oEACTS,QAAQ,GACJ,wBAAwB,GACxB,kDAAkD,EACrD;cACHR,OAAO,EAAEA,CAAA,KAAMP,cAAc,CAAC,KAAK,CAAE;cAAAL,QAAA,gBAErCN,OAAA,CAACyB,IAAI,CAACrB,IAAI;gBAACa,SAAS,EAAC;cAA4B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EACnDG,IAAI,CAACvB,IAAI;YAAA,GAVLuB,IAAI,CAACvB,IAAI;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAWV,CAAC;UAEX,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtB,OAAA;MAAKiB,SAAS,EAAC,0DAA0D;MAAAX,QAAA,eACvEN,OAAA;QAAKiB,SAAS,EAAC,+DAA+D;QAAAX,QAAA,gBAC5EN,OAAA;UAAKiB,SAAS,EAAC,sCAAsC;UAAAX,QAAA,eACnDN,OAAA;YAAMiB,SAAS,EAAC,iCAAiC;YAAAX,QAAA,EAAC;UAAuB;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7E,CAAC,eACNtB,OAAA;UAAKiB,SAAS,EAAC,2BAA2B;UAAAX,QAAA,eACxCN,OAAA;YAAKiB,SAAS,EAAC,uBAAuB;YAAAX,QAAA,EACnCL,UAAU,CAACuB,GAAG,CAAEC,IAAI,IAAK;cACxB,MAAMC,QAAQ,GAAGZ,QAAQ,CAACa,QAAQ,KAAKF,IAAI,CAACtB,IAAI;cAChD,oBACEH,OAAA,CAACf,IAAI;gBAEH2C,EAAE,EAAEH,IAAI,CAACtB,IAAK;gBACdc,SAAS,EAAE,oEACTS,QAAQ,GACJ,wBAAwB,GACxB,kDAAkD,EACrD;gBAAApB,QAAA,gBAEHN,OAAA,CAACyB,IAAI,CAACrB,IAAI;kBAACa,SAAS,EAAC;gBAA4B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACnDG,IAAI,CAACvB,IAAI;cAAA,GATLuB,IAAI,CAACvB,IAAI;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUV,CAAC;YAEX,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNtB,OAAA;UAAKiB,SAAS,EAAC,iDAAiD;UAAAX,QAAA,eAC9DN,OAAA;YAAKiB,SAAS,EAAC,0BAA0B;YAAAX,QAAA,gBACvCN,OAAA,CAACH,cAAc;cAACoB,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpDtB,OAAA;cAAKiB,SAAS,EAAC,aAAa;cAAAX,QAAA,gBAC1BN,OAAA;gBAAGiB,SAAS,EAAC,mCAAmC;gBAAAX,QAAA,EAAE,CAAAS,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEb,IAAI,MAAIa,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEc,KAAK,KAAI;cAAM;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1FtB,OAAA;gBAAGiB,SAAS,EAAC,uBAAuB;gBAAAX,QAAA,EAAE,CAAAS,IAAI,aAAJA,IAAI,wBAAAP,WAAA,GAAJO,IAAI,CAAEe,KAAK,cAAAtB,WAAA,uBAAXA,WAAA,CAAauB,IAAI,CAAC,IAAI,CAAC,KAAI;cAAY;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC,eACNtB,OAAA;cACEkB,OAAO,EAAEF,MAAO;cAChBC,SAAS,EAAC,yCAAyC;cACnDe,KAAK,EAAC,QAAQ;cAAA1B,QAAA,eAEdN,OAAA,CAACF,yBAAyB;gBAACmB,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtB,OAAA;MAAKiB,SAAS,EAAC,+BAA+B;MAAAX,QAAA,gBAE5CN,OAAA;QAAKiB,SAAS,EAAC,8DAA8D;QAAAX,QAAA,gBAC3EN,OAAA;UACEuB,IAAI,EAAC,QAAQ;UACbN,SAAS,EAAC,4HAA4H;UACtIC,OAAO,EAAEA,CAAA,KAAMP,cAAc,CAAC,IAAI,CAAE;UAAAL,QAAA,eAEpCN,OAAA,CAACL,SAAS;YAACsB,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eACTtB,OAAA;UAAKiB,SAAS,EAAC,kCAAkC;UAAAX,QAAA,gBAC/CN,OAAA;YAAKiB,SAAS,EAAC,aAAa;YAAAX,QAAA,eAC1BN,OAAA;cAAKiB,SAAS,EAAC,qBAAqB;cAAAX,QAAA,eAClCN,OAAA;gBAAKiB,SAAS,EAAC,0DAA0D;gBAAAX,QAAA,gBACvEN,OAAA;kBAAKiB,SAAS,EAAC,iEAAiE;kBAAAX,QAAA,eAC9EN,OAAA,CAACV,mBAAmB;oBAAC2B,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACNtB,OAAA;kBACEiB,SAAS,EAAC,gNAAgN;kBAC1NgB,WAAW,EAAC,qBAAqB;kBACjCV,IAAI,EAAC;gBAAQ;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNtB,OAAA;YAAKiB,SAAS,EAAC,gCAAgC;YAAAX,QAAA,eAC7CN,OAAA;cAAKiB,SAAS,EAAC,6BAA6B;cAAAX,QAAA,gBAC1CN,OAAA;gBAAMiB,SAAS,EAAC,qGAAqG;gBAAAX,QAAA,EAAC;cAEtH;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPtB,OAAA;gBAAKiB,SAAS,EAAC;cAAiD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAGvEtB,OAAA;gBAAKiB,SAAS,EAAC,UAAU;gBAAAX,QAAA,gBACvBN,OAAA;kBACEkB,OAAO,EAAEA,CAAA,KAAML,eAAe,CAAC,CAACD,YAAY,CAAE;kBAC9CK,SAAS,EAAC,2EAA2E;kBAAAX,QAAA,gBAErFN,OAAA,CAACH,cAAc;oBAACoB,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACtCtB,OAAA;oBAAMiB,SAAS,EAAC,qBAAqB;oBAAAX,QAAA,EAAE,CAAAS,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEb,IAAI,MAAIa,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEc,KAAK,KAAI;kBAAM;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E,CAAC,EAERV,YAAY,iBACXZ,OAAA;kBAAKiB,SAAS,EAAC,oEAAoE;kBAAAX,QAAA,gBACjFN,OAAA;oBAAKiB,SAAS,EAAC,oCAAoC;oBAAAX,QAAA,gBACjDN,OAAA;sBAAGiB,SAAS,EAAC,mCAAmC;sBAAAX,QAAA,EAAE,CAAAS,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEb,IAAI,KAAI;oBAAM;sBAAAiB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC3EtB,OAAA;sBAAGiB,SAAS,EAAC,uBAAuB;sBAAAX,QAAA,EAAES,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEc;oBAAK;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACtDtB,OAAA;sBAAGiB,SAAS,EAAC,uBAAuB;sBAAAX,QAAA,EAAES,IAAI,aAAJA,IAAI,wBAAAN,YAAA,GAAJM,IAAI,CAAEe,KAAK,cAAArB,YAAA,uBAAXA,YAAA,CAAasB,IAAI,CAAC,IAAI;oBAAC;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/D,CAAC,eACNtB,OAAA;oBACEkB,OAAO,EAAEA,CAAA,KAAM;sBACbL,eAAe,CAAC,KAAK,CAAC;sBACtBG,MAAM,CAAC,CAAC;oBACV,CAAE;oBACFC,SAAS,EAAC,0EAA0E;oBAAAX,QAAA,gBAEpFN,OAAA,CAACF,yBAAyB;sBAACmB,SAAS,EAAC;oBAAqB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,YAE/D;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtB,OAAA;QAAMiB,SAAS,EAAC,QAAQ;QAAAX,QAAA,eACtBN,OAAA;UAAKiB,SAAS,EAAC,MAAM;UAAAX,QAAA,eACnBN,OAAA;YAAKiB,SAAS,EAAC,wCAAwC;YAAAX,QAAA,EACpDA;UAAQ;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACf,EAAA,CA5KeF,MAAM;EAAA,QAGHnB,WAAW,EACHC,OAAO;AAAA;AAAA+C,EAAA,GAJlB7B,MAAM;AAAA,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}