// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: sourcecontrol/v1/sourcecontrol.proto

package sourcecontrolv1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetRepositoryOptionsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRepositoryOptionsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRepositoryOptionsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRepositoryOptionsRequestMultiError, or nil if none found.
func (m *GetRepositoryOptionsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRepositoryOptionsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetRepositoryOptionsRequestMultiError(errors)
	}

	return nil
}

// GetRepositoryOptionsRequestMultiError is an error wrapping multiple
// validation errors returned by GetRepositoryOptionsRequest.ValidateAll() if
// the designated constraints aren't met.
type GetRepositoryOptionsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRepositoryOptionsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRepositoryOptionsRequestMultiError) AllErrors() []error { return m }

// GetRepositoryOptionsRequestValidationError is the validation error returned
// by GetRepositoryOptionsRequest.Validate if the designated constraints
// aren't met.
type GetRepositoryOptionsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRepositoryOptionsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRepositoryOptionsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRepositoryOptionsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRepositoryOptionsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRepositoryOptionsRequestValidationError) ErrorName() string {
	return "GetRepositoryOptionsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRepositoryOptionsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRepositoryOptionsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRepositoryOptionsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRepositoryOptionsRequestValidationError{}

// Validate checks the field values on Entity with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Entity) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Entity with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in EntityMultiError, or nil if none found.
func (m *Entity) ValidateAll() error {
	return m.validate(true)
}

func (m *Entity) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for PhotoUrl

	if len(errors) > 0 {
		return EntityMultiError(errors)
	}

	return nil
}

// EntityMultiError is an error wrapping multiple validation errors returned by
// Entity.ValidateAll() if the designated constraints aren't met.
type EntityMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EntityMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EntityMultiError) AllErrors() []error { return m }

// EntityValidationError is the validation error returned by Entity.Validate if
// the designated constraints aren't met.
type EntityValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EntityValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EntityValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EntityValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EntityValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EntityValidationError) ErrorName() string { return "EntityValidationError" }

// Error satisfies the builtin error interface
func (e EntityValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEntity.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EntityValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EntityValidationError{}

// Validate checks the field values on GetRepositoryOptionsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRepositoryOptionsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRepositoryOptionsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRepositoryOptionsResponseMultiError, or nil if none found.
func (m *GetRepositoryOptionsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRepositoryOptionsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetAvailableOwners() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRepositoryOptionsResponseValidationError{
						field:  fmt.Sprintf("AvailableOwners[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRepositoryOptionsResponseValidationError{
						field:  fmt.Sprintf("AvailableOwners[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRepositoryOptionsResponseValidationError{
					field:  fmt.Sprintf("AvailableOwners[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetRepositoryOptionsResponseMultiError(errors)
	}

	return nil
}

// GetRepositoryOptionsResponseMultiError is an error wrapping multiple
// validation errors returned by GetRepositoryOptionsResponse.ValidateAll() if
// the designated constraints aren't met.
type GetRepositoryOptionsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRepositoryOptionsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRepositoryOptionsResponseMultiError) AllErrors() []error { return m }

// GetRepositoryOptionsResponseValidationError is the validation error returned
// by GetRepositoryOptionsResponse.Validate if the designated constraints
// aren't met.
type GetRepositoryOptionsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRepositoryOptionsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRepositoryOptionsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRepositoryOptionsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRepositoryOptionsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRepositoryOptionsResponseValidationError) ErrorName() string {
	return "GetRepositoryOptionsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRepositoryOptionsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRepositoryOptionsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRepositoryOptionsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRepositoryOptionsResponseValidationError{}

// Validate checks the field values on CreateRepositoryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateRepositoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateRepositoryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateRepositoryRequestMultiError, or nil if none found.
func (m *CreateRepositoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateRepositoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(m.GetOwner()) < 1 {
		err := CreateRepositoryRequestValidationError{
			field:  "Owner",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetName()) < 1 {
		err := CreateRepositoryRequestValidationError{
			field:  "Name",
			reason: "value length must be at least 1 bytes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Description

	oneofOptionsPresent := false
	switch v := m.Options.(type) {
	case *CreateRepositoryRequest_CustomOptions:
		if v == nil {
			err := CreateRepositoryRequestValidationError{
				field:  "Options",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		oneofOptionsPresent = true

		if all {
			switch v := interface{}(m.GetCustomOptions()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateRepositoryRequestValidationError{
						field:  "CustomOptions",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateRepositoryRequestValidationError{
						field:  "CustomOptions",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCustomOptions()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateRepositoryRequestValidationError{
					field:  "CustomOptions",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CreateRepositoryRequest_GithubOptions:
		if v == nil {
			err := CreateRepositoryRequestValidationError{
				field:  "Options",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		oneofOptionsPresent = true

		if all {
			switch v := interface{}(m.GetGithubOptions()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateRepositoryRequestValidationError{
						field:  "GithubOptions",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateRepositoryRequestValidationError{
						field:  "GithubOptions",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetGithubOptions()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateRepositoryRequestValidationError{
					field:  "GithubOptions",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	if !oneofOptionsPresent {
		err := CreateRepositoryRequestValidationError{
			field:  "Options",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CreateRepositoryRequestMultiError(errors)
	}

	return nil
}

// CreateRepositoryRequestMultiError is an error wrapping multiple validation
// errors returned by CreateRepositoryRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateRepositoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateRepositoryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateRepositoryRequestMultiError) AllErrors() []error { return m }

// CreateRepositoryRequestValidationError is the validation error returned by
// CreateRepositoryRequest.Validate if the designated constraints aren't met.
type CreateRepositoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateRepositoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateRepositoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateRepositoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateRepositoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateRepositoryRequestValidationError) ErrorName() string {
	return "CreateRepositoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateRepositoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateRepositoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateRepositoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateRepositoryRequestValidationError{}

// Validate checks the field values on CreateRepositoryResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateRepositoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateRepositoryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateRepositoryResponseMultiError, or nil if none found.
func (m *CreateRepositoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateRepositoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Url

	if len(errors) > 0 {
		return CreateRepositoryResponseMultiError(errors)
	}

	return nil
}

// CreateRepositoryResponseMultiError is an error wrapping multiple validation
// errors returned by CreateRepositoryResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateRepositoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateRepositoryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateRepositoryResponseMultiError) AllErrors() []error { return m }

// CreateRepositoryResponseValidationError is the validation error returned by
// CreateRepositoryResponse.Validate if the designated constraints aren't met.
type CreateRepositoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateRepositoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateRepositoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateRepositoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateRepositoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateRepositoryResponseValidationError) ErrorName() string {
	return "CreateRepositoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateRepositoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateRepositoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateRepositoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateRepositoryResponseValidationError{}
