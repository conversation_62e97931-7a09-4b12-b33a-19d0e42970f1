<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/icon.svg" />
    <link rel="shortcut icon" href="%PUBLIC_URL%/favicon.ico" />
    <link rel="preconnect" href="https://fonts.gstatic.com" />
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Clutch will assist you in safely modifying resources."
    />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" crossorigin="use-credentials"/>
    <!--
      %PUBLIC_URL% will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.
    -->
    <title>clutch</title>

    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=%REACT_APP_GA_TRACKING_ID%"></script>
    <script>
      if ('%NODE_ENV%' !== 'development') {
        window.dataLayer = window.dataLayer || [];
        function gtag() {
          dataLayer.push(arguments);
        }
        gtag('js', new Date());
        gtag('config', '%REACT_APP_GOOGLE_ANALYTICS_DEFAULT_TAG%');
        let additionalGATags = '%REACT_APP_GOOGLE_ANALYTICS_SUPPL_TAGS%'.split(',');
        additionalGATags.forEach(tag => gtag('config', tag));
      }
    </script>
    <!-- End Google Analytics -->
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>
