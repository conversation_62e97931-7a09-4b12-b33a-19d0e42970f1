{"ast": null, "code": "import React,{useEffect,useState}from'react';import{CogIcon,MagnifyingGlassIcon,GlobeAltIcon,ArrowTrendingUpIcon,ShieldCheckIcon,CircleStackIcon}from'@heroicons/react/24/outline';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const serviceIcons={audit:ShieldCheckIcon,autoscaler:ArrowTrendingUpIcon,db_admin:CircleStackIcon,discovery:MagnifyingGlassIcon,envoy_control_plane:GlobeAltIcon,workflow:CogIcon};export function Dashboard(){const[health,setHealth]=useState(null);const[loading,setLoading]=useState(true);useEffect(()=>{const fetchHealth=async()=>{try{const response=await fetch('/health');const data=await response.json();setHealth(data);}catch(error){console.error('Failed to fetch health status:',error);}finally{setLoading(false);}};fetchHealth();const interval=setInterval(fetchHealth,30000);// Refresh every 30 seconds\nreturn()=>clearInterval(interval);},[]);if(loading){return/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center h-64\",children:/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-32 w-32 border-b-2 border-cyan-500\"})});}return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-3xl font-bold text-white\",children:\"CAINuro Orchestrator Dashboard\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-gray-400\",children:\"Real-time system status and service monitoring\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-gray-800 overflow-hidden shadow rounded-lg\",children:/*#__PURE__*/_jsx(\"div\",{className:\"px-4 py-5 sm:p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 rounded-full flex items-center justify-center \".concat((health===null||health===void 0?void 0:health.status)==='healthy'?'bg-green-100':'bg-red-100'),children:/*#__PURE__*/_jsx(\"div\",{className:\"w-4 h-4 rounded-full \".concat((health===null||health===void 0?void 0:health.status)==='healthy'?'bg-green-500':'bg-red-500')})})}),/*#__PURE__*/_jsx(\"div\",{className:\"ml-5 w-0 flex-1\",children:/*#__PURE__*/_jsxs(\"dl\",{children:[/*#__PURE__*/_jsx(\"dt\",{className:\"text-sm font-medium text-gray-400 truncate\",children:\"System Status\"}),/*#__PURE__*/_jsxs(\"dd\",{className:\"flex items-baseline\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-2xl font-semibold text-white\",children:(health===null||health===void 0?void 0:health.status)==='healthy'?'All Systems Operational':'System Issues Detected'}),/*#__PURE__*/_jsxs(\"div\",{className:\"ml-2 flex items-baseline text-sm font-semibold text-green-400\",children:[health===null||health===void 0?void 0:health.mode,\" Mode\"]})]})]})})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3\",children:(health===null||health===void 0?void 0:health.services)&&Object.entries(health.services).map(_ref=>{let[serviceName,status]=_ref;const IconComponent=serviceIcons[serviceName]||CogIcon;const isActive=status==='active';return/*#__PURE__*/_jsx(\"div\",{className:\"bg-gray-800 overflow-hidden shadow rounded-lg\",children:/*#__PURE__*/_jsx(\"div\",{className:\"p-5\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(IconComponent,{className:\"h-6 w-6 \".concat(isActive?'text-green-400':'text-red-400')})}),/*#__PURE__*/_jsx(\"div\",{className:\"ml-5 w-0 flex-1\",children:/*#__PURE__*/_jsxs(\"dl\",{children:[/*#__PURE__*/_jsx(\"dt\",{className:\"text-sm font-medium text-gray-400 truncate\",children:serviceName.replace(/_/g,' ').replace(/\\b\\w/g,l=>l.toUpperCase())}),/*#__PURE__*/_jsx(\"dd\",{className:\"text-lg font-medium text-white\",children:/*#__PURE__*/_jsx(\"span\",{className:\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(isActive?'bg-green-100 text-green-800':'bg-red-100 text-red-800'),children:status})})]})})]})})},serviceName);})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-gray-800 shadow rounded-lg\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"px-4 py-5 sm:p-6\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg leading-6 font-medium text-white\",children:\"Quick Actions\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-5 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\",children:[/*#__PURE__*/_jsxs(\"button\",{className:\"relative group bg-gray-700 p-3 focus:outline-none focus:ring-2 focus:ring-cyan-500 rounded-lg hover:bg-gray-600 transition-colors\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"span\",{className:\"rounded-lg inline-flex p-3 bg-cyan-600 text-white\",children:/*#__PURE__*/_jsx(MagnifyingGlassIcon,{className:\"h-6 w-6\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-8\",children:[/*#__PURE__*/_jsxs(\"h3\",{className:\"text-lg font-medium text-white\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"absolute inset-0\"}),\"Discover Resources\"]}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-sm text-gray-400\",children:\"Search across AWS, GCP, and Azure\"})]})]}),/*#__PURE__*/_jsxs(\"button\",{className:\"relative group bg-gray-700 p-3 focus:outline-none focus:ring-2 focus:ring-cyan-500 rounded-lg hover:bg-gray-600 transition-colors\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"span\",{className:\"rounded-lg inline-flex p-3 bg-purple-600 text-white\",children:/*#__PURE__*/_jsx(CogIcon,{className:\"h-6 w-6\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-8\",children:[/*#__PURE__*/_jsxs(\"h3\",{className:\"text-lg font-medium text-white\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"absolute inset-0\"}),\"Run Workflow\"]}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-sm text-gray-400\",children:\"Execute automation workflows\"})]})]}),/*#__PURE__*/_jsxs(\"button\",{className:\"relative group bg-gray-700 p-3 focus:outline-none focus:ring-2 focus:ring-cyan-500 rounded-lg hover:bg-gray-600 transition-colors\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"span\",{className:\"rounded-lg inline-flex p-3 bg-green-600 text-white\",children:/*#__PURE__*/_jsx(GlobeAltIcon,{className:\"h-6 w-6\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-8\",children:[/*#__PURE__*/_jsxs(\"h3\",{className:\"text-lg font-medium text-white\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"absolute inset-0\"}),\"Envoy Config\"]}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-sm text-gray-400\",children:\"Manage Envoy configurations\"})]})]}),/*#__PURE__*/_jsxs(\"button\",{className:\"relative group bg-gray-700 p-3 focus:outline-none focus:ring-2 focus:ring-cyan-500 rounded-lg hover:bg-gray-600 transition-colors\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"span\",{className:\"rounded-lg inline-flex p-3 bg-orange-600 text-white\",children:/*#__PURE__*/_jsx(ShieldCheckIcon,{className:\"h-6 w-6\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-8\",children:[/*#__PURE__*/_jsxs(\"h3\",{className:\"text-lg font-medium text-white\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"absolute inset-0\"}),\"Audit Logs\"]}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-sm text-gray-400\",children:\"View tamper-proof audit trail\"})]})]})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-gray-800 shadow rounded-lg\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"px-4 py-5 sm:p-6\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg leading-6 font-medium text-white\",children:\"System Information\"}),/*#__PURE__*/_jsxs(\"dl\",{className:\"mt-5 grid grid-cols-1 gap-5 sm:grid-cols-3\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"dt\",{className:\"text-sm font-medium text-gray-400\",children:\"Version\"}),/*#__PURE__*/_jsx(\"dd\",{className:\"mt-1 text-sm text-white\",children:health===null||health===void 0?void 0:health.version})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"dt\",{className:\"text-sm font-medium text-gray-400\",children:\"Mode\"}),/*#__PURE__*/_jsx(\"dd\",{className:\"mt-1 text-sm text-white\",children:health===null||health===void 0?void 0:health.mode})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"dt\",{className:\"text-sm font-medium text-gray-400\",children:\"Services\"}),/*#__PURE__*/_jsxs(\"dd\",{className:\"mt-1 text-sm text-white\",children:[health!==null&&health!==void 0&&health.services?Object.keys(health.services).length:0,\" active\"]})]})]})]})})]});}", "map": {"version": 3, "names": ["React", "useEffect", "useState", "CogIcon", "MagnifyingGlassIcon", "GlobeAltIcon", "ArrowTrendingUpIcon", "ShieldCheckIcon", "CircleStackIcon", "jsx", "_jsx", "jsxs", "_jsxs", "serviceIcons", "audit", "autoscaler", "db_admin", "discovery", "envoy_control_plane", "workflow", "Dashboard", "health", "setHealth", "loading", "setLoading", "fetchHealth", "response", "fetch", "data", "json", "error", "console", "interval", "setInterval", "clearInterval", "className", "children", "concat", "status", "mode", "services", "Object", "entries", "map", "_ref", "serviceName", "IconComponent", "isActive", "replace", "l", "toUpperCase", "version", "keys", "length"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/Dashboard.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport {\n  ChartBarIcon,\n  CogIcon,\n  MagnifyingGlassIcon,\n  GlobeAltIcon,\n  ArrowTrendingUpIcon,\n  ShieldCheckIcon,\n  CircleStackIcon,\n} from '@heroicons/react/24/outline';\n\ninterface HealthStatus {\n  mode: string;\n  services: Record<string, string>;\n  status: string;\n  version: string;\n}\n\nconst serviceIcons: Record<string, any> = {\n  audit: ShieldCheckIcon,\n  autoscaler: ArrowTrendingUpIcon,\n  db_admin: CircleStackIcon,\n  discovery: MagnifyingGlassIcon,\n  envoy_control_plane: GlobeAltIcon,\n  workflow: CogIcon,\n};\n\nexport function Dashboard() {\n  const [health, setHealth] = useState<HealthStatus | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchHealth = async () => {\n      try {\n        const response = await fetch('/health');\n        const data = await response.json();\n        setHealth(data);\n      } catch (error) {\n        console.error('Failed to fetch health status:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchHealth();\n    const interval = setInterval(fetchHealth, 30000); // Refresh every 30 seconds\n\n    return () => clearInterval(interval);\n  }, []);\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-cyan-500\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div>\n        <h1 className=\"text-3xl font-bold text-white\">CAINuro Orchestrator Dashboard</h1>\n        <p className=\"mt-2 text-gray-400\">\n          Real-time system status and service monitoring\n        </p>\n      </div>\n\n      {/* Status Overview */}\n      <div className=\"bg-gray-800 overflow-hidden shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${\n                health?.status === 'healthy' ? 'bg-green-100' : 'bg-red-100'\n              }`}>\n                <div className={`w-4 h-4 rounded-full ${\n                  health?.status === 'healthy' ? 'bg-green-500' : 'bg-red-500'\n                }`}></div>\n              </div>\n            </div>\n            <div className=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt className=\"text-sm font-medium text-gray-400 truncate\">System Status</dt>\n                <dd className=\"flex items-baseline\">\n                  <div className=\"text-2xl font-semibold text-white\">\n                    {health?.status === 'healthy' ? 'All Systems Operational' : 'System Issues Detected'}\n                  </div>\n                  <div className=\"ml-2 flex items-baseline text-sm font-semibold text-green-400\">\n                    {health?.mode} Mode\n                  </div>\n                </dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Services Grid */}\n      <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3\">\n        {health?.services && Object.entries(health.services).map(([serviceName, status]) => {\n          const IconComponent = serviceIcons[serviceName] || CogIcon;\n          const isActive = status === 'active';\n          \n          return (\n            <div key={serviceName} className=\"bg-gray-800 overflow-hidden shadow rounded-lg\">\n              <div className=\"p-5\">\n                <div className=\"flex items-center\">\n                  <div className=\"flex-shrink-0\">\n                    <IconComponent className={`h-6 w-6 ${isActive ? 'text-green-400' : 'text-red-400'}`} />\n                  </div>\n                  <div className=\"ml-5 w-0 flex-1\">\n                    <dl>\n                      <dt className=\"text-sm font-medium text-gray-400 truncate\">\n                        {serviceName.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase())}\n                      </dt>\n                      <dd className=\"text-lg font-medium text-white\">\n                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                          isActive \n                            ? 'bg-green-100 text-green-800' \n                            : 'bg-red-100 text-red-800'\n                        }`}>\n                          {status}\n                        </span>\n                      </dd>\n                    </dl>\n                  </div>\n                </div>\n              </div>\n            </div>\n          );\n        })}\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"bg-gray-800 shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <h3 className=\"text-lg leading-6 font-medium text-white\">Quick Actions</h3>\n          <div className=\"mt-5 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\">\n            <button className=\"relative group bg-gray-700 p-3 focus:outline-none focus:ring-2 focus:ring-cyan-500 rounded-lg hover:bg-gray-600 transition-colors\">\n              <div>\n                <span className=\"rounded-lg inline-flex p-3 bg-cyan-600 text-white\">\n                  <MagnifyingGlassIcon className=\"h-6 w-6\" />\n                </span>\n              </div>\n              <div className=\"mt-8\">\n                <h3 className=\"text-lg font-medium text-white\">\n                  <span className=\"absolute inset-0\" />\n                  Discover Resources\n                </h3>\n                <p className=\"mt-2 text-sm text-gray-400\">\n                  Search across AWS, GCP, and Azure\n                </p>\n              </div>\n            </button>\n\n            <button className=\"relative group bg-gray-700 p-3 focus:outline-none focus:ring-2 focus:ring-cyan-500 rounded-lg hover:bg-gray-600 transition-colors\">\n              <div>\n                <span className=\"rounded-lg inline-flex p-3 bg-purple-600 text-white\">\n                  <CogIcon className=\"h-6 w-6\" />\n                </span>\n              </div>\n              <div className=\"mt-8\">\n                <h3 className=\"text-lg font-medium text-white\">\n                  <span className=\"absolute inset-0\" />\n                  Run Workflow\n                </h3>\n                <p className=\"mt-2 text-sm text-gray-400\">\n                  Execute automation workflows\n                </p>\n              </div>\n            </button>\n\n            <button className=\"relative group bg-gray-700 p-3 focus:outline-none focus:ring-2 focus:ring-cyan-500 rounded-lg hover:bg-gray-600 transition-colors\">\n              <div>\n                <span className=\"rounded-lg inline-flex p-3 bg-green-600 text-white\">\n                  <GlobeAltIcon className=\"h-6 w-6\" />\n                </span>\n              </div>\n              <div className=\"mt-8\">\n                <h3 className=\"text-lg font-medium text-white\">\n                  <span className=\"absolute inset-0\" />\n                  Envoy Config\n                </h3>\n                <p className=\"mt-2 text-sm text-gray-400\">\n                  Manage Envoy configurations\n                </p>\n              </div>\n            </button>\n\n            <button className=\"relative group bg-gray-700 p-3 focus:outline-none focus:ring-2 focus:ring-cyan-500 rounded-lg hover:bg-gray-600 transition-colors\">\n              <div>\n                <span className=\"rounded-lg inline-flex p-3 bg-orange-600 text-white\">\n                  <ShieldCheckIcon className=\"h-6 w-6\" />\n                </span>\n              </div>\n              <div className=\"mt-8\">\n                <h3 className=\"text-lg font-medium text-white\">\n                  <span className=\"absolute inset-0\" />\n                  Audit Logs\n                </h3>\n                <p className=\"mt-2 text-sm text-gray-400\">\n                  View tamper-proof audit trail\n                </p>\n              </div>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* System Info */}\n      <div className=\"bg-gray-800 shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <h3 className=\"text-lg leading-6 font-medium text-white\">System Information</h3>\n          <dl className=\"mt-5 grid grid-cols-1 gap-5 sm:grid-cols-3\">\n            <div>\n              <dt className=\"text-sm font-medium text-gray-400\">Version</dt>\n              <dd className=\"mt-1 text-sm text-white\">{health?.version}</dd>\n            </div>\n            <div>\n              <dt className=\"text-sm font-medium text-gray-400\">Mode</dt>\n              <dd className=\"mt-1 text-sm text-white\">{health?.mode}</dd>\n            </div>\n            <div>\n              <dt className=\"text-sm font-medium text-gray-400\">Services</dt>\n              <dd className=\"mt-1 text-sm text-white\">\n                {health?.services ? Object.keys(health.services).length : 0} active\n              </dd>\n            </div>\n          </dl>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAEEC,OAAO,CACPC,mBAAmB,CACnBC,YAAY,CACZC,mBAAmB,CACnBC,eAAe,CACfC,eAAe,KACV,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBASrC,KAAM,CAAAC,YAAiC,CAAG,CACxCC,KAAK,CAAEP,eAAe,CACtBQ,UAAU,CAAET,mBAAmB,CAC/BU,QAAQ,CAAER,eAAe,CACzBS,SAAS,CAAEb,mBAAmB,CAC9Bc,mBAAmB,CAAEb,YAAY,CACjCc,QAAQ,CAAEhB,OACZ,CAAC,CAED,MAAO,SAAS,CAAAiB,SAASA,CAAA,CAAG,CAC1B,KAAM,CAACC,MAAM,CAAEC,SAAS,CAAC,CAAGpB,QAAQ,CAAsB,IAAI,CAAC,CAC/D,KAAM,CAACqB,OAAO,CAAEC,UAAU,CAAC,CAAGtB,QAAQ,CAAC,IAAI,CAAC,CAE5CD,SAAS,CAAC,IAAM,CACd,KAAM,CAAAwB,WAAW,CAAG,KAAAA,CAAA,GAAY,CAC9B,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,SAAS,CAAC,CACvC,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAAC,CAAC,CAClCP,SAAS,CAACM,IAAI,CAAC,CACjB,CAAE,MAAOE,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACxD,CAAC,OAAS,CACRN,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAEDC,WAAW,CAAC,CAAC,CACb,KAAM,CAAAO,QAAQ,CAAGC,WAAW,CAACR,WAAW,CAAE,KAAK,CAAC,CAAE;AAElD,MAAO,IAAMS,aAAa,CAACF,QAAQ,CAAC,CACtC,CAAC,CAAE,EAAE,CAAC,CAEN,GAAIT,OAAO,CAAE,CACX,mBACEb,IAAA,QAAKyB,SAAS,CAAC,uCAAuC,CAAAC,QAAA,cACpD1B,IAAA,QAAKyB,SAAS,CAAC,gEAAgE,CAAM,CAAC,CACnF,CAAC,CAEV,CAEA,mBACEvB,KAAA,QAAKuB,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExBxB,KAAA,QAAAwB,QAAA,eACE1B,IAAA,OAAIyB,SAAS,CAAC,+BAA+B,CAAAC,QAAA,CAAC,gCAA8B,CAAI,CAAC,cACjF1B,IAAA,MAAGyB,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,gDAElC,CAAG,CAAC,EACD,CAAC,cAGN1B,IAAA,QAAKyB,SAAS,CAAC,+CAA+C,CAAAC,QAAA,cAC5D1B,IAAA,QAAKyB,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BxB,KAAA,QAAKuB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC1B,IAAA,QAAKyB,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5B1B,IAAA,QAAKyB,SAAS,0DAAAE,MAAA,CACZ,CAAAhB,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEiB,MAAM,IAAK,SAAS,CAAG,cAAc,CAAG,YAAY,CAC3D,CAAAF,QAAA,cACD1B,IAAA,QAAKyB,SAAS,yBAAAE,MAAA,CACZ,CAAAhB,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEiB,MAAM,IAAK,SAAS,CAAG,cAAc,CAAG,YAAY,CAC3D,CAAM,CAAC,CACP,CAAC,CACH,CAAC,cACN5B,IAAA,QAAKyB,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9BxB,KAAA,OAAAwB,QAAA,eACE1B,IAAA,OAAIyB,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CAAC,eAAa,CAAI,CAAC,cAC7ExB,KAAA,OAAIuB,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eACjC1B,IAAA,QAAKyB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC/C,CAAAf,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEiB,MAAM,IAAK,SAAS,CAAG,yBAAyB,CAAG,wBAAwB,CACjF,CAAC,cACN1B,KAAA,QAAKuB,SAAS,CAAC,+DAA+D,CAAAC,QAAA,EAC3Ef,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEkB,IAAI,CAAC,OAChB,EAAK,CAAC,EACJ,CAAC,EACH,CAAC,CACF,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,cAGN7B,IAAA,QAAKyB,SAAS,CAAC,sDAAsD,CAAAC,QAAA,CAClE,CAAAf,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEmB,QAAQ,GAAIC,MAAM,CAACC,OAAO,CAACrB,MAAM,CAACmB,QAAQ,CAAC,CAACG,GAAG,CAACC,IAAA,EAA2B,IAA1B,CAACC,WAAW,CAAEP,MAAM,CAAC,CAAAM,IAAA,CAC7E,KAAM,CAAAE,aAAa,CAAGjC,YAAY,CAACgC,WAAW,CAAC,EAAI1C,OAAO,CAC1D,KAAM,CAAA4C,QAAQ,CAAGT,MAAM,GAAK,QAAQ,CAEpC,mBACE5B,IAAA,QAAuByB,SAAS,CAAC,+CAA+C,CAAAC,QAAA,cAC9E1B,IAAA,QAAKyB,SAAS,CAAC,KAAK,CAAAC,QAAA,cAClBxB,KAAA,QAAKuB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC1B,IAAA,QAAKyB,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5B1B,IAAA,CAACoC,aAAa,EAACX,SAAS,YAAAE,MAAA,CAAaU,QAAQ,CAAG,gBAAgB,CAAG,cAAc,CAAG,CAAE,CAAC,CACpF,CAAC,cACNrC,IAAA,QAAKyB,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9BxB,KAAA,OAAAwB,QAAA,eACE1B,IAAA,OAAIyB,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CACvDS,WAAW,CAACG,OAAO,CAAC,IAAI,CAAE,GAAG,CAAC,CAACA,OAAO,CAAC,OAAO,CAAEC,CAAC,EAAIA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,CACpE,CAAC,cACLxC,IAAA,OAAIyB,SAAS,CAAC,gCAAgC,CAAAC,QAAA,cAC5C1B,IAAA,SAAMyB,SAAS,4EAAAE,MAAA,CACbU,QAAQ,CACJ,6BAA6B,CAC7B,yBAAyB,CAC5B,CAAAX,QAAA,CACAE,MAAM,CACH,CAAC,CACL,CAAC,EACH,CAAC,CACF,CAAC,EACH,CAAC,CACH,CAAC,EAvBEO,WAwBL,CAAC,CAEV,CAAC,CAAC,CACC,CAAC,cAGNnC,IAAA,QAAKyB,SAAS,CAAC,+BAA+B,CAAAC,QAAA,cAC5CxB,KAAA,QAAKuB,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/B1B,IAAA,OAAIyB,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,eAAa,CAAI,CAAC,cAC3ExB,KAAA,QAAKuB,SAAS,CAAC,2DAA2D,CAAAC,QAAA,eACxExB,KAAA,WAAQuB,SAAS,CAAC,mIAAmI,CAAAC,QAAA,eACnJ1B,IAAA,QAAA0B,QAAA,cACE1B,IAAA,SAAMyB,SAAS,CAAC,mDAAmD,CAAAC,QAAA,cACjE1B,IAAA,CAACN,mBAAmB,EAAC+B,SAAS,CAAC,SAAS,CAAE,CAAC,CACvC,CAAC,CACJ,CAAC,cACNvB,KAAA,QAAKuB,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBxB,KAAA,OAAIuB,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC5C1B,IAAA,SAAMyB,SAAS,CAAC,kBAAkB,CAAE,CAAC,qBAEvC,EAAI,CAAC,cACLzB,IAAA,MAAGyB,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,mCAE1C,CAAG,CAAC,EACD,CAAC,EACA,CAAC,cAETxB,KAAA,WAAQuB,SAAS,CAAC,mIAAmI,CAAAC,QAAA,eACnJ1B,IAAA,QAAA0B,QAAA,cACE1B,IAAA,SAAMyB,SAAS,CAAC,qDAAqD,CAAAC,QAAA,cACnE1B,IAAA,CAACP,OAAO,EAACgC,SAAS,CAAC,SAAS,CAAE,CAAC,CAC3B,CAAC,CACJ,CAAC,cACNvB,KAAA,QAAKuB,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBxB,KAAA,OAAIuB,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC5C1B,IAAA,SAAMyB,SAAS,CAAC,kBAAkB,CAAE,CAAC,eAEvC,EAAI,CAAC,cACLzB,IAAA,MAAGyB,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,8BAE1C,CAAG,CAAC,EACD,CAAC,EACA,CAAC,cAETxB,KAAA,WAAQuB,SAAS,CAAC,mIAAmI,CAAAC,QAAA,eACnJ1B,IAAA,QAAA0B,QAAA,cACE1B,IAAA,SAAMyB,SAAS,CAAC,oDAAoD,CAAAC,QAAA,cAClE1B,IAAA,CAACL,YAAY,EAAC8B,SAAS,CAAC,SAAS,CAAE,CAAC,CAChC,CAAC,CACJ,CAAC,cACNvB,KAAA,QAAKuB,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBxB,KAAA,OAAIuB,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC5C1B,IAAA,SAAMyB,SAAS,CAAC,kBAAkB,CAAE,CAAC,eAEvC,EAAI,CAAC,cACLzB,IAAA,MAAGyB,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,6BAE1C,CAAG,CAAC,EACD,CAAC,EACA,CAAC,cAETxB,KAAA,WAAQuB,SAAS,CAAC,mIAAmI,CAAAC,QAAA,eACnJ1B,IAAA,QAAA0B,QAAA,cACE1B,IAAA,SAAMyB,SAAS,CAAC,qDAAqD,CAAAC,QAAA,cACnE1B,IAAA,CAACH,eAAe,EAAC4B,SAAS,CAAC,SAAS,CAAE,CAAC,CACnC,CAAC,CACJ,CAAC,cACNvB,KAAA,QAAKuB,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBxB,KAAA,OAAIuB,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC5C1B,IAAA,SAAMyB,SAAS,CAAC,kBAAkB,CAAE,CAAC,aAEvC,EAAI,CAAC,cACLzB,IAAA,MAAGyB,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,+BAE1C,CAAG,CAAC,EACD,CAAC,EACA,CAAC,EACN,CAAC,EACH,CAAC,CACH,CAAC,cAGN1B,IAAA,QAAKyB,SAAS,CAAC,+BAA+B,CAAAC,QAAA,cAC5CxB,KAAA,QAAKuB,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/B1B,IAAA,OAAIyB,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,oBAAkB,CAAI,CAAC,cAChFxB,KAAA,OAAIuB,SAAS,CAAC,4CAA4C,CAAAC,QAAA,eACxDxB,KAAA,QAAAwB,QAAA,eACE1B,IAAA,OAAIyB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,SAAO,CAAI,CAAC,cAC9D1B,IAAA,OAAIyB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAEf,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAE8B,OAAO,CAAK,CAAC,EAC3D,CAAC,cACNvC,KAAA,QAAAwB,QAAA,eACE1B,IAAA,OAAIyB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,MAAI,CAAI,CAAC,cAC3D1B,IAAA,OAAIyB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAEf,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEkB,IAAI,CAAK,CAAC,EACxD,CAAC,cACN3B,KAAA,QAAAwB,QAAA,eACE1B,IAAA,OAAIyB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,UAAQ,CAAI,CAAC,cAC/DxB,KAAA,OAAIuB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,EACpCf,MAAM,SAANA,MAAM,WAANA,MAAM,CAAEmB,QAAQ,CAAGC,MAAM,CAACW,IAAI,CAAC/B,MAAM,CAACmB,QAAQ,CAAC,CAACa,MAAM,CAAG,CAAC,CAAC,SAC9D,EAAI,CAAC,EACF,CAAC,EACJ,CAAC,EACF,CAAC,CACH,CAAC,EACH,CAAC,CAEV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}