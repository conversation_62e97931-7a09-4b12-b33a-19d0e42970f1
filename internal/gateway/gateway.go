package gateway

import (
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/recover"
	"go.uber.org/zap"

	"github.com/cainuro/orchestrator/internal/config"
)

// Gateway represents the main gateway server
type Gateway struct {
	config   *config.Config
	logger   *zap.Logger
	fiberApp *fiber.App
}

// New creates a new gateway instance
func New(cfg *config.Config, logger *zap.Logger) (*Gateway, error) {
	// Create Fiber app with configuration
	app := fiber.New(fiber.Config{
		AppName:      "CAINuro Orchestrator",
		ServerHeader: "CAINuro/1.0",
		ErrorHandler: func(c *fiber.Ctx, err error) error {
			logger.Error("Request error", zap.Error(err))
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"error": "Internal server error",
			})
		},
	})

	// Add middleware
	app.Use(recover.New())
	app.Use(cors.New(cors.Config{
		AllowOrigins: "*",
		AllowMethods: "GET,POST,PUT,DELETE,OPTIONS",
		AllowHeaders: "Origin,Content-Type,Accept,Authorization",
	}))

	return &Gateway{
		config:   cfg,
		logger:   logger,
		fiberApp: app,
	}, nil
}

// SetupRoutes configures the core API routes
func (g *Gateway) SetupRoutes() {
	// Health check endpoint (must be before wildcard routes)
	g.fiberApp.Get("/health", g.healthCheck)

	// API v1 group
	v1 := g.fiberApp.Group("/v1")

	// Autoscaler endpoints
	v1.Get("/autoscaler/status", g.getAutoscalerStatus)
	v1.Post("/autoscaler/config", g.updateAutoscalerConfig)

	// Discovery endpoints
	v1.Post("/discovery/search", g.searchResources)
	v1.Get("/discovery/resources/:id", g.getResource)

	// Workflow endpoints
	v1.Get("/workflows", g.listWorkflows)
	v1.Post("/workflows/execute", g.executeWorkflow)
	v1.Get("/workflows/:id/status", g.getWorkflowStatus)

	// Envoy Control Plane endpoints
	v1.Post("/envoy/configs", g.createEnvoyConfig)
	v1.Get("/envoy/configs", g.listEnvoyConfigs)
	v1.Get("/envoy/nodes", g.listEnvoyNodes)

	// Database Admin endpoints
	v1.Get("/db/stats", g.getDatabaseStats)
	v1.Post("/db/query", g.executeQuery)

	// Audit endpoints
	v1.Get("/audit/events", g.queryAuditEvents)

	// Serve static files from React build (but not for API routes)
	g.fiberApp.Static("/static", "./frontend/orchestrator/build/static")
	g.fiberApp.Get("/favicon.ico", func(c *fiber.Ctx) error {
		return c.SendFile("./frontend/orchestrator/build/favicon.ico")
	})
	g.fiberApp.Get("/manifest.json", func(c *fiber.Ctx) error {
		return c.SendFile("./frontend/orchestrator/build/manifest.json")
	})

	// Fallback to index.html for SPA routing (only for non-API routes)
	g.fiberApp.Get("/*", func(c *fiber.Ctx) error {
		return c.SendFile("./frontend/orchestrator/build/index.html")
	})

	g.logger.Info("Routes configured")
}

// Start starts the gateway server
func (g *Gateway) Start(port int) error {
	// Setup graceful shutdown
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)

	go func() {
		<-c
		g.logger.Info("Shutting down server...")
		g.fiberApp.Shutdown()
	}()

	// Start server
	addr := fmt.Sprintf(":%d", port)
	g.logger.Info("Starting server", zap.String("address", addr))
	return g.fiberApp.Listen(addr)
}

// Health check handler
func (g *Gateway) healthCheck(c *fiber.Ctx) error {
	return c.JSON(fiber.Map{
		"status":  "healthy",
		"version": "1.0.0",
		"time":    time.Now().UTC(),
	})
}

// Mock handlers for now - these will be replaced with actual service calls
func (g *Gateway) getAutoscalerStatus(c *fiber.Ctx) error {
	return c.JSON(fiber.Map{
		"enabled":                 true,
		"current_replicas":        3,
		"desired_replicas":        3,
		"min_replicas":            1,
		"max_replicas":            10,
		"target_cpu_utilization":  70,
		"current_cpu_utilization": 45,
		"last_scale_time":         time.Now().Add(-2 * time.Hour),
		"status":                  "stable",
	})
}

func (g *Gateway) updateAutoscalerConfig(c *fiber.Ctx) error {
	var config map[string]interface{}
	if err := c.BodyParser(&config); err != nil {
		return c.Status(400).JSON(fiber.Map{"error": "Invalid request body"})
	}

	g.logger.Info("Updating autoscaler config", zap.Any("config", config))

	return c.JSON(fiber.Map{
		"message": "Autoscaler configuration updated successfully",
		"config":  config,
	})
}

func (g *Gateway) searchResources(c *fiber.Ctx) error {
	var req map[string]interface{}
	if err := c.BodyParser(&req); err != nil {
		return c.Status(400).JSON(fiber.Map{"error": "Invalid request body"})
	}

	// Mock response
	return c.JSON(fiber.Map{
		"resources": []fiber.Map{
			{
				"provider": "aws",
				"type":     "ec2.instance",
				"name":     "web-server-1",
				"id":       "i-1234567890abcdef0",
				"tags": fiber.Map{
					"Environment": "production",
					"Team":        "backend",
				},
				"region": "us-east-1",
			},
			{
				"provider": "gcp",
				"type":     "compute.instance",
				"name":     "api-server-1",
				"id":       "projects/my-project/zones/us-central1-a/instances/api-server-1",
				"tags": fiber.Map{
					"env":  "prod",
					"role": "api",
				},
				"region": "us-central1",
			},
		},
		"total_count": 2,
	})
}

func (g *Gateway) getResource(c *fiber.Ctx) error {
	resourceID := c.Params("id")

	return c.JSON(fiber.Map{
		"resource": fiber.Map{
			"provider": "aws",
			"type":     "ec2.instance",
			"name":     "web-server-1",
			"id":       resourceID,
			"tags": fiber.Map{
				"Environment": "production",
				"Team":        "backend",
			},
			"region": "us-east-1",
		},
	})
}

func (g *Gateway) listWorkflows(c *fiber.Ctx) error {
	return c.JSON(fiber.Map{
		"workflows": []fiber.Map{
			{
				"id":          "cloud-discovery",
				"name":        "Cloud Resource Discovery",
				"description": "Discover and catalog cloud resources across providers",
				"created_at":  time.Now().Add(-24 * time.Hour),
			},
			{
				"id":          "auto-scaling",
				"name":        "Auto Scaling Workflow",
				"description": "Automatically scale resources based on metrics",
				"created_at":  time.Now().Add(-48 * time.Hour),
			},
		},
	})
}

func (g *Gateway) executeWorkflow(c *fiber.Ctx) error {
	var req map[string]interface{}
	if err := c.BodyParser(&req); err != nil {
		return c.Status(400).JSON(fiber.Map{"error": "Invalid request body"})
	}

	return c.JSON(fiber.Map{
		"execution_id": "exec-" + fmt.Sprintf("%d", time.Now().Unix()),
		"status":       "running",
		"outputs":      fiber.Map{},
	})
}

func (g *Gateway) getWorkflowStatus(c *fiber.Ctx) error {
	workflowID := c.Params("id")

	return c.JSON(fiber.Map{
		"execution_id": "exec-123456",
		"workflow_id":  workflowID,
		"status":       "completed",
		"started_at":   time.Now().Add(-10 * time.Minute),
		"completed_at": time.Now().Add(-2 * time.Minute),
		"outputs": fiber.Map{
			"resources_discovered": 42,
			"providers_scanned":    3,
		},
	})
}

func (g *Gateway) createEnvoyConfig(c *fiber.Ctx) error {
	var req map[string]interface{}
	if err := c.BodyParser(&req); err != nil {
		return c.Status(400).JSON(fiber.Map{"error": "Invalid request body"})
	}

	return c.JSON(fiber.Map{
		"config": fiber.Map{
			"id":           "config-" + fmt.Sprintf("%d", time.Now().Unix()),
			"node_id":      req["node_id"],
			"cluster_name": req["cluster_name"],
			"config":       req["config"],
			"version":      "v1",
			"created_at":   time.Now(),
		},
	})
}

func (g *Gateway) listEnvoyConfigs(c *fiber.Ctx) error {
	return c.JSON(fiber.Map{
		"configs": []fiber.Map{
			{
				"id":           "config-001",
				"node_id":      "envoy-proxy-1",
				"cluster_name": "web-cluster",
				"version":      "v1",
				"created_at":   time.Now().Add(-1 * time.Hour),
			},
		},
	})
}

func (g *Gateway) listEnvoyNodes(c *fiber.Ctx) error {
	return c.JSON(fiber.Map{
		"nodes": []fiber.Map{
			{
				"id":        "envoy-proxy-1",
				"cluster":   "web-cluster",
				"version":   "1.28.0",
				"last_seen": time.Now().Add(-5 * time.Minute),
				"status":    "healthy",
			},
		},
	})
}

func (g *Gateway) getDatabaseStats(c *fiber.Ctx) error {
	return c.JSON(fiber.Map{
		"stats": fiber.Map{
			"total_resources":     1250,
			"total_workflows":     15,
			"total_envoy_configs": 8,
			"database_size":       "45.2 MB",
			"cache_hit_rate":      0.87,
			"cache_size":          "12.1 MB",
		},
	})
}

func (g *Gateway) executeQuery(c *fiber.Ctx) error {
	var req map[string]interface{}
	if err := c.BodyParser(&req); err != nil {
		return c.Status(400).JSON(fiber.Map{"error": "Invalid request body"})
	}

	return c.JSON(fiber.Map{
		"result": fiber.Map{
			"rows": []fiber.Map{
				{"id": "1", "name": "example", "status": "active"},
				{"id": "2", "name": "test", "status": "inactive"},
			},
			"count": 2,
		},
	})
}

func (g *Gateway) queryAuditEvents(c *fiber.Ctx) error {
	return c.JSON(fiber.Map{
		"events": []fiber.Map{
			{
				"id":          "audit-001",
				"timestamp":   time.Now().Add(-1 * time.Hour),
				"user_id":     "system",
				"action":      "autoscaler.update_config",
				"resource":    "autoscaler",
				"resource_id": "default",
				"details": fiber.Map{
					"min_replicas": 1,
					"max_replicas": 10,
				},
				"ip_address": "127.0.0.1",
				"user_agent": "cainuro-orchestrator/1.0",
				"success":    true,
			},
		},
		"total_count": 1,
	})
}
