package dbadmin

import (
	"context"
	"fmt"

	"github.com/cainuro/orchestrator/internal/config"
	"github.com/cainuro/orchestrator/internal/db"
	orchestratorv1 "github.com/cainuro/orchestrator/proto/orchestrator/v1"
	"go.uber.org/zap"
)

// Service defines the database administration service interface
type Service interface {
	GetDBStatus(ctx context.Context, req *orchestratorv1.GetDBStatusRequest) (*orchestratorv1.GetDBStatusResponse, error)
	ExecuteQuery(ctx context.Context, req *orchestratorv1.ExecuteQueryRequest) (*orchestratorv1.ExecuteQueryResponse, error)
}

// service implements the Service interface
type service struct {
	config *config.Config
	logger *zap.Logger
	store  *db.SQLiteStore
}

// NewService creates a new database administration service
func NewService(
	config *config.Config,
	logger *zap.Logger,
	store *db.SQLiteStore,
) (Service, error) {
	return &service{
		config: config,
		logger: logger,
		store:  store,
	}, nil
}

// GetDBStatus returns the current database status
func (s *service) GetDBStatus(ctx context.Context, req *orchestratorv1.GetDBStatusRequest) (*orchestratorv1.GetDBStatusResponse, error) {
	s.logger.Info("Getting database status")

	// Test database connectivity by running a simple query
	result, err := s.store.Query(ctx, "SELECT COUNT(*) as count FROM settings")
	connected := err == nil
	if result != nil {
		result.Close()
	}

	var totalRecords int64 = 0
	if connected {
		// Count total records across all tables
		tables := []string{"settings", "workflows", "workflow_executions", "users", "resource_cache", "envoy_snapshots"}
		for _, table := range tables {
			if result, err := s.store.Query(ctx, fmt.Sprintf("SELECT COUNT(*) as count FROM %s", table)); err == nil {
				var count struct {
					Count int64 `json:"count"`
				}
				if result.Next() {
					result.Scan(&count)
					totalRecords += count.Count
				}
				result.Close()
			}
		}
	}

	status := "healthy"
	if !connected {
		status = "disconnected"
	}

	return &orchestratorv1.GetDBStatusResponse{
		Connected:    connected,
		Version:      "Genji v0.16.0",
		TotalRecords: totalRecords,
		Status:       status,
	}, nil
}

// ExecuteQuery executes a database query
func (s *service) ExecuteQuery(ctx context.Context, req *orchestratorv1.ExecuteQueryRequest) (*orchestratorv1.ExecuteQueryResponse, error) {
	s.logger.Info("Executing database query", zap.String("query", req.Query))

	// For safety, only allow SELECT queries in this demo
	if len(req.Query) < 6 || req.Query[:6] != "SELECT" {
		return &orchestratorv1.ExecuteQueryResponse{
			Results:      []*map[string]string{},
			RowsAffected: 0,
		}, fmt.Errorf("only SELECT queries are allowed")
	}

	result, err := s.store.Query(ctx, req.Query, interfaceSliceToAny(req.Parameters)...)
	if err != nil {
		return nil, fmt.Errorf("failed to execute query: %w", err)
	}
	defer result.Close()

	var results []*map[string]string
	rowCount := int32(0)

	for result.Next() {
		var row map[string]interface{}
		if err := result.Scan(&row); err != nil {
			continue
		}

		// Convert to string map
		stringRow := make(map[string]string)
		for k, v := range row {
			if v != nil {
				stringRow[k] = fmt.Sprintf("%v", v)
			} else {
				stringRow[k] = ""
			}
		}
		results = append(results, &stringRow)
		rowCount++
	}

	return &orchestratorv1.ExecuteQueryResponse{
		Results:      results,
		RowsAffected: rowCount,
	}, nil
}

// Helper function to convert []string to []interface{}
func interfaceSliceToAny(slice []string) []interface{} {
	result := make([]interface{}, len(slice))
	for i, v := range slice {
		result[i] = v
	}
	return result
}
