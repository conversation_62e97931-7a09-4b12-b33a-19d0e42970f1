package db

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	_ "github.com/mattn/go-sqlite3"
)

// Store defines the database interface
type Store interface {
	Query(ctx context.Context, query string, args ...interface{}) (Result, error)
	Insert(ctx context.Context, table string, data interface{}) error
	Update(ctx context.Context, table string, data interface{}, where string, args ...interface{}) error
	Delete(ctx context.Context, table string, where string, args ...interface{}) error
	Close() error
}

// Result defines the query result interface
type Result interface {
	Next() bool
	Scan(dest interface{}) error
	Close() error
}

// CachedResource represents a cached cloud resource
type CachedResource struct {
	ID         int64     `json:"id" db:"id"`
	Provider   string    `json:"provider" db:"provider"`
	Type       string    `json:"type" db:"type"`
	Name       string    `json:"name" db:"name"`
	ResourceID string    `json:"resource_id" db:"resource_id"`
	Region     string    `json:"region" db:"region"`
	Tags       string    `json:"tags" db:"tags"`
	LastSeen   time.Time `json:"last_seen" db:"last_seen"`
}

// WorkflowExecution represents a workflow execution record
type WorkflowExecution struct {
	ID         string     `json:"id" db:"id"`
	WorkflowID string     `json:"workflow_id" db:"workflow_id"`
	Status     string     `json:"status" db:"status"`
	Input      string     `json:"input" db:"input"`
	Output     string     `json:"output" db:"output"`
	StartTime  time.Time  `json:"start_time" db:"start_time"`
	EndTime    *time.Time `json:"end_time" db:"end_time"`
	Error      string     `json:"error" db:"error"`
}

// EnvoyConfig represents an Envoy configuration
type EnvoyConfig struct {
	ID          string    `json:"id" db:"id"`
	NodeID      string    `json:"node_id" db:"node_id"`
	ClusterName string    `json:"cluster_name" db:"cluster_name"`
	Config      string    `json:"config" db:"config"`
	Version     string    `json:"version" db:"version"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

// SQLiteStore implements Store using SQLite database
type SQLiteStore struct {
	db *sql.DB
}

// NewSQLiteStore creates a new SQLite database store
func NewSQLiteStore(dbPath string) (*SQLiteStore, error) {
	db, err := sql.Open("sqlite3", dbPath)
	if err != nil {
		return nil, fmt.Errorf("failed to open SQLite database: %w", err)
	}

	store := &SQLiteStore{db: db}

	// Initialize database schema
	if err := store.initSchema(); err != nil {
		return nil, fmt.Errorf("failed to initialize schema: %w", err)
	}

	return store, nil
}

// initSchema creates the necessary tables
func (s *SQLiteStore) initSchema() error {
	// Create resource_cache table
	_, err := s.db.Exec(`
		CREATE TABLE IF NOT EXISTS resource_cache (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			provider TEXT NOT NULL,
			type TEXT NOT NULL,
			name TEXT NOT NULL,
			resource_id TEXT NOT NULL UNIQUE,
			region TEXT,
			tags TEXT,
			last_seen TIMESTAMP
		)
	`)
	if err != nil {
		return fmt.Errorf("failed to create resource_cache table: %w", err)
	}

	// Create workflow_executions table
	_, err = s.db.Exec(`
		CREATE TABLE IF NOT EXISTS workflow_executions (
			id TEXT PRIMARY KEY,
			workflow_id TEXT NOT NULL,
			status TEXT NOT NULL,
			input TEXT,
			output TEXT,
			start_time TIMESTAMP,
			end_time TIMESTAMP,
			error TEXT
		)
	`)
	if err != nil {
		return fmt.Errorf("failed to create workflow_executions table: %w", err)
	}

	// Create envoy_configs table
	_, err = s.db.Exec(`
		CREATE TABLE IF NOT EXISTS envoy_configs (
			id TEXT PRIMARY KEY,
			node_id TEXT NOT NULL,
			cluster_name TEXT NOT NULL,
			config TEXT NOT NULL,
			version TEXT NOT NULL,
			created_at TIMESTAMP,
			updated_at TIMESTAMP
		)
	`)
	if err != nil {
		return fmt.Errorf("failed to create envoy_configs table: %w", err)
	}

	// Create indexes for better performance
	s.db.Exec("CREATE INDEX IF NOT EXISTS idx_resource_provider ON resource_cache(provider)")
	s.db.Exec("CREATE INDEX IF NOT EXISTS idx_resource_type ON resource_cache(type)")
	s.db.Exec("CREATE INDEX IF NOT EXISTS idx_workflow_status ON workflow_executions(status)")
	s.db.Exec("CREATE INDEX IF NOT EXISTS idx_envoy_node ON envoy_configs(node_id)")

	return nil
}

// Query executes a query and returns results
func (s *SQLiteStore) Query(ctx context.Context, query string, args ...interface{}) (Result, error) {
	rows, err := s.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("query failed: %w", err)
	}
	return &SQLiteResult{rows: rows}, nil
}

// Insert inserts data into a table
func (s *SQLiteStore) Insert(ctx context.Context, table string, data interface{}) error {
	// Convert data to JSON for simple insertion
	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("failed to marshal data: %w", err)
	}

	// For now, use a simple approach - in production, use proper SQL generation
	query := fmt.Sprintf("INSERT INTO %s (data) VALUES (?)", table)
	_, err = s.db.ExecContext(ctx, query, string(jsonData))
	if err != nil {
		return fmt.Errorf("insert failed: %w", err)
	}
	return nil
}

// Update updates data in a table
func (s *SQLiteStore) Update(ctx context.Context, table string, data interface{}, where string, args ...interface{}) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("failed to marshal data: %w", err)
	}

	query := fmt.Sprintf("UPDATE %s SET data = ? WHERE %s", table, where)
	allArgs := append([]interface{}{string(jsonData)}, args...)
	_, err = s.db.ExecContext(ctx, query, allArgs...)
	if err != nil {
		return fmt.Errorf("update failed: %w", err)
	}
	return nil
}

// Delete deletes data from a table
func (s *SQLiteStore) Delete(ctx context.Context, table string, where string, args ...interface{}) error {
	query := fmt.Sprintf("DELETE FROM %s WHERE %s", table, where)
	_, err := s.db.ExecContext(ctx, query, args...)
	if err != nil {
		return fmt.Errorf("delete failed: %w", err)
	}
	return nil
}

// Close closes the database connection
func (s *SQLiteStore) Close() error {
	return s.db.Close()
}

// SQLiteResult implements Result for SQLite query results
type SQLiteResult struct {
	rows *sql.Rows
}

// Next advances to the next result
func (r *SQLiteResult) Next() bool {
	return r.rows.Next()
}

// Scan scans the current result into dest
func (r *SQLiteResult) Scan(dest interface{}) error {
	// For simplicity, we'll scan into a map and then convert
	columns, err := r.rows.Columns()
	if err != nil {
		return err
	}

	values := make([]interface{}, len(columns))
	valuePtrs := make([]interface{}, len(columns))
	for i := range values {
		valuePtrs[i] = &values[i]
	}

	if err := r.rows.Scan(valuePtrs...); err != nil {
		return err
	}

	// Convert to map
	result := make(map[string]interface{})
	for i, col := range columns {
		result[col] = values[i]
	}

	// Convert map to JSON and then to dest
	jsonData, err := json.Marshal(result)
	if err != nil {
		return err
	}

	return json.Unmarshal(jsonData, dest)
}

// Close closes the result
func (r *SQLiteResult) Close() error {
	return r.rows.Close()
}
