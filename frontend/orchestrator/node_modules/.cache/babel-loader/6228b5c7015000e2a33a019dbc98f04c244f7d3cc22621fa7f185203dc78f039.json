{"ast": null, "code": "function curry(fn) {\n  return function curried() {\n    var _this = this;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return args.length >= fn.length ? fn.apply(this, args) : function () {\n      for (var _len2 = arguments.length, nextArgs = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        nextArgs[_key2] = arguments[_key2];\n      }\n      return curried.apply(_this, [].concat(args, nextArgs));\n    };\n  };\n}\nexport default curry;", "map": {"version": 3, "names": ["curry", "fn", "curried", "_this", "_len", "arguments", "length", "args", "Array", "_key", "apply", "_len2", "nextArgs", "_key2", "concat"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/node_modules/@monaco-editor/loader/lib/es/utils/curry.js"], "sourcesContent": ["function curry(fn) {\n  return function curried() {\n    var _this = this;\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    return args.length >= fn.length ? fn.apply(this, args) : function () {\n      for (var _len2 = arguments.length, nextArgs = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        nextArgs[_key2] = arguments[_key2];\n      }\n\n      return curried.apply(_this, [].concat(args, nextArgs));\n    };\n  };\n}\n\nexport default curry;\n"], "mappings": "AAAA,SAASA,KAAKA,CAACC,EAAE,EAAE;EACjB,OAAO,SAASC,OAAOA,CAAA,EAAG;IACxB,IAAIC,KAAK,GAAG,IAAI;IAEhB,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAC9B;IAEA,OAAOF,IAAI,CAACD,MAAM,IAAIL,EAAE,CAACK,MAAM,GAAGL,EAAE,CAACS,KAAK,CAAC,IAAI,EAAEH,IAAI,CAAC,GAAG,YAAY;MACnE,KAAK,IAAII,KAAK,GAAGN,SAAS,CAACC,MAAM,EAAEM,QAAQ,GAAG,IAAIJ,KAAK,CAACG,KAAK,CAAC,EAAEE,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGF,KAAK,EAAEE,KAAK,EAAE,EAAE;QACjGD,QAAQ,CAACC,KAAK,CAAC,GAAGR,SAAS,CAACQ,KAAK,CAAC;MACpC;MAEA,OAAOX,OAAO,CAACQ,KAAK,CAACP,KAAK,EAAE,EAAE,CAACW,MAAM,CAACP,IAAI,EAAEK,QAAQ,CAAC,CAAC;IACxD,CAAC;EACH,CAAC;AACH;AAEA,eAAeZ,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}