{"version": 3, "file": "index.js", "sources": ["../src/util.ts", "../src/index.ts"], "sourcesContent": ["import type { NodePath, types as t } from \"@babel/core\";\n\n/**\n * Check whether a function expression can be affected by\n * https://bugs.webkit.org/show_bug.cgi?id=220517\n * @param path The function expression NodePath\n * @returns the name of function id if it should be transformed, otherwise returns false\n */\nexport function shouldTransform(\n  path: NodePath<t.FunctionExpression>,\n): string | false {\n  const { node } = path;\n  const functionId = node.id;\n  if (!functionId) return false;\n\n  const name = functionId.name;\n  // On collision, `getOwnBinding` returns the param binding\n  // with the id binding be registered as constant violation\n  const paramNameBinding = path.scope.getOwnBinding(name);\n  if (paramNameBinding === undefined) {\n    // Case 1: the function id is injected by babel-helper-name-function, which\n    // assigns `NOT_LOCAL_BINDING` to the `functionId` and thus not registering id\n    // in scope tracking\n    // Case 2: the function id is injected by a third party plugin which does not update the\n    // scope info\n    return false;\n  }\n  if (paramNameBinding.kind !== \"param\") {\n    // the function id does not reproduce in params\n    return false;\n  }\n\n  if (paramNameBinding.identifier === paramNameBinding.path.node) {\n    // the param binding is a simple parameter\n    // e.g. (function a(a) {})\n    return false;\n  }\n\n  return name;\n}\n", "import { declare } from \"@babel/helper-plugin-utils\";\nimport { shouldTransform } from \"./util.ts\";\n\nexport default declare(api => {\n  api.assertVersion(REQUIRED_VERSION(\"^7.16.0\"));\n\n  return {\n    name: \"plugin-bugfix-safari-id-destructuring-collision-in-function-expression\",\n\n    visitor: {\n      FunctionExpression(path) {\n        const name = shouldTransform(path);\n        if (name) {\n          // Now we have (function a([a]) {})\n          const { scope } = path;\n          // invariant: path.node.id is always an Identifier here\n          const newParamName = scope.generateUid(name);\n          scope.rename(name, newParamName);\n        }\n      },\n    },\n  };\n});\n"], "names": ["shouldTransform", "path", "node", "functionId", "id", "name", "paramNameBinding", "scope", "getOwnBinding", "undefined", "kind", "identifier", "declare", "api", "assertVersion", "visitor", "FunctionExpression", "newParamName", "generateUid", "rename"], "mappings": ";;;;;;AAQO,SAASA,eAAeA,CAC7BC,IAAoC,EACpB;EAChB,MAAM;AAAEC,IAAAA,IAAAA;AAAK,GAAC,GAAGD,IAAI,CAAA;AACrB,EAAA,MAAME,UAAU,GAAGD,IAAI,CAACE,EAAE,CAAA;AAC1B,EAAA,IAAI,CAACD,UAAU,EAAE,OAAO,KAAK,CAAA;AAE7B,EAAA,MAAME,IAAI,GAAGF,UAAU,CAACE,IAAI,CAAA;EAG5B,MAAMC,gBAAgB,GAAGL,IAAI,CAACM,KAAK,CAACC,aAAa,CAACH,IAAI,CAAC,CAAA;EACvD,IAAIC,gBAAgB,KAAKG,SAAS,EAAE;AAMlC,IAAA,OAAO,KAAK,CAAA;AACd,GAAA;AACA,EAAA,IAAIH,gBAAgB,CAACI,IAAI,KAAK,OAAO,EAAE;AAErC,IAAA,OAAO,KAAK,CAAA;AACd,GAAA;EAEA,IAAIJ,gBAAgB,CAACK,UAAU,KAAKL,gBAAgB,CAACL,IAAI,CAACC,IAAI,EAAE;AAG9D,IAAA,OAAO,KAAK,CAAA;AACd,GAAA;AAEA,EAAA,OAAOG,IAAI,CAAA;AACb;;ACpCA,YAAeO,yBAAO,CAACC,GAAG,IAAI;AAC5BA,EAAAA,GAAG,CAACC,aAAa,CAAkB,SAAU,CAAC,CAAA;EAE9C,OAAO;AACLT,IAAAA,IAAI,EAAE,wEAAwE;AAE9EU,IAAAA,OAAO,EAAE;MACPC,kBAAkBA,CAACf,IAAI,EAAE;AACvB,QAAA,MAAMI,IAAI,GAAGL,eAAe,CAACC,IAAI,CAAC,CAAA;AAClC,QAAA,IAAII,IAAI,EAAE;UAER,MAAM;AAAEE,YAAAA,KAAAA;AAAM,WAAC,GAAGN,IAAI,CAAA;AAEtB,UAAA,MAAMgB,YAAY,GAAGV,KAAK,CAACW,WAAW,CAACb,IAAI,CAAC,CAAA;AAC5CE,UAAAA,KAAK,CAACY,MAAM,CAACd,IAAI,EAAEY,YAAY,CAAC,CAAA;AAClC,SAAA;AACF,OAAA;AACF,KAAA;GACD,CAAA;AACH,CAAC,CAAC;;;;"}