package workflow

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/cainuro/orchestrator/internal/cache"
	"github.com/cainuro/orchestrator/internal/config"
	"github.com/cainuro/orchestrator/internal/db"
	orchestratorv1 "github.com/cainuro/orchestrator/proto/orchestrator/v1"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// Service defines the workflow service interface
type Service interface {
	ExecuteWorkflow(ctx context.Context, req *orchestratorv1.ExecuteWorkflowRequest) (*orchestratorv1.ExecuteWorkflowResponse, error)
	GetWorkflowStatus(ctx context.Context, req *orchestratorv1.GetWorkflowStatusRequest) (*orchestratorv1.GetWorkflowStatusResponse, error)
	ListWorkflows(ctx context.Context, req *orchestratorv1.ListWorkflowsRequest) (*orchestratorv1.ListWorkflowsResponse, error)
	LoadWorkflowDefinitions() error
}

// service implements the Service interface
type service struct {
	config *config.Config
	logger *zap.Logger
	store  *db.SQLiteStore
	cache  *cache.RistrettoCache

	// Workflow definitions loaded from YAML files
	workflows map[string]*WorkflowDefinition
}

// WorkflowDefinition represents a workflow definition from YAML
type WorkflowDefinition struct {
	ID          string                 `yaml:"id"`
	Name        string                 `yaml:"name"`
	Description string                 `yaml:"description"`
	Steps       []WorkflowStep         `yaml:"steps"`
	Inputs      map[string]interface{} `yaml:"inputs"`
	Outputs     map[string]interface{} `yaml:"outputs"`
}

// WorkflowStep represents a single step in a workflow
type WorkflowStep struct {
	ID        string                 `yaml:"id"`
	Type      string                 `yaml:"type"`
	With      map[string]interface{} `yaml:"with"`
	DependsOn []string               `yaml:"depends_on"`
}

// WorkflowExecution represents a running workflow execution
type WorkflowExecution struct {
	ID           string                 `json:"id"`
	WorkflowID   string                 `json:"workflow_id"`
	Status       string                 `json:"status"`
	Inputs       map[string]interface{} `json:"inputs"`
	Outputs      map[string]interface{} `json:"outputs"`
	StartedAt    time.Time              `json:"started_at"`
	CompletedAt  *time.Time             `json:"completed_at,omitempty"`
	ErrorMessage string                 `json:"error_message,omitempty"`
	Steps        []StepExecution        `json:"steps"`
}

// StepExecution represents the execution state of a workflow step
type StepExecution struct {
	ID           string                 `json:"id"`
	Status       string                 `json:"status"`
	StartedAt    time.Time              `json:"started_at"`
	CompletedAt  *time.Time             `json:"completed_at,omitempty"`
	Outputs      map[string]interface{} `json:"outputs"`
	ErrorMessage string                 `json:"error_message,omitempty"`
}

// NewService creates a new workflow service
func NewService(
	config *config.Config,
	logger *zap.Logger,
	store *db.SQLiteStore,
	cache *cache.RistrettoCache,
) (Service, error) {
	s := &service{
		config:    config,
		logger:    logger,
		store:     store,
		cache:     cache,
		workflows: make(map[string]*WorkflowDefinition),
	}

	// Load workflow definitions
	if err := s.LoadWorkflowDefinitions(); err != nil {
		return nil, fmt.Errorf("failed to load workflow definitions: %w", err)
	}

	return s, nil
}

// ExecuteWorkflow starts a new workflow execution
func (s *service) ExecuteWorkflow(ctx context.Context, req *orchestratorv1.ExecuteWorkflowRequest) (*orchestratorv1.ExecuteWorkflowResponse, error) {
	s.logger.Info("Executing workflow",
		zap.String("workflow_id", req.WorkflowId),
		zap.Any("inputs", req.Inputs))

	// Check if workflow exists
	workflow, exists := s.workflows[req.WorkflowId]
	if !exists {
		return nil, fmt.Errorf("workflow not found: %s", req.WorkflowId)
	}

	// Create execution
	// Convert inputs to interface{} map
	inputs := make(map[string]interface{})
	for k, v := range req.Inputs {
		inputs[k] = v
	}

	execution := &WorkflowExecution{
		ID:         generateExecutionID(),
		WorkflowID: req.WorkflowId,
		Status:     "running",
		Inputs:     inputs,
		Outputs:    make(map[string]interface{}),
		StartedAt:  time.Now().UTC(),
		Steps:      make([]StepExecution, 0, len(workflow.Steps)),
	}

	// Save execution to database
	if err := s.saveExecution(ctx, execution); err != nil {
		return nil, fmt.Errorf("failed to save execution: %w", err)
	}

	// Start execution asynchronously
	go s.executeWorkflowAsync(context.Background(), execution, workflow)

	return &orchestratorv1.ExecuteWorkflowResponse{
		ExecutionId: execution.ID,
		Status:      execution.Status,
	}, nil
}

// GetWorkflowStatus returns the status of a workflow execution
func (s *service) GetWorkflowStatus(ctx context.Context, req *orchestratorv1.GetWorkflowStatusRequest) (*orchestratorv1.GetWorkflowStatusResponse, error) {
	s.logger.Info("Getting workflow status", zap.String("workflow_id", req.WorkflowId))

	// Check cache first
	cacheKey := fmt.Sprintf("workflow_status:%s", req.WorkflowId)
	var cachedResponse orchestratorv1.GetWorkflowStatusResponse
	if found, err := s.cache.Get(ctx, cacheKey, &cachedResponse); err == nil && found {
		return &cachedResponse, nil
	}

	// Query database for latest execution
	result, err := s.store.Query(ctx, `
		SELECT * FROM workflow_executions 
		WHERE workflow_id = ? 
		ORDER BY started_at DESC 
		LIMIT 1
	`, req.WorkflowId)
	if err != nil {
		return nil, fmt.Errorf("failed to query workflow execution: %w", err)
	}
	defer result.Close()

	var execution WorkflowExecution
	if result.Next() {
		if err := result.Scan(&execution); err != nil {
			return nil, fmt.Errorf("failed to scan execution: %w", err)
		}
	} else {
		return nil, fmt.Errorf("no executions found for workflow: %s", req.WorkflowId)
	}

	response := &orchestratorv1.GetWorkflowStatusResponse{
		WorkflowId: execution.WorkflowID,
		Status:     execution.Status,
		StartedAt:  timestampFromTime(execution.StartedAt),
		Outputs:    convertMapToStringMap(execution.Outputs),
	}

	if execution.CompletedAt != nil {
		response.CompletedAt = timestampFromTime(*execution.CompletedAt)
	}

	// Cache for 30 seconds
	s.cache.SetWithTTL(ctx, cacheKey, response, 30*time.Second)

	return response, nil
}

// ListWorkflows returns a list of available workflows
func (s *service) ListWorkflows(ctx context.Context, req *orchestratorv1.ListWorkflowsRequest) (*orchestratorv1.ListWorkflowsResponse, error) {
	s.logger.Info("Listing workflows")

	var workflows []*orchestratorv1.WorkflowInfo
	for _, workflow := range s.workflows {
		workflows = append(workflows, &orchestratorv1.WorkflowInfo{
			Id:          workflow.ID,
			Name:        workflow.Name,
			Description: workflow.Description,
			CreatedAt:   timestampFromTime(time.Now()), // TODO: Get actual creation time
		})
	}

	return &orchestratorv1.ListWorkflowsResponse{
		Workflows: workflows,
	}, nil
}

// LoadWorkflowDefinitions loads workflow definitions from YAML files
func (s *service) LoadWorkflowDefinitions() error {
	// Load cloud-search.yaml
	cloudSearchWorkflow := &WorkflowDefinition{
		ID:          "cloud-search",
		Name:        "Cloud Resource Search",
		Description: "Search for cloud resources across providers",
		Steps: []WorkflowStep{
			{
				ID:   "fetch",
				Type: "discovery.search",
				With: map[string]interface{}{
					"provider":  "{{inputs.provider}}",
					"tag_query": "{{inputs.tag_query}}",
				},
			},
			{
				ID:        "export",
				Type:      "workflow.export.csv",
				DependsOn: []string{"fetch"},
			},
		},
	}

	// Load k8s-discover.yaml
	k8sDiscoverWorkflow := &WorkflowDefinition{
		ID:          "k8s-discover",
		Name:        "Kubernetes Discovery",
		Description: "Discover Kubernetes clusters, pods, and services",
		Steps: []WorkflowStep{
			{
				ID:   "list_clusters",
				Type: "k8s.list_clusters",
			},
			{
				ID:        "list_pods",
				Type:      "k8s.list_pods",
				DependsOn: []string{"list_clusters"},
			},
			{
				ID:        "list_services",
				Type:      "k8s.list_services",
				DependsOn: []string{"list_clusters"},
			},
		},
	}

	s.workflows[cloudSearchWorkflow.ID] = cloudSearchWorkflow
	s.workflows[k8sDiscoverWorkflow.ID] = k8sDiscoverWorkflow

	s.logger.Info("Loaded workflow definitions", zap.Int("count", len(s.workflows)))
	return nil
}

// executeWorkflowAsync executes a workflow asynchronously
func (s *service) executeWorkflowAsync(ctx context.Context, execution *WorkflowExecution, workflow *WorkflowDefinition) {
	defer func() {
		if r := recover(); r != nil {
			s.logger.Error("Workflow execution panicked", zap.Any("panic", r))
			execution.Status = "failed"
			execution.ErrorMessage = fmt.Sprintf("Panic: %v", r)
			now := time.Now().UTC()
			execution.CompletedAt = &now
			s.saveExecution(ctx, execution)
		}
	}()

	s.logger.Info("Starting workflow execution", zap.String("execution_id", execution.ID))

	// Execute steps in order
	for _, step := range workflow.Steps {
		stepExecution := StepExecution{
			ID:        step.ID,
			Status:    "running",
			StartedAt: time.Now().UTC(),
			Outputs:   make(map[string]interface{}),
		}

		execution.Steps = append(execution.Steps, stepExecution)

		// Execute the step
		if err := s.executeStep(ctx, &stepExecution, &step, execution); err != nil {
			stepExecution.Status = "failed"
			stepExecution.ErrorMessage = err.Error()
			execution.Status = "failed"
			execution.ErrorMessage = fmt.Sprintf("Step %s failed: %v", step.ID, err)
			now := time.Now().UTC()
			stepExecution.CompletedAt = &now
			execution.CompletedAt = &now
			s.saveExecution(ctx, execution)
			return
		}

		stepExecution.Status = "completed"
		now := time.Now().UTC()
		stepExecution.CompletedAt = &now
	}

	// Mark execution as completed
	execution.Status = "completed"
	now := time.Now().UTC()
	execution.CompletedAt = &now

	s.saveExecution(ctx, execution)
	s.logger.Info("Workflow execution completed", zap.String("execution_id", execution.ID))
}

// executeStep executes a single workflow step
func (s *service) executeStep(ctx context.Context, stepExecution *StepExecution, step *WorkflowStep, execution *WorkflowExecution) error {
	s.logger.Info("Executing step", zap.String("step_id", step.ID), zap.String("type", step.Type))

	switch step.Type {
	case "discovery.search":
		return s.executeDiscoverySearch(ctx, stepExecution, step, execution)
	case "workflow.export.csv":
		return s.executeExportCSV(ctx, stepExecution, step, execution)
	case "k8s.list_clusters":
		return s.executeK8sListClusters(ctx, stepExecution, step, execution)
	case "k8s.list_pods":
		return s.executeK8sListPods(ctx, stepExecution, step, execution)
	case "k8s.list_services":
		return s.executeK8sListServices(ctx, stepExecution, step, execution)
	default:
		return fmt.Errorf("unknown step type: %s", step.Type)
	}
}

// Step execution implementations
func (s *service) executeDiscoverySearch(ctx context.Context, stepExecution *StepExecution, step *WorkflowStep, execution *WorkflowExecution) error {
	// Mock implementation - would integrate with search service
	stepExecution.Outputs["resources_found"] = 42
	stepExecution.Outputs["provider"] = step.With["provider"]
	return nil
}

func (s *service) executeExportCSV(ctx context.Context, stepExecution *StepExecution, step *WorkflowStep, execution *WorkflowExecution) error {
	// Mock implementation - would export to CSV
	stepExecution.Outputs["csv_file"] = "/tmp/export.csv"
	stepExecution.Outputs["rows_exported"] = 42
	return nil
}

func (s *service) executeK8sListClusters(ctx context.Context, stepExecution *StepExecution, step *WorkflowStep, execution *WorkflowExecution) error {
	// Mock implementation - would list K8s clusters
	stepExecution.Outputs["clusters"] = []string{"cluster-1", "cluster-2"}
	return nil
}

func (s *service) executeK8sListPods(ctx context.Context, stepExecution *StepExecution, step *WorkflowStep, execution *WorkflowExecution) error {
	// Mock implementation - would list K8s pods
	stepExecution.Outputs["pods_count"] = 156
	return nil
}

func (s *service) executeK8sListServices(ctx context.Context, stepExecution *StepExecution, step *WorkflowStep, execution *WorkflowExecution) error {
	// Mock implementation - would list K8s services
	stepExecution.Outputs["services_count"] = 23
	return nil
}

// Helper functions
func (s *service) saveExecution(ctx context.Context, execution *WorkflowExecution) error {
	data, err := json.Marshal(execution)
	if err != nil {
		return err
	}

	return s.store.Exec(ctx, `
		INSERT INTO workflow_executions (id, workflow_id, status, inputs, outputs, started_at, completed_at, error_message)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?)
		ON CONFLICT (id) DO UPDATE SET
			status = ?, outputs = ?, completed_at = ?, error_message = ?
	`, execution.ID, execution.WorkflowID, execution.Status,
		string(data), string(data), execution.StartedAt.Format(time.RFC3339),
		formatTimePtr(execution.CompletedAt), execution.ErrorMessage,
		execution.Status, string(data), formatTimePtr(execution.CompletedAt), execution.ErrorMessage)
}

func generateExecutionID() string {
	return fmt.Sprintf("exec_%d", time.Now().UnixNano())
}

func timestampFromTime(t time.Time) *timestamppb.Timestamp {
	return timestamppb.New(t)
}

func convertMapToStringMap(m map[string]interface{}) map[string]string {
	result := make(map[string]string)
	for k, v := range m {
		result[k] = fmt.Sprintf("%v", v)
	}
	return result
}

func formatTimePtr(t *time.Time) string {
	if t == nil {
		return ""
	}
	return t.Format(time.RFC3339)
}
