import React, { useState, useEffect } from 'react';
import { MagnifyingGlassIcon, ServerIcon, CloudIcon, CogIcon } from '@heroicons/react/24/outline';

interface SearchResult {
  id: string;
  name: string;
  type: string;
  provider: string;
  region?: string;
  status: string;
  description?: string;
  tags?: Record<string, string>;
}

const Search: React.FC = () => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState('all');

  const mockResults: SearchResult[] = [
    {
      id: 'aws-ec2-i-1234567890abcdef0',
      name: 'web-server-1',
      type: 'EC2 Instance',
      provider: 'AWS',
      region: 'us-east-1',
      status: 'running',
      description: 'Production web server',
      tags: { Environment: 'production', Team: 'backend' }
    },
    {
      id: 'gcp-instance-1',
      name: 'database-server',
      type: 'Compute Instance',
      provider: 'GCP',
      region: 'us-central1',
      status: 'running',
      description: 'Primary database server',
      tags: { Environment: 'production', Team: 'data' }
    },
    {
      id: 'azure-vm-2',
      name: 'cache-server',
      type: 'Virtual Machine',
      provider: 'Azure',
      region: 'eastus',
      status: 'stopped',
      description: 'Redis cache server',
      tags: { Environment: 'staging', Team: 'backend' }
    },
    {
      id: 'k8s-pod-1',
      name: 'api-deployment-abc123',
      type: 'Kubernetes Pod',
      provider: 'Kubernetes',
      region: 'us-west-2',
      status: 'running',
      description: 'API service pod',
      tags: { Environment: 'production', App: 'api' }
    }
  ];

  const filters = [
    { id: 'all', name: 'All Resources', count: mockResults.length },
    { id: 'aws', name: 'AWS', count: mockResults.filter(r => r.provider === 'AWS').length },
    { id: 'gcp', name: 'GCP', count: mockResults.filter(r => r.provider === 'GCP').length },
    { id: 'azure', name: 'Azure', count: mockResults.filter(r => r.provider === 'Azure').length },
    { id: 'kubernetes', name: 'Kubernetes', count: mockResults.filter(r => r.provider === 'Kubernetes').length },
  ];

  const performSearch = async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setResults([]);
      return;
    }

    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      const filtered = mockResults.filter(result => {
        const matchesQuery = result.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           result.type.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           result.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           Object.values(result.tags || {}).some(tag => 
                             tag.toLowerCase().includes(searchQuery.toLowerCase())
                           );
        
        const matchesFilter = selectedFilter === 'all' || 
                            result.provider.toLowerCase() === selectedFilter.toLowerCase();
        
        return matchesQuery && matchesFilter;
      });
      
      setResults(filtered);
      setLoading(false);
    }, 300);
  };

  useEffect(() => {
    performSearch(query);
  }, [query, selectedFilter]);

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'running': return 'text-green-600 bg-green-100';
      case 'stopped': return 'text-red-600 bg-red-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getProviderIcon = (provider: string) => {
    switch (provider.toLowerCase()) {
      case 'aws': return <CloudIcon className="h-5 w-5 text-orange-500" />;
      case 'gcp': return <CloudIcon className="h-5 w-5 text-blue-500" />;
      case 'azure': return <CloudIcon className="h-5 w-5 text-blue-600" />;
      case 'kubernetes': return <ServerIcon className="h-5 w-5 text-purple-500" />;
      default: return <CogIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Resource Search</h1>
          <p className="mt-2 text-gray-600">
            Search across all your cloud resources and infrastructure
          </p>
        </div>

        {/* Search Bar */}
        <div className="mb-6">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-lg"
              placeholder="Search resources by name, type, tags, or description..."
            />
          </div>
        </div>

        {/* Filters */}
        <div className="mb-6">
          <div className="flex flex-wrap gap-2">
            {filters.map((filter) => (
              <button
                key={filter.id}
                onClick={() => setSelectedFilter(filter.id)}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                  selectedFilter === filter.id
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                }`}
              >
                {filter.name} ({filter.count})
              </button>
            ))}
          </div>
        </div>

        {/* Results */}
        <div className="bg-white shadow rounded-lg">
          {loading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-gray-500">Searching...</p>
            </div>
          ) : results.length > 0 ? (
            <div className="divide-y divide-gray-200">
              {results.map((result) => (
                <div key={result.id} className="p-6 hover:bg-gray-50 transition-colors">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3">
                      {getProviderIcon(result.provider)}
                      <div className="flex-1">
                        <h3 className="text-lg font-medium text-gray-900">{result.name}</h3>
                        <p className="text-sm text-gray-500">{result.type} • {result.provider}</p>
                        {result.description && (
                          <p className="mt-1 text-sm text-gray-600">{result.description}</p>
                        )}
                        <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                          <span>ID: {result.id}</span>
                          {result.region && <span>Region: {result.region}</span>}
                        </div>
                        {result.tags && Object.keys(result.tags).length > 0 && (
                          <div className="mt-2 flex flex-wrap gap-1">
                            {Object.entries(result.tags).map(([key, value]) => (
                              <span
                                key={key}
                                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                              >
                                {key}: {value}
                              </span>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(result.status)}`}>
                      {result.status}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          ) : query ? (
            <div className="p-8 text-center">
              <MagnifyingGlassIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No results found</h3>
              <p className="mt-1 text-sm text-gray-500">
                Try adjusting your search terms or filters
              </p>
            </div>
          ) : (
            <div className="p-8 text-center">
              <MagnifyingGlassIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Start searching</h3>
              <p className="mt-1 text-sm text-gray-500">
                Enter a search term to find resources across your infrastructure
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Search;
