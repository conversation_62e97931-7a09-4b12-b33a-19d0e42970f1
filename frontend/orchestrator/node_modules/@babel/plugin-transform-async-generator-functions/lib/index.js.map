{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_helperRemapAsyncToGenerator", "_core", "_traverse", "_forAwait", "_default", "exports", "default", "declare", "api", "assertVersion", "yieldStarVisitor", "visitors", "environmentVisitor", "ArrowFunctionExpression", "path", "skip", "YieldExpression", "node", "state", "delegate", "asyncIter", "t", "callExpression", "addHelper", "argument", "forAwaitVisitor", "ForOfStatement", "file", "await", "build", "rewriteForAwait", "getAsyncIterator", "declar", "loop", "block", "body", "ensureBlock", "push", "length", "blockStatement", "inherits", "p", "replaceParent", "parentPath", "replaceWithMultiple", "scope", "parent", "crawl", "visitor", "Function", "async", "traverse", "generator", "setData", "remapAsyncToGenerator", "wrapAsync", "wrapAwait", "name", "manipulateOptions", "_", "parser", "plugins", "Program"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport remapAsyncToGenerator from \"@babel/helper-remap-async-to-generator\";\nimport type { NodePath, Visitor, PluginPass } from \"@babel/core\";\nimport { types as t } from \"@babel/core\";\nimport { visitors } from \"@babel/traverse\";\nimport rewriteForAwait from \"./for-await.ts\";\n\nexport default declare(api => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  const yieldStarVisitor = visitors.environmentVisitor<PluginPass>({\n    ArrowFunctionExpression(path) {\n      path.skip();\n    },\n\n    YieldExpression({ node }, state) {\n      if (!node.delegate) return;\n      const asyncIter = t.callExpression(state.addHelper(\"asyncIterator\"), [\n        node.argument,\n      ]);\n      node.argument = t.callExpression(\n        state.addHelper(\"asyncGeneratorDelegate\"),\n        process.env.BABEL_8_BREAKING\n          ? [asyncIter]\n          : [asyncIter, state.addHelper(\"awaitAsyncGenerator\")],\n      );\n    },\n  });\n\n  const forAwaitVisitor = visitors.environmentVisitor<PluginPass>({\n    ArrowFunctionExpression(path) {\n      path.skip();\n    },\n\n    ForOfStatement(path: NodePath<t.ForOfStatement>, { file }) {\n      const { node } = path;\n      if (!node.await) return;\n\n      const build = rewriteForAwait(path, {\n        getAsyncIterator: file.addHelper(\"asyncIterator\"),\n      });\n\n      const { declar, loop } = build;\n      const block = loop.body as t.BlockStatement;\n\n      // ensure that it's a block so we can take all its statements\n      path.ensureBlock();\n\n      // add the value declaration to the new loop body\n      if (declar) {\n        block.body.push(declar);\n        if (path.node.body.body.length) {\n          block.body.push(t.blockStatement(path.node.body.body));\n        }\n      } else {\n        block.body.push(...path.node.body.body);\n      }\n\n      t.inherits(loop, node);\n      t.inherits(loop.body, node.body);\n\n      const p = build.replaceParent ? path.parentPath : path;\n      p.replaceWithMultiple(build.node);\n\n      // TODO: Avoid crawl\n      p.scope.parent.crawl();\n    },\n  });\n\n  const visitor: Visitor<PluginPass> = {\n    Function(path, state) {\n      if (!path.node.async) return;\n\n      path.traverse(forAwaitVisitor, state);\n\n      if (!path.node.generator) return;\n\n      path.traverse(yieldStarVisitor, state);\n\n      path.setData(\n        \"@babel/plugin-transform-async-generator-functions/async_generator_function\",\n        true,\n      );\n\n      // We don't need to pass the noNewArrows assumption, since\n      // async generators are never arrow functions.\n      remapAsyncToGenerator(path, {\n        wrapAsync: state.addHelper(\"wrapAsyncGenerator\"),\n        wrapAwait: state.addHelper(\"awaitAsyncGenerator\"),\n      });\n    },\n  };\n\n  return {\n    name: \"transform-async-generator-functions\",\n\n    manipulateOptions: process.env.BABEL_8_BREAKING\n      ? undefined\n      : (_, parser) => parser.plugins.push(\"asyncGenerators\"),\n\n    visitor: {\n      Program(path, state) {\n        // We need to traverse the ast here (instead of just vising Function\n        // in the top level visitor) because for-await needs to run before the\n        // async-to-generator plugin. This is because for-await is transpiled\n        // using \"await\" expressions, which are then converted to \"yield\".\n        //\n        // This is bad for performance, but plugin ordering will allow as to\n        // directly visit Function in the top level visitor.\n        path.traverse(visitor, state);\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,4BAAA,GAAAD,OAAA;AAEA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,SAAA,GAAAH,OAAA;AACA,IAAAI,SAAA,GAAAJ,OAAA;AAA6C,IAAAK,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAE9B,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,uCAAoB,CAAC;EAEtC,MAAMC,gBAAgB,GAAGC,kBAAQ,CAACC,kBAAkB,CAAa;IAC/DC,uBAAuBA,CAACC,IAAI,EAAE;MAC5BA,IAAI,CAACC,IAAI,CAAC,CAAC;IACb,CAAC;IAEDC,eAAeA,CAAC;MAAEC;IAAK,CAAC,EAAEC,KAAK,EAAE;MAC/B,IAAI,CAACD,IAAI,CAACE,QAAQ,EAAE;MACpB,MAAMC,SAAS,GAAGC,WAAC,CAACC,cAAc,CAACJ,KAAK,CAACK,SAAS,CAAC,eAAe,CAAC,EAAE,CACnEN,IAAI,CAACO,QAAQ,CACd,CAAC;MACFP,IAAI,CAACO,QAAQ,GAAGH,WAAC,CAACC,cAAc,CAC9BJ,KAAK,CAACK,SAAS,CAAC,wBAAwB,CAAC,EAGrC,CAACH,SAAS,EAAEF,KAAK,CAACK,SAAS,CAAC,qBAAqB,CAAC,CACxD,CAAC;IACH;EACF,CAAC,CAAC;EAEF,MAAME,eAAe,GAAGd,kBAAQ,CAACC,kBAAkB,CAAa;IAC9DC,uBAAuBA,CAACC,IAAI,EAAE;MAC5BA,IAAI,CAACC,IAAI,CAAC,CAAC;IACb,CAAC;IAEDW,cAAcA,CAACZ,IAAgC,EAAE;MAAEa;IAAK,CAAC,EAAE;MACzD,MAAM;QAAEV;MAAK,CAAC,GAAGH,IAAI;MACrB,IAAI,CAACG,IAAI,CAACW,KAAK,EAAE;MAEjB,MAAMC,KAAK,GAAG,IAAAC,iBAAe,EAAChB,IAAI,EAAE;QAClCiB,gBAAgB,EAAEJ,IAAI,CAACJ,SAAS,CAAC,eAAe;MAClD,CAAC,CAAC;MAEF,MAAM;QAAES,MAAM;QAAEC;MAAK,CAAC,GAAGJ,KAAK;MAC9B,MAAMK,KAAK,GAAGD,IAAI,CAACE,IAAwB;MAG3CrB,IAAI,CAACsB,WAAW,CAAC,CAAC;MAGlB,IAAIJ,MAAM,EAAE;QACVE,KAAK,CAACC,IAAI,CAACE,IAAI,CAACL,MAAM,CAAC;QACvB,IAAIlB,IAAI,CAACG,IAAI,CAACkB,IAAI,CAACA,IAAI,CAACG,MAAM,EAAE;UAC9BJ,KAAK,CAACC,IAAI,CAACE,IAAI,CAAChB,WAAC,CAACkB,cAAc,CAACzB,IAAI,CAACG,IAAI,CAACkB,IAAI,CAACA,IAAI,CAAC,CAAC;QACxD;MACF,CAAC,MAAM;QACLD,KAAK,CAACC,IAAI,CAACE,IAAI,CAAC,GAAGvB,IAAI,CAACG,IAAI,CAACkB,IAAI,CAACA,IAAI,CAAC;MACzC;MAEAd,WAAC,CAACmB,QAAQ,CAACP,IAAI,EAAEhB,IAAI,CAAC;MACtBI,WAAC,CAACmB,QAAQ,CAACP,IAAI,CAACE,IAAI,EAAElB,IAAI,CAACkB,IAAI,CAAC;MAEhC,MAAMM,CAAC,GAAGZ,KAAK,CAACa,aAAa,GAAG5B,IAAI,CAAC6B,UAAU,GAAG7B,IAAI;MACtD2B,CAAC,CAACG,mBAAmB,CAACf,KAAK,CAACZ,IAAI,CAAC;MAGjCwB,CAAC,CAACI,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC;IACxB;EACF,CAAC,CAAC;EAEF,MAAMC,OAA4B,GAAG;IACnCC,QAAQA,CAACnC,IAAI,EAAEI,KAAK,EAAE;MACpB,IAAI,CAACJ,IAAI,CAACG,IAAI,CAACiC,KAAK,EAAE;MAEtBpC,IAAI,CAACqC,QAAQ,CAAC1B,eAAe,EAAEP,KAAK,CAAC;MAErC,IAAI,CAACJ,IAAI,CAACG,IAAI,CAACmC,SAAS,EAAE;MAE1BtC,IAAI,CAACqC,QAAQ,CAACzC,gBAAgB,EAAEQ,KAAK,CAAC;MAEtCJ,IAAI,CAACuC,OAAO,CACV,4EAA4E,EAC5E,IACF,CAAC;MAID,IAAAC,oCAAqB,EAACxC,IAAI,EAAE;QAC1ByC,SAAS,EAAErC,KAAK,CAACK,SAAS,CAAC,oBAAoB,CAAC;QAChDiC,SAAS,EAAEtC,KAAK,CAACK,SAAS,CAAC,qBAAqB;MAClD,CAAC,CAAC;IACJ;EACF,CAAC;EAED,OAAO;IACLkC,IAAI,EAAE,qCAAqC;IAE3CC,iBAAiB,EAEbA,CAACC,CAAC,EAAEC,MAAM,KAAKA,MAAM,CAACC,OAAO,CAACxB,IAAI,CAAC,iBAAiB,CAAC;IAEzDW,OAAO,EAAE;MACPc,OAAOA,CAAChD,IAAI,EAAEI,KAAK,EAAE;QAQnBJ,IAAI,CAACqC,QAAQ,CAACH,OAAO,EAAE9B,KAAK,CAAC;MAC/B;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}