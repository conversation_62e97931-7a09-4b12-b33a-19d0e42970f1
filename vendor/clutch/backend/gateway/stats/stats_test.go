package stats

import (
	"fmt"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/uber-go/tally/v4"
)

type mockReporter struct {
	tally.StatsReporter

	actualName string
}

func (m *mockReporter) ReportCounter(name string, tags map[string]string, value int64) {
	m.actualName = name
}

var tests = []struct {
	name string
	tags map[string]string
}{
	{
		name: "foo.Bar.baz",
		tags: map[string]string{"bar": "baz", "hello": "world"},
	},
	{
		name: "foo",
		tags: map[string]string{"bar": "baz.bar.Bug", "hello": "world-ok"},
	},
}

func TestPointTagReporter(t *testing.T) {
	for idx, tt := range tests {
		t.Run(fmt.Sprintf("%d", idx), func(t *testing.T) {
			t.Parallel()

			m := &mockReporter{}
			p := pointTagReporter{
				StatsReporter: m,
				separator:     ",",
			}
			p.<PERSON>ou<PERSON>(tt.name, tt.tags, 10)

			actualTags := map[string]string{}
			for _, v := range strings.Split(m.actualName, ",")[1:] {
				ss := strings.Split(v, "=")
				actualTags[ss[0]] = ss[1]
			}

			sanitizedTags := map[string]string{}
			for k, v := range tt.tags {
				sanitizedTags[k] = replaceChars(v)
			}

			assert.Equal(t, sanitizedTags, actualTags)
			assert.True(t, strings.HasPrefix(m.actualName, tt.name))
		})
	}
}
