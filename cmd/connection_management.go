package main

import (
	"fmt"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
)

// Custom Connection Management System
// Allows users to register and manage custom connections to various systems

// List all custom connections
func getCustomConnections(c *fiber.Ctx) error {
	provider := c.Query("provider", "")
	connectionType := c.Query("type", "")
	status := c.Query("status", "")

	connections := []CustomConnection{}

	for _, conn := range customConnections {
		// Apply filters
		if provider != "" && conn.Provider != provider {
			continue
		}
		if connectionType != "" && conn.Type != connectionType {
			continue
		}
		if status != "" && conn.Status != status {
			continue
		}

		// Mask sensitive credentials
		maskedConn := conn
		maskedConn.Credentials = maskCredentials(conn.Credentials)
		connections = append(connections, maskedConn)
	}

	return c.JSON(fiber.Map{
		"connections": connections,
		"total":       len(connections),
		"filters": fiber.Map{
			"provider": provider,
			"type":     connectionType,
			"status":   status,
		},
	})
}

// Get specific custom connection
func getCustomConnection(c *fiber.Ctx) error {
	id := c.Params("id")

	conn, exists := customConnections[id]
	if !exists {
		return c.Status(404).JSON(fiber.Map{"error": "Connection not found"})
	}

	// Mask sensitive credentials
	conn.Credentials = maskCredentials(conn.Credentials)

	return c.JSON(conn)
}

// Create new custom connection
func createCustomConnection(c *fiber.Ctx) error {
	var req struct {
		Name        string                 `json:"name"`
		Type        string                 `json:"type"`
		Provider    string                 `json:"provider"`
		Config      map[string]interface{} `json:"config"`
		Credentials map[string]interface{} `json:"credentials"`
	}

	if err := c.BodyParser(&req); err != nil {
		return c.Status(400).JSON(fiber.Map{"error": "Invalid request body"})
	}

	// Validate required fields
	if req.Name == "" || req.Type == "" || req.Provider == "" {
		return c.Status(400).JSON(fiber.Map{"error": "Name, type, and provider are required"})
	}

	// Generate ID
	id := fmt.Sprintf("%s-%d", req.Provider, time.Now().Unix())

	// Test connection
	status := "active"
	if !testConnection(req.Type, req.Provider, req.Config, req.Credentials) {
		status = "error"
	}

	connection := CustomConnection{
		ID:          id,
		Name:        req.Name,
		Type:        req.Type,
		Provider:    req.Provider,
		Config:      req.Config,
		Credentials: req.Credentials,
		Status:      status,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	customConnections[id] = connection

	// Return connection with masked credentials
	connection.Credentials = maskCredentials(connection.Credentials)

	return c.Status(201).JSON(connection)
}

// Update custom connection
func updateCustomConnection(c *fiber.Ctx) error {
	id := c.Params("id")

	conn, exists := customConnections[id]
	if !exists {
		return c.Status(404).JSON(fiber.Map{"error": "Connection not found"})
	}

	var req struct {
		Name        string                 `json:"name,omitempty"`
		Config      map[string]interface{} `json:"config,omitempty"`
		Credentials map[string]interface{} `json:"credentials,omitempty"`
	}

	if err := c.BodyParser(&req); err != nil {
		return c.Status(400).JSON(fiber.Map{"error": "Invalid request body"})
	}

	// Update fields
	if req.Name != "" {
		conn.Name = req.Name
	}
	if req.Config != nil {
		conn.Config = req.Config
	}
	if req.Credentials != nil {
		conn.Credentials = req.Credentials
	}

	// Test updated connection
	if !testConnection(conn.Type, conn.Provider, conn.Config, conn.Credentials) {
		conn.Status = "error"
	} else {
		conn.Status = "active"
	}

	conn.UpdatedAt = time.Now()
	customConnections[id] = conn

	// Return connection with masked credentials
	conn.Credentials = maskCredentials(conn.Credentials)

	return c.JSON(conn)
}

// Delete custom connection
func deleteCustomConnection(c *fiber.Ctx) error {
	id := c.Params("id")

	if _, exists := customConnections[id]; !exists {
		return c.Status(404).JSON(fiber.Map{"error": "Connection not found"})
	}

	delete(customConnections, id)

	return c.JSON(fiber.Map{
		"message":    "Connection deleted successfully",
		"deleted_at": time.Now(),
	})
}

// Test custom connection
func testCustomConnectionEndpoint(c *fiber.Ctx) error {
	id := c.Params("id")

	conn, exists := customConnections[id]
	if !exists {
		return c.Status(404).JSON(fiber.Map{"error": "Connection not found"})
	}

	success := testConnection(conn.Type, conn.Provider, conn.Config, conn.Credentials)

	// Update connection status
	if success {
		conn.Status = "active"
	} else {
		conn.Status = "error"
	}
	conn.UpdatedAt = time.Now()
	customConnections[id] = conn

	return c.JSON(fiber.Map{
		"connection_id": id,
		"success":       success,
		"status":        conn.Status,
		"tested_at":     time.Now(),
		"message":       getTestMessage(success, conn.Provider),
	})
}

// Get connection types and their schemas
func getConnectionTypes(c *fiber.Ctx) error {
	types := map[string]interface{}{
		"cloud_provider": map[string]interface{}{
			"description": "Cloud provider connections (AWS, GCP, Azure)",
			"providers":   []string{"aws", "gcp", "azure"},
			"required_fields": map[string]interface{}{
				"aws":   []string{"region", "account_id"},
				"gcp":   []string{"project_id", "region"},
				"azure": []string{"subscription_id", "resource_group"},
			},
			"credential_fields": map[string]interface{}{
				"aws":   []string{"access_key_id", "secret_access_key", "role_arn"},
				"gcp":   []string{"service_account_key"},
				"azure": []string{"client_id", "client_secret", "tenant_id"},
			},
		},
		"kubernetes": map[string]interface{}{
			"description": "Kubernetes cluster connections",
			"providers":   []string{"kubernetes"},
			"required_fields": map[string]interface{}{
				"kubernetes": []string{"cluster_name", "endpoint"},
			},
			"credential_fields": map[string]interface{}{
				"kubernetes": []string{"kubeconfig", "token"},
			},
		},
		"database": map[string]interface{}{
			"description": "Database connections",
			"providers":   []string{"postgresql", "mysql", "mongodb", "redis"},
			"required_fields": map[string]interface{}{
				"postgresql": []string{"host", "port", "database"},
				"mysql":      []string{"host", "port", "database"},
				"mongodb":    []string{"host", "port", "database"},
				"redis":      []string{"host", "port"},
			},
			"credential_fields": map[string]interface{}{
				"postgresql": []string{"username", "password"},
				"mysql":      []string{"username", "password"},
				"mongodb":    []string{"username", "password"},
				"redis":      []string{"password"},
			},
		},
		"monitoring": map[string]interface{}{
			"description": "Monitoring system connections",
			"providers":   []string{"prometheus", "grafana", "datadog", "newrelic"},
			"required_fields": map[string]interface{}{
				"prometheus": []string{"endpoint"},
				"grafana":    []string{"endpoint"},
				"datadog":    []string{"site"},
				"newrelic":   []string{"account_id"},
			},
			"credential_fields": map[string]interface{}{
				"prometheus": []string{"username", "password"},
				"grafana":    []string{"api_key"},
				"datadog":    []string{"api_key", "app_key"},
				"newrelic":   []string{"api_key"},
			},
		},
		"messaging": map[string]interface{}{
			"description": "Messaging system connections",
			"providers":   []string{"kafka", "rabbitmq", "sqs", "pubsub"},
			"required_fields": map[string]interface{}{
				"kafka":    []string{"brokers"},
				"rabbitmq": []string{"host", "port"},
				"sqs":      []string{"region"},
				"pubsub":   []string{"project_id"},
			},
			"credential_fields": map[string]interface{}{
				"kafka":    []string{"username", "password"},
				"rabbitmq": []string{"username", "password"},
				"sqs":      []string{"access_key_id", "secret_access_key"},
				"pubsub":   []string{"service_account_key"},
			},
		},
		"custom": map[string]interface{}{
			"description": "Custom API connections",
			"providers":   []string{"rest_api", "graphql", "grpc"},
			"required_fields": map[string]interface{}{
				"rest_api": []string{"base_url"},
				"graphql":  []string{"endpoint"},
				"grpc":     []string{"endpoint"},
			},
			"credential_fields": map[string]interface{}{
				"rest_api": []string{"api_key", "bearer_token"},
				"graphql":  []string{"api_key", "bearer_token"},
				"grpc":     []string{"tls_cert", "api_key"},
			},
		},
	}

	return c.JSON(fiber.Map{
		"connection_types": types,
		"total_types":      len(types),
	})
}

// Get connection statistics
func getConnectionStats(c *fiber.Ctx) error {
	stats := fiber.Map{
		"total_connections": len(customConnections),
		"by_provider":       make(map[string]int),
		"by_type":           make(map[string]int),
		"by_status":         make(map[string]int),
		"recent_activity":   []fiber.Map{},
	}

	// Calculate statistics
	for _, conn := range customConnections {
		// By provider
		if count, ok := stats["by_provider"].(map[string]int)[conn.Provider]; ok {
			stats["by_provider"].(map[string]int)[conn.Provider] = count + 1
		} else {
			stats["by_provider"].(map[string]int)[conn.Provider] = 1
		}

		// By type
		if count, ok := stats["by_type"].(map[string]int)[conn.Type]; ok {
			stats["by_type"].(map[string]int)[conn.Type] = count + 1
		} else {
			stats["by_type"].(map[string]int)[conn.Type] = 1
		}

		// By status
		if count, ok := stats["by_status"].(map[string]int)[conn.Status]; ok {
			stats["by_status"].(map[string]int)[conn.Status] = count + 1
		} else {
			stats["by_status"].(map[string]int)[conn.Status] = 1
		}
	}

	return c.JSON(stats)
}

// Helper functions

// Mask sensitive credential information
func maskCredentials(credentials map[string]interface{}) map[string]interface{} {
	masked := make(map[string]interface{})

	for key, value := range credentials {
		if isSensitiveField(key) {
			if str, ok := value.(string); ok && len(str) > 4 {
				masked[key] = str[:4] + "***"
			} else {
				masked[key] = "***"
			}
		} else {
			masked[key] = value
		}
	}

	return masked
}

// Check if field contains sensitive information
func isSensitiveField(fieldName string) bool {
	sensitiveFields := []string{
		"password", "secret", "key", "token", "credential",
		"access_key", "secret_key", "api_key", "private_key",
		"client_secret", "service_account_key", "kubeconfig",
	}

	fieldLower := strings.ToLower(fieldName)
	for _, sensitive := range sensitiveFields {
		if strings.Contains(fieldLower, sensitive) {
			return true
		}
	}

	return false
}

// Test connection to external system
func testConnection(connType, provider string, config, credentials map[string]interface{}) bool {
	// Simulate connection testing
	// In a real implementation, this would actually test the connection

	switch provider {
	case "aws":
		return testAWSConnection(config, credentials)
	case "gcp":
		return testGCPConnection(config, credentials)
	case "azure":
		return testAzureConnection(config, credentials)
	case "kubernetes":
		return testKubernetesConnection(config, credentials)
	case "postgresql", "mysql", "mongodb", "redis":
		return testDatabaseConnection(provider, config, credentials)
	default:
		// For demo purposes, randomly succeed/fail
		return time.Now().Unix()%3 != 0 // ~67% success rate
	}
}

// Test AWS connection
func testAWSConnection(config, credentials map[string]interface{}) bool {
	// Simulate AWS connection test
	return credentials["access_key_id"] != nil && credentials["secret_access_key"] != nil
}

// Test GCP connection
func testGCPConnection(config, credentials map[string]interface{}) bool {
	// Simulate GCP connection test
	return credentials["service_account_key"] != nil && config["project_id"] != nil
}

// Test Azure connection
func testAzureConnection(config, credentials map[string]interface{}) bool {
	// Simulate Azure connection test
	return credentials["client_id"] != nil && credentials["client_secret"] != nil
}

// Test Kubernetes connection
func testKubernetesConnection(config, credentials map[string]interface{}) bool {
	// Simulate Kubernetes connection test
	return (credentials["kubeconfig"] != nil || credentials["token"] != nil) && config["endpoint"] != nil
}

// Test database connection
func testDatabaseConnection(provider string, config, credentials map[string]interface{}) bool {
	// Simulate database connection test
	return config["host"] != nil && credentials["username"] != nil
}

// Get test message based on result
func getTestMessage(success bool, provider string) string {
	if success {
		return fmt.Sprintf("Successfully connected to %s", provider)
	}
	return fmt.Sprintf("Failed to connect to %s - please check your configuration and credentials", provider)
}
