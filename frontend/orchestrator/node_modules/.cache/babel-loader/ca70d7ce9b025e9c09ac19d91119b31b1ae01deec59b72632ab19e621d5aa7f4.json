{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/EnvoyConfigEditor.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { fetchEnvoyConfigs, fetchEnvoyNodes, createEnvoyConfig, setSelectedConfig } from '../store/slices/envoySlice';\nimport { GlobeAltIcon, PlusIcon, DocumentTextIcon } from '@heroicons/react/24/outline';\nimport Editor from '@monaco-editor/react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EnvoyConfigEditor = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    configs,\n    nodes,\n    selectedConfig,\n    loading,\n    error\n  } = useSelector(state => state.envoy);\n  const [showCreateForm, setShowCreateForm] = useState(false);\n  const [newConfig, setNewConfig] = useState({\n    node_id: '',\n    cluster_name: '',\n    config: '',\n    version: '1.0.0'\n  });\n  useEffect(() => {\n    dispatch(fetchEnvoyConfigs());\n    dispatch(fetchEnvoyNodes());\n  }, [dispatch]);\n  const handleCreateConfig = () => {\n    dispatch(createEnvoyConfig(newConfig));\n    setNewConfig({\n      node_id: '',\n      cluster_name: '',\n      config: '',\n      version: '1.0.0'\n    });\n    setShowCreateForm(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-white\",\n          children: \"Envoy Configuration\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-1 text-sm text-gray-400\",\n          children: \"Manage Envoy proxy configurations and deployments\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setShowCreateForm(true),\n        className: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500\",\n        children: [/*#__PURE__*/_jsxDEV(PlusIcon, {\n          className: \"h-4 w-4 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this), \"New Config\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 shadow rounded-lg\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-4 py-5 sm:p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg leading-6 font-medium text-white mb-4\",\n            children: \"Configurations\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center h-32\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [configs.map(config => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `border rounded-lg p-4 cursor-pointer transition-colors ${(selectedConfig === null || selectedConfig === void 0 ? void 0 : selectedConfig.id) === config.id ? 'border-cyan-500 bg-cyan-500/10' : 'border-gray-600 hover:bg-gray-700'}`,\n              onClick: () => dispatch(setSelectedConfig(config)),\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-lg font-medium text-white\",\n                    children: config.cluster_name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 87,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mt-1 text-sm text-gray-400\",\n                    children: [\"Node: \", config.node_id]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 88,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mt-1 text-xs text-gray-500\",\n                    children: [\"Version: \", config.version]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 89,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 86,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(DocumentTextIcon, {\n                  className: \"h-6 w-6 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 91,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 21\n              }, this)\n            }, config.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 19\n            }, this)), configs.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-8\",\n              children: [/*#__PURE__*/_jsxDEV(GlobeAltIcon, {\n                className: \"mx-auto h-12 w-12 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"mt-2 text-sm font-medium text-gray-300\",\n                children: \"No configurations\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-gray-400\",\n                children: \"Create your first Envoy configuration.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 shadow rounded-lg\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-4 py-5 sm:p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg leading-6 font-medium text-white mb-4\",\n            children: \"Configuration Editor\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this), selectedConfig ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-300\",\n                children: \"Cluster Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\",\n                value: selectedConfig.cluster_name,\n                readOnly: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-300\",\n                children: \"Node ID\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\",\n                value: selectedConfig.node_id,\n                readOnly: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-300\",\n                children: \"Configuration (YAML)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-1 border border-gray-600 rounded-md overflow-hidden\",\n                children: /*#__PURE__*/_jsxDEV(Editor, {\n                  height: \"300px\",\n                  defaultLanguage: \"yaml\",\n                  value: selectedConfig.config,\n                  theme: \"vs-dark\",\n                  options: {\n                    readOnly: true,\n                    minimap: {\n                      enabled: false\n                    },\n                    scrollBeyondLastLine: false,\n                    fontSize: 14,\n                    fontFamily: 'JetBrains Mono, monospace',\n                    lineNumbers: 'on',\n                    folding: true,\n                    wordWrap: 'on'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-8\",\n            children: [/*#__PURE__*/_jsxDEV(DocumentTextIcon, {\n              className: \"mx-auto h-12 w-12 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"mt-2 text-sm font-medium text-gray-300\",\n              children: \"Select a configuration\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-gray-400\",\n              children: \"Choose a configuration from the list to view and edit it.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 shadow rounded-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-4 py-5 sm:p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg leading-6 font-medium text-white mb-4\",\n          children: \"Connected Envoy Nodes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), nodes.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-8\",\n          children: [/*#__PURE__*/_jsxDEV(GlobeAltIcon, {\n            className: \"mx-auto h-12 w-12 text-gray-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"mt-2 text-sm font-medium text-gray-300\",\n            children: \"No nodes connected\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-1 text-sm text-gray-400\",\n            children: \"Envoy nodes will appear here when they connect to the control plane.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3\",\n          children: nodes.map(node => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border border-gray-600 rounded-lg p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-lg font-medium text-white\",\n                  children: node.id\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-400\",\n                  children: [\"Cluster: \", node.cluster]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500\",\n                  children: [\"Version: \", node.version]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-3 h-3 rounded-full ${node.status === 'connected' ? 'bg-green-400' : 'bg-red-400'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-2 text-xs text-gray-500\",\n              children: [\"Last seen: \", new Date(node.last_seen).toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 19\n            }, this)]\n          }, node.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this), showCreateForm && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-gray-800\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-white mb-4\",\n            children: \"Create New Configuration\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-300\",\n                children: \"Node ID\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\",\n                value: newConfig.node_id,\n                onChange: e => setNewConfig({\n                  ...newConfig,\n                  node_id: e.target.value\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-300\",\n                children: \"Cluster Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\",\n                value: newConfig.cluster_name,\n                onChange: e => setNewConfig({\n                  ...newConfig,\n                  cluster_name: e.target.value\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-300\",\n                children: \"Configuration\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-1 border border-gray-600 rounded-md overflow-hidden\",\n                children: /*#__PURE__*/_jsxDEV(Editor, {\n                  height: \"200px\",\n                  defaultLanguage: \"yaml\",\n                  value: newConfig.config,\n                  onChange: value => setNewConfig({\n                    ...newConfig,\n                    config: value || ''\n                  }),\n                  theme: \"vs-dark\",\n                  options: {\n                    minimap: {\n                      enabled: false\n                    },\n                    scrollBeyondLastLine: false,\n                    fontSize: 14,\n                    fontFamily: 'JetBrains Mono, monospace',\n                    lineNumbers: 'on',\n                    folding: true,\n                    wordWrap: 'on'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-end space-x-3 mt-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowCreateForm(false),\n              className: \"px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700\",\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleCreateConfig,\n              className: \"px-4 py-2 bg-cyan-600 text-white rounded-md hover:bg-cyan-700\",\n              children: \"Create\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 43,\n    columnNumber: 5\n  }, this);\n};\n_s(EnvoyConfigEditor, \"jwnSYDZ6YBl384FSHox+SEb3rrs=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = EnvoyConfigEditor;\nexport default EnvoyConfigEditor;\nvar _c;\n$RefreshReg$(_c, \"EnvoyConfigEditor\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "fetchEnvoyConfigs", "fetchEnvoyNodes", "createEnvoyConfig", "setSelectedConfig", "GlobeAltIcon", "PlusIcon", "DocumentTextIcon", "Editor", "jsxDEV", "_jsxDEV", "EnvoyConfigEditor", "_s", "dispatch", "configs", "nodes", "selectedConfig", "loading", "error", "state", "envoy", "showCreateForm", "setShowCreateForm", "newConfig", "setNewConfig", "node_id", "cluster_name", "config", "version", "handleCreateConfig", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "map", "id", "length", "type", "value", "readOnly", "height", "defaultLanguage", "theme", "options", "minimap", "enabled", "scrollBeyondLastLine", "fontSize", "fontFamily", "lineNumbers", "folding", "wordWrap", "node", "cluster", "status", "Date", "last_seen", "toLocaleString", "onChange", "e", "target", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/EnvoyConfigEditor.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { RootState, AppDispatch } from '../store/store';\nimport {\n  fetchEnvoyConfigs,\n  fetchEnvoyNodes,\n  createEnvoyConfig,\n  setSelectedConfig,\n} from '../store/slices/envoySlice';\nimport {\n  GlobeAltIcon,\n  PlusIcon,\n  DocumentTextIcon,\n} from '@heroicons/react/24/outline';\nimport Editor from '@monaco-editor/react';\n\nconst EnvoyConfigEditor: React.FC = () => {\n  const dispatch = useDispatch<AppDispatch>();\n  const { configs, nodes, selectedConfig, loading, error } = useSelector(\n    (state: RootState) => state.envoy\n  );\n\n  const [showCreateForm, setShowCreateForm] = useState(false);\n  const [newConfig, setNewConfig] = useState({\n    node_id: '',\n    cluster_name: '',\n    config: '',\n    version: '1.0.0',\n  });\n\n  useEffect(() => {\n    dispatch(fetchEnvoyConfigs());\n    dispatch(fetchEnvoyNodes());\n  }, [dispatch]);\n\n  const handleCreateConfig = () => {\n    dispatch(createEnvoyConfig(newConfig));\n    setNewConfig({ node_id: '', cluster_name: '', config: '', version: '1.0.0' });\n    setShowCreateForm(false);\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-white\">Envoy Configuration</h1>\n          <p className=\"mt-1 text-sm text-gray-400\">\n            Manage Envoy proxy configurations and deployments\n          </p>\n        </div>\n        <button\n          onClick={() => setShowCreateForm(true)}\n          className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500\"\n        >\n          <PlusIcon className=\"h-4 w-4 mr-2\" />\n          New Config\n        </button>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Configurations List */}\n        <div className=\"bg-gray-800 shadow rounded-lg\">\n          <div className=\"px-4 py-5 sm:p-6\">\n            <h3 className=\"text-lg leading-6 font-medium text-white mb-4\">\n              Configurations\n            </h3>\n\n            {loading ? (\n              <div className=\"flex items-center justify-center h-32\">\n                <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500\"></div>\n              </div>\n            ) : (\n              <div className=\"space-y-3\">\n                {configs.map((config) => (\n                  <div\n                    key={config.id}\n                    className={`border rounded-lg p-4 cursor-pointer transition-colors ${\n                      selectedConfig?.id === config.id\n                        ? 'border-cyan-500 bg-cyan-500/10'\n                        : 'border-gray-600 hover:bg-gray-700'\n                    }`}\n                    onClick={() => dispatch(setSelectedConfig(config))}\n                  >\n                    <div className=\"flex items-start justify-between\">\n                      <div>\n                        <h4 className=\"text-lg font-medium text-white\">{config.cluster_name}</h4>\n                        <p className=\"mt-1 text-sm text-gray-400\">Node: {config.node_id}</p>\n                        <p className=\"mt-1 text-xs text-gray-500\">Version: {config.version}</p>\n                      </div>\n                      <DocumentTextIcon className=\"h-6 w-6 text-gray-400\" />\n                    </div>\n                  </div>\n                ))}\n\n                {configs.length === 0 && (\n                  <div className=\"text-center py-8\">\n                    <GlobeAltIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n                    <h3 className=\"mt-2 text-sm font-medium text-gray-300\">No configurations</h3>\n                    <p className=\"mt-1 text-sm text-gray-400\">\n                      Create your first Envoy configuration.\n                    </p>\n                  </div>\n                )}\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Config Editor */}\n        <div className=\"bg-gray-800 shadow rounded-lg\">\n          <div className=\"px-4 py-5 sm:p-6\">\n            <h3 className=\"text-lg leading-6 font-medium text-white mb-4\">\n              Configuration Editor\n            </h3>\n\n            {selectedConfig ? (\n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-300\">\n                    Cluster Name\n                  </label>\n                  <input\n                    type=\"text\"\n                    className=\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\"\n                    value={selectedConfig.cluster_name}\n                    readOnly\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-300\">\n                    Node ID\n                  </label>\n                  <input\n                    type=\"text\"\n                    className=\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\"\n                    value={selectedConfig.node_id}\n                    readOnly\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-300\">\n                    Configuration (YAML)\n                  </label>\n                  <div className=\"mt-1 border border-gray-600 rounded-md overflow-hidden\">\n                    <Editor\n                      height=\"300px\"\n                      defaultLanguage=\"yaml\"\n                      value={selectedConfig.config}\n                      theme=\"vs-dark\"\n                      options={{\n                        readOnly: true,\n                        minimap: { enabled: false },\n                        scrollBeyondLastLine: false,\n                        fontSize: 14,\n                        fontFamily: 'JetBrains Mono, monospace',\n                        lineNumbers: 'on',\n                        folding: true,\n                        wordWrap: 'on',\n                      }}\n                    />\n                  </div>\n                </div>\n              </div>\n            ) : (\n              <div className=\"text-center py-8\">\n                <DocumentTextIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n                <h3 className=\"mt-2 text-sm font-medium text-gray-300\">Select a configuration</h3>\n                <p className=\"mt-1 text-sm text-gray-400\">\n                  Choose a configuration from the list to view and edit it.\n                </p>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Connected Nodes */}\n      <div className=\"bg-gray-800 shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <h3 className=\"text-lg leading-6 font-medium text-white mb-4\">\n            Connected Envoy Nodes\n          </h3>\n\n          {nodes.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <GlobeAltIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n              <h3 className=\"mt-2 text-sm font-medium text-gray-300\">No nodes connected</h3>\n              <p className=\"mt-1 text-sm text-gray-400\">\n                Envoy nodes will appear here when they connect to the control plane.\n              </p>\n            </div>\n          ) : (\n            <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3\">\n              {nodes.map((node) => (\n                <div key={node.id} className=\"border border-gray-600 rounded-lg p-4\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <h4 className=\"text-lg font-medium text-white\">{node.id}</h4>\n                      <p className=\"text-sm text-gray-400\">Cluster: {node.cluster}</p>\n                      <p className=\"text-xs text-gray-500\">Version: {node.version}</p>\n                    </div>\n                    <div className={`w-3 h-3 rounded-full ${\n                      node.status === 'connected' ? 'bg-green-400' : 'bg-red-400'\n                    }`} />\n                  </div>\n                  <p className=\"mt-2 text-xs text-gray-500\">\n                    Last seen: {new Date(node.last_seen).toLocaleString()}\n                  </p>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Create Config Modal */}\n      {showCreateForm && (\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n          <div className=\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-gray-800\">\n            <div className=\"mt-3\">\n              <h3 className=\"text-lg font-medium text-white mb-4\">Create New Configuration</h3>\n              \n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-300\">Node ID</label>\n                  <input\n                    type=\"text\"\n                    className=\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\"\n                    value={newConfig.node_id}\n                    onChange={(e) => setNewConfig({ ...newConfig, node_id: e.target.value })}\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-300\">Cluster Name</label>\n                  <input\n                    type=\"text\"\n                    className=\"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500\"\n                    value={newConfig.cluster_name}\n                    onChange={(e) => setNewConfig({ ...newConfig, cluster_name: e.target.value })}\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-300\">Configuration</label>\n                  <div className=\"mt-1 border border-gray-600 rounded-md overflow-hidden\">\n                    <Editor\n                      height=\"200px\"\n                      defaultLanguage=\"yaml\"\n                      value={newConfig.config}\n                      onChange={(value) => setNewConfig({ ...newConfig, config: value || '' })}\n                      theme=\"vs-dark\"\n                      options={{\n                        minimap: { enabled: false },\n                        scrollBeyondLastLine: false,\n                        fontSize: 14,\n                        fontFamily: 'JetBrains Mono, monospace',\n                        lineNumbers: 'on',\n                        folding: true,\n                        wordWrap: 'on',\n                      }}\n                    />\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"flex justify-end space-x-3 mt-6\">\n                <button\n                  onClick={() => setShowCreateForm(false)}\n                  className=\"px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700\"\n                >\n                  Cancel\n                </button>\n                <button\n                  onClick={handleCreateConfig}\n                  className=\"px-4 py-2 bg-cyan-600 text-white rounded-md hover:bg-cyan-700\"\n                >\n                  Create\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default EnvoyConfigEditor;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,SACEC,iBAAiB,EACjBC,eAAe,EACfC,iBAAiB,EACjBC,iBAAiB,QACZ,4BAA4B;AACnC,SACEC,YAAY,EACZC,QAAQ,EACRC,gBAAgB,QACX,6BAA6B;AACpC,OAAOC,MAAM,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,iBAA2B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxC,MAAMC,QAAQ,GAAGd,WAAW,CAAc,CAAC;EAC3C,MAAM;IAAEe,OAAO;IAAEC,KAAK;IAAEC,cAAc;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAGlB,WAAW,CACnEmB,KAAgB,IAAKA,KAAK,CAACC,KAC9B,CAAC;EAED,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACyB,SAAS,EAAEC,YAAY,CAAC,GAAG1B,QAAQ,CAAC;IACzC2B,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,MAAM,EAAE,EAAE;IACVC,OAAO,EAAE;EACX,CAAC,CAAC;EAEF/B,SAAS,CAAC,MAAM;IACdgB,QAAQ,CAACZ,iBAAiB,CAAC,CAAC,CAAC;IAC7BY,QAAQ,CAACX,eAAe,CAAC,CAAC,CAAC;EAC7B,CAAC,EAAE,CAACW,QAAQ,CAAC,CAAC;EAEd,MAAMgB,kBAAkB,GAAGA,CAAA,KAAM;IAC/BhB,QAAQ,CAACV,iBAAiB,CAACoB,SAAS,CAAC,CAAC;IACtCC,YAAY,CAAC;MAAEC,OAAO,EAAE,EAAE;MAAEC,YAAY,EAAE,EAAE;MAAEC,MAAM,EAAE,EAAE;MAAEC,OAAO,EAAE;IAAQ,CAAC,CAAC;IAC7EN,iBAAiB,CAAC,KAAK,CAAC;EAC1B,CAAC;EAED,oBACEZ,OAAA;IAAKoB,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBrB,OAAA;MAAKoB,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDrB,OAAA;QAAAqB,QAAA,gBACErB,OAAA;UAAIoB,SAAS,EAAC,+BAA+B;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtEzB,OAAA;UAAGoB,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAE1C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNzB,OAAA;QACE0B,OAAO,EAAEA,CAAA,KAAMd,iBAAiB,CAAC,IAAI,CAAE;QACvCQ,SAAS,EAAC,wNAAwN;QAAAC,QAAA,gBAElOrB,OAAA,CAACJ,QAAQ;UAACwB,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,cAEvC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENzB,OAAA;MAAKoB,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpDrB,OAAA;QAAKoB,SAAS,EAAC,+BAA+B;QAAAC,QAAA,eAC5CrB,OAAA;UAAKoB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BrB,OAAA;YAAIoB,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAAC;UAE9D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAEJlB,OAAO,gBACNP,OAAA;YAAKoB,SAAS,EAAC,uCAAuC;YAAAC,QAAA,eACpDrB,OAAA;cAAKoB,SAAS,EAAC;YAA8D;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjF,CAAC,gBAENzB,OAAA;YAAKoB,SAAS,EAAC,WAAW;YAAAC,QAAA,GACvBjB,OAAO,CAACuB,GAAG,CAAEV,MAAM,iBAClBjB,OAAA;cAEEoB,SAAS,EAAE,0DACT,CAAAd,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEsB,EAAE,MAAKX,MAAM,CAACW,EAAE,GAC5B,gCAAgC,GAChC,mCAAmC,EACtC;cACHF,OAAO,EAAEA,CAAA,KAAMvB,QAAQ,CAACT,iBAAiB,CAACuB,MAAM,CAAC,CAAE;cAAAI,QAAA,eAEnDrB,OAAA;gBAAKoB,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/CrB,OAAA;kBAAAqB,QAAA,gBACErB,OAAA;oBAAIoB,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,EAAEJ,MAAM,CAACD;kBAAY;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACzEzB,OAAA;oBAAGoB,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,GAAC,QAAM,EAACJ,MAAM,CAACF,OAAO;kBAAA;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpEzB,OAAA;oBAAGoB,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,GAAC,WAAS,EAACJ,MAAM,CAACC,OAAO;kBAAA;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpE,CAAC,eACNzB,OAAA,CAACH,gBAAgB;kBAACuB,SAAS,EAAC;gBAAuB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD;YAAC,GAfDR,MAAM,CAACW,EAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBX,CACN,CAAC,EAEDrB,OAAO,CAACyB,MAAM,KAAK,CAAC,iBACnB7B,OAAA;cAAKoB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BrB,OAAA,CAACL,YAAY;gBAACyB,SAAS,EAAC;cAAiC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5DzB,OAAA;gBAAIoB,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7EzB,OAAA;gBAAGoB,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzB,OAAA;QAAKoB,SAAS,EAAC,+BAA+B;QAAAC,QAAA,eAC5CrB,OAAA;UAAKoB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BrB,OAAA;YAAIoB,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAAC;UAE9D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAEJnB,cAAc,gBACbN,OAAA;YAAKoB,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBrB,OAAA;cAAAqB,QAAA,gBACErB,OAAA;gBAAOoB,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAC;cAE3D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRzB,OAAA;gBACE8B,IAAI,EAAC,MAAM;gBACXV,SAAS,EAAC,0IAA0I;gBACpJW,KAAK,EAAEzB,cAAc,CAACU,YAAa;gBACnCgB,QAAQ;cAAA;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENzB,OAAA;cAAAqB,QAAA,gBACErB,OAAA;gBAAOoB,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAC;cAE3D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRzB,OAAA;gBACE8B,IAAI,EAAC,MAAM;gBACXV,SAAS,EAAC,0IAA0I;gBACpJW,KAAK,EAAEzB,cAAc,CAACS,OAAQ;gBAC9BiB,QAAQ;cAAA;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENzB,OAAA;cAAAqB,QAAA,gBACErB,OAAA;gBAAOoB,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAC;cAE3D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRzB,OAAA;gBAAKoB,SAAS,EAAC,wDAAwD;gBAAAC,QAAA,eACrErB,OAAA,CAACF,MAAM;kBACLmC,MAAM,EAAC,OAAO;kBACdC,eAAe,EAAC,MAAM;kBACtBH,KAAK,EAAEzB,cAAc,CAACW,MAAO;kBAC7BkB,KAAK,EAAC,SAAS;kBACfC,OAAO,EAAE;oBACPJ,QAAQ,EAAE,IAAI;oBACdK,OAAO,EAAE;sBAAEC,OAAO,EAAE;oBAAM,CAAC;oBAC3BC,oBAAoB,EAAE,KAAK;oBAC3BC,QAAQ,EAAE,EAAE;oBACZC,UAAU,EAAE,2BAA2B;oBACvCC,WAAW,EAAE,IAAI;oBACjBC,OAAO,EAAE,IAAI;oBACbC,QAAQ,EAAE;kBACZ;gBAAE;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAENzB,OAAA;YAAKoB,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BrB,OAAA,CAACH,gBAAgB;cAACuB,SAAS,EAAC;YAAiC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChEzB,OAAA;cAAIoB,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClFzB,OAAA;cAAGoB,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE1C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzB,OAAA;MAAKoB,SAAS,EAAC,+BAA+B;MAAAC,QAAA,eAC5CrB,OAAA;QAAKoB,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BrB,OAAA;UAAIoB,SAAS,EAAC,+CAA+C;UAAAC,QAAA,EAAC;QAE9D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAEJpB,KAAK,CAACwB,MAAM,KAAK,CAAC,gBACjB7B,OAAA;UAAKoB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BrB,OAAA,CAACL,YAAY;YAACyB,SAAS,EAAC;UAAiC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5DzB,OAAA;YAAIoB,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9EzB,OAAA;YAAGoB,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAE1C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,gBAENzB,OAAA;UAAKoB,SAAS,EAAC,sDAAsD;UAAAC,QAAA,EAClEhB,KAAK,CAACsB,GAAG,CAAEkB,IAAI,iBACd7C,OAAA;YAAmBoB,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBAClErB,OAAA;cAAKoB,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDrB,OAAA;gBAAAqB,QAAA,gBACErB,OAAA;kBAAIoB,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAAEwB,IAAI,CAACjB;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC7DzB,OAAA;kBAAGoB,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GAAC,WAAS,EAACwB,IAAI,CAACC,OAAO;gBAAA;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChEzB,OAAA;kBAAGoB,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GAAC,WAAS,EAACwB,IAAI,CAAC3B,OAAO;gBAAA;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC,eACNzB,OAAA;gBAAKoB,SAAS,EAAE,wBACdyB,IAAI,CAACE,MAAM,KAAK,WAAW,GAAG,cAAc,GAAG,YAAY;cAC1D;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNzB,OAAA;cAAGoB,SAAS,EAAC,4BAA4B;cAAAC,QAAA,GAAC,aAC7B,EAAC,IAAI2B,IAAI,CAACH,IAAI,CAACI,SAAS,CAAC,CAACC,cAAc,CAAC,CAAC;YAAA;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC;UAAA,GAbIoB,IAAI,CAACjB,EAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcZ,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLd,cAAc,iBACbX,OAAA;MAAKoB,SAAS,EAAC,4EAA4E;MAAAC,QAAA,eACzFrB,OAAA;QAAKoB,SAAS,EAAC,0EAA0E;QAAAC,QAAA,eACvFrB,OAAA;UAAKoB,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBrB,OAAA;YAAIoB,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEjFzB,OAAA;YAAKoB,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBrB,OAAA;cAAAqB,QAAA,gBACErB,OAAA;gBAAOoB,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1EzB,OAAA;gBACE8B,IAAI,EAAC,MAAM;gBACXV,SAAS,EAAC,0IAA0I;gBACpJW,KAAK,EAAElB,SAAS,CAACE,OAAQ;gBACzBoC,QAAQ,EAAGC,CAAC,IAAKtC,YAAY,CAAC;kBAAE,GAAGD,SAAS;kBAAEE,OAAO,EAAEqC,CAAC,CAACC,MAAM,CAACtB;gBAAM,CAAC;cAAE;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENzB,OAAA;cAAAqB,QAAA,gBACErB,OAAA;gBAAOoB,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/EzB,OAAA;gBACE8B,IAAI,EAAC,MAAM;gBACXV,SAAS,EAAC,0IAA0I;gBACpJW,KAAK,EAAElB,SAAS,CAACG,YAAa;gBAC9BmC,QAAQ,EAAGC,CAAC,IAAKtC,YAAY,CAAC;kBAAE,GAAGD,SAAS;kBAAEG,YAAY,EAAEoC,CAAC,CAACC,MAAM,CAACtB;gBAAM,CAAC;cAAE;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENzB,OAAA;cAAAqB,QAAA,gBACErB,OAAA;gBAAOoB,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChFzB,OAAA;gBAAKoB,SAAS,EAAC,wDAAwD;gBAAAC,QAAA,eACrErB,OAAA,CAACF,MAAM;kBACLmC,MAAM,EAAC,OAAO;kBACdC,eAAe,EAAC,MAAM;kBACtBH,KAAK,EAAElB,SAAS,CAACI,MAAO;kBACxBkC,QAAQ,EAAGpB,KAAK,IAAKjB,YAAY,CAAC;oBAAE,GAAGD,SAAS;oBAAEI,MAAM,EAAEc,KAAK,IAAI;kBAAG,CAAC,CAAE;kBACzEI,KAAK,EAAC,SAAS;kBACfC,OAAO,EAAE;oBACPC,OAAO,EAAE;sBAAEC,OAAO,EAAE;oBAAM,CAAC;oBAC3BC,oBAAoB,EAAE,KAAK;oBAC3BC,QAAQ,EAAE,EAAE;oBACZC,UAAU,EAAE,2BAA2B;oBACvCC,WAAW,EAAE,IAAI;oBACjBC,OAAO,EAAE,IAAI;oBACbC,QAAQ,EAAE;kBACZ;gBAAE;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENzB,OAAA;YAAKoB,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9CrB,OAAA;cACE0B,OAAO,EAAEA,CAAA,KAAMd,iBAAiB,CAAC,KAAK,CAAE;cACxCQ,SAAS,EAAC,+DAA+D;cAAAC,QAAA,EAC1E;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTzB,OAAA;cACE0B,OAAO,EAAEP,kBAAmB;cAC5BC,SAAS,EAAC,+DAA+D;cAAAC,QAAA,EAC1E;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACvB,EAAA,CAjRID,iBAA2B;EAAA,QACdZ,WAAW,EAC+BC,WAAW;AAAA;AAAAgE,EAAA,GAFlErD,iBAA2B;AAmRjC,eAAeA,iBAAiB;AAAC,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}