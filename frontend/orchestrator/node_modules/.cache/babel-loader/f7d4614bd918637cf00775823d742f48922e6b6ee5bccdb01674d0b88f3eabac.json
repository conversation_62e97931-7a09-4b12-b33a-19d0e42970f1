{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/DatabaseAdmin.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { fetchDatabaseStats, executeQuery, clearQueryResult } from '../store/slices/databaseSlice';\nimport { CircleStackIcon, PlayIcon, TrashIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DatabaseAdmin = () => {\n  _s();\n  var _stats$total_resource, _stats$total_workflow, _stats$total_envoy_co;\n  const dispatch = useDispatch();\n  const {\n    stats,\n    loading,\n    error,\n    queryResult,\n    queryLoading\n  } = useSelector(state => state.database);\n  const [query, setQuery] = useState('');\n  useEffect(() => {\n    dispatch(fetchDatabaseStats());\n  }, [dispatch]);\n  const handleExecuteQuery = () => {\n    if (query.trim()) {\n      dispatch(executeQuery(query));\n    }\n  };\n  const handleClearResult = () => {\n    dispatch(clearQueryResult());\n  };\n  const sampleQueries = ['SELECT * FROM resource_cache LIMIT 10', 'SELECT provider, COUNT(*) as count FROM resource_cache GROUP BY provider', 'SELECT * FROM workflow_executions WHERE status = \"running\"', 'SELECT * FROM envoy_configs ORDER BY updated_at DESC LIMIT 5'];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold text-white\",\n        children: \"Database Administration\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-1 text-sm text-gray-400\",\n        children: \"Monitor database health and execute queries\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 overflow-hidden shadow rounded-lg\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-5\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(CircleStackIcon, {\n                className: \"h-6 w-6 text-blue-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 56,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-5 w-0 flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                  className: \"text-sm font-medium text-gray-400 truncate\",\n                  children: \"Total Resources\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 60,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                  className: \"text-lg font-medium text-white\",\n                  children: (stats === null || stats === void 0 ? void 0 : (_stats$total_resource = stats.total_resources) === null || _stats$total_resource === void 0 ? void 0 : _stats$total_resource.toLocaleString()) || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 61,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 59,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 overflow-hidden shadow rounded-lg\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-5\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(CircleStackIcon, {\n                className: \"h-6 w-6 text-green-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-5 w-0 flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                  className: \"text-sm font-medium text-gray-400 truncate\",\n                  children: \"Workflows\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 78,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                  className: \"text-lg font-medium text-white\",\n                  children: (stats === null || stats === void 0 ? void 0 : (_stats$total_workflow = stats.total_workflows) === null || _stats$total_workflow === void 0 ? void 0 : _stats$total_workflow.toLocaleString()) || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 79,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 overflow-hidden shadow rounded-lg\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-5\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(CircleStackIcon, {\n                className: \"h-6 w-6 text-purple-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 92,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-5 w-0 flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                  className: \"text-sm font-medium text-gray-400 truncate\",\n                  children: \"Envoy Configs\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 96,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                  className: \"text-lg font-medium text-white\",\n                  children: (stats === null || stats === void 0 ? void 0 : (_stats$total_envoy_co = stats.total_envoy_configs) === null || _stats$total_envoy_co === void 0 ? void 0 : _stats$total_envoy_co.toLocaleString()) || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 97,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 95,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 overflow-hidden shadow rounded-lg\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-5\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(CircleStackIcon, {\n                className: \"h-6 w-6 text-yellow-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-5 w-0 flex-1\",\n              children: /*#__PURE__*/_jsxDEV(\"dl\", {\n                children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                  className: \"text-sm font-medium text-gray-400 truncate\",\n                  children: \"Cache Hit Rate\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 114,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                  className: \"text-lg font-medium text-white\",\n                  children: stats !== null && stats !== void 0 && stats.cache_hit_rate ? `${stats.cache_hit_rate}%` : 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 115,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 shadow rounded-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-4 py-5 sm:p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg leading-6 font-medium text-white mb-4\",\n          children: \"Database Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center h-32\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"dl\", {\n          className: \"grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n              className: \"text-sm font-medium text-gray-400\",\n              children: \"Database Size\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n              className: \"mt-1 text-sm text-white\",\n              children: (stats === null || stats === void 0 ? void 0 : stats.database_size) || 'Unknown'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n              className: \"text-sm font-medium text-gray-400\",\n              children: \"Cache Size\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n              className: \"mt-1 text-sm text-white\",\n              children: (stats === null || stats === void 0 ? void 0 : stats.cache_size) || 'Unknown'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-800 shadow rounded-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-4 py-5 sm:p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg leading-6 font-medium text-white mb-4\",\n          children: \"SQL Query Interface\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-300 mb-2\",\n              children: \"SQL Query\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              className: \"block w-full h-32 px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white font-mono text-sm focus:outline-none focus:ring-2 focus:ring-cyan-500\",\n              value: query,\n              onChange: e => setQuery(e.target.value),\n              placeholder: \"Enter your SQL query here...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleExecuteQuery,\n              disabled: queryLoading || !query.trim(),\n              className: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 disabled:opacity-50\",\n              children: [/*#__PURE__*/_jsxDEV(PlayIcon, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this), queryLoading ? 'Executing...' : 'Execute Query']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this), queryResult && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleClearResult,\n              className: \"inline-flex items-center px-4 py-2 border border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-300 bg-gray-700 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500\",\n              children: [/*#__PURE__*/_jsxDEV(TrashIcon, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 19\n              }, this), \"Clear Result\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-300 mb-2\",\n              children: \"Sample Queries\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: sampleQueries.map((sampleQuery, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setQuery(sampleQuery),\n                className: \"block w-full text-left px-3 py-2 text-sm text-gray-300 bg-gray-700 hover:bg-gray-600 rounded border border-gray-600\",\n                children: sampleQuery\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 bg-red-50 border border-red-200 rounded-md p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-red-700\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 13\n        }, this), queryResult && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-md font-medium text-white mb-3\",\n            children: \"Query Results\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-700 rounded-md p-4 overflow-x-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"pre\", {\n              className: \"text-sm text-gray-300\",\n              children: JSON.stringify(queryResult, null, 2)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 5\n  }, this);\n};\n_s(DatabaseAdmin, \"X1uLks2abtt9Iys1vJVDaL9sfUw=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = DatabaseAdmin;\nexport default DatabaseAdmin;\nvar _c;\n$RefreshReg$(_c, \"DatabaseAdmin\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "fetchDatabaseStats", "execute<PERSON>uery", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "CircleStackIcon", "PlayIcon", "TrashIcon", "jsxDEV", "_jsxDEV", "DatabaseAdmin", "_s", "_stats$total_resource", "_stats$total_workflow", "_stats$total_envoy_co", "dispatch", "stats", "loading", "error", "query<PERSON><PERSON>ult", "queryLoading", "state", "database", "query", "<PERSON><PERSON><PERSON><PERSON>", "handleExecuteQuery", "trim", "handleClearResult", "sampleQueries", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "total_resources", "toLocaleString", "total_workflows", "total_envoy_configs", "cache_hit_rate", "database_size", "cache_size", "value", "onChange", "e", "target", "placeholder", "onClick", "disabled", "map", "sampleQuery", "index", "JSON", "stringify", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/DatabaseAdmin.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { RootState, AppDispatch } from '../store/store';\nimport { fetchDatabaseStats, executeQuery, clearQueryResult } from '../store/slices/databaseSlice';\nimport {\n  CircleStackIcon,\n  PlayIcon,\n  TrashIcon,\n} from '@heroicons/react/24/outline';\n\nconst DatabaseAdmin: React.FC = () => {\n  const dispatch = useDispatch<AppDispatch>();\n  const { stats, loading, error, queryResult, queryLoading } = useSelector(\n    (state: RootState) => state.database\n  );\n\n  const [query, setQuery] = useState('');\n\n  useEffect(() => {\n    dispatch(fetchDatabaseStats());\n  }, [dispatch]);\n\n  const handleExecuteQuery = () => {\n    if (query.trim()) {\n      dispatch(executeQuery(query));\n    }\n  };\n\n  const handleClearResult = () => {\n    dispatch(clearQueryResult());\n  };\n\n  const sampleQueries = [\n    'SELECT * FROM resource_cache LIMIT 10',\n    'SELECT provider, COUNT(*) as count FROM resource_cache GROUP BY provider',\n    'SELECT * FROM workflow_executions WHERE status = \"running\"',\n    'SELECT * FROM envoy_configs ORDER BY updated_at DESC LIMIT 5',\n  ];\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div>\n        <h1 className=\"text-2xl font-bold text-white\">Database Administration</h1>\n        <p className=\"mt-1 text-sm text-gray-400\">\n          Monitor database health and execute queries\n        </p>\n      </div>\n\n      {/* Database Stats */}\n      <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\">\n        <div className=\"bg-gray-800 overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <CircleStackIcon className=\"h-6 w-6 text-blue-400\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-400 truncate\">Total Resources</dt>\n                  <dd className=\"text-lg font-medium text-white\">\n                    {stats?.total_resources?.toLocaleString() || 0}\n                  </dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-gray-800 overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <CircleStackIcon className=\"h-6 w-6 text-green-400\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-400 truncate\">Workflows</dt>\n                  <dd className=\"text-lg font-medium text-white\">\n                    {stats?.total_workflows?.toLocaleString() || 0}\n                  </dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-gray-800 overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <CircleStackIcon className=\"h-6 w-6 text-purple-400\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-400 truncate\">Envoy Configs</dt>\n                  <dd className=\"text-lg font-medium text-white\">\n                    {stats?.total_envoy_configs?.toLocaleString() || 0}\n                  </dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-gray-800 overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <CircleStackIcon className=\"h-6 w-6 text-yellow-400\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-400 truncate\">Cache Hit Rate</dt>\n                  <dd className=\"text-lg font-medium text-white\">\n                    {stats?.cache_hit_rate ? `${stats.cache_hit_rate}%` : 'N/A'}\n                  </dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Database Info */}\n      <div className=\"bg-gray-800 shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <h3 className=\"text-lg leading-6 font-medium text-white mb-4\">Database Information</h3>\n          \n          {loading ? (\n            <div className=\"flex items-center justify-center h-32\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500\"></div>\n            </div>\n          ) : (\n            <dl className=\"grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2\">\n              <div>\n                <dt className=\"text-sm font-medium text-gray-400\">Database Size</dt>\n                <dd className=\"mt-1 text-sm text-white\">{stats?.database_size || 'Unknown'}</dd>\n              </div>\n              <div>\n                <dt className=\"text-sm font-medium text-gray-400\">Cache Size</dt>\n                <dd className=\"mt-1 text-sm text-white\">{stats?.cache_size || 'Unknown'}</dd>\n              </div>\n            </dl>\n          )}\n        </div>\n      </div>\n\n      {/* Query Interface */}\n      <div className=\"bg-gray-800 shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <h3 className=\"text-lg leading-6 font-medium text-white mb-4\">SQL Query Interface</h3>\n          \n          <div className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                SQL Query\n              </label>\n              <textarea\n                className=\"block w-full h-32 px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white font-mono text-sm focus:outline-none focus:ring-2 focus:ring-cyan-500\"\n                value={query}\n                onChange={(e) => setQuery(e.target.value)}\n                placeholder=\"Enter your SQL query here...\"\n              />\n            </div>\n\n            <div className=\"flex items-center space-x-3\">\n              <button\n                onClick={handleExecuteQuery}\n                disabled={queryLoading || !query.trim()}\n                className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 disabled:opacity-50\"\n              >\n                <PlayIcon className=\"h-4 w-4 mr-2\" />\n                {queryLoading ? 'Executing...' : 'Execute Query'}\n              </button>\n\n              {queryResult && (\n                <button\n                  onClick={handleClearResult}\n                  className=\"inline-flex items-center px-4 py-2 border border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-300 bg-gray-700 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500\"\n                >\n                  <TrashIcon className=\"h-4 w-4 mr-2\" />\n                  Clear Result\n                </button>\n              )}\n            </div>\n\n            {/* Sample Queries */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Sample Queries\n              </label>\n              <div className=\"space-y-2\">\n                {sampleQueries.map((sampleQuery, index) => (\n                  <button\n                    key={index}\n                    onClick={() => setQuery(sampleQuery)}\n                    className=\"block w-full text-left px-3 py-2 text-sm text-gray-300 bg-gray-700 hover:bg-gray-600 rounded border border-gray-600\"\n                  >\n                    {sampleQuery}\n                  </button>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {error && (\n            <div className=\"mt-4 bg-red-50 border border-red-200 rounded-md p-4\">\n              <div className=\"text-sm text-red-700\">{error}</div>\n            </div>\n          )}\n\n          {/* Query Results */}\n          {queryResult && (\n            <div className=\"mt-6\">\n              <h4 className=\"text-md font-medium text-white mb-3\">Query Results</h4>\n              <div className=\"bg-gray-700 rounded-md p-4 overflow-x-auto\">\n                <pre className=\"text-sm text-gray-300\">\n                  {JSON.stringify(queryResult, null, 2)}\n                </pre>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default DatabaseAdmin;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,SAASC,kBAAkB,EAAEC,YAAY,EAAEC,gBAAgB,QAAQ,+BAA+B;AAClG,SACEC,eAAe,EACfC,QAAQ,EACRC,SAAS,QACJ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,aAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;EACpC,MAAMC,QAAQ,GAAGf,WAAW,CAAc,CAAC;EAC3C,MAAM;IAAEgB,KAAK;IAAEC,OAAO;IAAEC,KAAK;IAAEC,WAAW;IAAEC;EAAa,CAAC,GAAGnB,WAAW,CACrEoB,KAAgB,IAAKA,KAAK,CAACC,QAC9B,CAAC;EAED,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAEtCD,SAAS,CAAC,MAAM;IACdiB,QAAQ,CAACb,kBAAkB,CAAC,CAAC,CAAC;EAChC,CAAC,EAAE,CAACa,QAAQ,CAAC,CAAC;EAEd,MAAMU,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIF,KAAK,CAACG,IAAI,CAAC,CAAC,EAAE;MAChBX,QAAQ,CAACZ,YAAY,CAACoB,KAAK,CAAC,CAAC;IAC/B;EACF,CAAC;EAED,MAAMI,iBAAiB,GAAGA,CAAA,KAAM;IAC9BZ,QAAQ,CAACX,gBAAgB,CAAC,CAAC,CAAC;EAC9B,CAAC;EAED,MAAMwB,aAAa,GAAG,CACpB,uCAAuC,EACvC,0EAA0E,EAC1E,4DAA4D,EAC5D,8DAA8D,CAC/D;EAED,oBACEnB,OAAA;IAAKoB,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBrB,OAAA;MAAAqB,QAAA,gBACErB,OAAA;QAAIoB,SAAS,EAAC,+BAA+B;QAAAC,QAAA,EAAC;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1EzB,OAAA;QAAGoB,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAE1C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNzB,OAAA;MAAKoB,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBACnErB,OAAA;QAAKoB,SAAS,EAAC,+CAA+C;QAAAC,QAAA,eAC5DrB,OAAA;UAAKoB,SAAS,EAAC,KAAK;UAAAC,QAAA,eAClBrB,OAAA;YAAKoB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCrB,OAAA;cAAKoB,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BrB,OAAA,CAACJ,eAAe;gBAACwB,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACNzB,OAAA;cAAKoB,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9BrB,OAAA;gBAAAqB,QAAA,gBACErB,OAAA;kBAAIoB,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC/EzB,OAAA;kBAAIoB,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAC3C,CAAAd,KAAK,aAALA,KAAK,wBAAAJ,qBAAA,GAALI,KAAK,CAAEmB,eAAe,cAAAvB,qBAAA,uBAAtBA,qBAAA,CAAwBwB,cAAc,CAAC,CAAC,KAAI;gBAAC;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENzB,OAAA;QAAKoB,SAAS,EAAC,+CAA+C;QAAAC,QAAA,eAC5DrB,OAAA;UAAKoB,SAAS,EAAC,KAAK;UAAAC,QAAA,eAClBrB,OAAA;YAAKoB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCrB,OAAA;cAAKoB,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BrB,OAAA,CAACJ,eAAe;gBAACwB,SAAS,EAAC;cAAwB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eACNzB,OAAA;cAAKoB,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9BrB,OAAA;gBAAAqB,QAAA,gBACErB,OAAA;kBAAIoB,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzEzB,OAAA;kBAAIoB,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAC3C,CAAAd,KAAK,aAALA,KAAK,wBAAAH,qBAAA,GAALG,KAAK,CAAEqB,eAAe,cAAAxB,qBAAA,uBAAtBA,qBAAA,CAAwBuB,cAAc,CAAC,CAAC,KAAI;gBAAC;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENzB,OAAA;QAAKoB,SAAS,EAAC,+CAA+C;QAAAC,QAAA,eAC5DrB,OAAA;UAAKoB,SAAS,EAAC,KAAK;UAAAC,QAAA,eAClBrB,OAAA;YAAKoB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCrB,OAAA;cAAKoB,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BrB,OAAA,CAACJ,eAAe;gBAACwB,SAAS,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACNzB,OAAA;cAAKoB,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9BrB,OAAA;gBAAAqB,QAAA,gBACErB,OAAA;kBAAIoB,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7EzB,OAAA;kBAAIoB,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAC3C,CAAAd,KAAK,aAALA,KAAK,wBAAAF,qBAAA,GAALE,KAAK,CAAEsB,mBAAmB,cAAAxB,qBAAA,uBAA1BA,qBAAA,CAA4BsB,cAAc,CAAC,CAAC,KAAI;gBAAC;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENzB,OAAA;QAAKoB,SAAS,EAAC,+CAA+C;QAAAC,QAAA,eAC5DrB,OAAA;UAAKoB,SAAS,EAAC,KAAK;UAAAC,QAAA,eAClBrB,OAAA;YAAKoB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCrB,OAAA;cAAKoB,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BrB,OAAA,CAACJ,eAAe;gBAACwB,SAAS,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACNzB,OAAA;cAAKoB,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9BrB,OAAA;gBAAAqB,QAAA,gBACErB,OAAA;kBAAIoB,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9EzB,OAAA;kBAAIoB,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAC3Cd,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEuB,cAAc,GAAG,GAAGvB,KAAK,CAACuB,cAAc,GAAG,GAAG;gBAAK;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzB,OAAA;MAAKoB,SAAS,EAAC,+BAA+B;MAAAC,QAAA,eAC5CrB,OAAA;QAAKoB,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BrB,OAAA;UAAIoB,SAAS,EAAC,+CAA+C;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAEtFjB,OAAO,gBACNR,OAAA;UAAKoB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,eACpDrB,OAAA;YAAKoB,SAAS,EAAC;UAA8D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CAAC,gBAENzB,OAAA;UAAIoB,SAAS,EAAC,iDAAiD;UAAAC,QAAA,gBAC7DrB,OAAA;YAAAqB,QAAA,gBACErB,OAAA;cAAIoB,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpEzB,OAAA;cAAIoB,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAE,CAAAd,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEwB,aAAa,KAAI;YAAS;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CAAC,eACNzB,OAAA;YAAAqB,QAAA,gBACErB,OAAA;cAAIoB,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjEzB,OAAA;cAAIoB,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAE,CAAAd,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEyB,UAAU,KAAI;YAAS;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACL;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzB,OAAA;MAAKoB,SAAS,EAAC,+BAA+B;MAAAC,QAAA,eAC5CrB,OAAA;QAAKoB,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BrB,OAAA;UAAIoB,SAAS,EAAC,+CAA+C;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEtFzB,OAAA;UAAKoB,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBrB,OAAA;YAAAqB,QAAA,gBACErB,OAAA;cAAOoB,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRzB,OAAA;cACEoB,SAAS,EAAC,4JAA4J;cACtKa,KAAK,EAAEnB,KAAM;cACboB,QAAQ,EAAGC,CAAC,IAAKpB,QAAQ,CAACoB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAC1CI,WAAW,EAAC;YAA8B;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENzB,OAAA;YAAKoB,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CrB,OAAA;cACEsC,OAAO,EAAEtB,kBAAmB;cAC5BuB,QAAQ,EAAE5B,YAAY,IAAI,CAACG,KAAK,CAACG,IAAI,CAAC,CAAE;cACxCG,SAAS,EAAC,4OAA4O;cAAAC,QAAA,gBAEtPrB,OAAA,CAACH,QAAQ;gBAACuB,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EACpCd,YAAY,GAAG,cAAc,GAAG,eAAe;YAAA;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,EAERf,WAAW,iBACVV,OAAA;cACEsC,OAAO,EAAEpB,iBAAkB;cAC3BE,SAAS,EAAC,wNAAwN;cAAAC,QAAA,gBAElOrB,OAAA,CAACF,SAAS;gBAACsB,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAExC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNzB,OAAA;YAAAqB,QAAA,gBACErB,OAAA;cAAOoB,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRzB,OAAA;cAAKoB,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBF,aAAa,CAACqB,GAAG,CAAC,CAACC,WAAW,EAAEC,KAAK,kBACpC1C,OAAA;gBAEEsC,OAAO,EAAEA,CAAA,KAAMvB,QAAQ,CAAC0B,WAAW,CAAE;gBACrCrB,SAAS,EAAC,qHAAqH;gBAAAC,QAAA,EAE9HoB;cAAW,GAJPC,KAAK;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKJ,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELhB,KAAK,iBACJT,OAAA;UAAKoB,SAAS,EAAC,qDAAqD;UAAAC,QAAA,eAClErB,OAAA;YAAKoB,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAEZ;UAAK;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CACN,EAGAf,WAAW,iBACVV,OAAA;UAAKoB,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBrB,OAAA;YAAIoB,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtEzB,OAAA;YAAKoB,SAAS,EAAC,4CAA4C;YAAAC,QAAA,eACzDrB,OAAA;cAAKoB,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EACnCsB,IAAI,CAACC,SAAS,CAAClC,WAAW,EAAE,IAAI,EAAE,CAAC;YAAC;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvB,EAAA,CAzNID,aAAuB;EAAA,QACVV,WAAW,EACiCC,WAAW;AAAA;AAAAqD,EAAA,GAFpE5C,aAAuB;AA2N7B,eAAeA,aAAa;AAAC,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}