/*! For license information please see main.8b2000d8.js.LICENSE.txt */
(()=>{"use strict";var e={43:(e,t,n)=>{e.exports=n(202)},82:(e,t)=>{var n,r=Symbol.for("react.element"),a=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),o=Symbol.for("react.provider"),c=Symbol.for("react.context"),u=Symbol.for("react.server_context"),d=Symbol.for("react.forward_ref"),f=Symbol.for("react.suspense"),m=Symbol.for("react.suspense_list"),h=Symbol.for("react.memo"),p=Symbol.for("react.lazy"),x=Symbol.for("react.offscreen");function g(e){if("object"===typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case s:case i:case l:case f:case m:return e;default:switch(e=e&&e.$$typeof){case u:case c:case d:case p:case h:case o:return e;default:return t}}case a:return t}}}n=Symbol.for("react.module.reference")},86:(e,t,n)=>{n(82)},153:(e,t,n)=>{var r=n(43),a=Symbol.for("react.element"),s=Symbol.for("react.fragment"),l=Object.prototype.hasOwnProperty,i=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,o={key:!0,ref:!0,__self:!0,__source:!0};function c(e,t,n){var r,s={},c=null,u=null;for(r in void 0!==n&&(c=""+n),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(u=t.ref),t)l.call(t,r)&&!o.hasOwnProperty(r)&&(s[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===s[r]&&(s[r]=t[r]);return{$$typeof:a,type:e,key:c,ref:u,props:s,_owner:i.current}}t.Fragment=s,t.jsx=c,t.jsxs=c},202:(e,t)=>{var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),l=Symbol.for("react.profiler"),i=Symbol.for("react.provider"),o=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),m=Symbol.iterator;var h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},p=Object.assign,x={};function g(e,t,n){this.props=e,this.context=t,this.refs=x,this.updater=n||h}function y(){}function v(e,t,n){this.props=e,this.context=t,this.refs=x,this.updater=n||h}g.prototype.isReactComponent={},g.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},g.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},y.prototype=g.prototype;var b=v.prototype=new y;b.constructor=v,p(b,g.prototype),b.isPureReactComponent=!0;var w=Array.isArray,j=Object.prototype.hasOwnProperty,N={current:null},k={key:!0,ref:!0,__self:!0,__source:!0};function S(e,t,r){var a,s={},l=null,i=null;if(null!=t)for(a in void 0!==t.ref&&(i=t.ref),void 0!==t.key&&(l=""+t.key),t)j.call(t,a)&&!k.hasOwnProperty(a)&&(s[a]=t[a]);var o=arguments.length-2;if(1===o)s.children=r;else if(1<o){for(var c=Array(o),u=0;u<o;u++)c[u]=arguments[u+2];s.children=c}if(e&&e.defaultProps)for(a in o=e.defaultProps)void 0===s[a]&&(s[a]=o[a]);return{$$typeof:n,type:e,key:l,ref:i,props:s,_owner:N.current}}function E(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var C=/\/+/g;function O(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function P(e,t,a,s,l){var i=typeof e;"undefined"!==i&&"boolean"!==i||(e=null);var o=!1;if(null===e)o=!0;else switch(i){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case n:case r:o=!0}}if(o)return l=l(o=e),e=""===s?"."+O(o,0):s,w(l)?(a="",null!=e&&(a=e.replace(C,"$&/")+"/"),P(l,t,a,"",(function(e){return e}))):null!=l&&(E(l)&&(l=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(l,a+(!l.key||o&&o.key===l.key?"":(""+l.key).replace(C,"$&/")+"/")+e)),t.push(l)),1;if(o=0,s=""===s?".":s+":",w(e))for(var c=0;c<e.length;c++){var u=s+O(i=e[c],c);o+=P(i,t,a,u,l)}else if(u=function(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=m&&e[m]||e["@@iterator"])?e:null}(e),"function"===typeof u)for(e=u.call(e),c=0;!(i=e.next()).done;)o+=P(i=i.value,t,a,u=s+O(i,c++),l);else if("object"===i)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function _(e,t,n){if(null==e)return e;var r=[],a=0;return P(e,r,"","",(function(e){return t.call(n,e,a++)})),r}function R(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var L={current:null},T={transition:null},A={ReactCurrentDispatcher:L,ReactCurrentBatchConfig:T,ReactCurrentOwner:N};function D(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:_,forEach:function(e,t,n){_(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return _(e,(function(){t++})),t},toArray:function(e){return _(e,(function(e){return e}))||[]},only:function(e){if(!E(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=g,t.Fragment=a,t.Profiler=l,t.PureComponent=v,t.StrictMode=s,t.Suspense=u,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=A,t.act=D,t.cloneElement=function(e,t,r){if(null===e||void 0===e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var a=p({},e.props),s=e.key,l=e.ref,i=e._owner;if(null!=t){if(void 0!==t.ref&&(l=t.ref,i=N.current),void 0!==t.key&&(s=""+t.key),e.type&&e.type.defaultProps)var o=e.type.defaultProps;for(c in t)j.call(t,c)&&!k.hasOwnProperty(c)&&(a[c]=void 0===t[c]&&void 0!==o?o[c]:t[c])}var c=arguments.length-2;if(1===c)a.children=r;else if(1<c){o=Array(c);for(var u=0;u<c;u++)o[u]=arguments[u+2];a.children=o}return{$$typeof:n,type:e.type,key:s,ref:l,props:a,_owner:i}},t.createContext=function(e){return(e={$$typeof:o,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:i,_context:e},e.Consumer=e},t.createElement=S,t.createFactory=function(e){var t=S.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:c,render:e}},t.isValidElement=E,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:R}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=T.transition;T.transition={};try{e()}finally{T.transition=t}},t.unstable_act=D,t.useCallback=function(e,t){return L.current.useCallback(e,t)},t.useContext=function(e){return L.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return L.current.useDeferredValue(e)},t.useEffect=function(e,t){return L.current.useEffect(e,t)},t.useId=function(){return L.current.useId()},t.useImperativeHandle=function(e,t,n){return L.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return L.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return L.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return L.current.useMemo(e,t)},t.useReducer=function(e,t,n){return L.current.useReducer(e,t,n)},t.useRef=function(e){return L.current.useRef(e)},t.useState=function(e){return L.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return L.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return L.current.useTransition()},t.version="18.3.1"},219:(e,t,n)=>{var r=n(763),a={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},s={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},l={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},i={};function o(e){return r.isMemo(e)?l:i[e.$$typeof]||a}i[r.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},i[r.Memo]=l;var c=Object.defineProperty,u=Object.getOwnPropertyNames,d=Object.getOwnPropertySymbols,f=Object.getOwnPropertyDescriptor,m=Object.getPrototypeOf,h=Object.prototype;e.exports=function e(t,n,r){if("string"!==typeof n){if(h){var a=m(n);a&&a!==h&&e(t,a,r)}var l=u(n);d&&(l=l.concat(d(n)));for(var i=o(t),p=o(n),x=0;x<l.length;++x){var g=l[x];if(!s[g]&&(!r||!r[g])&&(!p||!p[g])&&(!i||!i[g])){var y=f(n,g);try{c(t,g,y)}catch(v){}}}}return t}},234:(e,t)=>{function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<s(a,t)))break e;e[r]=t,e[n]=a,n=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,l=a>>>1;r<l;){var i=2*(r+1)-1,o=e[i],c=i+1,u=e[c];if(0>s(o,n))c<a&&0>s(u,o)?(e[r]=u,e[c]=n,r=c):(e[r]=o,e[i]=n,r=i);else{if(!(c<a&&0>s(u,n)))break e;e[r]=u,e[c]=n,r=c}}}return t}function s(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"===typeof performance&&"function"===typeof performance.now){var l=performance;t.unstable_now=function(){return l.now()}}else{var i=Date,o=i.now();t.unstable_now=function(){return i.now()-o}}var c=[],u=[],d=1,f=null,m=3,h=!1,p=!1,x=!1,g="function"===typeof setTimeout?setTimeout:null,y="function"===typeof clearTimeout?clearTimeout:null,v="undefined"!==typeof setImmediate?setImmediate:null;function b(e){for(var t=r(u);null!==t;){if(null===t.callback)a(u);else{if(!(t.startTime<=e))break;a(u),t.sortIndex=t.expirationTime,n(c,t)}t=r(u)}}function w(e){if(x=!1,b(e),!p)if(null!==r(c))p=!0,T(j);else{var t=r(u);null!==t&&A(w,t.startTime-e)}}function j(e,n){p=!1,x&&(x=!1,y(E),E=-1),h=!0;var s=m;try{for(b(n),f=r(c);null!==f&&(!(f.expirationTime>n)||e&&!P());){var l=f.callback;if("function"===typeof l){f.callback=null,m=f.priorityLevel;var i=l(f.expirationTime<=n);n=t.unstable_now(),"function"===typeof i?f.callback=i:f===r(c)&&a(c),b(n)}else a(c);f=r(c)}if(null!==f)var o=!0;else{var d=r(u);null!==d&&A(w,d.startTime-n),o=!1}return o}finally{f=null,m=s,h=!1}}"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var N,k=!1,S=null,E=-1,C=5,O=-1;function P(){return!(t.unstable_now()-O<C)}function _(){if(null!==S){var e=t.unstable_now();O=e;var n=!0;try{n=S(!0,e)}finally{n?N():(k=!1,S=null)}}else k=!1}if("function"===typeof v)N=function(){v(_)};else if("undefined"!==typeof MessageChannel){var R=new MessageChannel,L=R.port2;R.port1.onmessage=_,N=function(){L.postMessage(null)}}else N=function(){g(_,0)};function T(e){S=e,k||(k=!0,N())}function A(e,n){E=g((function(){e(t.unstable_now())}),n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){p||h||(p=!0,T(j))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):C=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return m},t.unstable_getFirstCallbackNode=function(){return r(c)},t.unstable_next=function(e){switch(m){case 1:case 2:case 3:var t=3;break;default:t=m}var n=m;m=t;try{return e()}finally{m=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=m;m=e;try{return t()}finally{m=n}},t.unstable_scheduleCallback=function(e,a,s){var l=t.unstable_now();switch("object"===typeof s&&null!==s?s="number"===typeof(s=s.delay)&&0<s?l+s:l:s=l,e){case 1:var i=-1;break;case 2:i=250;break;case 5:i=1073741823;break;case 4:i=1e4;break;default:i=5e3}return e={id:d++,callback:a,priorityLevel:e,startTime:s,expirationTime:i=s+i,sortIndex:-1},s>l?(e.sortIndex=s,n(u,e),null===r(c)&&e===r(u)&&(x?(y(E),E=-1):x=!0,A(w,s-l))):(e.sortIndex=i,n(c,e),p||h||(p=!0,T(j))),e},t.unstable_shouldYield=P,t.unstable_wrapCallback=function(e){var t=m;return function(){var n=m;m=t;try{return e.apply(this,arguments)}finally{m=n}}}},330:(e,t,n)=>{var r=n(43);var a="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t},s=r.useState,l=r.useEffect,i=r.useLayoutEffect,o=r.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!a(e,n)}catch(r){return!0}}var u="undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=s({inst:{value:n,getSnapshot:t}}),a=r[0].inst,u=r[1];return i((function(){a.value=n,a.getSnapshot=t,c(a)&&u({inst:a})}),[e,n,t]),l((function(){return c(a)&&u({inst:a}),e((function(){c(a)&&u({inst:a})}))}),[e]),o(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:u},391:(e,t,n)=>{var r=n(950);t.createRoot=r.createRoot,t.hydrateRoot=r.hydrateRoot},443:(e,t,n)=>{e.exports=n(717)},461:(e,t,n)=>{e.exports=n(330)},579:(e,t,n)=>{e.exports=n(153)},717:(e,t,n)=>{var r=n(43),a=n(461);var s="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t},l=a.useSyncExternalStore,i=r.useRef,o=r.useEffect,c=r.useMemo,u=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,a){var d=i(null);if(null===d.current){var f={hasValue:!1,value:null};d.current=f}else f=d.current;d=c((function(){function e(e){if(!o){if(o=!0,l=e,e=r(e),void 0!==a&&f.hasValue){var t=f.value;if(a(t,e))return i=t}return i=e}if(t=i,s(l,e))return t;var n=r(e);return void 0!==a&&a(t,n)?(l=e,t):(l=e,i=n)}var l,i,o=!1,c=void 0===n?null:n;return[function(){return e(t())},null===c?void 0:function(){return e(c())}]}),[t,n,r,a]);var m=l(e,d[0],d[1]);return o((function(){f.hasValue=!0,f.value=m}),[m]),u(m),m}},730:(e,t,n)=>{var r=n(43),a=n(853);function s(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var l=new Set,i={};function o(e,t){c(e,t),c(e+"Capture",t)}function c(e,t){for(i[e]=t,e=0;e<t.length;e++)l.add(t[e])}var u=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),d=Object.prototype.hasOwnProperty,f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,m={},h={};function p(e,t,n,r,a,s,l){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=a,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=s,this.removeEmptyString=l}var x={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach((function(e){x[e]=new p(e,0,!1,e,null,!1,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t=e[0];x[t]=new p(t,1,!1,e[1],null,!1,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){x[e]=new p(e,2,!1,e.toLowerCase(),null,!1,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){x[e]=new p(e,2,!1,e,null,!1,!1)})),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach((function(e){x[e]=new p(e,3,!1,e.toLowerCase(),null,!1,!1)})),["checked","multiple","muted","selected"].forEach((function(e){x[e]=new p(e,3,!0,e,null,!1,!1)})),["capture","download"].forEach((function(e){x[e]=new p(e,4,!1,e,null,!1,!1)})),["cols","rows","size","span"].forEach((function(e){x[e]=new p(e,6,!1,e,null,!1,!1)})),["rowSpan","start"].forEach((function(e){x[e]=new p(e,5,!1,e.toLowerCase(),null,!1,!1)}));var g=/[\-:]([a-z])/g;function y(e){return e[1].toUpperCase()}function v(e,t,n,r){var a=x.hasOwnProperty(t)?x[t]:null;(null!==a?0!==a.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null===t||"undefined"===typeof t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,a,r)&&(n=null),r||null===a?function(e){return!!d.call(h,e)||!d.call(m,e)&&(f.test(e)?h[e]=!0:(m[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):a.mustUseProperty?e[a.propertyName]=null===n?3!==a.type&&"":n:(t=a.attributeName,r=a.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(a=a.type)||4===a&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach((function(e){var t=e.replace(g,y);x[t]=new p(t,1,!1,e,null,!1,!1)})),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach((function(e){var t=e.replace(g,y);x[t]=new p(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(g,y);x[t]=new p(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)})),["tabIndex","crossOrigin"].forEach((function(e){x[e]=new p(e,1,!1,e.toLowerCase(),null,!1,!1)})),x.xlinkHref=new p("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach((function(e){x[e]=new p(e,1,!1,e.toLowerCase(),null,!0,!0)}));var b=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,w=Symbol.for("react.element"),j=Symbol.for("react.portal"),N=Symbol.for("react.fragment"),k=Symbol.for("react.strict_mode"),S=Symbol.for("react.profiler"),E=Symbol.for("react.provider"),C=Symbol.for("react.context"),O=Symbol.for("react.forward_ref"),P=Symbol.for("react.suspense"),_=Symbol.for("react.suspense_list"),R=Symbol.for("react.memo"),L=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var T=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var A=Symbol.iterator;function D(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=A&&e[A]||e["@@iterator"])?e:null}var M,I=Object.assign;function F(e){if(void 0===M)try{throw Error()}catch(An){var t=An.stack.trim().match(/\n( *(at )?)/);M=t&&t[1]||""}return"\n"+M+e}var z=!1;function U(e,t){if(!e||z)return"";z=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(Fn){var r=Fn}Reflect.construct(e,[],t)}else{try{t.call()}catch(Fn){r=Fn}e.call(t.prototype)}else{try{throw Error()}catch(Fn){r=Fn}e()}}catch(Fn){if(Fn&&r&&"string"===typeof Fn.stack){for(var a=Fn.stack.split("\n"),s=r.stack.split("\n"),l=a.length-1,i=s.length-1;1<=l&&0<=i&&a[l]!==s[i];)i--;for(;1<=l&&0<=i;l--,i--)if(a[l]!==s[i]){if(1!==l||1!==i)do{if(l--,0>--i||a[l]!==s[i]){var o="\n"+a[l].replace(" at new "," at ");return e.displayName&&o.includes("<anonymous>")&&(o=o.replace("<anonymous>",e.displayName)),o}}while(1<=l&&0<=i);break}}}finally{z=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?F(e):""}function B(e){switch(e.tag){case 5:return F(e.type);case 16:return F("Lazy");case 13:return F("Suspense");case 19:return F("SuspenseList");case 0:case 2:case 15:return e=U(e.type,!1);case 11:return e=U(e.type.render,!1);case 1:return e=U(e.type,!0);default:return""}}function W(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case N:return"Fragment";case j:return"Portal";case S:return"Profiler";case k:return"StrictMode";case P:return"Suspense";case _:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case C:return(e.displayName||"Context")+".Consumer";case E:return(e._context.displayName||"Context")+".Provider";case O:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case R:return null!==(t=e.displayName||null)?t:W(e.type)||"Memo";case L:t=e._payload,e=e._init;try{return W(e(t))}catch(An){}}return null}function q(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return W(t);case 8:return t===k?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"===typeof t)return t.displayName||t.name||null;if("string"===typeof t)return t}return null}function V(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function $(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function H(e){e._valueTracker||(e._valueTracker=function(e){var t=$(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var a=n.get,s=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,s.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function Q(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=$(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function K(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(Wn){return e.body}}function J(e,t){var n=t.checked;return I({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function X(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=V(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function Z(e,t){null!=(t=t.checked)&&v(e,"checked",t,!1)}function G(e,t){Z(e,t);var n=V(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,V(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function Y(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&K(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+V(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(s(91));return I({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ae(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(s(92));if(te(n)){if(1<n.length)throw Error(s(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:V(n)}}function se(e,t){var n=V(t.value),r=V(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function le(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function ie(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function oe(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?ie(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ce,ue=function(e){return"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(t,n,r,a){MSApp.execUnsafeLocalFunction((function(){return e(t,n)}))}:e}((function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ce=ce||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ce.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}}));function de(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var fe={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},me=["Webkit","ms","Moz","O"];function he(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||fe.hasOwnProperty(e)&&fe[e]?(""+t).trim():t+"px"}function pe(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),a=he(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,a):e[n]=a}}Object.keys(fe).forEach((function(e){me.forEach((function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),fe[t]=fe[e]}))}));var xe=I({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ge(e,t){if(t){if(xe[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(s(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(s(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(s(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(s(62))}}function ye(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ve=null;function be(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var we=null,je=null,Ne=null;function ke(e){if(e=_a(e)){if("function"!==typeof we)throw Error(s(280));var t=e.stateNode;t&&(t=La(t),we(e.stateNode,e.type,t))}}function Se(e){je?Ne?Ne.push(e):Ne=[e]:je=e}function Ee(){if(je){var e=je,t=Ne;if(Ne=je=null,ke(e),t)for(e=0;e<t.length;e++)ke(t[e])}}function Ce(e,t){return e(t)}function Oe(){}var Pe=!1;function _e(e,t,n){if(Pe)return e(t,n);Pe=!0;try{return Ce(e,t,n)}finally{Pe=!1,(null!==je||null!==Ne)&&(Oe(),Ee())}}function Re(e,t){var n=e.stateNode;if(null===n)return null;var r=La(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(s(231,t,typeof n));return n}var Le=!1;if(u)try{var Te={};Object.defineProperty(Te,"passive",{get:function(){Le=!0}}),window.addEventListener("test",Te,Te),window.removeEventListener("test",Te,Te)}catch(Ln){Le=!1}function Ae(e,t,n,r,a,s,l,i,o){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(qn){this.onError(qn)}}var De=!1,Me=null,Ie=!1,Fe=null,ze={onError:function(e){De=!0,Me=e}};function Ue(e,t,n,r,a,s,l,i,o){De=!1,Me=null,Ae.apply(ze,arguments)}function Be(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function We(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function qe(e){if(Be(e)!==e)throw Error(s(188))}function Ve(e){return e=function(e){var t=e.alternate;if(!t){if(null===(t=Be(e)))throw Error(s(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var l=a.alternate;if(null===l){if(null!==(r=a.return)){n=r;continue}break}if(a.child===l.child){for(l=a.child;l;){if(l===n)return qe(a),e;if(l===r)return qe(a),t;l=l.sibling}throw Error(s(188))}if(n.return!==r.return)n=a,r=l;else{for(var i=!1,o=a.child;o;){if(o===n){i=!0,n=a,r=l;break}if(o===r){i=!0,r=a,n=l;break}o=o.sibling}if(!i){for(o=l.child;o;){if(o===n){i=!0,n=l,r=a;break}if(o===r){i=!0,r=l,n=a;break}o=o.sibling}if(!i)throw Error(s(189))}}if(n.alternate!==r)throw Error(s(190))}if(3!==n.tag)throw Error(s(188));return n.stateNode.current===n?e:t}(e),null!==e?$e(e):null}function $e(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=$e(e);if(null!==t)return t;e=e.sibling}return null}var He=a.unstable_scheduleCallback,Qe=a.unstable_cancelCallback,Ke=a.unstable_shouldYield,Je=a.unstable_requestPaint,Xe=a.unstable_now,Ze=a.unstable_getCurrentPriorityLevel,Ge=a.unstable_ImmediatePriority,Ye=a.unstable_UserBlockingPriority,et=a.unstable_NormalPriority,tt=a.unstable_LowPriority,nt=a.unstable_IdlePriority,rt=null,at=null;var st=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(lt(e)/it|0)|0},lt=Math.log,it=Math.LN2;var ot=64,ct=4194304;function ut(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function dt(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,a=e.suspendedLanes,s=e.pingedLanes,l=268435455&n;if(0!==l){var i=l&~a;0!==i?r=ut(i):0!==(s&=l)&&(r=ut(s))}else 0!==(l=n&~a)?r=ut(l):0!==s&&(r=ut(s));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&a)&&((a=r&-r)>=(s=t&-t)||16===a&&0!==(4194240&s)))return t;if(0!==(4&r)&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)a=1<<(n=31-st(t)),r|=e[n],t&=~a;return r}function ft(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function mt(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function ht(){var e=ot;return 0===(4194240&(ot<<=1))&&(ot=64),e}function pt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function xt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-st(t)]=n}function gt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-st(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}var yt=0;function vt(e){return 1<(e&=-e)?4<e?0!==(268435455&e)?16:536870912:4:1}var bt,wt,jt,Nt,kt,St=!1,Et=[],Ct=null,Ot=null,Pt=null,_t=new Map,Rt=new Map,Lt=[],Tt="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function At(e,t){switch(e){case"focusin":case"focusout":Ct=null;break;case"dragenter":case"dragleave":Ot=null;break;case"mouseover":case"mouseout":Pt=null;break;case"pointerover":case"pointerout":_t.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Rt.delete(t.pointerId)}}function Dt(e,t,n,r,a,s){return null===e||e.nativeEvent!==s?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:s,targetContainers:[a]},null!==t&&(null!==(t=_a(t))&&wt(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function Mt(e){var t=Pa(e.target);if(null!==t){var n=Be(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=We(n)))return e.blockedOn=t,void kt(e.priority,(function(){jt(n)}))}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function It(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Kt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=_a(n))&&wt(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);ve=r,n.target.dispatchEvent(r),ve=null,t.shift()}return!0}function Ft(e,t,n){It(e)&&n.delete(t)}function zt(){St=!1,null!==Ct&&It(Ct)&&(Ct=null),null!==Ot&&It(Ot)&&(Ot=null),null!==Pt&&It(Pt)&&(Pt=null),_t.forEach(Ft),Rt.forEach(Ft)}function Ut(e,t){e.blockedOn===t&&(e.blockedOn=null,St||(St=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,zt)))}function Bt(e){function t(t){return Ut(t,e)}if(0<Et.length){Ut(Et[0],e);for(var n=1;n<Et.length;n++){var r=Et[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==Ct&&Ut(Ct,e),null!==Ot&&Ut(Ot,e),null!==Pt&&Ut(Pt,e),_t.forEach(t),Rt.forEach(t),n=0;n<Lt.length;n++)(r=Lt[n]).blockedOn===e&&(r.blockedOn=null);for(;0<Lt.length&&null===(n=Lt[0]).blockedOn;)Mt(n),null===n.blockedOn&&Lt.shift()}var Wt=b.ReactCurrentBatchConfig,qt=!0;function Vt(e,t,n,r){var a=yt,s=Wt.transition;Wt.transition=null;try{yt=1,Ht(e,t,n,r)}finally{yt=a,Wt.transition=s}}function $t(e,t,n,r){var a=yt,s=Wt.transition;Wt.transition=null;try{yt=4,Ht(e,t,n,r)}finally{yt=a,Wt.transition=s}}function Ht(e,t,n,r){if(qt){var a=Kt(e,t,n,r);if(null===a)ta(e,t,r,Qt,n),At(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return Ct=Dt(Ct,e,t,n,r,a),!0;case"dragenter":return Ot=Dt(Ot,e,t,n,r,a),!0;case"mouseover":return Pt=Dt(Pt,e,t,n,r,a),!0;case"pointerover":var s=a.pointerId;return _t.set(s,Dt(_t.get(s)||null,e,t,n,r,a)),!0;case"gotpointercapture":return s=a.pointerId,Rt.set(s,Dt(Rt.get(s)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(At(e,r),4&t&&-1<Tt.indexOf(e)){for(;null!==a;){var s=_a(a);if(null!==s&&bt(s),null===(s=Kt(e,t,n,r))&&ta(e,t,r,Qt,n),s===a)break;a=s}null!==a&&r.stopPropagation()}else ta(e,t,r,null,n)}}var Qt=null;function Kt(e,t,n,r){if(Qt=null,null!==(e=Pa(e=be(r))))if(null===(t=Be(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=We(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Qt=e,null}function Jt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Ze()){case Ge:return 1;case Ye:return 4;case et:case tt:return 16;case nt:return 536870912;default:return 16}default:return 16}}var Xt=null,Zt=null,Gt=null;function Yt(){if(Gt)return Gt;var e,t,n=Zt,r=n.length,a="value"in Xt?Xt.value:Xt.textContent,s=a.length;for(e=0;e<r&&n[e]===a[e];e++);var l=r-e;for(t=1;t<=l&&n[r-t]===a[s-t];t++);return Gt=a.slice(e,1<t?1-t:void 0)}function en(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function tn(){return!0}function nn(){return!1}function rn(e){function t(t,n,r,a,s){for(var l in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=s,this.currentTarget=null,e)e.hasOwnProperty(l)&&(t=e[l],this[l]=t?t(a):a[l]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?tn:nn,this.isPropagationStopped=nn,this}return I(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=tn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=tn)},persist:function(){},isPersistent:tn}),t}var an,sn,ln,on={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},cn=rn(on),un=I({},on,{view:0,detail:0}),dn=rn(un),fn=I({},un,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:kn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ln&&(ln&&"mousemove"===e.type?(an=e.screenX-ln.screenX,sn=e.screenY-ln.screenY):sn=an=0,ln=e),an)},movementY:function(e){return"movementY"in e?e.movementY:sn}}),mn=rn(fn),hn=rn(I({},fn,{dataTransfer:0})),pn=rn(I({},un,{relatedTarget:0})),xn=rn(I({},on,{animationName:0,elapsedTime:0,pseudoElement:0})),gn=I({},on,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),yn=rn(gn),vn=rn(I({},on,{data:0})),bn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},wn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},jn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Nn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=jn[e])&&!!t[e]}function kn(){return Nn}var Sn=I({},un,{key:function(e){if(e.key){var t=bn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=en(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?wn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:kn,charCode:function(e){return"keypress"===e.type?en(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?en(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),En=rn(Sn),Pn=rn(I({},fn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),_n=rn(I({},un,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:kn})),Rn=rn(I({},on,{propertyName:0,elapsedTime:0,pseudoElement:0})),Dn=I({},fn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Mn=rn(Dn),In=[9,13,27,32],Bn=u&&"CompositionEvent"in window,Vn=null;u&&"documentMode"in document&&(Vn=document.documentMode);var $n=u&&"TextEvent"in window&&!Vn,Hn=u&&(!Bn||Vn&&8<Vn&&11>=Vn),Qn=String.fromCharCode(32),Kn=!1;function Jn(e,t){switch(e){case"keyup":return-1!==In.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Xn(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Zn=!1;var Gn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Yn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Gn[e.type]:"textarea"===t}function er(e,t,n,r){Se(r),0<(t=ra(t,"onChange")).length&&(n=new cn("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var tr=null,rr=null;function ar(e){Jr(e,0)}function sr(e){if(Q(Ra(e)))return e}function lr(e,t){if("change"===e)return t}var or=!1;if(u){var cr;if(u){var ur="oninput"in document;if(!ur){var dr=document.createElement("div");dr.setAttribute("oninput","return;"),ur="function"===typeof dr.oninput}cr=ur}else cr=!1;or=cr&&(!document.documentMode||9<document.documentMode)}function fr(){tr&&(tr.detachEvent("onpropertychange",mr),rr=tr=null)}function mr(e){if("value"===e.propertyName&&sr(rr)){var t=[];er(t,rr,e,be(e)),_e(ar,t)}}function hr(e,t,n){"focusin"===e?(fr(),rr=n,(tr=t).attachEvent("onpropertychange",mr)):"focusout"===e&&fr()}function pr(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return sr(rr)}function xr(e,t){if("click"===e)return sr(t)}function gr(e,t){if("input"===e||"change"===e)return sr(t)}var yr="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function vr(e,t){if(yr(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!d.call(t,a)||!yr(e[a],t[a]))return!1}return!0}function br(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function wr(e,t){var n,r=br(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=br(r)}}function jr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?jr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function Nr(){for(var e=window,t=K();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(zn){n=!1}if(!n)break;t=K((e=t.contentWindow).document)}return t}function kr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function Sr(e){var t=Nr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&jr(n.ownerDocument.documentElement,n)){if(null!==r&&kr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var a=n.textContent.length,s=Math.min(r.start,a);r=void 0===r.end?s:Math.min(r.end,a),!e.extend&&s>r&&(a=r,r=s,s=a),a=wr(n,s);var l=wr(n,r);a&&l&&(1!==e.rangeCount||e.anchorNode!==a.node||e.anchorOffset!==a.offset||e.focusNode!==l.node||e.focusOffset!==l.offset)&&((t=t.createRange()).setStart(a.node,a.offset),e.removeAllRanges(),s>r?(e.addRange(t),e.extend(l.node,l.offset)):(t.setEnd(l.node,l.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"===typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Er=u&&"documentMode"in document&&11>=document.documentMode,Cr=null,Or=null,Pr=null,_r=!1;function Rr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;_r||null==Cr||Cr!==K(r)||("selectionStart"in(r=Cr)&&kr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},Pr&&vr(Pr,r)||(Pr=r,0<(r=ra(Or,"onSelect")).length&&(t=new cn("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Cr)))}function Lr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Tr={animationend:Lr("Animation","AnimationEnd"),animationiteration:Lr("Animation","AnimationIteration"),animationstart:Lr("Animation","AnimationStart"),transitionend:Lr("Transition","TransitionEnd")},Ar={},Dr={};function Mr(e){if(Ar[e])return Ar[e];if(!Tr[e])return e;var t,n=Tr[e];for(t in n)if(n.hasOwnProperty(t)&&t in Dr)return Ar[e]=n[t];return e}u&&(Dr=document.createElement("div").style,"AnimationEvent"in window||(delete Tr.animationend.animation,delete Tr.animationiteration.animation,delete Tr.animationstart.animation),"TransitionEvent"in window||delete Tr.transitionend.transition);var Ir=Mr("animationend"),Fr=Mr("animationiteration"),zr=Mr("animationstart"),Ur=Mr("transitionend"),Br=new Map,Wr="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function qr(e,t){Br.set(e,t),o(t,[e])}for(var Vr=0;Vr<Wr.length;Vr++){var $r=Wr[Vr];qr($r.toLowerCase(),"on"+($r[0].toUpperCase()+$r.slice(1)))}qr(Ir,"onAnimationEnd"),qr(Fr,"onAnimationIteration"),qr(zr,"onAnimationStart"),qr("dblclick","onDoubleClick"),qr("focusin","onFocus"),qr("focusout","onBlur"),qr(Ur,"onTransitionEnd"),c("onMouseEnter",["mouseout","mouseover"]),c("onMouseLeave",["mouseout","mouseover"]),c("onPointerEnter",["pointerout","pointerover"]),c("onPointerLeave",["pointerout","pointerover"]),o("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),o("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),o("onBeforeInput",["compositionend","keypress","textInput","paste"]),o("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),o("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),o("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Hr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Qr=new Set("cancel close invalid load scroll toggle".split(" ").concat(Hr));function Kr(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,a,l,i,o,c){if(Ue.apply(this,arguments),De){if(!De)throw Error(s(198));var u=Me;De=!1,Me=null,Ie||(Ie=!0,Fe=u)}}(r,t,void 0,e),e.currentTarget=null}function Jr(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var s=void 0;if(t)for(var l=r.length-1;0<=l;l--){var i=r[l],o=i.instance,c=i.currentTarget;if(i=i.listener,o!==s&&a.isPropagationStopped())break e;Kr(a,i,c),s=o}else for(l=0;l<r.length;l++){if(o=(i=r[l]).instance,c=i.currentTarget,i=i.listener,o!==s&&a.isPropagationStopped())break e;Kr(a,i,c),s=o}}}if(Ie)throw e=Fe,Ie=!1,Fe=null,e}function Xr(e,t){var n=t[Ea];void 0===n&&(n=t[Ea]=new Set);var r=e+"__bubble";n.has(r)||(ea(t,e,2,!1),n.add(r))}function Zr(e,t,n){var r=0;t&&(r|=4),ea(n,e,r,t)}var Gr="_reactListening"+Math.random().toString(36).slice(2);function Yr(e){if(!e[Gr]){e[Gr]=!0,l.forEach((function(t){"selectionchange"!==t&&(Qr.has(t)||Zr(t,!1,e),Zr(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Gr]||(t[Gr]=!0,Zr("selectionchange",!1,t))}}function ea(e,t,n,r){switch(Jt(t)){case 1:var a=Vt;break;case 4:a=$t;break;default:a=Ht}n=a.bind(null,t,n,e),a=void 0,!Le||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function ta(e,t,n,r,a){var s=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var l=r.tag;if(3===l||4===l){var i=r.stateNode.containerInfo;if(i===a||8===i.nodeType&&i.parentNode===a)break;if(4===l)for(l=r.return;null!==l;){var o=l.tag;if((3===o||4===o)&&((o=l.stateNode.containerInfo)===a||8===o.nodeType&&o.parentNode===a))return;l=l.return}for(;null!==i;){if(null===(l=Pa(i)))return;if(5===(o=l.tag)||6===o){r=s=l;continue e}i=i.parentNode}}r=r.return}_e((function(){var r=s,a=be(n),l=[];e:{var i=Br.get(e);if(void 0!==i){var o=cn,c=e;switch(e){case"keypress":if(0===en(n))break e;case"keydown":case"keyup":o=En;break;case"focusin":c="focus",o=pn;break;case"focusout":c="blur",o=pn;break;case"beforeblur":case"afterblur":o=pn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":o=mn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":o=hn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":o=_n;break;case Ir:case Fr:case zr:o=xn;break;case Ur:o=Rn;break;case"scroll":o=dn;break;case"wheel":o=Mn;break;case"copy":case"cut":case"paste":o=yn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":o=Pn}var u=0!==(4&t),d=!u&&"scroll"===e,f=u?null!==i?i+"Capture":null:i;u=[];for(var m,h=r;null!==h;){var p=(m=h).stateNode;if(5===m.tag&&null!==p&&(m=p,null!==f&&(null!=(p=Re(h,f))&&u.push(na(h,p,m)))),d)break;h=h.return}0<u.length&&(i=new o(i,c,null,n,a),l.push({event:i,listeners:u}))}}if(0===(7&t)){if(o="mouseout"===e||"pointerout"===e,(!(i="mouseover"===e||"pointerover"===e)||n===ve||!(c=n.relatedTarget||n.fromElement)||!Pa(c)&&!c[Sa])&&(o||i)&&(i=a.window===a?a:(i=a.ownerDocument)?i.defaultView||i.parentWindow:window,o?(o=r,null!==(c=(c=n.relatedTarget||n.toElement)?Pa(c):null)&&(c!==(d=Be(c))||5!==c.tag&&6!==c.tag)&&(c=null)):(o=null,c=r),o!==c)){if(u=mn,p="onMouseLeave",f="onMouseEnter",h="mouse","pointerout"!==e&&"pointerover"!==e||(u=Pn,p="onPointerLeave",f="onPointerEnter",h="pointer"),d=null==o?i:Ra(o),m=null==c?i:Ra(c),(i=new u(p,h+"leave",o,n,a)).target=d,i.relatedTarget=m,p=null,Pa(a)===r&&((u=new u(f,h+"enter",c,n,a)).target=m,u.relatedTarget=d,p=u),d=p,o&&c)e:{for(f=c,h=0,m=u=o;m;m=aa(m))h++;for(m=0,p=f;p;p=aa(p))m++;for(;0<h-m;)u=aa(u),h--;for(;0<m-h;)f=aa(f),m--;for(;h--;){if(u===f||null!==f&&u===f.alternate)break e;u=aa(u),f=aa(f)}u=null}else u=null;null!==o&&sa(l,i,o,u,!1),null!==c&&null!==d&&sa(l,d,c,u,!0)}if("select"===(o=(i=r?Ra(r):window).nodeName&&i.nodeName.toLowerCase())||"input"===o&&"file"===i.type)var x=lr;else if(Yn(i))if(or)x=gr;else{x=pr;var g=hr}else(o=i.nodeName)&&"input"===o.toLowerCase()&&("checkbox"===i.type||"radio"===i.type)&&(x=xr);switch(x&&(x=x(e,r))?er(l,x,n,a):(g&&g(e,i,r),"focusout"===e&&(g=i._wrapperState)&&g.controlled&&"number"===i.type&&ee(i,"number",i.value)),g=r?Ra(r):window,e){case"focusin":(Yn(g)||"true"===g.contentEditable)&&(Cr=g,Or=r,Pr=null);break;case"focusout":Pr=Or=Cr=null;break;case"mousedown":_r=!0;break;case"contextmenu":case"mouseup":case"dragend":_r=!1,Rr(l,n,a);break;case"selectionchange":if(Er)break;case"keydown":case"keyup":Rr(l,n,a)}var y;if(Bn)e:{switch(e){case"compositionstart":var v="onCompositionStart";break e;case"compositionend":v="onCompositionEnd";break e;case"compositionupdate":v="onCompositionUpdate";break e}v=void 0}else Zn?Jn(e,n)&&(v="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(v="onCompositionStart");v&&(Hn&&"ko"!==n.locale&&(Zn||"onCompositionStart"!==v?"onCompositionEnd"===v&&Zn&&(y=Yt()):(Zt="value"in(Xt=a)?Xt.value:Xt.textContent,Zn=!0)),0<(g=ra(r,v)).length&&(v=new vn(v,e,null,n,a),l.push({event:v,listeners:g}),y?v.data=y:null!==(y=Xn(n))&&(v.data=y))),(y=$n?function(e,t){switch(e){case"compositionend":return Xn(t);case"keypress":return 32!==t.which?null:(Kn=!0,Qn);case"textInput":return(e=t.data)===Qn&&Kn?null:e;default:return null}}(e,n):function(e,t){if(Zn)return"compositionend"===e||!Bn&&Jn(e,t)?(e=Yt(),Gt=Zt=Xt=null,Zn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Hn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=ra(r,"onBeforeInput")).length&&(a=new vn("onBeforeInput","beforeinput",null,n,a),l.push({event:a,listeners:r}),a.data=y))}Jr(l,t)}))}function na(e,t,n){return{instance:e,listener:t,currentTarget:n}}function ra(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,s=a.stateNode;5===a.tag&&null!==s&&(a=s,null!=(s=Re(e,n))&&r.unshift(na(e,s,a)),null!=(s=Re(e,t))&&r.push(na(e,s,a))),e=e.return}return r}function aa(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function sa(e,t,n,r,a){for(var s=t._reactName,l=[];null!==n&&n!==r;){var i=n,o=i.alternate,c=i.stateNode;if(null!==o&&o===r)break;5===i.tag&&null!==c&&(i=c,a?null!=(o=Re(n,s))&&l.unshift(na(n,o,i)):a||null!=(o=Re(n,s))&&l.push(na(n,o,i))),n=n.return}0!==l.length&&e.push({event:t,listeners:l})}var la=/\r\n?/g,ia=/\u0000|\uFFFD/g;function oa(e){return("string"===typeof e?e:""+e).replace(la,"\n").replace(ia,"")}function ca(e,t,n){if(t=oa(t),oa(e)!==t&&n)throw Error(s(425))}function ua(){}var da=null,fa=null;function ma(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ha="function"===typeof setTimeout?setTimeout:void 0,pa="function"===typeof clearTimeout?clearTimeout:void 0,xa="function"===typeof Promise?Promise:void 0,ga="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof xa?function(e){return xa.resolve(null).then(e).catch(ya)}:ha;function ya(e){setTimeout((function(){throw e}))}function va(e,t){var n=t,r=0;do{var a=n.nextSibling;if(e.removeChild(n),a&&8===a.nodeType)if("/$"===(n=a.data)){if(0===r)return e.removeChild(a),void Bt(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=a}while(n);Bt(t)}function ba(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function wa(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var ja=Math.random().toString(36).slice(2),Na="__reactFiber$"+ja,ka="__reactProps$"+ja,Sa="__reactContainer$"+ja,Ea="__reactEvents$"+ja,Ca="__reactListeners$"+ja,Oa="__reactHandles$"+ja;function Pa(e){var t=e[Na];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Sa]||n[Na]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=wa(e);null!==e;){if(n=e[Na])return n;e=wa(e)}return t}n=(e=n).parentNode}return null}function _a(e){return!(e=e[Na]||e[Sa])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function Ra(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(s(33))}function La(e){return e[ka]||null}var Ta=[],Aa=-1;function Da(e){return{current:e}}function Ma(e){0>Aa||(e.current=Ta[Aa],Ta[Aa]=null,Aa--)}function Ia(e,t){Aa++,Ta[Aa]=e.current,e.current=t}var Fa={},za=Da(Fa),Ua=Da(!1),Ba=Fa;function Wa(e,t){var n=e.type.contextTypes;if(!n)return Fa;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var a,s={};for(a in n)s[a]=t[a];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=s),s}function qa(e){return null!==(e=e.childContextTypes)&&void 0!==e}function Va(){Ma(Ua),Ma(za)}function $a(e,t,n){if(za.current!==Fa)throw Error(s(168));Ia(za,t),Ia(Ua,n)}function Ha(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var a in r=r.getChildContext())if(!(a in t))throw Error(s(108,q(e)||"Unknown",a));return I({},n,r)}function Qa(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Fa,Ba=za.current,Ia(za,e),Ia(Ua,Ua.current),!0}function Ka(e,t,n){var r=e.stateNode;if(!r)throw Error(s(169));n?(e=Ha(e,t,Ba),r.__reactInternalMemoizedMergedChildContext=e,Ma(Ua),Ma(za),Ia(za,e)):Ma(Ua),Ia(Ua,n)}var Ja=null,Xa=!1,Za=!1;function Ga(e){null===Ja?Ja=[e]:Ja.push(e)}function Ya(){if(!Za&&null!==Ja){Za=!0;var e=0,t=yt;try{var n=Ja;for(yt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}Ja=null,Xa=!1}catch(a){throw null!==Ja&&(Ja=Ja.slice(e+1)),He(Ge,Ya),a}finally{yt=t,Za=!1}}return null}var es=[],ts=0,ns=null,rs=0,as=[],ss=0,ls=null,is=1,os="";function cs(e,t){es[ts++]=rs,es[ts++]=ns,ns=e,rs=t}function us(e,t,n){as[ss++]=is,as[ss++]=os,as[ss++]=ls,ls=e;var r=is;e=os;var a=32-st(r)-1;r&=~(1<<a),n+=1;var s=32-st(t)+a;if(30<s){var l=a-a%5;s=(r&(1<<l)-1).toString(32),r>>=l,a-=l,is=1<<32-st(t)+a|n<<a|r,os=s+e}else is=1<<s|n<<a|r,os=e}function ds(e){null!==e.return&&(cs(e,1),us(e,1,0))}function fs(e){for(;e===ns;)ns=es[--ts],es[ts]=null,rs=es[--ts],es[ts]=null;for(;e===ls;)ls=as[--ss],as[ss]=null,os=as[--ss],as[ss]=null,is=as[--ss],as[ss]=null}var ms=null,hs=null,ps=!1,xs=null;function gs(e,t){var n=Wc(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function ys(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,ms=e,hs=ba(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,ms=e,hs=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==ls?{id:is,overflow:os}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Wc(18,null,null,0)).stateNode=t,n.return=e,e.child=n,ms=e,hs=null,!0);default:return!1}}function vs(e){return 0!==(1&e.mode)&&0===(128&e.flags)}function bs(e){if(ps){var t=hs;if(t){var n=t;if(!ys(e,t)){if(vs(e))throw Error(s(418));t=ba(n.nextSibling);var r=ms;t&&ys(e,t)?gs(r,n):(e.flags=-4097&e.flags|2,ps=!1,ms=e)}}else{if(vs(e))throw Error(s(418));e.flags=-4097&e.flags|2,ps=!1,ms=e}}}function ws(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;ms=e}function js(e){if(e!==ms)return!1;if(!ps)return ws(e),ps=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!ma(e.type,e.memoizedProps)),t&&(t=hs)){if(vs(e))throw Ns(),Error(s(418));for(;t;)gs(e,t),t=ba(t.nextSibling)}if(ws(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(s(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){hs=ba(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}hs=null}}else hs=ms?ba(e.stateNode.nextSibling):null;return!0}function Ns(){for(var e=hs;e;)e=ba(e.nextSibling)}function ks(){hs=ms=null,ps=!1}function Ss(e){null===xs?xs=[e]:xs.push(e)}var Es=b.ReactCurrentBatchConfig;function Cs(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(s(309));var r=n.stateNode}if(!r)throw Error(s(147,e));var a=r,l=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===l?t.ref:(t=function(e){var t=a.refs;null===e?delete t[l]:t[l]=e},t._stringRef=l,t)}if("string"!==typeof e)throw Error(s(284));if(!n._owner)throw Error(s(290,e))}return e}function Os(e,t){throw e=Object.prototype.toString.call(t),Error(s(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Ps(e){return(0,e._init)(e._payload)}function _s(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function a(e,t){return(e=Vc(e,t)).index=0,e.sibling=null,e}function l(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function i(t){return e&&null===t.alternate&&(t.flags|=2),t}function o(e,t,n,r){return null===t||6!==t.tag?((t=Kc(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function c(e,t,n,r){var s=n.type;return s===N?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===s||"object"===typeof s&&null!==s&&s.$$typeof===L&&Ps(s)===t.type)?((r=a(t,n.props)).ref=Cs(e,t,n),r.return=e,r):((r=$c(n.type,n.key,n.props,null,e.mode,r)).ref=Cs(e,t,n),r.return=e,r)}function u(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Jc(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function d(e,t,n,r,s){return null===t||7!==t.tag?((t=Hc(n,e.mode,r,s)).return=e,t):((t=a(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t)return(t=Kc(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case w:return(n=$c(t.type,t.key,t.props,null,e.mode,n)).ref=Cs(e,null,t),n.return=e,n;case j:return(t=Jc(t,e.mode,n)).return=e,t;case L:return f(e,(0,t._init)(t._payload),n)}if(te(t)||D(t))return(t=Hc(t,e.mode,n,null)).return=e,t;Os(e,t)}return null}function m(e,t,n,r){var a=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n)return null!==a?null:o(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case w:return n.key===a?c(e,t,n,r):null;case j:return n.key===a?u(e,t,n,r):null;case L:return m(e,t,(a=n._init)(n._payload),r)}if(te(n)||D(n))return null!==a?null:d(e,t,n,r,null);Os(e,n)}return null}function h(e,t,n,r,a){if("string"===typeof r&&""!==r||"number"===typeof r)return o(t,e=e.get(n)||null,""+r,a);if("object"===typeof r&&null!==r){switch(r.$$typeof){case w:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case j:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case L:return h(e,t,n,(0,r._init)(r._payload),a)}if(te(r)||D(r))return d(t,e=e.get(n)||null,r,a,null);Os(t,r)}return null}function p(a,s,i,o){for(var c=null,u=null,d=s,p=s=0,x=null;null!==d&&p<i.length;p++){d.index>p?(x=d,d=null):x=d.sibling;var g=m(a,d,i[p],o);if(null===g){null===d&&(d=x);break}e&&d&&null===g.alternate&&t(a,d),s=l(g,s,p),null===u?c=g:u.sibling=g,u=g,d=x}if(p===i.length)return n(a,d),ps&&cs(a,p),c;if(null===d){for(;p<i.length;p++)null!==(d=f(a,i[p],o))&&(s=l(d,s,p),null===u?c=d:u.sibling=d,u=d);return ps&&cs(a,p),c}for(d=r(a,d);p<i.length;p++)null!==(x=h(d,a,p,i[p],o))&&(e&&null!==x.alternate&&d.delete(null===x.key?p:x.key),s=l(x,s,p),null===u?c=x:u.sibling=x,u=x);return e&&d.forEach((function(e){return t(a,e)})),ps&&cs(a,p),c}function x(a,i,o,c){var u=D(o);if("function"!==typeof u)throw Error(s(150));if(null==(o=u.call(o)))throw Error(s(151));for(var d=u=null,p=i,x=i=0,g=null,y=o.next();null!==p&&!y.done;x++,y=o.next()){p.index>x?(g=p,p=null):g=p.sibling;var v=m(a,p,y.value,c);if(null===v){null===p&&(p=g);break}e&&p&&null===v.alternate&&t(a,p),i=l(v,i,x),null===d?u=v:d.sibling=v,d=v,p=g}if(y.done)return n(a,p),ps&&cs(a,x),u;if(null===p){for(;!y.done;x++,y=o.next())null!==(y=f(a,y.value,c))&&(i=l(y,i,x),null===d?u=y:d.sibling=y,d=y);return ps&&cs(a,x),u}for(p=r(a,p);!y.done;x++,y=o.next())null!==(y=h(p,a,x,y.value,c))&&(e&&null!==y.alternate&&p.delete(null===y.key?x:y.key),i=l(y,i,x),null===d?u=y:d.sibling=y,d=y);return e&&p.forEach((function(e){return t(a,e)})),ps&&cs(a,x),u}return function e(r,s,l,o){if("object"===typeof l&&null!==l&&l.type===N&&null===l.key&&(l=l.props.children),"object"===typeof l&&null!==l){switch(l.$$typeof){case w:e:{for(var c=l.key,u=s;null!==u;){if(u.key===c){if((c=l.type)===N){if(7===u.tag){n(r,u.sibling),(s=a(u,l.props.children)).return=r,r=s;break e}}else if(u.elementType===c||"object"===typeof c&&null!==c&&c.$$typeof===L&&Ps(c)===u.type){n(r,u.sibling),(s=a(u,l.props)).ref=Cs(r,u,l),s.return=r,r=s;break e}n(r,u);break}t(r,u),u=u.sibling}l.type===N?((s=Hc(l.props.children,r.mode,o,l.key)).return=r,r=s):((o=$c(l.type,l.key,l.props,null,r.mode,o)).ref=Cs(r,s,l),o.return=r,r=o)}return i(r);case j:e:{for(u=l.key;null!==s;){if(s.key===u){if(4===s.tag&&s.stateNode.containerInfo===l.containerInfo&&s.stateNode.implementation===l.implementation){n(r,s.sibling),(s=a(s,l.children||[])).return=r,r=s;break e}n(r,s);break}t(r,s),s=s.sibling}(s=Jc(l,r.mode,o)).return=r,r=s}return i(r);case L:return e(r,s,(u=l._init)(l._payload),o)}if(te(l))return p(r,s,l,o);if(D(l))return x(r,s,l,o);Os(r,l)}return"string"===typeof l&&""!==l||"number"===typeof l?(l=""+l,null!==s&&6===s.tag?(n(r,s.sibling),(s=a(s,l)).return=r,r=s):(n(r,s),(s=Kc(l,r.mode,o)).return=r,r=s),i(r)):n(r,s)}}var Rs=_s(!0),Ls=_s(!1),Ts=Da(null),As=null,Ds=null,Ms=null;function Is(){Ms=Ds=As=null}function Fs(e){var t=Ts.current;Ma(Ts),e._currentValue=t}function zs(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Us(e,t){As=e,Ms=Ds=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(Pi=!0),e.firstContext=null)}function Bs(e){var t=e._currentValue;if(Ms!==e)if(e={context:e,memoizedValue:t,next:null},null===Ds){if(null===As)throw Error(s(308));Ds=e,As.dependencies={lanes:0,firstContext:e}}else Ds=Ds.next=e;return t}var Ws=null;function qs(e){null===Ws?Ws=[e]:Ws.push(e)}function Vs(e,t,n,r){var a=t.interleaved;return null===a?(n.next=n,qs(t)):(n.next=a.next,a.next=n),t.interleaved=n,$s(e,r)}function $s(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var Hs=!1;function Qs(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Ks(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Js(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Xs(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&zo)){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,$s(e,n)}return null===(a=r.interleaved)?(t.next=t,qs(r)):(t.next=a.next,a.next=t),r.interleaved=t,$s(e,n)}function Zs(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194240&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,gt(e,n)}}function Gs(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,s=null;if(null!==(n=n.firstBaseUpdate)){do{var l={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===s?a=s=l:s=s.next=l,n=n.next}while(null!==n);null===s?a=s=t:s=s.next=t}else a=s=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:s,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Ys(e,t,n,r){var a=e.updateQueue;Hs=!1;var s=a.firstBaseUpdate,l=a.lastBaseUpdate,i=a.shared.pending;if(null!==i){a.shared.pending=null;var o=i,c=o.next;o.next=null,null===l?s=c:l.next=c,l=o;var u=e.alternate;null!==u&&((i=(u=u.updateQueue).lastBaseUpdate)!==l&&(null===i?u.firstBaseUpdate=c:i.next=c,u.lastBaseUpdate=o))}if(null!==s){var d=a.baseState;for(l=0,u=c=o=null,i=s;;){var f=i.lane,m=i.eventTime;if((r&f)===f){null!==u&&(u=u.next={eventTime:m,lane:0,tag:i.tag,payload:i.payload,callback:i.callback,next:null});e:{var h=e,p=i;switch(f=t,m=n,p.tag){case 1:if("function"===typeof(h=p.payload)){d=h.call(m,d,f);break e}d=h;break e;case 3:h.flags=-65537&h.flags|128;case 0:if(null===(f="function"===typeof(h=p.payload)?h.call(m,d,f):h)||void 0===f)break e;d=I({},d,f);break e;case 2:Hs=!0}}null!==i.callback&&0!==i.lane&&(e.flags|=64,null===(f=a.effects)?a.effects=[i]:f.push(i))}else m={eventTime:m,lane:f,tag:i.tag,payload:i.payload,callback:i.callback,next:null},null===u?(c=u=m,o=d):u=u.next=m,l|=f;if(null===(i=i.next)){if(null===(i=a.shared.pending))break;i=(f=i).next,f.next=null,a.lastBaseUpdate=f,a.shared.pending=null}}if(null===u&&(o=d),a.baseState=o,a.firstBaseUpdate=c,a.lastBaseUpdate=u,null!==(t=a.shared.interleaved)){a=t;do{l|=a.lane,a=a.next}while(a!==t)}else null===s&&(a.shared.lanes=0);Qo|=l,e.lanes=l,e.memoizedState=d}}function el(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],a=r.callback;if(null!==a){if(r.callback=null,r=n,"function"!==typeof a)throw Error(s(191,a));a.call(r)}}}var tl={},nl=Da(tl),rl=Da(tl),al=Da(tl);function sl(e){if(e===tl)throw Error(s(174));return e}function ll(e,t){switch(Ia(al,t),Ia(rl,e),Ia(nl,tl),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:oe(null,"");break;default:t=oe(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}Ma(nl),Ia(nl,t)}function il(){Ma(nl),Ma(rl),Ma(al)}function ol(e){sl(al.current);var t=sl(nl.current),n=oe(t,e.type);t!==n&&(Ia(rl,e),Ia(nl,n))}function cl(e){rl.current===e&&(Ma(nl),Ma(rl))}var ul=Da(0);function dl(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var fl=[];function ml(){for(var e=0;e<fl.length;e++)fl[e]._workInProgressVersionPrimary=null;fl.length=0}var hl=b.ReactCurrentDispatcher,pl=b.ReactCurrentBatchConfig,xl=0,gl=null,yl=null,vl=null,bl=!1,wl=!1,jl=0,Nl=0;function kl(){throw Error(s(321))}function Sl(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!yr(e[n],t[n]))return!1;return!0}function El(e,t,n,r,a,l){if(xl=l,gl=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,hl.current=null===e||null===e.memoizedState?ci:ui,e=n(r,a),wl){l=0;do{if(wl=!1,jl=0,25<=l)throw Error(s(301));l+=1,vl=yl=null,t.updateQueue=null,hl.current=di,e=n(r,a)}while(wl)}if(hl.current=oi,t=null!==yl&&null!==yl.next,xl=0,vl=yl=gl=null,bl=!1,t)throw Error(s(300));return e}function Cl(){var e=0!==jl;return jl=0,e}function Ol(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===vl?gl.memoizedState=vl=e:vl=vl.next=e,vl}function Pl(){if(null===yl){var e=gl.alternate;e=null!==e?e.memoizedState:null}else e=yl.next;var t=null===vl?gl.memoizedState:vl.next;if(null!==t)vl=t,yl=e;else{if(null===e)throw Error(s(310));e={memoizedState:(yl=e).memoizedState,baseState:yl.baseState,baseQueue:yl.baseQueue,queue:yl.queue,next:null},null===vl?gl.memoizedState=vl=e:vl=vl.next=e}return vl}function _l(e,t){return"function"===typeof t?t(e):t}function Rl(e){var t=Pl(),n=t.queue;if(null===n)throw Error(s(311));n.lastRenderedReducer=e;var r=yl,a=r.baseQueue,l=n.pending;if(null!==l){if(null!==a){var i=a.next;a.next=l.next,l.next=i}r.baseQueue=a=l,n.pending=null}if(null!==a){l=a.next,r=r.baseState;var o=i=null,c=null,u=l;do{var d=u.lane;if((xl&d)===d)null!==c&&(c=c.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var f={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};null===c?(o=c=f,i=r):c=c.next=f,gl.lanes|=d,Qo|=d}u=u.next}while(null!==u&&u!==l);null===c?i=r:c.next=o,yr(r,t.memoizedState)||(Pi=!0),t.memoizedState=r,t.baseState=i,t.baseQueue=c,n.lastRenderedState=r}if(null!==(e=n.interleaved)){a=e;do{l=a.lane,gl.lanes|=l,Qo|=l,a=a.next}while(a!==e)}else null===a&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Ll(e){var t=Pl(),n=t.queue;if(null===n)throw Error(s(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,l=t.memoizedState;if(null!==a){n.pending=null;var i=a=a.next;do{l=e(l,i.action),i=i.next}while(i!==a);yr(l,t.memoizedState)||(Pi=!0),t.memoizedState=l,null===t.baseQueue&&(t.baseState=l),n.lastRenderedState=l}return[l,r]}function Tl(){}function Al(e,t){var n=gl,r=Pl(),a=t(),l=!yr(r.memoizedState,a);if(l&&(r.memoizedState=a,Pi=!0),r=r.queue,Hl(Il.bind(null,n,r,e),[e]),r.getSnapshot!==t||l||null!==vl&&1&vl.memoizedState.tag){if(n.flags|=2048,Bl(9,Ml.bind(null,n,r,a,t),void 0,null),null===Uo)throw Error(s(349));0!==(30&xl)||Dl(n,t,a)}return a}function Dl(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=gl.updateQueue)?(t={lastEffect:null,stores:null},gl.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Ml(e,t,n,r){t.value=n,t.getSnapshot=r,Fl(t)&&zl(e)}function Il(e,t,n){return n((function(){Fl(t)&&zl(e)}))}function Fl(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!yr(e,n)}catch(zn){return!0}}function zl(e){var t=$s(e,1);null!==t&&mc(t,e,1,-1)}function Ul(e){var t=Ol();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:_l,lastRenderedState:e},t.queue=e,e=e.dispatch=ai.bind(null,gl,e),[t.memoizedState,e]}function Bl(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=gl.updateQueue)?(t={lastEffect:null,stores:null},gl.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function Wl(){return Pl().memoizedState}function ql(e,t,n,r){var a=Ol();gl.flags|=e,a.memoizedState=Bl(1|t,n,void 0,void 0===r?null:r)}function Vl(e,t,n,r){var a=Pl();r=void 0===r?null:r;var s=void 0;if(null!==yl){var l=yl.memoizedState;if(s=l.destroy,null!==r&&Sl(r,l.deps))return void(a.memoizedState=Bl(t,n,s,r))}gl.flags|=e,a.memoizedState=Bl(1|t,n,s,r)}function $l(e,t){return ql(8390656,8,e,t)}function Hl(e,t){return Vl(2048,8,e,t)}function Ql(e,t){return Vl(4,2,e,t)}function Kl(e,t){return Vl(4,4,e,t)}function Jl(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Xl(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Vl(4,4,Jl.bind(null,t,e),n)}function Zl(){}function Gl(e,t){var n=Pl();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&Sl(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Yl(e,t){var n=Pl();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&Sl(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function ei(e,t,n){return 0===(21&xl)?(e.baseState&&(e.baseState=!1,Pi=!0),e.memoizedState=n):(yr(n,t)||(n=ht(),gl.lanes|=n,Qo|=n,e.baseState=!0),t)}function ti(e,t){var n=yt;yt=0!==n&&4>n?n:4,e(!0);var r=pl.transition;pl.transition={};try{e(!1),t()}finally{yt=n,pl.transition=r}}function ni(){return Pl().memoizedState}function ri(e,t,n){var r=fc(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},si(e))li(t,n);else if(null!==(n=Vs(e,t,n,r))){mc(n,e,r,dc()),ii(n,t,r)}}function ai(e,t,n){var r=fc(e),a={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(si(e))li(t,a);else{var s=e.alternate;if(0===e.lanes&&(null===s||0===s.lanes)&&null!==(s=t.lastRenderedReducer))try{var l=t.lastRenderedState,i=s(l,n);if(a.hasEagerState=!0,a.eagerState=i,yr(i,l)){var o=t.interleaved;return null===o?(a.next=a,qs(t)):(a.next=o.next,o.next=a),void(t.interleaved=a)}}catch(Fn){}null!==(n=Vs(e,t,a,r))&&(mc(n,e,r,a=dc()),ii(n,t,r))}}function si(e){var t=e.alternate;return e===gl||null!==t&&t===gl}function li(e,t){wl=bl=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function ii(e,t,n){if(0!==(4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,gt(e,n)}}var oi={readContext:Bs,useCallback:kl,useContext:kl,useEffect:kl,useImperativeHandle:kl,useInsertionEffect:kl,useLayoutEffect:kl,useMemo:kl,useReducer:kl,useRef:kl,useState:kl,useDebugValue:kl,useDeferredValue:kl,useTransition:kl,useMutableSource:kl,useSyncExternalStore:kl,useId:kl,unstable_isNewReconciler:!1},ci={readContext:Bs,useCallback:function(e,t){return Ol().memoizedState=[e,void 0===t?null:t],e},useContext:Bs,useEffect:$l,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,ql(4194308,4,Jl.bind(null,t,e),n)},useLayoutEffect:function(e,t){return ql(4194308,4,e,t)},useInsertionEffect:function(e,t){return ql(4,2,e,t)},useMemo:function(e,t){var n=Ol();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Ol();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=ri.bind(null,gl,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},Ol().memoizedState=e},useState:Ul,useDebugValue:Zl,useDeferredValue:function(e){return Ol().memoizedState=e},useTransition:function(){var e=Ul(!1),t=e[0];return e=ti.bind(null,e[1]),Ol().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=gl,a=Ol();if(ps){if(void 0===n)throw Error(s(407));n=n()}else{if(n=t(),null===Uo)throw Error(s(349));0!==(30&xl)||Dl(r,t,n)}a.memoizedState=n;var l={value:n,getSnapshot:t};return a.queue=l,$l(Il.bind(null,r,l,e),[e]),r.flags|=2048,Bl(9,Ml.bind(null,r,l,n,t),void 0,null),n},useId:function(){var e=Ol(),t=Uo.identifierPrefix;if(ps){var n=os;t=":"+t+"R"+(n=(is&~(1<<32-st(is)-1)).toString(32)+n),0<(n=jl++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=Nl++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},ui={readContext:Bs,useCallback:Gl,useContext:Bs,useEffect:Hl,useImperativeHandle:Xl,useInsertionEffect:Ql,useLayoutEffect:Kl,useMemo:Yl,useReducer:Rl,useRef:Wl,useState:function(){return Rl(_l)},useDebugValue:Zl,useDeferredValue:function(e){return ei(Pl(),yl.memoizedState,e)},useTransition:function(){return[Rl(_l)[0],Pl().memoizedState]},useMutableSource:Tl,useSyncExternalStore:Al,useId:ni,unstable_isNewReconciler:!1},di={readContext:Bs,useCallback:Gl,useContext:Bs,useEffect:Hl,useImperativeHandle:Xl,useInsertionEffect:Ql,useLayoutEffect:Kl,useMemo:Yl,useReducer:Ll,useRef:Wl,useState:function(){return Ll(_l)},useDebugValue:Zl,useDeferredValue:function(e){var t=Pl();return null===yl?t.memoizedState=e:ei(t,yl.memoizedState,e)},useTransition:function(){return[Ll(_l)[0],Pl().memoizedState]},useMutableSource:Tl,useSyncExternalStore:Al,useId:ni,unstable_isNewReconciler:!1};function fi(e,t){if(e&&e.defaultProps){for(var n in t=I({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function mi(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:I({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var hi={isMounted:function(e){return!!(e=e._reactInternals)&&Be(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=dc(),a=fc(e),s=Js(r,a);s.payload=t,void 0!==n&&null!==n&&(s.callback=n),null!==(t=Xs(e,s,a))&&(mc(t,e,a,r),Zs(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=dc(),a=fc(e),s=Js(r,a);s.tag=1,s.payload=t,void 0!==n&&null!==n&&(s.callback=n),null!==(t=Xs(e,s,a))&&(mc(t,e,a,r),Zs(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=dc(),r=fc(e),a=Js(n,r);a.tag=2,void 0!==t&&null!==t&&(a.callback=t),null!==(t=Xs(e,a,r))&&(mc(t,e,r,n),Zs(t,e,r))}};function pi(e,t,n,r,a,s,l){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,s,l):!t.prototype||!t.prototype.isPureReactComponent||(!vr(n,r)||!vr(a,s))}function xi(e,t,n){var r=!1,a=Fa,s=t.contextType;return"object"===typeof s&&null!==s?s=Bs(s):(a=qa(t)?Ba:za.current,s=(r=null!==(r=t.contextTypes)&&void 0!==r)?Wa(e,a):Fa),t=new t(n,s),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=hi,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=a,e.__reactInternalMemoizedMaskedChildContext=s),t}function gi(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&hi.enqueueReplaceState(t,t.state,null)}function yi(e,t,n,r){var a=e.stateNode;a.props=n,a.state=e.memoizedState,a.refs={},Qs(e);var s=t.contextType;"object"===typeof s&&null!==s?a.context=Bs(s):(s=qa(t)?Ba:za.current,a.context=Wa(e,s)),a.state=e.memoizedState,"function"===typeof(s=t.getDerivedStateFromProps)&&(mi(e,t,s,n),a.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof a.getSnapshotBeforeUpdate||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||(t=a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),t!==a.state&&hi.enqueueReplaceState(a,a.state,null),Ys(e,n,a,r),a.state=e.memoizedState),"function"===typeof a.componentDidMount&&(e.flags|=4194308)}function vi(e,t){try{var n="",r=t;do{n+=B(r),r=r.return}while(r);var a=n}catch(Tn){a="\nError generating stack: "+Tn.message+"\n"+Tn.stack}return{value:e,source:t,stack:a,digest:null}}function bi(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function wi(e,t){try{console.error(t.value)}catch(An){setTimeout((function(){throw An}))}}var ji="function"===typeof WeakMap?WeakMap:Map;function Ni(e,t,n){(n=Js(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){tc||(tc=!0,nc=r),wi(0,t)},n}function ki(e,t,n){(n=Js(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"===typeof r){var a=t.value;n.payload=function(){return r(a)},n.callback=function(){wi(0,t)}}var s=e.stateNode;return null!==s&&"function"===typeof s.componentDidCatch&&(n.callback=function(){wi(0,t),"function"!==typeof r&&(null===rc?rc=new Set([this]):rc.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function Si(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new ji;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(a.add(n),e=Mc.bind(null,e,t,n),t.then(e,e))}function Ei(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function Ci(e,t,n,r,a){return 0===(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Js(-1,1)).tag=2,Xs(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=a,e)}var Oi=b.ReactCurrentOwner,Pi=!1;function _i(e,t,n,r){t.child=null===e?Ls(t,null,n,r):Rs(t,e.child,n,r)}function Ri(e,t,n,r,a){n=n.render;var s=t.ref;return Us(t,a),r=El(e,t,n,r,s,a),n=Cl(),null===e||Pi?(ps&&n&&ds(t),t.flags|=1,_i(e,t,r,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,eo(e,t,a))}function Li(e,t,n,r,a){if(null===e){var s=n.type;return"function"!==typeof s||qc(s)||void 0!==s.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=$c(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=s,Ti(e,t,s,r,a))}if(s=e.child,0===(e.lanes&a)){var l=s.memoizedProps;if((n=null!==(n=n.compare)?n:vr)(l,r)&&e.ref===t.ref)return eo(e,t,a)}return t.flags|=1,(e=Vc(s,r)).ref=t.ref,e.return=t,t.child=e}function Ti(e,t,n,r,a){if(null!==e){var s=e.memoizedProps;if(vr(s,r)&&e.ref===t.ref){if(Pi=!1,t.pendingProps=r=s,0===(e.lanes&a))return t.lanes=e.lanes,eo(e,t,a);0!==(131072&e.flags)&&(Pi=!0)}}return Mi(e,t,n,r,a)}function Ai(e,t,n){var r=t.pendingProps,a=r.children,s=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(0===(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Ia(Vo,qo),qo|=n;else{if(0===(1073741824&n))return e=null!==s?s.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Ia(Vo,qo),qo|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==s?s.baseLanes:n,Ia(Vo,qo),qo|=r}else null!==s?(r=s.baseLanes|n,t.memoizedState=null):r=n,Ia(Vo,qo),qo|=r;return _i(e,t,a,n),t.child}function Di(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Mi(e,t,n,r,a){var s=qa(n)?Ba:za.current;return s=Wa(t,s),Us(t,a),n=El(e,t,n,r,s,a),r=Cl(),null===e||Pi?(ps&&r&&ds(t),t.flags|=1,_i(e,t,n,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,eo(e,t,a))}function Ii(e,t,n,r,a){if(qa(n)){var s=!0;Qa(t)}else s=!1;if(Us(t,a),null===t.stateNode)Yi(e,t),xi(t,n,r),yi(t,n,r,a),r=!0;else if(null===e){var l=t.stateNode,i=t.memoizedProps;l.props=i;var o=l.context,c=n.contextType;"object"===typeof c&&null!==c?c=Bs(c):c=Wa(t,c=qa(n)?Ba:za.current);var u=n.getDerivedStateFromProps,d="function"===typeof u||"function"===typeof l.getSnapshotBeforeUpdate;d||"function"!==typeof l.UNSAFE_componentWillReceiveProps&&"function"!==typeof l.componentWillReceiveProps||(i!==r||o!==c)&&gi(t,l,r,c),Hs=!1;var f=t.memoizedState;l.state=f,Ys(t,r,l,a),o=t.memoizedState,i!==r||f!==o||Ua.current||Hs?("function"===typeof u&&(mi(t,n,u,r),o=t.memoizedState),(i=Hs||pi(t,n,i,r,f,o,c))?(d||"function"!==typeof l.UNSAFE_componentWillMount&&"function"!==typeof l.componentWillMount||("function"===typeof l.componentWillMount&&l.componentWillMount(),"function"===typeof l.UNSAFE_componentWillMount&&l.UNSAFE_componentWillMount()),"function"===typeof l.componentDidMount&&(t.flags|=4194308)):("function"===typeof l.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=o),l.props=r,l.state=o,l.context=c,r=i):("function"===typeof l.componentDidMount&&(t.flags|=4194308),r=!1)}else{l=t.stateNode,Ks(e,t),i=t.memoizedProps,c=t.type===t.elementType?i:fi(t.type,i),l.props=c,d=t.pendingProps,f=l.context,"object"===typeof(o=n.contextType)&&null!==o?o=Bs(o):o=Wa(t,o=qa(n)?Ba:za.current);var m=n.getDerivedStateFromProps;(u="function"===typeof m||"function"===typeof l.getSnapshotBeforeUpdate)||"function"!==typeof l.UNSAFE_componentWillReceiveProps&&"function"!==typeof l.componentWillReceiveProps||(i!==d||f!==o)&&gi(t,l,r,o),Hs=!1,f=t.memoizedState,l.state=f,Ys(t,r,l,a);var h=t.memoizedState;i!==d||f!==h||Ua.current||Hs?("function"===typeof m&&(mi(t,n,m,r),h=t.memoizedState),(c=Hs||pi(t,n,c,r,f,h,o)||!1)?(u||"function"!==typeof l.UNSAFE_componentWillUpdate&&"function"!==typeof l.componentWillUpdate||("function"===typeof l.componentWillUpdate&&l.componentWillUpdate(r,h,o),"function"===typeof l.UNSAFE_componentWillUpdate&&l.UNSAFE_componentWillUpdate(r,h,o)),"function"===typeof l.componentDidUpdate&&(t.flags|=4),"function"===typeof l.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof l.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof l.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=h),l.props=r,l.state=h,l.context=o,r=c):("function"!==typeof l.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof l.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return Fi(e,t,n,r,s,a)}function Fi(e,t,n,r,a,s){Di(e,t);var l=0!==(128&t.flags);if(!r&&!l)return a&&Ka(t,n,!1),eo(e,t,s);r=t.stateNode,Oi.current=t;var i=l&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&l?(t.child=Rs(t,e.child,null,s),t.child=Rs(t,null,i,s)):_i(e,t,i,s),t.memoizedState=r.state,a&&Ka(t,n,!0),t.child}function zi(e){var t=e.stateNode;t.pendingContext?$a(0,t.pendingContext,t.pendingContext!==t.context):t.context&&$a(0,t.context,!1),ll(e,t.containerInfo)}function Ui(e,t,n,r,a){return ks(),Ss(a),t.flags|=256,_i(e,t,n,r),t.child}var Bi,Wi,qi,Vi,$i={dehydrated:null,treeContext:null,retryLane:0};function Hi(e){return{baseLanes:e,cachePool:null,transitions:null}}function Qi(e,t,n){var r,a=t.pendingProps,l=ul.current,i=!1,o=0!==(128&t.flags);if((r=o)||(r=(null===e||null!==e.memoizedState)&&0!==(2&l)),r?(i=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(l|=1),Ia(ul,1&l),null===e)return bs(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0===(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(o=a.children,e=a.fallback,i?(a=t.mode,i=t.child,o={mode:"hidden",children:o},0===(1&a)&&null!==i?(i.childLanes=0,i.pendingProps=o):i=Qc(o,a,0,null),e=Hc(e,a,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Hi(n),t.memoizedState=$i,e):Ki(t,o));if(null!==(l=e.memoizedState)&&null!==(r=l.dehydrated))return function(e,t,n,r,a,l,i){if(n)return 256&t.flags?(t.flags&=-257,Ji(e,t,i,r=bi(Error(s(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(l=r.fallback,a=t.mode,r=Qc({mode:"visible",children:r.children},a,0,null),(l=Hc(l,a,i,null)).flags|=2,r.return=t,l.return=t,r.sibling=l,t.child=r,0!==(1&t.mode)&&Rs(t,e.child,null,i),t.child.memoizedState=Hi(i),t.memoizedState=$i,l);if(0===(1&t.mode))return Ji(e,t,i,null);if("$!"===a.data){if(r=a.nextSibling&&a.nextSibling.dataset)var o=r.dgst;return r=o,Ji(e,t,i,r=bi(l=Error(s(419)),r,void 0))}if(o=0!==(i&e.childLanes),Pi||o){if(null!==(r=Uo)){switch(i&-i){case 4:a=2;break;case 16:a=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:a=32;break;case 536870912:a=268435456;break;default:a=0}0!==(a=0!==(a&(r.suspendedLanes|i))?0:a)&&a!==l.retryLane&&(l.retryLane=a,$s(e,a),mc(r,e,a,-1))}return Ec(),Ji(e,t,i,r=bi(Error(s(421))))}return"$?"===a.data?(t.flags|=128,t.child=e.child,t=Fc.bind(null,e),a._reactRetry=t,null):(e=l.treeContext,hs=ba(a.nextSibling),ms=t,ps=!0,xs=null,null!==e&&(as[ss++]=is,as[ss++]=os,as[ss++]=ls,is=e.id,os=e.overflow,ls=t),t=Ki(t,r.children),t.flags|=4096,t)}(e,t,o,a,r,l,n);if(i){i=a.fallback,o=t.mode,r=(l=e.child).sibling;var c={mode:"hidden",children:a.children};return 0===(1&o)&&t.child!==l?((a=t.child).childLanes=0,a.pendingProps=c,t.deletions=null):(a=Vc(l,c)).subtreeFlags=14680064&l.subtreeFlags,null!==r?i=Vc(r,i):(i=Hc(i,o,n,null)).flags|=2,i.return=t,a.return=t,a.sibling=i,t.child=a,a=i,i=t.child,o=null===(o=e.child.memoizedState)?Hi(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},i.memoizedState=o,i.childLanes=e.childLanes&~n,t.memoizedState=$i,a}return e=(i=e.child).sibling,a=Vc(i,{mode:"visible",children:a.children}),0===(1&t.mode)&&(a.lanes=n),a.return=t,a.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=a,t.memoizedState=null,a}function Ki(e,t){return(t=Qc({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Ji(e,t,n,r){return null!==r&&Ss(r),Rs(t,e.child,null,n),(e=Ki(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Xi(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),zs(e.return,t,n)}function Zi(e,t,n,r,a){var s=e.memoizedState;null===s?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=n,s.tailMode=a)}function Gi(e,t,n){var r=t.pendingProps,a=r.revealOrder,s=r.tail;if(_i(e,t,r.children,n),0!==(2&(r=ul.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Xi(e,n,t);else if(19===e.tag)Xi(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Ia(ul,r),0===(1&t.mode))t.memoizedState=null;else switch(a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===dl(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),Zi(t,!1,a,n,s);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===dl(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}Zi(t,!0,n,null,s);break;case"together":Zi(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Yi(e,t){0===(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function eo(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Qo|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(s(153));if(null!==t.child){for(n=Vc(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Vc(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function to(e,t){if(!ps)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function no(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=14680064&a.subtreeFlags,r|=14680064&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function ro(e,t,n){var r=t.pendingProps;switch(fs(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return no(t),null;case 1:case 17:return qa(t.type)&&Va(),no(t),null;case 3:return r=t.stateNode,il(),Ma(Ua),Ma(za),ml(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(js(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,null!==xs&&(gc(xs),xs=null))),Wi(e,t),no(t),null;case 5:cl(t);var a=sl(al.current);if(n=t.type,null!==e&&null!=t.stateNode)qi(e,t,n,r,a),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(s(166));return no(t),null}if(e=sl(nl.current),js(t)){r=t.stateNode,n=t.type;var l=t.memoizedProps;switch(r[Na]=t,r[ka]=l,e=0!==(1&t.mode),n){case"dialog":Xr("cancel",r),Xr("close",r);break;case"iframe":case"object":case"embed":Xr("load",r);break;case"video":case"audio":for(a=0;a<Hr.length;a++)Xr(Hr[a],r);break;case"source":Xr("error",r);break;case"img":case"image":case"link":Xr("error",r),Xr("load",r);break;case"details":Xr("toggle",r);break;case"input":X(r,l),Xr("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!l.multiple},Xr("invalid",r);break;case"textarea":ae(r,l),Xr("invalid",r)}for(var o in ge(n,l),a=null,l)if(l.hasOwnProperty(o)){var c=l[o];"children"===o?"string"===typeof c?r.textContent!==c&&(!0!==l.suppressHydrationWarning&&ca(r.textContent,c,e),a=["children",c]):"number"===typeof c&&r.textContent!==""+c&&(!0!==l.suppressHydrationWarning&&ca(r.textContent,c,e),a=["children",""+c]):i.hasOwnProperty(o)&&null!=c&&"onScroll"===o&&Xr("scroll",r)}switch(n){case"input":H(r),Y(r,l,!0);break;case"textarea":H(r),le(r);break;case"select":case"option":break;default:"function"===typeof l.onClick&&(r.onclick=ua)}r=a,t.updateQueue=r,null!==r&&(t.flags|=4)}else{o=9===a.nodeType?a:a.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=ie(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=o.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof r.is?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),"select"===n&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[Na]=t,e[ka]=r,Bi(e,t,!1,!1),t.stateNode=e;e:{switch(o=ye(n,r),n){case"dialog":Xr("cancel",e),Xr("close",e),a=r;break;case"iframe":case"object":case"embed":Xr("load",e),a=r;break;case"video":case"audio":for(a=0;a<Hr.length;a++)Xr(Hr[a],e);a=r;break;case"source":Xr("error",e),a=r;break;case"img":case"image":case"link":Xr("error",e),Xr("load",e),a=r;break;case"details":Xr("toggle",e),a=r;break;case"input":X(e,r),a=J(e,r),Xr("invalid",e);break;case"option":default:a=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},a=I({},r,{value:void 0}),Xr("invalid",e);break;case"textarea":ae(e,r),a=re(e,r),Xr("invalid",e)}for(l in ge(n,a),c=a)if(c.hasOwnProperty(l)){var u=c[l];"style"===l?pe(e,u):"dangerouslySetInnerHTML"===l?null!=(u=u?u.__html:void 0)&&ue(e,u):"children"===l?"string"===typeof u?("textarea"!==n||""!==u)&&de(e,u):"number"===typeof u&&de(e,""+u):"suppressContentEditableWarning"!==l&&"suppressHydrationWarning"!==l&&"autoFocus"!==l&&(i.hasOwnProperty(l)?null!=u&&"onScroll"===l&&Xr("scroll",e):null!=u&&v(e,l,u,o))}switch(n){case"input":H(e),Y(e,r,!1);break;case"textarea":H(e),le(e);break;case"option":null!=r.value&&e.setAttribute("value",""+V(r.value));break;case"select":e.multiple=!!r.multiple,null!=(l=r.value)?ne(e,!!r.multiple,l,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof a.onClick&&(e.onclick=ua)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return no(t),null;case 6:if(e&&null!=t.stateNode)Vi(e,t,e.memoizedProps,r);else{if("string"!==typeof r&&null===t.stateNode)throw Error(s(166));if(n=sl(al.current),sl(nl.current),js(t)){if(r=t.stateNode,n=t.memoizedProps,r[Na]=t,(l=r.nodeValue!==n)&&null!==(e=ms))switch(e.tag){case 3:ca(r.nodeValue,n,0!==(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&ca(r.nodeValue,n,0!==(1&e.mode))}l&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[Na]=t,t.stateNode=r}return no(t),null;case 13:if(Ma(ul),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(ps&&null!==hs&&0!==(1&t.mode)&&0===(128&t.flags))Ns(),ks(),t.flags|=98560,l=!1;else if(l=js(t),null!==r&&null!==r.dehydrated){if(null===e){if(!l)throw Error(s(318));if(!(l=null!==(l=t.memoizedState)?l.dehydrated:null))throw Error(s(317));l[Na]=t}else ks(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;no(t),l=!1}else null!==xs&&(gc(xs),xs=null),l=!0;if(!l)return 65536&t.flags?t:null}return 0!==(128&t.flags)?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,0!==(1&t.mode)&&(null===e||0!==(1&ul.current)?0===$o&&($o=3):Ec())),null!==t.updateQueue&&(t.flags|=4),no(t),null);case 4:return il(),Wi(e,t),null===e&&Yr(t.stateNode.containerInfo),no(t),null;case 10:return Fs(t.type._context),no(t),null;case 19:if(Ma(ul),null===(l=t.memoizedState))return no(t),null;if(r=0!==(128&t.flags),null===(o=l.rendering))if(r)to(l,!1);else{if(0!==$o||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(o=dl(e))){for(t.flags|=128,to(l,!1),null!==(r=o.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(l=n).flags&=14680066,null===(o=l.alternate)?(l.childLanes=0,l.lanes=e,l.child=null,l.subtreeFlags=0,l.memoizedProps=null,l.memoizedState=null,l.updateQueue=null,l.dependencies=null,l.stateNode=null):(l.childLanes=o.childLanes,l.lanes=o.lanes,l.child=o.child,l.subtreeFlags=0,l.deletions=null,l.memoizedProps=o.memoizedProps,l.memoizedState=o.memoizedState,l.updateQueue=o.updateQueue,l.type=o.type,e=o.dependencies,l.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Ia(ul,1&ul.current|2),t.child}e=e.sibling}null!==l.tail&&Xe()>Yo&&(t.flags|=128,r=!0,to(l,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=dl(o))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),to(l,!0),null===l.tail&&"hidden"===l.tailMode&&!o.alternate&&!ps)return no(t),null}else 2*Xe()-l.renderingStartTime>Yo&&1073741824!==n&&(t.flags|=128,r=!0,to(l,!1),t.lanes=4194304);l.isBackwards?(o.sibling=t.child,t.child=o):(null!==(n=l.last)?n.sibling=o:t.child=o,l.last=o)}return null!==l.tail?(t=l.tail,l.rendering=t,l.tail=t.sibling,l.renderingStartTime=Xe(),t.sibling=null,n=ul.current,Ia(ul,r?1&n|2:1&n),t):(no(t),null);case 22:case 23:return jc(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&0!==(1&t.mode)?0!==(1073741824&qo)&&(no(t),6&t.subtreeFlags&&(t.flags|=8192)):no(t),null;case 24:case 25:return null}throw Error(s(156,t.tag))}function ao(e,t){switch(fs(t),t.tag){case 1:return qa(t.type)&&Va(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return il(),Ma(Ua),Ma(za),ml(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 5:return cl(t),null;case 13:if(Ma(ul),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(s(340));ks()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return Ma(ul),null;case 4:return il(),null;case 10:return Fs(t.type._context),null;case 22:case 23:return jc(),null;default:return null}}Bi=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Wi=function(){},qi=function(e,t,n,r){var a=e.memoizedProps;if(a!==r){e=t.stateNode,sl(nl.current);var s,l=null;switch(n){case"input":a=J(e,a),r=J(e,r),l=[];break;case"select":a=I({},a,{value:void 0}),r=I({},r,{value:void 0}),l=[];break;case"textarea":a=re(e,a),r=re(e,r),l=[];break;default:"function"!==typeof a.onClick&&"function"===typeof r.onClick&&(e.onclick=ua)}for(u in ge(n,r),n=null,a)if(!r.hasOwnProperty(u)&&a.hasOwnProperty(u)&&null!=a[u])if("style"===u){var o=a[u];for(s in o)o.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else"dangerouslySetInnerHTML"!==u&&"children"!==u&&"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&"autoFocus"!==u&&(i.hasOwnProperty(u)?l||(l=[]):(l=l||[]).push(u,null));for(u in r){var c=r[u];if(o=null!=a?a[u]:void 0,r.hasOwnProperty(u)&&c!==o&&(null!=c||null!=o))if("style"===u)if(o){for(s in o)!o.hasOwnProperty(s)||c&&c.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in c)c.hasOwnProperty(s)&&o[s]!==c[s]&&(n||(n={}),n[s]=c[s])}else n||(l||(l=[]),l.push(u,n)),n=c;else"dangerouslySetInnerHTML"===u?(c=c?c.__html:void 0,o=o?o.__html:void 0,null!=c&&o!==c&&(l=l||[]).push(u,c)):"children"===u?"string"!==typeof c&&"number"!==typeof c||(l=l||[]).push(u,""+c):"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&(i.hasOwnProperty(u)?(null!=c&&"onScroll"===u&&Xr("scroll",e),l||o===c||(l=[])):(l=l||[]).push(u,c))}n&&(l=l||[]).push("style",n);var u=l;(t.updateQueue=u)&&(t.flags|=4)}},Vi=function(e,t,n,r){n!==r&&(t.flags|=4)};var so=!1,lo=!1,io="function"===typeof WeakSet?WeakSet:Set,oo=null;function co(e,t){var n=e.ref;if(null!==n)if("function"===typeof n)try{n(null)}catch(zn){Dc(e,t,zn)}else n.current=null}function uo(e,t,n){try{n()}catch(zn){Dc(e,t,zn)}}var fo=!1;function mo(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var a=r=r.next;do{if((a.tag&e)===e){var s=a.destroy;a.destroy=void 0,void 0!==s&&uo(t,n,s)}a=a.next}while(a!==r)}}function ho(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function po(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"===typeof t?t(e):t.current=e}}function xo(e){var t=e.alternate;null!==t&&(e.alternate=null,xo(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[Na],delete t[ka],delete t[Ea],delete t[Ca],delete t[Oa])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function go(e){return 5===e.tag||3===e.tag||4===e.tag}function yo(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||go(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function vo(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=ua));else if(4!==r&&null!==(e=e.child))for(vo(e,t,n),e=e.sibling;null!==e;)vo(e,t,n),e=e.sibling}function bo(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(bo(e,t,n),e=e.sibling;null!==e;)bo(e,t,n),e=e.sibling}var wo=null,jo=!1;function No(e,t,n){for(n=n.child;null!==n;)ko(e,t,n),n=n.sibling}function ko(e,t,n){if(at&&"function"===typeof at.onCommitFiberUnmount)try{at.onCommitFiberUnmount(rt,n)}catch(Un){}switch(n.tag){case 5:lo||co(n,t);case 6:var r=wo,a=jo;wo=null,No(e,t,n),jo=a,null!==(wo=r)&&(jo?(e=wo,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):wo.removeChild(n.stateNode));break;case 18:null!==wo&&(jo?(e=wo,n=n.stateNode,8===e.nodeType?va(e.parentNode,n):1===e.nodeType&&va(e,n),Bt(e)):va(wo,n.stateNode));break;case 4:r=wo,a=jo,wo=n.stateNode.containerInfo,jo=!0,No(e,t,n),wo=r,jo=a;break;case 0:case 11:case 14:case 15:if(!lo&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){a=r=r.next;do{var s=a,l=s.destroy;s=s.tag,void 0!==l&&(0!==(2&s)||0!==(4&s))&&uo(n,t,l),a=a.next}while(a!==r)}No(e,t,n);break;case 1:if(!lo&&(co(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(Un){Dc(n,t,Un)}No(e,t,n);break;case 21:No(e,t,n);break;case 22:1&n.mode?(lo=(r=lo)||null!==n.memoizedState,No(e,t,n),lo=r):No(e,t,n);break;default:No(e,t,n)}}function So(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new io),t.forEach((function(t){var r=zc.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}}function Eo(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r];try{var l=e,i=t,o=i;e:for(;null!==o;){switch(o.tag){case 5:wo=o.stateNode,jo=!1;break e;case 3:case 4:wo=o.stateNode.containerInfo,jo=!0;break e}o=o.return}if(null===wo)throw Error(s(160));ko(l,i,a),wo=null,jo=!1;var c=a.alternate;null!==c&&(c.return=null),a.return=null}catch(Fn){Dc(a,t,Fn)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)Co(t,e),t=t.sibling}function Co(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Eo(t,e),Oo(e),4&r){try{mo(3,e,e.return),ho(3,e)}catch(On){Dc(e,e.return,On)}try{mo(5,e,e.return)}catch(On){Dc(e,e.return,On)}}break;case 1:Eo(t,e),Oo(e),512&r&&null!==n&&co(n,n.return);break;case 5:if(Eo(t,e),Oo(e),512&r&&null!==n&&co(n,n.return),32&e.flags){var a=e.stateNode;try{de(a,"")}catch(On){Dc(e,e.return,On)}}if(4&r&&null!=(a=e.stateNode)){var l=e.memoizedProps,i=null!==n?n.memoizedProps:l,o=e.type,c=e.updateQueue;if(e.updateQueue=null,null!==c)try{"input"===o&&"radio"===l.type&&null!=l.name&&Z(a,l),ye(o,i);var u=ye(o,l);for(i=0;i<c.length;i+=2){var d=c[i],f=c[i+1];"style"===d?pe(a,f):"dangerouslySetInnerHTML"===d?ue(a,f):"children"===d?de(a,f):v(a,d,f,u)}switch(o){case"input":G(a,l);break;case"textarea":se(a,l);break;case"select":var m=a._wrapperState.wasMultiple;a._wrapperState.wasMultiple=!!l.multiple;var h=l.value;null!=h?ne(a,!!l.multiple,h,!1):m!==!!l.multiple&&(null!=l.defaultValue?ne(a,!!l.multiple,l.defaultValue,!0):ne(a,!!l.multiple,l.multiple?[]:"",!1))}a[ka]=l}catch(On){Dc(e,e.return,On)}}break;case 6:if(Eo(t,e),Oo(e),4&r){if(null===e.stateNode)throw Error(s(162));a=e.stateNode,l=e.memoizedProps;try{a.nodeValue=l}catch(On){Dc(e,e.return,On)}}break;case 3:if(Eo(t,e),Oo(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Bt(t.containerInfo)}catch(On){Dc(e,e.return,On)}break;case 4:default:Eo(t,e),Oo(e);break;case 13:Eo(t,e),Oo(e),8192&(a=e.child).flags&&(l=null!==a.memoizedState,a.stateNode.isHidden=l,!l||null!==a.alternate&&null!==a.alternate.memoizedState||(Go=Xe())),4&r&&So(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(lo=(u=lo)||d,Eo(t,e),lo=u):Eo(t,e),Oo(e),8192&r){if(u=null!==e.memoizedState,(e.stateNode.isHidden=u)&&!d&&0!==(1&e.mode))for(oo=e,d=e.child;null!==d;){for(f=oo=d;null!==oo;){switch(h=(m=oo).child,m.tag){case 0:case 11:case 14:case 15:mo(4,m,m.return);break;case 1:co(m,m.return);var p=m.stateNode;if("function"===typeof p.componentWillUnmount){r=m,n=m.return;try{t=r,p.props=t.memoizedProps,p.state=t.memoizedState,p.componentWillUnmount()}catch(On){Dc(r,n,On)}}break;case 5:co(m,m.return);break;case 22:if(null!==m.memoizedState){Lo(f);continue}}null!==h?(h.return=m,oo=h):Lo(f)}d=d.sibling}e:for(d=null,f=e;;){if(5===f.tag){if(null===d){d=f;try{a=f.stateNode,u?"function"===typeof(l=a.style).setProperty?l.setProperty("display","none","important"):l.display="none":(o=f.stateNode,i=void 0!==(c=f.memoizedProps.style)&&null!==c&&c.hasOwnProperty("display")?c.display:null,o.style.display=he("display",i))}catch(On){Dc(e,e.return,On)}}}else if(6===f.tag){if(null===d)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(On){Dc(e,e.return,On)}}else if((22!==f.tag&&23!==f.tag||null===f.memoizedState||f===e)&&null!==f.child){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;null===f.sibling;){if(null===f.return||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:Eo(t,e),Oo(e),4&r&&So(e);case 21:}}function Oo(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(go(n)){var r=n;break e}n=n.return}throw Error(s(160))}switch(r.tag){case 5:var a=r.stateNode;32&r.flags&&(de(a,""),r.flags&=-33),bo(e,yo(e),a);break;case 3:case 4:var l=r.stateNode.containerInfo;vo(e,yo(e),l);break;default:throw Error(s(161))}}catch(nr){Dc(e,e.return,nr)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function Po(e,t,n){oo=e,_o(e,t,n)}function _o(e,t,n){for(var r=0!==(1&e.mode);null!==oo;){var a=oo,s=a.child;if(22===a.tag&&r){var l=null!==a.memoizedState||so;if(!l){var i=a.alternate,o=null!==i&&null!==i.memoizedState||lo;i=so;var c=lo;if(so=l,(lo=o)&&!c)for(oo=a;null!==oo;)o=(l=oo).child,22===l.tag&&null!==l.memoizedState?To(a):null!==o?(o.return=l,oo=o):To(a);for(;null!==s;)oo=s,_o(s,t,n),s=s.sibling;oo=a,so=i,lo=c}Ro(e)}else 0!==(8772&a.subtreeFlags)&&null!==s?(s.return=a,oo=s):Ro(e)}}function Ro(e){for(;null!==oo;){var t=oo;if(0!==(8772&t.flags)){var n=t.alternate;try{if(0!==(8772&t.flags))switch(t.tag){case 0:case 11:case 15:lo||ho(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!lo)if(null===n)r.componentDidMount();else{var a=t.elementType===t.type?n.memoizedProps:fi(t.type,n.memoizedProps);r.componentDidUpdate(a,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var l=t.updateQueue;null!==l&&el(t,l,r);break;case 3:var i=t.updateQueue;if(null!==i){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}el(t,i,n)}break;case 5:var o=t.stateNode;if(null===n&&4&t.flags){n=o;var c=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":c.autoFocus&&n.focus();break;case"img":c.src&&(n.src=c.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var u=t.alternate;if(null!==u){var d=u.memoizedState;if(null!==d){var f=d.dehydrated;null!==f&&Bt(f)}}}break;default:throw Error(s(163))}lo||512&t.flags&&po(t)}catch(Cn){Dc(t,t.return,Cn)}}if(t===e){oo=null;break}if(null!==(n=t.sibling)){n.return=t.return,oo=n;break}oo=t.return}}function Lo(e){for(;null!==oo;){var t=oo;if(t===e){oo=null;break}var n=t.sibling;if(null!==n){n.return=t.return,oo=n;break}oo=t.return}}function To(e){for(;null!==oo;){var t=oo;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{ho(4,t)}catch(nr){Dc(t,n,nr)}break;case 1:var r=t.stateNode;if("function"===typeof r.componentDidMount){var a=t.return;try{r.componentDidMount()}catch(nr){Dc(t,a,nr)}}var s=t.return;try{po(t)}catch(nr){Dc(t,s,nr)}break;case 5:var l=t.return;try{po(t)}catch(nr){Dc(t,l,nr)}}}catch(nr){Dc(t,t.return,nr)}if(t===e){oo=null;break}var i=t.sibling;if(null!==i){i.return=t.return,oo=i;break}oo=t.return}}var Ao,Do=Math.ceil,Mo=b.ReactCurrentDispatcher,Io=b.ReactCurrentOwner,Fo=b.ReactCurrentBatchConfig,zo=0,Uo=null,Bo=null,Wo=0,qo=0,Vo=Da(0),$o=0,Ho=null,Qo=0,Ko=0,Jo=0,Xo=null,Zo=null,Go=0,Yo=1/0,ec=null,tc=!1,nc=null,rc=null,ac=!1,sc=null,lc=0,ic=0,oc=null,cc=-1,uc=0;function dc(){return 0!==(6&zo)?Xe():-1!==cc?cc:cc=Xe()}function fc(e){return 0===(1&e.mode)?1:0!==(2&zo)&&0!==Wo?Wo&-Wo:null!==Es.transition?(0===uc&&(uc=ht()),uc):0!==(e=yt)?e:e=void 0===(e=window.event)?16:Jt(e.type)}function mc(e,t,n,r){if(50<ic)throw ic=0,oc=null,Error(s(185));xt(e,n,r),0!==(2&zo)&&e===Uo||(e===Uo&&(0===(2&zo)&&(Ko|=n),4===$o&&yc(e,Wo)),hc(e,r),1===n&&0===zo&&0===(1&t.mode)&&(Yo=Xe()+500,Xa&&Ya()))}function hc(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,s=e.pendingLanes;0<s;){var l=31-st(s),i=1<<l,o=a[l];-1===o?0!==(i&n)&&0===(i&r)||(a[l]=ft(i,t)):o<=t&&(e.expiredLanes|=i),s&=~i}}(e,t);var r=dt(e,e===Uo?Wo:0);if(0===r)null!==n&&Qe(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&Qe(n),1===t)0===e.tag?function(e){Xa=!0,Ga(e)}(vc.bind(null,e)):Ga(vc.bind(null,e)),ga((function(){0===(6&zo)&&Ya()})),n=null;else{switch(vt(r)){case 1:n=Ge;break;case 4:n=Ye;break;case 16:default:n=et;break;case 536870912:n=nt}n=Uc(n,pc.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function pc(e,t){if(cc=-1,uc=0,0!==(6&zo))throw Error(s(327));var n=e.callbackNode;if(Tc()&&e.callbackNode!==n)return null;var r=dt(e,e===Uo?Wo:0);if(0===r)return null;if(0!==(30&r)||0!==(r&e.expiredLanes)||t)t=Cc(e,r);else{t=r;var a=zo;zo|=2;var l=Sc();for(Uo===e&&Wo===t||(ec=null,Yo=Xe()+500,Nc(e,t));;)try{Pc();break}catch(Un){kc(e,Un)}Is(),Mo.current=l,zo=a,null!==Bo?t=0:(Uo=null,Wo=0,t=$o)}if(0!==t){if(2===t&&(0!==(a=mt(e))&&(r=a,t=xc(e,a))),1===t)throw n=Ho,Nc(e,0),yc(e,r),hc(e,Xe()),n;if(6===t)yc(e,r);else{if(a=e.current.alternate,0===(30&r)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var a=n[r],s=a.getSnapshot;a=a.value;try{if(!yr(s(),a))return!1}catch(i){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(a)&&(2===(t=Cc(e,r))&&(0!==(l=mt(e))&&(r=l,t=xc(e,l))),1===t))throw n=Ho,Nc(e,0),yc(e,r),hc(e,Xe()),n;switch(e.finishedWork=a,e.finishedLanes=r,t){case 0:case 1:throw Error(s(345));case 2:case 5:Lc(e,Zo,ec);break;case 3:if(yc(e,r),(130023424&r)===r&&10<(t=Go+500-Xe())){if(0!==dt(e,0))break;if(((a=e.suspendedLanes)&r)!==r){dc(),e.pingedLanes|=e.suspendedLanes&a;break}e.timeoutHandle=ha(Lc.bind(null,e,Zo,ec),t);break}Lc(e,Zo,ec);break;case 4:if(yc(e,r),(4194240&r)===r)break;for(t=e.eventTimes,a=-1;0<r;){var i=31-st(r);l=1<<i,(i=t[i])>a&&(a=i),r&=~l}if(r=a,10<(r=(120>(r=Xe()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Do(r/1960))-r)){e.timeoutHandle=ha(Lc.bind(null,e,Zo,ec),r);break}Lc(e,Zo,ec);break;default:throw Error(s(329))}}}return hc(e,Xe()),e.callbackNode===n?pc.bind(null,e):null}function xc(e,t){var n=Xo;return e.current.memoizedState.isDehydrated&&(Nc(e,t).flags|=256),2!==(e=Cc(e,t))&&(t=Zo,Zo=n,null!==t&&gc(t)),e}function gc(e){null===Zo?Zo=e:Zo.push.apply(Zo,e)}function yc(e,t){for(t&=~Jo,t&=~Ko,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-st(t),r=1<<n;e[n]=-1,t&=~r}}function vc(e){if(0!==(6&zo))throw Error(s(327));Tc();var t=dt(e,0);if(0===(1&t))return hc(e,Xe()),null;var n=Cc(e,t);if(0!==e.tag&&2===n){var r=mt(e);0!==r&&(t=r,n=xc(e,r))}if(1===n)throw n=Ho,Nc(e,0),yc(e,t),hc(e,Xe()),n;if(6===n)throw Error(s(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Lc(e,Zo,ec),hc(e,Xe()),null}function bc(e,t){var n=zo;zo|=1;try{return e(t)}finally{0===(zo=n)&&(Yo=Xe()+500,Xa&&Ya())}}function wc(e){null!==sc&&0===sc.tag&&0===(6&zo)&&Tc();var t=zo;zo|=1;var n=Fo.transition,r=yt;try{if(Fo.transition=null,yt=1,e)return e()}finally{yt=r,Fo.transition=n,0===(6&(zo=t))&&Ya()}}function jc(){qo=Vo.current,Ma(Vo)}function Nc(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,pa(n)),null!==Bo)for(n=Bo.return;null!==n;){var r=n;switch(fs(r),r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&Va();break;case 3:il(),Ma(Ua),Ma(za),ml();break;case 5:cl(r);break;case 4:il();break;case 13:case 19:Ma(ul);break;case 10:Fs(r.type._context);break;case 22:case 23:jc()}n=n.return}if(Uo=e,Bo=e=Vc(e.current,null),Wo=qo=t,$o=0,Ho=null,Jo=Ko=Qo=0,Zo=Xo=null,null!==Ws){for(t=0;t<Ws.length;t++)if(null!==(r=(n=Ws[t]).interleaved)){n.interleaved=null;var a=r.next,s=n.pending;if(null!==s){var l=s.next;s.next=a,r.next=l}n.pending=r}Ws=null}return e}function kc(e,t){for(;;){var n=Bo;try{if(Is(),hl.current=oi,bl){for(var r=gl.memoizedState;null!==r;){var a=r.queue;null!==a&&(a.pending=null),r=r.next}bl=!1}if(xl=0,vl=yl=gl=null,wl=!1,jl=0,Io.current=null,null===n||null===n.return){$o=1,Ho=t,Bo=null;break}e:{var l=e,i=n.return,o=n,c=t;if(t=Wo,o.flags|=32768,null!==c&&"object"===typeof c&&"function"===typeof c.then){var u=c,d=o,f=d.tag;if(0===(1&d.mode)&&(0===f||11===f||15===f)){var m=d.alternate;m?(d.updateQueue=m.updateQueue,d.memoizedState=m.memoizedState,d.lanes=m.lanes):(d.updateQueue=null,d.memoizedState=null)}var h=Ei(i);if(null!==h){h.flags&=-257,Ci(h,i,o,0,t),1&h.mode&&Si(l,u,t),c=u;var p=(t=h).updateQueue;if(null===p){var x=new Set;x.add(c),t.updateQueue=x}else p.add(c);break e}if(0===(1&t)){Si(l,u,t),Ec();break e}c=Error(s(426))}else if(ps&&1&o.mode){var g=Ei(i);if(null!==g){0===(65536&g.flags)&&(g.flags|=256),Ci(g,i,o,0,t),Ss(vi(c,o));break e}}l=c=vi(c,o),4!==$o&&($o=2),null===Xo?Xo=[l]:Xo.push(l),l=i;do{switch(l.tag){case 3:l.flags|=65536,t&=-t,l.lanes|=t,Gs(l,Ni(0,c,t));break e;case 1:o=c;var y=l.type,v=l.stateNode;if(0===(128&l.flags)&&("function"===typeof y.getDerivedStateFromError||null!==v&&"function"===typeof v.componentDidCatch&&(null===rc||!rc.has(v)))){l.flags|=65536,t&=-t,l.lanes|=t,Gs(l,ki(l,o,t));break e}}l=l.return}while(null!==l)}Rc(n)}catch(b){t=b,Bo===n&&null!==n&&(Bo=n=n.return);continue}break}}function Sc(){var e=Mo.current;return Mo.current=oi,null===e?oi:e}function Ec(){0!==$o&&3!==$o&&2!==$o||($o=4),null===Uo||0===(268435455&Qo)&&0===(268435455&Ko)||yc(Uo,Wo)}function Cc(e,t){var n=zo;zo|=2;var r=Sc();for(Uo===e&&Wo===t||(ec=null,Nc(e,t));;)try{Oc();break}catch(a){kc(e,a)}if(Is(),zo=n,Mo.current=r,null!==Bo)throw Error(s(261));return Uo=null,Wo=0,$o}function Oc(){for(;null!==Bo;)_c(Bo)}function Pc(){for(;null!==Bo&&!Ke();)_c(Bo)}function _c(e){var t=Ao(e.alternate,e,qo);e.memoizedProps=e.pendingProps,null===t?Rc(e):Bo=t,Io.current=null}function Rc(e){var t=e;do{var n=t.alternate;if(e=t.return,0===(32768&t.flags)){if(null!==(n=ro(n,t,qo)))return void(Bo=n)}else{if(null!==(n=ao(n,t)))return n.flags&=32767,void(Bo=n);if(null===e)return $o=6,void(Bo=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(Bo=t);Bo=t=e}while(null!==t);0===$o&&($o=5)}function Lc(e,t,n){var r=yt,a=Fo.transition;try{Fo.transition=null,yt=1,function(e,t,n,r){do{Tc()}while(null!==sc);if(0!==(6&zo))throw Error(s(327));n=e.finishedWork;var a=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(s(177));e.callbackNode=null,e.callbackPriority=0;var l=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var a=31-st(n),s=1<<a;t[a]=0,r[a]=-1,e[a]=-1,n&=~s}}(e,l),e===Uo&&(Bo=Uo=null,Wo=0),0===(2064&n.subtreeFlags)&&0===(2064&n.flags)||ac||(ac=!0,Uc(et,(function(){return Tc(),null}))),l=0!==(15990&n.flags),0!==(15990&n.subtreeFlags)||l){l=Fo.transition,Fo.transition=null;var i=yt;yt=1;var o=zo;zo|=4,Io.current=null,function(e,t){if(da=qt,kr(e=Nr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,l=r.focusNode;r=r.focusOffset;try{n.nodeType,l.nodeType}catch(ir){n=null;break e}var i=0,o=-1,c=-1,u=0,d=0,f=e,m=null;t:for(;;){for(var h;f!==n||0!==a&&3!==f.nodeType||(o=i+a),f!==l||0!==r&&3!==f.nodeType||(c=i+r),3===f.nodeType&&(i+=f.nodeValue.length),null!==(h=f.firstChild);)m=f,f=h;for(;;){if(f===e)break t;if(m===n&&++u===a&&(o=i),m===l&&++d===r&&(c=i),null!==(h=f.nextSibling))break;m=(f=m).parentNode}f=h}n=-1===o||-1===c?null:{start:o,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(fa={focusedElem:e,selectionRange:n},qt=!1,oo=t;null!==oo;)if(e=(t=oo).child,0!==(1028&t.subtreeFlags)&&null!==e)e.return=t,oo=e;else for(;null!==oo;){t=oo;try{var p=t.alternate;if(0!==(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==p){var x=p.memoizedProps,g=p.memoizedState,y=t.stateNode,v=y.getSnapshotBeforeUpdate(t.elementType===t.type?x:fi(t.type,x),g);y.__reactInternalSnapshotBeforeUpdate=v}break;case 3:var b=t.stateNode.containerInfo;1===b.nodeType?b.textContent="":9===b.nodeType&&b.documentElement&&b.removeChild(b.documentElement);break;default:throw Error(s(163))}}catch(ir){Dc(t,t.return,ir)}if(null!==(e=t.sibling)){e.return=t.return,oo=e;break}oo=t.return}p=fo,fo=!1}(e,n),Co(n,e),Sr(fa),qt=!!da,fa=da=null,e.current=n,Po(n,e,a),Je(),zo=o,yt=i,Fo.transition=l}else e.current=n;if(ac&&(ac=!1,sc=e,lc=a),l=e.pendingLanes,0===l&&(rc=null),function(e){if(at&&"function"===typeof at.onCommitFiberRoot)try{at.onCommitFiberRoot(rt,e,void 0,128===(128&e.current.flags))}catch(t){}}(n.stateNode),hc(e,Xe()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)a=t[n],r(a.value,{componentStack:a.stack,digest:a.digest});if(tc)throw tc=!1,e=nc,nc=null,e;0!==(1&lc)&&0!==e.tag&&Tc(),l=e.pendingLanes,0!==(1&l)?e===oc?ic++:(ic=0,oc=e):ic=0,Ya()}(e,t,n,r)}finally{Fo.transition=a,yt=r}return null}function Tc(){if(null!==sc){var e=vt(lc),t=Fo.transition,n=yt;try{if(Fo.transition=null,yt=16>e?16:e,null===sc)var r=!1;else{if(e=sc,sc=null,lc=0,0!==(6&zo))throw Error(s(331));var a=zo;for(zo|=4,oo=e.current;null!==oo;){var l=oo,i=l.child;if(0!==(16&oo.flags)){var o=l.deletions;if(null!==o){for(var c=0;c<o.length;c++){var u=o[c];for(oo=u;null!==oo;){var d=oo;switch(d.tag){case 0:case 11:case 15:mo(8,d,l)}var f=d.child;if(null!==f)f.return=d,oo=f;else for(;null!==oo;){var m=(d=oo).sibling,h=d.return;if(xo(d),d===u){oo=null;break}if(null!==m){m.return=h,oo=m;break}oo=h}}}var p=l.alternate;if(null!==p){var x=p.child;if(null!==x){p.child=null;do{var g=x.sibling;x.sibling=null,x=g}while(null!==x)}}oo=l}}if(0!==(2064&l.subtreeFlags)&&null!==i)i.return=l,oo=i;else e:for(;null!==oo;){if(0!==(2048&(l=oo).flags))switch(l.tag){case 0:case 11:case 15:mo(9,l,l.return)}var y=l.sibling;if(null!==y){y.return=l.return,oo=y;break e}oo=l.return}}var v=e.current;for(oo=v;null!==oo;){var b=(i=oo).child;if(0!==(2064&i.subtreeFlags)&&null!==b)b.return=i,oo=b;else e:for(i=v;null!==oo;){if(0!==(2048&(o=oo).flags))try{switch(o.tag){case 0:case 11:case 15:ho(9,o)}}catch(j){Dc(o,o.return,j)}if(o===i){oo=null;break e}var w=o.sibling;if(null!==w){w.return=o.return,oo=w;break e}oo=o.return}}if(zo=a,Ya(),at&&"function"===typeof at.onPostCommitFiberRoot)try{at.onPostCommitFiberRoot(rt,e)}catch(j){}r=!0}return r}finally{yt=n,Fo.transition=t}}return!1}function Ac(e,t,n){e=Xs(e,t=Ni(0,t=vi(n,t),1),1),t=dc(),null!==e&&(xt(e,1,t),hc(e,t))}function Dc(e,t,n){if(3===e.tag)Ac(e,e,n);else for(;null!==t;){if(3===t.tag){Ac(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===rc||!rc.has(r))){t=Xs(t,e=ki(t,e=vi(n,e),1),1),e=dc(),null!==t&&(xt(t,1,e),hc(t,e));break}}t=t.return}}function Mc(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=dc(),e.pingedLanes|=e.suspendedLanes&n,Uo===e&&(Wo&n)===n&&(4===$o||3===$o&&(130023424&Wo)===Wo&&500>Xe()-Go?Nc(e,0):Jo|=n),hc(e,t)}function Ic(e,t){0===t&&(0===(1&e.mode)?t=1:(t=ct,0===(130023424&(ct<<=1))&&(ct=4194304)));var n=dc();null!==(e=$s(e,t))&&(xt(e,t,n),hc(e,n))}function Fc(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Ic(e,n)}function zc(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(s(314))}null!==r&&r.delete(t),Ic(e,n)}function Uc(e,t){return He(e,t)}function Bc(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Wc(e,t,n,r){return new Bc(e,t,n,r)}function qc(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Vc(e,t){var n=e.alternate;return null===n?((n=Wc(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function $c(e,t,n,r,a,l){var i=2;if(r=e,"function"===typeof e)qc(e)&&(i=1);else if("string"===typeof e)i=5;else e:switch(e){case N:return Hc(n.children,a,l,t);case k:i=8,a|=8;break;case S:return(e=Wc(12,n,t,2|a)).elementType=S,e.lanes=l,e;case P:return(e=Wc(13,n,t,a)).elementType=P,e.lanes=l,e;case _:return(e=Wc(19,n,t,a)).elementType=_,e.lanes=l,e;case T:return Qc(n,a,l,t);default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case E:i=10;break e;case C:i=9;break e;case O:i=11;break e;case R:i=14;break e;case L:i=16,r=null;break e}throw Error(s(130,null==e?e:typeof e,""))}return(t=Wc(i,n,t,a)).elementType=e,t.type=r,t.lanes=l,t}function Hc(e,t,n,r){return(e=Wc(7,e,r,t)).lanes=n,e}function Qc(e,t,n,r){return(e=Wc(22,e,r,t)).elementType=T,e.lanes=n,e.stateNode={isHidden:!1},e}function Kc(e,t,n){return(e=Wc(6,e,null,t)).lanes=n,e}function Jc(e,t,n){return(t=Wc(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Xc(e,t,n,r,a){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=pt(0),this.expirationTimes=pt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=pt(0),this.identifierPrefix=r,this.onRecoverableError=a,this.mutableSourceEagerHydrationData=null}function Zc(e,t,n,r,a,s,l,i,o){return e=new Xc(e,t,n,i,o),1===t?(t=1,!0===s&&(t|=8)):t=0,s=Wc(3,null,null,t),e.current=s,s.stateNode=e,s.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Qs(s),e}function Gc(e){if(!e)return Fa;e:{if(Be(e=e._reactInternals)!==e||1!==e.tag)throw Error(s(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(qa(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(s(171))}if(1===e.tag){var n=e.type;if(qa(n))return Ha(e,n,t)}return t}function Yc(e,t,n,r,a,s,l,i,o){return(e=Zc(n,r,!0,e,0,s,0,i,o)).context=Gc(null),n=e.current,(s=Js(r=dc(),a=fc(n))).callback=void 0!==t&&null!==t?t:null,Xs(n,s,a),e.current.lanes=a,xt(e,a,r),hc(e,r),e}function eu(e,t,n,r){var a=t.current,s=dc(),l=fc(a);return n=Gc(n),null===t.context?t.context=n:t.pendingContext=n,(t=Js(s,l)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Xs(a,t,l))&&(mc(e,a,l,s),Zs(e,a,l)),l}function tu(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function nu(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function ru(e,t){nu(e,t),(e=e.alternate)&&nu(e,t)}Ao=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||Ua.current)Pi=!0;else{if(0===(e.lanes&n)&&0===(128&t.flags))return Pi=!1,function(e,t,n){switch(t.tag){case 3:zi(t),ks();break;case 5:ol(t);break;case 1:qa(t.type)&&Qa(t);break;case 4:ll(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,a=t.memoizedProps.value;Ia(Ts,r._currentValue),r._currentValue=a;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(Ia(ul,1&ul.current),t.flags|=128,null):0!==(n&t.child.childLanes)?Qi(e,t,n):(Ia(ul,1&ul.current),null!==(e=eo(e,t,n))?e.sibling:null);Ia(ul,1&ul.current);break;case 19:if(r=0!==(n&t.childLanes),0!==(128&e.flags)){if(r)return Gi(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),Ia(ul,ul.current),r)break;return null;case 22:case 23:return t.lanes=0,Ai(e,t,n)}return eo(e,t,n)}(e,t,n);Pi=0!==(131072&e.flags)}else Pi=!1,ps&&0!==(1048576&t.flags)&&us(t,rs,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Yi(e,t),e=t.pendingProps;var a=Wa(t,za.current);Us(t,n),a=El(null,t,r,e,a,n);var l=Cl();return t.flags|=1,"object"===typeof a&&null!==a&&"function"===typeof a.render&&void 0===a.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,qa(r)?(l=!0,Qa(t)):l=!1,t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,Qs(t),a.updater=hi,t.stateNode=a,a._reactInternals=t,yi(t,r,e,n),t=Fi(null,t,r,!0,l,n)):(t.tag=0,ps&&l&&ds(t),_i(null,t,a,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Yi(e,t),e=t.pendingProps,r=(a=r._init)(r._payload),t.type=r,a=t.tag=function(e){if("function"===typeof e)return qc(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===O)return 11;if(e===R)return 14}return 2}(r),e=fi(r,e),a){case 0:t=Mi(null,t,r,e,n);break e;case 1:t=Ii(null,t,r,e,n);break e;case 11:t=Ri(null,t,r,e,n);break e;case 14:t=Li(null,t,r,fi(r.type,e),n);break e}throw Error(s(306,r,""))}return t;case 0:return r=t.type,a=t.pendingProps,Mi(e,t,r,a=t.elementType===r?a:fi(r,a),n);case 1:return r=t.type,a=t.pendingProps,Ii(e,t,r,a=t.elementType===r?a:fi(r,a),n);case 3:e:{if(zi(t),null===e)throw Error(s(387));r=t.pendingProps,a=(l=t.memoizedState).element,Ks(e,t),Ys(t,r,null,n);var i=t.memoizedState;if(r=i.element,l.isDehydrated){if(l={element:r,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=l,t.memoizedState=l,256&t.flags){t=Ui(e,t,r,n,a=vi(Error(s(423)),t));break e}if(r!==a){t=Ui(e,t,r,n,a=vi(Error(s(424)),t));break e}for(hs=ba(t.stateNode.containerInfo.firstChild),ms=t,ps=!0,xs=null,n=Ls(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(ks(),r===a){t=eo(e,t,n);break e}_i(e,t,r,n)}t=t.child}return t;case 5:return ol(t),null===e&&bs(t),r=t.type,a=t.pendingProps,l=null!==e?e.memoizedProps:null,i=a.children,ma(r,a)?i=null:null!==l&&ma(r,l)&&(t.flags|=32),Di(e,t),_i(e,t,i,n),t.child;case 6:return null===e&&bs(t),null;case 13:return Qi(e,t,n);case 4:return ll(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=Rs(t,null,r,n):_i(e,t,r,n),t.child;case 11:return r=t.type,a=t.pendingProps,Ri(e,t,r,a=t.elementType===r?a:fi(r,a),n);case 7:return _i(e,t,t.pendingProps,n),t.child;case 8:case 12:return _i(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,a=t.pendingProps,l=t.memoizedProps,i=a.value,Ia(Ts,r._currentValue),r._currentValue=i,null!==l)if(yr(l.value,i)){if(l.children===a.children&&!Ua.current){t=eo(e,t,n);break e}}else for(null!==(l=t.child)&&(l.return=t);null!==l;){var o=l.dependencies;if(null!==o){i=l.child;for(var c=o.firstContext;null!==c;){if(c.context===r){if(1===l.tag){(c=Js(-1,n&-n)).tag=2;var u=l.updateQueue;if(null!==u){var d=(u=u.shared).pending;null===d?c.next=c:(c.next=d.next,d.next=c),u.pending=c}}l.lanes|=n,null!==(c=l.alternate)&&(c.lanes|=n),zs(l.return,n,t),o.lanes|=n;break}c=c.next}}else if(10===l.tag)i=l.type===t.type?null:l.child;else if(18===l.tag){if(null===(i=l.return))throw Error(s(341));i.lanes|=n,null!==(o=i.alternate)&&(o.lanes|=n),zs(i,n,t),i=l.sibling}else i=l.child;if(null!==i)i.return=l;else for(i=l;null!==i;){if(i===t){i=null;break}if(null!==(l=i.sibling)){l.return=i.return,i=l;break}i=i.return}l=i}_i(e,t,a.children,n),t=t.child}return t;case 9:return a=t.type,r=t.pendingProps.children,Us(t,n),r=r(a=Bs(a)),t.flags|=1,_i(e,t,r,n),t.child;case 14:return a=fi(r=t.type,t.pendingProps),Li(e,t,r,a=fi(r.type,a),n);case 15:return Ti(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:fi(r,a),Yi(e,t),t.tag=1,qa(r)?(e=!0,Qa(t)):e=!1,Us(t,n),xi(t,r,a),yi(t,r,a,n),Fi(null,t,r,!0,e,n);case 19:return Gi(e,t,n);case 22:return Ai(e,t,n)}throw Error(s(156,t.tag))};var au="function"===typeof reportError?reportError:function(e){console.error(e)};function su(e){this._internalRoot=e}function lu(e){this._internalRoot=e}function iu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function ou(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function cu(){}function uu(e,t,n,r,a){var s=n._reactRootContainer;if(s){var l=s;if("function"===typeof a){var i=a;a=function(){var e=tu(l);i.call(e)}}eu(t,l,e,a)}else l=function(e,t,n,r,a){if(a){if("function"===typeof r){var s=r;r=function(){var e=tu(l);s.call(e)}}var l=Yc(t,r,e,0,null,!1,0,"",cu);return e._reactRootContainer=l,e[Sa]=l.current,Yr(8===e.nodeType?e.parentNode:e),wc(),l}for(;a=e.lastChild;)e.removeChild(a);if("function"===typeof r){var i=r;r=function(){var e=tu(o);i.call(e)}}var o=Zc(e,0,!1,null,0,!1,0,"",cu);return e._reactRootContainer=o,e[Sa]=o.current,Yr(8===e.nodeType?e.parentNode:e),wc((function(){eu(t,o,n,r)})),o}(n,t,e,a,r);return tu(l)}lu.prototype.render=su.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(s(409));eu(e,t,null,null)},lu.prototype.unmount=su.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;wc((function(){eu(null,e,null,null)})),t[Sa]=null}},lu.prototype.unstable_scheduleHydration=function(e){if(e){var t=Nt();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Lt.length&&0!==t&&t<Lt[n].priority;n++);Lt.splice(n,0,e),0===n&&Mt(e)}},bt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=ut(t.pendingLanes);0!==n&&(gt(t,1|n),hc(t,Xe()),0===(6&zo)&&(Yo=Xe()+500,Ya()))}break;case 13:wc((function(){var t=$s(e,1);if(null!==t){var n=dc();mc(t,e,1,n)}})),ru(e,1)}},wt=function(e){if(13===e.tag){var t=$s(e,134217728);if(null!==t)mc(t,e,134217728,dc());ru(e,134217728)}},jt=function(e){if(13===e.tag){var t=fc(e),n=$s(e,t);if(null!==n)mc(n,e,t,dc());ru(e,t)}},Nt=function(){return yt},kt=function(e,t){var n=yt;try{return yt=e,t()}finally{yt=n}},we=function(e,t,n){switch(t){case"input":if(G(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=La(r);if(!a)throw Error(s(90));Q(r),G(r,a)}}}break;case"textarea":se(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},Ce=bc,Oe=wc;var du={usingClientEntryPoint:!1,Events:[_a,Ra,La,Se,Ee,bc]},fu={findFiberByHostInstance:Pa,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},mu={bundleType:fu.bundleType,version:fu.version,rendererPackageName:fu.rendererPackageName,rendererConfig:fu.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:b.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=Ve(e))?null:e.stateNode},findFiberByHostInstance:fu.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var hu=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!hu.isDisabled&&hu.supportsFiber)try{rt=hu.inject(mu),at=hu}catch(Ln){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=du,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!iu(t))throw Error(s(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:j,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!iu(e))throw Error(s(299));var n=!1,r="",a=au;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(a=t.onRecoverableError)),t=Zc(e,1,!1,null,0,n,0,r,a),e[Sa]=t.current,Yr(8===e.nodeType?e.parentNode:e),new su(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(s(188));throw e=Object.keys(e).join(","),Error(s(268,e))}return e=null===(e=Ve(t))?null:e.stateNode},t.flushSync=function(e){return wc(e)},t.hydrate=function(e,t,n){if(!ou(t))throw Error(s(200));return uu(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!iu(e))throw Error(s(405));var r=null!=n&&n.hydratedSources||null,a=!1,l="",i=au;if(null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(a=!0),void 0!==n.identifierPrefix&&(l=n.identifierPrefix),void 0!==n.onRecoverableError&&(i=n.onRecoverableError)),t=Yc(t,null,e,1,null!=n?n:null,a,0,l,i),e[Sa]=t.current,Yr(e),r)for(e=0;e<r.length;e++)a=(a=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,a]:t.mutableSourceEagerHydrationData.push(n,a);return new lu(t)},t.render=function(e,t,n){if(!ou(t))throw Error(s(200));return uu(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!ou(e))throw Error(s(40));return!!e._reactRootContainer&&(wc((function(){uu(null,null,e,!1,(function(){e._reactRootContainer=null,e[Sa]=null}))})),!0)},t.unstable_batchedUpdates=bc,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!ou(n))throw Error(s(200));if(null==e||void 0===e._reactInternals)throw Error(s(38));return uu(e,t,n,!1,r)},t.version="18.3.1-next-f1338f8080-20240426"},763:(e,t,n)=>{e.exports=n(983)},853:(e,t,n)=>{e.exports=n(234)},950:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(730)},983:(e,t)=>{var n="function"===typeof Symbol&&Symbol.for,r=n?Symbol.for("react.element"):60103,a=n?Symbol.for("react.portal"):60106,s=n?Symbol.for("react.fragment"):60107,l=n?Symbol.for("react.strict_mode"):60108,i=n?Symbol.for("react.profiler"):60114,o=n?Symbol.for("react.provider"):60109,c=n?Symbol.for("react.context"):60110,u=n?Symbol.for("react.async_mode"):60111,d=n?Symbol.for("react.concurrent_mode"):60111,f=n?Symbol.for("react.forward_ref"):60112,m=n?Symbol.for("react.suspense"):60113,h=n?Symbol.for("react.suspense_list"):60120,p=n?Symbol.for("react.memo"):60115,x=n?Symbol.for("react.lazy"):60116,g=n?Symbol.for("react.block"):60121,y=n?Symbol.for("react.fundamental"):60117,v=n?Symbol.for("react.responder"):60118,b=n?Symbol.for("react.scope"):60119;function w(e){if("object"===typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case u:case d:case s:case i:case l:case m:return e;default:switch(e=e&&e.$$typeof){case c:case f:case x:case p:case o:return e;default:return t}}case a:return t}}}function j(e){return w(e)===d}t.AsyncMode=u,t.ConcurrentMode=d,t.ContextConsumer=c,t.ContextProvider=o,t.Element=r,t.ForwardRef=f,t.Fragment=s,t.Lazy=x,t.Memo=p,t.Portal=a,t.Profiler=i,t.StrictMode=l,t.Suspense=m,t.isAsyncMode=function(e){return j(e)||w(e)===u},t.isConcurrentMode=j,t.isContextConsumer=function(e){return w(e)===c},t.isContextProvider=function(e){return w(e)===o},t.isElement=function(e){return"object"===typeof e&&null!==e&&e.$$typeof===r},t.isForwardRef=function(e){return w(e)===f},t.isFragment=function(e){return w(e)===s},t.isLazy=function(e){return w(e)===x},t.isMemo=function(e){return w(e)===p},t.isPortal=function(e){return w(e)===a},t.isProfiler=function(e){return w(e)===i},t.isStrictMode=function(e){return w(e)===l},t.isSuspense=function(e){return w(e)===m},t.isValidElementType=function(e){return"string"===typeof e||"function"===typeof e||e===s||e===d||e===i||e===l||e===m||e===h||"object"===typeof e&&null!==e&&(e.$$typeof===x||e.$$typeof===p||e.$$typeof===o||e.$$typeof===c||e.$$typeof===f||e.$$typeof===y||e.$$typeof===v||e.$$typeof===b||e.$$typeof===g)},t.typeOf=w}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var s=t[r]={exports:{}};return e[r](s,s.exports,n),s.exports}n.m=e,(()=>{var e,t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__;n.t=function(r,a){if(1&a&&(r=this(r)),8&a)return r;if("object"===typeof r&&r){if(4&a&&r.__esModule)return r;if(16&a&&"function"===typeof r.then)return r}var s=Object.create(null);n.r(s);var l={};e=e||[null,t({}),t([]),t(t)];for(var i=2&a&&r;"object"==typeof i&&!~e.indexOf(i);i=t(i))Object.getOwnPropertyNames(i).forEach((e=>l[e]=()=>r[e]));return l.default=()=>r,n.d(s,l),s}})(),n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.f={},n.e=e=>Promise.all(Object.keys(n.f).reduce(((t,r)=>(n.f[r](e,t),t)),[])),n.u=e=>"static/js/"+e+".322e7791.chunk.js",n.miniCssF=e=>{},n.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={},t="cainuro-orchestrator-ui:";n.l=(r,a,s,l)=>{if(e[r])e[r].push(a);else{var i,o;if(void 0!==s)for(var c=document.getElementsByTagName("script"),u=0;u<c.length;u++){var d=c[u];if(d.getAttribute("src")==r||d.getAttribute("data-webpack")==t+s){i=d;break}}i||(o=!0,(i=document.createElement("script")).charset="utf-8",i.timeout=120,n.nc&&i.setAttribute("nonce",n.nc),i.setAttribute("data-webpack",t+s),i.src=r),e[r]=[a];var f=(t,n)=>{i.onerror=i.onload=null,clearTimeout(m);var a=e[r];if(delete e[r],i.parentNode&&i.parentNode.removeChild(i),a&&a.forEach((e=>e(n))),t)return t(n)},m=setTimeout(f.bind(null,void 0,{type:"timeout",target:i}),12e4);i.onerror=f.bind(null,i.onerror),i.onload=f.bind(null,i.onload),o&&document.head.appendChild(i)}}})(),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.p="/",(()=>{var e={792:0};n.f.j=(t,r)=>{var a=n.o(e,t)?e[t]:void 0;if(0!==a)if(a)r.push(a[2]);else{var s=new Promise(((n,r)=>a=e[t]=[n,r]));r.push(a[2]=s);var l=n.p+n.u(t),i=new Error;n.l(l,(r=>{if(n.o(e,t)&&(0!==(a=e[t])&&(e[t]=void 0),a)){var s=r&&("load"===r.type?"missing":r.type),l=r&&r.target&&r.target.src;i.message="Loading chunk "+t+" failed.\n("+s+": "+l+")",i.name="ChunkLoadError",i.type=s,i.request=l,a[1](i)}}),"chunk-"+t,t)}};var t=(t,r)=>{var a,s,l=r[0],i=r[1],o=r[2],c=0;if(l.some((t=>0!==e[t]))){for(a in i)n.o(i,a)&&(n.m[a]=i[a]);if(o)o(n)}for(t&&t(r);c<l.length;c++)s=l[c],n.o(e,s)&&e[s]&&e[s][0](),e[s]=0},r=self.webpackChunkcainuro_orchestrator_ui=self.webpackChunkcainuro_orchestrator_ui||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})();var r={};n.r(r),n.d(r,{hasBrowserEnv:()=>ws,hasStandardBrowserEnv:()=>Ns,hasStandardBrowserWebWorkerEnv:()=>ks,navigator:()=>js,origin:()=>Ss});var a=n(43),s=n.t(a,2),l=n(391),i=n(461),o=n(443),c=n(950),u=n.t(c,2);let d=function(e){e()};const f=()=>d,m=Symbol.for("react-redux-context"),h="undefined"!==typeof globalThis?globalThis:{};function p(){var e;if(!a.createContext)return{};const t=null!=(e=h[m])?e:h[m]=new Map;let n=t.get(a.createContext);return n||(n=a.createContext(null),t.set(a.createContext,n)),n}const x=p();function g(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:x;return function(){return(0,a.useContext)(e)}}const y=g();let v=()=>{throw new Error("uSES not initialized!")};const b=(e,t)=>e===t;function w(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:x;const t=e===x?y:g(e);return function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{equalityFn:r=b,stabilityCheck:s,noopCheck:l}="function"===typeof n?{equalityFn:n}:n;const{store:i,subscription:o,getServerState:c,stabilityCheck:u,noopCheck:d}=t(),f=((0,a.useRef)(!0),(0,a.useCallback)({[e.name]:t=>e(t)}[e.name],[e,u,s])),m=v(o.addNestedSub,i.getState,c||i.getState,f,r);return(0,a.useDebugValue)(m),m}}const j=w();n(219),n(86);const N={notify(){},get:()=>[]};function k(e,t){let n,r=N,a=0,s=!1;function l(){c.onStateChange&&c.onStateChange()}function i(){a++,n||(n=t?t.addNestedSub(l):e.subscribe(l),r=function(){const e=f();let t=null,n=null;return{clear(){t=null,n=null},notify(){e((()=>{let e=t;for(;e;)e.callback(),e=e.next}))},get(){let e=[],n=t;for(;n;)e.push(n),n=n.next;return e},subscribe(e){let r=!0,a=n={callback:e,next:null,prev:n};return a.prev?a.prev.next=a:t=a,function(){r&&null!==t&&(r=!1,a.next?a.next.prev=a.prev:n=a.prev,a.prev?a.prev.next=a.next:t=a.next)}}}}())}function o(){a--,n&&0===a&&(n(),n=void 0,r.clear(),r=N)}const c={addNestedSub:function(e){i();const t=r.subscribe(e);let n=!1;return()=>{n||(n=!0,t(),o())}},notifyNestedSubs:function(){r.notify()},handleChangeWrapper:l,isSubscribed:function(){return s},trySubscribe:function(){s||(s=!0,i())},tryUnsubscribe:function(){s&&(s=!1,o())},getListeners:()=>r};return c}const S=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement)?a.useLayoutEffect:a.useEffect;let E=null;const C=function(e){let{store:t,context:n,children:r,serverState:s,stabilityCheck:l="once",noopCheck:i="once"}=e;const o=a.useMemo((()=>{const e=k(t);return{store:t,subscription:e,getServerState:s?()=>s:void 0,stabilityCheck:l,noopCheck:i}}),[t,s,l,i]),c=a.useMemo((()=>t.getState()),[t]);S((()=>{const{subscription:e}=o;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),c!==t.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}}),[o,c]);const u=n||x;return a.createElement(u.Provider,{value:o},r)};function O(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:x;const t=e===x?y:g(e);return function(){const{store:e}=t();return e}}const P=O();function _(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:x;const t=e===x?P:O(e);return function(){return t().dispatch}}const R=_();var L,T;function A(){return A=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},A.apply(this,arguments)}(e=>{v=e})(o.useSyncExternalStoreWithSelector),(e=>{E=e})(i.useSyncExternalStore),L=c.unstable_batchedUpdates,d=L,function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"}(T||(T={}));const D="popstate";function M(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}function I(e,t){if(!e){"undefined"!==typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function F(e,t){return{usr:e.state,key:e.key,idx:t}}function z(e,t,n,r){return void 0===n&&(n=null),A({pathname:"string"===typeof e?e:e.pathname,search:"",hash:""},"string"===typeof t?B(t):t,{state:n,key:t&&t.key||r||Math.random().toString(36).substr(2,8)})}function U(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function B(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function W(e,t,n,r){void 0===r&&(r={});let{window:a=document.defaultView,v5Compat:s=!1}=r,l=a.history,i=T.Pop,o=null,c=u();function u(){return(l.state||{idx:null}).idx}function d(){i=T.Pop;let e=u(),t=null==e?null:e-c;c=e,o&&o({action:i,location:m.location,delta:t})}function f(e){let t="null"!==a.location.origin?a.location.origin:a.location.href,n="string"===typeof e?e:U(e);return n=n.replace(/ $/,"%20"),M(t,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,t)}null==c&&(c=0,l.replaceState(A({},l.state,{idx:c}),""));let m={get action(){return i},get location(){return e(a,l)},listen(e){if(o)throw new Error("A history only accepts one active listener");return a.addEventListener(D,d),o=e,()=>{a.removeEventListener(D,d),o=null}},createHref:e=>t(a,e),createURL:f,encodeLocation(e){let t=f(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){i=T.Push;let r=z(m.location,e,t);n&&n(r,e),c=u()+1;let d=F(r,c),f=m.createHref(r);try{l.pushState(d,"",f)}catch(h){if(h instanceof DOMException&&"DataCloneError"===h.name)throw h;a.location.assign(f)}s&&o&&o({action:i,location:m.location,delta:1})},replace:function(e,t){i=T.Replace;let r=z(m.location,e,t);n&&n(r,e),c=u();let a=F(r,c),d=m.createHref(r);l.replaceState(a,"",d),s&&o&&o({action:i,location:m.location,delta:0})},go:e=>l.go(e)};return m}var q;!function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"}(q||(q={}));new Set(["lazy","caseSensitive","path","id","index","children"]);function V(e,t,n){return void 0===n&&(n="/"),$(e,t,n,!1)}function $(e,t,n,r){let a=se(("string"===typeof t?B(t):t).pathname||"/",n);if(null==a)return null;let s=H(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every(((e,n)=>e===t[n]));return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(s);let l=null;for(let i=0;null==l&&i<s.length;++i){let e=ae(a);l=ne(s[i],e,r)}return l}function H(e,t,n,r){void 0===t&&(t=[]),void 0===n&&(n=[]),void 0===r&&(r="");let a=(e,a,s)=>{let l={relativePath:void 0===s?e.path||"":s,caseSensitive:!0===e.caseSensitive,childrenIndex:a,route:e};l.relativePath.startsWith("/")&&(M(l.relativePath.startsWith(r),'Absolute route path "'+l.relativePath+'" nested under path "'+r+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),l.relativePath=l.relativePath.slice(r.length));let i=ue([r,l.relativePath]),o=n.concat(l);e.children&&e.children.length>0&&(M(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+i+'".'),H(e.children,t,o,i)),(null!=e.path||e.index)&&t.push({path:i,score:te(i,e.index),routesMeta:o})};return e.forEach(((e,t)=>{var n;if(""!==e.path&&null!=(n=e.path)&&n.includes("?"))for(let r of Q(e.path))a(e,t,r);else a(e,t)})),t}function Q(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,a=n.endsWith("?"),s=n.replace(/\?$/,"");if(0===r.length)return a?[s,""]:[s];let l=Q(r.join("/")),i=[];return i.push(...l.map((e=>""===e?s:[s,e].join("/")))),a&&i.push(...l),i.map((t=>e.startsWith("/")&&""===t?"/":t))}const K=/^:[\w-]+$/,J=3,X=2,Z=1,G=10,Y=-2,ee=e=>"*"===e;function te(e,t){let n=e.split("/"),r=n.length;return n.some(ee)&&(r+=Y),t&&(r+=X),n.filter((e=>!ee(e))).reduce(((e,t)=>e+(K.test(t)?J:""===t?Z:G)),r)}function ne(e,t,n){void 0===n&&(n=!1);let{routesMeta:r}=e,a={},s="/",l=[];for(let i=0;i<r.length;++i){let e=r[i],o=i===r.length-1,c="/"===s?t:t.slice(s.length)||"/",u=re({path:e.relativePath,caseSensitive:e.caseSensitive,end:o},c),d=e.route;if(!u&&o&&n&&!r[r.length-1].route.index&&(u=re({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},c)),!u)return null;Object.assign(a,u.params),l.push({params:a,pathname:ue([s,u.pathname]),pathnameBase:de(ue([s,u.pathnameBase])),route:d}),"/"!==u.pathnameBase&&(s=ue([s,u.pathnameBase]))}return l}function re(e,t){"string"===typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=function(e,t,n){void 0===t&&(t=!1);void 0===n&&(n=!0);I("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)")));e.endsWith("*")?(r.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))");let s=new RegExp(a,t?void 0:"i");return[s,r]}(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let s=a[0],l=s.replace(/(.)\/+$/,"$1"),i=a.slice(1),o=r.reduce(((e,t,n)=>{let{paramName:r,isOptional:a}=t;if("*"===r){let e=i[n]||"";l=s.slice(0,s.length-e.length).replace(/(.)\/+$/,"$1")}const o=i[n];return e[r]=a&&!o?void 0:(o||"").replace(/%2F/g,"/"),e}),{});return{params:o,pathname:s,pathnameBase:l,pattern:e}}function ae(e){try{return e.split("/").map((e=>decodeURIComponent(e).replace(/\//g,"%2F"))).join("/")}catch(t){return I(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function se(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function le(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function ie(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}function oe(e,t){let n=ie(e);return t?n.map(((e,t)=>t===n.length-1?e.pathname:e.pathnameBase)):n.map((e=>e.pathnameBase))}function ce(e,t,n,r){let a;void 0===r&&(r=!1),"string"===typeof e?a=B(e):(a=A({},e),M(!a.pathname||!a.pathname.includes("?"),le("?","pathname","search",a)),M(!a.pathname||!a.pathname.includes("#"),le("#","pathname","hash",a)),M(!a.search||!a.search.includes("#"),le("#","search","hash",a)));let s,l=""===e||""===a.pathname,i=l?"/":a.pathname;if(null==i)s=n;else{let e=t.length-1;if(!r&&i.startsWith("..")){let t=i.split("/");for(;".."===t[0];)t.shift(),e-=1;a.pathname=t.join("/")}s=e>=0?t[e]:"/"}let o=function(e,t){void 0===t&&(t="/");let{pathname:n,search:r="",hash:a=""}="string"===typeof e?B(e):e,s=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)})),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:s,search:fe(r),hash:me(a)}}(a,s),c=i&&"/"!==i&&i.endsWith("/"),u=(l||"."===i)&&n.endsWith("/");return o.pathname.endsWith("/")||!c&&!u||(o.pathname+="/"),o}const ue=e=>e.join("/").replace(/\/\/+/g,"/"),de=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),fe=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",me=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";Error;function he(e){return null!=e&&"number"===typeof e.status&&"string"===typeof e.statusText&&"boolean"===typeof e.internal&&"data"in e}const pe=["post","put","patch","delete"],xe=(new Set(pe),["get",...pe]);new Set(xe),new Set([301,302,303,307,308]),new Set([307,308]);Symbol("deferred");function ge(){return ge=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ge.apply(this,arguments)}const ye=a.createContext(null);const ve=a.createContext(null);const be=a.createContext(null);const we=a.createContext(null);const je=a.createContext({outlet:null,matches:[],isDataRoute:!1});const Ne=a.createContext(null);function ke(){return null!=a.useContext(we)}function Se(){return ke()||M(!1),a.useContext(we).location}function Ee(e){a.useContext(be).static||a.useLayoutEffect(e)}function Ce(){let{isDataRoute:e}=a.useContext(je);return e?function(){let{router:e}=Ie(De.UseNavigateStable),t=ze(Me.UseNavigateStable),n=a.useRef(!1);return Ee((()=>{n.current=!0})),a.useCallback((function(r,a){void 0===a&&(a={}),n.current&&("number"===typeof r?e.navigate(r):e.navigate(r,ge({fromRouteId:t},a)))}),[e,t])}():function(){ke()||M(!1);let e=a.useContext(ye),{basename:t,future:n,navigator:r}=a.useContext(be),{matches:s}=a.useContext(je),{pathname:l}=Se(),i=JSON.stringify(oe(s,n.v7_relativeSplatPath)),o=a.useRef(!1);return Ee((()=>{o.current=!0})),a.useCallback((function(n,a){if(void 0===a&&(a={}),!o.current)return;if("number"===typeof n)return void r.go(n);let s=ce(n,JSON.parse(i),l,"path"===a.relative);null==e&&"/"!==t&&(s.pathname="/"===s.pathname?t:ue([t,s.pathname])),(a.replace?r.replace:r.push)(s,a.state,a)}),[t,r,i,l,e])}()}function Oe(e,t){let{relative:n}=void 0===t?{}:t,{future:r}=a.useContext(be),{matches:s}=a.useContext(je),{pathname:l}=Se(),i=JSON.stringify(oe(s,r.v7_relativeSplatPath));return a.useMemo((()=>ce(e,JSON.parse(i),l,"path"===n)),[e,i,l,n])}function Pe(e,t,n,r){ke()||M(!1);let{navigator:s}=a.useContext(be),{matches:l}=a.useContext(je),i=l[l.length-1],o=i?i.params:{},c=(i&&i.pathname,i?i.pathnameBase:"/");i&&i.route;let u,d=Se();if(t){var f;let e="string"===typeof t?B(t):t;"/"===c||(null==(f=e.pathname)?void 0:f.startsWith(c))||M(!1),u=e}else u=d;let m=u.pathname||"/",h=m;if("/"!==c){let e=c.replace(/^\//,"").split("/");h="/"+m.replace(/^\//,"").split("/").slice(e.length).join("/")}let p=V(e,{pathname:h});let x=Ae(p&&p.map((e=>Object.assign({},e,{params:Object.assign({},o,e.params),pathname:ue([c,s.encodeLocation?s.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?c:ue([c,s.encodeLocation?s.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),l,n,r);return t&&x?a.createElement(we.Provider,{value:{location:ge({pathname:"/",search:"",hash:"",state:null,key:"default"},u),navigationType:T.Pop}},x):x}function _e(){let e=function(){var e;let t=a.useContext(Ne),n=Fe(Me.UseRouteError),r=ze(Me.UseRouteError);if(void 0!==t)return t;return null==(e=n.errors)?void 0:e[r]}(),t=he(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r="rgba(200,200,200, 0.5)",s={padding:"0.5rem",backgroundColor:r};return a.createElement(a.Fragment,null,a.createElement("h2",null,"Unexpected Application Error!"),a.createElement("h3",{style:{fontStyle:"italic"}},t),n?a.createElement("pre",{style:s},n):null,null)}const Re=a.createElement(_e,null);class Le extends a.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?a.createElement(je.Provider,{value:this.props.routeContext},a.createElement(Ne.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Te(e){let{routeContext:t,match:n,children:r}=e,s=a.useContext(ye);return s&&s.static&&s.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(s.staticContext._deepestRenderedBoundaryId=n.route.id),a.createElement(je.Provider,{value:t},r)}function Ae(e,t,n,r){var s;if(void 0===t&&(t=[]),void 0===n&&(n=null),void 0===r&&(r=null),null==e){var l;if(!n)return null;if(n.errors)e=n.matches;else{if(!(null!=(l=r)&&l.v7_partialHydration&&0===t.length&&!n.initialized&&n.matches.length>0))return null;e=n.matches}}let i=e,o=null==(s=n)?void 0:s.errors;if(null!=o){let e=i.findIndex((e=>e.route.id&&void 0!==(null==o?void 0:o[e.route.id])));e>=0||M(!1),i=i.slice(0,Math.min(i.length,e+1))}let c=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let a=0;a<i.length;a++){let e=i[a];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(u=a),e.route.id){let{loaderData:t,errors:r}=n,a=e.route.loader&&void 0===t[e.route.id]&&(!r||void 0===r[e.route.id]);if(e.route.lazy||a){c=!0,i=u>=0?i.slice(0,u+1):[i[0]];break}}}return i.reduceRight(((e,r,s)=>{let l,d=!1,f=null,m=null;var h;n&&(l=o&&r.route.id?o[r.route.id]:void 0,f=r.route.errorElement||Re,c&&(u<0&&0===s?(h="route-fallback",!1||Ue[h]||(Ue[h]=!0),d=!0,m=null):u===s&&(d=!0,m=r.route.hydrateFallbackElement||null)));let p=t.concat(i.slice(0,s+1)),x=()=>{let t;return t=l?f:d?m:r.route.Component?a.createElement(r.route.Component,null):r.route.element?r.route.element:e,a.createElement(Te,{match:r,routeContext:{outlet:e,matches:p,isDataRoute:null!=n},children:t})};return n&&(r.route.ErrorBoundary||r.route.errorElement||0===s)?a.createElement(Le,{location:n.location,revalidation:n.revalidation,component:f,error:l,children:x(),routeContext:{outlet:null,matches:p,isDataRoute:!0}}):x()}),null)}var De=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(De||{}),Me=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Me||{});function Ie(e){let t=a.useContext(ye);return t||M(!1),t}function Fe(e){let t=a.useContext(ve);return t||M(!1),t}function ze(e){let t=function(){let e=a.useContext(je);return e||M(!1),e}(),n=t.matches[t.matches.length-1];return n.route.id||M(!1),n.route.id}const Ue={};function Be(e,t){null==e||e.v7_startTransition,void 0===(null==e?void 0:e.v7_relativeSplatPath)&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}s.startTransition;function We(e){M(!1)}function qe(e){let{basename:t="/",children:n=null,location:r,navigationType:s=T.Pop,navigator:l,static:i=!1,future:o}=e;ke()&&M(!1);let c=t.replace(/^\/*/,"/"),u=a.useMemo((()=>({basename:c,navigator:l,static:i,future:ge({v7_relativeSplatPath:!1},o)})),[c,o,l,i]);"string"===typeof r&&(r=B(r));let{pathname:d="/",search:f="",hash:m="",state:h=null,key:p="default"}=r,x=a.useMemo((()=>{let e=se(d,c);return null==e?null:{location:{pathname:e,search:f,hash:m,state:h,key:p},navigationType:s}}),[c,d,f,m,h,p,s]);return null==x?null:a.createElement(be.Provider,{value:u},a.createElement(we.Provider,{children:n,value:x}))}function Ve(e){let{children:t,location:n}=e;return Pe($e(t),n)}new Promise((()=>{}));a.Component;function $e(e,t){void 0===t&&(t=[]);let n=[];return a.Children.forEach(e,((e,r)=>{if(!a.isValidElement(e))return;let s=[...t,r];if(e.type===a.Fragment)return void n.push.apply(n,$e(e.props.children,s));e.type!==We&&M(!1),e.props.index&&e.props.children&&M(!1);let l={id:e.props.id||s.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(l.children=$e(e.props.children,s)),n.push(l)})),n}function He(){return He=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},He.apply(this,arguments)}function Qe(e,t){if(null==e)return{};var n,r,a={},s=Object.keys(e);for(r=0;r<s.length;r++)n=s[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);const Ke=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"];try{window.__reactRouterVersion="6"}catch(Tc){}new Map;const Je=s.startTransition;u.flushSync,s.useId;function Xe(e){let{basename:t,children:n,future:r,window:s}=e,l=a.useRef();var i;null==l.current&&(l.current=(void 0===(i={window:s,v5Compat:!0})&&(i={}),W((function(e,t){let{pathname:n,search:r,hash:a}=e.location;return z("",{pathname:n,search:r,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){return"string"===typeof t?t:U(t)}),null,i)));let o=l.current,[c,u]=a.useState({action:o.action,location:o.location}),{v7_startTransition:d}=r||{},f=a.useCallback((e=>{d&&Je?Je((()=>u(e))):u(e)}),[u,d]);return a.useLayoutEffect((()=>o.listen(f)),[o,f]),a.useEffect((()=>Be(r)),[r]),a.createElement(qe,{basename:t,children:n,location:c.location,navigationType:c.action,navigator:o,future:r})}const Ze="undefined"!==typeof window&&"undefined"!==typeof window.document&&"undefined"!==typeof window.document.createElement,Ge=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Ye=a.forwardRef((function(e,t){let n,{onClick:r,relative:s,reloadDocument:l,replace:i,state:o,target:c,to:u,preventScrollReset:d,viewTransition:f}=e,m=Qe(e,Ke),{basename:h}=a.useContext(be),p=!1;if("string"===typeof u&&Ge.test(u)&&(n=u,Ze))try{let e=new URL(window.location.href),t=u.startsWith("//")?new URL(e.protocol+u):new URL(u),n=se(t.pathname,h);t.origin===e.origin&&null!=n?u=n+t.search+t.hash:p=!0}catch(Tc){}let x=function(e,t){let{relative:n}=void 0===t?{}:t;ke()||M(!1);let{basename:r,navigator:s}=a.useContext(be),{hash:l,pathname:i,search:o}=Oe(e,{relative:n}),c=i;return"/"!==r&&(c="/"===i?r:ue([r,i])),s.createHref({pathname:c,search:o,hash:l})}(u,{relative:s}),g=function(e,t){let{target:n,replace:r,state:s,preventScrollReset:l,relative:i,viewTransition:o}=void 0===t?{}:t,c=Ce(),u=Se(),d=Oe(e,{relative:i});return a.useCallback((t=>{if(function(e,t){return 0===e.button&&(!t||"_self"===t)&&!function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e)}(t,n)){t.preventDefault();let n=void 0!==r?r:U(u)===U(d);c(e,{replace:n,state:s,preventScrollReset:l,relative:i,viewTransition:o})}}),[u,c,d,r,s,n,e,l,i,o])}(u,{replace:i,state:o,target:c,preventScrollReset:d,relative:s,viewTransition:f});return a.createElement("a",He({},m,{href:n||x,onClick:p||l?r:function(e){r&&r(e),e.defaultPrevented||g(e)},ref:t,target:c}))}));var et,tt;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(et||(et={})),function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"}(tt||(tt={}));function nt(e){return nt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},nt(e)}function rt(e){var t=function(e,t){if("object"!=nt(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=nt(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==nt(t)?t:t+""}function at(e,t,n){return(t=rt(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function st(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function lt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?st(Object(n),!0).forEach((function(t){at(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):st(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var it=n(579);const ot=(0,a.createContext)(void 0),ct=e=>{let{children:t}=e;const[n,r]=(0,a.useState)({isAuthenticated:!1,isLoading:!0,user:null,token:null,error:null});(0,a.useEffect)((()=>{s()}),[]);const s=async()=>{try{r((e=>lt(lt({},e),{},{isLoading:!0,error:null})));const e=await fetch("/v1/auth/user",{credentials:"include"});if(e.ok){const t=await e.json();r((e=>lt(lt({},e),{},{isAuthenticated:!0,user:t,isLoading:!1,error:null})))}else r((e=>lt(lt({},e),{},{isAuthenticated:!1,isLoading:!1,error:null})))}catch(e){console.error("Auth initialization failed:",e),r((e=>lt(lt({},e),{},{isAuthenticated:!1,isLoading:!1,error:null})))}},l=(e,t,n)=>{var r,a,s;if(!e||!e.permissions)return!1;if(e.permissions.includes("*"))return!0;const l="".concat(n,":").concat(t.toLowerCase());if(e.permissions.includes(l))return!0;const i="*:".concat(t.toLowerCase());if(e.permissions.includes(i))return!0;const o="".concat(n,":*");return!!e.permissions.includes(o)||(!(null===(r=e.roles)||void 0===r||!r.includes("admin"))||(!(null===(a=e.roles)||void 0===a||!a.includes("operator")||"read"!==t.toLowerCase()&&"execute"!==t.toLowerCase())||!(null===(s=e.roles)||void 0===s||!s.includes("viewer")||"read"!==t.toLowerCase())))},i=async()=>{try{const e=await fetch("/v1/auth/refresh",{method:"POST",credentials:"include"});if(e.ok){const t=await e.json(),n=await fetch("/v1/auth/user",{credentials:"include"});if(n.ok){const e=await n.json();r((n=>lt(lt({},n),{},{isAuthenticated:!0,user:e,token:t,isLoading:!1,error:null})))}}else r((e=>lt(lt({},e),{},{isAuthenticated:!1,isLoading:!1,user:null,token:null,error:null})))}catch(e){console.error("Token refresh failed:",e),r((e=>lt(lt({},e),{},{isAuthenticated:!1,isLoading:!1,user:null,token:null,error:null})))}};(0,a.useEffect)((()=>{if(n.token&&n.isAuthenticated){const e=setInterval((()=>{i()}),3e6);return()=>clearInterval(e)}}),[n.token,n.isAuthenticated]);const o=lt(lt({},n),{},{login:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"admin",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"admin";try{r((e=>lt(lt({},e),{},{isLoading:!0,error:null})));const n=await fetch("/v1/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({username:e,password:t})});if(!n.ok){const e=await n.json();throw new Error(e.error||"Login failed")}const a=await n.json();r((e=>lt(lt({},e),{},{isAuthenticated:!0,user:a.user,token:null,isLoading:!1,error:null})))}catch(n){console.error("Login failed:",n),r((e=>lt(lt({},e),{},{isLoading:!1,error:n instanceof Error?n.message:"Login failed"})))}},logout:async()=>{try{r((e=>lt(lt({},e),{},{isLoading:!0})));const e=await fetch("/v1/auth/logout",{method:"POST",credentials:"include"});if(r({isAuthenticated:!1,isLoading:!1,user:null,token:null,error:null}),e.ok){const t=await e.json();if(t.logout_url&&t.logout_url!==window.location.origin)return void(window.location.href=t.logout_url)}window.location.reload()}catch(e){console.error("Logout failed:",e),r((e=>lt(lt({},e),{},{isLoading:!1,error:"Logout failed"})))}},checkPermission:async(e,t)=>{try{var r;if(!n.user)return!1;const a=l(n.user,e,t);if(null!==a)return a;const s=await fetch("/v1/auth/check",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({action:e,resource:t})});if(!s.ok)return!1;return(null===(r=(await s.json()).decision)||void 0===r?void 0:r.allowed)||!1}catch(i){var a,s;return console.error("Permission check failed:",i),!(null===(a=n.user)||void 0===a||null===(s=a.roles)||void 0===s||!s.includes("admin"))}},refreshToken:i});return(0,it.jsx)(ot.Provider,{value:o,children:t})},ut=()=>{const e=(0,a.useContext)(ot);if(void 0===e)throw new Error("useAuth must be used within an AuthProvider");return e},dt=e=>{let{children:t,requiredPermission:n,fallback:r=(0,it.jsx)("div",{className:"p-4 text-red-600",children:"Access Denied"})}=e;const{isAuthenticated:s,isLoading:l,checkPermission:i}=ut(),[o,c]=(0,a.useState)(null);return(0,a.useEffect)((()=>{s&&n?i(n.action,n.resource).then(c):s&&c(!0)}),[s,n,i]),l?(0,it.jsx)("div",{className:"p-4",children:"Loading..."}):s?n&&!1===o?(0,it.jsx)(it.Fragment,{children:r}):n&&null===o?(0,it.jsx)("div",{className:"p-4",children:"Checking permissions..."}):(0,it.jsx)(it.Fragment,{children:t}):(0,it.jsx)("div",{className:"p-4",children:"Please log in to access this page."})},ft=()=>{const{login:e,isLoading:t,error:n}=ut(),[r,s]=a.useState("admin"),[l,i]=a.useState("admin");return(0,it.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center p-4",children:(0,it.jsxs)("div",{className:"max-w-md w-full bg-white/10 backdrop-blur-lg rounded-2xl shadow-2xl p-8 border border-white/20",children:[(0,it.jsxs)("div",{className:"text-center mb-8",children:[(0,it.jsx)("div",{className:"text-6xl mb-4",children:"\ud83c\udf1f"}),(0,it.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"CAINuro Orchestrator"}),(0,it.jsx)("p",{className:"text-blue-200",children:"Enterprise Cloud Resource Orchestration Platform"})]}),(0,it.jsxs)("form",{onSubmit:t=>{t.preventDefault(),e(r,l)},className:"mb-6",children:[(0,it.jsx)("div",{className:"bg-blue-500/20 border border-blue-500/50 rounded-lg p-4 mb-4",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"text-blue-400 mr-2",children:"\u2139\ufe0f"}),(0,it.jsxs)("div",{className:"text-blue-200 text-sm",children:[(0,it.jsx)("strong",{children:"Default Credentials:"})," admin/admin or user/user"]})]})}),n&&(0,it.jsx)("div",{className:"bg-red-500/20 border border-red-500/50 rounded-lg p-4 mb-4",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"text-red-400 mr-2",children:"\u26a0\ufe0f"}),(0,it.jsx)("div",{className:"text-red-200 text-sm",children:n})]})}),(0,it.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{htmlFor:"username",className:"block text-sm font-medium text-white mb-2",children:"Username"}),(0,it.jsx)("input",{id:"username",type:"text",value:r,onChange:e=>s(e.target.value),className:"w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter username",required:!0})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-white mb-2",children:"Password"}),(0,it.jsx)("input",{id:"password",type:"password",value:l,onChange:e=>i(e.target.value),className:"w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter password",required:!0})]})]}),(0,it.jsx)("button",{type:"submit",disabled:t,className:"w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:from-gray-600 disabled:to-gray-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105 disabled:scale-100 disabled:cursor-not-allowed shadow-lg",children:t?(0,it.jsxs)("div",{className:"flex items-center justify-center",children:[(0,it.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Signing in..."]}):(0,it.jsxs)("div",{className:"flex items-center justify-center",children:[(0,it.jsx)("div",{className:"mr-2",children:"\ud83d\udd10"}),"Sign In"]})})]}),(0,it.jsxs)("div",{className:"border-t border-white/20 pt-6",children:[(0,it.jsx)("div",{className:"text-center mb-4",children:(0,it.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"\ud83d\ude80 Authentication Features"})}),(0,it.jsxs)("div",{className:"grid grid-cols-1 gap-3 text-sm",children:[(0,it.jsxs)("div",{className:"flex items-center text-green-300",children:[(0,it.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full mr-3"}),"OIDC/OAuth2 Integration"]}),(0,it.jsxs)("div",{className:"flex items-center text-green-300",children:[(0,it.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full mr-3"}),"JWT Token Management"]}),(0,it.jsxs)("div",{className:"flex items-center text-green-300",children:[(0,it.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full mr-3"}),"Role-Based Access Control (RBAC)"]}),(0,it.jsxs)("div",{className:"flex items-center text-green-300",children:[(0,it.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full mr-3"}),"Session Management"]}),(0,it.jsxs)("div",{className:"flex items-center text-green-300",children:[(0,it.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full mr-3"}),"Multi-Factor Authentication"]}),(0,it.jsxs)("div",{className:"flex items-center text-green-300",children:[(0,it.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full mr-3"}),"Audit Logging"]})]})]}),(0,it.jsx)("div",{className:"mt-6 pt-6 border-t border-white/20",children:(0,it.jsxs)("div",{className:"text-center",children:[(0,it.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"\ud83c\udf10 Platform Capabilities"}),(0,it.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-xs text-blue-200",children:[(0,it.jsx)("div",{children:"\ud83d\udd0d Multi-Cloud Discovery"}),(0,it.jsx)("div",{children:"\u26a1 Workflow Automation"}),(0,it.jsx)("div",{children:"\u2638\ufe0f Kubernetes Management"}),(0,it.jsx)("div",{children:"\ud83d\udca5 Chaos Engineering"}),(0,it.jsx)("div",{children:"\ud83e\udd16 ChatOps Integration"}),(0,it.jsx)("div",{children:"\ud83d\udcca Real-time Monitoring"}),(0,it.jsx)("div",{children:"\ud83d\udd78\ufe0f Topology Mapping"}),(0,it.jsx)("div",{children:"\ud83d\udc19 GitHub Integration"}),(0,it.jsx)("div",{children:"\ud83d\udd17 URL Management"}),(0,it.jsx)("div",{children:"\ud83d\udccb Project Management"}),(0,it.jsx)("div",{children:"\ud83d\udcdd Feedback Collection"}),(0,it.jsx)("div",{children:"\ud83d\udee1\ufe0f Security Scanning"})]})]})}),(0,it.jsx)("div",{className:"mt-6 text-center",children:(0,it.jsx)("p",{className:"text-xs text-blue-300",children:"Powered by CAINuro \u2022 Enterprise Grade \u2022 Production Ready"})})]})})};function mt(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(r=0;r<s.length;r++)n=s[r],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}const ht=["title","titleId"];function pt(e,t){let{title:n,titleId:r}=e,s=mt(e,ht);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))}const xt=a.forwardRef(pt),gt=["title","titleId"];function yt(e,t){let{title:n,titleId:r}=e,s=mt(e,gt);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))}const vt=a.forwardRef(yt),bt=()=>{const[e,t]=(0,a.useState)(""),[n,r]=(0,a.useState)(!1),[s,l]=(0,a.useState)(!1),[i,o]=(0,a.useState)([]),[c,u]=(0,a.useState)(-1),d=Ce(),f=(0,a.useRef)(null),m=(0,a.useRef)(null),h=[{id:"1",label:"Dashboard",path:"/dashboard",category:"Navigation",description:"Main dashboard overview"},{id:"2",label:"Search Resources",path:"/search",category:"Navigation",description:"Search across all resources"},{id:"3",label:"Discovery Wizard",path:"/discovery",category:"Navigation",description:"Discover new resources"},{id:"4",label:"Workflows",path:"/workflows",category:"Navigation",description:"Manage workflows"},{id:"5",label:"Envoy Config",path:"/envoy",category:"Navigation",description:"Configure Envoy proxy"},{id:"6",label:"Autoscaler",path:"/autoscaler",category:"Navigation",description:"Autoscaling configuration"},{id:"7",label:"Audit Logs",path:"/audit",category:"Navigation",description:"View audit logs"},{id:"8",label:"Database Admin",path:"/admin/database",category:"Admin",description:"Database administration"},{id:"9",label:"User Management",path:"/admin/users",category:"Admin",description:"Manage users"},{id:"10",label:"RBAC Management",path:"/admin/rbac",category:"Admin",description:"Role-based access control"},{id:"11",label:"Feature Flags",path:"/admin/featureflags",category:"Admin",description:"Manage feature flags"},{id:"12",label:"Settings",path:"/admin/settings",category:"Admin",description:"System settings"}],p=e=>{d(e.path),r(!1),l(!1),t("")},x=()=>{r(!0),setTimeout((()=>{var e;return null===(e=f.current)||void 0===e?void 0:e.focus()}),100)},g=e=>{m.current&&!m.current.contains(e.target)&&l(!1)};return(0,a.useEffect)((()=>(document.addEventListener("mousedown",g),()=>document.removeEventListener("mousedown",g))),[]),(0,a.useEffect)((()=>{const e=e=>{"/"===e.key&&e.target!==f.current&&"INPUT"!==e.target.tagName&&(e.preventDefault(),r(!0),setTimeout((()=>{var e;return null===(e=f.current)||void 0===e?void 0:e.focus()}),100))};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)}),[]),n?(0,it.jsxs)("div",{className:"relative w-full max-w-lg",ref:m,children:[(0,it.jsxs)("div",{className:"relative",children:[(0,it.jsx)("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:(0,it.jsx)(xt,{className:"h-5 w-5 text-gray-400"})}),(0,it.jsx)("input",{ref:f,type:"text",value:e,onChange:e=>{const n=e.target.value;t(n),(e=>{if(!e.trim())return o([]),void l(!1);const t=h.filter((t=>{var n;return t.label.toLowerCase().includes(e.toLowerCase())||(null===(n=t.description)||void 0===n?void 0:n.toLowerCase().includes(e.toLowerCase()))||t.category.toLowerCase().includes(e.toLowerCase())}));o(t),l(!0),u(-1)})(n)},onKeyDown:e=>{var n;if(s&&0!==i.length)switch(e.key){case"ArrowDown":e.preventDefault(),u((e=>e<i.length-1?e+1:0));break;case"ArrowUp":e.preventDefault(),u((e=>e>0?e-1:i.length-1));break;case"Enter":e.preventDefault(),c>=0&&c<i.length?p(i[c]):i.length>0&&p(i[0]);break;case"Escape":r(!1),l(!1),t(""),null===(n=f.current)||void 0===n||n.blur()}},className:"block w-full pl-10 pr-10 py-2 border border-gray-600 rounded-md bg-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Search pages, features...",autoComplete:"off"}),(0,it.jsx)("button",{onClick:()=>{r(!1),l(!1),t("")},className:"absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-300",children:(0,it.jsx)(vt,{className:"h-5 w-5"})})]}),s&&i.length>0&&(0,it.jsx)("div",{className:"absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-96 overflow-y-auto",children:i.map(((e,t)=>(0,it.jsx)("div",{onClick:()=>p(e),className:"px-4 py-3 cursor-pointer border-b border-gray-100 last:border-b-0 ".concat(t===c?"bg-blue-50":"hover:bg-gray-50"),children:(0,it.jsxs)("div",{className:"flex items-center justify-between",children:[(0,it.jsxs)("div",{className:"flex-1",children:[(0,it.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.label}),e.description&&(0,it.jsx)("div",{className:"text-xs text-gray-500 mt-1",children:e.description})]}),(0,it.jsx)("div",{className:"text-xs text-gray-400 ml-2",children:e.category})]})},e.id)))}),s&&e&&0===i.length&&(0,it.jsx)("div",{className:"absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg",children:(0,it.jsxs)("div",{className:"px-4 py-3 text-sm text-gray-500 text-center",children:['No results found for "',e,'"']})})]}):(0,it.jsx)("button",{onClick:x,className:"p-2 text-gray-400 hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-md",title:"Search (Press / to focus)",children:(0,it.jsx)(xt,{className:"h-5 w-5"})})},wt=["title","titleId"];function jt(e,t){let{title:n,titleId:r}=e,s=mt(e,wt);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"}))}const Nt=a.forwardRef(jt),kt=["title","titleId"];function St(e,t){let{title:n,titleId:r}=e,s=mt(e,kt);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 12a7.5 7.5 0 0 0 15 0m-15 0a7.5 7.5 0 1 1 15 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077 1.41-.513m14.095-5.13 1.41-.513M5.106 17.785l1.15-.964m11.49-9.642 1.149-.964M7.501 19.795l.75-1.3m7.5-12.99.75-1.3m-6.063 16.658.26-1.477m2.605-14.772.26-1.477m0 17.726-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205 12 12m6.894 5.785-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495"}))}const Et=a.forwardRef(St),Ct=["title","titleId"];function Ot(e,t){let{title:n,titleId:r}=e,s=mt(e,Ct);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418"}))}const Pt=a.forwardRef(Ot),_t=["title","titleId"];function Rt(e,t){let{title:n,titleId:r}=e,s=mt(e,_t);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 18 9 11.25l4.306 4.306a11.95 11.95 0 0 1 5.814-5.518l2.74-1.22m0 0-5.94-2.281m5.94 2.28-2.28 5.941"}))}const Lt=a.forwardRef(Rt),Tt=["title","titleId"];function At(e,t){let{title:n,titleId:r}=e,s=mt(e,Tt);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}))}const Dt=a.forwardRef(At),Mt=["title","titleId"];function It(e,t){let{title:n,titleId:r}=e,s=mt(e,Mt);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125"}))}const Ft=a.forwardRef(It),zt=["title","titleId"];function Ut(e,t){let{title:n,titleId:r}=e,s=mt(e,zt);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"}))}const Bt=a.forwardRef(Ut),Wt=["title","titleId"];function qt(e,t){let{title:n,titleId:r}=e,s=mt(e,Wt);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 3v1.5M3 21v-6m0 0 2.77-.693a9 9 0 0 1 6.208.682l.108.054a9 9 0 0 0 6.086.71l3.114-.732a48.524 48.524 0 0 1-.005-10.499l-3.11.732a9 9 0 0 1-6.085-.711l-.108-.054a9 9 0 0 0-6.208-.682L3 4.5M3 15V4.5"}))}const Vt=a.forwardRef(qt),$t=["title","titleId"];function Ht(e,t){let{title:n,titleId:r}=e,s=mt(e,$t);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M17.982 18.725A7.488 7.488 0 0 0 12 15.75a7.488 7.488 0 0 0-5.982 2.975m11.963 0a9 9 0 1 0-11.963 0m11.963 0A8.966 8.966 0 0 1 12 21a8.966 8.966 0 0 1-5.982-2.275M15 9.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}const Qt=a.forwardRef(Ht),Kt=["title","titleId"];function Jt(e,t){let{title:n,titleId:r}=e,s=mt(e,Kt);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 9V5.25A2.25 2.25 0 0 0 13.5 3h-6a2.25 2.25 0 0 0-2.25 2.25v13.5A2.25 2.25 0 0 0 7.5 21h6a2.25 2.25 0 0 0 2.25-2.25V15m3 0 3-3m0 0-3-3m3 3H9"}))}const Xt=a.forwardRef(Jt),Zt=["title","titleId"];function Gt(e,t){let{title:n,titleId:r}=e,s=mt(e,Zt);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"}))}const Yt=a.forwardRef(Gt),en=[{name:"Dashboard",href:"/dashboard",icon:Nt},{name:"Search",href:"/search",icon:xt},{name:"Discovery Wizard",href:"/discovery",icon:xt},{name:"Workflows",href:"/workflows",icon:Et},{name:"Envoy Config",href:"/envoy",icon:Pt},{name:"Autoscaler",href:"/autoscaler",icon:Lt},{name:"Audit",href:"/audit",icon:Dt},{name:"Database",href:"/database",icon:Ft}],tn=[{name:"Admin Dashboard",href:"/admin",icon:Nt},{name:"User Management",href:"/admin/users",icon:Bt},{name:"RBAC Management",href:"/admin/rbac",icon:Dt},{name:"Feature Flags",href:"/admin/featureflags",icon:Vt},{name:"Settings",href:"/admin/settings",icon:Et}],nn=[{name:"Profile",href:"/profile",icon:Qt}];function rn(e){var t,n,r;let{children:s}=e;const[l,i]=(0,a.useState)(!1),[o,c]=(0,a.useState)(!1),u=Se(),{user:d,logout:f}=ut(),m=[...en,...null!==d&&void 0!==d&&null!==(t=d.roles)&&void 0!==t&&t.includes("admin")?tn:[],...nn];return(0,it.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,it.jsxs)("div",{className:"fixed inset-0 z-50 lg:hidden ".concat(l?"block":"hidden"),children:[(0,it.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75",onClick:()=>i(!1)}),(0,it.jsxs)("div",{className:"fixed inset-y-0 left-0 flex w-64 flex-col bg-gray-800",children:[(0,it.jsxs)("div",{className:"flex h-16 items-center justify-between px-4",children:[(0,it.jsx)("div",{className:"flex items-center",children:(0,it.jsx)("span",{className:"text-xl font-bold text-cyan-400",children:"\ud83d\ude80 CAINuro"})}),(0,it.jsx)("button",{type:"button",className:"text-gray-300 hover:text-white",onClick:()=>i(!1),children:(0,it.jsx)(vt,{className:"h-6 w-6"})})]}),(0,it.jsx)("nav",{className:"flex-1 space-y-1 px-2 py-4",children:m.map((e=>{const t=u.pathname===e.href;return(0,it.jsxs)(Ye,{to:e.href,className:"group flex items-center px-2 py-2 text-sm font-medium rounded-md ".concat(t?"bg-cyan-600 text-white":"text-gray-300 hover:bg-gray-700 hover:text-white"),onClick:()=>i(!1),children:[(0,it.jsx)(e.icon,{className:"mr-3 h-6 w-6 flex-shrink-0"}),e.name]},e.name)}))})]})]}),(0,it.jsx)("div",{className:"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col",children:(0,it.jsxs)("div",{className:"flex flex-col flex-grow bg-gray-800 pt-5 pb-4 overflow-y-auto",children:[(0,it.jsx)("div",{className:"flex items-center flex-shrink-0 px-4",children:(0,it.jsx)("span",{className:"text-xl font-bold text-cyan-400",children:"\ud83d\ude80 CAINuro Orchestrator"})}),(0,it.jsx)("div",{className:"mt-5 flex-1 flex flex-col",children:(0,it.jsx)("nav",{className:"flex-1 px-2 space-y-1",children:m.map((e=>{const t=u.pathname===e.href;return(0,it.jsxs)(Ye,{to:e.href,className:"group flex items-center px-2 py-2 text-sm font-medium rounded-md ".concat(t?"bg-cyan-600 text-white":"text-gray-300 hover:bg-gray-700 hover:text-white"),children:[(0,it.jsx)(e.icon,{className:"mr-3 h-6 w-6 flex-shrink-0"}),e.name]},e.name)}))})}),(0,it.jsx)("div",{className:"flex-shrink-0 flex border-t border-gray-700 p-4",children:(0,it.jsxs)("div",{className:"flex items-center w-full",children:[(0,it.jsx)(Qt,{className:"h-8 w-8 text-gray-400"}),(0,it.jsxs)("div",{className:"ml-3 flex-1",children:[(0,it.jsx)("p",{className:"text-sm font-medium text-gray-300",children:(null===d||void 0===d?void 0:d.name)||(null===d||void 0===d?void 0:d.email)||"User"}),(0,it.jsx)("p",{className:"text-xs text-gray-400",children:(null===d||void 0===d||null===(n=d.roles)||void 0===n?void 0:n.join(", "))||"Loading..."})]}),(0,it.jsx)("button",{onClick:f,className:"ml-2 p-1 text-gray-400 hover:text-white",title:"Logout",children:(0,it.jsx)(Xt,{className:"h-5 w-5"})})]})})]})}),(0,it.jsxs)("div",{className:"lg:pl-64 flex flex-col flex-1",children:[(0,it.jsxs)("div",{className:"sticky top-0 z-10 flex-shrink-0 flex h-16 bg-gray-800 shadow",children:[(0,it.jsx)("button",{type:"button",className:"px-4 border-r border-gray-700 text-gray-300 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-cyan-500 lg:hidden",onClick:()=>i(!0),children:(0,it.jsx)(Yt,{className:"h-6 w-6"})}),(0,it.jsxs)("div",{className:"flex-1 px-4 flex justify-between",children:[(0,it.jsx)("div",{className:"flex-1 flex",children:(0,it.jsx)("div",{className:"w-full flex md:ml-0",children:(0,it.jsx)(bt,{})})}),(0,it.jsx)("div",{className:"ml-4 flex items-center md:ml-6",children:(0,it.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,it.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"All Systems Operational"}),(0,it.jsx)("div",{className:"w-3 h-3 bg-green-400 rounded-full animate-pulse"}),(0,it.jsxs)("div",{className:"relative",children:[(0,it.jsxs)("button",{onClick:()=>c(!o),className:"flex items-center space-x-2 text-gray-300 hover:text-white p-2 rounded-md",children:[(0,it.jsx)(Qt,{className:"h-6 w-6"}),(0,it.jsx)("span",{className:"text-sm font-medium",children:(null===d||void 0===d?void 0:d.name)||(null===d||void 0===d?void 0:d.email)||"User"})]}),o&&(0,it.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50",children:[(0,it.jsxs)("div",{className:"px-4 py-2 border-b border-gray-200",children:[(0,it.jsx)("p",{className:"text-sm font-medium text-gray-900",children:(null===d||void 0===d?void 0:d.name)||"User"}),(0,it.jsx)("p",{className:"text-sm text-gray-500",children:null===d||void 0===d?void 0:d.email}),(0,it.jsx)("p",{className:"text-xs text-gray-400",children:null===d||void 0===d||null===(r=d.roles)||void 0===r?void 0:r.join(", ")})]}),(0,it.jsxs)(Ye,{to:"/profile",onClick:()=>c(!1),className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[(0,it.jsx)(Qt,{className:"inline h-4 w-4 mr-2"}),"Profile"]}),(0,it.jsxs)("button",{onClick:()=>{c(!1),f()},className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[(0,it.jsx)(Xt,{className:"inline h-4 w-4 mr-2"}),"Sign out"]})]})]})]})})]})]}),(0,it.jsx)("main",{className:"flex-1",children:(0,it.jsx)("div",{className:"py-6",children:(0,it.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 md:px-8",children:s})})})]})]})}const an=["title","titleId"];function sn(e,t){let{title:n,titleId:r}=e,s=mt(e,an);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z"}))}const ln=a.forwardRef(sn),on=["title","titleId"];function cn(e,t){let{title:n,titleId:r}=e,s=mt(e,on);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}const un=a.forwardRef(cn),dn=["title","titleId"];function fn(e,t){let{title:n,titleId:r}=e,s=mt(e,dn);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const mn=a.forwardRef(fn),hn=["title","titleId"];function pn(e,t){let{title:n,titleId:r}=e,s=mt(e,hn);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))}const xn=a.forwardRef(pn),gn=["title","titleId"];function yn(e,t){let{title:n,titleId:r}=e,s=mt(e,gn);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 17.25v-.228a4.5 4.5 0 0 0-.12-1.03l-2.268-9.64a3.375 3.375 0 0 0-3.285-2.602H7.923a3.375 3.375 0 0 0-3.285 2.602l-2.268 9.64a4.5 4.5 0 0 0-.12 1.03v.228m19.5 0a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3m19.5 0a3 3 0 0 0-3-3H5.25a3 3 0 0 0-3 3m16.5 0h.008v.008h-.008v-.008Zm-3 0h.008v.008h-.008v-.008Z"}))}const vn=a.forwardRef(yn),bn=["title","titleId"];function wn(e,t){let{title:n,titleId:r}=e,s=mt(e,bn);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const jn=a.forwardRef(wn),Nn={audit:Dt,autoscaler:Lt,db_admin:Ft,discovery:xt,envoy_control_plane:Pt,workflow:Et},kn=[{name:"Discovery Wizard",description:"Search across AWS, GCP, and Azure resources",icon:xt,href:"/discovery",color:"bg-blue-500"},{name:"Run Workflow",description:"Execute automation workflows",icon:ln,href:"/workflows",color:"bg-green-500"},{name:"Search Resources",description:"Advanced resource search and filtering",icon:un,href:"/search",color:"bg-purple-500"},{name:"Envoy Config",description:"Manage Envoy proxy configurations",icon:Pt,href:"/envoy",color:"bg-orange-500"},{name:"Audit Logs",description:"View tamper-proof audit trail",icon:Dt,href:"/audit",color:"bg-red-500"},{name:"Database Admin",description:"Manage database connections and queries",icon:Ft,href:"/database",color:"bg-indigo-500"}];function Sn(){const{user:e}=ut(),[t,n]=(0,a.useState)(null),[r,s]=(0,a.useState)(null),[l,i]=(0,a.useState)(!0);return(0,a.useEffect)((()=>{const e=async()=>{try{const e=await fetch("/health"),t=await e.json();n(t);const r=await fetch("/v1/admin/stats",{credentials:"include"});if(r.ok){const e=await r.json();s({totalUsers:e.totalUsers||0,activeConnections:e.activeConnections||0,runningWorkflows:e.runningWorkflows||0,systemUptime:e.systemUptime||"Unknown"})}}catch(e){console.error("Failed to fetch dashboard data:",e)}finally{i(!1)}};e();const t=setInterval(e,3e4);return()=>clearInterval(t)}),[]),l?(0,it.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,it.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,it.jsxs)("div",{className:"flex items-center justify-between",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"CAINuro Orchestrator"}),(0,it.jsxs)("p",{className:"text-gray-600",children:["Welcome back, ",null===e||void 0===e?void 0:e.name]})]}),(0,it.jsx)("div",{className:"flex items-center space-x-2",children:"healthy"===(null===t||void 0===t?void 0:t.status)?(0,it.jsxs)(it.Fragment,{children:[(0,it.jsx)(mn,{className:"h-8 w-8 text-green-500"}),(0,it.jsx)("span",{className:"text-sm font-medium text-green-600",children:"All Systems Operational"})]}):(0,it.jsxs)(it.Fragment,{children:[(0,it.jsx)(xn,{className:"h-8 w-8 text-red-500"}),(0,it.jsx)("span",{className:"text-sm font-medium text-red-600",children:"System Issues Detected"})]})})]})}),(0,it.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,it.jsxs)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:[(0,it.jsx)("div",{className:"p-5",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(Bt,{className:"h-6 w-6 text-gray-400"})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Total Users"}),(0,it.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:(null===r||void 0===r?void 0:r.totalUsers)||0})]})})]})}),(0,it.jsx)("div",{className:"bg-gray-50 px-5 py-3",children:(0,it.jsx)("div",{className:"text-sm",children:(0,it.jsx)("span",{className:"text-green-600 font-medium",children:"Active"})})})]}),(0,it.jsxs)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:[(0,it.jsx)("div",{className:"p-5",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(vn,{className:"h-6 w-6 text-gray-400"})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Connections"}),(0,it.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:(null===r||void 0===r?void 0:r.activeConnections)||0})]})})]})}),(0,it.jsx)("div",{className:"bg-gray-50 px-5 py-3",children:(0,it.jsx)("div",{className:"text-sm",children:(0,it.jsx)("span",{className:"text-blue-600 font-medium",children:"Active"})})})]}),(0,it.jsxs)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:[(0,it.jsx)("div",{className:"p-5",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(Et,{className:"h-6 w-6 text-gray-400"})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Workflows"}),(0,it.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:(null===r||void 0===r?void 0:r.runningWorkflows)||0})]})})]})}),(0,it.jsx)("div",{className:"bg-gray-50 px-5 py-3",children:(0,it.jsx)("div",{className:"text-sm",children:(0,it.jsx)("span",{className:"text-purple-600 font-medium",children:"Running"})})})]}),(0,it.jsxs)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:[(0,it.jsx)("div",{className:"p-5",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(jn,{className:"h-6 w-6 text-gray-400"})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Uptime"}),(0,it.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:(null===r||void 0===r?void 0:r.systemUptime)||"Unknown"})]})})]})}),(0,it.jsx)("div",{className:"bg-gray-50 px-5 py-3",children:(0,it.jsx)("div",{className:"text-sm",children:(0,it.jsxs)("span",{className:"text-green-600 font-medium",children:[null===t||void 0===t?void 0:t.mode," Mode"]})})})]})]}),(0,it.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,it.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,it.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"System Services"})}),(0,it.jsx)("div",{className:"p-6",children:(0,it.jsx)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3",children:(null===t||void 0===t?void 0:t.services)&&Object.entries(t.services).map((e=>{let[t,n]=e;const r=Nn[t]||Et,a="active"===n;return(0,it.jsxs)("div",{className:"flex items-center p-4 border border-gray-200 rounded-lg",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(r,{className:"h-6 w-6 ".concat(a?"text-green-500":"text-red-500")})}),(0,it.jsxs)("div",{className:"ml-4 flex-1",children:[(0,it.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:t.replace(/_/g," ").replace(/\b\w/g,(e=>e.toUpperCase()))}),(0,it.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(a?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:n})]})]},t)}))})})]}),(0,it.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,it.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,it.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Quick Actions"})}),(0,it.jsx)("div",{className:"p-6",children:(0,it.jsx)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3",children:kn.map((e=>{const t=e.icon;return(0,it.jsxs)(Ye,{to:e.href,className:"relative group bg-white p-6 border border-gray-200 rounded-lg hover:border-gray-300 hover:shadow-md transition-all duration-200",children:[(0,it.jsx)("div",{children:(0,it.jsx)("span",{className:"rounded-lg inline-flex p-3 ".concat(e.color," text-white"),children:(0,it.jsx)(t,{className:"h-6 w-6"})})}),(0,it.jsxs)("div",{className:"mt-4",children:[(0,it.jsx)("h3",{className:"text-lg font-medium text-gray-900 group-hover:text-blue-600",children:e.name}),(0,it.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:e.description})]})]},e.name)}))})})]}),(0,it.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,it.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,it.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"System Information"})}),(0,it.jsx)("div",{className:"p-6",children:(0,it.jsxs)("dl",{className:"grid grid-cols-1 gap-6 sm:grid-cols-3",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Version"}),(0,it.jsx)("dd",{className:"mt-1 text-lg font-semibold text-gray-900",children:null===t||void 0===t?void 0:t.version})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Mode"}),(0,it.jsx)("dd",{className:"mt-1 text-lg font-semibold text-gray-900",children:(0,it.jsx)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800",children:null===t||void 0===t?void 0:t.mode})})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Active Services"}),(0,it.jsxs)("dd",{className:"mt-1 text-lg font-semibold text-gray-900",children:[null!==t&&void 0!==t&&t.services?Object.keys(t.services).length:0," / ",null!==t&&void 0!==t&&t.services?Object.keys(t.services).length:0]})]})]})})]})]})}function En(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];throw Error("[Immer] minified error nr: "+e+(n.length?" "+n.map((function(e){return"'"+e+"'"})).join(","):"")+". Find the full error at: https://bit.ly/3cXEKWf")}function Cn(e){return!!e&&!!e[xr]}function On(e){var t;return!!e&&(function(e){if(!e||"object"!=typeof e)return!1;var t=Object.getPrototypeOf(e);if(null===t)return!0;var n=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return n===Object||"function"==typeof n&&Function.toString.call(n)===gr}(e)||Array.isArray(e)||!!e[pr]||!!(null===(t=e.constructor)||void 0===t?void 0:t[pr])||Dn(e)||Mn(e))}function Pn(e,t,n){void 0===n&&(n=!1),0===_n(e)?(n?Object.keys:yr)(e).forEach((function(r){n&&"symbol"==typeof r||t(r,e[r],e)})):e.forEach((function(n,r){return t(r,n,e)}))}function _n(e){var t=e[xr];return t?t.i>3?t.i-4:t.i:Array.isArray(e)?1:Dn(e)?2:Mn(e)?3:0}function Rn(e,t){return 2===_n(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function Ln(e,t){return 2===_n(e)?e.get(t):e[t]}function Tn(e,t,n){var r=_n(e);2===r?e.set(t,n):3===r?e.add(n):e[t]=n}function An(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t}function Dn(e){return dr&&e instanceof Map}function Mn(e){return fr&&e instanceof Set}function In(e){return e.o||e.t}function Fn(e){if(Array.isArray(e))return Array.prototype.slice.call(e);var t=vr(e);delete t[xr];for(var n=yr(t),r=0;r<n.length;r++){var a=n[r],s=t[a];!1===s.writable&&(s.writable=!0,s.configurable=!0),(s.get||s.set)&&(t[a]={configurable:!0,writable:!0,enumerable:s.enumerable,value:e[a]})}return Object.create(Object.getPrototypeOf(e),t)}function zn(e,t){return void 0===t&&(t=!1),Bn(e)||Cn(e)||!On(e)||(_n(e)>1&&(e.set=e.add=e.clear=e.delete=Un),Object.freeze(e),t&&Pn(e,(function(e,t){return zn(t,!0)}),!0)),e}function Un(){En(2)}function Bn(e){return null==e||"object"!=typeof e||Object.isFrozen(e)}function Wn(e){var t=br[e];return t||En(18,e),t}function qn(e,t){br[e]||(br[e]=t)}function Vn(){return cr}function $n(e,t){t&&(Wn("Patches"),e.u=[],e.s=[],e.v=t)}function Hn(e){Qn(e),e.p.forEach(Jn),e.p=null}function Qn(e){e===cr&&(cr=e.l)}function Kn(e){return cr={p:[],l:cr,h:e,m:!0,_:0}}function Jn(e){var t=e[xr];0===t.i||1===t.i?t.j():t.g=!0}function Xn(e,t){t._=t.p.length;var n=t.p[0],r=void 0!==e&&e!==n;return t.h.O||Wn("ES5").S(t,e,r),r?(n[xr].P&&(Hn(t),En(4)),On(e)&&(e=Zn(t,e),t.l||Yn(t,e)),t.u&&Wn("Patches").M(n[xr].t,e,t.u,t.s)):e=Zn(t,n,[]),Hn(t),t.u&&t.v(t.u,t.s),e!==hr?e:void 0}function Zn(e,t,n){if(Bn(t))return t;var r=t[xr];if(!r)return Pn(t,(function(a,s){return Gn(e,r,t,a,s,n)}),!0),t;if(r.A!==e)return t;if(!r.P)return Yn(e,r.t,!0),r.t;if(!r.I){r.I=!0,r.A._--;var a=4===r.i||5===r.i?r.o=Fn(r.k):r.o,s=a,l=!1;3===r.i&&(s=new Set(a),a.clear(),l=!0),Pn(s,(function(t,s){return Gn(e,r,a,t,s,n,l)})),Yn(e,a,!1),n&&e.u&&Wn("Patches").N(r,n,e.u,e.s)}return r.o}function Gn(e,t,n,r,a,s,l){if(Cn(a)){var i=Zn(e,a,s&&t&&3!==t.i&&!Rn(t.R,r)?s.concat(r):void 0);if(Tn(n,r,i),!Cn(i))return;e.m=!1}else l&&n.add(a);if(On(a)&&!Bn(a)){if(!e.h.D&&e._<1)return;Zn(e,a),t&&t.A.l||Yn(e,a)}}function Yn(e,t,n){void 0===n&&(n=!1),!e.l&&e.h.D&&e.m&&zn(t,n)}function er(e,t){var n=e[xr];return(n?In(n):e)[t]}function tr(e,t){if(t in e)for(var n=Object.getPrototypeOf(e);n;){var r=Object.getOwnPropertyDescriptor(n,t);if(r)return r;n=Object.getPrototypeOf(n)}}function nr(e){e.P||(e.P=!0,e.l&&nr(e.l))}function rr(e){e.o||(e.o=Fn(e.t))}function ar(e,t,n){var r=Dn(t)?Wn("MapSet").F(t,n):Mn(t)?Wn("MapSet").T(t,n):e.O?function(e,t){var n=Array.isArray(e),r={i:n?1:0,A:t?t.A:Vn(),P:!1,I:!1,R:{},l:t,t:e,k:null,o:null,j:null,C:!1},a=r,s=wr;n&&(a=[r],s=jr);var l=Proxy.revocable(a,s),i=l.revoke,o=l.proxy;return r.k=o,r.j=i,o}(t,n):Wn("ES5").J(t,n);return(n?n.A:Vn()).p.push(r),r}function sr(e){return Cn(e)||En(22,e),function e(t){if(!On(t))return t;var n,r=t[xr],a=_n(t);if(r){if(!r.P&&(r.i<4||!Wn("ES5").K(r)))return r.t;r.I=!0,n=lr(t,a),r.I=!1}else n=lr(t,a);return Pn(n,(function(t,a){r&&Ln(r.t,t)===a||Tn(n,t,e(a))})),3===a?new Set(n):n}(e)}function lr(e,t){switch(t){case 2:return new Map(e);case 3:return Array.from(e)}return Fn(e)}function ir(){function e(e,t){var n=a[e];return n?n.enumerable=t:a[e]=n={configurable:!0,enumerable:t,get:function(){var t=this[xr];return wr.get(t,e)},set:function(t){var n=this[xr];wr.set(n,e,t)}},n}function t(e){for(var t=e.length-1;t>=0;t--){var a=e[t][xr];if(!a.P)switch(a.i){case 5:r(a)&&nr(a);break;case 4:n(a)&&nr(a)}}}function n(e){for(var t=e.t,n=e.k,r=yr(n),a=r.length-1;a>=0;a--){var s=r[a];if(s!==xr){var l=t[s];if(void 0===l&&!Rn(t,s))return!0;var i=n[s],o=i&&i[xr];if(o?o.t!==l:!An(i,l))return!0}}var c=!!t[xr];return r.length!==yr(t).length+(c?0:1)}function r(e){var t=e.k;if(t.length!==e.t.length)return!0;var n=Object.getOwnPropertyDescriptor(t,t.length-1);if(n&&!n.get)return!0;for(var r=0;r<t.length;r++)if(!t.hasOwnProperty(r))return!0;return!1}var a={};qn("ES5",{J:function(t,n){var r=Array.isArray(t),a=function(t,n){if(t){for(var r=Array(n.length),a=0;a<n.length;a++)Object.defineProperty(r,""+a,e(a,!0));return r}var s=vr(n);delete s[xr];for(var l=yr(s),i=0;i<l.length;i++){var o=l[i];s[o]=e(o,t||!!s[o].enumerable)}return Object.create(Object.getPrototypeOf(n),s)}(r,t),s={i:r?5:4,A:n?n.A:Vn(),P:!1,I:!1,R:{},l:n,t:t,k:a,o:null,g:!1,C:!1};return Object.defineProperty(a,xr,{value:s,writable:!0}),a},S:function(e,n,a){a?Cn(n)&&n[xr].A===e&&t(e.p):(e.u&&function e(t){if(t&&"object"==typeof t){var n=t[xr];if(n){var a=n.t,s=n.k,l=n.R,i=n.i;if(4===i)Pn(s,(function(t){t!==xr&&(void 0!==a[t]||Rn(a,t)?l[t]||e(s[t]):(l[t]=!0,nr(n)))})),Pn(a,(function(e){void 0!==s[e]||Rn(s,e)||(l[e]=!1,nr(n))}));else if(5===i){if(r(n)&&(nr(n),l.length=!0),s.length<a.length)for(var o=s.length;o<a.length;o++)l[o]=!1;else for(var c=a.length;c<s.length;c++)l[c]=!0;for(var u=Math.min(s.length,a.length),d=0;d<u;d++)s.hasOwnProperty(d)||(l[d]=!0),void 0===l[d]&&e(s[d])}}}}(e.p[0]),t(e.p))},K:function(e){return 4===e.i?n(e):r(e)}})}var or,cr,ur="undefined"!=typeof Symbol&&"symbol"==typeof Symbol("x"),dr="undefined"!=typeof Map,fr="undefined"!=typeof Set,mr="undefined"!=typeof Proxy&&void 0!==Proxy.revocable&&"undefined"!=typeof Reflect,hr=ur?Symbol.for("immer-nothing"):((or={})["immer-nothing"]=!0,or),pr=ur?Symbol.for("immer-draftable"):"__$immer_draftable",xr=ur?Symbol.for("immer-state"):"__$immer_state",gr=("undefined"!=typeof Symbol&&Symbol.iterator,""+Object.prototype.constructor),yr="undefined"!=typeof Reflect&&Reflect.ownKeys?Reflect.ownKeys:void 0!==Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:Object.getOwnPropertyNames,vr=Object.getOwnPropertyDescriptors||function(e){var t={};return yr(e).forEach((function(n){t[n]=Object.getOwnPropertyDescriptor(e,n)})),t},br={},wr={get:function(e,t){if(t===xr)return e;var n=In(e);if(!Rn(n,t))return function(e,t,n){var r,a=tr(t,n);return a?"value"in a?a.value:null===(r=a.get)||void 0===r?void 0:r.call(e.k):void 0}(e,n,t);var r=n[t];return e.I||!On(r)?r:r===er(e.t,t)?(rr(e),e.o[t]=ar(e.A.h,r,e)):r},has:function(e,t){return t in In(e)},ownKeys:function(e){return Reflect.ownKeys(In(e))},set:function(e,t,n){var r=tr(In(e),t);if(null==r?void 0:r.set)return r.set.call(e.k,n),!0;if(!e.P){var a=er(In(e),t),s=null==a?void 0:a[xr];if(s&&s.t===n)return e.o[t]=n,e.R[t]=!1,!0;if(An(n,a)&&(void 0!==n||Rn(e.t,t)))return!0;rr(e),nr(e)}return e.o[t]===n&&(void 0!==n||t in e.o)||Number.isNaN(n)&&Number.isNaN(e.o[t])||(e.o[t]=n,e.R[t]=!0),!0},deleteProperty:function(e,t){return void 0!==er(e.t,t)||t in e.t?(e.R[t]=!1,rr(e),nr(e)):delete e.R[t],e.o&&delete e.o[t],!0},getOwnPropertyDescriptor:function(e,t){var n=In(e),r=Reflect.getOwnPropertyDescriptor(n,t);return r?{writable:!0,configurable:1!==e.i||"length"!==t,enumerable:r.enumerable,value:n[t]}:r},defineProperty:function(){En(11)},getPrototypeOf:function(e){return Object.getPrototypeOf(e.t)},setPrototypeOf:function(){En(12)}},jr={};Pn(wr,(function(e,t){jr[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}})),jr.deleteProperty=function(e,t){return jr.set.call(this,e,t,void 0)},jr.set=function(e,t,n){return wr.set.call(this,e[0],t,n,e[0])};var Nr=function(){function e(e){var t=this;this.O=mr,this.D=!0,this.produce=function(e,n,r){if("function"==typeof e&&"function"!=typeof n){var a=n;n=e;var s=t;return function(e){var t=this;void 0===e&&(e=a);for(var r=arguments.length,l=Array(r>1?r-1:0),i=1;i<r;i++)l[i-1]=arguments[i];return s.produce(e,(function(e){var r;return(r=n).call.apply(r,[t,e].concat(l))}))}}var l;if("function"!=typeof n&&En(6),void 0!==r&&"function"!=typeof r&&En(7),On(e)){var i=Kn(t),o=ar(t,e,void 0),c=!0;try{l=n(o),c=!1}finally{c?Hn(i):Qn(i)}return"undefined"!=typeof Promise&&l instanceof Promise?l.then((function(e){return $n(i,r),Xn(e,i)}),(function(e){throw Hn(i),e})):($n(i,r),Xn(l,i))}if(!e||"object"!=typeof e){if(void 0===(l=n(e))&&(l=e),l===hr&&(l=void 0),t.D&&zn(l,!0),r){var u=[],d=[];Wn("Patches").M(e,l,u,d),r(u,d)}return l}En(21,e)},this.produceWithPatches=function(e,n){if("function"==typeof e)return function(n){for(var r=arguments.length,a=Array(r>1?r-1:0),s=1;s<r;s++)a[s-1]=arguments[s];return t.produceWithPatches(n,(function(t){return e.apply(void 0,[t].concat(a))}))};var r,a,s=t.produce(e,n,(function(e,t){r=e,a=t}));return"undefined"!=typeof Promise&&s instanceof Promise?s.then((function(e){return[e,r,a]})):[s,r,a]},"boolean"==typeof(null==e?void 0:e.useProxies)&&this.setUseProxies(e.useProxies),"boolean"==typeof(null==e?void 0:e.autoFreeze)&&this.setAutoFreeze(e.autoFreeze)}var t=e.prototype;return t.createDraft=function(e){On(e)||En(8),Cn(e)&&(e=sr(e));var t=Kn(this),n=ar(this,e,void 0);return n[xr].C=!0,Qn(t),n},t.finishDraft=function(e,t){var n=(e&&e[xr]).A;return $n(n,t),Xn(void 0,n)},t.setAutoFreeze=function(e){this.D=e},t.setUseProxies=function(e){e&&!mr&&En(20),this.O=e},t.applyPatches=function(e,t){var n;for(n=t.length-1;n>=0;n--){var r=t[n];if(0===r.path.length&&"replace"===r.op){e=r.value;break}}n>-1&&(t=t.slice(n+1));var a=Wn("Patches").$;return Cn(e)?a(e,t):this.produce(e,(function(e){return a(e,t)}))},e}(),kr=new Nr,Sr=kr.produce;kr.produceWithPatches.bind(kr),kr.setAutoFreeze.bind(kr),kr.setUseProxies.bind(kr),kr.applyPatches.bind(kr),kr.createDraft.bind(kr),kr.finishDraft.bind(kr);const Er=Sr;function Cr(e){return"Minified Redux error #"+e+"; visit https://redux.js.org/Errors?code="+e+" for the full message or use the non-minified dev environment for full errors. "}var Or="function"===typeof Symbol&&Symbol.observable||"@@observable",Pr=function(){return Math.random().toString(36).substring(7).split("").join(".")},_r={INIT:"@@redux/INIT"+Pr(),REPLACE:"@@redux/REPLACE"+Pr(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+Pr()}};function Rr(e){if("object"!==typeof e||null===e)return!1;for(var t=e;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}function Lr(e,t,n){var r;if("function"===typeof t&&"function"===typeof n||"function"===typeof n&&"function"===typeof arguments[3])throw new Error(Cr(0));if("function"===typeof t&&"undefined"===typeof n&&(n=t,t=void 0),"undefined"!==typeof n){if("function"!==typeof n)throw new Error(Cr(1));return n(Lr)(e,t)}if("function"!==typeof e)throw new Error(Cr(2));var a=e,s=t,l=[],i=l,o=!1;function c(){i===l&&(i=l.slice())}function u(){if(o)throw new Error(Cr(3));return s}function d(e){if("function"!==typeof e)throw new Error(Cr(4));if(o)throw new Error(Cr(5));var t=!0;return c(),i.push(e),function(){if(t){if(o)throw new Error(Cr(6));t=!1,c();var n=i.indexOf(e);i.splice(n,1),l=null}}}function f(e){if(!Rr(e))throw new Error(Cr(7));if("undefined"===typeof e.type)throw new Error(Cr(8));if(o)throw new Error(Cr(9));try{o=!0,s=a(s,e)}finally{o=!1}for(var t=l=i,n=0;n<t.length;n++){(0,t[n])()}return e}return f({type:_r.INIT}),(r={dispatch:f,subscribe:d,getState:u,replaceReducer:function(e){if("function"!==typeof e)throw new Error(Cr(10));a=e,f({type:_r.REPLACE})}})[Or]=function(){var e,t=d;return(e={subscribe:function(e){if("object"!==typeof e||null===e)throw new Error(Cr(11));function n(){e.next&&e.next(u())}return n(),{unsubscribe:t(n)}}})[Or]=function(){return this},e},r}function Tr(e){for(var t=Object.keys(e),n={},r=0;r<t.length;r++){var a=t[r];0,"function"===typeof e[a]&&(n[a]=e[a])}var s,l=Object.keys(n);try{!function(e){Object.keys(e).forEach((function(t){var n=e[t];if("undefined"===typeof n(void 0,{type:_r.INIT}))throw new Error(Cr(12));if("undefined"===typeof n(void 0,{type:_r.PROBE_UNKNOWN_ACTION()}))throw new Error(Cr(13))}))}(n)}catch(Tc){s=Tc}return function(e,t){if(void 0===e&&(e={}),s)throw s;for(var r=!1,a={},i=0;i<l.length;i++){var o=l[i],c=n[o],u=e[o],d=c(u,t);if("undefined"===typeof d){t&&t.type;throw new Error(Cr(14))}a[o]=d,r=r||d!==u}return(r=r||l.length!==Object.keys(e).length)?a:e}}function Ar(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return 0===t.length?function(e){return e}:1===t.length?t[0]:t.reduce((function(e,t){return function(){return e(t.apply(void 0,arguments))}}))}function Dr(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){return function(){var n=e.apply(void 0,arguments),r=function(){throw new Error(Cr(15))},a={getState:n.getState,dispatch:function(){return r.apply(void 0,arguments)}},s=t.map((function(e){return e(a)}));return r=Ar.apply(void 0,s)(n.dispatch),lt(lt({},n),{},{dispatch:r})}}}function Mr(e){return function(t){var n=t.dispatch,r=t.getState;return function(t){return function(a){return"function"===typeof a?a(n,r,e):t(a)}}}}var Ir=Mr();Ir.withExtraArgument=Mr;const Fr=Ir;var zr=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!==typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),Ur=function(e,t){var n,r,a,s,l={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return s={next:i(0),throw:i(1),return:i(2)},"function"===typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function i(s){return function(i){return function(s){if(n)throw new TypeError("Generator is already executing.");for(;l;)try{if(n=1,r&&(a=2&s[0]?r.return:s[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,s[1])).done)return a;switch(r=0,a&&(s=[2&s[0],a.value]),s[0]){case 0:case 1:a=s;break;case 4:return l.label++,{value:s[1],done:!1};case 5:l.label++,r=s[1],s=[0];continue;case 7:s=l.ops.pop(),l.trys.pop();continue;default:if(!(a=(a=l.trys).length>0&&a[a.length-1])&&(6===s[0]||2===s[0])){l=0;continue}if(3===s[0]&&(!a||s[1]>a[0]&&s[1]<a[3])){l.label=s[1];break}if(6===s[0]&&l.label<a[1]){l.label=a[1],a=s;break}if(a&&l.label<a[2]){l.label=a[2],l.ops.push(s);break}a[2]&&l.ops.pop(),l.trys.pop();continue}s=t.call(e,l)}catch(Tc){s=[6,Tc],r=0}finally{n=a=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,i])}}},Br=function(e,t){for(var n=0,r=t.length,a=e.length;n<r;n++,a++)e[a]=t[n];return e},Wr=Object.defineProperty,qr=Object.defineProperties,Vr=Object.getOwnPropertyDescriptors,$r=Object.getOwnPropertySymbols,Hr=Object.prototype.hasOwnProperty,Qr=Object.prototype.propertyIsEnumerable,Kr=function(e,t,n){return t in e?Wr(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n},Jr=function(e,t){for(var n in t||(t={}))Hr.call(t,n)&&Kr(e,n,t[n]);if($r)for(var r=0,a=$r(t);r<a.length;r++){n=a[r];Qr.call(t,n)&&Kr(e,n,t[n])}return e},Xr=function(e,t){return qr(e,Vr(t))},Zr=function(e,t,n){return new Promise((function(r,a){var s=function(e){try{i(n.next(e))}catch(Tc){a(Tc)}},l=function(e){try{i(n.throw(e))}catch(Tc){a(Tc)}},i=function(e){return e.done?r(e.value):Promise.resolve(e.value).then(s,l)};i((n=n.apply(e,t)).next())}))},Gr="undefined"!==typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!==arguments.length)return"object"===typeof arguments[0]?Ar:Ar.apply(null,arguments)};"undefined"!==typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;function Yr(e){if("object"!==typeof e||null===e)return!1;var t=Object.getPrototypeOf(e);if(null===t)return!0;for(var n=t;null!==Object.getPrototypeOf(n);)n=Object.getPrototypeOf(n);return t===n}function ea(e,t){function n(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];if(t){var a=t.apply(void 0,n);if(!a)throw new Error("prepareAction did not return an object");return Jr(Jr({type:e,payload:a.payload},"meta"in a&&{meta:a.meta}),"error"in a&&{error:a.error})}return{type:e,payload:n[0]}}return n.toString=function(){return""+e},n.type=e,n.match=function(t){return t.type===e},n}var ta=function(e){function t(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var a=e.apply(this,n)||this;return Object.setPrototypeOf(a,t.prototype),a}return zr(t,e),Object.defineProperty(t,Symbol.species,{get:function(){return t},enumerable:!1,configurable:!0}),t.prototype.concat=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e.prototype.concat.apply(this,t)},t.prototype.prepend=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return 1===e.length&&Array.isArray(e[0])?new(t.bind.apply(t,Br([void 0],e[0].concat(this)))):new(t.bind.apply(t,Br([void 0],e.concat(this))))},t}(Array),na=function(e){function t(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var a=e.apply(this,n)||this;return Object.setPrototypeOf(a,t.prototype),a}return zr(t,e),Object.defineProperty(t,Symbol.species,{get:function(){return t},enumerable:!1,configurable:!0}),t.prototype.concat=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e.prototype.concat.apply(this,t)},t.prototype.prepend=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return 1===e.length&&Array.isArray(e[0])?new(t.bind.apply(t,Br([void 0],e[0].concat(this)))):new(t.bind.apply(t,Br([void 0],e.concat(this))))},t}(Array);function ra(e){return On(e)?Er(e,(function(){})):e}function aa(){return function(e){return function(e){void 0===e&&(e={});var t=e.thunk,n=void 0===t||t,r=(e.immutableCheck,e.serializableCheck,e.actionCreatorCheck,new ta);n&&(!function(e){return"boolean"===typeof e}(n)?r.push(Fr.withExtraArgument(n.extraArgument)):r.push(Fr));0;return r}(e)}}function sa(e){var t,n={},r=[],a={addCase:function(e,t){var r="string"===typeof e?e:e.type;if(!r)throw new Error("`builder.addCase` cannot be called with an empty action type");if(r in n)throw new Error("`builder.addCase` cannot be called with two reducers for the same action type");return n[r]=t,a},addMatcher:function(e,t){return r.push({matcher:e,reducer:t}),a},addDefaultCase:function(e){return t=e,a}};return e(a),[n,r,t]}function la(e){var t=e.name;if(!t)throw new Error("`name` is a required option for createSlice");var n,r="function"==typeof e.initialState?e.initialState:ra(e.initialState),a=e.reducers||{},s=Object.keys(a),l={},i={},o={};function c(){var t="function"===typeof e.extraReducers?sa(e.extraReducers):[e.extraReducers],n=t[0],a=void 0===n?{}:n,s=t[1],l=void 0===s?[]:s,o=t[2],c=void 0===o?void 0:o,u=Jr(Jr({},a),i);return function(e,t,n,r){void 0===n&&(n=[]);var a,s="function"===typeof t?sa(t):[t,n,r],l=s[0],i=s[1],o=s[2];if(function(e){return"function"===typeof e}(e))a=function(){return ra(e())};else{var c=ra(e);a=function(){return c}}function u(e,t){void 0===e&&(e=a());var n=Br([l[t.type]],i.filter((function(e){return(0,e.matcher)(t)})).map((function(e){return e.reducer})));return 0===n.filter((function(e){return!!e})).length&&(n=[o]),n.reduce((function(e,n){if(n){var r;if(Cn(e))return void 0===(r=n(e,t))?e:r;if(On(e))return Er(e,(function(e){return n(e,t)}));if(void 0===(r=n(e,t))){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return r}return e}),e)}return u.getInitialState=a,u}(r,(function(e){for(var t in u)e.addCase(t,u[t]);for(var n=0,r=l;n<r.length;n++){var a=r[n];e.addMatcher(a.matcher,a.reducer)}c&&e.addDefaultCase(c)}))}return s.forEach((function(e){var n,r,s=a[e],c=t+"/"+e;"reducer"in s?(n=s.reducer,r=s.prepare):n=s,l[e]=n,i[c]=n,o[e]=r?ea(c,r):ea(c)})),{name:t,reducer:function(e,t){return n||(n=c()),n(e,t)},actions:o,caseReducers:l,getInitialState:function(){return n||(n=c()),n.getInitialState()}}}var ia=function(e){void 0===e&&(e=21);for(var t="",n=e;n--;)t+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return t},oa=["name","message","stack","code"],ca=function(e,t){this.payload=e,this.meta=t},ua=function(e,t){this.payload=e,this.meta=t},da=function(e){if("object"===typeof e&&null!==e){for(var t={},n=0,r=oa;n<r.length;n++){var a=r[n];"string"===typeof e[a]&&(t[a]=e[a])}return t}return{message:String(e)}},fa=function(){function e(e,t,n){var r=ea(e+"/fulfilled",(function(e,t,n,r){return{payload:e,meta:Xr(Jr({},r||{}),{arg:n,requestId:t,requestStatus:"fulfilled"})}})),a=ea(e+"/pending",(function(e,t,n){return{payload:void 0,meta:Xr(Jr({},n||{}),{arg:t,requestId:e,requestStatus:"pending"})}})),s=ea(e+"/rejected",(function(e,t,r,a,s){return{payload:a,error:(n&&n.serializeError||da)(e||"Rejected"),meta:Xr(Jr({},s||{}),{arg:r,requestId:t,rejectedWithValue:!!a,requestStatus:"rejected",aborted:"AbortError"===(null==e?void 0:e.name),condition:"ConditionError"===(null==e?void 0:e.name)})}})),l="undefined"!==typeof AbortController?AbortController:function(){function e(){this.signal={aborted:!1,addEventListener:function(){},dispatchEvent:function(){return!1},onabort:function(){},removeEventListener:function(){},reason:void 0,throwIfAborted:function(){}}}return e.prototype.abort=function(){0},e}();return Object.assign((function(e){return function(i,o,c){var u,d=(null==n?void 0:n.idGenerator)?n.idGenerator(e):ia(),f=new l;function m(e){u=e,f.abort()}var h=function(){return Zr(this,null,(function(){var l,h,p,x,g,y;return Ur(this,(function(v){switch(v.label){case 0:return v.trys.push([0,4,,5]),x=null==(l=null==n?void 0:n.condition)?void 0:l.call(n,e,{getState:o,extra:c}),null===(b=x)||"object"!==typeof b||"function"!==typeof b.then?[3,2]:[4,x];case 1:x=v.sent(),v.label=2;case 2:if(!1===x||f.signal.aborted)throw{name:"ConditionError",message:"Aborted due to condition callback returning false."};return g=new Promise((function(e,t){return f.signal.addEventListener("abort",(function(){return t({name:"AbortError",message:u||"Aborted"})}))})),i(a(d,e,null==(h=null==n?void 0:n.getPendingMeta)?void 0:h.call(n,{requestId:d,arg:e},{getState:o,extra:c}))),[4,Promise.race([g,Promise.resolve(t(e,{dispatch:i,getState:o,extra:c,requestId:d,signal:f.signal,abort:m,rejectWithValue:function(e,t){return new ca(e,t)},fulfillWithValue:function(e,t){return new ua(e,t)}})).then((function(t){if(t instanceof ca)throw t;return t instanceof ua?r(t.payload,d,e,t.meta):r(t,d,e)}))])];case 3:return p=v.sent(),[3,5];case 4:return y=v.sent(),p=y instanceof ca?s(null,d,e,y.payload,y.meta):s(y,d,e),[3,5];case 5:return n&&!n.dispatchConditionRejection&&s.match(p)&&p.meta.condition||i(p),[2,p]}var b}))}))}();return Object.assign(h,{abort:m,requestId:d,arg:e,unwrap:function(){return h.then(ma)}})}}),{pending:a,rejected:s,fulfilled:r,typePrefix:e})}return e.withTypes=function(){return e},e}();function ma(e){if(e.meta&&e.meta.rejectedWithValue)throw e.payload;if(e.error)throw e.error;return e.payload}Object.assign;var ha="listenerMiddleware";ea(ha+"/add"),ea(ha+"/removeAll"),ea(ha+"/remove");"function"===typeof queueMicrotask&&queueMicrotask.bind("undefined"!==typeof window?window:"undefined"!==typeof n.g?n.g:globalThis);var pa,xa=function(e){return function(t){setTimeout(t,e)}};"undefined"!==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:xa(10);function ga(e,t){return function(){return e.apply(t,arguments)}}ir();const{toString:ya}=Object.prototype,{getPrototypeOf:va}=Object,{iterator:ba,toStringTag:wa}=Symbol,ja=(Na=Object.create(null),e=>{const t=ya.call(e);return Na[t]||(Na[t]=t.slice(8,-1).toLowerCase())});var Na;const ka=e=>(e=e.toLowerCase(),t=>ja(t)===e),Sa=e=>t=>typeof t===e,{isArray:Ea}=Array,Ca=Sa("undefined");const Oa=ka("ArrayBuffer");const Pa=Sa("string"),_a=Sa("function"),Ra=Sa("number"),La=e=>null!==e&&"object"===typeof e,Ta=e=>{if("object"!==ja(e))return!1;const t=va(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(wa in e)&&!(ba in e)},Aa=ka("Date"),Da=ka("File"),Ma=ka("Blob"),Ia=ka("FileList"),Fa=ka("URLSearchParams"),[za,Ua,Ba,Wa]=["ReadableStream","Request","Response","Headers"].map(ka);function qa(e,t){let n,r,{allOwnKeys:a=!1}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(null!==e&&"undefined"!==typeof e)if("object"!==typeof e&&(e=[e]),Ea(e))for(n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else{const r=a?Object.getOwnPropertyNames(e):Object.keys(e),s=r.length;let l;for(n=0;n<s;n++)l=r[n],t.call(null,e[l],l,e)}}function Va(e,t){t=t.toLowerCase();const n=Object.keys(e);let r,a=n.length;for(;a-- >0;)if(r=n[a],t===r.toLowerCase())return r;return null}const $a="undefined"!==typeof globalThis?globalThis:"undefined"!==typeof self?self:"undefined"!==typeof window?window:global,Ha=e=>!Ca(e)&&e!==$a;const Qa=(Ka="undefined"!==typeof Uint8Array&&va(Uint8Array),e=>Ka&&e instanceof Ka);var Ka;const Ja=ka("HTMLFormElement"),Xa=(e=>{let{hasOwnProperty:t}=e;return(e,n)=>t.call(e,n)})(Object.prototype),Za=ka("RegExp"),Ga=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};qa(n,((n,a)=>{let s;!1!==(s=t(n,a,e))&&(r[a]=s||n)})),Object.defineProperties(e,r)};const Ya=ka("AsyncFunction"),es=((e,t)=>{return e?setImmediate:t?(n="axios@".concat(Math.random()),r=[],$a.addEventListener("message",(e=>{let{source:t,data:a}=e;t===$a&&a===n&&r.length&&r.shift()()}),!1),e=>{r.push(e),$a.postMessage(n,"*")}):e=>setTimeout(e);var n,r})("function"===typeof setImmediate,_a($a.postMessage)),ts="undefined"!==typeof queueMicrotask?queueMicrotask.bind($a):"undefined"!==typeof process&&process.nextTick||es,ns={isArray:Ea,isArrayBuffer:Oa,isBuffer:function(e){return null!==e&&!Ca(e)&&null!==e.constructor&&!Ca(e.constructor)&&_a(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"===typeof FormData&&e instanceof FormData||_a(e.append)&&("formdata"===(t=ja(e))||"object"===t&&_a(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&Oa(e.buffer),t},isString:Pa,isNumber:Ra,isBoolean:e=>!0===e||!1===e,isObject:La,isPlainObject:Ta,isReadableStream:za,isRequest:Ua,isResponse:Ba,isHeaders:Wa,isUndefined:Ca,isDate:Aa,isFile:Da,isBlob:Ma,isRegExp:Za,isFunction:_a,isStream:e=>La(e)&&_a(e.pipe),isURLSearchParams:Fa,isTypedArray:Qa,isFileList:Ia,forEach:qa,merge:function e(){const{caseless:t}=Ha(this)&&this||{},n={},r=(r,a)=>{const s=t&&Va(n,a)||a;Ta(n[s])&&Ta(r)?n[s]=e(n[s],r):Ta(r)?n[s]=e({},r):Ea(r)?n[s]=r.slice():n[s]=r};for(let a=0,s=arguments.length;a<s;a++)arguments[a]&&qa(arguments[a],r);return n},extend:function(e,t,n){let{allOwnKeys:r}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return qa(t,((t,r)=>{n&&_a(t)?e[r]=ga(t,n):e[r]=t}),{allOwnKeys:r}),e},trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,r)=>{let a,s,l;const i={};if(t=t||{},null==e)return t;do{for(a=Object.getOwnPropertyNames(e),s=a.length;s-- >0;)l=a[s],r&&!r(l,e,t)||i[l]||(t[l]=e[l],i[l]=!0);e=!1!==n&&va(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:ja,kindOfTest:ka,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return-1!==r&&r===n},toArray:e=>{if(!e)return null;if(Ea(e))return e;let t=e.length;if(!Ra(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{const n=(e&&e[ba]).call(e);let r;for(;(r=n.next())&&!r.done;){const n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let n;const r=[];for(;null!==(n=e.exec(t));)r.push(n);return r},isHTMLForm:Ja,hasOwnProperty:Xa,hasOwnProp:Xa,reduceDescriptors:Ga,freezeMethods:e=>{Ga(e,((t,n)=>{if(_a(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=e[n];_a(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))}))},toObjectSet:(e,t)=>{const n={},r=e=>{e.forEach((e=>{n[e]=!0}))};return Ea(e)?r(e):r(String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function(e,t,n){return t.toUpperCase()+n})),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:Va,global:$a,isContextDefined:Ha,isSpecCompliantForm:function(e){return!!(e&&_a(e.append)&&"FormData"===e[wa]&&e[ba])},toJSONObject:e=>{const t=new Array(10),n=(e,r)=>{if(La(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[r]=e;const a=Ea(e)?[]:{};return qa(e,((e,t)=>{const s=n(e,r+1);!Ca(s)&&(a[t]=s)})),t[r]=void 0,a}}return e};return n(e,0)},isAsyncFn:Ya,isThenable:e=>e&&(La(e)||_a(e))&&_a(e.then)&&_a(e.catch),setImmediate:es,asap:ts,isIterable:e=>null!=e&&_a(e[ba])};function rs(e,t,n,r,a){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),a&&(this.response=a,this.status=a.status?a.status:null)}ns.inherits(rs,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:ns.toJSONObject(this.config),code:this.code,status:this.status}}});const as=rs.prototype,ss={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((e=>{ss[e]={value:e}})),Object.defineProperties(rs,ss),Object.defineProperty(as,"isAxiosError",{value:!0}),rs.from=(e,t,n,r,a,s)=>{const l=Object.create(as);return ns.toFlatObject(e,l,(function(e){return e!==Error.prototype}),(e=>"isAxiosError"!==e)),rs.call(l,e.message,t,n,r,a),l.cause=e,l.name=e.name,s&&Object.assign(l,s),l};const ls=rs;function is(e){return ns.isPlainObject(e)||ns.isArray(e)}function os(e){return ns.endsWith(e,"[]")?e.slice(0,-2):e}function cs(e,t,n){return e?e.concat(t).map((function(e,t){return e=os(e),!n&&t?"["+e+"]":e})).join(n?".":""):t}const us=ns.toFlatObject(ns,{},null,(function(e){return/^is[A-Z]/.test(e)}));const ds=function(e,t,n){if(!ns.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const r=(n=ns.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(e,t){return!ns.isUndefined(t[e])}))).metaTokens,a=n.visitor||c,s=n.dots,l=n.indexes,i=(n.Blob||"undefined"!==typeof Blob&&Blob)&&ns.isSpecCompliantForm(t);if(!ns.isFunction(a))throw new TypeError("visitor must be a function");function o(e){if(null===e)return"";if(ns.isDate(e))return e.toISOString();if(!i&&ns.isBlob(e))throw new ls("Blob is not supported. Use a Buffer instead.");return ns.isArrayBuffer(e)||ns.isTypedArray(e)?i&&"function"===typeof Blob?new Blob([e]):Buffer.from(e):e}function c(e,n,a){let i=e;if(e&&!a&&"object"===typeof e)if(ns.endsWith(n,"{}"))n=r?n:n.slice(0,-2),e=JSON.stringify(e);else if(ns.isArray(e)&&function(e){return ns.isArray(e)&&!e.some(is)}(e)||(ns.isFileList(e)||ns.endsWith(n,"[]"))&&(i=ns.toArray(e)))return n=os(n),i.forEach((function(e,r){!ns.isUndefined(e)&&null!==e&&t.append(!0===l?cs([n],r,s):null===l?n:n+"[]",o(e))})),!1;return!!is(e)||(t.append(cs(a,n,s),o(e)),!1)}const u=[],d=Object.assign(us,{defaultVisitor:c,convertValue:o,isVisitable:is});if(!ns.isObject(e))throw new TypeError("data must be an object");return function e(n,r){if(!ns.isUndefined(n)){if(-1!==u.indexOf(n))throw Error("Circular reference detected in "+r.join("."));u.push(n),ns.forEach(n,(function(n,s){!0===(!(ns.isUndefined(n)||null===n)&&a.call(t,n,ns.isString(s)?s.trim():s,r,d))&&e(n,r?r.concat(s):[s])})),u.pop()}}(e),t};function fs(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,(function(e){return t[e]}))}function ms(e,t){this._pairs=[],e&&ds(e,this,t)}const hs=ms.prototype;hs.append=function(e,t){this._pairs.push([e,t])},hs.toString=function(e){const t=e?function(t){return e.call(this,t,fs)}:fs;return this._pairs.map((function(e){return t(e[0])+"="+t(e[1])}),"").join("&")};const ps=ms;function xs(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function gs(e,t,n){if(!t)return e;const r=n&&n.encode||xs;ns.isFunction(n)&&(n={serialize:n});const a=n&&n.serialize;let s;if(s=a?a(t,n):ns.isURLSearchParams(t)?t.toString():new ps(t,n).toString(r),s){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+s}return e}const ys=class{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){ns.forEach(this.handlers,(function(t){null!==t&&e(t)}))}},vs={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},bs={isBrowser:!0,classes:{URLSearchParams:"undefined"!==typeof URLSearchParams?URLSearchParams:ps,FormData:"undefined"!==typeof FormData?FormData:null,Blob:"undefined"!==typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},ws="undefined"!==typeof window&&"undefined"!==typeof document,js="object"===typeof navigator&&navigator||void 0,Ns=ws&&(!js||["ReactNative","NativeScript","NS"].indexOf(js.product)<0),ks="undefined"!==typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"===typeof self.importScripts,Ss=ws&&window.location.href||"http://localhost",Es=lt(lt({},r),bs);const Cs=function(e){function t(e,n,r,a){let s=e[a++];if("__proto__"===s)return!0;const l=Number.isFinite(+s),i=a>=e.length;if(s=!s&&ns.isArray(r)?r.length:s,i)return ns.hasOwnProp(r,s)?r[s]=[r[s],n]:r[s]=n,!l;r[s]&&ns.isObject(r[s])||(r[s]=[]);return t(e,n,r[s],a)&&ns.isArray(r[s])&&(r[s]=function(e){const t={},n=Object.keys(e);let r;const a=n.length;let s;for(r=0;r<a;r++)s=n[r],t[s]=e[s];return t}(r[s])),!l}if(ns.isFormData(e)&&ns.isFunction(e.entries)){const n={};return ns.forEachEntry(e,((e,r)=>{t(function(e){return ns.matchAll(/\w+|\[(\w*)]/g,e).map((e=>"[]"===e[0]?"":e[1]||e[0]))}(e),r,n,0)})),n}return null};const Os={transitional:vs,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const n=t.getContentType()||"",r=n.indexOf("application/json")>-1,a=ns.isObject(e);a&&ns.isHTMLForm(e)&&(e=new FormData(e));if(ns.isFormData(e))return r?JSON.stringify(Cs(e)):e;if(ns.isArrayBuffer(e)||ns.isBuffer(e)||ns.isStream(e)||ns.isFile(e)||ns.isBlob(e)||ns.isReadableStream(e))return e;if(ns.isArrayBufferView(e))return e.buffer;if(ns.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let s;if(a){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return ds(e,new Es.classes.URLSearchParams,Object.assign({visitor:function(e,t,n,r){return Es.isNode&&ns.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((s=ns.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return ds(s?{"files[]":e}:e,t&&new t,this.formSerializer)}}return a||r?(t.setContentType("application/json",!1),function(e,t,n){if(ns.isString(e))try{return(t||JSON.parse)(e),ns.trim(e)}catch(Tc){if("SyntaxError"!==Tc.name)throw Tc}return(n||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||Os.transitional,n=t&&t.forcedJSONParsing,r="json"===this.responseType;if(ns.isResponse(e)||ns.isReadableStream(e))return e;if(e&&ns.isString(e)&&(n&&!this.responseType||r)){const n=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(Tc){if(n){if("SyntaxError"===Tc.name)throw ls.from(Tc,ls.ERR_BAD_RESPONSE,this,null,this.response);throw Tc}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Es.classes.FormData,Blob:Es.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};ns.forEach(["delete","get","head","post","put","patch"],(e=>{Os.headers[e]={}}));const Ps=Os,_s=ns.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Rs=Symbol("internals");function Ls(e){return e&&String(e).trim().toLowerCase()}function Ts(e){return!1===e||null==e?e:ns.isArray(e)?e.map(Ts):String(e)}function As(e,t,n,r,a){return ns.isFunction(r)?r.call(this,t,n):(a&&(t=n),ns.isString(t)?ns.isString(r)?-1!==t.indexOf(r):ns.isRegExp(r)?r.test(t):void 0:void 0)}class Ds{constructor(e){e&&this.set(e)}set(e,t,n){const r=this;function a(e,t,n){const a=Ls(t);if(!a)throw new Error("header name must be a non-empty string");const s=ns.findKey(r,a);(!s||void 0===r[s]||!0===n||void 0===n&&!1!==r[s])&&(r[s||t]=Ts(e))}const s=(e,t)=>ns.forEach(e,((e,n)=>a(e,n,t)));if(ns.isPlainObject(e)||e instanceof this.constructor)s(e,t);else if(ns.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim()))s((e=>{const t={};let n,r,a;return e&&e.split("\n").forEach((function(e){a=e.indexOf(":"),n=e.substring(0,a).trim().toLowerCase(),r=e.substring(a+1).trim(),!n||t[n]&&_s[n]||("set-cookie"===n?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)})),t})(e),t);else if(ns.isObject(e)&&ns.isIterable(e)){let n,r,a={};for(const t of e){if(!ns.isArray(t))throw TypeError("Object iterator must return a key-value pair");a[r=t[0]]=(n=a[r])?ns.isArray(n)?[...n,t[1]]:[n,t[1]]:t[1]}s(a,t)}else null!=e&&a(t,e,n);return this}get(e,t){if(e=Ls(e)){const n=ns.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}(e);if(ns.isFunction(t))return t.call(this,e,n);if(ns.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=Ls(e)){const n=ns.findKey(this,e);return!(!n||void 0===this[n]||t&&!As(0,this[n],n,t))}return!1}delete(e,t){const n=this;let r=!1;function a(e){if(e=Ls(e)){const a=ns.findKey(n,e);!a||t&&!As(0,n[a],a,t)||(delete n[a],r=!0)}}return ns.isArray(e)?e.forEach(a):a(e),r}clear(e){const t=Object.keys(this);let n=t.length,r=!1;for(;n--;){const a=t[n];e&&!As(0,this[a],a,e,!0)||(delete this[a],r=!0)}return r}normalize(e){const t=this,n={};return ns.forEach(this,((r,a)=>{const s=ns.findKey(n,a);if(s)return t[s]=Ts(r),void delete t[a];const l=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((e,t,n)=>t.toUpperCase()+n))}(a):String(a).trim();l!==a&&delete t[a],t[l]=Ts(r),n[l]=!0})),this}concat(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.constructor.concat(this,...t)}toJSON(e){const t=Object.create(null);return ns.forEach(this,((n,r)=>{null!=n&&!1!==n&&(t[r]=e&&ns.isArray(n)?n.join(", "):n)})),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((e=>{let[t,n]=e;return t+": "+n})).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e){const t=new this(e);for(var n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];return r.forEach((e=>t.set(e))),t}static accessor(e){const t=(this[Rs]=this[Rs]={accessors:{}}).accessors,n=this.prototype;function r(e){const r=Ls(e);t[r]||(!function(e,t){const n=ns.toCamelCase(" "+t);["get","set","has"].forEach((r=>{Object.defineProperty(e,r+n,{value:function(e,n,a){return this[r].call(this,t,e,n,a)},configurable:!0})}))}(n,e),t[r]=!0)}return ns.isArray(e)?e.forEach(r):r(e),this}}Ds.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),ns.reduceDescriptors(Ds.prototype,((e,t)=>{let{value:n}=e,r=t[0].toUpperCase()+t.slice(1);return{get:()=>n,set(e){this[r]=e}}})),ns.freezeMethods(Ds);const Ms=Ds;function Is(e,t){const n=this||Ps,r=t||n,a=Ms.from(r.headers);let s=r.data;return ns.forEach(e,(function(e){s=e.call(n,s,a.normalize(),t?t.status:void 0)})),a.normalize(),s}function Fs(e){return!(!e||!e.__CANCEL__)}function zs(e,t,n){ls.call(this,null==e?"canceled":e,ls.ERR_CANCELED,t,n),this.name="CanceledError"}ns.inherits(zs,ls,{__CANCEL__:!0});const Us=zs;function Bs(e,t,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new ls("Request failed with status code "+n.status,[ls.ERR_BAD_REQUEST,ls.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}const Ws=function(e,t){e=e||10;const n=new Array(e),r=new Array(e);let a,s=0,l=0;return t=void 0!==t?t:1e3,function(i){const o=Date.now(),c=r[l];a||(a=o),n[s]=i,r[s]=o;let u=l,d=0;for(;u!==s;)d+=n[u++],u%=e;if(s=(s+1)%e,s===l&&(l=(l+1)%e),o-a<t)return;const f=c&&o-c;return f?Math.round(1e3*d/f):void 0}};const qs=function(e,t){let n,r,a=0,s=1e3/t;const l=function(t){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Date.now();a=s,n=null,r&&(clearTimeout(r),r=null),e.apply(null,t)};return[function(){const e=Date.now(),t=e-a;for(var i=arguments.length,o=new Array(i),c=0;c<i;c++)o[c]=arguments[c];t>=s?l(o,e):(n=o,r||(r=setTimeout((()=>{r=null,l(n)}),s-t)))},()=>n&&l(n)]},Vs=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3,r=0;const a=Ws(50,250);return qs((n=>{const s=n.loaded,l=n.lengthComputable?n.total:void 0,i=s-r,o=a(i);r=s;e({loaded:s,total:l,progress:l?s/l:void 0,bytes:i,rate:o||void 0,estimated:o&&l&&s<=l?(l-s)/o:void 0,event:n,lengthComputable:null!=l,[t?"download":"upload"]:!0})}),n)},$s=(e,t)=>{const n=null!=e;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},Hs=e=>function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return ns.asap((()=>e(...n)))},Qs=Es.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,Es.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(Es.origin),Es.navigator&&/(msie|trident)/i.test(Es.navigator.userAgent)):()=>!0,Ks=Es.hasStandardBrowserEnv?{write(e,t,n,r,a,s){const l=[e+"="+encodeURIComponent(t)];ns.isNumber(n)&&l.push("expires="+new Date(n).toGMTString()),ns.isString(r)&&l.push("path="+r),ns.isString(a)&&l.push("domain="+a),!0===s&&l.push("secure"),document.cookie=l.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function Js(e,t,n){let r=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t);return e&&(r||0==n)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const Xs=e=>e instanceof Ms?lt({},e):e;function Zs(e,t){t=t||{};const n={};function r(e,t,n,r){return ns.isPlainObject(e)&&ns.isPlainObject(t)?ns.merge.call({caseless:r},e,t):ns.isPlainObject(t)?ns.merge({},t):ns.isArray(t)?t.slice():t}function a(e,t,n,a){return ns.isUndefined(t)?ns.isUndefined(e)?void 0:r(void 0,e,0,a):r(e,t,0,a)}function s(e,t){if(!ns.isUndefined(t))return r(void 0,t)}function l(e,t){return ns.isUndefined(t)?ns.isUndefined(e)?void 0:r(void 0,e):r(void 0,t)}function i(n,a,s){return s in t?r(n,a):s in e?r(void 0,n):void 0}const o={url:s,method:s,data:s,baseURL:l,transformRequest:l,transformResponse:l,paramsSerializer:l,timeout:l,timeoutMessage:l,withCredentials:l,withXSRFToken:l,adapter:l,responseType:l,xsrfCookieName:l,xsrfHeaderName:l,onUploadProgress:l,onDownloadProgress:l,decompress:l,maxContentLength:l,maxBodyLength:l,beforeRedirect:l,transport:l,httpAgent:l,httpsAgent:l,cancelToken:l,socketPath:l,responseEncoding:l,validateStatus:i,headers:(e,t,n)=>a(Xs(e),Xs(t),0,!0)};return ns.forEach(Object.keys(Object.assign({},e,t)),(function(r){const s=o[r]||a,l=s(e[r],t[r],r);ns.isUndefined(l)&&s!==i||(n[r]=l)})),n}const Gs=e=>{const t=Zs({},e);let n,{data:r,withXSRFToken:a,xsrfHeaderName:s,xsrfCookieName:l,headers:i,auth:o}=t;if(t.headers=i=Ms.from(i),t.url=gs(Js(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),o&&i.set("Authorization","Basic "+btoa((o.username||"")+":"+(o.password?unescape(encodeURIComponent(o.password)):""))),ns.isFormData(r))if(Es.hasStandardBrowserEnv||Es.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if(!1!==(n=i.getContentType())){const[e,...t]=n?n.split(";").map((e=>e.trim())).filter(Boolean):[];i.setContentType([e||"multipart/form-data",...t].join("; "))}if(Es.hasStandardBrowserEnv&&(a&&ns.isFunction(a)&&(a=a(t)),a||!1!==a&&Qs(t.url))){const e=s&&l&&Ks.read(l);e&&i.set(s,e)}return t},Ys="undefined"!==typeof XMLHttpRequest&&function(e){return new Promise((function(t,n){const r=Gs(e);let a=r.data;const s=Ms.from(r.headers).normalize();let l,i,o,c,u,{responseType:d,onUploadProgress:f,onDownloadProgress:m}=r;function h(){c&&c(),u&&u(),r.cancelToken&&r.cancelToken.unsubscribe(l),r.signal&&r.signal.removeEventListener("abort",l)}let p=new XMLHttpRequest;function x(){if(!p)return;const r=Ms.from("getAllResponseHeaders"in p&&p.getAllResponseHeaders());Bs((function(e){t(e),h()}),(function(e){n(e),h()}),{data:d&&"text"!==d&&"json"!==d?p.response:p.responseText,status:p.status,statusText:p.statusText,headers:r,config:e,request:p}),p=null}p.open(r.method.toUpperCase(),r.url,!0),p.timeout=r.timeout,"onloadend"in p?p.onloadend=x:p.onreadystatechange=function(){p&&4===p.readyState&&(0!==p.status||p.responseURL&&0===p.responseURL.indexOf("file:"))&&setTimeout(x)},p.onabort=function(){p&&(n(new ls("Request aborted",ls.ECONNABORTED,e,p)),p=null)},p.onerror=function(){n(new ls("Network Error",ls.ERR_NETWORK,e,p)),p=null},p.ontimeout=function(){let t=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const a=r.transitional||vs;r.timeoutErrorMessage&&(t=r.timeoutErrorMessage),n(new ls(t,a.clarifyTimeoutError?ls.ETIMEDOUT:ls.ECONNABORTED,e,p)),p=null},void 0===a&&s.setContentType(null),"setRequestHeader"in p&&ns.forEach(s.toJSON(),(function(e,t){p.setRequestHeader(t,e)})),ns.isUndefined(r.withCredentials)||(p.withCredentials=!!r.withCredentials),d&&"json"!==d&&(p.responseType=r.responseType),m&&([o,u]=Vs(m,!0),p.addEventListener("progress",o)),f&&p.upload&&([i,c]=Vs(f),p.upload.addEventListener("progress",i),p.upload.addEventListener("loadend",c)),(r.cancelToken||r.signal)&&(l=t=>{p&&(n(!t||t.type?new Us(null,e,p):t),p.abort(),p=null)},r.cancelToken&&r.cancelToken.subscribe(l),r.signal&&(r.signal.aborted?l():r.signal.addEventListener("abort",l)));const g=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(r.url);g&&-1===Es.protocols.indexOf(g)?n(new ls("Unsupported protocol "+g+":",ls.ERR_BAD_REQUEST,e)):p.send(a||null)}))},el=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let n,r=new AbortController;const a=function(e){if(!n){n=!0,l();const t=e instanceof Error?e:this.reason;r.abort(t instanceof ls?t:new Us(t instanceof Error?t.message:t))}};let s=t&&setTimeout((()=>{s=null,a(new ls("timeout ".concat(t," of ms exceeded"),ls.ETIMEDOUT))}),t);const l=()=>{e&&(s&&clearTimeout(s),s=null,e.forEach((e=>{e.unsubscribe?e.unsubscribe(a):e.removeEventListener("abort",a)})),e=null)};e.forEach((e=>e.addEventListener("abort",a)));const{signal:i}=r;return i.unsubscribe=()=>ns.asap(l),i}};function tl(e,t){this.v=e,this.k=t}function nl(e){return function(){return new rl(e.apply(this,arguments))}}function rl(e){var t,n;function r(t,n){try{var s=e[t](n),l=s.value,i=l instanceof tl;Promise.resolve(i?l.v:l).then((function(n){if(i){var o="return"===t?"return":"next";if(!l.k||n.done)return r(o,n);n=e[o](n).value}a(s.done?"return":"normal",n)}),(function(e){r("throw",e)}))}catch(e){a("throw",e)}}function a(e,a){switch(e){case"return":t.resolve({value:a,done:!0});break;case"throw":t.reject(a);break;default:t.resolve({value:a,done:!1})}(t=t.next)?r(t.key,t.arg):n=null}this._invoke=function(e,a){return new Promise((function(s,l){var i={key:e,arg:a,resolve:s,reject:l,next:null};n?n=n.next=i:(t=n=i,r(e,a))}))},"function"!=typeof e.return&&(this.return=void 0)}function al(e){return new tl(e,0)}function sl(e){var t={},n=!1;function r(t,r){return n=!0,r=new Promise((function(n){n(e[t](r))})),{done:!1,value:new tl(r,1)}}return t["undefined"!=typeof Symbol&&Symbol.iterator||"@@iterator"]=function(){return this},t.next=function(e){return n?(n=!1,e):r("next",e)},"function"==typeof e.throw&&(t.throw=function(e){if(n)throw n=!1,e;return r("throw",e)}),"function"==typeof e.return&&(t.return=function(e){return n?(n=!1,e):r("return",e)}),t}function ll(e){var t,n,r,a=2;for("undefined"!=typeof Symbol&&(n=Symbol.asyncIterator,r=Symbol.iterator);a--;){if(n&&null!=(t=e[n]))return t.call(e);if(r&&null!=(t=e[r]))return new il(t.call(e));n="@@asyncIterator",r="@@iterator"}throw new TypeError("Object is not async iterable")}function il(e){function t(e){if(Object(e)!==e)return Promise.reject(new TypeError(e+" is not an object."));var t=e.done;return Promise.resolve(e.value).then((function(e){return{value:e,done:t}}))}return il=function(e){this.s=e,this.n=e.next},il.prototype={s:null,n:null,next:function(){return t(this.n.apply(this.s,arguments))},return:function(e){var n=this.s.return;return void 0===n?Promise.resolve({value:e,done:!0}):t(n.apply(this.s,arguments))},throw:function(e){var n=this.s.return;return void 0===n?Promise.reject(e):t(n.apply(this.s,arguments))}},new il(e)}rl.prototype["function"==typeof Symbol&&Symbol.asyncIterator||"@@asyncIterator"]=function(){return this},rl.prototype.next=function(e){return this._invoke("next",e)},rl.prototype.throw=function(e){return this._invoke("throw",e)},rl.prototype.return=function(e){return this._invoke("return",e)};const ol=function*(e,t){let n=e.byteLength;if(!t||n<t)return void(yield e);let r,a=0;for(;a<n;)r=a+t,yield e.slice(a,r),a=r},cl=function(){var e=nl((function*(e,t){var n,r=!1,a=!1;try{for(var s,l=ll(ul(e));r=!(s=yield al(l.next())).done;r=!1){const e=s.value;yield*sl(ll(ol(e,t)))}}catch(i){a=!0,n=i}finally{try{r&&null!=l.return&&(yield al(l.return()))}finally{if(a)throw n}}}));return function(t,n){return e.apply(this,arguments)}}(),ul=function(){var e=nl((function*(e){if(e[Symbol.asyncIterator])return void(yield*sl(ll(e)));const t=e.getReader();try{for(;;){const{done:e,value:n}=yield al(t.read());if(e)break;yield n}}finally{yield al(t.cancel())}}));return function(t){return e.apply(this,arguments)}}(),dl=(e,t,n,r)=>{const a=cl(e,t);let s,l=0,i=e=>{s||(s=!0,r&&r(e))};return new ReadableStream({async pull(e){try{const{done:t,value:r}=await a.next();if(t)return i(),void e.close();let s=r.byteLength;if(n){let e=l+=s;n(e)}e.enqueue(new Uint8Array(r))}catch(t){throw i(t),t}},cancel:e=>(i(e),a.return())},{highWaterMark:2})},fl="function"===typeof fetch&&"function"===typeof Request&&"function"===typeof Response,ml=fl&&"function"===typeof ReadableStream,hl=fl&&("function"===typeof TextEncoder?(pl=new TextEncoder,e=>pl.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer()));var pl;const xl=function(e){try{for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return!!e(...n)}catch(Tc){return!1}},gl=ml&&xl((()=>{let e=!1;const t=new Request(Es.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t})),yl=ml&&xl((()=>ns.isReadableStream(new Response("").body))),vl={stream:yl&&(e=>e.body)};var bl;fl&&(bl=new Response,["text","arrayBuffer","blob","formData","stream"].forEach((e=>{!vl[e]&&(vl[e]=ns.isFunction(bl[e])?t=>t[e]():(t,n)=>{throw new ls("Response type '".concat(e,"' is not supported"),ls.ERR_NOT_SUPPORT,n)})})));const wl=async(e,t)=>{const n=ns.toFiniteNumber(e.getContentLength());return null==n?(async e=>{if(null==e)return 0;if(ns.isBlob(e))return e.size;if(ns.isSpecCompliantForm(e)){const t=new Request(Es.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return ns.isArrayBufferView(e)||ns.isArrayBuffer(e)?e.byteLength:(ns.isURLSearchParams(e)&&(e+=""),ns.isString(e)?(await hl(e)).byteLength:void 0)})(t):n},jl=fl&&(async e=>{let{url:t,method:n,data:r,signal:a,cancelToken:s,timeout:l,onDownloadProgress:i,onUploadProgress:o,responseType:c,headers:u,withCredentials:d="same-origin",fetchOptions:f}=Gs(e);c=c?(c+"").toLowerCase():"text";let m,h=el([a,s&&s.toAbortSignal()],l);const p=h&&h.unsubscribe&&(()=>{h.unsubscribe()});let x;try{if(o&&gl&&"get"!==n&&"head"!==n&&0!==(x=await wl(u,r))){let e,n=new Request(t,{method:"POST",body:r,duplex:"half"});if(ns.isFormData(r)&&(e=n.headers.get("content-type"))&&u.setContentType(e),n.body){const[e,t]=$s(x,Vs(Hs(o)));r=dl(n.body,65536,e,t)}}ns.isString(d)||(d=d?"include":"omit");const a="credentials"in Request.prototype;m=new Request(t,lt(lt({},f),{},{signal:h,method:n.toUpperCase(),headers:u.normalize().toJSON(),body:r,duplex:"half",credentials:a?d:void 0}));let s=await fetch(m);const l=yl&&("stream"===c||"response"===c);if(yl&&(i||l&&p)){const e={};["status","statusText","headers"].forEach((t=>{e[t]=s[t]}));const t=ns.toFiniteNumber(s.headers.get("content-length")),[n,r]=i&&$s(t,Vs(Hs(i),!0))||[];s=new Response(dl(s.body,65536,n,(()=>{r&&r(),p&&p()})),e)}c=c||"text";let g=await vl[ns.findKey(vl,c)||"text"](s,e);return!l&&p&&p(),await new Promise(((t,n)=>{Bs(t,n,{data:g,headers:Ms.from(s.headers),status:s.status,statusText:s.statusText,config:e,request:m})}))}catch(g){if(p&&p(),g&&"TypeError"===g.name&&/Load failed|fetch/i.test(g.message))throw Object.assign(new ls("Network Error",ls.ERR_NETWORK,e,m),{cause:g.cause||g});throw ls.from(g,g&&g.code,e,m)}}),Nl={http:null,xhr:Ys,fetch:jl};ns.forEach(Nl,((e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(Tc){}Object.defineProperty(e,"adapterName",{value:t})}}));const kl=e=>"- ".concat(e),Sl=e=>ns.isFunction(e)||null===e||!1===e,El=e=>{e=ns.isArray(e)?e:[e];const{length:t}=e;let n,r;const a={};for(let s=0;s<t;s++){let t;if(n=e[s],r=n,!Sl(n)&&(r=Nl[(t=String(n)).toLowerCase()],void 0===r))throw new ls("Unknown adapter '".concat(t,"'"));if(r)break;a[t||"#"+s]=r}if(!r){const e=Object.entries(a).map((e=>{let[t,n]=e;return"adapter ".concat(t," ")+(!1===n?"is not supported by the environment":"is not available in the build")}));let n=t?e.length>1?"since :\n"+e.map(kl).join("\n"):" "+kl(e[0]):"as no adapter specified";throw new ls("There is no suitable adapter to dispatch the request "+n,"ERR_NOT_SUPPORT")}return r};function Cl(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Us(null,e)}function Ol(e){Cl(e),e.headers=Ms.from(e.headers),e.data=Is.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);return El(e.adapter||Ps.adapter)(e).then((function(t){return Cl(e),t.data=Is.call(e,e.transformResponse,t),t.headers=Ms.from(t.headers),t}),(function(t){return Fs(t)||(Cl(e),t&&t.response&&(t.response.data=Is.call(e,e.transformResponse,t.response),t.response.headers=Ms.from(t.response.headers))),Promise.reject(t)}))}const Pl="1.9.0",_l={};["object","boolean","number","function","string","symbol"].forEach(((e,t)=>{_l[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}}));const Rl={};_l.transitional=function(e,t,n){function r(e,t){return"[Axios v1.9.0] Transitional option '"+e+"'"+t+(n?". "+n:"")}return(n,a,s)=>{if(!1===e)throw new ls(r(a," has been removed"+(t?" in "+t:"")),ls.ERR_DEPRECATED);return t&&!Rl[a]&&(Rl[a]=!0,console.warn(r(a," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,a,s)}},_l.spelling=function(e){return(t,n)=>(console.warn("".concat(n," is likely a misspelling of ").concat(e)),!0)};const Ll={assertOptions:function(e,t,n){if("object"!==typeof e)throw new ls("options must be an object",ls.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let a=r.length;for(;a-- >0;){const s=r[a],l=t[s];if(l){const t=e[s],n=void 0===t||l(t,s,e);if(!0!==n)throw new ls("option "+s+" must be "+n,ls.ERR_BAD_OPTION_VALUE)}else if(!0!==n)throw new ls("Unknown option "+s,ls.ERR_BAD_OPTION)}},validators:_l},Tl=Ll.validators;class Al{constructor(e){this.defaults=e||{},this.interceptors={request:new ys,response:new ys}}async request(e,t){try{return await this._request(e,t)}catch(n){if(n instanceof Error){let e={};Error.captureStackTrace?Error.captureStackTrace(e):e=new Error;const t=e.stack?e.stack.replace(/^.+\n/,""):"";try{n.stack?t&&!String(n.stack).endsWith(t.replace(/^.+\n.+\n/,""))&&(n.stack+="\n"+t):n.stack=t}catch(Tc){}}throw n}}_request(e,t){"string"===typeof e?(t=t||{}).url=e:t=e||{},t=Zs(this.defaults,t);const{transitional:n,paramsSerializer:r,headers:a}=t;void 0!==n&&Ll.assertOptions(n,{silentJSONParsing:Tl.transitional(Tl.boolean),forcedJSONParsing:Tl.transitional(Tl.boolean),clarifyTimeoutError:Tl.transitional(Tl.boolean)},!1),null!=r&&(ns.isFunction(r)?t.paramsSerializer={serialize:r}:Ll.assertOptions(r,{encode:Tl.function,serialize:Tl.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),Ll.assertOptions(t,{baseUrl:Tl.spelling("baseURL"),withXsrfToken:Tl.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let s=a&&ns.merge(a.common,a[t.method]);a&&ns.forEach(["delete","get","head","post","put","patch","common"],(e=>{delete a[e]})),t.headers=Ms.concat(s,a);const l=[];let i=!0;this.interceptors.request.forEach((function(e){"function"===typeof e.runWhen&&!1===e.runWhen(t)||(i=i&&e.synchronous,l.unshift(e.fulfilled,e.rejected))}));const o=[];let c;this.interceptors.response.forEach((function(e){o.push(e.fulfilled,e.rejected)}));let u,d=0;if(!i){const e=[Ol.bind(this),void 0];for(e.unshift.apply(e,l),e.push.apply(e,o),u=e.length,c=Promise.resolve(t);d<u;)c=c.then(e[d++],e[d++]);return c}u=l.length;let f=t;for(d=0;d<u;){const e=l[d++],t=l[d++];try{f=e(f)}catch(m){t.call(this,m);break}}try{c=Ol.call(this,f)}catch(m){return Promise.reject(m)}for(d=0,u=o.length;d<u;)c=c.then(o[d++],o[d++]);return c}getUri(e){return gs(Js((e=Zs(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}}ns.forEach(["delete","get","head","options"],(function(e){Al.prototype[e]=function(t,n){return this.request(Zs(n||{},{method:e,url:t,data:(n||{}).data}))}})),ns.forEach(["post","put","patch"],(function(e){function t(t){return function(n,r,a){return this.request(Zs(a||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}Al.prototype[e]=t(),Al.prototype[e+"Form"]=t(!0)}));const Dl=Al;class Ml{constructor(e){if("function"!==typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise((function(e){t=e}));const n=this;this.promise.then((e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null})),this.promise.then=e=>{let t;const r=new Promise((e=>{n.subscribe(e),t=e})).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e((function(e,r,a){n.reason||(n.reason=new Us(e,r,a),t(n.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;const t=new Ml((function(t){e=t}));return{token:t,cancel:e}}}const Il=Ml;const Fl={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Fl).forEach((e=>{let[t,n]=e;Fl[n]=t}));const zl=Fl;const Ul=function e(t){const n=new Dl(t),r=ga(Dl.prototype.request,n);return ns.extend(r,Dl.prototype,n,{allOwnKeys:!0}),ns.extend(r,n,null,{allOwnKeys:!0}),r.create=function(n){return e(Zs(t,n))},r}(Ps);Ul.Axios=Dl,Ul.CanceledError=Us,Ul.CancelToken=Il,Ul.isCancel=Fs,Ul.VERSION=Pl,Ul.toFormData=ds,Ul.AxiosError=ls,Ul.Cancel=Ul.CanceledError,Ul.all=function(e){return Promise.all(e)},Ul.spread=function(e){return function(t){return e.apply(null,t)}},Ul.isAxiosError=function(e){return ns.isObject(e)&&!0===e.isAxiosError},Ul.mergeConfig=Zs,Ul.AxiosHeaders=Ms,Ul.formToJSON=e=>Cs(ns.isHTMLForm(e)?new FormData(e):e),Ul.getAdapter=El,Ul.HttpStatusCode=zl,Ul.default=Ul;const Bl=Ul,Wl=fa("search/searchResources",(async e=>(await Bl.post("/v1/discovery/search",e)).data)),ql=fa("search/getResource",(async e=>(await Bl.get("/v1/discovery/resources/".concat(e))).data.resource)),Vl=la({name:"search",initialState:{resources:[],loading:!1,error:null,totalCount:0,query:"",provider:""},reducers:{setQuery:(e,t)=>{e.query=t.payload},setProvider:(e,t)=>{e.provider=t.payload},clearResults:e=>{e.resources=[],e.totalCount=0,e.error=null}},extraReducers:e=>{e.addCase(Wl.pending,(e=>{e.loading=!0,e.error=null})).addCase(Wl.fulfilled,((e,t)=>{e.loading=!1,e.resources=t.payload.resources,e.totalCount=t.payload.total_count})).addCase(Wl.rejected,((e,t)=>{e.loading=!1,e.error=t.error.message||"Search failed"})).addCase(ql.pending,(e=>{e.loading=!0,e.error=null})).addCase(ql.fulfilled,((e,t)=>{e.loading=!1;const n=e.resources.findIndex((e=>e.id===t.payload.id));-1!==n&&(e.resources[n]=t.payload)})).addCase(ql.rejected,((e,t)=>{e.loading=!1,e.error=t.error.message||"Failed to get resource"}))}}),{setQuery:$l,setProvider:Hl,clearResults:Ql}=Vl.actions,Kl=Vl.reducer,Jl=["title","titleId"];function Xl(e,t){let{title:n,titleId:r}=e,s=mt(e,Jl);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 15a4.5 4.5 0 0 0 4.5 4.5H18a3.75 3.75 0 0 0 1.332-7.257 3 3 0 0 0-3.758-3.848 5.25 5.25 0 0 0-10.233 2.33A4.502 4.502 0 0 0 2.25 15Z"}))}const Zl=a.forwardRef(Xl),Gl=["title","titleId"];function Yl(e,t){let{title:n,titleId:r}=e,s=mt(e,Gl);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.568 3H5.25A2.25 2.25 0 0 0 3 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 0 0 5.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 0 0 9.568 3Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 6h.008v.008H6V6Z"}))}const ei=a.forwardRef(Yl),ti=()=>{const e=R(),{resources:t,loading:n,error:r,totalCount:s,query:l,provider:i}=j((e=>e.search)),[o,c]=(0,a.useState)(l),[u,d]=(0,a.useState)(i),f=()=>{e($l(o)),e(Hl(u)),e(Wl({query:o,provider:u,limit:50}))};(0,a.useEffect)((()=>{e(Wl({limit:20}))}),[e]);const m=e=>{switch(e.toLowerCase()){case"aws":return"bg-orange-100 text-orange-800";case"gcp":return"bg-blue-100 text-blue-800";case"azure":return"bg-cyan-100 text-cyan-800";default:return"bg-gray-100 text-gray-800"}};return(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h1",{className:"text-2xl font-bold text-white",children:"Resource Discovery"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Search and discover resources across all your cloud providers"})]}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsx)("div",{className:"px-4 py-5 sm:p-6",children:(0,it.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-4",children:[(0,it.jsxs)("div",{className:"sm:col-span-2",children:[(0,it.jsx)("label",{htmlFor:"search",className:"block text-sm font-medium text-gray-300",children:"Search Query"}),(0,it.jsxs)("div",{className:"mt-1 relative rounded-md shadow-sm",children:[(0,it.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,it.jsx)(xt,{className:"h-5 w-5 text-gray-400"})}),(0,it.jsx)("input",{type:"text",id:"search",className:"block w-full pl-10 pr-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent",placeholder:"Search by name, type, or tags...",value:o,onChange:e=>c(e.target.value),onKeyPress:e=>"Enter"===e.key&&f()})]})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{htmlFor:"provider",className:"block text-sm font-medium text-gray-300",children:"Provider"}),(0,it.jsx)("select",{id:"provider",className:"mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-600 bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent rounded-md",value:u,onChange:e=>d(e.target.value),children:[{value:"",label:"All Providers"},{value:"aws",label:"AWS"},{value:"gcp",label:"GCP"},{value:"azure",label:"Azure"}].map((e=>(0,it.jsx)("option",{value:e.value,children:e.label},e.value)))})]}),(0,it.jsxs)("div",{className:"flex items-end space-x-2",children:[(0,it.jsx)("button",{type:"button",onClick:f,disabled:n,className:"flex-1 bg-cyan-600 border border-transparent rounded-md py-2 px-4 inline-flex justify-center text-sm font-medium text-white hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 disabled:opacity-50",children:n?"Searching...":"Search"}),(0,it.jsx)("button",{type:"button",onClick:()=>{c(""),d(""),e(Ql())},className:"bg-gray-600 border border-transparent rounded-md py-2 px-4 inline-flex justify-center text-sm font-medium text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500",children:"Clear"})]})]})})}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white",children:"Search Results"}),(0,it.jsxs)("span",{className:"text-sm text-gray-400",children:[s," resources found"]})]}),r&&(0,it.jsx)("div",{className:"mb-4 bg-red-50 border border-red-200 rounded-md p-4",children:(0,it.jsx)("div",{className:"text-sm text-red-700",children:r})}),n?(0,it.jsx)("div",{className:"flex items-center justify-center h-32",children:(0,it.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500"})}):(0,it.jsx)("div",{className:"space-y-4",children:0===t.length?(0,it.jsxs)("div",{className:"text-center py-12",children:[(0,it.jsx)(Zl,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,it.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-300",children:"No resources found"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Try adjusting your search criteria or run a discovery scan."})]}):t.map((e=>(0,it.jsxs)("div",{className:"border border-gray-600 rounded-lg p-4 hover:bg-gray-700 transition-colors",children:[(0,it.jsx)("div",{className:"flex items-start justify-between",children:(0,it.jsxs)("div",{className:"flex-1",children:[(0,it.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,it.jsx)(vn,{className:"h-5 w-5 text-gray-400"}),(0,it.jsx)("h4",{className:"text-lg font-medium text-white",children:e.name}),(0,it.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(m(e.provider)),children:e.provider.toUpperCase()})]}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:e.type}),(0,it.jsx)("p",{className:"mt-1 text-xs text-gray-500 font-mono",children:e.id})]})}),Object.keys(e.tags).length>0&&(0,it.jsxs)("div",{className:"mt-3",children:[(0,it.jsxs)("div",{className:"flex items-center space-x-1 mb-2",children:[(0,it.jsx)(ei,{className:"h-4 w-4 text-gray-400"}),(0,it.jsx)("span",{className:"text-sm text-gray-400",children:"Tags"})]}),(0,it.jsx)("div",{className:"flex flex-wrap gap-1",children:Object.entries(e.tags).map((e=>{let[t,n]=e;return(0,it.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-600 text-gray-200",children:[t,": ",n]},t)}))})]})]},e.id)))})]})})]})},ni=["title","titleId"];function ri(e,t){let{title:n,titleId:r}=e,s=mt(e,ni);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 5.25a3 3 0 0 1 3 3m3 0a6 6 0 0 1-7.029 5.912c-.563-.097-1.159.026-1.563.43L10.5 17.25H8.25v2.25H6v2.25H2.25v-2.818c0-.597.237-1.17.659-1.591l6.499-6.499c.404-.404.527-1 .43-1.563A6 6 0 1 1 21.75 8.25Z"}))}const ai=a.forwardRef(ri),si=()=>{const e=R(),[t,n]=(0,a.useState)(1),[r,s]=(0,a.useState)({provider:"aws"}),[l,i]=(0,a.useState)(!1),[o,c]=(0,a.useState)(null),u=[{id:1,name:"Choose Provider",description:"Select your cloud provider"},{id:2,name:"Add Credentials",description:"Configure authentication"},{id:3,name:"Configure Discovery",description:"Set discovery parameters"},{id:4,name:"Launch Discovery",description:"Start resource discovery"}],d=[{id:"aws",name:"Amazon Web Services",icon:"\ud83d\udfe0",description:"Discover EC2, S3, RDS, and other AWS resources",fields:["accessKey","secretKey","region"]},{id:"gcp",name:"Google Cloud Platform",icon:"\ud83d\udd35",description:"Discover Compute Engine, Cloud Storage, and GCP resources",fields:["projectId"]},{id:"azure",name:"Microsoft Azure",icon:"\ud83d\udd37",description:"Discover Virtual Machines, Storage, and Azure resources",fields:["subscriptionId","tenantId"]}],f=(e,t)=>{s((n=>lt(lt({},n),{},{[e]:t})))},m=async()=>{i(!0),c(null),setTimeout((()=>{c("success"),i(!1)}),2e3)},h=()=>{e(Wl({provider:r.provider,limit:100})),n(4)};return(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h1",{className:"text-2xl font-bold text-white",children:"Discovery Wizard"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Multi-step wizard to add credentials and launch discovery workflows"})]}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsx)("div",{className:"px-4 py-5 sm:p-6",children:(0,it.jsx)("nav",{"aria-label":"Progress",children:(0,it.jsx)("ol",{className:"flex items-center",children:u.map(((e,n)=>(0,it.jsxs)("li",{className:"".concat(n!==u.length-1?"pr-8 sm:pr-20":""," relative"),children:[(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"relative flex h-8 w-8 items-center justify-center rounded-full ".concat(e.id<=t?"bg-cyan-600 text-white":"border-2 border-gray-600 bg-gray-800 text-gray-400"),children:e.id<t?(0,it.jsx)(mn,{className:"h-5 w-5"}):(0,it.jsx)("span",{className:"text-sm font-medium",children:e.id})}),(0,it.jsxs)("div",{className:"ml-4 min-w-0 flex-1",children:[(0,it.jsx)("p",{className:"text-sm font-medium ".concat(e.id<=t?"text-white":"text-gray-400"),children:e.name}),(0,it.jsx)("p",{className:"text-sm text-gray-400",children:e.description})]})]}),n!==u.length-1&&(0,it.jsx)("div",{className:"absolute top-4 left-4 -ml-px mt-0.5 h-full w-0.5 bg-gray-600"})]},e.id)))})})})}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsx)("div",{className:"px-4 py-5 sm:p-6",children:(()=>{switch(t){case 1:return(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsxs)("div",{className:"text-center",children:[(0,it.jsx)(Zl,{className:"mx-auto h-12 w-12 text-cyan-400"}),(0,it.jsx)("h3",{className:"mt-2 text-lg font-medium text-white",children:"Choose Cloud Provider"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Select the cloud provider you want to discover resources from"})]}),(0,it.jsx)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-3",children:d.map((e=>(0,it.jsx)("button",{onClick:()=>{return t=e.id,s({provider:t}),void n(2);var t},className:"relative rounded-lg border border-gray-600 bg-gray-800 p-6 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-cyan-500 transition-colors",children:(0,it.jsxs)("div",{className:"text-center",children:[(0,it.jsx)("div",{className:"text-3xl mb-3",children:e.icon}),(0,it.jsx)("h4",{className:"text-lg font-medium text-white",children:e.name}),(0,it.jsx)("p",{className:"mt-2 text-sm text-gray-400",children:e.description})]})},e.id)))})]});case 2:const e=d.find((e=>e.id===r.provider));return(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsxs)("div",{className:"text-center",children:[(0,it.jsx)(ai,{className:"mx-auto h-12 w-12 text-cyan-400"}),(0,it.jsxs)("h3",{className:"mt-2 text-lg font-medium text-white",children:["Configure ",null===e||void 0===e?void 0:e.name," Credentials"]}),(0,it.jsxs)("p",{className:"mt-1 text-sm text-gray-400",children:["Enter your authentication credentials for ",null===e||void 0===e?void 0:e.name]})]}),(0,it.jsxs)("div",{className:"space-y-4",children:["aws"===r.provider&&(0,it.jsxs)(it.Fragment,{children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Access Key ID"}),(0,it.jsx)("input",{type:"text",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:r.accessKey||"",onChange:e=>f("accessKey",e.target.value),placeholder:"AKIA..."})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Secret Access Key"}),(0,it.jsx)("input",{type:"password",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:r.secretKey||"",onChange:e=>f("secretKey",e.target.value),placeholder:"\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Region"}),(0,it.jsxs)("select",{className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:r.region||"",onChange:e=>f("region",e.target.value),children:[(0,it.jsx)("option",{value:"",children:"Select a region"}),(0,it.jsx)("option",{value:"us-east-1",children:"US East (N. Virginia)"}),(0,it.jsx)("option",{value:"us-west-2",children:"US West (Oregon)"}),(0,it.jsx)("option",{value:"eu-west-1",children:"Europe (Ireland)"}),(0,it.jsx)("option",{value:"ap-southeast-1",children:"Asia Pacific (Singapore)"})]})]})]}),"gcp"===r.provider&&(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Project ID"}),(0,it.jsx)("input",{type:"text",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:r.projectId||"",onChange:e=>f("projectId",e.target.value),placeholder:"my-gcp-project"})]}),"azure"===r.provider&&(0,it.jsxs)(it.Fragment,{children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Subscription ID"}),(0,it.jsx)("input",{type:"text",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:r.subscriptionId||"",onChange:e=>f("subscriptionId",e.target.value),placeholder:"xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Tenant ID"}),(0,it.jsx)("input",{type:"text",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:r.tenantId||"",onChange:e=>f("tenantId",e.target.value),placeholder:"xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"})]})]})]}),(0,it.jsxs)("div",{className:"flex justify-between",children:[(0,it.jsx)("button",{onClick:()=>n(1),className:"px-4 py-2 border border-gray-600 rounded-md text-gray-300 hover:bg-gray-700",children:"Back"}),(0,it.jsx)("button",{onClick:m,disabled:l,className:"px-4 py-2 bg-cyan-600 text-white rounded-md hover:bg-cyan-700 disabled:opacity-50",children:l?"Validating...":"Validate & Continue"})]}),"success"===o&&(0,it.jsxs)("div",{className:"flex items-center space-x-2 text-green-400",children:[(0,it.jsx)(mn,{className:"h-5 w-5"}),(0,it.jsx)("span",{children:"Credentials validated successfully!"}),(0,it.jsx)("button",{onClick:()=>n(3),className:"ml-4 px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700",children:"Continue"})]}),"error"===o&&(0,it.jsxs)("div",{className:"flex items-center space-x-2 text-red-400",children:[(0,it.jsx)(xn,{className:"h-5 w-5"}),(0,it.jsx)("span",{children:"Credential validation failed. Please check your credentials."})]})]});case 3:return(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsxs)("div",{className:"text-center",children:[(0,it.jsx)(xt,{className:"mx-auto h-12 w-12 text-cyan-400"}),(0,it.jsx)("h3",{className:"mt-2 text-lg font-medium text-white",children:"Configure Discovery"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Set parameters for resource discovery"})]}),(0,it.jsxs)("div",{className:"space-y-4",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Discovery Scope"}),(0,it.jsxs)("select",{className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",children:[(0,it.jsx)("option",{children:"All Resources"}),(0,it.jsx)("option",{children:"Compute Only"}),(0,it.jsx)("option",{children:"Storage Only"}),(0,it.jsx)("option",{children:"Network Only"})]})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Resource Tags Filter"}),(0,it.jsx)("input",{type:"text",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",placeholder:"Environment=production,Team=platform"})]}),(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("input",{type:"checkbox",className:"h-4 w-4 text-cyan-600 focus:ring-cyan-500 border-gray-600 rounded bg-gray-700",defaultChecked:!0}),(0,it.jsx)("label",{className:"ml-2 block text-sm text-gray-300",children:"Cache results for faster subsequent searches"})]})]}),(0,it.jsxs)("div",{className:"flex justify-between",children:[(0,it.jsx)("button",{onClick:()=>n(2),className:"px-4 py-2 border border-gray-600 rounded-md text-gray-300 hover:bg-gray-700",children:"Back"}),(0,it.jsx)("button",{onClick:h,className:"px-4 py-2 bg-cyan-600 text-white rounded-md hover:bg-cyan-700",children:"Launch Discovery"})]})]});case 4:return(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsxs)("div",{className:"text-center",children:[(0,it.jsx)(mn,{className:"mx-auto h-12 w-12 text-green-400"}),(0,it.jsx)("h3",{className:"mt-2 text-lg font-medium text-white",children:"Discovery Launched!"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Resource discovery is now running in the background"})]}),(0,it.jsxs)("div",{className:"bg-gray-700 rounded-lg p-4",children:[(0,it.jsx)("h4",{className:"text-md font-medium text-white mb-2",children:"Discovery Status"}),(0,it.jsxs)("div",{className:"space-y-2",children:[(0,it.jsxs)("div",{className:"flex justify-between",children:[(0,it.jsx)("span",{className:"text-gray-300",children:"Provider:"}),(0,it.jsx)("span",{className:"text-white",children:r.provider.toUpperCase()})]}),(0,it.jsxs)("div",{className:"flex justify-between",children:[(0,it.jsx)("span",{className:"text-gray-300",children:"Status:"}),(0,it.jsx)("span",{className:"text-green-400",children:"Running"})]}),(0,it.jsxs)("div",{className:"flex justify-between",children:[(0,it.jsx)("span",{className:"text-gray-300",children:"Resources Found:"}),(0,it.jsx)("span",{className:"text-white",children:"42"})]})]})]}),(0,it.jsx)("div",{className:"flex justify-center",children:(0,it.jsx)("button",{onClick:()=>window.location.href="/search",className:"px-6 py-2 bg-cyan-600 text-white rounded-md hover:bg-cyan-700",children:"View Results"})})]});default:return null}})()})})]})},li=fa("workflow/fetchWorkflows",(async()=>(await Bl.get("/v1/workflows")).data.workflows)),ii=fa("workflow/executeWorkflow",(async e=>(await Bl.post("/v1/workflows/execute",{workflow_id:e.workflowId,inputs:e.inputs})).data)),oi=fa("workflow/getWorkflowStatus",(async e=>(await Bl.get("/v1/workflows/".concat(e,"/status"))).data)),ci=la({name:"workflow",initialState:{workflows:[],executions:[],loading:!1,error:null,selectedWorkflow:null},reducers:{setSelectedWorkflow:(e,t)=>{e.selectedWorkflow=t.payload},clearExecutions:e=>{e.executions=[]},updateExecutionStatus:(e,t)=>{const n=e.executions.find((e=>e.execution_id===t.payload.executionId));n&&(n.status=t.payload.status)}},extraReducers:e=>{e.addCase(li.pending,(e=>{e.loading=!0,e.error=null})).addCase(li.fulfilled,((e,t)=>{e.loading=!1,e.workflows=t.payload})).addCase(li.rejected,((e,t)=>{e.loading=!1,e.error=t.error.message||"Failed to fetch workflows"})).addCase(ii.pending,(e=>{e.loading=!0,e.error=null})).addCase(ii.fulfilled,((e,t)=>{e.loading=!1,e.executions.push(t.payload)})).addCase(ii.rejected,((e,t)=>{e.loading=!1,e.error=t.error.message||"Failed to execute workflow"})).addCase(oi.fulfilled,((e,t)=>{const n=e.executions.findIndex((e=>e.execution_id===t.payload.workflow_id));-1!==n&&(e.executions[n]=lt(lt({},e.executions[n]),t.payload))}))}}),{setSelectedWorkflow:ui,clearExecutions:di,updateExecutionStatus:fi}=ci.actions,mi=ci.reducer,hi=["title","titleId"];function pi(e,t){let{title:n,titleId:r}=e,s=mt(e,hi);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const xi=a.forwardRef(pi),gi=()=>{const e=R(),{workflows:t,executions:n,loading:r,error:s,selectedWorkflow:l}=j((e=>e.workflow)),[i,o]=(0,a.useState)({});(0,a.useEffect)((()=>{e(li())}),[e]);const c=e=>{switch(e){case"running":return(0,it.jsx)(jn,{className:"h-5 w-5 text-yellow-400"});case"completed":return(0,it.jsx)(mn,{className:"h-5 w-5 text-green-400"});case"failed":return(0,it.jsx)(xi,{className:"h-5 w-5 text-red-400"});default:return(0,it.jsx)(Et,{className:"h-5 w-5 text-gray-400"})}},u=e=>{switch(e){case"running":return"bg-yellow-100 text-yellow-800";case"completed":return"bg-green-100 text-green-800";case"failed":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}};return(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h1",{className:"text-2xl font-bold text-white",children:"Workflow Executor"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Execute and monitor automated workflows across your infrastructure"})]}),(0,it.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white mb-4",children:"Available Workflows"}),r?(0,it.jsx)("div",{className:"flex items-center justify-center h-32",children:(0,it.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500"})}):(0,it.jsxs)("div",{className:"space-y-3",children:[t.map((t=>(0,it.jsx)("div",{className:"border rounded-lg p-4 cursor-pointer transition-colors ".concat((null===l||void 0===l?void 0:l.id)===t.id?"border-cyan-500 bg-cyan-500/10":"border-gray-600 hover:bg-gray-700"),onClick:()=>e(ui(t)),children:(0,it.jsxs)("div",{className:"flex items-start justify-between",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h4",{className:"text-lg font-medium text-white",children:t.name}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:t.description}),(0,it.jsxs)("p",{className:"mt-1 text-xs text-gray-500",children:["Created: ",new Date(t.created_at).toLocaleDateString()]})]}),(0,it.jsx)(Et,{className:"h-6 w-6 text-gray-400"})]})},t.id))),0===t.length&&(0,it.jsxs)("div",{className:"text-center py-8",children:[(0,it.jsx)(Et,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,it.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-300",children:"No workflows available"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Create a workflow to get started."})]})]})]})}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white mb-4",children:"Execute Workflow"}),l?(0,it.jsxs)("div",{className:"space-y-4",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h4",{className:"text-md font-medium text-white",children:l.name}),(0,it.jsx)("p",{className:"text-sm text-gray-400",children:l.description})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Input Parameters (JSON)"}),(0,it.jsx)("textarea",{className:"block w-full h-32 px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent",placeholder:'{"key": "value"}',value:JSON.stringify(i,null,2),onChange:e=>{try{o(JSON.parse(e.target.value||"{}"))}catch(t){}}})]}),(0,it.jsxs)("button",{onClick:()=>{l&&(e(ii({workflowId:l.id,inputs:i})),o({}))},disabled:r,className:"w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 disabled:opacity-50",children:[(0,it.jsx)(ln,{className:"h-4 w-4 mr-2"}),r?"Executing...":"Execute Workflow"]})]}):(0,it.jsxs)("div",{className:"text-center py-8",children:[(0,it.jsx)(ln,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,it.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-300",children:"Select a workflow"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Choose a workflow from the list to execute it."})]}),s&&(0,it.jsx)("div",{className:"mt-4 bg-red-50 border border-red-200 rounded-md p-4",children:(0,it.jsx)("div",{className:"text-sm text-red-700",children:s})})]})})]}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white mb-4",children:"Execution History"}),0===n.length?(0,it.jsxs)("div",{className:"text-center py-8",children:[(0,it.jsx)(jn,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,it.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-300",children:"No executions yet"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Execute a workflow to see the history here."})]}):(0,it.jsx)("div",{className:"space-y-4",children:n.map((t=>(0,it.jsxs)("div",{className:"border border-gray-600 rounded-lg p-4",children:[(0,it.jsxs)("div",{className:"flex items-start justify-between",children:[(0,it.jsxs)("div",{className:"flex-1",children:[(0,it.jsxs)("div",{className:"flex items-center space-x-2",children:[c(t.status),(0,it.jsx)("h4",{className:"text-lg font-medium text-white",children:t.workflow_id}),(0,it.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(u(t.status)),children:t.status})]}),(0,it.jsxs)("p",{className:"mt-1 text-sm text-gray-400",children:["Execution ID: ",t.execution_id]}),t.started_at&&(0,it.jsxs)("p",{className:"mt-1 text-xs text-gray-500",children:["Started: ",new Date(t.started_at).toLocaleString()]}),t.completed_at&&(0,it.jsxs)("p",{className:"mt-1 text-xs text-gray-500",children:["Completed: ",new Date(t.completed_at).toLocaleString()]})]}),(0,it.jsx)("button",{onClick:()=>e(oi(t.execution_id)),className:"text-cyan-400 hover:text-cyan-300 text-sm",children:"Refresh"})]}),t.outputs&&Object.keys(t.outputs).length>0&&(0,it.jsxs)("div",{className:"mt-3",children:[(0,it.jsx)("h5",{className:"text-sm font-medium text-gray-300",children:"Outputs:"}),(0,it.jsx)("pre",{className:"mt-1 text-xs text-gray-400 bg-gray-700 p-2 rounded overflow-x-auto",children:JSON.stringify(t.outputs,null,2)})]})]},t.execution_id)))})]})})]})},yi=fa("envoy/fetchConfigs",(async()=>(await Bl.get("/v1/envoy/configs")).data.configs)),vi=fa("envoy/fetchNodes",(async()=>(await Bl.get("/v1/envoy/nodes")).data.nodes)),bi=fa("envoy/createConfig",(async e=>(await Bl.post("/v1/envoy/configs",e)).data)),wi=fa("envoy/updateConfig",(async e=>(await Bl.put("/v1/envoy/configs/".concat(e.id),e)).data)),ji=la({name:"envoy",initialState:{configs:[],nodes:[],selectedConfig:null,loading:!1,error:null},reducers:{setSelectedConfig:(e,t)=>{e.selectedConfig=t.payload}},extraReducers:e=>{e.addCase(yi.pending,(e=>{e.loading=!0,e.error=null})).addCase(yi.fulfilled,((e,t)=>{e.loading=!1,e.configs=t.payload})).addCase(yi.rejected,((e,t)=>{e.loading=!1,e.error=t.error.message||"Failed to fetch Envoy configs"})).addCase(vi.fulfilled,((e,t)=>{e.nodes=t.payload})).addCase(bi.fulfilled,((e,t)=>{e.configs.push(t.payload)})).addCase(wi.fulfilled,((e,t)=>{const n=e.configs.findIndex((e=>e.id===t.payload.id));-1!==n&&(e.configs[n]=t.payload)}))}}),{setSelectedConfig:Ni}=ji.actions,ki=ji.reducer,Si=["title","titleId"];function Ei(e,t){let{title:n,titleId:r}=e,s=mt(e,Si);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))}const Ci=a.forwardRef(Ei),Oi=["title","titleId"];function Pi(e,t){let{title:n,titleId:r}=e,s=mt(e,Oi);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))}const _i=a.forwardRef(Pi);function Ri(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Li(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Ti(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Li(Object(n),!0).forEach((function(t){Ri(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Li(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Ai(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n,r,a={},s=Object.keys(e);for(r=0;r<s.length;r++)n=s[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(r=0;r<s.length;r++)n=s[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}function Di(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Mi(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ii(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Fi(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ii(Object(n),!0).forEach((function(t){Mi(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ii(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function zi(e){return function t(){for(var n=this,r=arguments.length,a=new Array(r),s=0;s<r;s++)a[s]=arguments[s];return a.length>=e.length?e.apply(this,a):function(){for(var e=arguments.length,r=new Array(e),s=0;s<e;s++)r[s]=arguments[s];return t.apply(n,[].concat(a,r))}}}function Ui(e){return{}.toString.call(e).includes("Object")}function Bi(e){return"function"===typeof e}var Wi=zi((function(e,t){throw new Error(e[t]||e.default)}))({initialIsRequired:"initial state is required",initialType:"initial state should be an object",initialContent:"initial state shouldn't be an empty object",handlerType:"handler should be an object or a function",handlersType:"all handlers should be a functions",selectorType:"selector should be a function",changeType:"provided value of changes should be an object",changeField:'it seams you want to change a field in the state which is not specified in the "initial" state',default:"an unknown error accured in `state-local` package"}),qi={changes:function(e,t){return Ui(t)||Wi("changeType"),Object.keys(t).some((function(t){return n=e,r=t,!Object.prototype.hasOwnProperty.call(n,r);var n,r}))&&Wi("changeField"),t},selector:function(e){Bi(e)||Wi("selectorType")},handler:function(e){Bi(e)||Ui(e)||Wi("handlerType"),Ui(e)&&Object.values(e).some((function(e){return!Bi(e)}))&&Wi("handlersType")},initial:function(e){var t;e||Wi("initialIsRequired"),Ui(e)||Wi("initialType"),t=e,Object.keys(t).length||Wi("initialContent")}};function Vi(e,t){return Bi(t)?t(e.current):t}function $i(e,t){return e.current=Fi(Fi({},e.current),t),t}function Hi(e,t,n){return Bi(t)?t(e.current):Object.keys(n).forEach((function(n){var r;return null===(r=t[n])||void 0===r?void 0:r.call(t,e.current[n])})),n}var Qi={create:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};qi.initial(e),qi.handler(t);var n={current:e},r=zi(Hi)(n,t),a=zi($i)(n),s=zi(qi.changes)(e),l=zi(Vi)(n);return[function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(e){return e};return qi.selector(e),e(n.current)},function(e){!function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){return t.reduceRight((function(e,t){return t(e)}),e)}}(r,a,s,l)(e)}]}};const Ki=Qi;const Ji={paths:{vs:"https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs"}};const Xi=function(e){return function t(){for(var n=this,r=arguments.length,a=new Array(r),s=0;s<r;s++)a[s]=arguments[s];return a.length>=e.length?e.apply(this,a):function(){for(var e=arguments.length,r=new Array(e),s=0;s<e;s++)r[s]=arguments[s];return t.apply(n,[].concat(a,r))}}};const Zi=function(e){return{}.toString.call(e).includes("Object")};var Gi={configIsRequired:"the configuration object is required",configType:"the configuration object should be an object",default:"an unknown error accured in `@monaco-editor/loader` package",deprecation:"Deprecation warning!\n    You are using deprecated way of configuration.\n\n    Instead of using\n      monaco.config({ urls: { monacoBase: '...' } })\n    use\n      monaco.config({ paths: { vs: '...' } })\n\n    For more please check the link https://github.com/suren-atoyan/monaco-loader#config\n  "},Yi=Xi((function(e,t){throw new Error(e[t]||e.default)}))(Gi),eo={config:function(e){return e||Yi("configIsRequired"),Zi(e)||Yi("configType"),e.urls?(console.warn(Gi.deprecation),{paths:{vs:e.urls.monacoBase}}):e}};const to=eo;const no=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){return t.reduceRight((function(e,t){return t(e)}),e)}};const ro=function e(t,n){return Object.keys(n).forEach((function(r){n[r]instanceof Object&&t[r]&&Object.assign(n[r],e(t[r],n[r]))})),Ti(Ti({},t),n)};var ao={type:"cancelation",msg:"operation is manually canceled"};const so=function(e){var t=!1,n=new Promise((function(n,r){e.then((function(e){return t?r(ao):n(e)})),e.catch(r)}));return n.cancel=function(){return t=!0},n};var lo=function(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"===typeof Symbol||!(Symbol.iterator in Object(e)))return;var n=[],r=!0,a=!1,s=void 0;try{for(var l,i=e[Symbol.iterator]();!(r=(l=i.next()).done)&&(n.push(l.value),!t||n.length!==t);r=!0);}catch(o){a=!0,s=o}finally{try{r||null==i.return||i.return()}finally{if(a)throw s}}return n}(e,t)||function(e,t){if(!e)return;if("string"===typeof e)return Di(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Di(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(Ki.create({config:Ji,isInitialized:!1,resolve:null,reject:null,monaco:null}),2),io=lo[0],oo=lo[1];function co(e){return document.body.appendChild(e)}function uo(e){var t=io((function(e){return{config:e.config,reject:e.reject}})),n=function(e){var t=document.createElement("script");return e&&(t.src=e),t}("".concat(t.config.paths.vs,"/loader.js"));return n.onload=function(){return e()},n.onerror=t.reject,n}function fo(){var e=io((function(e){return{config:e.config,resolve:e.resolve,reject:e.reject}})),t=window.require;t.config(e.config),t(["vs/editor/editor.main"],(function(t){mo(t),e.resolve(t)}),(function(t){e.reject(t)}))}function mo(e){io().monaco||oo({monaco:e})}var ho=new Promise((function(e,t){return oo({resolve:e,reject:t})})),po={config:function(e){var t=to.config(e),n=t.monaco,r=Ai(t,["monaco"]);oo((function(e){return{config:ro(e.config,r),monaco:n}}))},init:function(){var e=io((function(e){return{monaco:e.monaco,isInitialized:e.isInitialized,resolve:e.resolve}}));if(!e.isInitialized){if(oo({isInitialized:!0}),e.monaco)return e.resolve(e.monaco),so(ho);if(window.monaco&&window.monaco.editor)return mo(window.monaco),e.resolve(window.monaco),so(ho);no(co,uo)(fo)}return so(ho)},__getMonacoInstance:function(){return io((function(e){return e.monaco}))}};const xo=po;var go={wrapper:{display:"flex",position:"relative",textAlign:"initial"},fullWidth:{width:"100%"},hide:{display:"none"}},yo={container:{display:"flex",height:"100%",width:"100%",justifyContent:"center",alignItems:"center"}};var vo=function(e){let{children:t}=e;return a.createElement("div",{style:yo.container},t)},bo=vo;var wo=function(e){let{width:t,height:n,isEditorReady:r,loading:s,_ref:l,className:i,wrapperProps:o}=e;return a.createElement("section",lt({style:lt(lt({},go.wrapper),{},{width:t,height:n})},o),!r&&a.createElement(bo,null,s),a.createElement("div",{ref:l,style:lt(lt({},go.fullWidth),!r&&go.hide),className:i}))},jo=(0,a.memo)(wo);var No=function(e){(0,a.useEffect)(e,[])};var ko=function(e,t){let n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],r=(0,a.useRef)(!0);(0,a.useEffect)(r.current||!n?()=>{r.current=!1}:e,t)};function So(){}function Eo(e,t,n,r){return function(e,t){return e.editor.getModel(Co(e,t))}(e,r)||function(e,t,n,r){return e.editor.createModel(t,n,r?Co(e,r):void 0)}(e,t,n,r)}function Co(e,t){return e.Uri.parse(t)}var Oo=function(e){let{original:t,modified:n,language:r,originalLanguage:s,modifiedLanguage:l,originalModelPath:i,modifiedModelPath:o,keepCurrentOriginalModel:c=!1,keepCurrentModifiedModel:u=!1,theme:d="light",loading:f="Loading...",options:m={},height:h="100%",width:p="100%",className:x,wrapperProps:g={},beforeMount:y=So,onMount:v=So}=e,[b,w]=(0,a.useState)(!1),[j,N]=(0,a.useState)(!0),k=(0,a.useRef)(null),S=(0,a.useRef)(null),E=(0,a.useRef)(null),C=(0,a.useRef)(v),O=(0,a.useRef)(y),P=(0,a.useRef)(!1);No((()=>{let e=xo.init();return e.then((e=>(S.current=e)&&N(!1))).catch((e=>"cancelation"!==(null===e||void 0===e?void 0:e.type)&&console.error("Monaco initialization: error:",e))),()=>k.current?function(){var e,t,n,r;let a=null===(e=k.current)||void 0===e?void 0:e.getModel();c||null!==a&&void 0!==a&&null!==(t=a.original)&&void 0!==t&&t.dispose(),u||null!==a&&void 0!==a&&null!==(n=a.modified)&&void 0!==n&&n.dispose(),null===(r=k.current)||void 0===r||r.dispose()}():e.cancel()})),ko((()=>{if(k.current&&S.current){let e=k.current.getOriginalEditor(),n=Eo(S.current,t||"",s||r||"text",i||"");n!==e.getModel()&&e.setModel(n)}}),[i],b),ko((()=>{if(k.current&&S.current){let e=k.current.getModifiedEditor(),t=Eo(S.current,n||"",l||r||"text",o||"");t!==e.getModel()&&e.setModel(t)}}),[o],b),ko((()=>{let e=k.current.getModifiedEditor();e.getOption(S.current.editor.EditorOption.readOnly)?e.setValue(n||""):n!==e.getValue()&&(e.executeEdits("",[{range:e.getModel().getFullModelRange(),text:n||"",forceMoveMarkers:!0}]),e.pushUndoStop())}),[n],b),ko((()=>{var e;null===(e=k.current)||void 0===e||null===(e=e.getModel())||void 0===e||e.original.setValue(t||"")}),[t],b),ko((()=>{let{original:e,modified:t}=k.current.getModel();S.current.editor.setModelLanguage(e,s||r||"text"),S.current.editor.setModelLanguage(t,l||r||"text")}),[r,s,l],b),ko((()=>{var e;null===(e=S.current)||void 0===e||e.editor.setTheme(d)}),[d],b),ko((()=>{var e;null===(e=k.current)||void 0===e||e.updateOptions(m)}),[m],b);let _=(0,a.useCallback)((()=>{var e;if(!S.current)return;O.current(S.current);let a=Eo(S.current,t||"",s||r||"text",i||""),c=Eo(S.current,n||"",l||r||"text",o||"");null===(e=k.current)||void 0===e||e.setModel({original:a,modified:c})}),[r,n,l,t,s,i,o]),R=(0,a.useCallback)((()=>{var e;!P.current&&E.current&&(k.current=S.current.editor.createDiffEditor(E.current,lt({automaticLayout:!0},m)),_(),null!==(e=S.current)&&void 0!==e&&e.editor.setTheme(d),w(!0),P.current=!0)}),[m,d,_]);return(0,a.useEffect)((()=>{b&&C.current(k.current,S.current)}),[b]),(0,a.useEffect)((()=>{!j&&!b&&R()}),[j,b,R]),a.createElement(jo,{width:p,height:h,isEditorReady:b,loading:f,_ref:E,className:x,wrapperProps:g})};(0,a.memo)(Oo);var Po=function(e){let t=(0,a.useRef)();return(0,a.useEffect)((()=>{t.current=e}),[e]),t.current},_o=new Map;var Ro=function(e){let{defaultValue:t,defaultLanguage:n,defaultPath:r,value:s,language:l,path:i,theme:o="light",line:c,loading:u="Loading...",options:d={},overrideServices:f={},saveViewState:m=!0,keepCurrentModel:h=!1,width:p="100%",height:x="100%",className:g,wrapperProps:y={},beforeMount:v=So,onMount:b=So,onChange:w,onValidate:j=So}=e,[N,k]=(0,a.useState)(!1),[S,E]=(0,a.useState)(!0),C=(0,a.useRef)(null),O=(0,a.useRef)(null),P=(0,a.useRef)(null),_=(0,a.useRef)(b),R=(0,a.useRef)(v),L=(0,a.useRef)(),T=(0,a.useRef)(s),A=Po(i),D=(0,a.useRef)(!1),M=(0,a.useRef)(!1);No((()=>{let e=xo.init();return e.then((e=>(C.current=e)&&E(!1))).catch((e=>"cancelation"!==(null===e||void 0===e?void 0:e.type)&&console.error("Monaco initialization: error:",e))),()=>O.current?function(){var e,t;null!==(e=L.current)&&void 0!==e&&e.dispose(),h?m&&_o.set(i,O.current.saveViewState()):null===(t=O.current.getModel())||void 0===t||t.dispose(),O.current.dispose()}():e.cancel()})),ko((()=>{var e,a,o,c;let u=Eo(C.current,t||s||"",n||l||"",i||r||"");u!==(null===(e=O.current)||void 0===e?void 0:e.getModel())&&(m&&_o.set(A,null===(a=O.current)||void 0===a?void 0:a.saveViewState()),null!==(o=O.current)&&void 0!==o&&o.setModel(u),m&&(null===(c=O.current)||void 0===c||c.restoreViewState(_o.get(i))))}),[i],N),ko((()=>{var e;null===(e=O.current)||void 0===e||e.updateOptions(d)}),[d],N),ko((()=>{!O.current||void 0===s||(O.current.getOption(C.current.editor.EditorOption.readOnly)?O.current.setValue(s):s!==O.current.getValue()&&(M.current=!0,O.current.executeEdits("",[{range:O.current.getModel().getFullModelRange(),text:s,forceMoveMarkers:!0}]),O.current.pushUndoStop(),M.current=!1))}),[s],N),ko((()=>{var e,t;let n=null===(e=O.current)||void 0===e?void 0:e.getModel();n&&l&&(null===(t=C.current)||void 0===t||t.editor.setModelLanguage(n,l))}),[l],N),ko((()=>{var e;void 0!==c&&(null===(e=O.current)||void 0===e||e.revealLine(c))}),[c],N),ko((()=>{var e;null===(e=C.current)||void 0===e||e.editor.setTheme(o)}),[o],N);let I=(0,a.useCallback)((()=>{if(P.current&&C.current&&!D.current){var e;R.current(C.current);let a=i||r,u=Eo(C.current,s||t||"",n||l||"",a||"");O.current=null===(e=C.current)||void 0===e?void 0:e.editor.create(P.current,lt({model:u,automaticLayout:!0},d),f),m&&O.current.restoreViewState(_o.get(a)),C.current.editor.setTheme(o),void 0!==c&&O.current.revealLine(c),k(!0),D.current=!0}}),[t,n,r,s,l,i,d,f,m,o,c]);return(0,a.useEffect)((()=>{N&&_.current(O.current,C.current)}),[N]),(0,a.useEffect)((()=>{!S&&!N&&I()}),[S,N,I]),T.current=s,(0,a.useEffect)((()=>{var e,t;N&&w&&(null!==(e=L.current)&&void 0!==e&&e.dispose(),L.current=null===(t=O.current)||void 0===t?void 0:t.onDidChangeModelContent((e=>{M.current||w(O.current.getValue(),e)})))}),[N,w]),(0,a.useEffect)((()=>{if(N){let e=C.current.editor.onDidChangeMarkers((e=>{var t;let n=null===(t=O.current.getModel())||void 0===t?void 0:t.uri;if(n&&e.find((e=>e.path===n.path))){let e=C.current.editor.getModelMarkers({resource:n});null===j||void 0===j||j(e)}}));return()=>{null===e||void 0===e||e.dispose()}}return()=>{}}),[N,j]),a.createElement(jo,{width:p,height:x,isEditorReady:N,loading:u,_ref:P,className:g,wrapperProps:y})},Lo=(0,a.memo)(Ro);const To=()=>{const e=R(),{configs:t,nodes:n,selectedConfig:r,loading:s,error:l}=j((e=>e.envoy)),[i,o]=(0,a.useState)(!1),[c,u]=(0,a.useState)({node_id:"",cluster_name:"",config:"",version:"1.0.0"});(0,a.useEffect)((()=>{e(yi()),e(vi())}),[e]);return(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsxs)("div",{className:"flex items-center justify-between",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h1",{className:"text-2xl font-bold text-white",children:"Envoy Configuration"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Manage Envoy proxy configurations and deployments"})]}),(0,it.jsxs)("button",{onClick:()=>o(!0),className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500",children:[(0,it.jsx)(Ci,{className:"h-4 w-4 mr-2"}),"New Config"]})]}),(0,it.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white mb-4",children:"Configurations"}),s?(0,it.jsx)("div",{className:"flex items-center justify-center h-32",children:(0,it.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500"})}):(0,it.jsxs)("div",{className:"space-y-3",children:[t.map((t=>(0,it.jsx)("div",{className:"border rounded-lg p-4 cursor-pointer transition-colors ".concat((null===r||void 0===r?void 0:r.id)===t.id?"border-cyan-500 bg-cyan-500/10":"border-gray-600 hover:bg-gray-700"),onClick:()=>e(Ni(t)),children:(0,it.jsxs)("div",{className:"flex items-start justify-between",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h4",{className:"text-lg font-medium text-white",children:t.cluster_name}),(0,it.jsxs)("p",{className:"mt-1 text-sm text-gray-400",children:["Node: ",t.node_id]}),(0,it.jsxs)("p",{className:"mt-1 text-xs text-gray-500",children:["Version: ",t.version]})]}),(0,it.jsx)(_i,{className:"h-6 w-6 text-gray-400"})]})},t.id))),0===t.length&&(0,it.jsxs)("div",{className:"text-center py-8",children:[(0,it.jsx)(Pt,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,it.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-300",children:"No configurations"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Create your first Envoy configuration."})]})]})]})}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white mb-4",children:"Configuration Editor"}),r?(0,it.jsxs)("div",{className:"space-y-4",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Cluster Name"}),(0,it.jsx)("input",{type:"text",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:r.cluster_name,readOnly:!0})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Node ID"}),(0,it.jsx)("input",{type:"text",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:r.node_id,readOnly:!0})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Configuration (YAML)"}),(0,it.jsx)("div",{className:"mt-1 border border-gray-600 rounded-md overflow-hidden",children:(0,it.jsx)(Lo,{height:"300px",defaultLanguage:"yaml",value:r.config,theme:"vs-dark",options:{readOnly:!0,minimap:{enabled:!1},scrollBeyondLastLine:!1,fontSize:14,fontFamily:"JetBrains Mono, monospace",lineNumbers:"on",folding:!0,wordWrap:"on"}})})]})]}):(0,it.jsxs)("div",{className:"text-center py-8",children:[(0,it.jsx)(_i,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,it.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-300",children:"Select a configuration"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Choose a configuration from the list to view and edit it."})]})]})})]}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white mb-4",children:"Connected Envoy Nodes"}),0===n.length?(0,it.jsxs)("div",{className:"text-center py-8",children:[(0,it.jsx)(Pt,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,it.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-300",children:"No nodes connected"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Envoy nodes will appear here when they connect to the control plane."})]}):(0,it.jsx)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3",children:n.map((e=>(0,it.jsxs)("div",{className:"border border-gray-600 rounded-lg p-4",children:[(0,it.jsxs)("div",{className:"flex items-center justify-between",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h4",{className:"text-lg font-medium text-white",children:e.id}),(0,it.jsxs)("p",{className:"text-sm text-gray-400",children:["Cluster: ",e.cluster]}),(0,it.jsxs)("p",{className:"text-xs text-gray-500",children:["Version: ",e.version]})]}),(0,it.jsx)("div",{className:"w-3 h-3 rounded-full ".concat("connected"===e.status?"bg-green-400":"bg-red-400")})]}),(0,it.jsxs)("p",{className:"mt-2 text-xs text-gray-500",children:["Last seen: ",new Date(e.last_seen).toLocaleString()]})]},e.id)))})]})}),i&&(0,it.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,it.jsx)("div",{className:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-gray-800",children:(0,it.jsxs)("div",{className:"mt-3",children:[(0,it.jsx)("h3",{className:"text-lg font-medium text-white mb-4",children:"Create New Configuration"}),(0,it.jsxs)("div",{className:"space-y-4",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Node ID"}),(0,it.jsx)("input",{type:"text",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:c.node_id,onChange:e=>u(lt(lt({},c),{},{node_id:e.target.value}))})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Cluster Name"}),(0,it.jsx)("input",{type:"text",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:c.cluster_name,onChange:e=>u(lt(lt({},c),{},{cluster_name:e.target.value}))})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Configuration"}),(0,it.jsx)("div",{className:"mt-1 border border-gray-600 rounded-md overflow-hidden",children:(0,it.jsx)(Lo,{height:"200px",defaultLanguage:"yaml",value:c.config,onChange:e=>u(lt(lt({},c),{},{config:e||""})),theme:"vs-dark",options:{minimap:{enabled:!1},scrollBeyondLastLine:!1,fontSize:14,fontFamily:"JetBrains Mono, monospace",lineNumbers:"on",folding:!0,wordWrap:"on"}})})]})]}),(0,it.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,it.jsx)("button",{onClick:()=>o(!1),className:"px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700",children:"Cancel"}),(0,it.jsx)("button",{onClick:()=>{e(bi(c)),u({node_id:"",cluster_name:"",config:"",version:"1.0.0"}),o(!1)},className:"px-4 py-2 bg-cyan-600 text-white rounded-md hover:bg-cyan-700",children:"Create"})]})]})})})]})},Ao=fa("autoscaler/fetchStatus",(async()=>(await Bl.get("/v1/autoscaler/status")).data)),Do=fa("autoscaler/updateConfig",(async e=>(await Bl.put("/v1/autoscaler/config",e)).data)),Mo=fa("autoscaler/fetchEvents",(async()=>(await Bl.get("/v1/autoscaler/events")).data.events)),Io=fa("autoscaler/fetchMetrics",(async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"1h";return(await Bl.get("/v1/autoscaler/metrics?range=".concat(e))).data})),Fo=la({name:"autoscaler",initialState:{status:null,events:[],loading:!1,error:null,metrics:{cpu_usage:[],memory_usage:[],replica_count:[]}},reducers:{clearEvents:e=>{e.events=[]},addEvent:(e,t)=>{e.events.unshift(t.payload),e.events.length>100&&(e.events=e.events.slice(0,100))}},extraReducers:e=>{e.addCase(Ao.pending,(e=>{e.loading=!0,e.error=null})).addCase(Ao.fulfilled,((e,t)=>{e.loading=!1,e.status=t.payload})).addCase(Ao.rejected,((e,t)=>{e.loading=!1,e.error=t.error.message||"Failed to fetch autoscaler status"})).addCase(Do.pending,(e=>{e.loading=!0,e.error=null})).addCase(Do.fulfilled,((e,t)=>{e.loading=!1,e.status=lt(lt({},e.status),t.payload)})).addCase(Do.rejected,((e,t)=>{e.loading=!1,e.error=t.error.message||"Failed to update autoscaler config"})).addCase(Mo.fulfilled,((e,t)=>{e.events=t.payload})).addCase(Io.fulfilled,((e,t)=>{e.metrics=t.payload}))}}),{clearEvents:zo,addEvent:Uo}=Fo.actions,Bo=Fo.reducer,Wo=["title","titleId"];function qo(e,t){let{title:n,titleId:r}=e,s=mt(e,Wo);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-15 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a2.25 2.25 0 0 0 2.25-2.25V6.75a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 6.75v10.5a2.25 2.25 0 0 0 2.25 2.25Zm.75-12h9v9h-9v-9Z"}))}const Vo=a.forwardRef(qo),$o=["title","titleId"];function Ho(e,t){let{title:n,titleId:r}=e,s=mt(e,$o);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 6 9 12.75l4.286-4.286a11.948 11.948 0 0 1 4.306 6.43l.776 2.898m0 0 3.182-5.511m-3.182 5.51-5.511-3.181"}))}const Qo=a.forwardRef(Ho),Ko=()=>{const e=R(),{status:t,events:n,loading:r,error:s,metrics:l}=j((e=>e.autoscaler));(0,a.useEffect)((()=>{e(Ao()),e(Io("1h"))}),[e]);return(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h1",{className:"text-2xl font-bold text-white",children:"Autoscaler Dashboard"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Monitor and configure automatic scaling for your applications"})]}),(0,it.jsxs)("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4",children:[(0,it.jsx)("div",{className:"bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,it.jsx)("div",{className:"p-5",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(vn,{className:"h-6 w-6 text-blue-400"})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400 truncate",children:"Current Replicas"}),(0,it.jsx)("dd",{className:"text-lg font-medium text-white",children:(null===t||void 0===t?void 0:t.current_replicas)||0})]})})]})})}),(0,it.jsx)("div",{className:"bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,it.jsx)("div",{className:"p-5",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(Lt,{className:"h-6 w-6 text-green-400"})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400 truncate",children:"Desired Replicas"}),(0,it.jsx)("dd",{className:"text-lg font-medium text-white",children:(null===t||void 0===t?void 0:t.desired_replicas)||0})]})})]})})}),(0,it.jsx)("div",{className:"bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,it.jsx)("div",{className:"p-5",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(Vo,{className:"h-6 w-6 text-yellow-400"})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400 truncate",children:"CPU Utilization"}),(0,it.jsxs)("dd",{className:"text-lg font-medium text-white",children:[(null===t||void 0===t?void 0:t.current_cpu_utilization)||0,"%"]})]})})]})})}),(0,it.jsx)("div",{className:"bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,it.jsx)("div",{className:"p-5",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(null!==t&&void 0!==t&&t.enabled?"bg-green-400":"bg-red-400")})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400 truncate",children:"Status"}),(0,it.jsx)("dd",{className:"text-lg font-medium text-white",children:null!==t&&void 0!==t&&t.enabled?"Enabled":"Disabled"})]})})]})})})]}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white mb-4",children:"Autoscaler Configuration"}),t&&(0,it.jsxs)("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Min Replicas"}),(0,it.jsx)("div",{className:"mt-1",children:(0,it.jsx)("input",{type:"number",className:"block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:t.min_replicas,readOnly:!0})})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Max Replicas"}),(0,it.jsx)("div",{className:"mt-1",children:(0,it.jsx)("input",{type:"number",className:"block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:t.max_replicas,readOnly:!0})})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Target CPU Utilization (%)"}),(0,it.jsx)("div",{className:"mt-1",children:(0,it.jsx)("input",{type:"number",className:"block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:t.target_cpu_utilization,readOnly:!0})})]}),(0,it.jsx)("div",{className:"flex items-end",children:(0,it.jsxs)("button",{onClick:()=>{t&&e(Do({enabled:!t.enabled}))},disabled:r,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 ".concat(t.enabled?"bg-red-600 hover:bg-red-700 focus:ring-red-500":"bg-green-600 hover:bg-green-700 focus:ring-green-500"),children:[t.enabled?"Disable":"Enable"," Autoscaler"]})})]}),s&&(0,it.jsx)("div",{className:"mt-4 bg-red-50 border border-red-200 rounded-md p-4",children:(0,it.jsx)("div",{className:"text-sm text-red-700",children:s})})]})}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white mb-4",children:"Recent Scaling Events"}),0===n.length?(0,it.jsxs)("div",{className:"text-center py-8",children:[(0,it.jsx)(Lt,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,it.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-300",children:"No scaling events"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Scaling events will appear here when they occur."})]}):(0,it.jsx)("div",{className:"space-y-4",children:n.slice(0,10).map(((e,t)=>(0,it.jsx)("div",{className:"border border-gray-600 rounded-lg p-4",children:(0,it.jsxs)("div",{className:"flex items-start justify-between",children:[(0,it.jsxs)("div",{className:"flex items-center space-x-3",children:[e.to_replicas>e.from_replicas?(0,it.jsx)(Lt,{className:"h-5 w-5 text-green-400"}):(0,it.jsx)(Qo,{className:"h-5 w-5 text-red-400"}),(0,it.jsxs)("div",{children:[(0,it.jsxs)("p",{className:"text-sm font-medium text-white",children:["Scaled from ",e.from_replicas," to ",e.to_replicas," replicas"]}),(0,it.jsx)("p",{className:"text-sm text-gray-400",children:e.reason}),(0,it.jsx)("p",{className:"text-xs text-gray-500",children:e.message})]})]}),(0,it.jsx)("span",{className:"text-xs text-gray-400",children:new Date(e.timestamp).toLocaleString()})]})},t)))})]})})]})},Jo=fa("audit/fetchEvents",(async e=>(await Bl.post("/v1/audit/query",e)).data)),Xo=la({name:"audit",initialState:{events:[],loading:!1,error:null,totalCount:0,query:{}},reducers:{setQuery:(e,t)=>{e.query=t.payload},clearEvents:e=>{e.events=[],e.totalCount=0}},extraReducers:e=>{e.addCase(Jo.pending,(e=>{e.loading=!0,e.error=null})).addCase(Jo.fulfilled,((e,t)=>{e.loading=!1,e.events=t.payload.events,e.totalCount=t.payload.total_count})).addCase(Jo.rejected,((e,t)=>{e.loading=!1,e.error=t.error.message||"Failed to fetch audit events"}))}}),{setQuery:Zo,clearEvents:Go}=Xo.actions,Yo=Xo.reducer,ec=()=>{const e=R(),{events:t,loading:n,error:r,totalCount:s,query:l}=j((e=>e.audit)),[i,o]=(0,a.useState)({user_id:"",action:"",resource:"",start_time:"",end_time:""});(0,a.useEffect)((()=>{e(Jo({limit:50}))}),[e]);return(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h1",{className:"text-2xl font-bold text-white",children:"Audit Trail"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Tamper-proof audit logs powered by ImmuDB"})]}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white mb-4",children:"Filters"}),(0,it.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-3",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"User ID"}),(0,it.jsx)("input",{type:"text",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:i.user_id,onChange:e=>o(lt(lt({},i),{},{user_id:e.target.value})),placeholder:"Filter by user..."})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Action"}),(0,it.jsx)("input",{type:"text",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:i.action,onChange:e=>o(lt(lt({},i),{},{action:e.target.value})),placeholder:"Filter by action..."})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Resource"}),(0,it.jsx)("input",{type:"text",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:i.resource,onChange:e=>o(lt(lt({},i),{},{resource:e.target.value})),placeholder:"Filter by resource..."})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Start Date"}),(0,it.jsx)("input",{type:"datetime-local",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:i.start_time,onChange:e=>o(lt(lt({},i),{},{start_time:e.target.value}))})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"End Date"}),(0,it.jsx)("input",{type:"datetime-local",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:i.end_time,onChange:e=>o(lt(lt({},i),{},{end_time:e.target.value}))})]}),(0,it.jsx)("div",{className:"flex items-end",children:(0,it.jsxs)("button",{onClick:()=>{const t=lt(lt({},i),{},{limit:50});e(Zo(t)),e(Jo(t))},disabled:n,className:"w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 disabled:opacity-50",children:[(0,it.jsx)(xt,{className:"h-4 w-4 mr-2"}),"Search"]})})]})]})}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white",children:"Audit Events"}),(0,it.jsxs)("span",{className:"text-sm text-gray-400",children:[s," events found"]})]}),r&&(0,it.jsx)("div",{className:"mb-4 bg-red-50 border border-red-200 rounded-md p-4",children:(0,it.jsx)("div",{className:"text-sm text-red-700",children:r})}),n?(0,it.jsx)("div",{className:"flex items-center justify-center h-32",children:(0,it.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500"})}):(0,it.jsx)("div",{className:"space-y-4",children:0===t.length?(0,it.jsxs)("div",{className:"text-center py-12",children:[(0,it.jsx)(Dt,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,it.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-300",children:"No audit events found"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Try adjusting your search criteria."})]}):t.map((e=>{return(0,it.jsx)("div",{className:"border border-gray-600 rounded-lg p-4",children:(0,it.jsxs)("div",{className:"flex items-start justify-between",children:[(0,it.jsxs)("div",{className:"flex-1",children:[(0,it.jsxs)("div",{className:"flex items-center space-x-2",children:[e.success?(0,it.jsx)(mn,{className:"h-5 w-5 text-green-400"}):(0,it.jsx)(xi,{className:"h-5 w-5 text-red-400"}),(0,it.jsx)("span",{className:"font-medium ".concat((t=e.action,t.includes("create")||t.includes("add")?"text-green-400":t.includes("delete")||t.includes("remove")?"text-red-400":t.includes("update")||t.includes("modify")?"text-yellow-400":"text-blue-400")),children:e.action}),(0,it.jsx)("span",{className:"text-gray-400",children:"on"}),(0,it.jsx)("span",{className:"text-white",children:e.resource})]}),(0,it.jsxs)("div",{className:"mt-2 text-sm text-gray-400",children:[(0,it.jsxs)("p",{children:["User: ",e.user_id]}),(0,it.jsxs)("p",{children:["Resource ID: ",e.resource_id]}),(0,it.jsxs)("p",{children:["IP: ",e.ip_address]}),e.error_msg&&(0,it.jsxs)("p",{className:"text-red-400",children:["Error: ",e.error_msg]})]}),Object.keys(e.details).length>0&&(0,it.jsx)("div",{className:"mt-3",children:(0,it.jsxs)("details",{className:"group",children:[(0,it.jsx)("summary",{className:"cursor-pointer text-sm text-cyan-400 hover:text-cyan-300",children:"View Details"}),(0,it.jsx)("pre",{className:"mt-2 text-xs text-gray-400 bg-gray-700 p-2 rounded overflow-x-auto",children:JSON.stringify(e.details,null,2)})]})})]}),(0,it.jsxs)("div",{className:"text-right text-sm text-gray-400",children:[(0,it.jsx)("p",{children:new Date(e.timestamp).toLocaleDateString()}),(0,it.jsx)("p",{children:new Date(e.timestamp).toLocaleTimeString()})]})]})},e.id);var t}))})]})})]})},tc=fa("database/fetchStats",(async()=>(await Bl.get("/v1/database/stats")).data)),nc=fa("database/executeQuery",(async e=>(await Bl.post("/v1/database/query",{query:e})).data)),rc=la({name:"database",initialState:{stats:null,loading:!1,error:null,queryResult:null,queryLoading:!1},reducers:{clearQueryResult:e=>{e.queryResult=null}},extraReducers:e=>{e.addCase(tc.pending,(e=>{e.loading=!0,e.error=null})).addCase(tc.fulfilled,((e,t)=>{e.loading=!1,e.stats=t.payload})).addCase(tc.rejected,((e,t)=>{e.loading=!1,e.error=t.error.message||"Failed to fetch database stats"})).addCase(nc.pending,(e=>{e.queryLoading=!0,e.error=null})).addCase(nc.fulfilled,((e,t)=>{e.queryLoading=!1,e.queryResult=t.payload})).addCase(nc.rejected,((e,t)=>{e.queryLoading=!1,e.error=t.error.message||"Query execution failed"}))}}),{clearQueryResult:ac}=rc.actions,sc=rc.reducer,lc=["title","titleId"];function ic(e,t){let{title:n,titleId:r}=e,s=mt(e,lc);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))}const oc=a.forwardRef(ic),cc=()=>{var e,t,n;const r=R(),{stats:s,loading:l,error:i,queryResult:o,queryLoading:c}=j((e=>e.database)),[u,d]=(0,a.useState)("");(0,a.useEffect)((()=>{r(tc())}),[r]);return(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h1",{className:"text-2xl font-bold text-white",children:"Database Administration"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Monitor database health and execute queries"})]}),(0,it.jsxs)("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4",children:[(0,it.jsx)("div",{className:"bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,it.jsx)("div",{className:"p-5",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(Ft,{className:"h-6 w-6 text-blue-400"})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400 truncate",children:"Total Resources"}),(0,it.jsx)("dd",{className:"text-lg font-medium text-white",children:(null===s||void 0===s||null===(e=s.total_resources)||void 0===e?void 0:e.toLocaleString())||0})]})})]})})}),(0,it.jsx)("div",{className:"bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,it.jsx)("div",{className:"p-5",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(Ft,{className:"h-6 w-6 text-green-400"})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400 truncate",children:"Workflows"}),(0,it.jsx)("dd",{className:"text-lg font-medium text-white",children:(null===s||void 0===s||null===(t=s.total_workflows)||void 0===t?void 0:t.toLocaleString())||0})]})})]})})}),(0,it.jsx)("div",{className:"bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,it.jsx)("div",{className:"p-5",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(Ft,{className:"h-6 w-6 text-purple-400"})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400 truncate",children:"Envoy Configs"}),(0,it.jsx)("dd",{className:"text-lg font-medium text-white",children:(null===s||void 0===s||null===(n=s.total_envoy_configs)||void 0===n?void 0:n.toLocaleString())||0})]})})]})})}),(0,it.jsx)("div",{className:"bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,it.jsx)("div",{className:"p-5",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(Ft,{className:"h-6 w-6 text-yellow-400"})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400 truncate",children:"Cache Hit Rate"}),(0,it.jsx)("dd",{className:"text-lg font-medium text-white",children:null!==s&&void 0!==s&&s.cache_hit_rate?"".concat(s.cache_hit_rate,"%"):"N/A"})]})})]})})})]}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white mb-4",children:"Database Information"}),l?(0,it.jsx)("div",{className:"flex items-center justify-center h-32",children:(0,it.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500"})}):(0,it.jsxs)("dl",{className:"grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400",children:"Database Size"}),(0,it.jsx)("dd",{className:"mt-1 text-sm text-white",children:(null===s||void 0===s?void 0:s.database_size)||"Unknown"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400",children:"Cache Size"}),(0,it.jsx)("dd",{className:"mt-1 text-sm text-white",children:(null===s||void 0===s?void 0:s.cache_size)||"Unknown"})]})]})]})}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white mb-4",children:"SQL Query Interface"}),(0,it.jsxs)("div",{className:"space-y-4",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"SQL Query"}),(0,it.jsx)("textarea",{className:"block w-full h-32 px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white font-mono text-sm focus:outline-none focus:ring-2 focus:ring-cyan-500",value:u,onChange:e=>d(e.target.value),placeholder:"Enter your SQL query here..."})]}),(0,it.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,it.jsxs)("button",{onClick:()=>{u.trim()&&r(nc(u))},disabled:c||!u.trim(),className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 disabled:opacity-50",children:[(0,it.jsx)(ln,{className:"h-4 w-4 mr-2"}),c?"Executing...":"Execute Query"]}),o&&(0,it.jsxs)("button",{onClick:()=>{r(ac())},className:"inline-flex items-center px-4 py-2 border border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-300 bg-gray-700 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500",children:[(0,it.jsx)(oc,{className:"h-4 w-4 mr-2"}),"Clear Result"]})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Sample Queries"}),(0,it.jsx)("div",{className:"space-y-2",children:["SELECT * FROM resource_cache LIMIT 10","SELECT provider, COUNT(*) as count FROM resource_cache GROUP BY provider",'SELECT * FROM workflow_executions WHERE status = "running"',"SELECT * FROM envoy_configs ORDER BY updated_at DESC LIMIT 5"].map(((e,t)=>(0,it.jsx)("button",{onClick:()=>d(e),className:"block w-full text-left px-3 py-2 text-sm text-gray-300 bg-gray-700 hover:bg-gray-600 rounded border border-gray-600",children:e},t)))})]})]}),i&&(0,it.jsx)("div",{className:"mt-4 bg-red-50 border border-red-200 rounded-md p-4",children:(0,it.jsx)("div",{className:"text-sm text-red-700",children:i})}),o&&(0,it.jsxs)("div",{className:"mt-6",children:[(0,it.jsx)("h4",{className:"text-md font-medium text-white mb-3",children:"Query Results"}),(0,it.jsx)("div",{className:"bg-gray-700 rounded-md p-4 overflow-x-auto",children:(0,it.jsx)("pre",{className:"text-sm text-gray-300",children:JSON.stringify(o,null,2)})})]})]})})]})},uc=()=>{const{user:e}=ut(),[t,n]=(0,a.useState)(null),[r,s]=(0,a.useState)([]),[l,i]=(0,a.useState)(!0);(0,a.useEffect)((()=>{o()}),[]);const o=async()=>{try{const e=await fetch("/v1/admin/stats",{credentials:"include"});if(e.ok){const t=await e.json();n(t)}const t=await fetch("/v1/admin/activity",{credentials:"include"});if(t.ok){const e=await t.json();s(e.activities||[])}}catch(e){console.error("Failed to fetch admin data:",e)}finally{i(!1)}},c=e=>{switch(e){case"user_login":return(0,it.jsx)(Bt,{className:"h-5 w-5 text-blue-500"});case"user_created":return(0,it.jsx)(Bt,{className:"h-5 w-5 text-green-500"});case"connection_added":return(0,it.jsx)(vn,{className:"h-5 w-5 text-purple-500"});case"workflow_executed":return(0,it.jsx)(Et,{className:"h-5 w-5 text-orange-500"});default:return(0,it.jsx)(jn,{className:"h-5 w-5 text-gray-500"})}},u=e=>{switch(e){case"user_login":return"bg-blue-50 border-blue-200";case"user_created":return"bg-green-50 border-green-200";case"connection_added":return"bg-purple-50 border-purple-200";case"workflow_executed":return"bg-orange-50 border-orange-200";default:return"bg-gray-50 border-gray-200"}};return l?(0,it.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,it.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,it.jsxs)("div",{className:"flex items-center justify-between",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Admin Dashboard"}),(0,it.jsxs)("p",{className:"text-gray-600",children:["Welcome back, ",null===e||void 0===e?void 0:e.name]})]}),(0,it.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,it.jsx)(Dt,{className:"h-8 w-8 text-green-500"}),(0,it.jsx)("span",{className:"text-sm font-medium text-green-600",children:"System Healthy"})]})]})}),(0,it.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,it.jsxs)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:[(0,it.jsx)("div",{className:"p-5",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(Bt,{className:"h-6 w-6 text-gray-400"})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Total Users"}),(0,it.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:(null===t||void 0===t?void 0:t.totalUsers)||0})]})})]})}),(0,it.jsx)("div",{className:"bg-gray-50 px-5 py-3",children:(0,it.jsxs)("div",{className:"text-sm",children:[(0,it.jsx)("span",{className:"text-green-600 font-medium",children:(null===t||void 0===t?void 0:t.activeUsers)||0}),(0,it.jsx)("span",{className:"text-gray-500",children:" active"})]})})]}),(0,it.jsxs)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:[(0,it.jsx)("div",{className:"p-5",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(vn,{className:"h-6 w-6 text-gray-400"})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Connections"}),(0,it.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:(null===t||void 0===t?void 0:t.totalConnections)||0})]})})]})}),(0,it.jsx)("div",{className:"bg-gray-50 px-5 py-3",children:(0,it.jsxs)("div",{className:"text-sm",children:[(0,it.jsx)("span",{className:"text-green-600 font-medium",children:(null===t||void 0===t?void 0:t.activeConnections)||0}),(0,it.jsx)("span",{className:"text-gray-500",children:" active"})]})})]}),(0,it.jsxs)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:[(0,it.jsx)("div",{className:"p-5",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(Et,{className:"h-6 w-6 text-gray-400"})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Workflows"}),(0,it.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:(null===t||void 0===t?void 0:t.totalWorkflows)||0})]})})]})}),(0,it.jsx)("div",{className:"bg-gray-50 px-5 py-3",children:(0,it.jsxs)("div",{className:"text-sm",children:[(0,it.jsx)("span",{className:"text-blue-600 font-medium",children:(null===t||void 0===t?void 0:t.runningWorkflows)||0}),(0,it.jsx)("span",{className:"text-gray-500",children:" running"})]})})]}),(0,it.jsxs)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:[(0,it.jsx)("div",{className:"p-5",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(Nt,{className:"h-6 w-6 text-gray-400"})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"System Uptime"}),(0,it.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:(null===t||void 0===t?void 0:t.systemUptime)||"N/A"})]})})]})}),(0,it.jsx)("div",{className:"bg-gray-50 px-5 py-3",children:(0,it.jsx)("div",{className:"text-sm",children:(0,it.jsxs)("span",{className:"text-gray-500",children:["Last backup: ",(null===t||void 0===t?void 0:t.lastBackup)||"N/A"]})})})]})]}),(0,it.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,it.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,it.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Recent Activity"})}),(0,it.jsx)("div",{className:"divide-y divide-gray-200",children:r.length>0?r.map((e=>(0,it.jsx)("div",{className:"px-6 py-4",children:(0,it.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,it.jsx)("div",{className:"flex-shrink-0 p-2 rounded-full border ".concat(u(e.type)),children:c(e.type)}),(0,it.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,it.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.description}),(0,it.jsxs)("p",{className:"text-sm text-gray-500",children:["by ",e.user]})]}),(0,it.jsx)("div",{className:"flex-shrink-0 text-sm text-gray-500",children:e.timestamp})]})},e.id))):(0,it.jsxs)("div",{className:"px-6 py-8 text-center",children:[(0,it.jsx)(jn,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,it.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No recent activity"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Activity will appear here as users interact with the system."})]})})]}),(0,it.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,it.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Quick Actions"}),(0,it.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,it.jsxs)("a",{href:"/admin/users",className:"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors",children:[(0,it.jsx)(Bt,{className:"h-5 w-5 mr-2"}),"Manage Users"]}),(0,it.jsxs)("a",{href:"/admin/rbac",className:"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors",children:[(0,it.jsx)(Dt,{className:"h-5 w-5 mr-2"}),"RBAC Management"]}),(0,it.jsxs)("a",{href:"/admin/featureflags",className:"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors",children:[(0,it.jsx)(Vt,{className:"h-5 w-5 mr-2"}),"Feature Flags"]}),(0,it.jsxs)("a",{href:"/admin/settings",className:"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors",children:[(0,it.jsx)(Et,{className:"h-5 w-5 mr-2"}),"System Settings"]})]})]}),(0,it.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,it.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,it.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"System Health"})}),(0,it.jsx)("div",{className:"p-6",children:(0,it.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(mn,{className:"h-8 w-8 text-green-500"})}),(0,it.jsxs)("div",{className:"ml-3",children:[(0,it.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Database"}),(0,it.jsx)("p",{className:"text-sm text-green-600",children:"Healthy"})]})]}),(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(mn,{className:"h-8 w-8 text-green-500"})}),(0,it.jsxs)("div",{className:"ml-3",children:[(0,it.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Authentication"}),(0,it.jsx)("p",{className:"text-sm text-green-600",children:"Operational"})]})]}),(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(mn,{className:"h-8 w-8 text-green-500"})}),(0,it.jsxs)("div",{className:"ml-3",children:[(0,it.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"API Services"}),(0,it.jsx)("p",{className:"text-sm text-green-600",children:"All Systems Go"})]})]})]})})]})]})},dc=["title","titleId"];function fc(e,t){let{title:n,titleId:r}=e,s=mt(e,dc);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"}))}const mc=a.forwardRef(fc),hc=()=>{const{user:e}=ut(),[t,n]=(0,a.useState)([]),[r,s]=(0,a.useState)(!0),[l,i]=(0,a.useState)(""),[o,c]=(0,a.useState)(!1),[u,d]=(0,a.useState)(!1),[f,m]=(0,a.useState)(!1),[h,p]=(0,a.useState)(null),[x,g]=(0,a.useState)({username:"",email:"",name:"",password:"",roles:["viewer"],groups:["cainuro-users"]}),[y,v]=(0,a.useState)({email:"",name:"",roles:["viewer"],groups:["cainuro-users"],active:!0,password:""});(0,a.useEffect)((()=>{b()}),[]);const b=async()=>{try{const e=await fetch("/v1/users",{credentials:"include"});if(e.ok){const t=await e.json();n(t.users||[])}}catch(e){console.error("Failed to fetch users:",e)}finally{s(!1)}},w=t.filter((e=>e.username.toLowerCase().includes(l.toLowerCase())||e.email.toLowerCase().includes(l.toLowerCase())||e.name.toLowerCase().includes(l.toLowerCase()))),j=e=>{switch(e){case"admin":return"bg-red-100 text-red-800";case"operator":return"bg-blue-100 text-blue-800";case"viewer":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}};return r?(0,it.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,it.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,it.jsxs)("div",{className:"flex items-center justify-between",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"User Management"}),(0,it.jsx)("p",{className:"text-gray-600",children:"Manage system users and their permissions"})]}),(0,it.jsxs)("button",{onClick:()=>c(!0),className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700",children:[(0,it.jsx)(Ci,{className:"h-4 w-4 mr-2"}),"Create User"]})]})}),(0,it.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,it.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,it.jsxs)("div",{className:"flex-1 relative",children:[(0,it.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,it.jsx)(xt,{className:"h-5 w-5 text-gray-400"})}),(0,it.jsx)("input",{type:"text",placeholder:"Search users...",value:l,onChange:e=>i(e.target.value),className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"})]}),(0,it.jsxs)("div",{className:"text-sm text-gray-500",children:[w.length," of ",t.length," users"]})]})}),(0,it.jsxs)("div",{className:"bg-white shadow rounded-lg overflow-hidden",children:[(0,it.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,it.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Users"})}),(0,it.jsx)("div",{className:"overflow-x-auto",children:(0,it.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,it.jsx)("thead",{className:"bg-gray-50",children:(0,it.jsxs)("tr",{children:[(0,it.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,it.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Roles"}),(0,it.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,it.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Last Login"}),(0,it.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,it.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:w.map((t=>(0,it.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,it.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0 h-10 w-10",children:(0,it.jsx)("div",{className:"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center",children:(0,it.jsx)("span",{className:"text-sm font-medium text-gray-700",children:t.name.split(" ").map((e=>e[0])).join("").toUpperCase()})})}),(0,it.jsxs)("div",{className:"ml-4",children:[(0,it.jsx)("div",{className:"text-sm font-medium text-gray-900",children:t.name}),(0,it.jsx)("div",{className:"text-sm text-gray-500",children:t.email}),(0,it.jsxs)("div",{className:"text-xs text-gray-400",children:["@",t.username]})]})]})}),(0,it.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,it.jsx)("div",{className:"flex flex-wrap gap-1",children:t.roles.map((e=>(0,it.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(j(e)),children:e},e)))})}),(0,it.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,it.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(t.active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:t.active?(0,it.jsxs)(it.Fragment,{children:[(0,it.jsx)(mn,{className:"h-3 w-3 mr-1"}),"Active"]}):(0,it.jsxs)(it.Fragment,{children:[(0,it.jsx)(xi,{className:"h-3 w-3 mr-1"}),"Inactive"]})})}),(0,it.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:t.last_login?new Date(t.last_login).toLocaleDateString():"Never"}),(0,it.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,it.jsxs)("div",{className:"flex space-x-2",children:[(0,it.jsx)("button",{onClick:()=>p(t),className:"text-blue-600 hover:text-blue-900",title:"View User",children:(0,it.jsx)(un,{className:"h-4 w-4"})}),(0,it.jsx)("button",{onClick:()=>(e=>{p(e),v({email:e.email,name:e.name,roles:e.roles,groups:e.groups,active:e.active||!0,password:""}),d(!0)})(t),className:"text-yellow-600 hover:text-yellow-900",title:"Edit User",children:(0,it.jsx)(mc,{className:"h-4 w-4"})}),t.username!==(null===e||void 0===e?void 0:e.username)&&t.id!==(null===e||void 0===e?void 0:e.id)&&(0,it.jsx)("button",{onClick:()=>(e=>{p(e),m(!0)})(t),className:"text-red-600 hover:text-red-900",title:"Delete User",children:(0,it.jsx)(oc,{className:"h-4 w-4"})})]})})]},t.id)))})]})})]}),o&&(0,it.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,it.jsx)("div",{className:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white",children:(0,it.jsxs)("div",{className:"mt-3",children:[(0,it.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Create New User"}),(0,it.jsxs)("form",{onSubmit:async e=>{e.preventDefault();try{(await fetch("/v1/users",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(x)})).ok&&(c(!1),g({username:"",email:"",name:"",password:"",roles:["viewer"],groups:["cainuro-users"]}),b())}catch(t){console.error("Failed to create user:",t)}},className:"space-y-4",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Username"}),(0,it.jsx)("input",{type:"text",required:!0,value:x.username,onChange:e=>g(lt(lt({},x),{},{username:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Email"}),(0,it.jsx)("input",{type:"email",required:!0,value:x.email,onChange:e=>g(lt(lt({},x),{},{email:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Full Name"}),(0,it.jsx)("input",{type:"text",required:!0,value:x.name,onChange:e=>g(lt(lt({},x),{},{name:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,it.jsx)("input",{type:"password",required:!0,value:x.password,onChange:e=>g(lt(lt({},x),{},{password:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,it.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,it.jsx)("button",{type:"button",onClick:()=>c(!1),className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Cancel"}),(0,it.jsx)("button",{type:"submit",className:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700",children:"Create User"})]})]})]})})}),u&&h&&(0,it.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,it.jsx)("div",{className:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white",children:(0,it.jsxs)("div",{className:"mt-3",children:[(0,it.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:["Edit User: ",h.name]}),(0,it.jsxs)("form",{onSubmit:async e=>{if(e.preventDefault(),h)try{const e={email:y.email,name:y.name,roles:y.roles,groups:y.groups,active:y.active};y.password&&(e.password=y.password);(await fetch("/v1/users/".concat(h.id),{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(e)})).ok&&(d(!1),p(null),b())}catch(t){console.error("Failed to update user:",t)}},className:"space-y-4",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Email"}),(0,it.jsx)("input",{type:"email",required:!0,value:y.email,onChange:e=>v(lt(lt({},y),{},{email:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Full Name"}),(0,it.jsx)("input",{type:"text",required:!0,value:y.name,onChange:e=>v(lt(lt({},y),{},{name:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Roles"}),(0,it.jsxs)("select",{multiple:!0,value:y.roles,onChange:e=>v(lt(lt({},y),{},{roles:Array.from(e.target.selectedOptions,(e=>e.value))})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,it.jsx)("option",{value:"admin",children:"Admin"}),(0,it.jsx)("option",{value:"operator",children:"Operator"}),(0,it.jsx)("option",{value:"viewer",children:"Viewer"})]})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"New Password (optional)"}),(0,it.jsx)("input",{type:"password",value:y.password,onChange:e=>v(lt(lt({},y),{},{password:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Leave blank to keep current password"})]}),(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("input",{type:"checkbox",checked:y.active,onChange:e=>v(lt(lt({},y),{},{active:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,it.jsx)("label",{className:"ml-2 block text-sm text-gray-900",children:"Active User"})]}),(0,it.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,it.jsx)("button",{type:"button",onClick:()=>d(!1),className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Cancel"}),(0,it.jsx)("button",{type:"submit",className:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700",children:"Update User"})]})]})]})})}),f&&h&&(0,it.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,it.jsx)("div",{className:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white",children:(0,it.jsxs)("div",{className:"mt-3",children:[(0,it.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Delete User"}),(0,it.jsxs)("p",{className:"text-sm text-gray-500 mb-4",children:["Are you sure you want to delete ",(0,it.jsx)("strong",{children:h.name})," (",h.email,")? This action cannot be undone."]}),(0,it.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,it.jsx)("button",{onClick:()=>m(!1),className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Cancel"}),(0,it.jsx)("button",{onClick:async()=>{if(h)try{(await fetch("/v1/users/".concat(h.id),{method:"DELETE",credentials:"include"})).ok&&(m(!1),p(null),b())}catch(e){console.error("Failed to delete user:",e)}},className:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700",children:"Delete User"})]})]})})})]})},pc=()=>{var e,t,n,r,s,l,i;const{user:o}=ut(),[c,u]=(0,a.useState)("profile"),[d,f]=(0,a.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[m,h]=(0,a.useState)({theme:(null===o||void 0===o||null===(e=o.preferences)||void 0===e?void 0:e.theme)||"dark",timezone:(null===o||void 0===o||null===(t=o.preferences)||void 0===t?void 0:t.timezone)||"UTC",language:(null===o||void 0===o||null===(n=o.preferences)||void 0===n?void 0:n.language)||"en",notifications:!0}),[p,x]=(0,a.useState)(null),[g,y]=(0,a.useState)(!1),v=[{id:"profile",name:"Profile",icon:Qt},{id:"security",name:"Security",icon:Dt},{id:"preferences",name:"Preferences",icon:Et}];return(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,it.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,it.jsx)("div",{className:"h-16 w-16 rounded-full bg-gray-300 flex items-center justify-center",children:(0,it.jsx)("span",{className:"text-xl font-medium text-gray-700",children:null===o||void 0===o||null===(r=o.name)||void 0===r?void 0:r.split(" ").map((e=>e[0])).join("").toUpperCase()})}),(0,it.jsxs)("div",{children:[(0,it.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:null===o||void 0===o?void 0:o.name}),(0,it.jsx)("p",{className:"text-gray-600",children:null===o||void 0===o?void 0:o.email}),(0,it.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,it.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[(0,it.jsx)(mn,{className:"h-3 w-3 mr-1"}),"Active"]}),null===o||void 0===o||null===(s=o.roles)||void 0===s?void 0:s.map((e=>(0,it.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:e},e)))]})]})]})}),(0,it.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,it.jsx)("div",{className:"border-b border-gray-200",children:(0,it.jsx)("nav",{className:"-mb-px flex space-x-8 px-6",children:v.map((e=>{const t=e.icon;return(0,it.jsxs)("button",{onClick:()=>u(e.id),className:"py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ".concat(c===e.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[(0,it.jsx)(t,{className:"h-4 w-4"}),(0,it.jsx)("span",{children:e.name})]},e.id)}))})}),(0,it.jsxs)("div",{className:"p-6",children:["profile"===c&&(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Profile Information"}),(0,it.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Username"}),(0,it.jsx)("div",{className:"mt-1 text-sm text-gray-900",children:null===o||void 0===o?void 0:o.username})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Email"}),(0,it.jsx)("div",{className:"mt-1 text-sm text-gray-900",children:null===o||void 0===o?void 0:o.email})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Full Name"}),(0,it.jsx)("div",{className:"mt-1 text-sm text-gray-900",children:null===o||void 0===o?void 0:o.name})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"User ID"}),(0,it.jsx)("div",{className:"mt-1 text-sm text-gray-900 font-mono",children:null===o||void 0===o?void 0:o.id})]})]})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Roles & Permissions"}),(0,it.jsxs)("div",{className:"space-y-4",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Roles"}),(0,it.jsx)("div",{className:"mt-1 flex flex-wrap gap-2",children:null===o||void 0===o||null===(l=o.roles)||void 0===l?void 0:l.map((e=>(0,it.jsx)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800",children:e},e)))})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Groups"}),(0,it.jsx)("div",{className:"mt-1 flex flex-wrap gap-2",children:null===o||void 0===o||null===(i=o.groups)||void 0===i?void 0:i.map((e=>(0,it.jsx)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800",children:e},e)))})]})]})]})]}),"security"===c&&(0,it.jsx)("div",{className:"space-y-6",children:(0,it.jsxs)("div",{children:[(0,it.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Change Password"}),p&&(0,it.jsx)("div",{className:"mb-4 p-4 rounded-md ".concat("success"===p.type?"bg-green-50 text-green-800":"bg-red-50 text-red-800"),children:(0,it.jsxs)("div",{className:"flex",children:["success"===p.type?(0,it.jsx)(mn,{className:"h-5 w-5 mr-2"}):(0,it.jsx)(xn,{className:"h-5 w-5 mr-2"}),p.text]})}),(0,it.jsxs)("form",{onSubmit:async e=>{if(e.preventDefault(),d.newPassword===d.confirmPassword)if(d.newPassword.length<4)x({type:"error",text:"Password must be at least 4 characters long"});else{y(!0);try{const e=await fetch("/v1/auth/change-password",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({current_password:d.currentPassword,new_password:d.newPassword})});if(e.ok)x({type:"success",text:"Password changed successfully"}),f({currentPassword:"",newPassword:"",confirmPassword:""});else{const t=await e.json();x({type:"error",text:t.error||"Failed to change password"})}}catch(t){x({type:"error",text:"Failed to change password"})}finally{y(!1)}}else x({type:"error",text:"New passwords do not match"})},className:"space-y-4 max-w-md",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Current Password"}),(0,it.jsx)("input",{type:"password",required:!0,value:d.currentPassword,onChange:e=>f(lt(lt({},d),{},{currentPassword:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"New Password"}),(0,it.jsx)("input",{type:"password",required:!0,value:d.newPassword,onChange:e=>f(lt(lt({},d),{},{newPassword:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Confirm New Password"}),(0,it.jsx)("input",{type:"password",required:!0,value:d.confirmPassword,onChange:e=>f(lt(lt({},d),{},{confirmPassword:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,it.jsxs)("button",{type:"submit",disabled:g,className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50",children:[(0,it.jsx)(ai,{className:"h-4 w-4 mr-2"}),g?"Changing...":"Change Password"]})]})]})}),"preferences"===c&&(0,it.jsx)("div",{className:"space-y-6",children:(0,it.jsxs)("div",{children:[(0,it.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"User Preferences"}),(0,it.jsxs)("form",{onSubmit:async e=>{e.preventDefault(),console.log("Updating preferences:",m)},className:"space-y-4 max-w-md",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Theme"}),(0,it.jsxs)("select",{value:m.theme,onChange:e=>h(lt(lt({},m),{},{theme:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,it.jsx)("option",{value:"light",children:"Light"}),(0,it.jsx)("option",{value:"dark",children:"Dark"}),(0,it.jsx)("option",{value:"auto",children:"Auto"})]})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Timezone"}),(0,it.jsxs)("select",{value:m.timezone,onChange:e=>h(lt(lt({},m),{},{timezone:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,it.jsx)("option",{value:"UTC",children:"UTC"}),(0,it.jsx)("option",{value:"America/New_York",children:"Eastern Time"}),(0,it.jsx)("option",{value:"America/Chicago",children:"Central Time"}),(0,it.jsx)("option",{value:"America/Denver",children:"Mountain Time"}),(0,it.jsx)("option",{value:"America/Los_Angeles",children:"Pacific Time"})]})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Language"}),(0,it.jsxs)("select",{value:m.language,onChange:e=>h(lt(lt({},m),{},{language:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,it.jsx)("option",{value:"en",children:"English"}),(0,it.jsx)("option",{value:"es",children:"Spanish"}),(0,it.jsx)("option",{value:"fr",children:"French"}),(0,it.jsx)("option",{value:"de",children:"German"})]})]}),(0,it.jsxs)("button",{type:"submit",className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700",children:[(0,it.jsx)(Et,{className:"h-4 w-4 mr-2"}),"Update Preferences"]})]})]})})]})]})]})},xc=["title","titleId"];function gc(e,t){let{title:n,titleId:r}=e,s=mt(e,xc);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"}))}const yc=a.forwardRef(gc),vc=()=>{const{user:e}=ut(),[t,n]=(0,a.useState)("authentication"),[r,s]=(0,a.useState)({authentication:{sessionTimeout:24,maxLoginAttempts:5,passwordMinLength:4,requireMFA:!1},security:{enableAuditLogging:!0,enableRateLimiting:!0,allowedOrigins:["http://localhost:3000","http://localhost:8080"],encryptionEnabled:!0},notifications:{emailEnabled:!1,slackEnabled:!1,webhookUrl:""},system:{logLevel:"info",maxConnections:1e3,backupEnabled:!0,backupInterval:"daily"}}),[l,i]=(0,a.useState)(!1),[o,c]=(0,a.useState)(null);(0,a.useEffect)((()=>{u()}),[]);const u=async()=>{try{const e=await fetch("/v1/admin/settings",{credentials:"include"});if(e.ok){const t=await e.json();s(t.settings||r)}}catch(e){console.error("Failed to fetch settings:",e)}},d=[{id:"authentication",name:"Authentication",icon:Dt},{id:"security",name:"Security",icon:ai},{id:"notifications",name:"Notifications",icon:yc},{id:"system",name:"System",icon:vn}];return(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,it.jsxs)("div",{className:"flex items-center justify-between",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"System Settings"}),(0,it.jsx)("p",{className:"text-gray-600",children:"Configure system-wide settings and preferences"})]}),(0,it.jsxs)("button",{onClick:async()=>{i(!0);try{(await fetch("/v1/admin/settings",{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({settings:r})})).ok?c({type:"success",text:"Settings saved successfully"}):c({type:"error",text:"Failed to save settings"})}catch(e){c({type:"error",text:"Failed to save settings"})}finally{i(!1)}},disabled:l,className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50",children:[(0,it.jsx)(Et,{className:"h-4 w-4 mr-2"}),l?"Saving...":"Save Settings"]})]})}),o&&(0,it.jsx)("div",{className:"p-4 rounded-md ".concat("success"===o.type?"bg-green-50 text-green-800":"bg-red-50 text-red-800"),children:(0,it.jsxs)("div",{className:"flex",children:["success"===o.type?(0,it.jsx)(mn,{className:"h-5 w-5 mr-2"}):(0,it.jsx)(xn,{className:"h-5 w-5 mr-2"}),o.text]})}),(0,it.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,it.jsx)("div",{className:"border-b border-gray-200",children:(0,it.jsx)("nav",{className:"-mb-px flex space-x-8 px-6",children:d.map((e=>{const r=e.icon;return(0,it.jsxs)("button",{onClick:()=>n(e.id),className:"py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ".concat(t===e.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[(0,it.jsx)(r,{className:"h-4 w-4"}),(0,it.jsx)("span",{children:e.name})]},e.id)}))})}),(0,it.jsxs)("div",{className:"p-6",children:["authentication"===t&&(0,it.jsx)("div",{className:"space-y-6",children:(0,it.jsxs)("div",{children:[(0,it.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Authentication Settings"}),(0,it.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Session Timeout (hours)"}),(0,it.jsx)("input",{type:"number",value:r.authentication.sessionTimeout,onChange:e=>s(lt(lt({},r),{},{authentication:lt(lt({},r.authentication),{},{sessionTimeout:parseInt(e.target.value)})})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Max Login Attempts"}),(0,it.jsx)("input",{type:"number",value:r.authentication.maxLoginAttempts,onChange:e=>s(lt(lt({},r),{},{authentication:lt(lt({},r.authentication),{},{maxLoginAttempts:parseInt(e.target.value)})})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Password Min Length"}),(0,it.jsx)("input",{type:"number",value:r.authentication.passwordMinLength,onChange:e=>s(lt(lt({},r),{},{authentication:lt(lt({},r.authentication),{},{passwordMinLength:parseInt(e.target.value)})})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("input",{type:"checkbox",checked:r.authentication.requireMFA,onChange:e=>s(lt(lt({},r),{},{authentication:lt(lt({},r.authentication),{},{requireMFA:e.target.checked})})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,it.jsx)("label",{className:"ml-2 block text-sm text-gray-900",children:"Require Multi-Factor Authentication"})]})]})]})}),"security"===t&&(0,it.jsx)("div",{className:"space-y-6",children:(0,it.jsxs)("div",{children:[(0,it.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Security Settings"}),(0,it.jsxs)("div",{className:"space-y-4",children:[(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("input",{type:"checkbox",checked:r.security.enableAuditLogging,onChange:e=>s(lt(lt({},r),{},{security:lt(lt({},r.security),{},{enableAuditLogging:e.target.checked})})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,it.jsx)("label",{className:"ml-2 block text-sm text-gray-900",children:"Enable Audit Logging"})]}),(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("input",{type:"checkbox",checked:r.security.enableRateLimiting,onChange:e=>s(lt(lt({},r),{},{security:lt(lt({},r.security),{},{enableRateLimiting:e.target.checked})})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,it.jsx)("label",{className:"ml-2 block text-sm text-gray-900",children:"Enable Rate Limiting"})]}),(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("input",{type:"checkbox",checked:r.security.encryptionEnabled,onChange:e=>s(lt(lt({},r),{},{security:lt(lt({},r.security),{},{encryptionEnabled:e.target.checked})})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,it.jsx)("label",{className:"ml-2 block text-sm text-gray-900",children:"Enable Data Encryption"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Allowed Origins (one per line)"}),(0,it.jsx)("textarea",{value:r.security.allowedOrigins.join("\n"),onChange:e=>s(lt(lt({},r),{},{security:lt(lt({},r.security),{},{allowedOrigins:e.target.value.split("\n").filter((e=>e.trim()))})})),rows:4,className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]})]})}),"notifications"===t&&(0,it.jsx)("div",{className:"space-y-6",children:(0,it.jsxs)("div",{children:[(0,it.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Notification Settings"}),(0,it.jsxs)("div",{className:"space-y-4",children:[(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("input",{type:"checkbox",checked:r.notifications.emailEnabled,onChange:e=>s(lt(lt({},r),{},{notifications:lt(lt({},r.notifications),{},{emailEnabled:e.target.checked})})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,it.jsx)("label",{className:"ml-2 block text-sm text-gray-900",children:"Enable Email Notifications"})]}),(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("input",{type:"checkbox",checked:r.notifications.slackEnabled,onChange:e=>s(lt(lt({},r),{},{notifications:lt(lt({},r.notifications),{},{slackEnabled:e.target.checked})})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,it.jsx)("label",{className:"ml-2 block text-sm text-gray-900",children:"Enable Slack Notifications"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Webhook URL"}),(0,it.jsx)("input",{type:"url",value:r.notifications.webhookUrl,onChange:e=>s(lt(lt({},r),{},{notifications:lt(lt({},r.notifications),{},{webhookUrl:e.target.value})})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"https://hooks.slack.com/services/..."})]})]})]})}),"system"===t&&(0,it.jsx)("div",{className:"space-y-6",children:(0,it.jsxs)("div",{children:[(0,it.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"System Settings"}),(0,it.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Log Level"}),(0,it.jsxs)("select",{value:r.system.logLevel,onChange:e=>s(lt(lt({},r),{},{system:lt(lt({},r.system),{},{logLevel:e.target.value})})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,it.jsx)("option",{value:"debug",children:"Debug"}),(0,it.jsx)("option",{value:"info",children:"Info"}),(0,it.jsx)("option",{value:"warn",children:"Warning"}),(0,it.jsx)("option",{value:"error",children:"Error"})]})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Max Connections"}),(0,it.jsx)("input",{type:"number",value:r.system.maxConnections,onChange:e=>s(lt(lt({},r),{},{system:lt(lt({},r.system),{},{maxConnections:parseInt(e.target.value)})})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Backup Interval"}),(0,it.jsxs)("select",{value:r.system.backupInterval,onChange:e=>s(lt(lt({},r),{},{system:lt(lt({},r.system),{},{backupInterval:e.target.value})})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,it.jsx)("option",{value:"hourly",children:"Hourly"}),(0,it.jsx)("option",{value:"daily",children:"Daily"}),(0,it.jsx)("option",{value:"weekly",children:"Weekly"}),(0,it.jsx)("option",{value:"monthly",children:"Monthly"})]})]}),(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("input",{type:"checkbox",checked:r.system.backupEnabled,onChange:e=>s(lt(lt({},r),{},{system:lt(lt({},r.system),{},{backupEnabled:e.target.checked})})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,it.jsx)("label",{className:"ml-2 block text-sm text-gray-900",children:"Enable Automatic Backups"})]})]})]})})]})]})]})},bc=()=>{const{user:e}=ut(),[t,n]=(0,a.useState)([]),[r,s]=(0,a.useState)(!0),[l,i]=(0,a.useState)(""),[o,c]=(0,a.useState)(!1),[u,d]=(0,a.useState)(!1),[f,m]=(0,a.useState)(!1),[h,p]=(0,a.useState)(null),[x,g]=(0,a.useState)({id:"",name:"",description:"",enabled:!1,type:"boolean",value:!1,environments:["production"],rollout_percentage:100});(0,a.useEffect)((()=>{y()}),[]);const y=async()=>{try{const e=await fetch("/v1/featureflags",{credentials:"include"});if(e.ok){const t=await e.json(),r=t.flags?Object.values(t.flags):[];n(r)}}catch(e){console.error("Failed to fetch feature flags:",e)}finally{s(!1)}},v=t.filter((e=>e.name.toLowerCase().includes(l.toLowerCase())||e.description.toLowerCase().includes(l.toLowerCase())||e.id.toLowerCase().includes(l.toLowerCase())));return r?(0,it.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,it.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"})}):(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,it.jsxs)("div",{className:"flex items-center justify-between",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Feature Flag Management"}),(0,it.jsx)("p",{className:"text-gray-600",children:"Control feature rollouts and experiments"})]}),(0,it.jsxs)("button",{onClick:()=>c(!0),className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700",children:[(0,it.jsx)(Ci,{className:"h-4 w-4 mr-2"}),"Create Flag"]})]})}),(0,it.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,it.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,it.jsxs)("div",{className:"flex-1 relative",children:[(0,it.jsx)(xt,{className:"h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),(0,it.jsx)("input",{type:"text",placeholder:"Search feature flags...",value:l,onChange:e=>i(e.target.value),className:"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"})]}),(0,it.jsx)("div",{className:"flex items-center space-x-2",children:(0,it.jsxs)("span",{className:"text-sm text-gray-500",children:[v.length," of ",t.length," flags"]})})]})}),(0,it.jsxs)("div",{className:"bg-white shadow rounded-lg overflow-hidden",children:[(0,it.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,it.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Feature Flags"})}),(0,it.jsx)("div",{className:"overflow-x-auto",children:(0,it.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,it.jsx)("thead",{className:"bg-gray-50",children:(0,it.jsxs)("tr",{children:[(0,it.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Flag"}),(0,it.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,it.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),(0,it.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Value"}),(0,it.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Rollout"}),(0,it.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Updated"}),(0,it.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,it.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:v.map((e=>(0,it.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,it.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,it.jsxs)("div",{children:[(0,it.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,it.jsx)("div",{className:"text-sm text-gray-500",children:e.description}),(0,it.jsx)("div",{className:"text-xs text-gray-400 font-mono",children:e.id})]})}),(0,it.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,it.jsxs)("button",{onClick:()=>(async e=>{try{(await fetch("/v1/featureflags/".concat(e.id),{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(lt(lt({},e),{},{enabled:!e.enabled}))})).ok&&y()}catch(t){console.error("Failed to toggle feature flag:",t)}})(e),className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(e.enabled?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:[e.enabled?(0,it.jsx)(mn,{className:"h-3 w-3 mr-1"}):(0,it.jsx)(xi,{className:"h-3 w-3 mr-1"}),e.enabled?"Enabled":"Disabled"]})}),(0,it.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:(0,it.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:e.type})}),(0,it.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:(0,it.jsx)("code",{className:"bg-gray-100 px-2 py-1 rounded text-xs",children:void 0!==e.value?String(e.value):"N/A"})}),(0,it.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:void 0!==e.rollout_percentage?"".concat(e.rollout_percentage,"%"):"100%"}),(0,it.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.updated_at).toLocaleDateString()}),(0,it.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,it.jsxs)("div",{className:"flex items-center justify-end space-x-2",children:[(0,it.jsx)("button",{onClick:()=>{p(e),d(!0)},className:"text-blue-600 hover:text-blue-900",children:(0,it.jsx)(mc,{className:"h-4 w-4"})}),(0,it.jsx)("button",{onClick:()=>{p(e),m(!0)},className:"text-red-600 hover:text-red-900",children:(0,it.jsx)(oc,{className:"h-4 w-4"})})]})})]},e.id)))})]})})]}),o&&(0,it.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,it.jsx)("div",{className:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white",children:(0,it.jsxs)("div",{className:"mt-3",children:[(0,it.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Create Feature Flag"}),(0,it.jsxs)("form",{onSubmit:async e=>{e.preventDefault();try{const e=await fetch("/v1/featureflags",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(x)});if(e.ok)c(!1),g({id:"",name:"",description:"",enabled:!1,type:"boolean",value:!1,environments:["production"],rollout_percentage:100}),y(),alert("Feature flag created successfully!");else{const t=await e.json();alert("Failed to create feature flag: ".concat(t.error||"Unknown error"))}}catch(t){console.error("Failed to create feature flag:",t),alert("Failed to create feature flag. Please check the console for details.")}},className:"space-y-4",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Flag ID"}),(0,it.jsx)("input",{type:"text",required:!0,value:x.id,onChange:e=>g(lt(lt({},x),{},{id:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"feature_name"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Name"}),(0,it.jsx)("input",{type:"text",required:!0,value:x.name,onChange:e=>g(lt(lt({},x),{},{name:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Feature Name"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Description"}),(0,it.jsx)("textarea",{required:!0,value:x.description,onChange:e=>g(lt(lt({},x),{},{description:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500",rows:3,placeholder:"Describe what this feature flag controls"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Type"}),(0,it.jsxs)("select",{value:x.type,onChange:e=>g(lt(lt({},x),{},{type:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,it.jsx)("option",{value:"boolean",children:"Boolean"}),(0,it.jsx)("option",{value:"string",children:"String"}),(0,it.jsx)("option",{value:"number",children:"Number"})]})]}),(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("input",{type:"checkbox",checked:x.enabled,onChange:e=>g(lt(lt({},x),{},{enabled:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,it.jsx)("label",{className:"ml-2 block text-sm text-gray-900",children:"Enable immediately"})]}),(0,it.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,it.jsx)("button",{type:"button",onClick:()=>c(!1),className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Cancel"}),(0,it.jsx)("button",{type:"submit",className:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700",children:"Create Flag"})]})]})]})})}),f&&h&&(0,it.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,it.jsx)("div",{className:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white",children:(0,it.jsxs)("div",{className:"mt-3 text-center",children:[(0,it.jsx)(xn,{className:"mx-auto h-12 w-12 text-red-600"}),(0,it.jsx)("h3",{className:"text-lg font-medium text-gray-900 mt-2",children:"Delete Feature Flag"}),(0,it.jsxs)("p",{className:"text-sm text-gray-500 mt-2",children:['Are you sure you want to delete "',h.name,'"? This action cannot be undone.']}),(0,it.jsxs)("div",{className:"flex justify-center space-x-3 mt-4",children:[(0,it.jsx)("button",{onClick:()=>m(!1),className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Cancel"}),(0,it.jsx)("button",{onClick:async()=>{if(h)try{(await fetch("/v1/featureflags/".concat(h.id),{method:"DELETE",credentials:"include"})).ok&&(m(!1),p(null),y())}catch(e){console.error("Failed to delete feature flag:",e)}},className:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700",children:"Delete"})]})]})})})]})},wc=["title","titleId"];function jc(e,t){let{title:n,titleId:r}=e,s=mt(e,wc);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z"}))}const Nc=a.forwardRef(jc),kc=e=>{let{roles:t,onEdit:n,onDelete:r}=e;return(0,it.jsx)("div",{className:"overflow-x-auto",children:(0,it.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,it.jsx)("thead",{className:"bg-gray-50",children:(0,it.jsxs)("tr",{children:[(0,it.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Role"}),(0,it.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Permissions"}),(0,it.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Users"}),(0,it.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Updated"}),(0,it.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,it.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:t.map((e=>(0,it.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,it.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,it.jsxs)("div",{children:[(0,it.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,it.jsx)("div",{className:"text-sm text-gray-500",children:e.description})]})}),(0,it.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,it.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:[e.permissions.length," permissions"]})}),(0,it.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[e.user_count||0," users"]}),(0,it.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.updated_at).toLocaleDateString()}),(0,it.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,it.jsxs)("div",{className:"flex items-center justify-end space-x-2",children:[(0,it.jsx)("button",{onClick:()=>n(e),className:"text-blue-600 hover:text-blue-900",children:(0,it.jsx)(mc,{className:"h-4 w-4"})}),(0,it.jsx)("button",{onClick:()=>r(e),className:"text-red-600 hover:text-red-900",children:(0,it.jsx)(oc,{className:"h-4 w-4"})})]})})]},e.id)))})]})})},Sc=e=>{let{permissions:t,onEdit:n,onDelete:r}=e;return(0,it.jsx)("div",{className:"overflow-x-auto",children:(0,it.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,it.jsx)("thead",{className:"bg-gray-50",children:(0,it.jsxs)("tr",{children:[(0,it.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Permission"}),(0,it.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Resource"}),(0,it.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Action"}),(0,it.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Category"}),(0,it.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,it.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:t.map((e=>(0,it.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,it.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,it.jsxs)("div",{children:[(0,it.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,it.jsx)("div",{className:"text-sm text-gray-500",children:e.description})]})}),(0,it.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,it.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:e.resource})}),(0,it.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,it.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800",children:e.action})}),(0,it.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.category}),(0,it.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,it.jsxs)("div",{className:"flex items-center justify-end space-x-2",children:[(0,it.jsx)("button",{onClick:()=>n(e),className:"text-blue-600 hover:text-blue-900",children:(0,it.jsx)(mc,{className:"h-4 w-4"})}),(0,it.jsx)("button",{onClick:()=>r(e),className:"text-red-600 hover:text-red-900",children:(0,it.jsx)(oc,{className:"h-4 w-4"})})]})})]},e.id)))})]})})},Ec=e=>{let{policies:t,onEdit:n,onDelete:r}=e;return(0,it.jsx)("div",{className:"overflow-x-auto",children:(0,it.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,it.jsx)("thead",{className:"bg-gray-50",children:(0,it.jsxs)("tr",{children:[(0,it.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Policy"}),(0,it.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Effect"}),(0,it.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,it.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Resources"}),(0,it.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,it.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:t.map((e=>(0,it.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,it.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,it.jsxs)("div",{children:[(0,it.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,it.jsx)("div",{className:"text-sm text-gray-500",children:e.description})]})}),(0,it.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,it.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat("allow"===e.effect?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.effect})}),(0,it.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,it.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(e.enabled?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"),children:[e.enabled?(0,it.jsx)(mn,{className:"h-3 w-3 mr-1"}):(0,it.jsx)(xi,{className:"h-3 w-3 mr-1"}),e.enabled?"Enabled":"Disabled"]})}),(0,it.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[e.resources.length," resources"]}),(0,it.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,it.jsxs)("div",{className:"flex items-center justify-end space-x-2",children:[(0,it.jsx)("button",{onClick:()=>n(e),className:"text-blue-600 hover:text-blue-900",children:(0,it.jsx)(mc,{className:"h-4 w-4"})}),(0,it.jsx)("button",{onClick:()=>r(e),className:"text-red-600 hover:text-red-900",children:(0,it.jsx)(oc,{className:"h-4 w-4"})})]})})]},e.id)))})]})})},Cc=e=>{let{type:t,onSubmit:n,onCancel:r}=e;const[s,l]=(0,a.useState)({});return(0,it.jsxs)("form",{onSubmit:e=>{e.preventDefault(),n(s)},className:"space-y-4",children:[(()=>{switch(t){case"roles":return(0,it.jsxs)(it.Fragment,{children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Role ID"}),(0,it.jsx)("input",{type:"text",required:!0,value:s.id||"",onChange:e=>l(lt(lt({},s),{},{id:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"role_name"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Name"}),(0,it.jsx)("input",{type:"text",required:!0,value:s.name||"",onChange:e=>l(lt(lt({},s),{},{name:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Role Name"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Description"}),(0,it.jsx)("textarea",{required:!0,value:s.description||"",onChange:e=>l(lt(lt({},s),{},{description:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500",rows:3,placeholder:"Describe this role"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Permissions"}),(0,it.jsx)("input",{type:"text",value:s.permissions||"",onChange:e=>l(lt(lt({},s),{},{permissions:e.target.value.split(",").map((e=>e.trim()))})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"permission1, permission2, permission3"}),(0,it.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Comma-separated list of permissions"})]})]});case"permissions":return(0,it.jsxs)(it.Fragment,{children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Permission ID"}),(0,it.jsx)("input",{type:"text",required:!0,value:s.id||"",onChange:e=>l(lt(lt({},s),{},{id:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"resource:action"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Name"}),(0,it.jsx)("input",{type:"text",required:!0,value:s.name||"",onChange:e=>l(lt(lt({},s),{},{name:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Permission Name"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Description"}),(0,it.jsx)("textarea",{required:!0,value:s.description||"",onChange:e=>l(lt(lt({},s),{},{description:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500",rows:3,placeholder:"Describe this permission"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Resource"}),(0,it.jsx)("input",{type:"text",required:!0,value:s.resource||"",onChange:e=>l(lt(lt({},s),{},{resource:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"workflows"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Action"}),(0,it.jsxs)("select",{required:!0,value:s.action||"",onChange:e=>l(lt(lt({},s),{},{action:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,it.jsx)("option",{value:"",children:"Select action"}),(0,it.jsx)("option",{value:"read",children:"Read"}),(0,it.jsx)("option",{value:"write",children:"Write"}),(0,it.jsx)("option",{value:"execute",children:"Execute"}),(0,it.jsx)("option",{value:"delete",children:"Delete"})]})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Category"}),(0,it.jsx)("input",{type:"text",required:!0,value:s.category||"",onChange:e=>l(lt(lt({},s),{},{category:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Workflows"})]})]});case"policies":return(0,it.jsxs)(it.Fragment,{children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Policy ID"}),(0,it.jsx)("input",{type:"text",required:!0,value:s.id||"",onChange:e=>l(lt(lt({},s),{},{id:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"policy-name"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Name"}),(0,it.jsx)("input",{type:"text",required:!0,value:s.name||"",onChange:e=>l(lt(lt({},s),{},{name:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Policy Name"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Description"}),(0,it.jsx)("textarea",{required:!0,value:s.description||"",onChange:e=>l(lt(lt({},s),{},{description:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500",rows:3,placeholder:"Describe this policy"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Effect"}),(0,it.jsxs)("select",{required:!0,value:s.effect||"",onChange:e=>l(lt(lt({},s),{},{effect:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,it.jsx)("option",{value:"",children:"Select effect"}),(0,it.jsx)("option",{value:"allow",children:"Allow"}),(0,it.jsx)("option",{value:"deny",children:"Deny"})]})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Subjects"}),(0,it.jsx)("input",{type:"text",value:s.subjects||"",onChange:e=>l(lt(lt({},s),{},{subjects:e.target.value.split(",").map((e=>e.trim()))})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"role:admin, user:john"}),(0,it.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Comma-separated list of subjects"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Resources"}),(0,it.jsx)("input",{type:"text",value:s.resources||"",onChange:e=>l(lt(lt({},s),{},{resources:e.target.value.split(",").map((e=>e.trim()))})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"workflows, discovery"}),(0,it.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Comma-separated list of resources"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Actions"}),(0,it.jsx)("input",{type:"text",value:s.actions||"",onChange:e=>l(lt(lt({},s),{},{actions:e.target.value.split(",").map((e=>e.trim()))})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"read, write, execute"}),(0,it.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Comma-separated list of actions"})]}),(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("input",{type:"checkbox",checked:s.enabled||!1,onChange:e=>l(lt(lt({},s),{},{enabled:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,it.jsx)("label",{className:"ml-2 block text-sm text-gray-900",children:"Enable policy"})]})]});default:return null}})(),(0,it.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,it.jsx)("button",{type:"button",onClick:r,className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Cancel"}),(0,it.jsxs)("button",{type:"submit",className:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700",children:["Create ",t.slice(0,-1)]})]})]})},Oc=()=>{const{user:e}=ut(),[t,n]=(0,a.useState)("roles"),[r,s]=(0,a.useState)([]),[l,i]=(0,a.useState)([]),[o,c]=(0,a.useState)([]),[u,d]=(0,a.useState)(!0),[f,m]=(0,a.useState)(""),[h,p]=(0,a.useState)(!1),[x,g]=(0,a.useState)(!1),[y,v]=(0,a.useState)(!1),[b,w]=(0,a.useState)(null);(0,a.useEffect)((()=>{j()}),[t]);const j=async()=>{d(!0);try{switch(t){case"roles":await N();break;case"permissions":await k();break;case"policies":await S()}}catch(e){console.error("Failed to fetch ".concat(t,":"),e)}finally{d(!1)}},N=async()=>{try{const e=await fetch("/v1/rbac/roles",{credentials:"include"});if(e.ok){const t=await e.json();s(t.roles||[])}}catch(e){console.error("Failed to fetch roles:",e)}},k=async()=>{try{const e=await fetch("/v1/rbac/permissions",{credentials:"include"});if(e.ok){const t=await e.json();i(t.permissions||[])}}catch(e){console.error("Failed to fetch permissions:",e)}},S=async()=>{try{const e=await fetch("/v1/rbac/policies",{credentials:"include"});if(e.ok){const t=await e.json();c(t.policies||[])}}catch(e){console.error("Failed to fetch policies:",e)}},E=()=>{let e=[];switch(t){case"roles":e=r;break;case"permissions":e=l;break;case"policies":e=o}return e.filter((e=>e.name.toLowerCase().includes(f.toLowerCase())||e.description.toLowerCase().includes(f.toLowerCase())))},C=[{id:"roles",name:"Roles",icon:Nc},{id:"permissions",name:"Permissions",icon:ai},{id:"policies",name:"Policies",icon:Dt}];return u?(0,it.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,it.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"})}):(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,it.jsxs)("div",{className:"flex items-center justify-between",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"RBAC Management"}),(0,it.jsx)("p",{className:"text-gray-600",children:"Manage roles, permissions, and access policies"})]}),(0,it.jsxs)("button",{onClick:()=>p(!0),className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700",children:[(0,it.jsx)(Ci,{className:"h-4 w-4 mr-2"}),"Create ",t.slice(0,-1)]})]})}),(0,it.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,it.jsx)("div",{className:"border-b border-gray-200",children:(0,it.jsx)("nav",{className:"-mb-px flex space-x-8 px-6",children:C.map((e=>(0,it.jsxs)("button",{onClick:()=>n(e.id),className:"py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ".concat(t===e.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[(0,it.jsx)(e.icon,{className:"h-5 w-5"}),(0,it.jsx)("span",{children:e.name})]},e.id)))})}),(0,it.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,it.jsxs)("div",{className:"relative",children:[(0,it.jsx)(xt,{className:"h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),(0,it.jsx)("input",{type:"text",placeholder:"Search ".concat(t,"..."),value:f,onChange:e=>m(e.target.value),className:"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"})]})}),(0,it.jsxs)("div",{className:"p-6",children:["roles"===t&&(0,it.jsx)(kc,{roles:E(),onEdit:e=>{w(e),g(!0)},onDelete:e=>{w(e),v(!0)}}),"permissions"===t&&(0,it.jsx)(Sc,{permissions:E(),onEdit:e=>{w(e),g(!0)},onDelete:e=>{w(e),v(!0)}}),"policies"===t&&(0,it.jsx)(Ec,{policies:E(),onEdit:e=>{w(e),g(!0)},onDelete:e=>{w(e),v(!0)}})]})]}),h&&(0,it.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,it.jsx)("div",{className:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white",children:(0,it.jsxs)("div",{className:"mt-3",children:[(0,it.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:["Create ",t.slice(0,-1)]}),(0,it.jsx)(Cc,{type:t,onSubmit:async e=>{try{const n=await fetch("/v1/rbac/".concat(t),{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(e)});if(n.ok)p(!1),j(),alert("".concat(t.slice(0,-1)," created successfully!"));else{const e=await n.json();alert("Failed to create ".concat(t.slice(0,-1),": ").concat(e.error||"Unknown error"))}}catch(n){console.error("Failed to create ".concat(t.slice(0,-1),":"),n),alert("Failed to create ".concat(t.slice(0,-1),". Please check the console for details."))}},onCancel:()=>p(!1)})]})})}),y&&b&&(0,it.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,it.jsx)("div",{className:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white",children:(0,it.jsxs)("div",{className:"mt-3 text-center",children:[(0,it.jsx)(xn,{className:"mx-auto h-12 w-12 text-red-600"}),(0,it.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mt-2",children:["Delete ",t.slice(0,-1)]}),(0,it.jsxs)("p",{className:"text-sm text-gray-500 mt-2",children:['Are you sure you want to delete "',b.name,'"? This action cannot be undone.']}),(0,it.jsxs)("div",{className:"flex justify-center space-x-3 mt-4",children:[(0,it.jsx)("button",{onClick:()=>v(!1),className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Cancel"}),(0,it.jsx)("button",{onClick:async()=>{if(b)try{(await fetch("/v1/rbac/".concat(t,"/").concat(b.id),{method:"DELETE",credentials:"include"})).ok&&(v(!1),w(null),j())}catch(e){console.error("Failed to delete ".concat(t.slice(0,-1),":"),e)}},className:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700",children:"Delete"})]})]})})})]})};function Pc(){const{isAuthenticated:e,isLoading:t}=ut();return t?(0,it.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center",children:(0,it.jsxs)("div",{className:"text-center",children:[(0,it.jsx)("div",{className:"animate-spin rounded-full h-16 w-16 border-b-2 border-white mx-auto mb-4"}),(0,it.jsx)("div",{className:"text-white text-lg",children:"Loading CAINuro Orchestrator..."})]})}):e?(0,it.jsx)("div",{className:"min-h-screen bg-theme-bg-container",children:(0,it.jsx)(rn,{children:(0,it.jsxs)(Ve,{children:[(0,it.jsx)(We,{path:"/",element:(0,it.jsx)(dt,{children:(0,it.jsx)(Sn,{})})}),(0,it.jsx)(We,{path:"/dashboard",element:(0,it.jsx)(dt,{children:(0,it.jsx)(Sn,{})})}),(0,it.jsx)(We,{path:"/search",element:(0,it.jsx)(dt,{requiredPermission:{action:"READ",resource:"discovery"},children:(0,it.jsx)(ti,{})})}),(0,it.jsx)(We,{path:"/discovery",element:(0,it.jsx)(dt,{requiredPermission:{action:"READ",resource:"discovery"},children:(0,it.jsx)(si,{})})}),(0,it.jsx)(We,{path:"/workflows",element:(0,it.jsx)(dt,{requiredPermission:{action:"EXECUTE",resource:"workflows"},children:(0,it.jsx)(gi,{})})}),(0,it.jsx)(We,{path:"/envoy",element:(0,it.jsx)(dt,{requiredPermission:{action:"READ",resource:"envoy"},children:(0,it.jsx)(To,{})})}),(0,it.jsx)(We,{path:"/autoscaler",element:(0,it.jsx)(dt,{requiredPermission:{action:"READ",resource:"autoscaler"},children:(0,it.jsx)(Ko,{})})}),(0,it.jsx)(We,{path:"/audit",element:(0,it.jsx)(dt,{requiredPermission:{action:"READ",resource:"audit"},children:(0,it.jsx)(ec,{})})}),(0,it.jsx)(We,{path:"/database",element:(0,it.jsx)(dt,{requiredPermission:{action:"READ",resource:"database"},children:(0,it.jsx)(cc,{})})}),(0,it.jsx)(We,{path:"/admin",element:(0,it.jsx)(dt,{requiredPermission:{action:"READ",resource:"admin"},children:(0,it.jsx)(uc,{})})}),(0,it.jsx)(We,{path:"/admin/users",element:(0,it.jsx)(dt,{requiredPermission:{action:"READ",resource:"admin"},children:(0,it.jsx)(hc,{})})}),(0,it.jsx)(We,{path:"/admin/settings",element:(0,it.jsx)(dt,{requiredPermission:{action:"READ",resource:"admin"},children:(0,it.jsx)(vc,{})})}),(0,it.jsx)(We,{path:"/admin/featureflags",element:(0,it.jsx)(dt,{requiredPermission:{action:"READ",resource:"admin"},children:(0,it.jsx)(bc,{})})}),(0,it.jsx)(We,{path:"/admin/rbac",element:(0,it.jsx)(dt,{requiredPermission:{action:"READ",resource:"admin"},children:(0,it.jsx)(Oc,{})})}),(0,it.jsx)(We,{path:"/profile",element:(0,it.jsx)(dt,{children:(0,it.jsx)(pc,{})})})]})})}):(0,it.jsx)(ft,{})}const _c=function(){return(0,it.jsx)(ct,{children:(0,it.jsx)(Pc,{})})},Rc=function(e){var t,n=aa(),r=e||{},a=r.reducer,s=void 0===a?void 0:a,l=r.middleware,i=void 0===l?n():l,o=r.devTools,c=void 0===o||o,u=r.preloadedState,d=void 0===u?void 0:u,f=r.enhancers,m=void 0===f?void 0:f;if("function"===typeof s)t=s;else{if(!Yr(s))throw new Error('"reducer" is a required argument, and must be a function or an object of functions that can be passed to combineReducers');t=Tr(s)}var h=i;"function"===typeof h&&(h=h(n));var p=Dr.apply(void 0,h),x=Ar;c&&(x=Gr(Jr({trace:!1},"object"===typeof c&&c)));var g=new na(p),y=g;return Array.isArray(m)?y=Br([p],m):"function"===typeof m&&(y=m(g)),Lr(t,d,x.apply(void 0,y))}({reducer:{search:Kl,workflow:mi,envoy:ki,autoscaler:Bo,audit:Yo,database:sc},middleware:e=>e({serializableCheck:{ignoredActions:["persist/PERSIST"]}})}),Lc=e=>{e&&e instanceof Function&&n.e(488).then(n.bind(n,488)).then((t=>{let{getCLS:n,getFID:r,getFCP:a,getLCP:s,getTTFB:l}=t;n(e),r(e),a(e),s(e),l(e)}))};l.createRoot(document.getElementById("root")).render((0,it.jsx)(a.StrictMode,{children:(0,it.jsx)(C,{store:Rc,children:(0,it.jsx)(Xe,{children:(0,it.jsx)(_c,{})})})})),Lc()})();
//# sourceMappingURL=main.8b2000d8.js.map