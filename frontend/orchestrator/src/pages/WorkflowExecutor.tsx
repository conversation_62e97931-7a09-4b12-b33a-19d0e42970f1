import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../store/store';
import {
  fetchWorkflows,
  executeWorkflow,
  setSelectedWorkflow,
  getWorkflowStatus,
} from '../store/slices/workflowSlice';
import {
  PlayIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  CogIcon,
} from '@heroicons/react/24/outline';

const WorkflowExecutor: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { workflows, executions, loading, error, selectedWorkflow } = useSelector(
    (state: RootState) => state.workflow
  );

  const [inputs, setInputs] = useState<Record<string, any>>({});

  useEffect(() => {
    dispatch(fetchWorkflows());
  }, [dispatch]);

  const handleExecute = () => {
    if (selectedWorkflow) {
      dispatch(executeWorkflow({
        workflowId: selectedWorkflow.id,
        inputs,
      }));
      setInputs({});
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <ClockIcon className="h-5 w-5 text-yellow-400" />;
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-400" />;
      case 'failed':
        return <XCircleIcon className="h-5 w-5 text-red-400" />;
      default:
        return <CogIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'bg-yellow-100 text-yellow-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-white">Workflow Executor</h1>
        <p className="mt-1 text-sm text-gray-400">
          Execute and monitor automated workflows across your infrastructure
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Workflow Selection */}
        <div className="bg-gray-800 shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-white mb-4">
              Available Workflows
            </h3>

            {loading ? (
              <div className="flex items-center justify-center h-32">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500"></div>
              </div>
            ) : (
              <div className="space-y-3">
                {workflows.map((workflow) => (
                  <div
                    key={workflow.id}
                    className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                      selectedWorkflow?.id === workflow.id
                        ? 'border-cyan-500 bg-cyan-500/10'
                        : 'border-gray-600 hover:bg-gray-700'
                    }`}
                    onClick={() => dispatch(setSelectedWorkflow(workflow))}
                  >
                    <div className="flex items-start justify-between">
                      <div>
                        <h4 className="text-lg font-medium text-white">{workflow.name}</h4>
                        <p className="mt-1 text-sm text-gray-400">{workflow.description}</p>
                        <p className="mt-1 text-xs text-gray-500">
                          Created: {new Date(workflow.created_at).toLocaleDateString()}
                        </p>
                      </div>
                      <CogIcon className="h-6 w-6 text-gray-400" />
                    </div>
                  </div>
                ))}

                {workflows.length === 0 && (
                  <div className="text-center py-8">
                    <CogIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-300">No workflows available</h3>
                    <p className="mt-1 text-sm text-gray-400">
                      Create a workflow to get started.
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Workflow Execution */}
        <div className="bg-gray-800 shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-white mb-4">
              Execute Workflow
            </h3>

            {selectedWorkflow ? (
              <div className="space-y-4">
                <div>
                  <h4 className="text-md font-medium text-white">{selectedWorkflow.name}</h4>
                  <p className="text-sm text-gray-400">{selectedWorkflow.description}</p>
                </div>

                {/* Input Parameters */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Input Parameters (JSON)
                  </label>
                  <textarea
                    className="block w-full h-32 px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                    placeholder='{"key": "value"}'
                    value={JSON.stringify(inputs, null, 2)}
                    onChange={(e) => {
                      try {
                        setInputs(JSON.parse(e.target.value || '{}'));
                      } catch {
                        // Invalid JSON, keep the text as is
                      }
                    }}
                  />
                </div>

                <button
                  onClick={handleExecute}
                  disabled={loading}
                  className="w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 disabled:opacity-50"
                >
                  <PlayIcon className="h-4 w-4 mr-2" />
                  {loading ? 'Executing...' : 'Execute Workflow'}
                </button>
              </div>
            ) : (
              <div className="text-center py-8">
                <PlayIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-300">Select a workflow</h3>
                <p className="mt-1 text-sm text-gray-400">
                  Choose a workflow from the list to execute it.
                </p>
              </div>
            )}

            {error && (
              <div className="mt-4 bg-red-50 border border-red-200 rounded-md p-4">
                <div className="text-sm text-red-700">{error}</div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Execution History */}
      <div className="bg-gray-800 shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-white mb-4">
            Execution History
          </h3>

          {executions.length === 0 ? (
            <div className="text-center py-8">
              <ClockIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-300">No executions yet</h3>
              <p className="mt-1 text-sm text-gray-400">
                Execute a workflow to see the history here.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {executions.map((execution) => (
                <div
                  key={execution.execution_id}
                  className="border border-gray-600 rounded-lg p-4"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(execution.status)}
                        <h4 className="text-lg font-medium text-white">
                          {execution.workflow_id}
                        </h4>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(execution.status)}`}>
                          {execution.status}
                        </span>
                      </div>
                      <p className="mt-1 text-sm text-gray-400">
                        Execution ID: {execution.execution_id}
                      </p>
                      {execution.started_at && (
                        <p className="mt-1 text-xs text-gray-500">
                          Started: {new Date(execution.started_at).toLocaleString()}
                        </p>
                      )}
                      {execution.completed_at && (
                        <p className="mt-1 text-xs text-gray-500">
                          Completed: {new Date(execution.completed_at).toLocaleString()}
                        </p>
                      )}
                    </div>
                    <button
                      onClick={() => dispatch(getWorkflowStatus(execution.execution_id))}
                      className="text-cyan-400 hover:text-cyan-300 text-sm"
                    >
                      Refresh
                    </button>
                  </div>

                  {execution.outputs && Object.keys(execution.outputs).length > 0 && (
                    <div className="mt-3">
                      <h5 className="text-sm font-medium text-gray-300">Outputs:</h5>
                      <pre className="mt-1 text-xs text-gray-400 bg-gray-700 p-2 rounded overflow-x-auto">
                        {JSON.stringify(execution.outputs, null, 2)}
                      </pre>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default WorkflowExecutor;
