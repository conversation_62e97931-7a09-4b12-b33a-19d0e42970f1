/*! For license information please see main.38999409.js.LICENSE.txt */
(()=>{"use strict";var e={43:(e,t,n)=>{e.exports=n(202)},82:(e,t)=>{var n,r=Symbol.for("react.element"),a=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),s=Symbol.for("react.provider"),u=Symbol.for("react.context"),c=Symbol.for("react.server_context"),d=Symbol.for("react.forward_ref"),f=Symbol.for("react.suspense"),p=Symbol.for("react.suspense_list"),h=Symbol.for("react.memo"),m=Symbol.for("react.lazy"),g=Symbol.for("react.offscreen");function v(e){if("object"===typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case o:case i:case l:case f:case p:return e;default:switch(e=e&&e.$$typeof){case c:case u:case d:case m:case h:case s:return e;default:return t}}case a:return t}}}n=Symbol.for("react.module.reference")},86:(e,t,n)=>{n(82)},153:(e,t,n)=>{var r=n(43),a=Symbol.for("react.element"),o=Symbol.for("react.fragment"),l=Object.prototype.hasOwnProperty,i=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,s={key:!0,ref:!0,__self:!0,__source:!0};function u(e,t,n){var r,o={},u=null,c=null;for(r in void 0!==n&&(u=""+n),void 0!==t.key&&(u=""+t.key),void 0!==t.ref&&(c=t.ref),t)l.call(t,r)&&!s.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===o[r]&&(o[r]=t[r]);return{$$typeof:a,type:e,key:u,ref:c,props:o,_owner:i.current}}t.Fragment=o,t.jsx=u,t.jsxs=u},202:(e,t)=>{var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),l=Symbol.for("react.profiler"),i=Symbol.for("react.provider"),s=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator;var h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function v(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}function y(){}function x(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}v.prototype.isReactComponent={},v.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},v.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},y.prototype=v.prototype;var b=x.prototype=new y;b.constructor=x,m(b,v.prototype),b.isPureReactComponent=!0;var w=Array.isArray,j=Object.prototype.hasOwnProperty,N={current:null},k={key:!0,ref:!0,__self:!0,__source:!0};function S(e,t,r){var a,o={},l=null,i=null;if(null!=t)for(a in void 0!==t.ref&&(i=t.ref),void 0!==t.key&&(l=""+t.key),t)j.call(t,a)&&!k.hasOwnProperty(a)&&(o[a]=t[a]);var s=arguments.length-2;if(1===s)o.children=r;else if(1<s){for(var u=Array(s),c=0;c<s;c++)u[c]=arguments[c+2];o.children=u}if(e&&e.defaultProps)for(a in s=e.defaultProps)void 0===o[a]&&(o[a]=s[a]);return{$$typeof:n,type:e,key:l,ref:i,props:o,_owner:N.current}}function E(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var C=/\/+/g;function O(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function P(e,t,a,o,l){var i=typeof e;"undefined"!==i&&"boolean"!==i||(e=null);var s=!1;if(null===e)s=!0;else switch(i){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case n:case r:s=!0}}if(s)return l=l(s=e),e=""===o?"."+O(s,0):o,w(l)?(a="",null!=e&&(a=e.replace(C,"$&/")+"/"),P(l,t,a,"",(function(e){return e}))):null!=l&&(E(l)&&(l=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(l,a+(!l.key||s&&s.key===l.key?"":(""+l.key).replace(C,"$&/")+"/")+e)),t.push(l)),1;if(s=0,o=""===o?".":o+":",w(e))for(var u=0;u<e.length;u++){var c=o+O(i=e[u],u);s+=P(i,t,a,c,l)}else if(c=function(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=p&&e[p]||e["@@iterator"])?e:null}(e),"function"===typeof c)for(e=c.call(e),u=0;!(i=e.next()).done;)s+=P(i=i.value,t,a,c=o+O(i,u++),l);else if("object"===i)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function _(e,t,n){if(null==e)return e;var r=[],a=0;return P(e,r,"","",(function(e){return t.call(n,e,a++)})),r}function R(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var L={current:null},T={transition:null},A={ReactCurrentDispatcher:L,ReactCurrentBatchConfig:T,ReactCurrentOwner:N};function M(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:_,forEach:function(e,t,n){_(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return _(e,(function(){t++})),t},toArray:function(e){return _(e,(function(e){return e}))||[]},only:function(e){if(!E(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=v,t.Fragment=a,t.Profiler=l,t.PureComponent=x,t.StrictMode=o,t.Suspense=c,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=A,t.act=M,t.cloneElement=function(e,t,r){if(null===e||void 0===e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var a=m({},e.props),o=e.key,l=e.ref,i=e._owner;if(null!=t){if(void 0!==t.ref&&(l=t.ref,i=N.current),void 0!==t.key&&(o=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(u in t)j.call(t,u)&&!k.hasOwnProperty(u)&&(a[u]=void 0===t[u]&&void 0!==s?s[u]:t[u])}var u=arguments.length-2;if(1===u)a.children=r;else if(1<u){s=Array(u);for(var c=0;c<u;c++)s[c]=arguments[c+2];a.children=s}return{$$typeof:n,type:e.type,key:o,ref:l,props:a,_owner:i}},t.createContext=function(e){return(e={$$typeof:s,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:i,_context:e},e.Consumer=e},t.createElement=S,t.createFactory=function(e){var t=S.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=E,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:R}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=T.transition;T.transition={};try{e()}finally{T.transition=t}},t.unstable_act=M,t.useCallback=function(e,t){return L.current.useCallback(e,t)},t.useContext=function(e){return L.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return L.current.useDeferredValue(e)},t.useEffect=function(e,t){return L.current.useEffect(e,t)},t.useId=function(){return L.current.useId()},t.useImperativeHandle=function(e,t,n){return L.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return L.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return L.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return L.current.useMemo(e,t)},t.useReducer=function(e,t,n){return L.current.useReducer(e,t,n)},t.useRef=function(e){return L.current.useRef(e)},t.useState=function(e){return L.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return L.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return L.current.useTransition()},t.version="18.3.1"},219:(e,t,n)=>{var r=n(763),a={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},o={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},l={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},i={};function s(e){return r.isMemo(e)?l:i[e.$$typeof]||a}i[r.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},i[r.Memo]=l;var u=Object.defineProperty,c=Object.getOwnPropertyNames,d=Object.getOwnPropertySymbols,f=Object.getOwnPropertyDescriptor,p=Object.getPrototypeOf,h=Object.prototype;e.exports=function e(t,n,r){if("string"!==typeof n){if(h){var a=p(n);a&&a!==h&&e(t,a,r)}var l=c(n);d&&(l=l.concat(d(n)));for(var i=s(t),m=s(n),g=0;g<l.length;++g){var v=l[g];if(!o[v]&&(!r||!r[v])&&(!m||!m[v])&&(!i||!i[v])){var y=f(n,v);try{u(t,v,y)}catch(x){}}}}return t}},234:(e,t)=>{function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<o(a,t)))break e;e[r]=t,e[n]=a,n=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,l=a>>>1;r<l;){var i=2*(r+1)-1,s=e[i],u=i+1,c=e[u];if(0>o(s,n))u<a&&0>o(c,s)?(e[r]=c,e[u]=n,r=u):(e[r]=s,e[i]=n,r=i);else{if(!(u<a&&0>o(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function o(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"===typeof performance&&"function"===typeof performance.now){var l=performance;t.unstable_now=function(){return l.now()}}else{var i=Date,s=i.now();t.unstable_now=function(){return i.now()-s}}var u=[],c=[],d=1,f=null,p=3,h=!1,m=!1,g=!1,v="function"===typeof setTimeout?setTimeout:null,y="function"===typeof clearTimeout?clearTimeout:null,x="undefined"!==typeof setImmediate?setImmediate:null;function b(e){for(var t=r(c);null!==t;){if(null===t.callback)a(c);else{if(!(t.startTime<=e))break;a(c),t.sortIndex=t.expirationTime,n(u,t)}t=r(c)}}function w(e){if(g=!1,b(e),!m)if(null!==r(u))m=!0,T(j);else{var t=r(c);null!==t&&A(w,t.startTime-e)}}function j(e,n){m=!1,g&&(g=!1,y(E),E=-1),h=!0;var o=p;try{for(b(n),f=r(u);null!==f&&(!(f.expirationTime>n)||e&&!P());){var l=f.callback;if("function"===typeof l){f.callback=null,p=f.priorityLevel;var i=l(f.expirationTime<=n);n=t.unstable_now(),"function"===typeof i?f.callback=i:f===r(u)&&a(u),b(n)}else a(u);f=r(u)}if(null!==f)var s=!0;else{var d=r(c);null!==d&&A(w,d.startTime-n),s=!1}return s}finally{f=null,p=o,h=!1}}"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var N,k=!1,S=null,E=-1,C=5,O=-1;function P(){return!(t.unstable_now()-O<C)}function _(){if(null!==S){var e=t.unstable_now();O=e;var n=!0;try{n=S(!0,e)}finally{n?N():(k=!1,S=null)}}else k=!1}if("function"===typeof x)N=function(){x(_)};else if("undefined"!==typeof MessageChannel){var R=new MessageChannel,L=R.port2;R.port1.onmessage=_,N=function(){L.postMessage(null)}}else N=function(){v(_,0)};function T(e){S=e,k||(k=!0,N())}function A(e,n){E=v((function(){e(t.unstable_now())}),n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){m||h||(m=!0,T(j))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):C=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_getFirstCallbackNode=function(){return r(u)},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var n=p;p=t;try{return e()}finally{p=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=p;p=e;try{return t()}finally{p=n}},t.unstable_scheduleCallback=function(e,a,o){var l=t.unstable_now();switch("object"===typeof o&&null!==o?o="number"===typeof(o=o.delay)&&0<o?l+o:l:o=l,e){case 1:var i=-1;break;case 2:i=250;break;case 5:i=1073741823;break;case 4:i=1e4;break;default:i=5e3}return e={id:d++,callback:a,priorityLevel:e,startTime:o,expirationTime:i=o+i,sortIndex:-1},o>l?(e.sortIndex=o,n(c,e),null===r(u)&&e===r(c)&&(g?(y(E),E=-1):g=!0,A(w,o-l))):(e.sortIndex=i,n(u,e),m||h||(m=!0,T(j))),e},t.unstable_shouldYield=P,t.unstable_wrapCallback=function(e){var t=p;return function(){var n=p;p=t;try{return e.apply(this,arguments)}finally{p=n}}}},330:(e,t,n)=>{var r=n(43);var a="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t},o=r.useState,l=r.useEffect,i=r.useLayoutEffect,s=r.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!a(e,n)}catch(r){return!0}}var c="undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=o({inst:{value:n,getSnapshot:t}}),a=r[0].inst,c=r[1];return i((function(){a.value=n,a.getSnapshot=t,u(a)&&c({inst:a})}),[e,n,t]),l((function(){return u(a)&&c({inst:a}),e((function(){u(a)&&c({inst:a})}))}),[e]),s(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:c},391:(e,t,n)=>{var r=n(950);t.createRoot=r.createRoot,t.hydrateRoot=r.hydrateRoot},443:(e,t,n)=>{e.exports=n(717)},461:(e,t,n)=>{e.exports=n(330)},579:(e,t,n)=>{e.exports=n(153)},717:(e,t,n)=>{var r=n(43),a=n(461);var o="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t},l=a.useSyncExternalStore,i=r.useRef,s=r.useEffect,u=r.useMemo,c=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,a){var d=i(null);if(null===d.current){var f={hasValue:!1,value:null};d.current=f}else f=d.current;d=u((function(){function e(e){if(!s){if(s=!0,l=e,e=r(e),void 0!==a&&f.hasValue){var t=f.value;if(a(t,e))return i=t}return i=e}if(t=i,o(l,e))return t;var n=r(e);return void 0!==a&&a(t,n)?(l=e,t):(l=e,i=n)}var l,i,s=!1,u=void 0===n?null:n;return[function(){return e(t())},null===u?void 0:function(){return e(u())}]}),[t,n,r,a]);var p=l(e,d[0],d[1]);return s((function(){f.hasValue=!0,f.value=p}),[p]),c(p),p}},730:(e,t,n)=>{var r=n(43),a=n(853);function o(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var l=new Set,i={};function s(e,t){u(e,t),u(e+"Capture",t)}function u(e,t){for(i[e]=t,e=0;e<t.length;e++)l.add(t[e])}var c=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),d=Object.prototype.hasOwnProperty,f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,p={},h={};function m(e,t,n,r,a,o,l){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=a,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=l}var g={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach((function(e){g[e]=new m(e,0,!1,e,null,!1,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t=e[0];g[t]=new m(t,1,!1,e[1],null,!1,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){g[e]=new m(e,2,!1,e.toLowerCase(),null,!1,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){g[e]=new m(e,2,!1,e,null,!1,!1)})),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach((function(e){g[e]=new m(e,3,!1,e.toLowerCase(),null,!1,!1)})),["checked","multiple","muted","selected"].forEach((function(e){g[e]=new m(e,3,!0,e,null,!1,!1)})),["capture","download"].forEach((function(e){g[e]=new m(e,4,!1,e,null,!1,!1)})),["cols","rows","size","span"].forEach((function(e){g[e]=new m(e,6,!1,e,null,!1,!1)})),["rowSpan","start"].forEach((function(e){g[e]=new m(e,5,!1,e.toLowerCase(),null,!1,!1)}));var v=/[\-:]([a-z])/g;function y(e){return e[1].toUpperCase()}function x(e,t,n,r){var a=g.hasOwnProperty(t)?g[t]:null;(null!==a?0!==a.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null===t||"undefined"===typeof t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,a,r)&&(n=null),r||null===a?function(e){return!!d.call(h,e)||!d.call(p,e)&&(f.test(e)?h[e]=!0:(p[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):a.mustUseProperty?e[a.propertyName]=null===n?3!==a.type&&"":n:(t=a.attributeName,r=a.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(a=a.type)||4===a&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach((function(e){var t=e.replace(v,y);g[t]=new m(t,1,!1,e,null,!1,!1)})),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach((function(e){var t=e.replace(v,y);g[t]=new m(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(v,y);g[t]=new m(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)})),["tabIndex","crossOrigin"].forEach((function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!1,!1)})),g.xlinkHref=new m("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach((function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!0,!0)}));var b=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,w=Symbol.for("react.element"),j=Symbol.for("react.portal"),N=Symbol.for("react.fragment"),k=Symbol.for("react.strict_mode"),S=Symbol.for("react.profiler"),E=Symbol.for("react.provider"),C=Symbol.for("react.context"),O=Symbol.for("react.forward_ref"),P=Symbol.for("react.suspense"),_=Symbol.for("react.suspense_list"),R=Symbol.for("react.memo"),L=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var T=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var A=Symbol.iterator;function M(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=A&&e[A]||e["@@iterator"])?e:null}var I,D=Object.assign;function z(e){if(void 0===I)try{throw Error()}catch(on){var t=on.stack.trim().match(/\n( *(at )?)/);I=t&&t[1]||""}return"\n"+I+e}var F=!1;function U(e,t){if(!e||F)return"";F=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(cn){var r=cn}Reflect.construct(e,[],t)}else{try{t.call()}catch(cn){r=cn}e.call(t.prototype)}else{try{throw Error()}catch(cn){r=cn}e()}}catch(cn){if(cn&&r&&"string"===typeof cn.stack){for(var a=cn.stack.split("\n"),o=r.stack.split("\n"),l=a.length-1,i=o.length-1;1<=l&&0<=i&&a[l]!==o[i];)i--;for(;1<=l&&0<=i;l--,i--)if(a[l]!==o[i]){if(1!==l||1!==i)do{if(l--,0>--i||a[l]!==o[i]){var s="\n"+a[l].replace(" at new "," at ");return e.displayName&&s.includes("<anonymous>")&&(s=s.replace("<anonymous>",e.displayName)),s}}while(1<=l&&0<=i);break}}}finally{F=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?z(e):""}function B(e){switch(e.tag){case 5:return z(e.type);case 16:return z("Lazy");case 13:return z("Suspense");case 19:return z("SuspenseList");case 0:case 2:case 15:return e=U(e.type,!1);case 11:return e=U(e.type.render,!1);case 1:return e=U(e.type,!0);default:return""}}function W(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case N:return"Fragment";case j:return"Portal";case S:return"Profiler";case k:return"StrictMode";case P:return"Suspense";case _:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case C:return(e.displayName||"Context")+".Consumer";case E:return(e._context.displayName||"Context")+".Provider";case O:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case R:return null!==(t=e.displayName||null)?t:W(e.type)||"Memo";case L:t=e._payload,e=e._init;try{return W(e(t))}catch(on){}}return null}function V(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return W(t);case 8:return t===k?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"===typeof t)return t.displayName||t.name||null;if("string"===typeof t)return t}return null}function $(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function q(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function H(e){e._valueTracker||(e._valueTracker=function(e){var t=q(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var a=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,o.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function Q(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=q(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function K(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(hn){return e.body}}function J(e,t){var n=t.checked;return D({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function X(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=$(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function Z(e,t){null!=(t=t.checked)&&x(e,"checked",t,!1)}function Y(e,t){Z(e,t);var n=$(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,$(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function G(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&K(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+$(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(o(91));return D({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ae(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(o(92));if(te(n)){if(1<n.length)throw Error(o(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:$(n)}}function oe(e,t){var n=$(t.value),r=$(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function le(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function ie(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function se(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?ie(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ue,ce=function(e){return"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(t,n,r,a){MSApp.execUnsafeLocalFunction((function(){return e(t,n)}))}:e}((function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ue=ue||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ue.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}}));function de(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var fe={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},pe=["Webkit","ms","Moz","O"];function he(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||fe.hasOwnProperty(e)&&fe[e]?(""+t).trim():t+"px"}function me(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),a=he(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,a):e[n]=a}}Object.keys(fe).forEach((function(e){pe.forEach((function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),fe[t]=fe[e]}))}));var ge=D({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ve(e,t){if(t){if(ge[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(o(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(o(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(o(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(o(62))}}function ye(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var xe=null;function be(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var we=null,je=null,Ne=null;function ke(e){if(e=_a(e)){if("function"!==typeof we)throw Error(o(280));var t=e.stateNode;t&&(t=La(t),we(e.stateNode,e.type,t))}}function Se(e){je?Ne?Ne.push(e):Ne=[e]:je=e}function Ee(){if(je){var e=je,t=Ne;if(Ne=je=null,ke(e),t)for(e=0;e<t.length;e++)ke(t[e])}}function Ce(e,t){return e(t)}function Oe(){}var Pe=!1;function _e(e,t,n){if(Pe)return e(t,n);Pe=!0;try{return Ce(e,t,n)}finally{Pe=!1,(null!==je||null!==Ne)&&(Oe(),Ee())}}function Re(e,t){var n=e.stateNode;if(null===n)return null;var r=La(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(o(231,t,typeof n));return n}var Le=!1;if(c)try{var Te={};Object.defineProperty(Te,"passive",{get:function(){Le=!0}}),window.addEventListener("test",Te,Te),window.removeEventListener("test",Te,Te)}catch(rn){Le=!1}function Ae(e,t,n,r,a,o,l,i,s){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(mn){this.onError(mn)}}var Me=!1,Ie=null,De=!1,ze=null,Fe={onError:function(e){Me=!0,Ie=e}};function Ue(e,t,n,r,a,o,l,i,s){Me=!1,Ie=null,Ae.apply(Fe,arguments)}function Be(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function We(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function Ve(e){if(Be(e)!==e)throw Error(o(188))}function $e(e){return e=function(e){var t=e.alternate;if(!t){if(null===(t=Be(e)))throw Error(o(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var l=a.alternate;if(null===l){if(null!==(r=a.return)){n=r;continue}break}if(a.child===l.child){for(l=a.child;l;){if(l===n)return Ve(a),e;if(l===r)return Ve(a),t;l=l.sibling}throw Error(o(188))}if(n.return!==r.return)n=a,r=l;else{for(var i=!1,s=a.child;s;){if(s===n){i=!0,n=a,r=l;break}if(s===r){i=!0,r=a,n=l;break}s=s.sibling}if(!i){for(s=l.child;s;){if(s===n){i=!0,n=l,r=a;break}if(s===r){i=!0,r=l,n=a;break}s=s.sibling}if(!i)throw Error(o(189))}}if(n.alternate!==r)throw Error(o(190))}if(3!==n.tag)throw Error(o(188));return n.stateNode.current===n?e:t}(e),null!==e?qe(e):null}function qe(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=qe(e);if(null!==t)return t;e=e.sibling}return null}var He=a.unstable_scheduleCallback,Qe=a.unstable_cancelCallback,Ke=a.unstable_shouldYield,Je=a.unstable_requestPaint,Xe=a.unstable_now,Ze=a.unstable_getCurrentPriorityLevel,Ye=a.unstable_ImmediatePriority,Ge=a.unstable_UserBlockingPriority,et=a.unstable_NormalPriority,tt=a.unstable_LowPriority,nt=a.unstable_IdlePriority,rt=null,at=null;var ot=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(lt(e)/it|0)|0},lt=Math.log,it=Math.LN2;var st=64,ut=4194304;function ct(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function dt(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,a=e.suspendedLanes,o=e.pingedLanes,l=268435455&n;if(0!==l){var i=l&~a;0!==i?r=ct(i):0!==(o&=l)&&(r=ct(o))}else 0!==(l=n&~a)?r=ct(l):0!==o&&(r=ct(o));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&a)&&((a=r&-r)>=(o=t&-t)||16===a&&0!==(4194240&o)))return t;if(0!==(4&r)&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)a=1<<(n=31-ot(t)),r|=e[n],t&=~a;return r}function ft(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function pt(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function ht(){var e=st;return 0===(4194240&(st<<=1))&&(st=64),e}function mt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function gt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-ot(t)]=n}function vt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-ot(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}var yt=0;function xt(e){return 1<(e&=-e)?4<e?0!==(268435455&e)?16:536870912:4:1}var bt,wt,jt,Nt,kt,St=!1,Et=[],Ct=null,Ot=null,Pt=null,_t=new Map,Rt=new Map,Lt=[],Tt="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function At(e,t){switch(e){case"focusin":case"focusout":Ct=null;break;case"dragenter":case"dragleave":Ot=null;break;case"mouseover":case"mouseout":Pt=null;break;case"pointerover":case"pointerout":_t.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Rt.delete(t.pointerId)}}function Mt(e,t,n,r,a,o){return null===e||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[a]},null!==t&&(null!==(t=_a(t))&&wt(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function It(e){var t=Pa(e.target);if(null!==t){var n=Be(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=We(n)))return e.blockedOn=t,void kt(e.priority,(function(){jt(n)}))}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Dt(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Kt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=_a(n))&&wt(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);xe=r,n.target.dispatchEvent(r),xe=null,t.shift()}return!0}function zt(e,t,n){Dt(e)&&n.delete(t)}function Ft(){St=!1,null!==Ct&&Dt(Ct)&&(Ct=null),null!==Ot&&Dt(Ot)&&(Ot=null),null!==Pt&&Dt(Pt)&&(Pt=null),_t.forEach(zt),Rt.forEach(zt)}function Ut(e,t){e.blockedOn===t&&(e.blockedOn=null,St||(St=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,Ft)))}function Bt(e){function t(t){return Ut(t,e)}if(0<Et.length){Ut(Et[0],e);for(var n=1;n<Et.length;n++){var r=Et[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==Ct&&Ut(Ct,e),null!==Ot&&Ut(Ot,e),null!==Pt&&Ut(Pt,e),_t.forEach(t),Rt.forEach(t),n=0;n<Lt.length;n++)(r=Lt[n]).blockedOn===e&&(r.blockedOn=null);for(;0<Lt.length&&null===(n=Lt[0]).blockedOn;)It(n),null===n.blockedOn&&Lt.shift()}var Wt=b.ReactCurrentBatchConfig,Vt=!0;function $t(e,t,n,r){var a=yt,o=Wt.transition;Wt.transition=null;try{yt=1,Ht(e,t,n,r)}finally{yt=a,Wt.transition=o}}function qt(e,t,n,r){var a=yt,o=Wt.transition;Wt.transition=null;try{yt=4,Ht(e,t,n,r)}finally{yt=a,Wt.transition=o}}function Ht(e,t,n,r){if(Vt){var a=Kt(e,t,n,r);if(null===a)ta(e,t,r,Qt,n),At(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return Ct=Mt(Ct,e,t,n,r,a),!0;case"dragenter":return Ot=Mt(Ot,e,t,n,r,a),!0;case"mouseover":return Pt=Mt(Pt,e,t,n,r,a),!0;case"pointerover":var o=a.pointerId;return _t.set(o,Mt(_t.get(o)||null,e,t,n,r,a)),!0;case"gotpointercapture":return o=a.pointerId,Rt.set(o,Mt(Rt.get(o)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(At(e,r),4&t&&-1<Tt.indexOf(e)){for(;null!==a;){var o=_a(a);if(null!==o&&bt(o),null===(o=Kt(e,t,n,r))&&ta(e,t,r,Qt,n),o===a)break;a=o}null!==a&&r.stopPropagation()}else ta(e,t,r,null,n)}}var Qt=null;function Kt(e,t,n,r){if(Qt=null,null!==(e=Pa(e=be(r))))if(null===(t=Be(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=We(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Qt=e,null}function Jt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Ze()){case Ye:return 1;case Ge:return 4;case et:case tt:return 16;case nt:return 536870912;default:return 16}default:return 16}}var Xt=null,Zt=null,en=null;function tn(){if(en)return en;var e,t,n=Zt,r=n.length,a="value"in Xt?Xt.value:Xt.textContent,o=a.length;for(e=0;e<r&&n[e]===a[e];e++);var l=r-e;for(t=1;t<=l&&n[r-t]===a[o-t];t++);return en=a.slice(e,1<t?1-t:void 0)}function nn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function ln(){return!0}function sn(){return!1}function un(e){function t(t,n,r,a,o){for(var l in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=o,this.currentTarget=null,e)e.hasOwnProperty(l)&&(t=e[l],this[l]=t?t(a):a[l]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?ln:sn,this.isPropagationStopped=sn,this}return D(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=ln)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=ln)},persist:function(){},isPersistent:ln}),t}var pn,gn,vn,yn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},xn=un(yn),bn=D({},yn,{view:0,detail:0}),wn=un(bn),jn=D({},bn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:In,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==vn&&(vn&&"mousemove"===e.type?(pn=e.screenX-vn.screenX,gn=e.screenY-vn.screenY):gn=pn=0,vn=e),pn)},movementY:function(e){return"movementY"in e?e.movementY:gn}}),Nn=un(jn),kn=un(D({},jn,{dataTransfer:0})),Sn=un(D({},bn,{relatedTarget:0})),En=un(D({},yn,{animationName:0,elapsedTime:0,pseudoElement:0})),Cn=D({},yn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Pn=un(Cn),_n=un(D({},yn,{data:0})),Rn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Ln={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},An={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Mn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=An[e])&&!!t[e]}function In(){return Mn}var Dn=D({},bn,{key:function(e){if(e.key){var t=Rn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=nn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?Ln[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:In,charCode:function(e){return"keypress"===e.type?nn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?nn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),zn=un(Dn),Fn=un(D({},jn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Un=un(D({},bn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:In})),Bn=un(D({},yn,{propertyName:0,elapsedTime:0,pseudoElement:0})),Wn=D({},jn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Vn=un(Wn),$n=[9,13,27,32],qn=c&&"CompositionEvent"in window,Hn=null;c&&"documentMode"in document&&(Hn=document.documentMode);var Qn=c&&"TextEvent"in window&&!Hn,Kn=c&&(!qn||Hn&&8<Hn&&11>=Hn),Jn=String.fromCharCode(32),Xn=!1;function Zn(e,t){switch(e){case"keyup":return-1!==$n.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Yn(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Gn=!1;var er={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function tr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!er[e.type]:"textarea"===t}function nr(e,t,n,r){Se(r),0<(t=ra(t,"onChange")).length&&(n=new xn("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var rr=null,ar=null;function or(e){Jr(e,0)}function lr(e){if(Q(Ra(e)))return e}function ir(e,t){if("change"===e)return t}var sr=!1;if(c){var ur;if(c){var cr="oninput"in document;if(!cr){var dr=document.createElement("div");dr.setAttribute("oninput","return;"),cr="function"===typeof dr.oninput}ur=cr}else ur=!1;sr=ur&&(!document.documentMode||9<document.documentMode)}function fr(){rr&&(rr.detachEvent("onpropertychange",pr),ar=rr=null)}function pr(e){if("value"===e.propertyName&&lr(ar)){var t=[];nr(t,ar,e,be(e)),_e(or,t)}}function hr(e,t,n){"focusin"===e?(fr(),ar=n,(rr=t).attachEvent("onpropertychange",pr)):"focusout"===e&&fr()}function mr(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return lr(ar)}function gr(e,t){if("click"===e)return lr(t)}function vr(e,t){if("input"===e||"change"===e)return lr(t)}var yr="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function xr(e,t){if(yr(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!d.call(t,a)||!yr(e[a],t[a]))return!1}return!0}function br(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function wr(e,t){var n,r=br(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=br(r)}}function jr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?jr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function Nr(){for(var e=window,t=K();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(dn){n=!1}if(!n)break;t=K((e=t.contentWindow).document)}return t}function kr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function Sr(e){var t=Nr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&jr(n.ownerDocument.documentElement,n)){if(null!==r&&kr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var a=n.textContent.length,o=Math.min(r.start,a);r=void 0===r.end?o:Math.min(r.end,a),!e.extend&&o>r&&(a=r,r=o,o=a),a=wr(n,o);var l=wr(n,r);a&&l&&(1!==e.rangeCount||e.anchorNode!==a.node||e.anchorOffset!==a.offset||e.focusNode!==l.node||e.focusOffset!==l.offset)&&((t=t.createRange()).setStart(a.node,a.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(l.node,l.offset)):(t.setEnd(l.node,l.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"===typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Er=c&&"documentMode"in document&&11>=document.documentMode,Cr=null,Or=null,Pr=null,_r=!1;function Rr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;_r||null==Cr||Cr!==K(r)||("selectionStart"in(r=Cr)&&kr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},Pr&&xr(Pr,r)||(Pr=r,0<(r=ra(Or,"onSelect")).length&&(t=new xn("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Cr)))}function Lr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Tr={animationend:Lr("Animation","AnimationEnd"),animationiteration:Lr("Animation","AnimationIteration"),animationstart:Lr("Animation","AnimationStart"),transitionend:Lr("Transition","TransitionEnd")},Ar={},Mr={};function Ir(e){if(Ar[e])return Ar[e];if(!Tr[e])return e;var t,n=Tr[e];for(t in n)if(n.hasOwnProperty(t)&&t in Mr)return Ar[e]=n[t];return e}c&&(Mr=document.createElement("div").style,"AnimationEvent"in window||(delete Tr.animationend.animation,delete Tr.animationiteration.animation,delete Tr.animationstart.animation),"TransitionEvent"in window||delete Tr.transitionend.transition);var Dr=Ir("animationend"),zr=Ir("animationiteration"),Fr=Ir("animationstart"),Ur=Ir("transitionend"),Br=new Map,Wr="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Vr(e,t){Br.set(e,t),s(t,[e])}for(var $r=0;$r<Wr.length;$r++){var qr=Wr[$r];Vr(qr.toLowerCase(),"on"+(qr[0].toUpperCase()+qr.slice(1)))}Vr(Dr,"onAnimationEnd"),Vr(zr,"onAnimationIteration"),Vr(Fr,"onAnimationStart"),Vr("dblclick","onDoubleClick"),Vr("focusin","onFocus"),Vr("focusout","onBlur"),Vr(Ur,"onTransitionEnd"),u("onMouseEnter",["mouseout","mouseover"]),u("onMouseLeave",["mouseout","mouseover"]),u("onPointerEnter",["pointerout","pointerover"]),u("onPointerLeave",["pointerout","pointerover"]),s("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),s("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),s("onBeforeInput",["compositionend","keypress","textInput","paste"]),s("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Hr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Qr=new Set("cancel close invalid load scroll toggle".split(" ").concat(Hr));function Kr(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,a,l,i,s,u){if(Ue.apply(this,arguments),Me){if(!Me)throw Error(o(198));var c=Ie;Me=!1,Ie=null,De||(De=!0,ze=c)}}(r,t,void 0,e),e.currentTarget=null}function Jr(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var l=r.length-1;0<=l;l--){var i=r[l],s=i.instance,u=i.currentTarget;if(i=i.listener,s!==o&&a.isPropagationStopped())break e;Kr(a,i,u),o=s}else for(l=0;l<r.length;l++){if(s=(i=r[l]).instance,u=i.currentTarget,i=i.listener,s!==o&&a.isPropagationStopped())break e;Kr(a,i,u),o=s}}}if(De)throw e=ze,De=!1,ze=null,e}function Xr(e,t){var n=t[Ea];void 0===n&&(n=t[Ea]=new Set);var r=e+"__bubble";n.has(r)||(ea(t,e,2,!1),n.add(r))}function Zr(e,t,n){var r=0;t&&(r|=4),ea(n,e,r,t)}var Yr="_reactListening"+Math.random().toString(36).slice(2);function Gr(e){if(!e[Yr]){e[Yr]=!0,l.forEach((function(t){"selectionchange"!==t&&(Qr.has(t)||Zr(t,!1,e),Zr(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Yr]||(t[Yr]=!0,Zr("selectionchange",!1,t))}}function ea(e,t,n,r){switch(Jt(t)){case 1:var a=$t;break;case 4:a=qt;break;default:a=Ht}n=a.bind(null,t,n,e),a=void 0,!Le||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function ta(e,t,n,r,a){var o=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var l=r.tag;if(3===l||4===l){var i=r.stateNode.containerInfo;if(i===a||8===i.nodeType&&i.parentNode===a)break;if(4===l)for(l=r.return;null!==l;){var s=l.tag;if((3===s||4===s)&&((s=l.stateNode.containerInfo)===a||8===s.nodeType&&s.parentNode===a))return;l=l.return}for(;null!==i;){if(null===(l=Pa(i)))return;if(5===(s=l.tag)||6===s){r=o=l;continue e}i=i.parentNode}}r=r.return}_e((function(){var r=o,a=be(n),l=[];e:{var i=Br.get(e);if(void 0!==i){var s=xn,u=e;switch(e){case"keypress":if(0===nn(n))break e;case"keydown":case"keyup":s=zn;break;case"focusin":u="focus",s=Sn;break;case"focusout":u="blur",s=Sn;break;case"beforeblur":case"afterblur":s=Sn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":s=Nn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":s=kn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":s=Un;break;case Dr:case zr:case Fr:s=En;break;case Ur:s=Bn;break;case"scroll":s=wn;break;case"wheel":s=Vn;break;case"copy":case"cut":case"paste":s=Pn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":s=Fn}var c=0!==(4&t),d=!c&&"scroll"===e,f=c?null!==i?i+"Capture":null:i;c=[];for(var p,h=r;null!==h;){var m=(p=h).stateNode;if(5===p.tag&&null!==m&&(p=m,null!==f&&(null!=(m=Re(h,f))&&c.push(na(h,m,p)))),d)break;h=h.return}0<c.length&&(i=new s(i,u,null,n,a),l.push({event:i,listeners:c}))}}if(0===(7&t)){if(s="mouseout"===e||"pointerout"===e,(!(i="mouseover"===e||"pointerover"===e)||n===xe||!(u=n.relatedTarget||n.fromElement)||!Pa(u)&&!u[Sa])&&(s||i)&&(i=a.window===a?a:(i=a.ownerDocument)?i.defaultView||i.parentWindow:window,s?(s=r,null!==(u=(u=n.relatedTarget||n.toElement)?Pa(u):null)&&(u!==(d=Be(u))||5!==u.tag&&6!==u.tag)&&(u=null)):(s=null,u=r),s!==u)){if(c=Nn,m="onMouseLeave",f="onMouseEnter",h="mouse","pointerout"!==e&&"pointerover"!==e||(c=Fn,m="onPointerLeave",f="onPointerEnter",h="pointer"),d=null==s?i:Ra(s),p=null==u?i:Ra(u),(i=new c(m,h+"leave",s,n,a)).target=d,i.relatedTarget=p,m=null,Pa(a)===r&&((c=new c(f,h+"enter",u,n,a)).target=p,c.relatedTarget=d,m=c),d=m,s&&u)e:{for(f=u,h=0,p=c=s;p;p=aa(p))h++;for(p=0,m=f;m;m=aa(m))p++;for(;0<h-p;)c=aa(c),h--;for(;0<p-h;)f=aa(f),p--;for(;h--;){if(c===f||null!==f&&c===f.alternate)break e;c=aa(c),f=aa(f)}c=null}else c=null;null!==s&&oa(l,i,s,c,!1),null!==u&&null!==d&&oa(l,d,u,c,!0)}if("select"===(s=(i=r?Ra(r):window).nodeName&&i.nodeName.toLowerCase())||"input"===s&&"file"===i.type)var g=ir;else if(tr(i))if(sr)g=vr;else{g=mr;var v=hr}else(s=i.nodeName)&&"input"===s.toLowerCase()&&("checkbox"===i.type||"radio"===i.type)&&(g=gr);switch(g&&(g=g(e,r))?nr(l,g,n,a):(v&&v(e,i,r),"focusout"===e&&(v=i._wrapperState)&&v.controlled&&"number"===i.type&&ee(i,"number",i.value)),v=r?Ra(r):window,e){case"focusin":(tr(v)||"true"===v.contentEditable)&&(Cr=v,Or=r,Pr=null);break;case"focusout":Pr=Or=Cr=null;break;case"mousedown":_r=!0;break;case"contextmenu":case"mouseup":case"dragend":_r=!1,Rr(l,n,a);break;case"selectionchange":if(Er)break;case"keydown":case"keyup":Rr(l,n,a)}var y;if(qn)e:{switch(e){case"compositionstart":var x="onCompositionStart";break e;case"compositionend":x="onCompositionEnd";break e;case"compositionupdate":x="onCompositionUpdate";break e}x=void 0}else Gn?Zn(e,n)&&(x="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(x="onCompositionStart");x&&(Kn&&"ko"!==n.locale&&(Gn||"onCompositionStart"!==x?"onCompositionEnd"===x&&Gn&&(y=tn()):(Zt="value"in(Xt=a)?Xt.value:Xt.textContent,Gn=!0)),0<(v=ra(r,x)).length&&(x=new _n(x,e,null,n,a),l.push({event:x,listeners:v}),y?x.data=y:null!==(y=Yn(n))&&(x.data=y))),(y=Qn?function(e,t){switch(e){case"compositionend":return Yn(t);case"keypress":return 32!==t.which?null:(Xn=!0,Jn);case"textInput":return(e=t.data)===Jn&&Xn?null:e;default:return null}}(e,n):function(e,t){if(Gn)return"compositionend"===e||!qn&&Zn(e,t)?(e=tn(),en=Zt=Xt=null,Gn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Kn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=ra(r,"onBeforeInput")).length&&(a=new _n("onBeforeInput","beforeinput",null,n,a),l.push({event:a,listeners:r}),a.data=y))}Jr(l,t)}))}function na(e,t,n){return{instance:e,listener:t,currentTarget:n}}function ra(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,o=a.stateNode;5===a.tag&&null!==o&&(a=o,null!=(o=Re(e,n))&&r.unshift(na(e,o,a)),null!=(o=Re(e,t))&&r.push(na(e,o,a))),e=e.return}return r}function aa(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function oa(e,t,n,r,a){for(var o=t._reactName,l=[];null!==n&&n!==r;){var i=n,s=i.alternate,u=i.stateNode;if(null!==s&&s===r)break;5===i.tag&&null!==u&&(i=u,a?null!=(s=Re(n,o))&&l.unshift(na(n,s,i)):a||null!=(s=Re(n,o))&&l.push(na(n,s,i))),n=n.return}0!==l.length&&e.push({event:t,listeners:l})}var la=/\r\n?/g,ia=/\u0000|\uFFFD/g;function sa(e){return("string"===typeof e?e:""+e).replace(la,"\n").replace(ia,"")}function ua(e,t,n){if(t=sa(t),sa(e)!==t&&n)throw Error(o(425))}function ca(){}var da=null,fa=null;function pa(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ha="function"===typeof setTimeout?setTimeout:void 0,ma="function"===typeof clearTimeout?clearTimeout:void 0,ga="function"===typeof Promise?Promise:void 0,va="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof ga?function(e){return ga.resolve(null).then(e).catch(ya)}:ha;function ya(e){setTimeout((function(){throw e}))}function xa(e,t){var n=t,r=0;do{var a=n.nextSibling;if(e.removeChild(n),a&&8===a.nodeType)if("/$"===(n=a.data)){if(0===r)return e.removeChild(a),void Bt(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=a}while(n);Bt(t)}function ba(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function wa(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var ja=Math.random().toString(36).slice(2),Na="__reactFiber$"+ja,ka="__reactProps$"+ja,Sa="__reactContainer$"+ja,Ea="__reactEvents$"+ja,Ca="__reactListeners$"+ja,Oa="__reactHandles$"+ja;function Pa(e){var t=e[Na];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Sa]||n[Na]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=wa(e);null!==e;){if(n=e[Na])return n;e=wa(e)}return t}n=(e=n).parentNode}return null}function _a(e){return!(e=e[Na]||e[Sa])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function Ra(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(o(33))}function La(e){return e[ka]||null}var Ta=[],Aa=-1;function Ma(e){return{current:e}}function Ia(e){0>Aa||(e.current=Ta[Aa],Ta[Aa]=null,Aa--)}function Da(e,t){Aa++,Ta[Aa]=e.current,e.current=t}var za={},Fa=Ma(za),Ua=Ma(!1),Ba=za;function Wa(e,t){var n=e.type.contextTypes;if(!n)return za;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var a,o={};for(a in n)o[a]=t[a];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Va(e){return null!==(e=e.childContextTypes)&&void 0!==e}function $a(){Ia(Ua),Ia(Fa)}function qa(e,t,n){if(Fa.current!==za)throw Error(o(168));Da(Fa,t),Da(Ua,n)}function Ha(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var a in r=r.getChildContext())if(!(a in t))throw Error(o(108,V(e)||"Unknown",a));return D({},n,r)}function Qa(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||za,Ba=Fa.current,Da(Fa,e),Da(Ua,Ua.current),!0}function Ka(e,t,n){var r=e.stateNode;if(!r)throw Error(o(169));n?(e=Ha(e,t,Ba),r.__reactInternalMemoizedMergedChildContext=e,Ia(Ua),Ia(Fa),Da(Fa,e)):Ia(Ua),Da(Ua,n)}var Ja=null,Xa=!1,Za=!1;function Ya(e){null===Ja?Ja=[e]:Ja.push(e)}function Ga(){if(!Za&&null!==Ja){Za=!0;var e=0,t=yt;try{var n=Ja;for(yt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}Ja=null,Xa=!1}catch(a){throw null!==Ja&&(Ja=Ja.slice(e+1)),He(Ye,Ga),a}finally{yt=t,Za=!1}}return null}var eo=[],to=0,no=null,ro=0,ao=[],oo=0,lo=null,io=1,so="";function uo(e,t){eo[to++]=ro,eo[to++]=no,no=e,ro=t}function co(e,t,n){ao[oo++]=io,ao[oo++]=so,ao[oo++]=lo,lo=e;var r=io;e=so;var a=32-ot(r)-1;r&=~(1<<a),n+=1;var o=32-ot(t)+a;if(30<o){var l=a-a%5;o=(r&(1<<l)-1).toString(32),r>>=l,a-=l,io=1<<32-ot(t)+a|n<<a|r,so=o+e}else io=1<<o|n<<a|r,so=e}function fo(e){null!==e.return&&(uo(e,1),co(e,1,0))}function po(e){for(;e===no;)no=eo[--to],eo[to]=null,ro=eo[--to],eo[to]=null;for(;e===lo;)lo=ao[--oo],ao[oo]=null,so=ao[--oo],ao[oo]=null,io=ao[--oo],ao[oo]=null}var ho=null,mo=null,go=!1,vo=null;function yo(e,t){var n=Wu(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function xo(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,ho=e,mo=ba(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,ho=e,mo=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==lo?{id:io,overflow:so}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Wu(18,null,null,0)).stateNode=t,n.return=e,e.child=n,ho=e,mo=null,!0);default:return!1}}function bo(e){return 0!==(1&e.mode)&&0===(128&e.flags)}function wo(e){if(go){var t=mo;if(t){var n=t;if(!xo(e,t)){if(bo(e))throw Error(o(418));t=ba(n.nextSibling);var r=ho;t&&xo(e,t)?yo(r,n):(e.flags=-4097&e.flags|2,go=!1,ho=e)}}else{if(bo(e))throw Error(o(418));e.flags=-4097&e.flags|2,go=!1,ho=e}}}function jo(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;ho=e}function No(e){if(e!==ho)return!1;if(!go)return jo(e),go=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!pa(e.type,e.memoizedProps)),t&&(t=mo)){if(bo(e))throw ko(),Error(o(418));for(;t;)yo(e,t),t=ba(t.nextSibling)}if(jo(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(o(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){mo=ba(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}mo=null}}else mo=ho?ba(e.stateNode.nextSibling):null;return!0}function ko(){for(var e=mo;e;)e=ba(e.nextSibling)}function So(){mo=ho=null,go=!1}function Eo(e){null===vo?vo=[e]:vo.push(e)}var Co=b.ReactCurrentBatchConfig;function Oo(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(o(309));var r=n.stateNode}if(!r)throw Error(o(147,e));var a=r,l=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===l?t.ref:(t=function(e){var t=a.refs;null===e?delete t[l]:t[l]=e},t._stringRef=l,t)}if("string"!==typeof e)throw Error(o(284));if(!n._owner)throw Error(o(290,e))}return e}function Po(e,t){throw e=Object.prototype.toString.call(t),Error(o(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function _o(e){return(0,e._init)(e._payload)}function Ro(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function a(e,t){return(e=$u(e,t)).index=0,e.sibling=null,e}function l(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function i(t){return e&&null===t.alternate&&(t.flags|=2),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=Ku(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function u(e,t,n,r){var o=n.type;return o===N?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===o||"object"===typeof o&&null!==o&&o.$$typeof===L&&_o(o)===t.type)?((r=a(t,n.props)).ref=Oo(e,t,n),r.return=e,r):((r=qu(n.type,n.key,n.props,null,e.mode,r)).ref=Oo(e,t,n),r.return=e,r)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Ju(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function d(e,t,n,r,o){return null===t||7!==t.tag?((t=Hu(n,e.mode,r,o)).return=e,t):((t=a(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t)return(t=Ku(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case w:return(n=qu(t.type,t.key,t.props,null,e.mode,n)).ref=Oo(e,null,t),n.return=e,n;case j:return(t=Ju(t,e.mode,n)).return=e,t;case L:return f(e,(0,t._init)(t._payload),n)}if(te(t)||M(t))return(t=Hu(t,e.mode,n,null)).return=e,t;Po(e,t)}return null}function p(e,t,n,r){var a=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n)return null!==a?null:s(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case w:return n.key===a?u(e,t,n,r):null;case j:return n.key===a?c(e,t,n,r):null;case L:return p(e,t,(a=n._init)(n._payload),r)}if(te(n)||M(n))return null!==a?null:d(e,t,n,r,null);Po(e,n)}return null}function h(e,t,n,r,a){if("string"===typeof r&&""!==r||"number"===typeof r)return s(t,e=e.get(n)||null,""+r,a);if("object"===typeof r&&null!==r){switch(r.$$typeof){case w:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case j:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case L:return h(e,t,n,(0,r._init)(r._payload),a)}if(te(r)||M(r))return d(t,e=e.get(n)||null,r,a,null);Po(t,r)}return null}function m(a,o,i,s){for(var u=null,c=null,d=o,m=o=0,g=null;null!==d&&m<i.length;m++){d.index>m?(g=d,d=null):g=d.sibling;var v=p(a,d,i[m],s);if(null===v){null===d&&(d=g);break}e&&d&&null===v.alternate&&t(a,d),o=l(v,o,m),null===c?u=v:c.sibling=v,c=v,d=g}if(m===i.length)return n(a,d),go&&uo(a,m),u;if(null===d){for(;m<i.length;m++)null!==(d=f(a,i[m],s))&&(o=l(d,o,m),null===c?u=d:c.sibling=d,c=d);return go&&uo(a,m),u}for(d=r(a,d);m<i.length;m++)null!==(g=h(d,a,m,i[m],s))&&(e&&null!==g.alternate&&d.delete(null===g.key?m:g.key),o=l(g,o,m),null===c?u=g:c.sibling=g,c=g);return e&&d.forEach((function(e){return t(a,e)})),go&&uo(a,m),u}function g(a,i,s,u){var c=M(s);if("function"!==typeof c)throw Error(o(150));if(null==(s=c.call(s)))throw Error(o(151));for(var d=c=null,m=i,g=i=0,v=null,y=s.next();null!==m&&!y.done;g++,y=s.next()){m.index>g?(v=m,m=null):v=m.sibling;var x=p(a,m,y.value,u);if(null===x){null===m&&(m=v);break}e&&m&&null===x.alternate&&t(a,m),i=l(x,i,g),null===d?c=x:d.sibling=x,d=x,m=v}if(y.done)return n(a,m),go&&uo(a,g),c;if(null===m){for(;!y.done;g++,y=s.next())null!==(y=f(a,y.value,u))&&(i=l(y,i,g),null===d?c=y:d.sibling=y,d=y);return go&&uo(a,g),c}for(m=r(a,m);!y.done;g++,y=s.next())null!==(y=h(m,a,g,y.value,u))&&(e&&null!==y.alternate&&m.delete(null===y.key?g:y.key),i=l(y,i,g),null===d?c=y:d.sibling=y,d=y);return e&&m.forEach((function(e){return t(a,e)})),go&&uo(a,g),c}return function e(r,o,l,s){if("object"===typeof l&&null!==l&&l.type===N&&null===l.key&&(l=l.props.children),"object"===typeof l&&null!==l){switch(l.$$typeof){case w:e:{for(var u=l.key,c=o;null!==c;){if(c.key===u){if((u=l.type)===N){if(7===c.tag){n(r,c.sibling),(o=a(c,l.props.children)).return=r,r=o;break e}}else if(c.elementType===u||"object"===typeof u&&null!==u&&u.$$typeof===L&&_o(u)===c.type){n(r,c.sibling),(o=a(c,l.props)).ref=Oo(r,c,l),o.return=r,r=o;break e}n(r,c);break}t(r,c),c=c.sibling}l.type===N?((o=Hu(l.props.children,r.mode,s,l.key)).return=r,r=o):((s=qu(l.type,l.key,l.props,null,r.mode,s)).ref=Oo(r,o,l),s.return=r,r=s)}return i(r);case j:e:{for(c=l.key;null!==o;){if(o.key===c){if(4===o.tag&&o.stateNode.containerInfo===l.containerInfo&&o.stateNode.implementation===l.implementation){n(r,o.sibling),(o=a(o,l.children||[])).return=r,r=o;break e}n(r,o);break}t(r,o),o=o.sibling}(o=Ju(l,r.mode,s)).return=r,r=o}return i(r);case L:return e(r,o,(c=l._init)(l._payload),s)}if(te(l))return m(r,o,l,s);if(M(l))return g(r,o,l,s);Po(r,l)}return"string"===typeof l&&""!==l||"number"===typeof l?(l=""+l,null!==o&&6===o.tag?(n(r,o.sibling),(o=a(o,l)).return=r,r=o):(n(r,o),(o=Ku(l,r.mode,s)).return=r,r=o),i(r)):n(r,o)}}var Lo=Ro(!0),To=Ro(!1),Ao=Ma(null),Mo=null,Io=null,Do=null;function zo(){Do=Io=Mo=null}function Fo(e){var t=Ao.current;Ia(Ao),e._currentValue=t}function Uo(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Bo(e,t){Mo=e,Do=Io=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(_i=!0),e.firstContext=null)}function Wo(e){var t=e._currentValue;if(Do!==e)if(e={context:e,memoizedValue:t,next:null},null===Io){if(null===Mo)throw Error(o(308));Io=e,Mo.dependencies={lanes:0,firstContext:e}}else Io=Io.next=e;return t}var Vo=null;function $o(e){null===Vo?Vo=[e]:Vo.push(e)}function qo(e,t,n,r){var a=t.interleaved;return null===a?(n.next=n,$o(t)):(n.next=a.next,a.next=n),t.interleaved=n,Ho(e,r)}function Ho(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var Qo=!1;function Ko(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Jo(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Xo(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Zo(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&Fs)){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,Ho(e,n)}return null===(a=r.interleaved)?(t.next=t,$o(r)):(t.next=a.next,a.next=t),r.interleaved=t,Ho(e,n)}function Yo(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194240&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,vt(e,n)}}function Go(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,o=null;if(null!==(n=n.firstBaseUpdate)){do{var l={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===o?a=o=l:o=o.next=l,n=n.next}while(null!==n);null===o?a=o=t:o=o.next=t}else a=o=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:o,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function el(e,t,n,r){var a=e.updateQueue;Qo=!1;var o=a.firstBaseUpdate,l=a.lastBaseUpdate,i=a.shared.pending;if(null!==i){a.shared.pending=null;var s=i,u=s.next;s.next=null,null===l?o=u:l.next=u,l=s;var c=e.alternate;null!==c&&((i=(c=c.updateQueue).lastBaseUpdate)!==l&&(null===i?c.firstBaseUpdate=u:i.next=u,c.lastBaseUpdate=s))}if(null!==o){var d=a.baseState;for(l=0,c=u=s=null,i=o;;){var f=i.lane,p=i.eventTime;if((r&f)===f){null!==c&&(c=c.next={eventTime:p,lane:0,tag:i.tag,payload:i.payload,callback:i.callback,next:null});e:{var h=e,m=i;switch(f=t,p=n,m.tag){case 1:if("function"===typeof(h=m.payload)){d=h.call(p,d,f);break e}d=h;break e;case 3:h.flags=-65537&h.flags|128;case 0:if(null===(f="function"===typeof(h=m.payload)?h.call(p,d,f):h)||void 0===f)break e;d=D({},d,f);break e;case 2:Qo=!0}}null!==i.callback&&0!==i.lane&&(e.flags|=64,null===(f=a.effects)?a.effects=[i]:f.push(i))}else p={eventTime:p,lane:f,tag:i.tag,payload:i.payload,callback:i.callback,next:null},null===c?(u=c=p,s=d):c=c.next=p,l|=f;if(null===(i=i.next)){if(null===(i=a.shared.pending))break;i=(f=i).next,f.next=null,a.lastBaseUpdate=f,a.shared.pending=null}}if(null===c&&(s=d),a.baseState=s,a.firstBaseUpdate=u,a.lastBaseUpdate=c,null!==(t=a.shared.interleaved)){a=t;do{l|=a.lane,a=a.next}while(a!==t)}else null===o&&(a.shared.lanes=0);Qs|=l,e.lanes=l,e.memoizedState=d}}function tl(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],a=r.callback;if(null!==a){if(r.callback=null,r=n,"function"!==typeof a)throw Error(o(191,a));a.call(r)}}}var nl={},rl=Ma(nl),al=Ma(nl),ol=Ma(nl);function ll(e){if(e===nl)throw Error(o(174));return e}function il(e,t){switch(Da(ol,t),Da(al,e),Da(rl,nl),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:se(null,"");break;default:t=se(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}Ia(rl),Da(rl,t)}function sl(){Ia(rl),Ia(al),Ia(ol)}function ul(e){ll(ol.current);var t=ll(rl.current),n=se(t,e.type);t!==n&&(Da(al,e),Da(rl,n))}function cl(e){al.current===e&&(Ia(rl),Ia(al))}var dl=Ma(0);function fl(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var pl=[];function hl(){for(var e=0;e<pl.length;e++)pl[e]._workInProgressVersionPrimary=null;pl.length=0}var ml=b.ReactCurrentDispatcher,gl=b.ReactCurrentBatchConfig,vl=0,yl=null,xl=null,bl=null,wl=!1,jl=!1,Nl=0,kl=0;function Sl(){throw Error(o(321))}function El(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!yr(e[n],t[n]))return!1;return!0}function Cl(e,t,n,r,a,l){if(vl=l,yl=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ml.current=null===e||null===e.memoizedState?ci:di,e=n(r,a),jl){l=0;do{if(jl=!1,Nl=0,25<=l)throw Error(o(301));l+=1,bl=xl=null,t.updateQueue=null,ml.current=fi,e=n(r,a)}while(jl)}if(ml.current=ui,t=null!==xl&&null!==xl.next,vl=0,bl=xl=yl=null,wl=!1,t)throw Error(o(300));return e}function Ol(){var e=0!==Nl;return Nl=0,e}function Pl(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===bl?yl.memoizedState=bl=e:bl=bl.next=e,bl}function _l(){if(null===xl){var e=yl.alternate;e=null!==e?e.memoizedState:null}else e=xl.next;var t=null===bl?yl.memoizedState:bl.next;if(null!==t)bl=t,xl=e;else{if(null===e)throw Error(o(310));e={memoizedState:(xl=e).memoizedState,baseState:xl.baseState,baseQueue:xl.baseQueue,queue:xl.queue,next:null},null===bl?yl.memoizedState=bl=e:bl=bl.next=e}return bl}function Rl(e,t){return"function"===typeof t?t(e):t}function Ll(e){var t=_l(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=xl,a=r.baseQueue,l=n.pending;if(null!==l){if(null!==a){var i=a.next;a.next=l.next,l.next=i}r.baseQueue=a=l,n.pending=null}if(null!==a){l=a.next,r=r.baseState;var s=i=null,u=null,c=l;do{var d=c.lane;if((vl&d)===d)null!==u&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var f={lane:d,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};null===u?(s=u=f,i=r):u=u.next=f,yl.lanes|=d,Qs|=d}c=c.next}while(null!==c&&c!==l);null===u?i=r:u.next=s,yr(r,t.memoizedState)||(_i=!0),t.memoizedState=r,t.baseState=i,t.baseQueue=u,n.lastRenderedState=r}if(null!==(e=n.interleaved)){a=e;do{l=a.lane,yl.lanes|=l,Qs|=l,a=a.next}while(a!==e)}else null===a&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Tl(e){var t=_l(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,l=t.memoizedState;if(null!==a){n.pending=null;var i=a=a.next;do{l=e(l,i.action),i=i.next}while(i!==a);yr(l,t.memoizedState)||(_i=!0),t.memoizedState=l,null===t.baseQueue&&(t.baseState=l),n.lastRenderedState=l}return[l,r]}function Al(){}function Ml(e,t){var n=yl,r=_l(),a=t(),l=!yr(r.memoizedState,a);if(l&&(r.memoizedState=a,_i=!0),r=r.queue,Ql(zl.bind(null,n,r,e),[e]),r.getSnapshot!==t||l||null!==bl&&1&bl.memoizedState.tag){if(n.flags|=2048,Wl(9,Dl.bind(null,n,r,a,t),void 0,null),null===Us)throw Error(o(349));0!==(30&vl)||Il(n,t,a)}return a}function Il(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=yl.updateQueue)?(t={lastEffect:null,stores:null},yl.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Dl(e,t,n,r){t.value=n,t.getSnapshot=r,Fl(t)&&Ul(e)}function zl(e,t,n){return n((function(){Fl(t)&&Ul(e)}))}function Fl(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!yr(e,n)}catch(dn){return!0}}function Ul(e){var t=Ho(e,1);null!==t&&pu(t,e,1,-1)}function Bl(e){var t=Pl();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Rl,lastRenderedState:e},t.queue=e,e=e.dispatch=oi.bind(null,yl,e),[t.memoizedState,e]}function Wl(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=yl.updateQueue)?(t={lastEffect:null,stores:null},yl.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function Vl(){return _l().memoizedState}function $l(e,t,n,r){var a=Pl();yl.flags|=e,a.memoizedState=Wl(1|t,n,void 0,void 0===r?null:r)}function ql(e,t,n,r){var a=_l();r=void 0===r?null:r;var o=void 0;if(null!==xl){var l=xl.memoizedState;if(o=l.destroy,null!==r&&El(r,l.deps))return void(a.memoizedState=Wl(t,n,o,r))}yl.flags|=e,a.memoizedState=Wl(1|t,n,o,r)}function Hl(e,t){return $l(8390656,8,e,t)}function Ql(e,t){return ql(2048,8,e,t)}function Kl(e,t){return ql(4,2,e,t)}function Jl(e,t){return ql(4,4,e,t)}function Xl(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Zl(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,ql(4,4,Xl.bind(null,t,e),n)}function Yl(){}function Gl(e,t){var n=_l();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&El(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function ei(e,t){var n=_l();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&El(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function ti(e,t,n){return 0===(21&vl)?(e.baseState&&(e.baseState=!1,_i=!0),e.memoizedState=n):(yr(n,t)||(n=ht(),yl.lanes|=n,Qs|=n,e.baseState=!0),t)}function ni(e,t){var n=yt;yt=0!==n&&4>n?n:4,e(!0);var r=gl.transition;gl.transition={};try{e(!1),t()}finally{yt=n,gl.transition=r}}function ri(){return _l().memoizedState}function ai(e,t,n){var r=fu(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},li(e))ii(t,n);else if(null!==(n=qo(e,t,n,r))){pu(n,e,r,du()),si(n,t,r)}}function oi(e,t,n){var r=fu(e),a={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(li(e))ii(t,a);else{var o=e.alternate;if(0===e.lanes&&(null===o||0===o.lanes)&&null!==(o=t.lastRenderedReducer))try{var l=t.lastRenderedState,i=o(l,n);if(a.hasEagerState=!0,a.eagerState=i,yr(i,l)){var s=t.interleaved;return null===s?(a.next=a,$o(t)):(a.next=s.next,s.next=a),void(t.interleaved=a)}}catch(cn){}null!==(n=qo(e,t,a,r))&&(pu(n,e,r,a=du()),si(n,t,r))}}function li(e){var t=e.alternate;return e===yl||null!==t&&t===yl}function ii(e,t){jl=wl=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function si(e,t,n){if(0!==(4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,vt(e,n)}}var ui={readContext:Wo,useCallback:Sl,useContext:Sl,useEffect:Sl,useImperativeHandle:Sl,useInsertionEffect:Sl,useLayoutEffect:Sl,useMemo:Sl,useReducer:Sl,useRef:Sl,useState:Sl,useDebugValue:Sl,useDeferredValue:Sl,useTransition:Sl,useMutableSource:Sl,useSyncExternalStore:Sl,useId:Sl,unstable_isNewReconciler:!1},ci={readContext:Wo,useCallback:function(e,t){return Pl().memoizedState=[e,void 0===t?null:t],e},useContext:Wo,useEffect:Hl,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,$l(4194308,4,Xl.bind(null,t,e),n)},useLayoutEffect:function(e,t){return $l(4194308,4,e,t)},useInsertionEffect:function(e,t){return $l(4,2,e,t)},useMemo:function(e,t){var n=Pl();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Pl();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=ai.bind(null,yl,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},Pl().memoizedState=e},useState:Bl,useDebugValue:Yl,useDeferredValue:function(e){return Pl().memoizedState=e},useTransition:function(){var e=Bl(!1),t=e[0];return e=ni.bind(null,e[1]),Pl().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=yl,a=Pl();if(go){if(void 0===n)throw Error(o(407));n=n()}else{if(n=t(),null===Us)throw Error(o(349));0!==(30&vl)||Il(r,t,n)}a.memoizedState=n;var l={value:n,getSnapshot:t};return a.queue=l,Hl(zl.bind(null,r,l,e),[e]),r.flags|=2048,Wl(9,Dl.bind(null,r,l,n,t),void 0,null),n},useId:function(){var e=Pl(),t=Us.identifierPrefix;if(go){var n=so;t=":"+t+"R"+(n=(io&~(1<<32-ot(io)-1)).toString(32)+n),0<(n=Nl++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=kl++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},di={readContext:Wo,useCallback:Gl,useContext:Wo,useEffect:Ql,useImperativeHandle:Zl,useInsertionEffect:Kl,useLayoutEffect:Jl,useMemo:ei,useReducer:Ll,useRef:Vl,useState:function(){return Ll(Rl)},useDebugValue:Yl,useDeferredValue:function(e){return ti(_l(),xl.memoizedState,e)},useTransition:function(){return[Ll(Rl)[0],_l().memoizedState]},useMutableSource:Al,useSyncExternalStore:Ml,useId:ri,unstable_isNewReconciler:!1},fi={readContext:Wo,useCallback:Gl,useContext:Wo,useEffect:Ql,useImperativeHandle:Zl,useInsertionEffect:Kl,useLayoutEffect:Jl,useMemo:ei,useReducer:Tl,useRef:Vl,useState:function(){return Tl(Rl)},useDebugValue:Yl,useDeferredValue:function(e){var t=_l();return null===xl?t.memoizedState=e:ti(t,xl.memoizedState,e)},useTransition:function(){return[Tl(Rl)[0],_l().memoizedState]},useMutableSource:Al,useSyncExternalStore:Ml,useId:ri,unstable_isNewReconciler:!1};function pi(e,t){if(e&&e.defaultProps){for(var n in t=D({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function hi(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:D({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var mi={isMounted:function(e){return!!(e=e._reactInternals)&&Be(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=du(),a=fu(e),o=Xo(r,a);o.payload=t,void 0!==n&&null!==n&&(o.callback=n),null!==(t=Zo(e,o,a))&&(pu(t,e,a,r),Yo(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=du(),a=fu(e),o=Xo(r,a);o.tag=1,o.payload=t,void 0!==n&&null!==n&&(o.callback=n),null!==(t=Zo(e,o,a))&&(pu(t,e,a,r),Yo(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=du(),r=fu(e),a=Xo(n,r);a.tag=2,void 0!==t&&null!==t&&(a.callback=t),null!==(t=Zo(e,a,r))&&(pu(t,e,r,n),Yo(t,e,r))}};function gi(e,t,n,r,a,o,l){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,o,l):!t.prototype||!t.prototype.isPureReactComponent||(!xr(n,r)||!xr(a,o))}function vi(e,t,n){var r=!1,a=za,o=t.contextType;return"object"===typeof o&&null!==o?o=Wo(o):(a=Va(t)?Ba:Fa.current,o=(r=null!==(r=t.contextTypes)&&void 0!==r)?Wa(e,a):za),t=new t(n,o),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=mi,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=a,e.__reactInternalMemoizedMaskedChildContext=o),t}function yi(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&mi.enqueueReplaceState(t,t.state,null)}function xi(e,t,n,r){var a=e.stateNode;a.props=n,a.state=e.memoizedState,a.refs={},Ko(e);var o=t.contextType;"object"===typeof o&&null!==o?a.context=Wo(o):(o=Va(t)?Ba:Fa.current,a.context=Wa(e,o)),a.state=e.memoizedState,"function"===typeof(o=t.getDerivedStateFromProps)&&(hi(e,t,o,n),a.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof a.getSnapshotBeforeUpdate||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||(t=a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),t!==a.state&&mi.enqueueReplaceState(a,a.state,null),el(e,n,a,r),a.state=e.memoizedState),"function"===typeof a.componentDidMount&&(e.flags|=4194308)}function bi(e,t){try{var n="",r=t;do{n+=B(r),r=r.return}while(r);var a=n}catch(an){a="\nError generating stack: "+an.message+"\n"+an.stack}return{value:e,source:t,stack:a,digest:null}}function wi(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function ji(e,t){try{console.error(t.value)}catch(on){setTimeout((function(){throw on}))}}var Ni="function"===typeof WeakMap?WeakMap:Map;function ki(e,t,n){(n=Xo(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){tu||(tu=!0,nu=r),ji(0,t)},n}function Si(e,t,n){(n=Xo(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"===typeof r){var a=t.value;n.payload=function(){return r(a)},n.callback=function(){ji(0,t)}}var o=e.stateNode;return null!==o&&"function"===typeof o.componentDidCatch&&(n.callback=function(){ji(0,t),"function"!==typeof r&&(null===ru?ru=new Set([this]):ru.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function Ei(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new Ni;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(a.add(n),e=Iu.bind(null,e,t,n),t.then(e,e))}function Ci(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function Oi(e,t,n,r,a){return 0===(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Xo(-1,1)).tag=2,Zo(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=a,e)}var Pi=b.ReactCurrentOwner,_i=!1;function Ri(e,t,n,r){t.child=null===e?To(t,null,n,r):Lo(t,e.child,n,r)}function Li(e,t,n,r,a){n=n.render;var o=t.ref;return Bo(t,a),r=Cl(e,t,n,r,o,a),n=Ol(),null===e||_i?(go&&n&&fo(t),t.flags|=1,Ri(e,t,r,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,ts(e,t,a))}function Ti(e,t,n,r,a){if(null===e){var o=n.type;return"function"!==typeof o||Vu(o)||void 0!==o.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=qu(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=o,Ai(e,t,o,r,a))}if(o=e.child,0===(e.lanes&a)){var l=o.memoizedProps;if((n=null!==(n=n.compare)?n:xr)(l,r)&&e.ref===t.ref)return ts(e,t,a)}return t.flags|=1,(e=$u(o,r)).ref=t.ref,e.return=t,t.child=e}function Ai(e,t,n,r,a){if(null!==e){var o=e.memoizedProps;if(xr(o,r)&&e.ref===t.ref){if(_i=!1,t.pendingProps=r=o,0===(e.lanes&a))return t.lanes=e.lanes,ts(e,t,a);0!==(131072&e.flags)&&(_i=!0)}}return Di(e,t,n,r,a)}function Mi(e,t,n){var r=t.pendingProps,a=r.children,o=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(0===(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Da($s,Vs),Vs|=n;else{if(0===(1073741824&n))return e=null!==o?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Da($s,Vs),Vs|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==o?o.baseLanes:n,Da($s,Vs),Vs|=r}else null!==o?(r=o.baseLanes|n,t.memoizedState=null):r=n,Da($s,Vs),Vs|=r;return Ri(e,t,a,n),t.child}function Ii(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Di(e,t,n,r,a){var o=Va(n)?Ba:Fa.current;return o=Wa(t,o),Bo(t,a),n=Cl(e,t,n,r,o,a),r=Ol(),null===e||_i?(go&&r&&fo(t),t.flags|=1,Ri(e,t,n,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,ts(e,t,a))}function zi(e,t,n,r,a){if(Va(n)){var o=!0;Qa(t)}else o=!1;if(Bo(t,a),null===t.stateNode)es(e,t),vi(t,n,r),xi(t,n,r,a),r=!0;else if(null===e){var l=t.stateNode,i=t.memoizedProps;l.props=i;var s=l.context,u=n.contextType;"object"===typeof u&&null!==u?u=Wo(u):u=Wa(t,u=Va(n)?Ba:Fa.current);var c=n.getDerivedStateFromProps,d="function"===typeof c||"function"===typeof l.getSnapshotBeforeUpdate;d||"function"!==typeof l.UNSAFE_componentWillReceiveProps&&"function"!==typeof l.componentWillReceiveProps||(i!==r||s!==u)&&yi(t,l,r,u),Qo=!1;var f=t.memoizedState;l.state=f,el(t,r,l,a),s=t.memoizedState,i!==r||f!==s||Ua.current||Qo?("function"===typeof c&&(hi(t,n,c,r),s=t.memoizedState),(i=Qo||gi(t,n,i,r,f,s,u))?(d||"function"!==typeof l.UNSAFE_componentWillMount&&"function"!==typeof l.componentWillMount||("function"===typeof l.componentWillMount&&l.componentWillMount(),"function"===typeof l.UNSAFE_componentWillMount&&l.UNSAFE_componentWillMount()),"function"===typeof l.componentDidMount&&(t.flags|=4194308)):("function"===typeof l.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=s),l.props=r,l.state=s,l.context=u,r=i):("function"===typeof l.componentDidMount&&(t.flags|=4194308),r=!1)}else{l=t.stateNode,Jo(e,t),i=t.memoizedProps,u=t.type===t.elementType?i:pi(t.type,i),l.props=u,d=t.pendingProps,f=l.context,"object"===typeof(s=n.contextType)&&null!==s?s=Wo(s):s=Wa(t,s=Va(n)?Ba:Fa.current);var p=n.getDerivedStateFromProps;(c="function"===typeof p||"function"===typeof l.getSnapshotBeforeUpdate)||"function"!==typeof l.UNSAFE_componentWillReceiveProps&&"function"!==typeof l.componentWillReceiveProps||(i!==d||f!==s)&&yi(t,l,r,s),Qo=!1,f=t.memoizedState,l.state=f,el(t,r,l,a);var h=t.memoizedState;i!==d||f!==h||Ua.current||Qo?("function"===typeof p&&(hi(t,n,p,r),h=t.memoizedState),(u=Qo||gi(t,n,u,r,f,h,s)||!1)?(c||"function"!==typeof l.UNSAFE_componentWillUpdate&&"function"!==typeof l.componentWillUpdate||("function"===typeof l.componentWillUpdate&&l.componentWillUpdate(r,h,s),"function"===typeof l.UNSAFE_componentWillUpdate&&l.UNSAFE_componentWillUpdate(r,h,s)),"function"===typeof l.componentDidUpdate&&(t.flags|=4),"function"===typeof l.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof l.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof l.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=h),l.props=r,l.state=h,l.context=s,r=u):("function"!==typeof l.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof l.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return Fi(e,t,n,r,o,a)}function Fi(e,t,n,r,a,o){Ii(e,t);var l=0!==(128&t.flags);if(!r&&!l)return a&&Ka(t,n,!1),ts(e,t,o);r=t.stateNode,Pi.current=t;var i=l&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&l?(t.child=Lo(t,e.child,null,o),t.child=Lo(t,null,i,o)):Ri(e,t,i,o),t.memoizedState=r.state,a&&Ka(t,n,!0),t.child}function Ui(e){var t=e.stateNode;t.pendingContext?qa(0,t.pendingContext,t.pendingContext!==t.context):t.context&&qa(0,t.context,!1),il(e,t.containerInfo)}function Bi(e,t,n,r,a){return So(),Eo(a),t.flags|=256,Ri(e,t,n,r),t.child}var Wi,Vi,$i,qi,Hi={dehydrated:null,treeContext:null,retryLane:0};function Qi(e){return{baseLanes:e,cachePool:null,transitions:null}}function Ki(e,t,n){var r,a=t.pendingProps,l=dl.current,i=!1,s=0!==(128&t.flags);if((r=s)||(r=(null===e||null!==e.memoizedState)&&0!==(2&l)),r?(i=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(l|=1),Da(dl,1&l),null===e)return wo(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0===(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(s=a.children,e=a.fallback,i?(a=t.mode,i=t.child,s={mode:"hidden",children:s},0===(1&a)&&null!==i?(i.childLanes=0,i.pendingProps=s):i=Qu(s,a,0,null),e=Hu(e,a,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Qi(n),t.memoizedState=Hi,e):Ji(t,s));if(null!==(l=e.memoizedState)&&null!==(r=l.dehydrated))return function(e,t,n,r,a,l,i){if(n)return 256&t.flags?(t.flags&=-257,Xi(e,t,i,r=wi(Error(o(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(l=r.fallback,a=t.mode,r=Qu({mode:"visible",children:r.children},a,0,null),(l=Hu(l,a,i,null)).flags|=2,r.return=t,l.return=t,r.sibling=l,t.child=r,0!==(1&t.mode)&&Lo(t,e.child,null,i),t.child.memoizedState=Qi(i),t.memoizedState=Hi,l);if(0===(1&t.mode))return Xi(e,t,i,null);if("$!"===a.data){if(r=a.nextSibling&&a.nextSibling.dataset)var s=r.dgst;return r=s,Xi(e,t,i,r=wi(l=Error(o(419)),r,void 0))}if(s=0!==(i&e.childLanes),_i||s){if(null!==(r=Us)){switch(i&-i){case 4:a=2;break;case 16:a=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:a=32;break;case 536870912:a=268435456;break;default:a=0}0!==(a=0!==(a&(r.suspendedLanes|i))?0:a)&&a!==l.retryLane&&(l.retryLane=a,Ho(e,a),pu(r,e,a,-1))}return Eu(),Xi(e,t,i,r=wi(Error(o(421))))}return"$?"===a.data?(t.flags|=128,t.child=e.child,t=zu.bind(null,e),a._reactRetry=t,null):(e=l.treeContext,mo=ba(a.nextSibling),ho=t,go=!0,vo=null,null!==e&&(ao[oo++]=io,ao[oo++]=so,ao[oo++]=lo,io=e.id,so=e.overflow,lo=t),t=Ji(t,r.children),t.flags|=4096,t)}(e,t,s,a,r,l,n);if(i){i=a.fallback,s=t.mode,r=(l=e.child).sibling;var u={mode:"hidden",children:a.children};return 0===(1&s)&&t.child!==l?((a=t.child).childLanes=0,a.pendingProps=u,t.deletions=null):(a=$u(l,u)).subtreeFlags=14680064&l.subtreeFlags,null!==r?i=$u(r,i):(i=Hu(i,s,n,null)).flags|=2,i.return=t,a.return=t,a.sibling=i,t.child=a,a=i,i=t.child,s=null===(s=e.child.memoizedState)?Qi(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},i.memoizedState=s,i.childLanes=e.childLanes&~n,t.memoizedState=Hi,a}return e=(i=e.child).sibling,a=$u(i,{mode:"visible",children:a.children}),0===(1&t.mode)&&(a.lanes=n),a.return=t,a.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=a,t.memoizedState=null,a}function Ji(e,t){return(t=Qu({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Xi(e,t,n,r){return null!==r&&Eo(r),Lo(t,e.child,null,n),(e=Ji(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Zi(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),Uo(e.return,t,n)}function Yi(e,t,n,r,a){var o=e.memoizedState;null===o?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=a)}function Gi(e,t,n){var r=t.pendingProps,a=r.revealOrder,o=r.tail;if(Ri(e,t,r.children,n),0!==(2&(r=dl.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Zi(e,n,t);else if(19===e.tag)Zi(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Da(dl,r),0===(1&t.mode))t.memoizedState=null;else switch(a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===fl(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),Yi(t,!1,a,n,o);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===fl(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}Yi(t,!0,n,null,o);break;case"together":Yi(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function es(e,t){0===(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function ts(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Qs|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(o(153));if(null!==t.child){for(n=$u(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=$u(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function ns(e,t){if(!go)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function rs(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=14680064&a.subtreeFlags,r|=14680064&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function as(e,t,n){var r=t.pendingProps;switch(po(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return rs(t),null;case 1:case 17:return Va(t.type)&&$a(),rs(t),null;case 3:return r=t.stateNode,sl(),Ia(Ua),Ia(Fa),hl(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(No(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,null!==vo&&(vu(vo),vo=null))),Vi(e,t),rs(t),null;case 5:cl(t);var a=ll(ol.current);if(n=t.type,null!==e&&null!=t.stateNode)$i(e,t,n,r,a),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(o(166));return rs(t),null}if(e=ll(rl.current),No(t)){r=t.stateNode,n=t.type;var l=t.memoizedProps;switch(r[Na]=t,r[ka]=l,e=0!==(1&t.mode),n){case"dialog":Xr("cancel",r),Xr("close",r);break;case"iframe":case"object":case"embed":Xr("load",r);break;case"video":case"audio":for(a=0;a<Hr.length;a++)Xr(Hr[a],r);break;case"source":Xr("error",r);break;case"img":case"image":case"link":Xr("error",r),Xr("load",r);break;case"details":Xr("toggle",r);break;case"input":X(r,l),Xr("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!l.multiple},Xr("invalid",r);break;case"textarea":ae(r,l),Xr("invalid",r)}for(var s in ve(n,l),a=null,l)if(l.hasOwnProperty(s)){var u=l[s];"children"===s?"string"===typeof u?r.textContent!==u&&(!0!==l.suppressHydrationWarning&&ua(r.textContent,u,e),a=["children",u]):"number"===typeof u&&r.textContent!==""+u&&(!0!==l.suppressHydrationWarning&&ua(r.textContent,u,e),a=["children",""+u]):i.hasOwnProperty(s)&&null!=u&&"onScroll"===s&&Xr("scroll",r)}switch(n){case"input":H(r),G(r,l,!0);break;case"textarea":H(r),le(r);break;case"select":case"option":break;default:"function"===typeof l.onClick&&(r.onclick=ca)}r=a,t.updateQueue=r,null!==r&&(t.flags|=4)}else{s=9===a.nodeType?a:a.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=ie(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=s.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof r.is?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),"select"===n&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[Na]=t,e[ka]=r,Wi(e,t,!1,!1),t.stateNode=e;e:{switch(s=ye(n,r),n){case"dialog":Xr("cancel",e),Xr("close",e),a=r;break;case"iframe":case"object":case"embed":Xr("load",e),a=r;break;case"video":case"audio":for(a=0;a<Hr.length;a++)Xr(Hr[a],e);a=r;break;case"source":Xr("error",e),a=r;break;case"img":case"image":case"link":Xr("error",e),Xr("load",e),a=r;break;case"details":Xr("toggle",e),a=r;break;case"input":X(e,r),a=J(e,r),Xr("invalid",e);break;case"option":default:a=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},a=D({},r,{value:void 0}),Xr("invalid",e);break;case"textarea":ae(e,r),a=re(e,r),Xr("invalid",e)}for(l in ve(n,a),u=a)if(u.hasOwnProperty(l)){var c=u[l];"style"===l?me(e,c):"dangerouslySetInnerHTML"===l?null!=(c=c?c.__html:void 0)&&ce(e,c):"children"===l?"string"===typeof c?("textarea"!==n||""!==c)&&de(e,c):"number"===typeof c&&de(e,""+c):"suppressContentEditableWarning"!==l&&"suppressHydrationWarning"!==l&&"autoFocus"!==l&&(i.hasOwnProperty(l)?null!=c&&"onScroll"===l&&Xr("scroll",e):null!=c&&x(e,l,c,s))}switch(n){case"input":H(e),G(e,r,!1);break;case"textarea":H(e),le(e);break;case"option":null!=r.value&&e.setAttribute("value",""+$(r.value));break;case"select":e.multiple=!!r.multiple,null!=(l=r.value)?ne(e,!!r.multiple,l,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof a.onClick&&(e.onclick=ca)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return rs(t),null;case 6:if(e&&null!=t.stateNode)qi(e,t,e.memoizedProps,r);else{if("string"!==typeof r&&null===t.stateNode)throw Error(o(166));if(n=ll(ol.current),ll(rl.current),No(t)){if(r=t.stateNode,n=t.memoizedProps,r[Na]=t,(l=r.nodeValue!==n)&&null!==(e=ho))switch(e.tag){case 3:ua(r.nodeValue,n,0!==(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&ua(r.nodeValue,n,0!==(1&e.mode))}l&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[Na]=t,t.stateNode=r}return rs(t),null;case 13:if(Ia(dl),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(go&&null!==mo&&0!==(1&t.mode)&&0===(128&t.flags))ko(),So(),t.flags|=98560,l=!1;else if(l=No(t),null!==r&&null!==r.dehydrated){if(null===e){if(!l)throw Error(o(318));if(!(l=null!==(l=t.memoizedState)?l.dehydrated:null))throw Error(o(317));l[Na]=t}else So(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;rs(t),l=!1}else null!==vo&&(vu(vo),vo=null),l=!0;if(!l)return 65536&t.flags?t:null}return 0!==(128&t.flags)?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,0!==(1&t.mode)&&(null===e||0!==(1&dl.current)?0===qs&&(qs=3):Eu())),null!==t.updateQueue&&(t.flags|=4),rs(t),null);case 4:return sl(),Vi(e,t),null===e&&Gr(t.stateNode.containerInfo),rs(t),null;case 10:return Fo(t.type._context),rs(t),null;case 19:if(Ia(dl),null===(l=t.memoizedState))return rs(t),null;if(r=0!==(128&t.flags),null===(s=l.rendering))if(r)ns(l,!1);else{if(0!==qs||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(s=fl(e))){for(t.flags|=128,ns(l,!1),null!==(r=s.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(l=n).flags&=14680066,null===(s=l.alternate)?(l.childLanes=0,l.lanes=e,l.child=null,l.subtreeFlags=0,l.memoizedProps=null,l.memoizedState=null,l.updateQueue=null,l.dependencies=null,l.stateNode=null):(l.childLanes=s.childLanes,l.lanes=s.lanes,l.child=s.child,l.subtreeFlags=0,l.deletions=null,l.memoizedProps=s.memoizedProps,l.memoizedState=s.memoizedState,l.updateQueue=s.updateQueue,l.type=s.type,e=s.dependencies,l.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Da(dl,1&dl.current|2),t.child}e=e.sibling}null!==l.tail&&Xe()>Gs&&(t.flags|=128,r=!0,ns(l,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=fl(s))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),ns(l,!0),null===l.tail&&"hidden"===l.tailMode&&!s.alternate&&!go)return rs(t),null}else 2*Xe()-l.renderingStartTime>Gs&&1073741824!==n&&(t.flags|=128,r=!0,ns(l,!1),t.lanes=4194304);l.isBackwards?(s.sibling=t.child,t.child=s):(null!==(n=l.last)?n.sibling=s:t.child=s,l.last=s)}return null!==l.tail?(t=l.tail,l.rendering=t,l.tail=t.sibling,l.renderingStartTime=Xe(),t.sibling=null,n=dl.current,Da(dl,r?1&n|2:1&n),t):(rs(t),null);case 22:case 23:return ju(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&0!==(1&t.mode)?0!==(1073741824&Vs)&&(rs(t),6&t.subtreeFlags&&(t.flags|=8192)):rs(t),null;case 24:case 25:return null}throw Error(o(156,t.tag))}function os(e,t){switch(po(t),t.tag){case 1:return Va(t.type)&&$a(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return sl(),Ia(Ua),Ia(Fa),hl(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 5:return cl(t),null;case 13:if(Ia(dl),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(o(340));So()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return Ia(dl),null;case 4:return sl(),null;case 10:return Fo(t.type._context),null;case 22:case 23:return ju(),null;default:return null}}Wi=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Vi=function(){},$i=function(e,t,n,r){var a=e.memoizedProps;if(a!==r){e=t.stateNode,ll(rl.current);var o,l=null;switch(n){case"input":a=J(e,a),r=J(e,r),l=[];break;case"select":a=D({},a,{value:void 0}),r=D({},r,{value:void 0}),l=[];break;case"textarea":a=re(e,a),r=re(e,r),l=[];break;default:"function"!==typeof a.onClick&&"function"===typeof r.onClick&&(e.onclick=ca)}for(c in ve(n,r),n=null,a)if(!r.hasOwnProperty(c)&&a.hasOwnProperty(c)&&null!=a[c])if("style"===c){var s=a[c];for(o in s)s.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else"dangerouslySetInnerHTML"!==c&&"children"!==c&&"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&"autoFocus"!==c&&(i.hasOwnProperty(c)?l||(l=[]):(l=l||[]).push(c,null));for(c in r){var u=r[c];if(s=null!=a?a[c]:void 0,r.hasOwnProperty(c)&&u!==s&&(null!=u||null!=s))if("style"===c)if(s){for(o in s)!s.hasOwnProperty(o)||u&&u.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in u)u.hasOwnProperty(o)&&s[o]!==u[o]&&(n||(n={}),n[o]=u[o])}else n||(l||(l=[]),l.push(c,n)),n=u;else"dangerouslySetInnerHTML"===c?(u=u?u.__html:void 0,s=s?s.__html:void 0,null!=u&&s!==u&&(l=l||[]).push(c,u)):"children"===c?"string"!==typeof u&&"number"!==typeof u||(l=l||[]).push(c,""+u):"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&(i.hasOwnProperty(c)?(null!=u&&"onScroll"===c&&Xr("scroll",e),l||s===u||(l=[])):(l=l||[]).push(c,u))}n&&(l=l||[]).push("style",n);var c=l;(t.updateQueue=c)&&(t.flags|=4)}},qi=function(e,t,n,r){n!==r&&(t.flags|=4)};var ls=!1,is=!1,ss="function"===typeof WeakSet?WeakSet:Set,us=null;function cs(e,t){var n=e.ref;if(null!==n)if("function"===typeof n)try{n(null)}catch(dn){Mu(e,t,dn)}else n.current=null}function ds(e,t,n){try{n()}catch(dn){Mu(e,t,dn)}}var fs=!1;function ps(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var a=r=r.next;do{if((a.tag&e)===e){var o=a.destroy;a.destroy=void 0,void 0!==o&&ds(t,n,o)}a=a.next}while(a!==r)}}function hs(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function ms(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"===typeof t?t(e):t.current=e}}function gs(e){var t=e.alternate;null!==t&&(e.alternate=null,gs(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[Na],delete t[ka],delete t[Ea],delete t[Ca],delete t[Oa])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function vs(e){return 5===e.tag||3===e.tag||4===e.tag}function ys(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||vs(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function xs(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=ca));else if(4!==r&&null!==(e=e.child))for(xs(e,t,n),e=e.sibling;null!==e;)xs(e,t,n),e=e.sibling}function bs(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(bs(e,t,n),e=e.sibling;null!==e;)bs(e,t,n),e=e.sibling}var ws=null,js=!1;function Ns(e,t,n){for(n=n.child;null!==n;)ks(e,t,n),n=n.sibling}function ks(e,t,n){if(at&&"function"===typeof at.onCommitFiberUnmount)try{at.onCommitFiberUnmount(rt,n)}catch(fn){}switch(n.tag){case 5:is||cs(n,t);case 6:var r=ws,a=js;ws=null,Ns(e,t,n),js=a,null!==(ws=r)&&(js?(e=ws,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):ws.removeChild(n.stateNode));break;case 18:null!==ws&&(js?(e=ws,n=n.stateNode,8===e.nodeType?xa(e.parentNode,n):1===e.nodeType&&xa(e,n),Bt(e)):xa(ws,n.stateNode));break;case 4:r=ws,a=js,ws=n.stateNode.containerInfo,js=!0,Ns(e,t,n),ws=r,js=a;break;case 0:case 11:case 14:case 15:if(!is&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){a=r=r.next;do{var o=a,l=o.destroy;o=o.tag,void 0!==l&&(0!==(2&o)||0!==(4&o))&&ds(n,t,l),a=a.next}while(a!==r)}Ns(e,t,n);break;case 1:if(!is&&(cs(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(fn){Mu(n,t,fn)}Ns(e,t,n);break;case 21:Ns(e,t,n);break;case 22:1&n.mode?(is=(r=is)||null!==n.memoizedState,Ns(e,t,n),is=r):Ns(e,t,n);break;default:Ns(e,t,n)}}function Ss(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new ss),t.forEach((function(t){var r=Fu.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}}function Es(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r];try{var l=e,i=t,s=i;e:for(;null!==s;){switch(s.tag){case 5:ws=s.stateNode,js=!1;break e;case 3:case 4:ws=s.stateNode.containerInfo,js=!0;break e}s=s.return}if(null===ws)throw Error(o(160));ks(l,i,a),ws=null,js=!1;var u=a.alternate;null!==u&&(u.return=null),a.return=null}catch(cn){Mu(a,t,cn)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)Cs(t,e),t=t.sibling}function Cs(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Es(t,e),Os(e),4&r){try{ps(3,e,e.return),hs(3,e)}catch(Gt){Mu(e,e.return,Gt)}try{ps(5,e,e.return)}catch(Gt){Mu(e,e.return,Gt)}}break;case 1:Es(t,e),Os(e),512&r&&null!==n&&cs(n,n.return);break;case 5:if(Es(t,e),Os(e),512&r&&null!==n&&cs(n,n.return),32&e.flags){var a=e.stateNode;try{de(a,"")}catch(Gt){Mu(e,e.return,Gt)}}if(4&r&&null!=(a=e.stateNode)){var l=e.memoizedProps,i=null!==n?n.memoizedProps:l,s=e.type,u=e.updateQueue;if(e.updateQueue=null,null!==u)try{"input"===s&&"radio"===l.type&&null!=l.name&&Z(a,l),ye(s,i);var c=ye(s,l);for(i=0;i<u.length;i+=2){var d=u[i],f=u[i+1];"style"===d?me(a,f):"dangerouslySetInnerHTML"===d?ce(a,f):"children"===d?de(a,f):x(a,d,f,c)}switch(s){case"input":Y(a,l);break;case"textarea":oe(a,l);break;case"select":var p=a._wrapperState.wasMultiple;a._wrapperState.wasMultiple=!!l.multiple;var h=l.value;null!=h?ne(a,!!l.multiple,h,!1):p!==!!l.multiple&&(null!=l.defaultValue?ne(a,!!l.multiple,l.defaultValue,!0):ne(a,!!l.multiple,l.multiple?[]:"",!1))}a[ka]=l}catch(Gt){Mu(e,e.return,Gt)}}break;case 6:if(Es(t,e),Os(e),4&r){if(null===e.stateNode)throw Error(o(162));a=e.stateNode,l=e.memoizedProps;try{a.nodeValue=l}catch(Gt){Mu(e,e.return,Gt)}}break;case 3:if(Es(t,e),Os(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Bt(t.containerInfo)}catch(Gt){Mu(e,e.return,Gt)}break;case 4:default:Es(t,e),Os(e);break;case 13:Es(t,e),Os(e),8192&(a=e.child).flags&&(l=null!==a.memoizedState,a.stateNode.isHidden=l,!l||null!==a.alternate&&null!==a.alternate.memoizedState||(Ys=Xe())),4&r&&Ss(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(is=(c=is)||d,Es(t,e),is=c):Es(t,e),Os(e),8192&r){if(c=null!==e.memoizedState,(e.stateNode.isHidden=c)&&!d&&0!==(1&e.mode))for(us=e,d=e.child;null!==d;){for(f=us=d;null!==us;){switch(h=(p=us).child,p.tag){case 0:case 11:case 14:case 15:ps(4,p,p.return);break;case 1:cs(p,p.return);var m=p.stateNode;if("function"===typeof m.componentWillUnmount){r=p,n=p.return;try{t=r,m.props=t.memoizedProps,m.state=t.memoizedState,m.componentWillUnmount()}catch(Gt){Mu(r,n,Gt)}}break;case 5:cs(p,p.return);break;case 22:if(null!==p.memoizedState){Ls(f);continue}}null!==h?(h.return=p,us=h):Ls(f)}d=d.sibling}e:for(d=null,f=e;;){if(5===f.tag){if(null===d){d=f;try{a=f.stateNode,c?"function"===typeof(l=a.style).setProperty?l.setProperty("display","none","important"):l.display="none":(s=f.stateNode,i=void 0!==(u=f.memoizedProps.style)&&null!==u&&u.hasOwnProperty("display")?u.display:null,s.style.display=he("display",i))}catch(Gt){Mu(e,e.return,Gt)}}}else if(6===f.tag){if(null===d)try{f.stateNode.nodeValue=c?"":f.memoizedProps}catch(Gt){Mu(e,e.return,Gt)}}else if((22!==f.tag&&23!==f.tag||null===f.memoizedState||f===e)&&null!==f.child){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;null===f.sibling;){if(null===f.return||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:Es(t,e),Os(e),4&r&&Ss(e);case 21:}}function Os(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(vs(n)){var r=n;break e}n=n.return}throw Error(o(160))}switch(r.tag){case 5:var a=r.stateNode;32&r.flags&&(de(a,""),r.flags&=-33),bs(e,ys(e),a);break;case 3:case 4:var l=r.stateNode.containerInfo;xs(e,ys(e),l);break;default:throw Error(o(161))}}catch(On){Mu(e,e.return,On)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function Ps(e,t,n){us=e,_s(e,t,n)}function _s(e,t,n){for(var r=0!==(1&e.mode);null!==us;){var a=us,o=a.child;if(22===a.tag&&r){var l=null!==a.memoizedState||ls;if(!l){var i=a.alternate,s=null!==i&&null!==i.memoizedState||is;i=ls;var u=is;if(ls=l,(is=s)&&!u)for(us=a;null!==us;)s=(l=us).child,22===l.tag&&null!==l.memoizedState?Ts(a):null!==s?(s.return=l,us=s):Ts(a);for(;null!==o;)us=o,_s(o,t,n),o=o.sibling;us=a,ls=i,is=u}Rs(e)}else 0!==(8772&a.subtreeFlags)&&null!==o?(o.return=a,us=o):Rs(e)}}function Rs(e){for(;null!==us;){var t=us;if(0!==(8772&t.flags)){var n=t.alternate;try{if(0!==(8772&t.flags))switch(t.tag){case 0:case 11:case 15:is||hs(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!is)if(null===n)r.componentDidMount();else{var a=t.elementType===t.type?n.memoizedProps:pi(t.type,n.memoizedProps);r.componentDidUpdate(a,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var l=t.updateQueue;null!==l&&tl(t,l,r);break;case 3:var i=t.updateQueue;if(null!==i){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}tl(t,i,n)}break;case 5:var s=t.stateNode;if(null===n&&4&t.flags){n=s;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var c=t.alternate;if(null!==c){var d=c.memoizedState;if(null!==d){var f=d.dehydrated;null!==f&&Bt(f)}}}break;default:throw Error(o(163))}is||512&t.flags&&ms(t)}catch(Yt){Mu(t,t.return,Yt)}}if(t===e){us=null;break}if(null!==(n=t.sibling)){n.return=t.return,us=n;break}us=t.return}}function Ls(e){for(;null!==us;){var t=us;if(t===e){us=null;break}var n=t.sibling;if(null!==n){n.return=t.return,us=n;break}us=t.return}}function Ts(e){for(;null!==us;){var t=us;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{hs(4,t)}catch(On){Mu(t,n,On)}break;case 1:var r=t.stateNode;if("function"===typeof r.componentDidMount){var a=t.return;try{r.componentDidMount()}catch(On){Mu(t,a,On)}}var o=t.return;try{ms(t)}catch(On){Mu(t,o,On)}break;case 5:var l=t.return;try{ms(t)}catch(On){Mu(t,l,On)}}}catch(On){Mu(t,t.return,On)}if(t===e){us=null;break}var i=t.sibling;if(null!==i){i.return=t.return,us=i;break}us=t.return}}var As,Ms=Math.ceil,Is=b.ReactCurrentDispatcher,Ds=b.ReactCurrentOwner,zs=b.ReactCurrentBatchConfig,Fs=0,Us=null,Bs=null,Ws=0,Vs=0,$s=Ma(0),qs=0,Hs=null,Qs=0,Ks=0,Js=0,Xs=null,Zs=null,Ys=0,Gs=1/0,eu=null,tu=!1,nu=null,ru=null,au=!1,ou=null,lu=0,iu=0,su=null,uu=-1,cu=0;function du(){return 0!==(6&Fs)?Xe():-1!==uu?uu:uu=Xe()}function fu(e){return 0===(1&e.mode)?1:0!==(2&Fs)&&0!==Ws?Ws&-Ws:null!==Co.transition?(0===cu&&(cu=ht()),cu):0!==(e=yt)?e:e=void 0===(e=window.event)?16:Jt(e.type)}function pu(e,t,n,r){if(50<iu)throw iu=0,su=null,Error(o(185));gt(e,n,r),0!==(2&Fs)&&e===Us||(e===Us&&(0===(2&Fs)&&(Ks|=n),4===qs&&yu(e,Ws)),hu(e,r),1===n&&0===Fs&&0===(1&t.mode)&&(Gs=Xe()+500,Xa&&Ga()))}function hu(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,o=e.pendingLanes;0<o;){var l=31-ot(o),i=1<<l,s=a[l];-1===s?0!==(i&n)&&0===(i&r)||(a[l]=ft(i,t)):s<=t&&(e.expiredLanes|=i),o&=~i}}(e,t);var r=dt(e,e===Us?Ws:0);if(0===r)null!==n&&Qe(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&Qe(n),1===t)0===e.tag?function(e){Xa=!0,Ya(e)}(xu.bind(null,e)):Ya(xu.bind(null,e)),va((function(){0===(6&Fs)&&Ga()})),n=null;else{switch(xt(r)){case 1:n=Ye;break;case 4:n=Ge;break;case 16:default:n=et;break;case 536870912:n=nt}n=Uu(n,mu.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function mu(e,t){if(uu=-1,cu=0,0!==(6&Fs))throw Error(o(327));var n=e.callbackNode;if(Tu()&&e.callbackNode!==n)return null;var r=dt(e,e===Us?Ws:0);if(0===r)return null;if(0!==(30&r)||0!==(r&e.expiredLanes)||t)t=Cu(e,r);else{t=r;var a=Fs;Fs|=2;var l=Su();for(Us===e&&Ws===t||(eu=null,Gs=Xe()+500,Nu(e,t));;)try{Pu();break}catch(fn){ku(e,fn)}zo(),Is.current=l,Fs=a,null!==Bs?t=0:(Us=null,Ws=0,t=qs)}if(0!==t){if(2===t&&(0!==(a=pt(e))&&(r=a,t=gu(e,a))),1===t)throw n=Hs,Nu(e,0),yu(e,r),hu(e,Xe()),n;if(6===t)yu(e,r);else{if(a=e.current.alternate,0===(30&r)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var a=n[r],o=a.getSnapshot;a=a.value;try{if(!yr(o(),a))return!1}catch(i){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(a)&&(2===(t=Cu(e,r))&&(0!==(l=pt(e))&&(r=l,t=gu(e,l))),1===t))throw n=Hs,Nu(e,0),yu(e,r),hu(e,Xe()),n;switch(e.finishedWork=a,e.finishedLanes=r,t){case 0:case 1:throw Error(o(345));case 2:case 5:Lu(e,Zs,eu);break;case 3:if(yu(e,r),(130023424&r)===r&&10<(t=Ys+500-Xe())){if(0!==dt(e,0))break;if(((a=e.suspendedLanes)&r)!==r){du(),e.pingedLanes|=e.suspendedLanes&a;break}e.timeoutHandle=ha(Lu.bind(null,e,Zs,eu),t);break}Lu(e,Zs,eu);break;case 4:if(yu(e,r),(4194240&r)===r)break;for(t=e.eventTimes,a=-1;0<r;){var i=31-ot(r);l=1<<i,(i=t[i])>a&&(a=i),r&=~l}if(r=a,10<(r=(120>(r=Xe()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Ms(r/1960))-r)){e.timeoutHandle=ha(Lu.bind(null,e,Zs,eu),r);break}Lu(e,Zs,eu);break;default:throw Error(o(329))}}}return hu(e,Xe()),e.callbackNode===n?mu.bind(null,e):null}function gu(e,t){var n=Xs;return e.current.memoizedState.isDehydrated&&(Nu(e,t).flags|=256),2!==(e=Cu(e,t))&&(t=Zs,Zs=n,null!==t&&vu(t)),e}function vu(e){null===Zs?Zs=e:Zs.push.apply(Zs,e)}function yu(e,t){for(t&=~Js,t&=~Ks,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-ot(t),r=1<<n;e[n]=-1,t&=~r}}function xu(e){if(0!==(6&Fs))throw Error(o(327));Tu();var t=dt(e,0);if(0===(1&t))return hu(e,Xe()),null;var n=Cu(e,t);if(0!==e.tag&&2===n){var r=pt(e);0!==r&&(t=r,n=gu(e,r))}if(1===n)throw n=Hs,Nu(e,0),yu(e,t),hu(e,Xe()),n;if(6===n)throw Error(o(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Lu(e,Zs,eu),hu(e,Xe()),null}function bu(e,t){var n=Fs;Fs|=1;try{return e(t)}finally{0===(Fs=n)&&(Gs=Xe()+500,Xa&&Ga())}}function wu(e){null!==ou&&0===ou.tag&&0===(6&Fs)&&Tu();var t=Fs;Fs|=1;var n=zs.transition,r=yt;try{if(zs.transition=null,yt=1,e)return e()}finally{yt=r,zs.transition=n,0===(6&(Fs=t))&&Ga()}}function ju(){Vs=$s.current,Ia($s)}function Nu(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,ma(n)),null!==Bs)for(n=Bs.return;null!==n;){var r=n;switch(po(r),r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&$a();break;case 3:sl(),Ia(Ua),Ia(Fa),hl();break;case 5:cl(r);break;case 4:sl();break;case 13:case 19:Ia(dl);break;case 10:Fo(r.type._context);break;case 22:case 23:ju()}n=n.return}if(Us=e,Bs=e=$u(e.current,null),Ws=Vs=t,qs=0,Hs=null,Js=Ks=Qs=0,Zs=Xs=null,null!==Vo){for(t=0;t<Vo.length;t++)if(null!==(r=(n=Vo[t]).interleaved)){n.interleaved=null;var a=r.next,o=n.pending;if(null!==o){var l=o.next;o.next=a,r.next=l}n.pending=r}Vo=null}return e}function ku(e,t){for(;;){var n=Bs;try{if(zo(),ml.current=ui,wl){for(var r=yl.memoizedState;null!==r;){var a=r.queue;null!==a&&(a.pending=null),r=r.next}wl=!1}if(vl=0,bl=xl=yl=null,jl=!1,Nl=0,Ds.current=null,null===n||null===n.return){qs=1,Hs=t,Bs=null;break}e:{var l=e,i=n.return,s=n,u=t;if(t=Ws,s.flags|=32768,null!==u&&"object"===typeof u&&"function"===typeof u.then){var c=u,d=s,f=d.tag;if(0===(1&d.mode)&&(0===f||11===f||15===f)){var p=d.alternate;p?(d.updateQueue=p.updateQueue,d.memoizedState=p.memoizedState,d.lanes=p.lanes):(d.updateQueue=null,d.memoizedState=null)}var h=Ci(i);if(null!==h){h.flags&=-257,Oi(h,i,s,0,t),1&h.mode&&Ei(l,c,t),u=c;var m=(t=h).updateQueue;if(null===m){var g=new Set;g.add(u),t.updateQueue=g}else m.add(u);break e}if(0===(1&t)){Ei(l,c,t),Eu();break e}u=Error(o(426))}else if(go&&1&s.mode){var v=Ci(i);if(null!==v){0===(65536&v.flags)&&(v.flags|=256),Oi(v,i,s,0,t),Eo(bi(u,s));break e}}l=u=bi(u,s),4!==qs&&(qs=2),null===Xs?Xs=[l]:Xs.push(l),l=i;do{switch(l.tag){case 3:l.flags|=65536,t&=-t,l.lanes|=t,Go(l,ki(0,u,t));break e;case 1:s=u;var y=l.type,x=l.stateNode;if(0===(128&l.flags)&&("function"===typeof y.getDerivedStateFromError||null!==x&&"function"===typeof x.componentDidCatch&&(null===ru||!ru.has(x)))){l.flags|=65536,t&=-t,l.lanes|=t,Go(l,Si(l,s,t));break e}}l=l.return}while(null!==l)}Ru(n)}catch(b){t=b,Bs===n&&null!==n&&(Bs=n=n.return);continue}break}}function Su(){var e=Is.current;return Is.current=ui,null===e?ui:e}function Eu(){0!==qs&&3!==qs&&2!==qs||(qs=4),null===Us||0===(268435455&Qs)&&0===(268435455&Ks)||yu(Us,Ws)}function Cu(e,t){var n=Fs;Fs|=2;var r=Su();for(Us===e&&Ws===t||(eu=null,Nu(e,t));;)try{Ou();break}catch(a){ku(e,a)}if(zo(),Fs=n,Is.current=r,null!==Bs)throw Error(o(261));return Us=null,Ws=0,qs}function Ou(){for(;null!==Bs;)_u(Bs)}function Pu(){for(;null!==Bs&&!Ke();)_u(Bs)}function _u(e){var t=As(e.alternate,e,Vs);e.memoizedProps=e.pendingProps,null===t?Ru(e):Bs=t,Ds.current=null}function Ru(e){var t=e;do{var n=t.alternate;if(e=t.return,0===(32768&t.flags)){if(null!==(n=as(n,t,Vs)))return void(Bs=n)}else{if(null!==(n=os(n,t)))return n.flags&=32767,void(Bs=n);if(null===e)return qs=6,void(Bs=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(Bs=t);Bs=t=e}while(null!==t);0===qs&&(qs=5)}function Lu(e,t,n){var r=yt,a=zs.transition;try{zs.transition=null,yt=1,function(e,t,n,r){do{Tu()}while(null!==ou);if(0!==(6&Fs))throw Error(o(327));n=e.finishedWork;var a=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(o(177));e.callbackNode=null,e.callbackPriority=0;var l=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var a=31-ot(n),o=1<<a;t[a]=0,r[a]=-1,e[a]=-1,n&=~o}}(e,l),e===Us&&(Bs=Us=null,Ws=0),0===(2064&n.subtreeFlags)&&0===(2064&n.flags)||au||(au=!0,Uu(et,(function(){return Tu(),null}))),l=0!==(15990&n.flags),0!==(15990&n.subtreeFlags)||l){l=zs.transition,zs.transition=null;var i=yt;yt=1;var s=Fs;Fs|=4,Ds.current=null,function(e,t){if(da=Vt,kr(e=Nr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,l=r.focusNode;r=r.focusOffset;try{n.nodeType,l.nodeType}catch(Tn){n=null;break e}var i=0,s=-1,u=-1,c=0,d=0,f=e,p=null;t:for(;;){for(var h;f!==n||0!==a&&3!==f.nodeType||(s=i+a),f!==l||0!==r&&3!==f.nodeType||(u=i+r),3===f.nodeType&&(i+=f.nodeValue.length),null!==(h=f.firstChild);)p=f,f=h;for(;;){if(f===e)break t;if(p===n&&++c===a&&(s=i),p===l&&++d===r&&(u=i),null!==(h=f.nextSibling))break;p=(f=p).parentNode}f=h}n=-1===s||-1===u?null:{start:s,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(fa={focusedElem:e,selectionRange:n},Vt=!1,us=t;null!==us;)if(e=(t=us).child,0!==(1028&t.subtreeFlags)&&null!==e)e.return=t,us=e;else for(;null!==us;){t=us;try{var m=t.alternate;if(0!==(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==m){var g=m.memoizedProps,v=m.memoizedState,y=t.stateNode,x=y.getSnapshotBeforeUpdate(t.elementType===t.type?g:pi(t.type,g),v);y.__reactInternalSnapshotBeforeUpdate=x}break;case 3:var b=t.stateNode.containerInfo;1===b.nodeType?b.textContent="":9===b.nodeType&&b.documentElement&&b.removeChild(b.documentElement);break;default:throw Error(o(163))}}catch(Tn){Mu(t,t.return,Tn)}if(null!==(e=t.sibling)){e.return=t.return,us=e;break}us=t.return}m=fs,fs=!1}(e,n),Cs(n,e),Sr(fa),Vt=!!da,fa=da=null,e.current=n,Ps(n,e,a),Je(),Fs=s,yt=i,zs.transition=l}else e.current=n;if(au&&(au=!1,ou=e,lu=a),l=e.pendingLanes,0===l&&(ru=null),function(e){if(at&&"function"===typeof at.onCommitFiberRoot)try{at.onCommitFiberRoot(rt,e,void 0,128===(128&e.current.flags))}catch(t){}}(n.stateNode),hu(e,Xe()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)a=t[n],r(a.value,{componentStack:a.stack,digest:a.digest});if(tu)throw tu=!1,e=nu,nu=null,e;0!==(1&lu)&&0!==e.tag&&Tu(),l=e.pendingLanes,0!==(1&l)?e===su?iu++:(iu=0,su=e):iu=0,Ga()}(e,t,n,r)}finally{zs.transition=a,yt=r}return null}function Tu(){if(null!==ou){var e=xt(lu),t=zs.transition,n=yt;try{if(zs.transition=null,yt=16>e?16:e,null===ou)var r=!1;else{if(e=ou,ou=null,lu=0,0!==(6&Fs))throw Error(o(331));var a=Fs;for(Fs|=4,us=e.current;null!==us;){var l=us,i=l.child;if(0!==(16&us.flags)){var s=l.deletions;if(null!==s){for(var u=0;u<s.length;u++){var c=s[u];for(us=c;null!==us;){var d=us;switch(d.tag){case 0:case 11:case 15:ps(8,d,l)}var f=d.child;if(null!==f)f.return=d,us=f;else for(;null!==us;){var p=(d=us).sibling,h=d.return;if(gs(d),d===c){us=null;break}if(null!==p){p.return=h,us=p;break}us=h}}}var m=l.alternate;if(null!==m){var g=m.child;if(null!==g){m.child=null;do{var v=g.sibling;g.sibling=null,g=v}while(null!==g)}}us=l}}if(0!==(2064&l.subtreeFlags)&&null!==i)i.return=l,us=i;else e:for(;null!==us;){if(0!==(2048&(l=us).flags))switch(l.tag){case 0:case 11:case 15:ps(9,l,l.return)}var y=l.sibling;if(null!==y){y.return=l.return,us=y;break e}us=l.return}}var x=e.current;for(us=x;null!==us;){var b=(i=us).child;if(0!==(2064&i.subtreeFlags)&&null!==b)b.return=i,us=b;else e:for(i=x;null!==us;){if(0!==(2048&(s=us).flags))try{switch(s.tag){case 0:case 11:case 15:hs(9,s)}}catch(j){Mu(s,s.return,j)}if(s===i){us=null;break e}var w=s.sibling;if(null!==w){w.return=s.return,us=w;break e}us=s.return}}if(Fs=a,Ga(),at&&"function"===typeof at.onPostCommitFiberRoot)try{at.onPostCommitFiberRoot(rt,e)}catch(j){}r=!0}return r}finally{yt=n,zs.transition=t}}return!1}function Au(e,t,n){e=Zo(e,t=ki(0,t=bi(n,t),1),1),t=du(),null!==e&&(gt(e,1,t),hu(e,t))}function Mu(e,t,n){if(3===e.tag)Au(e,e,n);else for(;null!==t;){if(3===t.tag){Au(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===ru||!ru.has(r))){t=Zo(t,e=Si(t,e=bi(n,e),1),1),e=du(),null!==t&&(gt(t,1,e),hu(t,e));break}}t=t.return}}function Iu(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=du(),e.pingedLanes|=e.suspendedLanes&n,Us===e&&(Ws&n)===n&&(4===qs||3===qs&&(130023424&Ws)===Ws&&500>Xe()-Ys?Nu(e,0):Js|=n),hu(e,t)}function Du(e,t){0===t&&(0===(1&e.mode)?t=1:(t=ut,0===(130023424&(ut<<=1))&&(ut=4194304)));var n=du();null!==(e=Ho(e,t))&&(gt(e,t,n),hu(e,n))}function zu(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Du(e,n)}function Fu(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(o(314))}null!==r&&r.delete(t),Du(e,n)}function Uu(e,t){return He(e,t)}function Bu(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Wu(e,t,n,r){return new Bu(e,t,n,r)}function Vu(e){return!(!(e=e.prototype)||!e.isReactComponent)}function $u(e,t){var n=e.alternate;return null===n?((n=Wu(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function qu(e,t,n,r,a,l){var i=2;if(r=e,"function"===typeof e)Vu(e)&&(i=1);else if("string"===typeof e)i=5;else e:switch(e){case N:return Hu(n.children,a,l,t);case k:i=8,a|=8;break;case S:return(e=Wu(12,n,t,2|a)).elementType=S,e.lanes=l,e;case P:return(e=Wu(13,n,t,a)).elementType=P,e.lanes=l,e;case _:return(e=Wu(19,n,t,a)).elementType=_,e.lanes=l,e;case T:return Qu(n,a,l,t);default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case E:i=10;break e;case C:i=9;break e;case O:i=11;break e;case R:i=14;break e;case L:i=16,r=null;break e}throw Error(o(130,null==e?e:typeof e,""))}return(t=Wu(i,n,t,a)).elementType=e,t.type=r,t.lanes=l,t}function Hu(e,t,n,r){return(e=Wu(7,e,r,t)).lanes=n,e}function Qu(e,t,n,r){return(e=Wu(22,e,r,t)).elementType=T,e.lanes=n,e.stateNode={isHidden:!1},e}function Ku(e,t,n){return(e=Wu(6,e,null,t)).lanes=n,e}function Ju(e,t,n){return(t=Wu(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Xu(e,t,n,r,a){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=mt(0),this.expirationTimes=mt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=mt(0),this.identifierPrefix=r,this.onRecoverableError=a,this.mutableSourceEagerHydrationData=null}function Zu(e,t,n,r,a,o,l,i,s){return e=new Xu(e,t,n,i,s),1===t?(t=1,!0===o&&(t|=8)):t=0,o=Wu(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ko(o),e}function Yu(e){if(!e)return za;e:{if(Be(e=e._reactInternals)!==e||1!==e.tag)throw Error(o(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Va(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(o(171))}if(1===e.tag){var n=e.type;if(Va(n))return Ha(e,n,t)}return t}function Gu(e,t,n,r,a,o,l,i,s){return(e=Zu(n,r,!0,e,0,o,0,i,s)).context=Yu(null),n=e.current,(o=Xo(r=du(),a=fu(n))).callback=void 0!==t&&null!==t?t:null,Zo(n,o,a),e.current.lanes=a,gt(e,a,r),hu(e,r),e}function ec(e,t,n,r){var a=t.current,o=du(),l=fu(a);return n=Yu(n),null===t.context?t.context=n:t.pendingContext=n,(t=Xo(o,l)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Zo(a,t,l))&&(pu(e,a,l,o),Yo(e,a,l)),l}function tc(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function nc(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function rc(e,t){nc(e,t),(e=e.alternate)&&nc(e,t)}As=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||Ua.current)_i=!0;else{if(0===(e.lanes&n)&&0===(128&t.flags))return _i=!1,function(e,t,n){switch(t.tag){case 3:Ui(t),So();break;case 5:ul(t);break;case 1:Va(t.type)&&Qa(t);break;case 4:il(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,a=t.memoizedProps.value;Da(Ao,r._currentValue),r._currentValue=a;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(Da(dl,1&dl.current),t.flags|=128,null):0!==(n&t.child.childLanes)?Ki(e,t,n):(Da(dl,1&dl.current),null!==(e=ts(e,t,n))?e.sibling:null);Da(dl,1&dl.current);break;case 19:if(r=0!==(n&t.childLanes),0!==(128&e.flags)){if(r)return Gi(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),Da(dl,dl.current),r)break;return null;case 22:case 23:return t.lanes=0,Mi(e,t,n)}return ts(e,t,n)}(e,t,n);_i=0!==(131072&e.flags)}else _i=!1,go&&0!==(1048576&t.flags)&&co(t,ro,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;es(e,t),e=t.pendingProps;var a=Wa(t,Fa.current);Bo(t,n),a=Cl(null,t,r,e,a,n);var l=Ol();return t.flags|=1,"object"===typeof a&&null!==a&&"function"===typeof a.render&&void 0===a.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Va(r)?(l=!0,Qa(t)):l=!1,t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,Ko(t),a.updater=mi,t.stateNode=a,a._reactInternals=t,xi(t,r,e,n),t=Fi(null,t,r,!0,l,n)):(t.tag=0,go&&l&&fo(t),Ri(null,t,a,n),t=t.child),t;case 16:r=t.elementType;e:{switch(es(e,t),e=t.pendingProps,r=(a=r._init)(r._payload),t.type=r,a=t.tag=function(e){if("function"===typeof e)return Vu(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===O)return 11;if(e===R)return 14}return 2}(r),e=pi(r,e),a){case 0:t=Di(null,t,r,e,n);break e;case 1:t=zi(null,t,r,e,n);break e;case 11:t=Li(null,t,r,e,n);break e;case 14:t=Ti(null,t,r,pi(r.type,e),n);break e}throw Error(o(306,r,""))}return t;case 0:return r=t.type,a=t.pendingProps,Di(e,t,r,a=t.elementType===r?a:pi(r,a),n);case 1:return r=t.type,a=t.pendingProps,zi(e,t,r,a=t.elementType===r?a:pi(r,a),n);case 3:e:{if(Ui(t),null===e)throw Error(o(387));r=t.pendingProps,a=(l=t.memoizedState).element,Jo(e,t),el(t,r,null,n);var i=t.memoizedState;if(r=i.element,l.isDehydrated){if(l={element:r,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=l,t.memoizedState=l,256&t.flags){t=Bi(e,t,r,n,a=bi(Error(o(423)),t));break e}if(r!==a){t=Bi(e,t,r,n,a=bi(Error(o(424)),t));break e}for(mo=ba(t.stateNode.containerInfo.firstChild),ho=t,go=!0,vo=null,n=To(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(So(),r===a){t=ts(e,t,n);break e}Ri(e,t,r,n)}t=t.child}return t;case 5:return ul(t),null===e&&wo(t),r=t.type,a=t.pendingProps,l=null!==e?e.memoizedProps:null,i=a.children,pa(r,a)?i=null:null!==l&&pa(r,l)&&(t.flags|=32),Ii(e,t),Ri(e,t,i,n),t.child;case 6:return null===e&&wo(t),null;case 13:return Ki(e,t,n);case 4:return il(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=Lo(t,null,r,n):Ri(e,t,r,n),t.child;case 11:return r=t.type,a=t.pendingProps,Li(e,t,r,a=t.elementType===r?a:pi(r,a),n);case 7:return Ri(e,t,t.pendingProps,n),t.child;case 8:case 12:return Ri(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,a=t.pendingProps,l=t.memoizedProps,i=a.value,Da(Ao,r._currentValue),r._currentValue=i,null!==l)if(yr(l.value,i)){if(l.children===a.children&&!Ua.current){t=ts(e,t,n);break e}}else for(null!==(l=t.child)&&(l.return=t);null!==l;){var s=l.dependencies;if(null!==s){i=l.child;for(var u=s.firstContext;null!==u;){if(u.context===r){if(1===l.tag){(u=Xo(-1,n&-n)).tag=2;var c=l.updateQueue;if(null!==c){var d=(c=c.shared).pending;null===d?u.next=u:(u.next=d.next,d.next=u),c.pending=u}}l.lanes|=n,null!==(u=l.alternate)&&(u.lanes|=n),Uo(l.return,n,t),s.lanes|=n;break}u=u.next}}else if(10===l.tag)i=l.type===t.type?null:l.child;else if(18===l.tag){if(null===(i=l.return))throw Error(o(341));i.lanes|=n,null!==(s=i.alternate)&&(s.lanes|=n),Uo(i,n,t),i=l.sibling}else i=l.child;if(null!==i)i.return=l;else for(i=l;null!==i;){if(i===t){i=null;break}if(null!==(l=i.sibling)){l.return=i.return,i=l;break}i=i.return}l=i}Ri(e,t,a.children,n),t=t.child}return t;case 9:return a=t.type,r=t.pendingProps.children,Bo(t,n),r=r(a=Wo(a)),t.flags|=1,Ri(e,t,r,n),t.child;case 14:return a=pi(r=t.type,t.pendingProps),Ti(e,t,r,a=pi(r.type,a),n);case 15:return Ai(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:pi(r,a),es(e,t),t.tag=1,Va(r)?(e=!0,Qa(t)):e=!1,Bo(t,n),vi(t,r,a),xi(t,r,a,n),Fi(null,t,r,!0,e,n);case 19:return Gi(e,t,n);case 22:return Mi(e,t,n)}throw Error(o(156,t.tag))};var ac="function"===typeof reportError?reportError:function(e){console.error(e)};function oc(e){this._internalRoot=e}function lc(e){this._internalRoot=e}function ic(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function sc(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function uc(){}function cc(e,t,n,r,a){var o=n._reactRootContainer;if(o){var l=o;if("function"===typeof a){var i=a;a=function(){var e=tc(l);i.call(e)}}ec(t,l,e,a)}else l=function(e,t,n,r,a){if(a){if("function"===typeof r){var o=r;r=function(){var e=tc(l);o.call(e)}}var l=Gu(t,r,e,0,null,!1,0,"",uc);return e._reactRootContainer=l,e[Sa]=l.current,Gr(8===e.nodeType?e.parentNode:e),wu(),l}for(;a=e.lastChild;)e.removeChild(a);if("function"===typeof r){var i=r;r=function(){var e=tc(s);i.call(e)}}var s=Zu(e,0,!1,null,0,!1,0,"",uc);return e._reactRootContainer=s,e[Sa]=s.current,Gr(8===e.nodeType?e.parentNode:e),wu((function(){ec(t,s,n,r)})),s}(n,t,e,a,r);return tc(l)}lc.prototype.render=oc.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(o(409));ec(e,t,null,null)},lc.prototype.unmount=oc.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;wu((function(){ec(null,e,null,null)})),t[Sa]=null}},lc.prototype.unstable_scheduleHydration=function(e){if(e){var t=Nt();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Lt.length&&0!==t&&t<Lt[n].priority;n++);Lt.splice(n,0,e),0===n&&It(e)}},bt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=ct(t.pendingLanes);0!==n&&(vt(t,1|n),hu(t,Xe()),0===(6&Fs)&&(Gs=Xe()+500,Ga()))}break;case 13:wu((function(){var t=Ho(e,1);if(null!==t){var n=du();pu(t,e,1,n)}})),rc(e,1)}},wt=function(e){if(13===e.tag){var t=Ho(e,134217728);if(null!==t)pu(t,e,134217728,du());rc(e,134217728)}},jt=function(e){if(13===e.tag){var t=fu(e),n=Ho(e,t);if(null!==n)pu(n,e,t,du());rc(e,t)}},Nt=function(){return yt},kt=function(e,t){var n=yt;try{return yt=e,t()}finally{yt=n}},we=function(e,t,n){switch(t){case"input":if(Y(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=La(r);if(!a)throw Error(o(90));Q(r),Y(r,a)}}}break;case"textarea":oe(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},Ce=bu,Oe=wu;var dc={usingClientEntryPoint:!1,Events:[_a,Ra,La,Se,Ee,bu]},fc={findFiberByHostInstance:Pa,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},pc={bundleType:fc.bundleType,version:fc.version,rendererPackageName:fc.rendererPackageName,rendererConfig:fc.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:b.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=$e(e))?null:e.stateNode},findFiberByHostInstance:fc.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var hc=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!hc.isDisabled&&hc.supportsFiber)try{rt=hc.inject(pc),at=hc}catch(rn){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=dc,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!ic(t))throw Error(o(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:j,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!ic(e))throw Error(o(299));var n=!1,r="",a=ac;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(a=t.onRecoverableError)),t=Zu(e,1,!1,null,0,n,0,r,a),e[Sa]=t.current,Gr(8===e.nodeType?e.parentNode:e),new oc(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(o(188));throw e=Object.keys(e).join(","),Error(o(268,e))}return e=null===(e=$e(t))?null:e.stateNode},t.flushSync=function(e){return wu(e)},t.hydrate=function(e,t,n){if(!sc(t))throw Error(o(200));return cc(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!ic(e))throw Error(o(405));var r=null!=n&&n.hydratedSources||null,a=!1,l="",i=ac;if(null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(a=!0),void 0!==n.identifierPrefix&&(l=n.identifierPrefix),void 0!==n.onRecoverableError&&(i=n.onRecoverableError)),t=Gu(t,null,e,1,null!=n?n:null,a,0,l,i),e[Sa]=t.current,Gr(e),r)for(e=0;e<r.length;e++)a=(a=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,a]:t.mutableSourceEagerHydrationData.push(n,a);return new lc(t)},t.render=function(e,t,n){if(!sc(t))throw Error(o(200));return cc(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!sc(e))throw Error(o(40));return!!e._reactRootContainer&&(wu((function(){cc(null,null,e,!1,(function(){e._reactRootContainer=null,e[Sa]=null}))})),!0)},t.unstable_batchedUpdates=bu,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!sc(n))throw Error(o(200));if(null==e||void 0===e._reactInternals)throw Error(o(38));return cc(e,t,n,!1,r)},t.version="18.3.1-next-f1338f8080-20240426"},763:(e,t,n)=>{e.exports=n(983)},853:(e,t,n)=>{e.exports=n(234)},950:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(730)},983:(e,t)=>{var n="function"===typeof Symbol&&Symbol.for,r=n?Symbol.for("react.element"):60103,a=n?Symbol.for("react.portal"):60106,o=n?Symbol.for("react.fragment"):60107,l=n?Symbol.for("react.strict_mode"):60108,i=n?Symbol.for("react.profiler"):60114,s=n?Symbol.for("react.provider"):60109,u=n?Symbol.for("react.context"):60110,c=n?Symbol.for("react.async_mode"):60111,d=n?Symbol.for("react.concurrent_mode"):60111,f=n?Symbol.for("react.forward_ref"):60112,p=n?Symbol.for("react.suspense"):60113,h=n?Symbol.for("react.suspense_list"):60120,m=n?Symbol.for("react.memo"):60115,g=n?Symbol.for("react.lazy"):60116,v=n?Symbol.for("react.block"):60121,y=n?Symbol.for("react.fundamental"):60117,x=n?Symbol.for("react.responder"):60118,b=n?Symbol.for("react.scope"):60119;function w(e){if("object"===typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case c:case d:case o:case i:case l:case p:return e;default:switch(e=e&&e.$$typeof){case u:case f:case g:case m:case s:return e;default:return t}}case a:return t}}}function j(e){return w(e)===d}t.AsyncMode=c,t.ConcurrentMode=d,t.ContextConsumer=u,t.ContextProvider=s,t.Element=r,t.ForwardRef=f,t.Fragment=o,t.Lazy=g,t.Memo=m,t.Portal=a,t.Profiler=i,t.StrictMode=l,t.Suspense=p,t.isAsyncMode=function(e){return j(e)||w(e)===c},t.isConcurrentMode=j,t.isContextConsumer=function(e){return w(e)===u},t.isContextProvider=function(e){return w(e)===s},t.isElement=function(e){return"object"===typeof e&&null!==e&&e.$$typeof===r},t.isForwardRef=function(e){return w(e)===f},t.isFragment=function(e){return w(e)===o},t.isLazy=function(e){return w(e)===g},t.isMemo=function(e){return w(e)===m},t.isPortal=function(e){return w(e)===a},t.isProfiler=function(e){return w(e)===i},t.isStrictMode=function(e){return w(e)===l},t.isSuspense=function(e){return w(e)===p},t.isValidElementType=function(e){return"string"===typeof e||"function"===typeof e||e===o||e===d||e===i||e===l||e===p||e===h||"object"===typeof e&&null!==e&&(e.$$typeof===g||e.$$typeof===m||e.$$typeof===s||e.$$typeof===u||e.$$typeof===f||e.$$typeof===y||e.$$typeof===x||e.$$typeof===b||e.$$typeof===v)},t.typeOf=w}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,n),o.exports}n.m=e,(()=>{var e,t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__;n.t=function(r,a){if(1&a&&(r=this(r)),8&a)return r;if("object"===typeof r&&r){if(4&a&&r.__esModule)return r;if(16&a&&"function"===typeof r.then)return r}var o=Object.create(null);n.r(o);var l={};e=e||[null,t({}),t([]),t(t)];for(var i=2&a&&r;"object"==typeof i&&!~e.indexOf(i);i=t(i))Object.getOwnPropertyNames(i).forEach((e=>l[e]=()=>r[e]));return l.default=()=>r,n.d(o,l),o}})(),n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.f={},n.e=e=>Promise.all(Object.keys(n.f).reduce(((t,r)=>(n.f[r](e,t),t)),[])),n.u=e=>"static/js/"+e+".322e7791.chunk.js",n.miniCssF=e=>{},n.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={},t="cainuro-orchestrator-ui:";n.l=(r,a,o,l)=>{if(e[r])e[r].push(a);else{var i,s;if(void 0!==o)for(var u=document.getElementsByTagName("script"),c=0;c<u.length;c++){var d=u[c];if(d.getAttribute("src")==r||d.getAttribute("data-webpack")==t+o){i=d;break}}i||(s=!0,(i=document.createElement("script")).charset="utf-8",i.timeout=120,n.nc&&i.setAttribute("nonce",n.nc),i.setAttribute("data-webpack",t+o),i.src=r),e[r]=[a];var f=(t,n)=>{i.onerror=i.onload=null,clearTimeout(p);var a=e[r];if(delete e[r],i.parentNode&&i.parentNode.removeChild(i),a&&a.forEach((e=>e(n))),t)return t(n)},p=setTimeout(f.bind(null,void 0,{type:"timeout",target:i}),12e4);i.onerror=f.bind(null,i.onerror),i.onload=f.bind(null,i.onload),s&&document.head.appendChild(i)}}})(),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.p="/",(()=>{var e={792:0};n.f.j=(t,r)=>{var a=n.o(e,t)?e[t]:void 0;if(0!==a)if(a)r.push(a[2]);else{var o=new Promise(((n,r)=>a=e[t]=[n,r]));r.push(a[2]=o);var l=n.p+n.u(t),i=new Error;n.l(l,(r=>{if(n.o(e,t)&&(0!==(a=e[t])&&(e[t]=void 0),a)){var o=r&&("load"===r.type?"missing":r.type),l=r&&r.target&&r.target.src;i.message="Loading chunk "+t+" failed.\n("+o+": "+l+")",i.name="ChunkLoadError",i.type=o,i.request=l,a[1](i)}}),"chunk-"+t,t)}};var t=(t,r)=>{var a,o,l=r[0],i=r[1],s=r[2],u=0;if(l.some((t=>0!==e[t]))){for(a in i)n.o(i,a)&&(n.m[a]=i[a]);if(s)s(n)}for(t&&t(r);u<l.length;u++)o=l[u],n.o(e,o)&&e[o]&&e[o][0](),e[o]=0},r=self.webpackChunkcainuro_orchestrator_ui=self.webpackChunkcainuro_orchestrator_ui||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})();var r={};n.r(r),n.d(r,{hasBrowserEnv:()=>Qa,hasStandardBrowserEnv:()=>Ja,hasStandardBrowserWebWorkerEnv:()=>Xa,navigator:()=>Ka,origin:()=>Za});var a=n(43),o=n.t(a,2),l=n(391),i=n(461),s=n(443),u=n(950),c=n.t(u,2);let d=function(e){e()};const f=()=>d,p=Symbol.for("react-redux-context"),h="undefined"!==typeof globalThis?globalThis:{};function m(){var e;if(!a.createContext)return{};const t=null!=(e=h[p])?e:h[p]=new Map;let n=t.get(a.createContext);return n||(n=a.createContext(null),t.set(a.createContext,n)),n}const g=m();function v(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:g;return function(){return(0,a.useContext)(e)}}const y=v();let x=()=>{throw new Error("uSES not initialized!")};const b=(e,t)=>e===t;function w(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:g;const t=e===g?y:v(e);return function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{equalityFn:r=b,stabilityCheck:o,noopCheck:l}="function"===typeof n?{equalityFn:n}:n;const{store:i,subscription:s,getServerState:u,stabilityCheck:c,noopCheck:d}=t(),f=((0,a.useRef)(!0),(0,a.useCallback)({[e.name]:t=>e(t)}[e.name],[e,c,o])),p=x(s.addNestedSub,i.getState,u||i.getState,f,r);return(0,a.useDebugValue)(p),p}}const j=w();n(219),n(86);const N={notify(){},get:()=>[]};function k(e,t){let n,r=N,a=0,o=!1;function l(){u.onStateChange&&u.onStateChange()}function i(){a++,n||(n=t?t.addNestedSub(l):e.subscribe(l),r=function(){const e=f();let t=null,n=null;return{clear(){t=null,n=null},notify(){e((()=>{let e=t;for(;e;)e.callback(),e=e.next}))},get(){let e=[],n=t;for(;n;)e.push(n),n=n.next;return e},subscribe(e){let r=!0,a=n={callback:e,next:null,prev:n};return a.prev?a.prev.next=a:t=a,function(){r&&null!==t&&(r=!1,a.next?a.next.prev=a.prev:n=a.prev,a.prev?a.prev.next=a.next:t=a.next)}}}}())}function s(){a--,n&&0===a&&(n(),n=void 0,r.clear(),r=N)}const u={addNestedSub:function(e){i();const t=r.subscribe(e);let n=!1;return()=>{n||(n=!0,t(),s())}},notifyNestedSubs:function(){r.notify()},handleChangeWrapper:l,isSubscribed:function(){return o},trySubscribe:function(){o||(o=!0,i())},tryUnsubscribe:function(){o&&(o=!1,s())},getListeners:()=>r};return u}const S=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement)?a.useLayoutEffect:a.useEffect;let E=null;const C=function(e){let{store:t,context:n,children:r,serverState:o,stabilityCheck:l="once",noopCheck:i="once"}=e;const s=a.useMemo((()=>{const e=k(t);return{store:t,subscription:e,getServerState:o?()=>o:void 0,stabilityCheck:l,noopCheck:i}}),[t,o,l,i]),u=a.useMemo((()=>t.getState()),[t]);S((()=>{const{subscription:e}=s;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),u!==t.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}}),[s,u]);const c=n||g;return a.createElement(c.Provider,{value:s},r)};function O(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:g;const t=e===g?y:v(e);return function(){const{store:e}=t();return e}}const P=O();function _(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:g;const t=e===g?P:O(e);return function(){return t().dispatch}}const R=_();var L,T;function A(){return A=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},A.apply(this,arguments)}(e=>{x=e})(s.useSyncExternalStoreWithSelector),(e=>{E=e})(i.useSyncExternalStore),L=u.unstable_batchedUpdates,d=L,function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"}(T||(T={}));const M="popstate";function I(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}function D(e,t){if(!e){"undefined"!==typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function z(e,t){return{usr:e.state,key:e.key,idx:t}}function F(e,t,n,r){return void 0===n&&(n=null),A({pathname:"string"===typeof e?e:e.pathname,search:"",hash:""},"string"===typeof t?B(t):t,{state:n,key:t&&t.key||r||Math.random().toString(36).substr(2,8)})}function U(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function B(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function W(e,t,n,r){void 0===r&&(r={});let{window:a=document.defaultView,v5Compat:o=!1}=r,l=a.history,i=T.Pop,s=null,u=c();function c(){return(l.state||{idx:null}).idx}function d(){i=T.Pop;let e=c(),t=null==e?null:e-u;u=e,s&&s({action:i,location:p.location,delta:t})}function f(e){let t="null"!==a.location.origin?a.location.origin:a.location.href,n="string"===typeof e?e:U(e);return n=n.replace(/ $/,"%20"),I(t,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,t)}null==u&&(u=0,l.replaceState(A({},l.state,{idx:u}),""));let p={get action(){return i},get location(){return e(a,l)},listen(e){if(s)throw new Error("A history only accepts one active listener");return a.addEventListener(M,d),s=e,()=>{a.removeEventListener(M,d),s=null}},createHref:e=>t(a,e),createURL:f,encodeLocation(e){let t=f(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){i=T.Push;let r=F(p.location,e,t);n&&n(r,e),u=c()+1;let d=z(r,u),f=p.createHref(r);try{l.pushState(d,"",f)}catch(h){if(h instanceof DOMException&&"DataCloneError"===h.name)throw h;a.location.assign(f)}o&&s&&s({action:i,location:p.location,delta:1})},replace:function(e,t){i=T.Replace;let r=F(p.location,e,t);n&&n(r,e),u=c();let a=z(r,u),d=p.createHref(r);l.replaceState(a,"",d),o&&s&&s({action:i,location:p.location,delta:0})},go:e=>l.go(e)};return p}var V;!function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"}(V||(V={}));new Set(["lazy","caseSensitive","path","id","index","children"]);function $(e,t,n){return void 0===n&&(n="/"),q(e,t,n,!1)}function q(e,t,n,r){let a=oe(("string"===typeof t?B(t):t).pathname||"/",n);if(null==a)return null;let o=H(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every(((e,n)=>e===t[n]));return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(o);let l=null;for(let i=0;null==l&&i<o.length;++i){let e=ae(a);l=ne(o[i],e,r)}return l}function H(e,t,n,r){void 0===t&&(t=[]),void 0===n&&(n=[]),void 0===r&&(r="");let a=(e,a,o)=>{let l={relativePath:void 0===o?e.path||"":o,caseSensitive:!0===e.caseSensitive,childrenIndex:a,route:e};l.relativePath.startsWith("/")&&(I(l.relativePath.startsWith(r),'Absolute route path "'+l.relativePath+'" nested under path "'+r+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),l.relativePath=l.relativePath.slice(r.length));let i=ce([r,l.relativePath]),s=n.concat(l);e.children&&e.children.length>0&&(I(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+i+'".'),H(e.children,t,s,i)),(null!=e.path||e.index)&&t.push({path:i,score:te(i,e.index),routesMeta:s})};return e.forEach(((e,t)=>{var n;if(""!==e.path&&null!=(n=e.path)&&n.includes("?"))for(let r of Q(e.path))a(e,t,r);else a(e,t)})),t}function Q(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,a=n.endsWith("?"),o=n.replace(/\?$/,"");if(0===r.length)return a?[o,""]:[o];let l=Q(r.join("/")),i=[];return i.push(...l.map((e=>""===e?o:[o,e].join("/")))),a&&i.push(...l),i.map((t=>e.startsWith("/")&&""===t?"/":t))}const K=/^:[\w-]+$/,J=3,X=2,Z=1,Y=10,G=-2,ee=e=>"*"===e;function te(e,t){let n=e.split("/"),r=n.length;return n.some(ee)&&(r+=G),t&&(r+=X),n.filter((e=>!ee(e))).reduce(((e,t)=>e+(K.test(t)?J:""===t?Z:Y)),r)}function ne(e,t,n){void 0===n&&(n=!1);let{routesMeta:r}=e,a={},o="/",l=[];for(let i=0;i<r.length;++i){let e=r[i],s=i===r.length-1,u="/"===o?t:t.slice(o.length)||"/",c=re({path:e.relativePath,caseSensitive:e.caseSensitive,end:s},u),d=e.route;if(!c&&s&&n&&!r[r.length-1].route.index&&(c=re({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},u)),!c)return null;Object.assign(a,c.params),l.push({params:a,pathname:ce([o,c.pathname]),pathnameBase:de(ce([o,c.pathnameBase])),route:d}),"/"!==c.pathnameBase&&(o=ce([o,c.pathnameBase]))}return l}function re(e,t){"string"===typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=function(e,t,n){void 0===t&&(t=!1);void 0===n&&(n=!0);D("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)")));e.endsWith("*")?(r.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))");let o=new RegExp(a,t?void 0:"i");return[o,r]}(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let o=a[0],l=o.replace(/(.)\/+$/,"$1"),i=a.slice(1),s=r.reduce(((e,t,n)=>{let{paramName:r,isOptional:a}=t;if("*"===r){let e=i[n]||"";l=o.slice(0,o.length-e.length).replace(/(.)\/+$/,"$1")}const s=i[n];return e[r]=a&&!s?void 0:(s||"").replace(/%2F/g,"/"),e}),{});return{params:s,pathname:o,pathnameBase:l,pattern:e}}function ae(e){try{return e.split("/").map((e=>decodeURIComponent(e).replace(/\//g,"%2F"))).join("/")}catch(t){return D(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function oe(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function le(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function ie(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}function se(e,t){let n=ie(e);return t?n.map(((e,t)=>t===n.length-1?e.pathname:e.pathnameBase)):n.map((e=>e.pathnameBase))}function ue(e,t,n,r){let a;void 0===r&&(r=!1),"string"===typeof e?a=B(e):(a=A({},e),I(!a.pathname||!a.pathname.includes("?"),le("?","pathname","search",a)),I(!a.pathname||!a.pathname.includes("#"),le("#","pathname","hash",a)),I(!a.search||!a.search.includes("#"),le("#","search","hash",a)));let o,l=""===e||""===a.pathname,i=l?"/":a.pathname;if(null==i)o=n;else{let e=t.length-1;if(!r&&i.startsWith("..")){let t=i.split("/");for(;".."===t[0];)t.shift(),e-=1;a.pathname=t.join("/")}o=e>=0?t[e]:"/"}let s=function(e,t){void 0===t&&(t="/");let{pathname:n,search:r="",hash:a=""}="string"===typeof e?B(e):e,o=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)})),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:o,search:fe(r),hash:pe(a)}}(a,o),u=i&&"/"!==i&&i.endsWith("/"),c=(l||"."===i)&&n.endsWith("/");return s.pathname.endsWith("/")||!u&&!c||(s.pathname+="/"),s}const ce=e=>e.join("/").replace(/\/\/+/g,"/"),de=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),fe=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",pe=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";Error;function he(e){return null!=e&&"number"===typeof e.status&&"string"===typeof e.statusText&&"boolean"===typeof e.internal&&"data"in e}const me=["post","put","patch","delete"],ge=(new Set(me),["get",...me]);new Set(ge),new Set([301,302,303,307,308]),new Set([307,308]);Symbol("deferred");function ve(){return ve=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ve.apply(this,arguments)}const ye=a.createContext(null);const xe=a.createContext(null);const be=a.createContext(null);const we=a.createContext(null);const je=a.createContext({outlet:null,matches:[],isDataRoute:!1});const Ne=a.createContext(null);function ke(){return null!=a.useContext(we)}function Se(){return ke()||I(!1),a.useContext(we).location}function Ee(e){a.useContext(be).static||a.useLayoutEffect(e)}function Ce(){let{isDataRoute:e}=a.useContext(je);return e?function(){let{router:e}=De(Me.UseNavigateStable),t=Fe(Ie.UseNavigateStable),n=a.useRef(!1);return Ee((()=>{n.current=!0})),a.useCallback((function(r,a){void 0===a&&(a={}),n.current&&("number"===typeof r?e.navigate(r):e.navigate(r,ve({fromRouteId:t},a)))}),[e,t])}():function(){ke()||I(!1);let e=a.useContext(ye),{basename:t,future:n,navigator:r}=a.useContext(be),{matches:o}=a.useContext(je),{pathname:l}=Se(),i=JSON.stringify(se(o,n.v7_relativeSplatPath)),s=a.useRef(!1);return Ee((()=>{s.current=!0})),a.useCallback((function(n,a){if(void 0===a&&(a={}),!s.current)return;if("number"===typeof n)return void r.go(n);let o=ue(n,JSON.parse(i),l,"path"===a.relative);null==e&&"/"!==t&&(o.pathname="/"===o.pathname?t:ce([t,o.pathname])),(a.replace?r.replace:r.push)(o,a.state,a)}),[t,r,i,l,e])}()}function Oe(e,t){let{relative:n}=void 0===t?{}:t,{future:r}=a.useContext(be),{matches:o}=a.useContext(je),{pathname:l}=Se(),i=JSON.stringify(se(o,r.v7_relativeSplatPath));return a.useMemo((()=>ue(e,JSON.parse(i),l,"path"===n)),[e,i,l,n])}function Pe(e,t,n,r){ke()||I(!1);let{navigator:o}=a.useContext(be),{matches:l}=a.useContext(je),i=l[l.length-1],s=i?i.params:{},u=(i&&i.pathname,i?i.pathnameBase:"/");i&&i.route;let c,d=Se();if(t){var f;let e="string"===typeof t?B(t):t;"/"===u||(null==(f=e.pathname)?void 0:f.startsWith(u))||I(!1),c=e}else c=d;let p=c.pathname||"/",h=p;if("/"!==u){let e=u.replace(/^\//,"").split("/");h="/"+p.replace(/^\//,"").split("/").slice(e.length).join("/")}let m=$(e,{pathname:h});let g=Ae(m&&m.map((e=>Object.assign({},e,{params:Object.assign({},s,e.params),pathname:ce([u,o.encodeLocation?o.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?u:ce([u,o.encodeLocation?o.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),l,n,r);return t&&g?a.createElement(we.Provider,{value:{location:ve({pathname:"/",search:"",hash:"",state:null,key:"default"},c),navigationType:T.Pop}},g):g}function _e(){let e=function(){var e;let t=a.useContext(Ne),n=ze(Ie.UseRouteError),r=Fe(Ie.UseRouteError);if(void 0!==t)return t;return null==(e=n.errors)?void 0:e[r]}(),t=he(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r="rgba(200,200,200, 0.5)",o={padding:"0.5rem",backgroundColor:r};return a.createElement(a.Fragment,null,a.createElement("h2",null,"Unexpected Application Error!"),a.createElement("h3",{style:{fontStyle:"italic"}},t),n?a.createElement("pre",{style:o},n):null,null)}const Re=a.createElement(_e,null);class Le extends a.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?a.createElement(je.Provider,{value:this.props.routeContext},a.createElement(Ne.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Te(e){let{routeContext:t,match:n,children:r}=e,o=a.useContext(ye);return o&&o.static&&o.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=n.route.id),a.createElement(je.Provider,{value:t},r)}function Ae(e,t,n,r){var o;if(void 0===t&&(t=[]),void 0===n&&(n=null),void 0===r&&(r=null),null==e){var l;if(!n)return null;if(n.errors)e=n.matches;else{if(!(null!=(l=r)&&l.v7_partialHydration&&0===t.length&&!n.initialized&&n.matches.length>0))return null;e=n.matches}}let i=e,s=null==(o=n)?void 0:o.errors;if(null!=s){let e=i.findIndex((e=>e.route.id&&void 0!==(null==s?void 0:s[e.route.id])));e>=0||I(!1),i=i.slice(0,Math.min(i.length,e+1))}let u=!1,c=-1;if(n&&r&&r.v7_partialHydration)for(let a=0;a<i.length;a++){let e=i[a];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(c=a),e.route.id){let{loaderData:t,errors:r}=n,a=e.route.loader&&void 0===t[e.route.id]&&(!r||void 0===r[e.route.id]);if(e.route.lazy||a){u=!0,i=c>=0?i.slice(0,c+1):[i[0]];break}}}return i.reduceRight(((e,r,o)=>{let l,d=!1,f=null,p=null;var h;n&&(l=s&&r.route.id?s[r.route.id]:void 0,f=r.route.errorElement||Re,u&&(c<0&&0===o?(h="route-fallback",!1||Ue[h]||(Ue[h]=!0),d=!0,p=null):c===o&&(d=!0,p=r.route.hydrateFallbackElement||null)));let m=t.concat(i.slice(0,o+1)),g=()=>{let t;return t=l?f:d?p:r.route.Component?a.createElement(r.route.Component,null):r.route.element?r.route.element:e,a.createElement(Te,{match:r,routeContext:{outlet:e,matches:m,isDataRoute:null!=n},children:t})};return n&&(r.route.ErrorBoundary||r.route.errorElement||0===o)?a.createElement(Le,{location:n.location,revalidation:n.revalidation,component:f,error:l,children:g(),routeContext:{outlet:null,matches:m,isDataRoute:!0}}):g()}),null)}var Me=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(Me||{}),Ie=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Ie||{});function De(e){let t=a.useContext(ye);return t||I(!1),t}function ze(e){let t=a.useContext(xe);return t||I(!1),t}function Fe(e){let t=function(){let e=a.useContext(je);return e||I(!1),e}(),n=t.matches[t.matches.length-1];return n.route.id||I(!1),n.route.id}const Ue={};function Be(e,t){null==e||e.v7_startTransition,void 0===(null==e?void 0:e.v7_relativeSplatPath)&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}o.startTransition;function We(e){I(!1)}function Ve(e){let{basename:t="/",children:n=null,location:r,navigationType:o=T.Pop,navigator:l,static:i=!1,future:s}=e;ke()&&I(!1);let u=t.replace(/^\/*/,"/"),c=a.useMemo((()=>({basename:u,navigator:l,static:i,future:ve({v7_relativeSplatPath:!1},s)})),[u,s,l,i]);"string"===typeof r&&(r=B(r));let{pathname:d="/",search:f="",hash:p="",state:h=null,key:m="default"}=r,g=a.useMemo((()=>{let e=oe(d,u);return null==e?null:{location:{pathname:e,search:f,hash:p,state:h,key:m},navigationType:o}}),[u,d,f,p,h,m,o]);return null==g?null:a.createElement(be.Provider,{value:c},a.createElement(we.Provider,{children:n,value:g}))}function $e(e){let{children:t,location:n}=e;return Pe(qe(t),n)}new Promise((()=>{}));a.Component;function qe(e,t){void 0===t&&(t=[]);let n=[];return a.Children.forEach(e,((e,r)=>{if(!a.isValidElement(e))return;let o=[...t,r];if(e.type===a.Fragment)return void n.push.apply(n,qe(e.props.children,o));e.type!==We&&I(!1),e.props.index&&e.props.children&&I(!1);let l={id:e.props.id||o.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(l.children=qe(e.props.children,o)),n.push(l)})),n}function He(){return He=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},He.apply(this,arguments)}function Qe(e,t){if(null==e)return{};var n,r,a={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);const Ke=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"];try{window.__reactRouterVersion="6"}catch(tu){}new Map;const Je=o.startTransition;c.flushSync,o.useId;function Xe(e){let{basename:t,children:n,future:r,window:o}=e,l=a.useRef();var i;null==l.current&&(l.current=(void 0===(i={window:o,v5Compat:!0})&&(i={}),W((function(e,t){let{pathname:n,search:r,hash:a}=e.location;return F("",{pathname:n,search:r,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){return"string"===typeof t?t:U(t)}),null,i)));let s=l.current,[u,c]=a.useState({action:s.action,location:s.location}),{v7_startTransition:d}=r||{},f=a.useCallback((e=>{d&&Je?Je((()=>c(e))):c(e)}),[c,d]);return a.useLayoutEffect((()=>s.listen(f)),[s,f]),a.useEffect((()=>Be(r)),[r]),a.createElement(Ve,{basename:t,children:n,location:u.location,navigationType:u.action,navigator:s,future:r})}const Ze="undefined"!==typeof window&&"undefined"!==typeof window.document&&"undefined"!==typeof window.document.createElement,Ye=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Ge=a.forwardRef((function(e,t){let n,{onClick:r,relative:o,reloadDocument:l,replace:i,state:s,target:u,to:c,preventScrollReset:d,viewTransition:f}=e,p=Qe(e,Ke),{basename:h}=a.useContext(be),m=!1;if("string"===typeof c&&Ye.test(c)&&(n=c,Ze))try{let e=new URL(window.location.href),t=c.startsWith("//")?new URL(e.protocol+c):new URL(c),n=oe(t.pathname,h);t.origin===e.origin&&null!=n?c=n+t.search+t.hash:m=!0}catch(tu){}let g=function(e,t){let{relative:n}=void 0===t?{}:t;ke()||I(!1);let{basename:r,navigator:o}=a.useContext(be),{hash:l,pathname:i,search:s}=Oe(e,{relative:n}),u=i;return"/"!==r&&(u="/"===i?r:ce([r,i])),o.createHref({pathname:u,search:s,hash:l})}(c,{relative:o}),v=function(e,t){let{target:n,replace:r,state:o,preventScrollReset:l,relative:i,viewTransition:s}=void 0===t?{}:t,u=Ce(),c=Se(),d=Oe(e,{relative:i});return a.useCallback((t=>{if(function(e,t){return 0===e.button&&(!t||"_self"===t)&&!function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e)}(t,n)){t.preventDefault();let n=void 0!==r?r:U(c)===U(d);u(e,{replace:n,state:o,preventScrollReset:l,relative:i,viewTransition:s})}}),[c,u,d,r,o,n,e,l,i,s])}(c,{replace:i,state:s,target:u,preventScrollReset:d,relative:o,viewTransition:f});return a.createElement("a",He({},p,{href:n||g,onClick:m||l?r:function(e){r&&r(e),e.defaultPrevented||v(e)},ref:t,target:u}))}));var et,tt;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(et||(et={})),function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"}(tt||(tt={}));function nt(e){return nt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},nt(e)}function rt(e){var t=function(e,t){if("object"!=nt(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=nt(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==nt(t)?t:t+""}function at(e,t,n){return(t=rt(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ot(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function lt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ot(Object(n),!0).forEach((function(t){at(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ot(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var it=n(579);const st=(0,a.createContext)(void 0),ut=e=>{let{children:t}=e;const[n,r]=(0,a.useState)({isAuthenticated:!1,isLoading:!0,user:null,token:null,error:null});(0,a.useEffect)((()=>{o()}),[]);const o=async()=>{try{r((e=>lt(lt({},e),{},{isLoading:!0,error:null})));const e=await fetch("/v1/auth/user",{credentials:"include"});if(e.ok){const t=await e.json();r((e=>lt(lt({},e),{},{isAuthenticated:!0,user:t,isLoading:!1})))}else await l()}catch(e){console.error("Auth initialization failed:",e),r((e=>lt(lt({},e),{},{isAuthenticated:!1,isLoading:!1,error:"Authentication initialization failed"})))}},l=async()=>{try{const e=await fetch("/v1/auth/refresh",{method:"POST",credentials:"include"});if(!e.ok)throw new Error("Token refresh failed");{const t=await e.json(),n=await fetch("/v1/auth/user",{credentials:"include"});if(n.ok){const e=await n.json();r((n=>lt(lt({},n),{},{isAuthenticated:!0,user:e,token:t,isLoading:!1,error:null})))}}}catch(e){console.error("Token refresh failed:",e),r((e=>lt(lt({},e),{},{isAuthenticated:!1,isLoading:!1,user:null,token:null,error:"Session expired"})))}};(0,a.useEffect)((()=>{if(n.token&&n.isAuthenticated){const e=setInterval((()=>{l()}),3e6);return()=>clearInterval(e)}}),[n.token,n.isAuthenticated]);const i=lt(lt({},n),{},{login:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"admin",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"admin";try{r((e=>lt(lt({},e),{},{isLoading:!0,error:null})));const n=await fetch("/v1/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({username:e,password:t})});if(!n.ok){const e=await n.json();throw new Error(e.error||"Login failed")}const a=await n.json();r((e=>lt(lt({},e),{},{isAuthenticated:!0,user:a.user,token:null,isLoading:!1,error:null})))}catch(n){console.error("Login failed:",n),r((e=>lt(lt({},e),{},{isLoading:!1,error:n instanceof Error?n.message:"Login failed"})))}},logout:async()=>{try{r((e=>lt(lt({},e),{},{isLoading:!0})));const e=await fetch("/v1/auth/logout",{method:"POST",credentials:"include"});if(r({isAuthenticated:!1,isLoading:!1,user:null,token:null,error:null}),e.ok){const t=await e.json();if(t.logout_url&&t.logout_url!==window.location.origin)return void(window.location.href=t.logout_url)}window.location.reload()}catch(e){console.error("Logout failed:",e),r((e=>lt(lt({},e),{},{isLoading:!1,error:"Logout failed"})))}},checkPermission:async(e,t)=>{try{var n;const r=await fetch("/v1/auth/check",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({action:e,resource:t})});if(!r.ok)return!1;return(null===(n=(await r.json()).decision)||void 0===n?void 0:n.allowed)||!1}catch(r){return console.error("Permission check failed:",r),!1}},refreshToken:l});return(0,it.jsx)(st.Provider,{value:i,children:t})},ct=()=>{const e=(0,a.useContext)(st);if(void 0===e)throw new Error("useAuth must be used within an AuthProvider");return e},dt=e=>{let{children:t,requiredPermission:n,fallback:r=(0,it.jsx)("div",{className:"p-4 text-red-600",children:"Access Denied"})}=e;const{isAuthenticated:o,isLoading:l,checkPermission:i}=ct(),[s,u]=(0,a.useState)(null);return(0,a.useEffect)((()=>{o&&n?i(n.action,n.resource).then(u):o&&u(!0)}),[o,n,i]),l?(0,it.jsx)("div",{className:"p-4",children:"Loading..."}):o?n&&!1===s?(0,it.jsx)(it.Fragment,{children:r}):n&&null===s?(0,it.jsx)("div",{className:"p-4",children:"Checking permissions..."}):(0,it.jsx)(it.Fragment,{children:t}):(0,it.jsx)("div",{className:"p-4",children:"Please log in to access this page."})},ft=()=>{const{login:e,isLoading:t,error:n}=ct(),[r,o]=a.useState("admin"),[l,i]=a.useState("admin");return(0,it.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center p-4",children:(0,it.jsxs)("div",{className:"max-w-md w-full bg-white/10 backdrop-blur-lg rounded-2xl shadow-2xl p-8 border border-white/20",children:[(0,it.jsxs)("div",{className:"text-center mb-8",children:[(0,it.jsx)("div",{className:"text-6xl mb-4",children:"\ud83c\udf1f"}),(0,it.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"CAINuro Orchestrator"}),(0,it.jsx)("p",{className:"text-blue-200",children:"Enterprise Cloud Resource Orchestration Platform"})]}),(0,it.jsxs)("form",{onSubmit:t=>{t.preventDefault(),e(r,l)},className:"mb-6",children:[(0,it.jsx)("div",{className:"bg-blue-500/20 border border-blue-500/50 rounded-lg p-4 mb-4",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"text-blue-400 mr-2",children:"\u2139\ufe0f"}),(0,it.jsxs)("div",{className:"text-blue-200 text-sm",children:[(0,it.jsx)("strong",{children:"Default Credentials:"})," admin/admin or user/user"]})]})}),n&&(0,it.jsx)("div",{className:"bg-red-500/20 border border-red-500/50 rounded-lg p-4 mb-4",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"text-red-400 mr-2",children:"\u26a0\ufe0f"}),(0,it.jsx)("div",{className:"text-red-200 text-sm",children:n})]})}),(0,it.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{htmlFor:"username",className:"block text-sm font-medium text-white mb-2",children:"Username"}),(0,it.jsx)("input",{id:"username",type:"text",value:r,onChange:e=>o(e.target.value),className:"w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter username",required:!0})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-white mb-2",children:"Password"}),(0,it.jsx)("input",{id:"password",type:"password",value:l,onChange:e=>i(e.target.value),className:"w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter password",required:!0})]})]}),(0,it.jsx)("button",{type:"submit",disabled:t,className:"w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:from-gray-600 disabled:to-gray-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105 disabled:scale-100 disabled:cursor-not-allowed shadow-lg",children:t?(0,it.jsxs)("div",{className:"flex items-center justify-center",children:[(0,it.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Signing in..."]}):(0,it.jsxs)("div",{className:"flex items-center justify-center",children:[(0,it.jsx)("div",{className:"mr-2",children:"\ud83d\udd10"}),"Sign In"]})})]}),(0,it.jsxs)("div",{className:"border-t border-white/20 pt-6",children:[(0,it.jsx)("div",{className:"text-center mb-4",children:(0,it.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"\ud83d\ude80 Authentication Features"})}),(0,it.jsxs)("div",{className:"grid grid-cols-1 gap-3 text-sm",children:[(0,it.jsxs)("div",{className:"flex items-center text-green-300",children:[(0,it.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full mr-3"}),"OIDC/OAuth2 Integration"]}),(0,it.jsxs)("div",{className:"flex items-center text-green-300",children:[(0,it.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full mr-3"}),"JWT Token Management"]}),(0,it.jsxs)("div",{className:"flex items-center text-green-300",children:[(0,it.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full mr-3"}),"Role-Based Access Control (RBAC)"]}),(0,it.jsxs)("div",{className:"flex items-center text-green-300",children:[(0,it.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full mr-3"}),"Session Management"]}),(0,it.jsxs)("div",{className:"flex items-center text-green-300",children:[(0,it.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full mr-3"}),"Multi-Factor Authentication"]}),(0,it.jsxs)("div",{className:"flex items-center text-green-300",children:[(0,it.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full mr-3"}),"Audit Logging"]})]})]}),(0,it.jsx)("div",{className:"mt-6 pt-6 border-t border-white/20",children:(0,it.jsxs)("div",{className:"text-center",children:[(0,it.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"\ud83c\udf10 Platform Capabilities"}),(0,it.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-xs text-blue-200",children:[(0,it.jsx)("div",{children:"\ud83d\udd0d Multi-Cloud Discovery"}),(0,it.jsx)("div",{children:"\u26a1 Workflow Automation"}),(0,it.jsx)("div",{children:"\u2638\ufe0f Kubernetes Management"}),(0,it.jsx)("div",{children:"\ud83d\udca5 Chaos Engineering"}),(0,it.jsx)("div",{children:"\ud83e\udd16 ChatOps Integration"}),(0,it.jsx)("div",{children:"\ud83d\udcca Real-time Monitoring"}),(0,it.jsx)("div",{children:"\ud83d\udd78\ufe0f Topology Mapping"}),(0,it.jsx)("div",{children:"\ud83d\udc19 GitHub Integration"}),(0,it.jsx)("div",{children:"\ud83d\udd17 URL Management"}),(0,it.jsx)("div",{children:"\ud83d\udccb Project Management"}),(0,it.jsx)("div",{children:"\ud83d\udcdd Feedback Collection"}),(0,it.jsx)("div",{children:"\ud83d\udee1\ufe0f Security Scanning"})]})]})}),(0,it.jsx)("div",{className:"mt-6 text-center",children:(0,it.jsx)("p",{className:"text-xs text-blue-300",children:"Powered by CAINuro \u2022 Enterprise Grade \u2022 Production Ready"})})]})})};function pt(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}const ht=["title","titleId"];function mt(e,t){let{title:n,titleId:r}=e,o=pt(e,ht);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},o),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"}))}const gt=a.forwardRef(mt),vt=["title","titleId"];function yt(e,t){let{title:n,titleId:r}=e,o=pt(e,vt);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},o),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))}const xt=a.forwardRef(yt),bt=["title","titleId"];function wt(e,t){let{title:n,titleId:r}=e,o=pt(e,bt);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},o),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 12a7.5 7.5 0 0 0 15 0m-15 0a7.5 7.5 0 1 1 15 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077 1.41-.513m14.095-5.13 1.41-.513M5.106 17.785l1.15-.964m11.49-9.642 1.149-.964M7.501 19.795l.75-1.3m7.5-12.99.75-1.3m-6.063 16.658.26-1.477m2.605-14.772.26-1.477m0 17.726-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205 12 12m6.894 5.785-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495"}))}const jt=a.forwardRef(wt),Nt=["title","titleId"];function kt(e,t){let{title:n,titleId:r}=e,o=pt(e,Nt);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},o),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418"}))}const St=a.forwardRef(kt),Et=["title","titleId"];function Ct(e,t){let{title:n,titleId:r}=e,o=pt(e,Et);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},o),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 18 9 11.25l4.306 4.306a11.95 11.95 0 0 1 5.814-5.518l2.74-1.22m0 0-5.94-2.281m5.94 2.28-2.28 5.941"}))}const Ot=a.forwardRef(Ct),Pt=["title","titleId"];function _t(e,t){let{title:n,titleId:r}=e,o=pt(e,Pt);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},o),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}))}const Rt=a.forwardRef(_t),Lt=["title","titleId"];function Tt(e,t){let{title:n,titleId:r}=e,o=pt(e,Lt);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},o),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125"}))}const At=a.forwardRef(Tt),Mt=["title","titleId"];function It(e,t){let{title:n,titleId:r}=e,o=pt(e,Mt);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},o),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))}const Dt=a.forwardRef(It),zt=["title","titleId"];function Ft(e,t){let{title:n,titleId:r}=e,o=pt(e,zt);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},o),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M17.982 18.725A7.488 7.488 0 0 0 12 15.75a7.488 7.488 0 0 0-5.982 2.975m11.963 0a9 9 0 1 0-11.963 0m11.963 0A8.966 8.966 0 0 1 12 21a8.966 8.966 0 0 1-5.982-2.275M15 9.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}const Ut=a.forwardRef(Ft),Bt=["title","titleId"];function Wt(e,t){let{title:n,titleId:r}=e,o=pt(e,Bt);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},o),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 9V5.25A2.25 2.25 0 0 0 13.5 3h-6a2.25 2.25 0 0 0-2.25 2.25v13.5A2.25 2.25 0 0 0 7.5 21h6a2.25 2.25 0 0 0 2.25-2.25V15m3 0 3-3m0 0-3-3m3 3H9"}))}const Vt=a.forwardRef(Wt),$t=["title","titleId"];function qt(e,t){let{title:n,titleId:r}=e,o=pt(e,$t);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},o),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"}))}const Ht=a.forwardRef(qt),Qt=[{name:"Dashboard",href:"/dashboard",icon:gt},{name:"Search",href:"/search",icon:xt},{name:"Discovery Wizard",href:"/discovery",icon:xt},{name:"Workflows",href:"/workflows",icon:jt},{name:"Envoy Config",href:"/envoy",icon:St},{name:"Autoscaler",href:"/autoscaler",icon:Ot},{name:"Audit",href:"/audit",icon:Rt},{name:"Database",href:"/database",icon:At}];function Kt(e){var t,n;let{children:r}=e;const[o,l]=(0,a.useState)(!1),[i,s]=(0,a.useState)(!1),u=Se(),{user:c,logout:d}=ct();return(0,it.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,it.jsxs)("div",{className:"fixed inset-0 z-50 lg:hidden ".concat(o?"block":"hidden"),children:[(0,it.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75",onClick:()=>l(!1)}),(0,it.jsxs)("div",{className:"fixed inset-y-0 left-0 flex w-64 flex-col bg-gray-800",children:[(0,it.jsxs)("div",{className:"flex h-16 items-center justify-between px-4",children:[(0,it.jsx)("div",{className:"flex items-center",children:(0,it.jsx)("span",{className:"text-xl font-bold text-cyan-400",children:"\ud83d\ude80 CAINuro"})}),(0,it.jsx)("button",{type:"button",className:"text-gray-300 hover:text-white",onClick:()=>l(!1),children:(0,it.jsx)(Dt,{className:"h-6 w-6"})})]}),(0,it.jsx)("nav",{className:"flex-1 space-y-1 px-2 py-4",children:Qt.map((e=>{const t=u.pathname===e.href;return(0,it.jsxs)(Ge,{to:e.href,className:"group flex items-center px-2 py-2 text-sm font-medium rounded-md ".concat(t?"bg-cyan-600 text-white":"text-gray-300 hover:bg-gray-700 hover:text-white"),onClick:()=>l(!1),children:[(0,it.jsx)(e.icon,{className:"mr-3 h-6 w-6 flex-shrink-0"}),e.name]},e.name)}))})]})]}),(0,it.jsx)("div",{className:"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col",children:(0,it.jsxs)("div",{className:"flex flex-col flex-grow bg-gray-800 pt-5 pb-4 overflow-y-auto",children:[(0,it.jsx)("div",{className:"flex items-center flex-shrink-0 px-4",children:(0,it.jsx)("span",{className:"text-xl font-bold text-cyan-400",children:"\ud83d\ude80 CAINuro Orchestrator"})}),(0,it.jsx)("div",{className:"mt-5 flex-1 flex flex-col",children:(0,it.jsx)("nav",{className:"flex-1 px-2 space-y-1",children:Qt.map((e=>{const t=u.pathname===e.href;return(0,it.jsxs)(Ge,{to:e.href,className:"group flex items-center px-2 py-2 text-sm font-medium rounded-md ".concat(t?"bg-cyan-600 text-white":"text-gray-300 hover:bg-gray-700 hover:text-white"),children:[(0,it.jsx)(e.icon,{className:"mr-3 h-6 w-6 flex-shrink-0"}),e.name]},e.name)}))})}),(0,it.jsx)("div",{className:"flex-shrink-0 flex border-t border-gray-700 p-4",children:(0,it.jsxs)("div",{className:"flex items-center w-full",children:[(0,it.jsx)(Ut,{className:"h-8 w-8 text-gray-400"}),(0,it.jsxs)("div",{className:"ml-3 flex-1",children:[(0,it.jsx)("p",{className:"text-sm font-medium text-gray-300",children:(null===c||void 0===c?void 0:c.name)||(null===c||void 0===c?void 0:c.email)||"User"}),(0,it.jsx)("p",{className:"text-xs text-gray-400",children:(null===c||void 0===c||null===(t=c.roles)||void 0===t?void 0:t.join(", "))||"Loading..."})]}),(0,it.jsx)("button",{onClick:d,className:"ml-2 p-1 text-gray-400 hover:text-white",title:"Logout",children:(0,it.jsx)(Vt,{className:"h-5 w-5"})})]})})]})}),(0,it.jsxs)("div",{className:"lg:pl-64 flex flex-col flex-1",children:[(0,it.jsxs)("div",{className:"sticky top-0 z-10 flex-shrink-0 flex h-16 bg-gray-800 shadow",children:[(0,it.jsx)("button",{type:"button",className:"px-4 border-r border-gray-700 text-gray-300 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-cyan-500 lg:hidden",onClick:()=>l(!0),children:(0,it.jsx)(Ht,{className:"h-6 w-6"})}),(0,it.jsxs)("div",{className:"flex-1 px-4 flex justify-between",children:[(0,it.jsx)("div",{className:"flex-1 flex",children:(0,it.jsx)("div",{className:"w-full flex md:ml-0",children:(0,it.jsxs)("div",{className:"relative w-full text-gray-400 focus-within:text-gray-600",children:[(0,it.jsx)("div",{className:"absolute inset-y-0 left-0 flex items-center pointer-events-none",children:(0,it.jsx)(xt,{className:"h-5 w-5"})}),(0,it.jsx)("input",{className:"block w-full h-full pl-8 pr-3 py-2 border-transparent text-gray-300 placeholder-gray-400 bg-gray-700 focus:outline-none focus:bg-gray-600 focus:border-transparent focus:ring-0 focus:text-gray-100 sm:text-sm",placeholder:"Search resources...",type:"search"})]})})}),(0,it.jsx)("div",{className:"ml-4 flex items-center md:ml-6",children:(0,it.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,it.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"All Systems Operational"}),(0,it.jsx)("div",{className:"w-3 h-3 bg-green-400 rounded-full animate-pulse"}),(0,it.jsxs)("div",{className:"relative",children:[(0,it.jsxs)("button",{onClick:()=>s(!i),className:"flex items-center space-x-2 text-gray-300 hover:text-white p-2 rounded-md",children:[(0,it.jsx)(Ut,{className:"h-6 w-6"}),(0,it.jsx)("span",{className:"text-sm font-medium",children:(null===c||void 0===c?void 0:c.name)||(null===c||void 0===c?void 0:c.email)||"User"})]}),i&&(0,it.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50",children:[(0,it.jsxs)("div",{className:"px-4 py-2 border-b border-gray-200",children:[(0,it.jsx)("p",{className:"text-sm font-medium text-gray-900",children:(null===c||void 0===c?void 0:c.name)||"User"}),(0,it.jsx)("p",{className:"text-sm text-gray-500",children:null===c||void 0===c?void 0:c.email}),(0,it.jsx)("p",{className:"text-xs text-gray-400",children:null===c||void 0===c||null===(n=c.roles)||void 0===n?void 0:n.join(", ")})]}),(0,it.jsxs)("button",{onClick:()=>{s(!1),d()},className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[(0,it.jsx)(Vt,{className:"inline h-4 w-4 mr-2"}),"Sign out"]})]})]})]})})]})]}),(0,it.jsx)("main",{className:"flex-1",children:(0,it.jsx)("div",{className:"py-6",children:(0,it.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 md:px-8",children:r})})})]})]})}const Jt={audit:Rt,autoscaler:Ot,db_admin:At,discovery:xt,envoy_control_plane:St,workflow:jt};function Xt(){const[e,t]=(0,a.useState)(null),[n,r]=(0,a.useState)(!0);return(0,a.useEffect)((()=>{const e=async()=>{try{const e=await fetch("/health"),n=await e.json();t(n)}catch(e){console.error("Failed to fetch health status:",e)}finally{r(!1)}};e();const n=setInterval(e,3e4);return()=>clearInterval(n)}),[]),n?(0,it.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,it.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-cyan-500"})}):(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h1",{className:"text-3xl font-bold text-white",children:"CAINuro Orchestrator Dashboard"}),(0,it.jsx)("p",{className:"mt-2 text-gray-400",children:"Real-time system status and service monitoring"})]}),(0,it.jsx)("div",{className:"bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,it.jsx)("div",{className:"px-4 py-5 sm:p-6",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center ".concat("healthy"===(null===e||void 0===e?void 0:e.status)?"bg-green-100":"bg-red-100"),children:(0,it.jsx)("div",{className:"w-4 h-4 rounded-full ".concat("healthy"===(null===e||void 0===e?void 0:e.status)?"bg-green-500":"bg-red-500")})})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400 truncate",children:"System Status"}),(0,it.jsxs)("dd",{className:"flex items-baseline",children:[(0,it.jsx)("div",{className:"text-2xl font-semibold text-white",children:"healthy"===(null===e||void 0===e?void 0:e.status)?"All Systems Operational":"System Issues Detected"}),(0,it.jsxs)("div",{className:"ml-2 flex items-baseline text-sm font-semibold text-green-400",children:[null===e||void 0===e?void 0:e.mode," Mode"]})]})]})})]})})}),(0,it.jsx)("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3",children:(null===e||void 0===e?void 0:e.services)&&Object.entries(e.services).map((e=>{let[t,n]=e;const r=Jt[t]||jt,a="active"===n;return(0,it.jsx)("div",{className:"bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,it.jsx)("div",{className:"p-5",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(r,{className:"h-6 w-6 ".concat(a?"text-green-400":"text-red-400")})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400 truncate",children:t.replace(/_/g," ").replace(/\b\w/g,(e=>e.toUpperCase()))}),(0,it.jsx)("dd",{className:"text-lg font-medium text-white",children:(0,it.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(a?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:n})})]})})]})})},t)}))}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white",children:"Quick Actions"}),(0,it.jsxs)("div",{className:"mt-5 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4",children:[(0,it.jsxs)("button",{className:"relative group bg-gray-700 p-3 focus:outline-none focus:ring-2 focus:ring-cyan-500 rounded-lg hover:bg-gray-600 transition-colors",children:[(0,it.jsx)("div",{children:(0,it.jsx)("span",{className:"rounded-lg inline-flex p-3 bg-cyan-600 text-white",children:(0,it.jsx)(xt,{className:"h-6 w-6"})})}),(0,it.jsxs)("div",{className:"mt-8",children:[(0,it.jsxs)("h3",{className:"text-lg font-medium text-white",children:[(0,it.jsx)("span",{className:"absolute inset-0"}),"Discover Resources"]}),(0,it.jsx)("p",{className:"mt-2 text-sm text-gray-400",children:"Search across AWS, GCP, and Azure"})]})]}),(0,it.jsxs)("button",{className:"relative group bg-gray-700 p-3 focus:outline-none focus:ring-2 focus:ring-cyan-500 rounded-lg hover:bg-gray-600 transition-colors",children:[(0,it.jsx)("div",{children:(0,it.jsx)("span",{className:"rounded-lg inline-flex p-3 bg-purple-600 text-white",children:(0,it.jsx)(jt,{className:"h-6 w-6"})})}),(0,it.jsxs)("div",{className:"mt-8",children:[(0,it.jsxs)("h3",{className:"text-lg font-medium text-white",children:[(0,it.jsx)("span",{className:"absolute inset-0"}),"Run Workflow"]}),(0,it.jsx)("p",{className:"mt-2 text-sm text-gray-400",children:"Execute automation workflows"})]})]}),(0,it.jsxs)("button",{className:"relative group bg-gray-700 p-3 focus:outline-none focus:ring-2 focus:ring-cyan-500 rounded-lg hover:bg-gray-600 transition-colors",children:[(0,it.jsx)("div",{children:(0,it.jsx)("span",{className:"rounded-lg inline-flex p-3 bg-green-600 text-white",children:(0,it.jsx)(St,{className:"h-6 w-6"})})}),(0,it.jsxs)("div",{className:"mt-8",children:[(0,it.jsxs)("h3",{className:"text-lg font-medium text-white",children:[(0,it.jsx)("span",{className:"absolute inset-0"}),"Envoy Config"]}),(0,it.jsx)("p",{className:"mt-2 text-sm text-gray-400",children:"Manage Envoy configurations"})]})]}),(0,it.jsxs)("button",{className:"relative group bg-gray-700 p-3 focus:outline-none focus:ring-2 focus:ring-cyan-500 rounded-lg hover:bg-gray-600 transition-colors",children:[(0,it.jsx)("div",{children:(0,it.jsx)("span",{className:"rounded-lg inline-flex p-3 bg-orange-600 text-white",children:(0,it.jsx)(Rt,{className:"h-6 w-6"})})}),(0,it.jsxs)("div",{className:"mt-8",children:[(0,it.jsxs)("h3",{className:"text-lg font-medium text-white",children:[(0,it.jsx)("span",{className:"absolute inset-0"}),"Audit Logs"]}),(0,it.jsx)("p",{className:"mt-2 text-sm text-gray-400",children:"View tamper-proof audit trail"})]})]})]})]})}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white",children:"System Information"}),(0,it.jsxs)("dl",{className:"mt-5 grid grid-cols-1 gap-5 sm:grid-cols-3",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400",children:"Version"}),(0,it.jsx)("dd",{className:"mt-1 text-sm text-white",children:null===e||void 0===e?void 0:e.version})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400",children:"Mode"}),(0,it.jsx)("dd",{className:"mt-1 text-sm text-white",children:null===e||void 0===e?void 0:e.mode})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400",children:"Services"}),(0,it.jsxs)("dd",{className:"mt-1 text-sm text-white",children:[null!==e&&void 0!==e&&e.services?Object.keys(e.services).length:0," active"]})]})]})]})})]})}function Zt(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];throw Error("[Immer] minified error nr: "+e+(n.length?" "+n.map((function(e){return"'"+e+"'"})).join(","):"")+". Find the full error at: https://bit.ly/3cXEKWf")}function Yt(e){return!!e&&!!e[Wn]}function Gt(e){var t;return!!e&&(function(e){if(!e||"object"!=typeof e)return!1;var t=Object.getPrototypeOf(e);if(null===t)return!0;var n=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return n===Object||"function"==typeof n&&Function.toString.call(n)===Vn}(e)||Array.isArray(e)||!!e[Bn]||!!(null===(t=e.constructor)||void 0===t?void 0:t[Bn])||ln(e)||sn(e))}function en(e,t,n){void 0===n&&(n=!1),0===tn(e)?(n?Object.keys:$n)(e).forEach((function(r){n&&"symbol"==typeof r||t(r,e[r],e)})):e.forEach((function(n,r){return t(r,n,e)}))}function tn(e){var t=e[Wn];return t?t.i>3?t.i-4:t.i:Array.isArray(e)?1:ln(e)?2:sn(e)?3:0}function nn(e,t){return 2===tn(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function rn(e,t){return 2===tn(e)?e.get(t):e[t]}function an(e,t,n){var r=tn(e);2===r?e.set(t,n):3===r?e.add(n):e[t]=n}function on(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t}function ln(e){return Dn&&e instanceof Map}function sn(e){return zn&&e instanceof Set}function un(e){return e.o||e.t}function cn(e){if(Array.isArray(e))return Array.prototype.slice.call(e);var t=qn(e);delete t[Wn];for(var n=$n(t),r=0;r<n.length;r++){var a=n[r],o=t[a];!1===o.writable&&(o.writable=!0,o.configurable=!0),(o.get||o.set)&&(t[a]={configurable:!0,writable:!0,enumerable:o.enumerable,value:e[a]})}return Object.create(Object.getPrototypeOf(e),t)}function dn(e,t){return void 0===t&&(t=!1),pn(e)||Yt(e)||!Gt(e)||(tn(e)>1&&(e.set=e.add=e.clear=e.delete=fn),Object.freeze(e),t&&en(e,(function(e,t){return dn(t,!0)}),!0)),e}function fn(){Zt(2)}function pn(e){return null==e||"object"!=typeof e||Object.isFrozen(e)}function hn(e){var t=Hn[e];return t||Zt(18,e),t}function mn(e,t){Hn[e]||(Hn[e]=t)}function gn(){return Mn}function vn(e,t){t&&(hn("Patches"),e.u=[],e.s=[],e.v=t)}function yn(e){xn(e),e.p.forEach(wn),e.p=null}function xn(e){e===Mn&&(Mn=e.l)}function bn(e){return Mn={p:[],l:Mn,h:e,m:!0,_:0}}function wn(e){var t=e[Wn];0===t.i||1===t.i?t.j():t.g=!0}function jn(e,t){t._=t.p.length;var n=t.p[0],r=void 0!==e&&e!==n;return t.h.O||hn("ES5").S(t,e,r),r?(n[Wn].P&&(yn(t),Zt(4)),Gt(e)&&(e=Nn(t,e),t.l||Sn(t,e)),t.u&&hn("Patches").M(n[Wn].t,e,t.u,t.s)):e=Nn(t,n,[]),yn(t),t.u&&t.v(t.u,t.s),e!==Un?e:void 0}function Nn(e,t,n){if(pn(t))return t;var r=t[Wn];if(!r)return en(t,(function(a,o){return kn(e,r,t,a,o,n)}),!0),t;if(r.A!==e)return t;if(!r.P)return Sn(e,r.t,!0),r.t;if(!r.I){r.I=!0,r.A._--;var a=4===r.i||5===r.i?r.o=cn(r.k):r.o,o=a,l=!1;3===r.i&&(o=new Set(a),a.clear(),l=!0),en(o,(function(t,o){return kn(e,r,a,t,o,n,l)})),Sn(e,a,!1),n&&e.u&&hn("Patches").N(r,n,e.u,e.s)}return r.o}function kn(e,t,n,r,a,o,l){if(Yt(a)){var i=Nn(e,a,o&&t&&3!==t.i&&!nn(t.R,r)?o.concat(r):void 0);if(an(n,r,i),!Yt(i))return;e.m=!1}else l&&n.add(a);if(Gt(a)&&!pn(a)){if(!e.h.D&&e._<1)return;Nn(e,a),t&&t.A.l||Sn(e,a)}}function Sn(e,t,n){void 0===n&&(n=!1),!e.l&&e.h.D&&e.m&&dn(t,n)}function En(e,t){var n=e[Wn];return(n?un(n):e)[t]}function Cn(e,t){if(t in e)for(var n=Object.getPrototypeOf(e);n;){var r=Object.getOwnPropertyDescriptor(n,t);if(r)return r;n=Object.getPrototypeOf(n)}}function On(e){e.P||(e.P=!0,e.l&&On(e.l))}function Pn(e){e.o||(e.o=cn(e.t))}function _n(e,t,n){var r=ln(t)?hn("MapSet").F(t,n):sn(t)?hn("MapSet").T(t,n):e.O?function(e,t){var n=Array.isArray(e),r={i:n?1:0,A:t?t.A:gn(),P:!1,I:!1,R:{},l:t,t:e,k:null,o:null,j:null,C:!1},a=r,o=Qn;n&&(a=[r],o=Kn);var l=Proxy.revocable(a,o),i=l.revoke,s=l.proxy;return r.k=s,r.j=i,s}(t,n):hn("ES5").J(t,n);return(n?n.A:gn()).p.push(r),r}function Rn(e){return Yt(e)||Zt(22,e),function e(t){if(!Gt(t))return t;var n,r=t[Wn],a=tn(t);if(r){if(!r.P&&(r.i<4||!hn("ES5").K(r)))return r.t;r.I=!0,n=Ln(t,a),r.I=!1}else n=Ln(t,a);return en(n,(function(t,a){r&&rn(r.t,t)===a||an(n,t,e(a))})),3===a?new Set(n):n}(e)}function Ln(e,t){switch(t){case 2:return new Map(e);case 3:return Array.from(e)}return cn(e)}function Tn(){function e(e,t){var n=a[e];return n?n.enumerable=t:a[e]=n={configurable:!0,enumerable:t,get:function(){var t=this[Wn];return Qn.get(t,e)},set:function(t){var n=this[Wn];Qn.set(n,e,t)}},n}function t(e){for(var t=e.length-1;t>=0;t--){var a=e[t][Wn];if(!a.P)switch(a.i){case 5:r(a)&&On(a);break;case 4:n(a)&&On(a)}}}function n(e){for(var t=e.t,n=e.k,r=$n(n),a=r.length-1;a>=0;a--){var o=r[a];if(o!==Wn){var l=t[o];if(void 0===l&&!nn(t,o))return!0;var i=n[o],s=i&&i[Wn];if(s?s.t!==l:!on(i,l))return!0}}var u=!!t[Wn];return r.length!==$n(t).length+(u?0:1)}function r(e){var t=e.k;if(t.length!==e.t.length)return!0;var n=Object.getOwnPropertyDescriptor(t,t.length-1);if(n&&!n.get)return!0;for(var r=0;r<t.length;r++)if(!t.hasOwnProperty(r))return!0;return!1}var a={};mn("ES5",{J:function(t,n){var r=Array.isArray(t),a=function(t,n){if(t){for(var r=Array(n.length),a=0;a<n.length;a++)Object.defineProperty(r,""+a,e(a,!0));return r}var o=qn(n);delete o[Wn];for(var l=$n(o),i=0;i<l.length;i++){var s=l[i];o[s]=e(s,t||!!o[s].enumerable)}return Object.create(Object.getPrototypeOf(n),o)}(r,t),o={i:r?5:4,A:n?n.A:gn(),P:!1,I:!1,R:{},l:n,t:t,k:a,o:null,g:!1,C:!1};return Object.defineProperty(a,Wn,{value:o,writable:!0}),a},S:function(e,n,a){a?Yt(n)&&n[Wn].A===e&&t(e.p):(e.u&&function e(t){if(t&&"object"==typeof t){var n=t[Wn];if(n){var a=n.t,o=n.k,l=n.R,i=n.i;if(4===i)en(o,(function(t){t!==Wn&&(void 0!==a[t]||nn(a,t)?l[t]||e(o[t]):(l[t]=!0,On(n)))})),en(a,(function(e){void 0!==o[e]||nn(o,e)||(l[e]=!1,On(n))}));else if(5===i){if(r(n)&&(On(n),l.length=!0),o.length<a.length)for(var s=o.length;s<a.length;s++)l[s]=!1;else for(var u=a.length;u<o.length;u++)l[u]=!0;for(var c=Math.min(o.length,a.length),d=0;d<c;d++)o.hasOwnProperty(d)||(l[d]=!0),void 0===l[d]&&e(o[d])}}}}(e.p[0]),t(e.p))},K:function(e){return 4===e.i?n(e):r(e)}})}var An,Mn,In="undefined"!=typeof Symbol&&"symbol"==typeof Symbol("x"),Dn="undefined"!=typeof Map,zn="undefined"!=typeof Set,Fn="undefined"!=typeof Proxy&&void 0!==Proxy.revocable&&"undefined"!=typeof Reflect,Un=In?Symbol.for("immer-nothing"):((An={})["immer-nothing"]=!0,An),Bn=In?Symbol.for("immer-draftable"):"__$immer_draftable",Wn=In?Symbol.for("immer-state"):"__$immer_state",Vn=("undefined"!=typeof Symbol&&Symbol.iterator,""+Object.prototype.constructor),$n="undefined"!=typeof Reflect&&Reflect.ownKeys?Reflect.ownKeys:void 0!==Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:Object.getOwnPropertyNames,qn=Object.getOwnPropertyDescriptors||function(e){var t={};return $n(e).forEach((function(n){t[n]=Object.getOwnPropertyDescriptor(e,n)})),t},Hn={},Qn={get:function(e,t){if(t===Wn)return e;var n=un(e);if(!nn(n,t))return function(e,t,n){var r,a=Cn(t,n);return a?"value"in a?a.value:null===(r=a.get)||void 0===r?void 0:r.call(e.k):void 0}(e,n,t);var r=n[t];return e.I||!Gt(r)?r:r===En(e.t,t)?(Pn(e),e.o[t]=_n(e.A.h,r,e)):r},has:function(e,t){return t in un(e)},ownKeys:function(e){return Reflect.ownKeys(un(e))},set:function(e,t,n){var r=Cn(un(e),t);if(null==r?void 0:r.set)return r.set.call(e.k,n),!0;if(!e.P){var a=En(un(e),t),o=null==a?void 0:a[Wn];if(o&&o.t===n)return e.o[t]=n,e.R[t]=!1,!0;if(on(n,a)&&(void 0!==n||nn(e.t,t)))return!0;Pn(e),On(e)}return e.o[t]===n&&(void 0!==n||t in e.o)||Number.isNaN(n)&&Number.isNaN(e.o[t])||(e.o[t]=n,e.R[t]=!0),!0},deleteProperty:function(e,t){return void 0!==En(e.t,t)||t in e.t?(e.R[t]=!1,Pn(e),On(e)):delete e.R[t],e.o&&delete e.o[t],!0},getOwnPropertyDescriptor:function(e,t){var n=un(e),r=Reflect.getOwnPropertyDescriptor(n,t);return r?{writable:!0,configurable:1!==e.i||"length"!==t,enumerable:r.enumerable,value:n[t]}:r},defineProperty:function(){Zt(11)},getPrototypeOf:function(e){return Object.getPrototypeOf(e.t)},setPrototypeOf:function(){Zt(12)}},Kn={};en(Qn,(function(e,t){Kn[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}})),Kn.deleteProperty=function(e,t){return Kn.set.call(this,e,t,void 0)},Kn.set=function(e,t,n){return Qn.set.call(this,e[0],t,n,e[0])};var Jn=function(){function e(e){var t=this;this.O=Fn,this.D=!0,this.produce=function(e,n,r){if("function"==typeof e&&"function"!=typeof n){var a=n;n=e;var o=t;return function(e){var t=this;void 0===e&&(e=a);for(var r=arguments.length,l=Array(r>1?r-1:0),i=1;i<r;i++)l[i-1]=arguments[i];return o.produce(e,(function(e){var r;return(r=n).call.apply(r,[t,e].concat(l))}))}}var l;if("function"!=typeof n&&Zt(6),void 0!==r&&"function"!=typeof r&&Zt(7),Gt(e)){var i=bn(t),s=_n(t,e,void 0),u=!0;try{l=n(s),u=!1}finally{u?yn(i):xn(i)}return"undefined"!=typeof Promise&&l instanceof Promise?l.then((function(e){return vn(i,r),jn(e,i)}),(function(e){throw yn(i),e})):(vn(i,r),jn(l,i))}if(!e||"object"!=typeof e){if(void 0===(l=n(e))&&(l=e),l===Un&&(l=void 0),t.D&&dn(l,!0),r){var c=[],d=[];hn("Patches").M(e,l,c,d),r(c,d)}return l}Zt(21,e)},this.produceWithPatches=function(e,n){if("function"==typeof e)return function(n){for(var r=arguments.length,a=Array(r>1?r-1:0),o=1;o<r;o++)a[o-1]=arguments[o];return t.produceWithPatches(n,(function(t){return e.apply(void 0,[t].concat(a))}))};var r,a,o=t.produce(e,n,(function(e,t){r=e,a=t}));return"undefined"!=typeof Promise&&o instanceof Promise?o.then((function(e){return[e,r,a]})):[o,r,a]},"boolean"==typeof(null==e?void 0:e.useProxies)&&this.setUseProxies(e.useProxies),"boolean"==typeof(null==e?void 0:e.autoFreeze)&&this.setAutoFreeze(e.autoFreeze)}var t=e.prototype;return t.createDraft=function(e){Gt(e)||Zt(8),Yt(e)&&(e=Rn(e));var t=bn(this),n=_n(this,e,void 0);return n[Wn].C=!0,xn(t),n},t.finishDraft=function(e,t){var n=(e&&e[Wn]).A;return vn(n,t),jn(void 0,n)},t.setAutoFreeze=function(e){this.D=e},t.setUseProxies=function(e){e&&!Fn&&Zt(20),this.O=e},t.applyPatches=function(e,t){var n;for(n=t.length-1;n>=0;n--){var r=t[n];if(0===r.path.length&&"replace"===r.op){e=r.value;break}}n>-1&&(t=t.slice(n+1));var a=hn("Patches").$;return Yt(e)?a(e,t):this.produce(e,(function(e){return a(e,t)}))},e}(),Xn=new Jn,Zn=Xn.produce;Xn.produceWithPatches.bind(Xn),Xn.setAutoFreeze.bind(Xn),Xn.setUseProxies.bind(Xn),Xn.applyPatches.bind(Xn),Xn.createDraft.bind(Xn),Xn.finishDraft.bind(Xn);const Yn=Zn;function Gn(e){return"Minified Redux error #"+e+"; visit https://redux.js.org/Errors?code="+e+" for the full message or use the non-minified dev environment for full errors. "}var er="function"===typeof Symbol&&Symbol.observable||"@@observable",tr=function(){return Math.random().toString(36).substring(7).split("").join(".")},nr={INIT:"@@redux/INIT"+tr(),REPLACE:"@@redux/REPLACE"+tr(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+tr()}};function rr(e){if("object"!==typeof e||null===e)return!1;for(var t=e;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}function ar(e,t,n){var r;if("function"===typeof t&&"function"===typeof n||"function"===typeof n&&"function"===typeof arguments[3])throw new Error(Gn(0));if("function"===typeof t&&"undefined"===typeof n&&(n=t,t=void 0),"undefined"!==typeof n){if("function"!==typeof n)throw new Error(Gn(1));return n(ar)(e,t)}if("function"!==typeof e)throw new Error(Gn(2));var a=e,o=t,l=[],i=l,s=!1;function u(){i===l&&(i=l.slice())}function c(){if(s)throw new Error(Gn(3));return o}function d(e){if("function"!==typeof e)throw new Error(Gn(4));if(s)throw new Error(Gn(5));var t=!0;return u(),i.push(e),function(){if(t){if(s)throw new Error(Gn(6));t=!1,u();var n=i.indexOf(e);i.splice(n,1),l=null}}}function f(e){if(!rr(e))throw new Error(Gn(7));if("undefined"===typeof e.type)throw new Error(Gn(8));if(s)throw new Error(Gn(9));try{s=!0,o=a(o,e)}finally{s=!1}for(var t=l=i,n=0;n<t.length;n++){(0,t[n])()}return e}return f({type:nr.INIT}),(r={dispatch:f,subscribe:d,getState:c,replaceReducer:function(e){if("function"!==typeof e)throw new Error(Gn(10));a=e,f({type:nr.REPLACE})}})[er]=function(){var e,t=d;return(e={subscribe:function(e){if("object"!==typeof e||null===e)throw new Error(Gn(11));function n(){e.next&&e.next(c())}return n(),{unsubscribe:t(n)}}})[er]=function(){return this},e},r}function or(e){for(var t=Object.keys(e),n={},r=0;r<t.length;r++){var a=t[r];0,"function"===typeof e[a]&&(n[a]=e[a])}var o,l=Object.keys(n);try{!function(e){Object.keys(e).forEach((function(t){var n=e[t];if("undefined"===typeof n(void 0,{type:nr.INIT}))throw new Error(Gn(12));if("undefined"===typeof n(void 0,{type:nr.PROBE_UNKNOWN_ACTION()}))throw new Error(Gn(13))}))}(n)}catch(tu){o=tu}return function(e,t){if(void 0===e&&(e={}),o)throw o;for(var r=!1,a={},i=0;i<l.length;i++){var s=l[i],u=n[s],c=e[s],d=u(c,t);if("undefined"===typeof d){t&&t.type;throw new Error(Gn(14))}a[s]=d,r=r||d!==c}return(r=r||l.length!==Object.keys(e).length)?a:e}}function lr(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return 0===t.length?function(e){return e}:1===t.length?t[0]:t.reduce((function(e,t){return function(){return e(t.apply(void 0,arguments))}}))}function ir(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){return function(){var n=e.apply(void 0,arguments),r=function(){throw new Error(Gn(15))},a={getState:n.getState,dispatch:function(){return r.apply(void 0,arguments)}},o=t.map((function(e){return e(a)}));return r=lr.apply(void 0,o)(n.dispatch),lt(lt({},n),{},{dispatch:r})}}}function sr(e){return function(t){var n=t.dispatch,r=t.getState;return function(t){return function(a){return"function"===typeof a?a(n,r,e):t(a)}}}}var ur=sr();ur.withExtraArgument=sr;const cr=ur;var dr=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!==typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),fr=function(e,t){var n,r,a,o,l={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return o={next:i(0),throw:i(1),return:i(2)},"function"===typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function i(o){return function(i){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;l;)try{if(n=1,r&&(a=2&o[0]?r.return:o[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,o[1])).done)return a;switch(r=0,a&&(o=[2&o[0],a.value]),o[0]){case 0:case 1:a=o;break;case 4:return l.label++,{value:o[1],done:!1};case 5:l.label++,r=o[1],o=[0];continue;case 7:o=l.ops.pop(),l.trys.pop();continue;default:if(!(a=(a=l.trys).length>0&&a[a.length-1])&&(6===o[0]||2===o[0])){l=0;continue}if(3===o[0]&&(!a||o[1]>a[0]&&o[1]<a[3])){l.label=o[1];break}if(6===o[0]&&l.label<a[1]){l.label=a[1],a=o;break}if(a&&l.label<a[2]){l.label=a[2],l.ops.push(o);break}a[2]&&l.ops.pop(),l.trys.pop();continue}o=t.call(e,l)}catch(tu){o=[6,tu],r=0}finally{n=a=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,i])}}},pr=function(e,t){for(var n=0,r=t.length,a=e.length;n<r;n++,a++)e[a]=t[n];return e},hr=Object.defineProperty,mr=Object.defineProperties,gr=Object.getOwnPropertyDescriptors,vr=Object.getOwnPropertySymbols,yr=Object.prototype.hasOwnProperty,xr=Object.prototype.propertyIsEnumerable,br=function(e,t,n){return t in e?hr(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n},wr=function(e,t){for(var n in t||(t={}))yr.call(t,n)&&br(e,n,t[n]);if(vr)for(var r=0,a=vr(t);r<a.length;r++){n=a[r];xr.call(t,n)&&br(e,n,t[n])}return e},jr=function(e,t){return mr(e,gr(t))},Nr=function(e,t,n){return new Promise((function(r,a){var o=function(e){try{i(n.next(e))}catch(tu){a(tu)}},l=function(e){try{i(n.throw(e))}catch(tu){a(tu)}},i=function(e){return e.done?r(e.value):Promise.resolve(e.value).then(o,l)};i((n=n.apply(e,t)).next())}))},kr="undefined"!==typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!==arguments.length)return"object"===typeof arguments[0]?lr:lr.apply(null,arguments)};"undefined"!==typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;function Sr(e){if("object"!==typeof e||null===e)return!1;var t=Object.getPrototypeOf(e);if(null===t)return!0;for(var n=t;null!==Object.getPrototypeOf(n);)n=Object.getPrototypeOf(n);return t===n}function Er(e,t){function n(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];if(t){var a=t.apply(void 0,n);if(!a)throw new Error("prepareAction did not return an object");return wr(wr({type:e,payload:a.payload},"meta"in a&&{meta:a.meta}),"error"in a&&{error:a.error})}return{type:e,payload:n[0]}}return n.toString=function(){return""+e},n.type=e,n.match=function(t){return t.type===e},n}var Cr=function(e){function t(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var a=e.apply(this,n)||this;return Object.setPrototypeOf(a,t.prototype),a}return dr(t,e),Object.defineProperty(t,Symbol.species,{get:function(){return t},enumerable:!1,configurable:!0}),t.prototype.concat=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e.prototype.concat.apply(this,t)},t.prototype.prepend=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return 1===e.length&&Array.isArray(e[0])?new(t.bind.apply(t,pr([void 0],e[0].concat(this)))):new(t.bind.apply(t,pr([void 0],e.concat(this))))},t}(Array),Or=function(e){function t(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var a=e.apply(this,n)||this;return Object.setPrototypeOf(a,t.prototype),a}return dr(t,e),Object.defineProperty(t,Symbol.species,{get:function(){return t},enumerable:!1,configurable:!0}),t.prototype.concat=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e.prototype.concat.apply(this,t)},t.prototype.prepend=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return 1===e.length&&Array.isArray(e[0])?new(t.bind.apply(t,pr([void 0],e[0].concat(this)))):new(t.bind.apply(t,pr([void 0],e.concat(this))))},t}(Array);function Pr(e){return Gt(e)?Yn(e,(function(){})):e}function _r(){return function(e){return function(e){void 0===e&&(e={});var t=e.thunk,n=void 0===t||t,r=(e.immutableCheck,e.serializableCheck,e.actionCreatorCheck,new Cr);n&&(!function(e){return"boolean"===typeof e}(n)?r.push(cr.withExtraArgument(n.extraArgument)):r.push(cr));0;return r}(e)}}function Rr(e){var t,n={},r=[],a={addCase:function(e,t){var r="string"===typeof e?e:e.type;if(!r)throw new Error("`builder.addCase` cannot be called with an empty action type");if(r in n)throw new Error("`builder.addCase` cannot be called with two reducers for the same action type");return n[r]=t,a},addMatcher:function(e,t){return r.push({matcher:e,reducer:t}),a},addDefaultCase:function(e){return t=e,a}};return e(a),[n,r,t]}function Lr(e){var t=e.name;if(!t)throw new Error("`name` is a required option for createSlice");var n,r="function"==typeof e.initialState?e.initialState:Pr(e.initialState),a=e.reducers||{},o=Object.keys(a),l={},i={},s={};function u(){var t="function"===typeof e.extraReducers?Rr(e.extraReducers):[e.extraReducers],n=t[0],a=void 0===n?{}:n,o=t[1],l=void 0===o?[]:o,s=t[2],u=void 0===s?void 0:s,c=wr(wr({},a),i);return function(e,t,n,r){void 0===n&&(n=[]);var a,o="function"===typeof t?Rr(t):[t,n,r],l=o[0],i=o[1],s=o[2];if(function(e){return"function"===typeof e}(e))a=function(){return Pr(e())};else{var u=Pr(e);a=function(){return u}}function c(e,t){void 0===e&&(e=a());var n=pr([l[t.type]],i.filter((function(e){return(0,e.matcher)(t)})).map((function(e){return e.reducer})));return 0===n.filter((function(e){return!!e})).length&&(n=[s]),n.reduce((function(e,n){if(n){var r;if(Yt(e))return void 0===(r=n(e,t))?e:r;if(Gt(e))return Yn(e,(function(e){return n(e,t)}));if(void 0===(r=n(e,t))){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return r}return e}),e)}return c.getInitialState=a,c}(r,(function(e){for(var t in c)e.addCase(t,c[t]);for(var n=0,r=l;n<r.length;n++){var a=r[n];e.addMatcher(a.matcher,a.reducer)}u&&e.addDefaultCase(u)}))}return o.forEach((function(e){var n,r,o=a[e],u=t+"/"+e;"reducer"in o?(n=o.reducer,r=o.prepare):n=o,l[e]=n,i[u]=n,s[e]=r?Er(u,r):Er(u)})),{name:t,reducer:function(e,t){return n||(n=u()),n(e,t)},actions:s,caseReducers:l,getInitialState:function(){return n||(n=u()),n.getInitialState()}}}var Tr=function(e){void 0===e&&(e=21);for(var t="",n=e;n--;)t+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return t},Ar=["name","message","stack","code"],Mr=function(e,t){this.payload=e,this.meta=t},Ir=function(e,t){this.payload=e,this.meta=t},Dr=function(e){if("object"===typeof e&&null!==e){for(var t={},n=0,r=Ar;n<r.length;n++){var a=r[n];"string"===typeof e[a]&&(t[a]=e[a])}return t}return{message:String(e)}},zr=function(){function e(e,t,n){var r=Er(e+"/fulfilled",(function(e,t,n,r){return{payload:e,meta:jr(wr({},r||{}),{arg:n,requestId:t,requestStatus:"fulfilled"})}})),a=Er(e+"/pending",(function(e,t,n){return{payload:void 0,meta:jr(wr({},n||{}),{arg:t,requestId:e,requestStatus:"pending"})}})),o=Er(e+"/rejected",(function(e,t,r,a,o){return{payload:a,error:(n&&n.serializeError||Dr)(e||"Rejected"),meta:jr(wr({},o||{}),{arg:r,requestId:t,rejectedWithValue:!!a,requestStatus:"rejected",aborted:"AbortError"===(null==e?void 0:e.name),condition:"ConditionError"===(null==e?void 0:e.name)})}})),l="undefined"!==typeof AbortController?AbortController:function(){function e(){this.signal={aborted:!1,addEventListener:function(){},dispatchEvent:function(){return!1},onabort:function(){},removeEventListener:function(){},reason:void 0,throwIfAborted:function(){}}}return e.prototype.abort=function(){0},e}();return Object.assign((function(e){return function(i,s,u){var c,d=(null==n?void 0:n.idGenerator)?n.idGenerator(e):Tr(),f=new l;function p(e){c=e,f.abort()}var h=function(){return Nr(this,null,(function(){var l,h,m,g,v,y;return fr(this,(function(x){switch(x.label){case 0:return x.trys.push([0,4,,5]),g=null==(l=null==n?void 0:n.condition)?void 0:l.call(n,e,{getState:s,extra:u}),null===(b=g)||"object"!==typeof b||"function"!==typeof b.then?[3,2]:[4,g];case 1:g=x.sent(),x.label=2;case 2:if(!1===g||f.signal.aborted)throw{name:"ConditionError",message:"Aborted due to condition callback returning false."};return v=new Promise((function(e,t){return f.signal.addEventListener("abort",(function(){return t({name:"AbortError",message:c||"Aborted"})}))})),i(a(d,e,null==(h=null==n?void 0:n.getPendingMeta)?void 0:h.call(n,{requestId:d,arg:e},{getState:s,extra:u}))),[4,Promise.race([v,Promise.resolve(t(e,{dispatch:i,getState:s,extra:u,requestId:d,signal:f.signal,abort:p,rejectWithValue:function(e,t){return new Mr(e,t)},fulfillWithValue:function(e,t){return new Ir(e,t)}})).then((function(t){if(t instanceof Mr)throw t;return t instanceof Ir?r(t.payload,d,e,t.meta):r(t,d,e)}))])];case 3:return m=x.sent(),[3,5];case 4:return y=x.sent(),m=y instanceof Mr?o(null,d,e,y.payload,y.meta):o(y,d,e),[3,5];case 5:return n&&!n.dispatchConditionRejection&&o.match(m)&&m.meta.condition||i(m),[2,m]}var b}))}))}();return Object.assign(h,{abort:p,requestId:d,arg:e,unwrap:function(){return h.then(Fr)}})}}),{pending:a,rejected:o,fulfilled:r,typePrefix:e})}return e.withTypes=function(){return e},e}();function Fr(e){if(e.meta&&e.meta.rejectedWithValue)throw e.payload;if(e.error)throw e.error;return e.payload}Object.assign;var Ur="listenerMiddleware";Er(Ur+"/add"),Er(Ur+"/removeAll"),Er(Ur+"/remove");"function"===typeof queueMicrotask&&queueMicrotask.bind("undefined"!==typeof window?window:"undefined"!==typeof n.g?n.g:globalThis);var Br,Wr=function(e){return function(t){setTimeout(t,e)}};"undefined"!==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:Wr(10);function Vr(e,t){return function(){return e.apply(t,arguments)}}Tn();const{toString:$r}=Object.prototype,{getPrototypeOf:qr}=Object,{iterator:Hr,toStringTag:Qr}=Symbol,Kr=(Jr=Object.create(null),e=>{const t=$r.call(e);return Jr[t]||(Jr[t]=t.slice(8,-1).toLowerCase())});var Jr;const Xr=e=>(e=e.toLowerCase(),t=>Kr(t)===e),Zr=e=>t=>typeof t===e,{isArray:Yr}=Array,Gr=Zr("undefined");const ea=Xr("ArrayBuffer");const ta=Zr("string"),na=Zr("function"),ra=Zr("number"),aa=e=>null!==e&&"object"===typeof e,oa=e=>{if("object"!==Kr(e))return!1;const t=qr(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Qr in e)&&!(Hr in e)},la=Xr("Date"),ia=Xr("File"),sa=Xr("Blob"),ua=Xr("FileList"),ca=Xr("URLSearchParams"),[da,fa,pa,ha]=["ReadableStream","Request","Response","Headers"].map(Xr);function ma(e,t){let n,r,{allOwnKeys:a=!1}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(null!==e&&"undefined"!==typeof e)if("object"!==typeof e&&(e=[e]),Yr(e))for(n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else{const r=a?Object.getOwnPropertyNames(e):Object.keys(e),o=r.length;let l;for(n=0;n<o;n++)l=r[n],t.call(null,e[l],l,e)}}function ga(e,t){t=t.toLowerCase();const n=Object.keys(e);let r,a=n.length;for(;a-- >0;)if(r=n[a],t===r.toLowerCase())return r;return null}const va="undefined"!==typeof globalThis?globalThis:"undefined"!==typeof self?self:"undefined"!==typeof window?window:global,ya=e=>!Gr(e)&&e!==va;const xa=(ba="undefined"!==typeof Uint8Array&&qr(Uint8Array),e=>ba&&e instanceof ba);var ba;const wa=Xr("HTMLFormElement"),ja=(e=>{let{hasOwnProperty:t}=e;return(e,n)=>t.call(e,n)})(Object.prototype),Na=Xr("RegExp"),ka=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};ma(n,((n,a)=>{let o;!1!==(o=t(n,a,e))&&(r[a]=o||n)})),Object.defineProperties(e,r)};const Sa=Xr("AsyncFunction"),Ea=((e,t)=>{return e?setImmediate:t?(n="axios@".concat(Math.random()),r=[],va.addEventListener("message",(e=>{let{source:t,data:a}=e;t===va&&a===n&&r.length&&r.shift()()}),!1),e=>{r.push(e),va.postMessage(n,"*")}):e=>setTimeout(e);var n,r})("function"===typeof setImmediate,na(va.postMessage)),Ca="undefined"!==typeof queueMicrotask?queueMicrotask.bind(va):"undefined"!==typeof process&&process.nextTick||Ea,Oa={isArray:Yr,isArrayBuffer:ea,isBuffer:function(e){return null!==e&&!Gr(e)&&null!==e.constructor&&!Gr(e.constructor)&&na(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"===typeof FormData&&e instanceof FormData||na(e.append)&&("formdata"===(t=Kr(e))||"object"===t&&na(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&ea(e.buffer),t},isString:ta,isNumber:ra,isBoolean:e=>!0===e||!1===e,isObject:aa,isPlainObject:oa,isReadableStream:da,isRequest:fa,isResponse:pa,isHeaders:ha,isUndefined:Gr,isDate:la,isFile:ia,isBlob:sa,isRegExp:Na,isFunction:na,isStream:e=>aa(e)&&na(e.pipe),isURLSearchParams:ca,isTypedArray:xa,isFileList:ua,forEach:ma,merge:function e(){const{caseless:t}=ya(this)&&this||{},n={},r=(r,a)=>{const o=t&&ga(n,a)||a;oa(n[o])&&oa(r)?n[o]=e(n[o],r):oa(r)?n[o]=e({},r):Yr(r)?n[o]=r.slice():n[o]=r};for(let a=0,o=arguments.length;a<o;a++)arguments[a]&&ma(arguments[a],r);return n},extend:function(e,t,n){let{allOwnKeys:r}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return ma(t,((t,r)=>{n&&na(t)?e[r]=Vr(t,n):e[r]=t}),{allOwnKeys:r}),e},trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,r)=>{let a,o,l;const i={};if(t=t||{},null==e)return t;do{for(a=Object.getOwnPropertyNames(e),o=a.length;o-- >0;)l=a[o],r&&!r(l,e,t)||i[l]||(t[l]=e[l],i[l]=!0);e=!1!==n&&qr(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:Kr,kindOfTest:Xr,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return-1!==r&&r===n},toArray:e=>{if(!e)return null;if(Yr(e))return e;let t=e.length;if(!ra(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{const n=(e&&e[Hr]).call(e);let r;for(;(r=n.next())&&!r.done;){const n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let n;const r=[];for(;null!==(n=e.exec(t));)r.push(n);return r},isHTMLForm:wa,hasOwnProperty:ja,hasOwnProp:ja,reduceDescriptors:ka,freezeMethods:e=>{ka(e,((t,n)=>{if(na(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=e[n];na(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))}))},toObjectSet:(e,t)=>{const n={},r=e=>{e.forEach((e=>{n[e]=!0}))};return Yr(e)?r(e):r(String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function(e,t,n){return t.toUpperCase()+n})),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:ga,global:va,isContextDefined:ya,isSpecCompliantForm:function(e){return!!(e&&na(e.append)&&"FormData"===e[Qr]&&e[Hr])},toJSONObject:e=>{const t=new Array(10),n=(e,r)=>{if(aa(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[r]=e;const a=Yr(e)?[]:{};return ma(e,((e,t)=>{const o=n(e,r+1);!Gr(o)&&(a[t]=o)})),t[r]=void 0,a}}return e};return n(e,0)},isAsyncFn:Sa,isThenable:e=>e&&(aa(e)||na(e))&&na(e.then)&&na(e.catch),setImmediate:Ea,asap:Ca,isIterable:e=>null!=e&&na(e[Hr])};function Pa(e,t,n,r,a){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),a&&(this.response=a,this.status=a.status?a.status:null)}Oa.inherits(Pa,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:Oa.toJSONObject(this.config),code:this.code,status:this.status}}});const _a=Pa.prototype,Ra={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((e=>{Ra[e]={value:e}})),Object.defineProperties(Pa,Ra),Object.defineProperty(_a,"isAxiosError",{value:!0}),Pa.from=(e,t,n,r,a,o)=>{const l=Object.create(_a);return Oa.toFlatObject(e,l,(function(e){return e!==Error.prototype}),(e=>"isAxiosError"!==e)),Pa.call(l,e.message,t,n,r,a),l.cause=e,l.name=e.name,o&&Object.assign(l,o),l};const La=Pa;function Ta(e){return Oa.isPlainObject(e)||Oa.isArray(e)}function Aa(e){return Oa.endsWith(e,"[]")?e.slice(0,-2):e}function Ma(e,t,n){return e?e.concat(t).map((function(e,t){return e=Aa(e),!n&&t?"["+e+"]":e})).join(n?".":""):t}const Ia=Oa.toFlatObject(Oa,{},null,(function(e){return/^is[A-Z]/.test(e)}));const Da=function(e,t,n){if(!Oa.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const r=(n=Oa.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(e,t){return!Oa.isUndefined(t[e])}))).metaTokens,a=n.visitor||u,o=n.dots,l=n.indexes,i=(n.Blob||"undefined"!==typeof Blob&&Blob)&&Oa.isSpecCompliantForm(t);if(!Oa.isFunction(a))throw new TypeError("visitor must be a function");function s(e){if(null===e)return"";if(Oa.isDate(e))return e.toISOString();if(!i&&Oa.isBlob(e))throw new La("Blob is not supported. Use a Buffer instead.");return Oa.isArrayBuffer(e)||Oa.isTypedArray(e)?i&&"function"===typeof Blob?new Blob([e]):Buffer.from(e):e}function u(e,n,a){let i=e;if(e&&!a&&"object"===typeof e)if(Oa.endsWith(n,"{}"))n=r?n:n.slice(0,-2),e=JSON.stringify(e);else if(Oa.isArray(e)&&function(e){return Oa.isArray(e)&&!e.some(Ta)}(e)||(Oa.isFileList(e)||Oa.endsWith(n,"[]"))&&(i=Oa.toArray(e)))return n=Aa(n),i.forEach((function(e,r){!Oa.isUndefined(e)&&null!==e&&t.append(!0===l?Ma([n],r,o):null===l?n:n+"[]",s(e))})),!1;return!!Ta(e)||(t.append(Ma(a,n,o),s(e)),!1)}const c=[],d=Object.assign(Ia,{defaultVisitor:u,convertValue:s,isVisitable:Ta});if(!Oa.isObject(e))throw new TypeError("data must be an object");return function e(n,r){if(!Oa.isUndefined(n)){if(-1!==c.indexOf(n))throw Error("Circular reference detected in "+r.join("."));c.push(n),Oa.forEach(n,(function(n,o){!0===(!(Oa.isUndefined(n)||null===n)&&a.call(t,n,Oa.isString(o)?o.trim():o,r,d))&&e(n,r?r.concat(o):[o])})),c.pop()}}(e),t};function za(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,(function(e){return t[e]}))}function Fa(e,t){this._pairs=[],e&&Da(e,this,t)}const Ua=Fa.prototype;Ua.append=function(e,t){this._pairs.push([e,t])},Ua.toString=function(e){const t=e?function(t){return e.call(this,t,za)}:za;return this._pairs.map((function(e){return t(e[0])+"="+t(e[1])}),"").join("&")};const Ba=Fa;function Wa(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Va(e,t,n){if(!t)return e;const r=n&&n.encode||Wa;Oa.isFunction(n)&&(n={serialize:n});const a=n&&n.serialize;let o;if(o=a?a(t,n):Oa.isURLSearchParams(t)?t.toString():new Ba(t,n).toString(r),o){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+o}return e}const $a=class{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){Oa.forEach(this.handlers,(function(t){null!==t&&e(t)}))}},qa={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Ha={isBrowser:!0,classes:{URLSearchParams:"undefined"!==typeof URLSearchParams?URLSearchParams:Ba,FormData:"undefined"!==typeof FormData?FormData:null,Blob:"undefined"!==typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},Qa="undefined"!==typeof window&&"undefined"!==typeof document,Ka="object"===typeof navigator&&navigator||void 0,Ja=Qa&&(!Ka||["ReactNative","NativeScript","NS"].indexOf(Ka.product)<0),Xa="undefined"!==typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"===typeof self.importScripts,Za=Qa&&window.location.href||"http://localhost",Ya=lt(lt({},r),Ha);const Ga=function(e){function t(e,n,r,a){let o=e[a++];if("__proto__"===o)return!0;const l=Number.isFinite(+o),i=a>=e.length;if(o=!o&&Oa.isArray(r)?r.length:o,i)return Oa.hasOwnProp(r,o)?r[o]=[r[o],n]:r[o]=n,!l;r[o]&&Oa.isObject(r[o])||(r[o]=[]);return t(e,n,r[o],a)&&Oa.isArray(r[o])&&(r[o]=function(e){const t={},n=Object.keys(e);let r;const a=n.length;let o;for(r=0;r<a;r++)o=n[r],t[o]=e[o];return t}(r[o])),!l}if(Oa.isFormData(e)&&Oa.isFunction(e.entries)){const n={};return Oa.forEachEntry(e,((e,r)=>{t(function(e){return Oa.matchAll(/\w+|\[(\w*)]/g,e).map((e=>"[]"===e[0]?"":e[1]||e[0]))}(e),r,n,0)})),n}return null};const eo={transitional:qa,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const n=t.getContentType()||"",r=n.indexOf("application/json")>-1,a=Oa.isObject(e);a&&Oa.isHTMLForm(e)&&(e=new FormData(e));if(Oa.isFormData(e))return r?JSON.stringify(Ga(e)):e;if(Oa.isArrayBuffer(e)||Oa.isBuffer(e)||Oa.isStream(e)||Oa.isFile(e)||Oa.isBlob(e)||Oa.isReadableStream(e))return e;if(Oa.isArrayBufferView(e))return e.buffer;if(Oa.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let o;if(a){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return Da(e,new Ya.classes.URLSearchParams,Object.assign({visitor:function(e,t,n,r){return Ya.isNode&&Oa.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((o=Oa.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return Da(o?{"files[]":e}:e,t&&new t,this.formSerializer)}}return a||r?(t.setContentType("application/json",!1),function(e,t,n){if(Oa.isString(e))try{return(t||JSON.parse)(e),Oa.trim(e)}catch(tu){if("SyntaxError"!==tu.name)throw tu}return(n||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||eo.transitional,n=t&&t.forcedJSONParsing,r="json"===this.responseType;if(Oa.isResponse(e)||Oa.isReadableStream(e))return e;if(e&&Oa.isString(e)&&(n&&!this.responseType||r)){const n=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(tu){if(n){if("SyntaxError"===tu.name)throw La.from(tu,La.ERR_BAD_RESPONSE,this,null,this.response);throw tu}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Ya.classes.FormData,Blob:Ya.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};Oa.forEach(["delete","get","head","post","put","patch"],(e=>{eo.headers[e]={}}));const to=eo,no=Oa.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),ro=Symbol("internals");function ao(e){return e&&String(e).trim().toLowerCase()}function oo(e){return!1===e||null==e?e:Oa.isArray(e)?e.map(oo):String(e)}function lo(e,t,n,r,a){return Oa.isFunction(r)?r.call(this,t,n):(a&&(t=n),Oa.isString(t)?Oa.isString(r)?-1!==t.indexOf(r):Oa.isRegExp(r)?r.test(t):void 0:void 0)}class io{constructor(e){e&&this.set(e)}set(e,t,n){const r=this;function a(e,t,n){const a=ao(t);if(!a)throw new Error("header name must be a non-empty string");const o=Oa.findKey(r,a);(!o||void 0===r[o]||!0===n||void 0===n&&!1!==r[o])&&(r[o||t]=oo(e))}const o=(e,t)=>Oa.forEach(e,((e,n)=>a(e,n,t)));if(Oa.isPlainObject(e)||e instanceof this.constructor)o(e,t);else if(Oa.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim()))o((e=>{const t={};let n,r,a;return e&&e.split("\n").forEach((function(e){a=e.indexOf(":"),n=e.substring(0,a).trim().toLowerCase(),r=e.substring(a+1).trim(),!n||t[n]&&no[n]||("set-cookie"===n?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)})),t})(e),t);else if(Oa.isObject(e)&&Oa.isIterable(e)){let n,r,a={};for(const t of e){if(!Oa.isArray(t))throw TypeError("Object iterator must return a key-value pair");a[r=t[0]]=(n=a[r])?Oa.isArray(n)?[...n,t[1]]:[n,t[1]]:t[1]}o(a,t)}else null!=e&&a(t,e,n);return this}get(e,t){if(e=ao(e)){const n=Oa.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}(e);if(Oa.isFunction(t))return t.call(this,e,n);if(Oa.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=ao(e)){const n=Oa.findKey(this,e);return!(!n||void 0===this[n]||t&&!lo(0,this[n],n,t))}return!1}delete(e,t){const n=this;let r=!1;function a(e){if(e=ao(e)){const a=Oa.findKey(n,e);!a||t&&!lo(0,n[a],a,t)||(delete n[a],r=!0)}}return Oa.isArray(e)?e.forEach(a):a(e),r}clear(e){const t=Object.keys(this);let n=t.length,r=!1;for(;n--;){const a=t[n];e&&!lo(0,this[a],a,e,!0)||(delete this[a],r=!0)}return r}normalize(e){const t=this,n={};return Oa.forEach(this,((r,a)=>{const o=Oa.findKey(n,a);if(o)return t[o]=oo(r),void delete t[a];const l=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((e,t,n)=>t.toUpperCase()+n))}(a):String(a).trim();l!==a&&delete t[a],t[l]=oo(r),n[l]=!0})),this}concat(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.constructor.concat(this,...t)}toJSON(e){const t=Object.create(null);return Oa.forEach(this,((n,r)=>{null!=n&&!1!==n&&(t[r]=e&&Oa.isArray(n)?n.join(", "):n)})),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((e=>{let[t,n]=e;return t+": "+n})).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e){const t=new this(e);for(var n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];return r.forEach((e=>t.set(e))),t}static accessor(e){const t=(this[ro]=this[ro]={accessors:{}}).accessors,n=this.prototype;function r(e){const r=ao(e);t[r]||(!function(e,t){const n=Oa.toCamelCase(" "+t);["get","set","has"].forEach((r=>{Object.defineProperty(e,r+n,{value:function(e,n,a){return this[r].call(this,t,e,n,a)},configurable:!0})}))}(n,e),t[r]=!0)}return Oa.isArray(e)?e.forEach(r):r(e),this}}io.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),Oa.reduceDescriptors(io.prototype,((e,t)=>{let{value:n}=e,r=t[0].toUpperCase()+t.slice(1);return{get:()=>n,set(e){this[r]=e}}})),Oa.freezeMethods(io);const so=io;function uo(e,t){const n=this||to,r=t||n,a=so.from(r.headers);let o=r.data;return Oa.forEach(e,(function(e){o=e.call(n,o,a.normalize(),t?t.status:void 0)})),a.normalize(),o}function co(e){return!(!e||!e.__CANCEL__)}function fo(e,t,n){La.call(this,null==e?"canceled":e,La.ERR_CANCELED,t,n),this.name="CanceledError"}Oa.inherits(fo,La,{__CANCEL__:!0});const po=fo;function ho(e,t,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new La("Request failed with status code "+n.status,[La.ERR_BAD_REQUEST,La.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}const mo=function(e,t){e=e||10;const n=new Array(e),r=new Array(e);let a,o=0,l=0;return t=void 0!==t?t:1e3,function(i){const s=Date.now(),u=r[l];a||(a=s),n[o]=i,r[o]=s;let c=l,d=0;for(;c!==o;)d+=n[c++],c%=e;if(o=(o+1)%e,o===l&&(l=(l+1)%e),s-a<t)return;const f=u&&s-u;return f?Math.round(1e3*d/f):void 0}};const go=function(e,t){let n,r,a=0,o=1e3/t;const l=function(t){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Date.now();a=o,n=null,r&&(clearTimeout(r),r=null),e.apply(null,t)};return[function(){const e=Date.now(),t=e-a;for(var i=arguments.length,s=new Array(i),u=0;u<i;u++)s[u]=arguments[u];t>=o?l(s,e):(n=s,r||(r=setTimeout((()=>{r=null,l(n)}),o-t)))},()=>n&&l(n)]},vo=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3,r=0;const a=mo(50,250);return go((n=>{const o=n.loaded,l=n.lengthComputable?n.total:void 0,i=o-r,s=a(i);r=o;e({loaded:o,total:l,progress:l?o/l:void 0,bytes:i,rate:s||void 0,estimated:s&&l&&o<=l?(l-o)/s:void 0,event:n,lengthComputable:null!=l,[t?"download":"upload"]:!0})}),n)},yo=(e,t)=>{const n=null!=e;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},xo=e=>function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return Oa.asap((()=>e(...n)))},bo=Ya.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,Ya.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(Ya.origin),Ya.navigator&&/(msie|trident)/i.test(Ya.navigator.userAgent)):()=>!0,wo=Ya.hasStandardBrowserEnv?{write(e,t,n,r,a,o){const l=[e+"="+encodeURIComponent(t)];Oa.isNumber(n)&&l.push("expires="+new Date(n).toGMTString()),Oa.isString(r)&&l.push("path="+r),Oa.isString(a)&&l.push("domain="+a),!0===o&&l.push("secure"),document.cookie=l.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function jo(e,t,n){let r=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t);return e&&(r||0==n)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const No=e=>e instanceof so?lt({},e):e;function ko(e,t){t=t||{};const n={};function r(e,t,n,r){return Oa.isPlainObject(e)&&Oa.isPlainObject(t)?Oa.merge.call({caseless:r},e,t):Oa.isPlainObject(t)?Oa.merge({},t):Oa.isArray(t)?t.slice():t}function a(e,t,n,a){return Oa.isUndefined(t)?Oa.isUndefined(e)?void 0:r(void 0,e,0,a):r(e,t,0,a)}function o(e,t){if(!Oa.isUndefined(t))return r(void 0,t)}function l(e,t){return Oa.isUndefined(t)?Oa.isUndefined(e)?void 0:r(void 0,e):r(void 0,t)}function i(n,a,o){return o in t?r(n,a):o in e?r(void 0,n):void 0}const s={url:o,method:o,data:o,baseURL:l,transformRequest:l,transformResponse:l,paramsSerializer:l,timeout:l,timeoutMessage:l,withCredentials:l,withXSRFToken:l,adapter:l,responseType:l,xsrfCookieName:l,xsrfHeaderName:l,onUploadProgress:l,onDownloadProgress:l,decompress:l,maxContentLength:l,maxBodyLength:l,beforeRedirect:l,transport:l,httpAgent:l,httpsAgent:l,cancelToken:l,socketPath:l,responseEncoding:l,validateStatus:i,headers:(e,t,n)=>a(No(e),No(t),0,!0)};return Oa.forEach(Object.keys(Object.assign({},e,t)),(function(r){const o=s[r]||a,l=o(e[r],t[r],r);Oa.isUndefined(l)&&o!==i||(n[r]=l)})),n}const So=e=>{const t=ko({},e);let n,{data:r,withXSRFToken:a,xsrfHeaderName:o,xsrfCookieName:l,headers:i,auth:s}=t;if(t.headers=i=so.from(i),t.url=Va(jo(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),s&&i.set("Authorization","Basic "+btoa((s.username||"")+":"+(s.password?unescape(encodeURIComponent(s.password)):""))),Oa.isFormData(r))if(Ya.hasStandardBrowserEnv||Ya.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if(!1!==(n=i.getContentType())){const[e,...t]=n?n.split(";").map((e=>e.trim())).filter(Boolean):[];i.setContentType([e||"multipart/form-data",...t].join("; "))}if(Ya.hasStandardBrowserEnv&&(a&&Oa.isFunction(a)&&(a=a(t)),a||!1!==a&&bo(t.url))){const e=o&&l&&wo.read(l);e&&i.set(o,e)}return t},Eo="undefined"!==typeof XMLHttpRequest&&function(e){return new Promise((function(t,n){const r=So(e);let a=r.data;const o=so.from(r.headers).normalize();let l,i,s,u,c,{responseType:d,onUploadProgress:f,onDownloadProgress:p}=r;function h(){u&&u(),c&&c(),r.cancelToken&&r.cancelToken.unsubscribe(l),r.signal&&r.signal.removeEventListener("abort",l)}let m=new XMLHttpRequest;function g(){if(!m)return;const r=so.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders());ho((function(e){t(e),h()}),(function(e){n(e),h()}),{data:d&&"text"!==d&&"json"!==d?m.response:m.responseText,status:m.status,statusText:m.statusText,headers:r,config:e,request:m}),m=null}m.open(r.method.toUpperCase(),r.url,!0),m.timeout=r.timeout,"onloadend"in m?m.onloadend=g:m.onreadystatechange=function(){m&&4===m.readyState&&(0!==m.status||m.responseURL&&0===m.responseURL.indexOf("file:"))&&setTimeout(g)},m.onabort=function(){m&&(n(new La("Request aborted",La.ECONNABORTED,e,m)),m=null)},m.onerror=function(){n(new La("Network Error",La.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let t=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const a=r.transitional||qa;r.timeoutErrorMessage&&(t=r.timeoutErrorMessage),n(new La(t,a.clarifyTimeoutError?La.ETIMEDOUT:La.ECONNABORTED,e,m)),m=null},void 0===a&&o.setContentType(null),"setRequestHeader"in m&&Oa.forEach(o.toJSON(),(function(e,t){m.setRequestHeader(t,e)})),Oa.isUndefined(r.withCredentials)||(m.withCredentials=!!r.withCredentials),d&&"json"!==d&&(m.responseType=r.responseType),p&&([s,c]=vo(p,!0),m.addEventListener("progress",s)),f&&m.upload&&([i,u]=vo(f),m.upload.addEventListener("progress",i),m.upload.addEventListener("loadend",u)),(r.cancelToken||r.signal)&&(l=t=>{m&&(n(!t||t.type?new po(null,e,m):t),m.abort(),m=null)},r.cancelToken&&r.cancelToken.subscribe(l),r.signal&&(r.signal.aborted?l():r.signal.addEventListener("abort",l)));const v=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(r.url);v&&-1===Ya.protocols.indexOf(v)?n(new La("Unsupported protocol "+v+":",La.ERR_BAD_REQUEST,e)):m.send(a||null)}))},Co=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let n,r=new AbortController;const a=function(e){if(!n){n=!0,l();const t=e instanceof Error?e:this.reason;r.abort(t instanceof La?t:new po(t instanceof Error?t.message:t))}};let o=t&&setTimeout((()=>{o=null,a(new La("timeout ".concat(t," of ms exceeded"),La.ETIMEDOUT))}),t);const l=()=>{e&&(o&&clearTimeout(o),o=null,e.forEach((e=>{e.unsubscribe?e.unsubscribe(a):e.removeEventListener("abort",a)})),e=null)};e.forEach((e=>e.addEventListener("abort",a)));const{signal:i}=r;return i.unsubscribe=()=>Oa.asap(l),i}};function Oo(e,t){this.v=e,this.k=t}function Po(e){return function(){return new _o(e.apply(this,arguments))}}function _o(e){var t,n;function r(t,n){try{var o=e[t](n),l=o.value,i=l instanceof Oo;Promise.resolve(i?l.v:l).then((function(n){if(i){var s="return"===t?"return":"next";if(!l.k||n.done)return r(s,n);n=e[s](n).value}a(o.done?"return":"normal",n)}),(function(e){r("throw",e)}))}catch(e){a("throw",e)}}function a(e,a){switch(e){case"return":t.resolve({value:a,done:!0});break;case"throw":t.reject(a);break;default:t.resolve({value:a,done:!1})}(t=t.next)?r(t.key,t.arg):n=null}this._invoke=function(e,a){return new Promise((function(o,l){var i={key:e,arg:a,resolve:o,reject:l,next:null};n?n=n.next=i:(t=n=i,r(e,a))}))},"function"!=typeof e.return&&(this.return=void 0)}function Ro(e){return new Oo(e,0)}function Lo(e){var t={},n=!1;function r(t,r){return n=!0,r=new Promise((function(n){n(e[t](r))})),{done:!1,value:new Oo(r,1)}}return t["undefined"!=typeof Symbol&&Symbol.iterator||"@@iterator"]=function(){return this},t.next=function(e){return n?(n=!1,e):r("next",e)},"function"==typeof e.throw&&(t.throw=function(e){if(n)throw n=!1,e;return r("throw",e)}),"function"==typeof e.return&&(t.return=function(e){return n?(n=!1,e):r("return",e)}),t}function To(e){var t,n,r,a=2;for("undefined"!=typeof Symbol&&(n=Symbol.asyncIterator,r=Symbol.iterator);a--;){if(n&&null!=(t=e[n]))return t.call(e);if(r&&null!=(t=e[r]))return new Ao(t.call(e));n="@@asyncIterator",r="@@iterator"}throw new TypeError("Object is not async iterable")}function Ao(e){function t(e){if(Object(e)!==e)return Promise.reject(new TypeError(e+" is not an object."));var t=e.done;return Promise.resolve(e.value).then((function(e){return{value:e,done:t}}))}return Ao=function(e){this.s=e,this.n=e.next},Ao.prototype={s:null,n:null,next:function(){return t(this.n.apply(this.s,arguments))},return:function(e){var n=this.s.return;return void 0===n?Promise.resolve({value:e,done:!0}):t(n.apply(this.s,arguments))},throw:function(e){var n=this.s.return;return void 0===n?Promise.reject(e):t(n.apply(this.s,arguments))}},new Ao(e)}_o.prototype["function"==typeof Symbol&&Symbol.asyncIterator||"@@asyncIterator"]=function(){return this},_o.prototype.next=function(e){return this._invoke("next",e)},_o.prototype.throw=function(e){return this._invoke("throw",e)},_o.prototype.return=function(e){return this._invoke("return",e)};const Mo=function*(e,t){let n=e.byteLength;if(!t||n<t)return void(yield e);let r,a=0;for(;a<n;)r=a+t,yield e.slice(a,r),a=r},Io=function(){var e=Po((function*(e,t){var n,r=!1,a=!1;try{for(var o,l=To(Do(e));r=!(o=yield Ro(l.next())).done;r=!1){const e=o.value;yield*Lo(To(Mo(e,t)))}}catch(i){a=!0,n=i}finally{try{r&&null!=l.return&&(yield Ro(l.return()))}finally{if(a)throw n}}}));return function(t,n){return e.apply(this,arguments)}}(),Do=function(){var e=Po((function*(e){if(e[Symbol.asyncIterator])return void(yield*Lo(To(e)));const t=e.getReader();try{for(;;){const{done:e,value:n}=yield Ro(t.read());if(e)break;yield n}}finally{yield Ro(t.cancel())}}));return function(t){return e.apply(this,arguments)}}(),zo=(e,t,n,r)=>{const a=Io(e,t);let o,l=0,i=e=>{o||(o=!0,r&&r(e))};return new ReadableStream({async pull(e){try{const{done:t,value:r}=await a.next();if(t)return i(),void e.close();let o=r.byteLength;if(n){let e=l+=o;n(e)}e.enqueue(new Uint8Array(r))}catch(t){throw i(t),t}},cancel:e=>(i(e),a.return())},{highWaterMark:2})},Fo="function"===typeof fetch&&"function"===typeof Request&&"function"===typeof Response,Uo=Fo&&"function"===typeof ReadableStream,Bo=Fo&&("function"===typeof TextEncoder?(Wo=new TextEncoder,e=>Wo.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer()));var Wo;const Vo=function(e){try{for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return!!e(...n)}catch(tu){return!1}},$o=Uo&&Vo((()=>{let e=!1;const t=new Request(Ya.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t})),qo=Uo&&Vo((()=>Oa.isReadableStream(new Response("").body))),Ho={stream:qo&&(e=>e.body)};var Qo;Fo&&(Qo=new Response,["text","arrayBuffer","blob","formData","stream"].forEach((e=>{!Ho[e]&&(Ho[e]=Oa.isFunction(Qo[e])?t=>t[e]():(t,n)=>{throw new La("Response type '".concat(e,"' is not supported"),La.ERR_NOT_SUPPORT,n)})})));const Ko=async(e,t)=>{const n=Oa.toFiniteNumber(e.getContentLength());return null==n?(async e=>{if(null==e)return 0;if(Oa.isBlob(e))return e.size;if(Oa.isSpecCompliantForm(e)){const t=new Request(Ya.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return Oa.isArrayBufferView(e)||Oa.isArrayBuffer(e)?e.byteLength:(Oa.isURLSearchParams(e)&&(e+=""),Oa.isString(e)?(await Bo(e)).byteLength:void 0)})(t):n},Jo=Fo&&(async e=>{let{url:t,method:n,data:r,signal:a,cancelToken:o,timeout:l,onDownloadProgress:i,onUploadProgress:s,responseType:u,headers:c,withCredentials:d="same-origin",fetchOptions:f}=So(e);u=u?(u+"").toLowerCase():"text";let p,h=Co([a,o&&o.toAbortSignal()],l);const m=h&&h.unsubscribe&&(()=>{h.unsubscribe()});let g;try{if(s&&$o&&"get"!==n&&"head"!==n&&0!==(g=await Ko(c,r))){let e,n=new Request(t,{method:"POST",body:r,duplex:"half"});if(Oa.isFormData(r)&&(e=n.headers.get("content-type"))&&c.setContentType(e),n.body){const[e,t]=yo(g,vo(xo(s)));r=zo(n.body,65536,e,t)}}Oa.isString(d)||(d=d?"include":"omit");const a="credentials"in Request.prototype;p=new Request(t,lt(lt({},f),{},{signal:h,method:n.toUpperCase(),headers:c.normalize().toJSON(),body:r,duplex:"half",credentials:a?d:void 0}));let o=await fetch(p);const l=qo&&("stream"===u||"response"===u);if(qo&&(i||l&&m)){const e={};["status","statusText","headers"].forEach((t=>{e[t]=o[t]}));const t=Oa.toFiniteNumber(o.headers.get("content-length")),[n,r]=i&&yo(t,vo(xo(i),!0))||[];o=new Response(zo(o.body,65536,n,(()=>{r&&r(),m&&m()})),e)}u=u||"text";let v=await Ho[Oa.findKey(Ho,u)||"text"](o,e);return!l&&m&&m(),await new Promise(((t,n)=>{ho(t,n,{data:v,headers:so.from(o.headers),status:o.status,statusText:o.statusText,config:e,request:p})}))}catch(v){if(m&&m(),v&&"TypeError"===v.name&&/Load failed|fetch/i.test(v.message))throw Object.assign(new La("Network Error",La.ERR_NETWORK,e,p),{cause:v.cause||v});throw La.from(v,v&&v.code,e,p)}}),Xo={http:null,xhr:Eo,fetch:Jo};Oa.forEach(Xo,((e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(tu){}Object.defineProperty(e,"adapterName",{value:t})}}));const Zo=e=>"- ".concat(e),Yo=e=>Oa.isFunction(e)||null===e||!1===e,Go=e=>{e=Oa.isArray(e)?e:[e];const{length:t}=e;let n,r;const a={};for(let o=0;o<t;o++){let t;if(n=e[o],r=n,!Yo(n)&&(r=Xo[(t=String(n)).toLowerCase()],void 0===r))throw new La("Unknown adapter '".concat(t,"'"));if(r)break;a[t||"#"+o]=r}if(!r){const e=Object.entries(a).map((e=>{let[t,n]=e;return"adapter ".concat(t," ")+(!1===n?"is not supported by the environment":"is not available in the build")}));let n=t?e.length>1?"since :\n"+e.map(Zo).join("\n"):" "+Zo(e[0]):"as no adapter specified";throw new La("There is no suitable adapter to dispatch the request "+n,"ERR_NOT_SUPPORT")}return r};function el(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new po(null,e)}function tl(e){el(e),e.headers=so.from(e.headers),e.data=uo.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);return Go(e.adapter||to.adapter)(e).then((function(t){return el(e),t.data=uo.call(e,e.transformResponse,t),t.headers=so.from(t.headers),t}),(function(t){return co(t)||(el(e),t&&t.response&&(t.response.data=uo.call(e,e.transformResponse,t.response),t.response.headers=so.from(t.response.headers))),Promise.reject(t)}))}const nl="1.9.0",rl={};["object","boolean","number","function","string","symbol"].forEach(((e,t)=>{rl[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}}));const al={};rl.transitional=function(e,t,n){function r(e,t){return"[Axios v1.9.0] Transitional option '"+e+"'"+t+(n?". "+n:"")}return(n,a,o)=>{if(!1===e)throw new La(r(a," has been removed"+(t?" in "+t:"")),La.ERR_DEPRECATED);return t&&!al[a]&&(al[a]=!0,console.warn(r(a," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,a,o)}},rl.spelling=function(e){return(t,n)=>(console.warn("".concat(n," is likely a misspelling of ").concat(e)),!0)};const ol={assertOptions:function(e,t,n){if("object"!==typeof e)throw new La("options must be an object",La.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let a=r.length;for(;a-- >0;){const o=r[a],l=t[o];if(l){const t=e[o],n=void 0===t||l(t,o,e);if(!0!==n)throw new La("option "+o+" must be "+n,La.ERR_BAD_OPTION_VALUE)}else if(!0!==n)throw new La("Unknown option "+o,La.ERR_BAD_OPTION)}},validators:rl},ll=ol.validators;class il{constructor(e){this.defaults=e||{},this.interceptors={request:new $a,response:new $a}}async request(e,t){try{return await this._request(e,t)}catch(n){if(n instanceof Error){let e={};Error.captureStackTrace?Error.captureStackTrace(e):e=new Error;const t=e.stack?e.stack.replace(/^.+\n/,""):"";try{n.stack?t&&!String(n.stack).endsWith(t.replace(/^.+\n.+\n/,""))&&(n.stack+="\n"+t):n.stack=t}catch(tu){}}throw n}}_request(e,t){"string"===typeof e?(t=t||{}).url=e:t=e||{},t=ko(this.defaults,t);const{transitional:n,paramsSerializer:r,headers:a}=t;void 0!==n&&ol.assertOptions(n,{silentJSONParsing:ll.transitional(ll.boolean),forcedJSONParsing:ll.transitional(ll.boolean),clarifyTimeoutError:ll.transitional(ll.boolean)},!1),null!=r&&(Oa.isFunction(r)?t.paramsSerializer={serialize:r}:ol.assertOptions(r,{encode:ll.function,serialize:ll.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),ol.assertOptions(t,{baseUrl:ll.spelling("baseURL"),withXsrfToken:ll.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let o=a&&Oa.merge(a.common,a[t.method]);a&&Oa.forEach(["delete","get","head","post","put","patch","common"],(e=>{delete a[e]})),t.headers=so.concat(o,a);const l=[];let i=!0;this.interceptors.request.forEach((function(e){"function"===typeof e.runWhen&&!1===e.runWhen(t)||(i=i&&e.synchronous,l.unshift(e.fulfilled,e.rejected))}));const s=[];let u;this.interceptors.response.forEach((function(e){s.push(e.fulfilled,e.rejected)}));let c,d=0;if(!i){const e=[tl.bind(this),void 0];for(e.unshift.apply(e,l),e.push.apply(e,s),c=e.length,u=Promise.resolve(t);d<c;)u=u.then(e[d++],e[d++]);return u}c=l.length;let f=t;for(d=0;d<c;){const e=l[d++],t=l[d++];try{f=e(f)}catch(p){t.call(this,p);break}}try{u=tl.call(this,f)}catch(p){return Promise.reject(p)}for(d=0,c=s.length;d<c;)u=u.then(s[d++],s[d++]);return u}getUri(e){return Va(jo((e=ko(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}}Oa.forEach(["delete","get","head","options"],(function(e){il.prototype[e]=function(t,n){return this.request(ko(n||{},{method:e,url:t,data:(n||{}).data}))}})),Oa.forEach(["post","put","patch"],(function(e){function t(t){return function(n,r,a){return this.request(ko(a||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}il.prototype[e]=t(),il.prototype[e+"Form"]=t(!0)}));const sl=il;class ul{constructor(e){if("function"!==typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise((function(e){t=e}));const n=this;this.promise.then((e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null})),this.promise.then=e=>{let t;const r=new Promise((e=>{n.subscribe(e),t=e})).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e((function(e,r,a){n.reason||(n.reason=new po(e,r,a),t(n.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;const t=new ul((function(t){e=t}));return{token:t,cancel:e}}}const cl=ul;const dl={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(dl).forEach((e=>{let[t,n]=e;dl[n]=t}));const fl=dl;const pl=function e(t){const n=new sl(t),r=Vr(sl.prototype.request,n);return Oa.extend(r,sl.prototype,n,{allOwnKeys:!0}),Oa.extend(r,n,null,{allOwnKeys:!0}),r.create=function(n){return e(ko(t,n))},r}(to);pl.Axios=sl,pl.CanceledError=po,pl.CancelToken=cl,pl.isCancel=co,pl.VERSION=nl,pl.toFormData=Da,pl.AxiosError=La,pl.Cancel=pl.CanceledError,pl.all=function(e){return Promise.all(e)},pl.spread=function(e){return function(t){return e.apply(null,t)}},pl.isAxiosError=function(e){return Oa.isObject(e)&&!0===e.isAxiosError},pl.mergeConfig=ko,pl.AxiosHeaders=so,pl.formToJSON=e=>Ga(Oa.isHTMLForm(e)?new FormData(e):e),pl.getAdapter=Go,pl.HttpStatusCode=fl,pl.default=pl;const hl=pl,ml=zr("search/searchResources",(async e=>(await hl.post("/v1/discovery/search",e)).data)),gl=zr("search/getResource",(async e=>(await hl.get("/v1/discovery/resources/".concat(e))).data.resource)),vl=Lr({name:"search",initialState:{resources:[],loading:!1,error:null,totalCount:0,query:"",provider:""},reducers:{setQuery:(e,t)=>{e.query=t.payload},setProvider:(e,t)=>{e.provider=t.payload},clearResults:e=>{e.resources=[],e.totalCount=0,e.error=null}},extraReducers:e=>{e.addCase(ml.pending,(e=>{e.loading=!0,e.error=null})).addCase(ml.fulfilled,((e,t)=>{e.loading=!1,e.resources=t.payload.resources,e.totalCount=t.payload.total_count})).addCase(ml.rejected,((e,t)=>{e.loading=!1,e.error=t.error.message||"Search failed"})).addCase(gl.pending,(e=>{e.loading=!0,e.error=null})).addCase(gl.fulfilled,((e,t)=>{e.loading=!1;const n=e.resources.findIndex((e=>e.id===t.payload.id));-1!==n&&(e.resources[n]=t.payload)})).addCase(gl.rejected,((e,t)=>{e.loading=!1,e.error=t.error.message||"Failed to get resource"}))}}),{setQuery:yl,setProvider:xl,clearResults:bl}=vl.actions,wl=vl.reducer,jl=["title","titleId"];function Nl(e,t){let{title:n,titleId:r}=e,o=pt(e,jl);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},o),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 15a4.5 4.5 0 0 0 4.5 4.5H18a3.75 3.75 0 0 0 1.332-7.257 3 3 0 0 0-3.758-3.848 5.25 5.25 0 0 0-10.233 2.33A4.502 4.502 0 0 0 2.25 15Z"}))}const kl=a.forwardRef(Nl),Sl=["title","titleId"];function El(e,t){let{title:n,titleId:r}=e,o=pt(e,Sl);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},o),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 17.25v-.228a4.5 4.5 0 0 0-.12-1.03l-2.268-9.64a3.375 3.375 0 0 0-3.285-2.602H7.923a3.375 3.375 0 0 0-3.285 2.602l-2.268 9.64a4.5 4.5 0 0 0-.12 1.03v.228m19.5 0a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3m19.5 0a3 3 0 0 0-3-3H5.25a3 3 0 0 0-3 3m16.5 0h.008v.008h-.008v-.008Zm-3 0h.008v.008h-.008v-.008Z"}))}const Cl=a.forwardRef(El),Ol=["title","titleId"];function Pl(e,t){let{title:n,titleId:r}=e,o=pt(e,Ol);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},o),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.568 3H5.25A2.25 2.25 0 0 0 3 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 0 0 5.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 0 0 9.568 3Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 6h.008v.008H6V6Z"}))}const _l=a.forwardRef(Pl),Rl=()=>{const e=R(),{resources:t,loading:n,error:r,totalCount:o,query:l,provider:i}=j((e=>e.search)),[s,u]=(0,a.useState)(l),[c,d]=(0,a.useState)(i),f=()=>{e(yl(s)),e(xl(c)),e(ml({query:s,provider:c,limit:50}))};(0,a.useEffect)((()=>{e(ml({limit:20}))}),[e]);const p=e=>{switch(e.toLowerCase()){case"aws":return"bg-orange-100 text-orange-800";case"gcp":return"bg-blue-100 text-blue-800";case"azure":return"bg-cyan-100 text-cyan-800";default:return"bg-gray-100 text-gray-800"}};return(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h1",{className:"text-2xl font-bold text-white",children:"Resource Discovery"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Search and discover resources across all your cloud providers"})]}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsx)("div",{className:"px-4 py-5 sm:p-6",children:(0,it.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-4",children:[(0,it.jsxs)("div",{className:"sm:col-span-2",children:[(0,it.jsx)("label",{htmlFor:"search",className:"block text-sm font-medium text-gray-300",children:"Search Query"}),(0,it.jsxs)("div",{className:"mt-1 relative rounded-md shadow-sm",children:[(0,it.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,it.jsx)(xt,{className:"h-5 w-5 text-gray-400"})}),(0,it.jsx)("input",{type:"text",id:"search",className:"block w-full pl-10 pr-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent",placeholder:"Search by name, type, or tags...",value:s,onChange:e=>u(e.target.value),onKeyPress:e=>"Enter"===e.key&&f()})]})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{htmlFor:"provider",className:"block text-sm font-medium text-gray-300",children:"Provider"}),(0,it.jsx)("select",{id:"provider",className:"mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-600 bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent rounded-md",value:c,onChange:e=>d(e.target.value),children:[{value:"",label:"All Providers"},{value:"aws",label:"AWS"},{value:"gcp",label:"GCP"},{value:"azure",label:"Azure"}].map((e=>(0,it.jsx)("option",{value:e.value,children:e.label},e.value)))})]}),(0,it.jsxs)("div",{className:"flex items-end space-x-2",children:[(0,it.jsx)("button",{type:"button",onClick:f,disabled:n,className:"flex-1 bg-cyan-600 border border-transparent rounded-md py-2 px-4 inline-flex justify-center text-sm font-medium text-white hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 disabled:opacity-50",children:n?"Searching...":"Search"}),(0,it.jsx)("button",{type:"button",onClick:()=>{u(""),d(""),e(bl())},className:"bg-gray-600 border border-transparent rounded-md py-2 px-4 inline-flex justify-center text-sm font-medium text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500",children:"Clear"})]})]})})}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white",children:"Search Results"}),(0,it.jsxs)("span",{className:"text-sm text-gray-400",children:[o," resources found"]})]}),r&&(0,it.jsx)("div",{className:"mb-4 bg-red-50 border border-red-200 rounded-md p-4",children:(0,it.jsx)("div",{className:"text-sm text-red-700",children:r})}),n?(0,it.jsx)("div",{className:"flex items-center justify-center h-32",children:(0,it.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500"})}):(0,it.jsx)("div",{className:"space-y-4",children:0===t.length?(0,it.jsxs)("div",{className:"text-center py-12",children:[(0,it.jsx)(kl,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,it.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-300",children:"No resources found"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Try adjusting your search criteria or run a discovery scan."})]}):t.map((e=>(0,it.jsxs)("div",{className:"border border-gray-600 rounded-lg p-4 hover:bg-gray-700 transition-colors",children:[(0,it.jsx)("div",{className:"flex items-start justify-between",children:(0,it.jsxs)("div",{className:"flex-1",children:[(0,it.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,it.jsx)(Cl,{className:"h-5 w-5 text-gray-400"}),(0,it.jsx)("h4",{className:"text-lg font-medium text-white",children:e.name}),(0,it.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(p(e.provider)),children:e.provider.toUpperCase()})]}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:e.type}),(0,it.jsx)("p",{className:"mt-1 text-xs text-gray-500 font-mono",children:e.id})]})}),Object.keys(e.tags).length>0&&(0,it.jsxs)("div",{className:"mt-3",children:[(0,it.jsxs)("div",{className:"flex items-center space-x-1 mb-2",children:[(0,it.jsx)(_l,{className:"h-4 w-4 text-gray-400"}),(0,it.jsx)("span",{className:"text-sm text-gray-400",children:"Tags"})]}),(0,it.jsx)("div",{className:"flex flex-wrap gap-1",children:Object.entries(e.tags).map((e=>{let[t,n]=e;return(0,it.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-600 text-gray-200",children:[t,": ",n]},t)}))})]})]},e.id)))})]})})]})},Ll=["title","titleId"];function Tl(e,t){let{title:n,titleId:r}=e,o=pt(e,Ll);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},o),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 5.25a3 3 0 0 1 3 3m3 0a6 6 0 0 1-7.029 5.912c-.563-.097-1.159.026-1.563.43L10.5 17.25H8.25v2.25H6v2.25H2.25v-2.818c0-.597.237-1.17.659-1.591l6.499-6.499c.404-.404.527-1 .43-1.563A6 6 0 1 1 21.75 8.25Z"}))}const Al=a.forwardRef(Tl),Ml=["title","titleId"];function Il(e,t){let{title:n,titleId:r}=e,o=pt(e,Ml);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},o),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const Dl=a.forwardRef(Il),zl=["title","titleId"];function Fl(e,t){let{title:n,titleId:r}=e,o=pt(e,zl);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},o),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))}const Ul=a.forwardRef(Fl),Bl=()=>{const e=R(),[t,n]=(0,a.useState)(1),[r,o]=(0,a.useState)({provider:"aws"}),[l,i]=(0,a.useState)(!1),[s,u]=(0,a.useState)(null),c=[{id:1,name:"Choose Provider",description:"Select your cloud provider"},{id:2,name:"Add Credentials",description:"Configure authentication"},{id:3,name:"Configure Discovery",description:"Set discovery parameters"},{id:4,name:"Launch Discovery",description:"Start resource discovery"}],d=[{id:"aws",name:"Amazon Web Services",icon:"\ud83d\udfe0",description:"Discover EC2, S3, RDS, and other AWS resources",fields:["accessKey","secretKey","region"]},{id:"gcp",name:"Google Cloud Platform",icon:"\ud83d\udd35",description:"Discover Compute Engine, Cloud Storage, and GCP resources",fields:["projectId"]},{id:"azure",name:"Microsoft Azure",icon:"\ud83d\udd37",description:"Discover Virtual Machines, Storage, and Azure resources",fields:["subscriptionId","tenantId"]}],f=(e,t)=>{o((n=>lt(lt({},n),{},{[e]:t})))},p=async()=>{i(!0),u(null),setTimeout((()=>{u("success"),i(!1)}),2e3)},h=()=>{e(ml({provider:r.provider,limit:100})),n(4)};return(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h1",{className:"text-2xl font-bold text-white",children:"Discovery Wizard"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Multi-step wizard to add credentials and launch discovery workflows"})]}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsx)("div",{className:"px-4 py-5 sm:p-6",children:(0,it.jsx)("nav",{"aria-label":"Progress",children:(0,it.jsx)("ol",{className:"flex items-center",children:c.map(((e,n)=>(0,it.jsxs)("li",{className:"".concat(n!==c.length-1?"pr-8 sm:pr-20":""," relative"),children:[(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"relative flex h-8 w-8 items-center justify-center rounded-full ".concat(e.id<=t?"bg-cyan-600 text-white":"border-2 border-gray-600 bg-gray-800 text-gray-400"),children:e.id<t?(0,it.jsx)(Dl,{className:"h-5 w-5"}):(0,it.jsx)("span",{className:"text-sm font-medium",children:e.id})}),(0,it.jsxs)("div",{className:"ml-4 min-w-0 flex-1",children:[(0,it.jsx)("p",{className:"text-sm font-medium ".concat(e.id<=t?"text-white":"text-gray-400"),children:e.name}),(0,it.jsx)("p",{className:"text-sm text-gray-400",children:e.description})]})]}),n!==c.length-1&&(0,it.jsx)("div",{className:"absolute top-4 left-4 -ml-px mt-0.5 h-full w-0.5 bg-gray-600"})]},e.id)))})})})}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsx)("div",{className:"px-4 py-5 sm:p-6",children:(()=>{switch(t){case 1:return(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsxs)("div",{className:"text-center",children:[(0,it.jsx)(kl,{className:"mx-auto h-12 w-12 text-cyan-400"}),(0,it.jsx)("h3",{className:"mt-2 text-lg font-medium text-white",children:"Choose Cloud Provider"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Select the cloud provider you want to discover resources from"})]}),(0,it.jsx)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-3",children:d.map((e=>(0,it.jsx)("button",{onClick:()=>{return t=e.id,o({provider:t}),void n(2);var t},className:"relative rounded-lg border border-gray-600 bg-gray-800 p-6 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-cyan-500 transition-colors",children:(0,it.jsxs)("div",{className:"text-center",children:[(0,it.jsx)("div",{className:"text-3xl mb-3",children:e.icon}),(0,it.jsx)("h4",{className:"text-lg font-medium text-white",children:e.name}),(0,it.jsx)("p",{className:"mt-2 text-sm text-gray-400",children:e.description})]})},e.id)))})]});case 2:const e=d.find((e=>e.id===r.provider));return(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsxs)("div",{className:"text-center",children:[(0,it.jsx)(Al,{className:"mx-auto h-12 w-12 text-cyan-400"}),(0,it.jsxs)("h3",{className:"mt-2 text-lg font-medium text-white",children:["Configure ",null===e||void 0===e?void 0:e.name," Credentials"]}),(0,it.jsxs)("p",{className:"mt-1 text-sm text-gray-400",children:["Enter your authentication credentials for ",null===e||void 0===e?void 0:e.name]})]}),(0,it.jsxs)("div",{className:"space-y-4",children:["aws"===r.provider&&(0,it.jsxs)(it.Fragment,{children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Access Key ID"}),(0,it.jsx)("input",{type:"text",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:r.accessKey||"",onChange:e=>f("accessKey",e.target.value),placeholder:"AKIA..."})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Secret Access Key"}),(0,it.jsx)("input",{type:"password",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:r.secretKey||"",onChange:e=>f("secretKey",e.target.value),placeholder:"\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Region"}),(0,it.jsxs)("select",{className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:r.region||"",onChange:e=>f("region",e.target.value),children:[(0,it.jsx)("option",{value:"",children:"Select a region"}),(0,it.jsx)("option",{value:"us-east-1",children:"US East (N. Virginia)"}),(0,it.jsx)("option",{value:"us-west-2",children:"US West (Oregon)"}),(0,it.jsx)("option",{value:"eu-west-1",children:"Europe (Ireland)"}),(0,it.jsx)("option",{value:"ap-southeast-1",children:"Asia Pacific (Singapore)"})]})]})]}),"gcp"===r.provider&&(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Project ID"}),(0,it.jsx)("input",{type:"text",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:r.projectId||"",onChange:e=>f("projectId",e.target.value),placeholder:"my-gcp-project"})]}),"azure"===r.provider&&(0,it.jsxs)(it.Fragment,{children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Subscription ID"}),(0,it.jsx)("input",{type:"text",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:r.subscriptionId||"",onChange:e=>f("subscriptionId",e.target.value),placeholder:"xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Tenant ID"}),(0,it.jsx)("input",{type:"text",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:r.tenantId||"",onChange:e=>f("tenantId",e.target.value),placeholder:"xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"})]})]})]}),(0,it.jsxs)("div",{className:"flex justify-between",children:[(0,it.jsx)("button",{onClick:()=>n(1),className:"px-4 py-2 border border-gray-600 rounded-md text-gray-300 hover:bg-gray-700",children:"Back"}),(0,it.jsx)("button",{onClick:p,disabled:l,className:"px-4 py-2 bg-cyan-600 text-white rounded-md hover:bg-cyan-700 disabled:opacity-50",children:l?"Validating...":"Validate & Continue"})]}),"success"===s&&(0,it.jsxs)("div",{className:"flex items-center space-x-2 text-green-400",children:[(0,it.jsx)(Dl,{className:"h-5 w-5"}),(0,it.jsx)("span",{children:"Credentials validated successfully!"}),(0,it.jsx)("button",{onClick:()=>n(3),className:"ml-4 px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700",children:"Continue"})]}),"error"===s&&(0,it.jsxs)("div",{className:"flex items-center space-x-2 text-red-400",children:[(0,it.jsx)(Ul,{className:"h-5 w-5"}),(0,it.jsx)("span",{children:"Credential validation failed. Please check your credentials."})]})]});case 3:return(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsxs)("div",{className:"text-center",children:[(0,it.jsx)(xt,{className:"mx-auto h-12 w-12 text-cyan-400"}),(0,it.jsx)("h3",{className:"mt-2 text-lg font-medium text-white",children:"Configure Discovery"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Set parameters for resource discovery"})]}),(0,it.jsxs)("div",{className:"space-y-4",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Discovery Scope"}),(0,it.jsxs)("select",{className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",children:[(0,it.jsx)("option",{children:"All Resources"}),(0,it.jsx)("option",{children:"Compute Only"}),(0,it.jsx)("option",{children:"Storage Only"}),(0,it.jsx)("option",{children:"Network Only"})]})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Resource Tags Filter"}),(0,it.jsx)("input",{type:"text",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",placeholder:"Environment=production,Team=platform"})]}),(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("input",{type:"checkbox",className:"h-4 w-4 text-cyan-600 focus:ring-cyan-500 border-gray-600 rounded bg-gray-700",defaultChecked:!0}),(0,it.jsx)("label",{className:"ml-2 block text-sm text-gray-300",children:"Cache results for faster subsequent searches"})]})]}),(0,it.jsxs)("div",{className:"flex justify-between",children:[(0,it.jsx)("button",{onClick:()=>n(2),className:"px-4 py-2 border border-gray-600 rounded-md text-gray-300 hover:bg-gray-700",children:"Back"}),(0,it.jsx)("button",{onClick:h,className:"px-4 py-2 bg-cyan-600 text-white rounded-md hover:bg-cyan-700",children:"Launch Discovery"})]})]});case 4:return(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsxs)("div",{className:"text-center",children:[(0,it.jsx)(Dl,{className:"mx-auto h-12 w-12 text-green-400"}),(0,it.jsx)("h3",{className:"mt-2 text-lg font-medium text-white",children:"Discovery Launched!"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Resource discovery is now running in the background"})]}),(0,it.jsxs)("div",{className:"bg-gray-700 rounded-lg p-4",children:[(0,it.jsx)("h4",{className:"text-md font-medium text-white mb-2",children:"Discovery Status"}),(0,it.jsxs)("div",{className:"space-y-2",children:[(0,it.jsxs)("div",{className:"flex justify-between",children:[(0,it.jsx)("span",{className:"text-gray-300",children:"Provider:"}),(0,it.jsx)("span",{className:"text-white",children:r.provider.toUpperCase()})]}),(0,it.jsxs)("div",{className:"flex justify-between",children:[(0,it.jsx)("span",{className:"text-gray-300",children:"Status:"}),(0,it.jsx)("span",{className:"text-green-400",children:"Running"})]}),(0,it.jsxs)("div",{className:"flex justify-between",children:[(0,it.jsx)("span",{className:"text-gray-300",children:"Resources Found:"}),(0,it.jsx)("span",{className:"text-white",children:"42"})]})]})]}),(0,it.jsx)("div",{className:"flex justify-center",children:(0,it.jsx)("button",{onClick:()=>window.location.href="/search",className:"px-6 py-2 bg-cyan-600 text-white rounded-md hover:bg-cyan-700",children:"View Results"})})]});default:return null}})()})})]})},Wl=zr("workflow/fetchWorkflows",(async()=>(await hl.get("/v1/workflows")).data.workflows)),Vl=zr("workflow/executeWorkflow",(async e=>(await hl.post("/v1/workflows/execute",{workflow_id:e.workflowId,inputs:e.inputs})).data)),$l=zr("workflow/getWorkflowStatus",(async e=>(await hl.get("/v1/workflows/".concat(e,"/status"))).data)),ql=Lr({name:"workflow",initialState:{workflows:[],executions:[],loading:!1,error:null,selectedWorkflow:null},reducers:{setSelectedWorkflow:(e,t)=>{e.selectedWorkflow=t.payload},clearExecutions:e=>{e.executions=[]},updateExecutionStatus:(e,t)=>{const n=e.executions.find((e=>e.execution_id===t.payload.executionId));n&&(n.status=t.payload.status)}},extraReducers:e=>{e.addCase(Wl.pending,(e=>{e.loading=!0,e.error=null})).addCase(Wl.fulfilled,((e,t)=>{e.loading=!1,e.workflows=t.payload})).addCase(Wl.rejected,((e,t)=>{e.loading=!1,e.error=t.error.message||"Failed to fetch workflows"})).addCase(Vl.pending,(e=>{e.loading=!0,e.error=null})).addCase(Vl.fulfilled,((e,t)=>{e.loading=!1,e.executions.push(t.payload)})).addCase(Vl.rejected,((e,t)=>{e.loading=!1,e.error=t.error.message||"Failed to execute workflow"})).addCase($l.fulfilled,((e,t)=>{const n=e.executions.findIndex((e=>e.execution_id===t.payload.workflow_id));-1!==n&&(e.executions[n]=lt(lt({},e.executions[n]),t.payload))}))}}),{setSelectedWorkflow:Hl,clearExecutions:Ql,updateExecutionStatus:Kl}=ql.actions,Jl=ql.reducer,Xl=["title","titleId"];function Zl(e,t){let{title:n,titleId:r}=e,o=pt(e,Xl);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},o),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const Yl=a.forwardRef(Zl),Gl=["title","titleId"];function ei(e,t){let{title:n,titleId:r}=e,o=pt(e,Gl);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},o),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const ti=a.forwardRef(ei),ni=["title","titleId"];function ri(e,t){let{title:n,titleId:r}=e,o=pt(e,ni);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},o),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z"}))}const ai=a.forwardRef(ri),oi=()=>{const e=R(),{workflows:t,executions:n,loading:r,error:o,selectedWorkflow:l}=j((e=>e.workflow)),[i,s]=(0,a.useState)({});(0,a.useEffect)((()=>{e(Wl())}),[e]);const u=e=>{switch(e){case"running":return(0,it.jsx)(Yl,{className:"h-5 w-5 text-yellow-400"});case"completed":return(0,it.jsx)(Dl,{className:"h-5 w-5 text-green-400"});case"failed":return(0,it.jsx)(ti,{className:"h-5 w-5 text-red-400"});default:return(0,it.jsx)(jt,{className:"h-5 w-5 text-gray-400"})}},c=e=>{switch(e){case"running":return"bg-yellow-100 text-yellow-800";case"completed":return"bg-green-100 text-green-800";case"failed":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}};return(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h1",{className:"text-2xl font-bold text-white",children:"Workflow Executor"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Execute and monitor automated workflows across your infrastructure"})]}),(0,it.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white mb-4",children:"Available Workflows"}),r?(0,it.jsx)("div",{className:"flex items-center justify-center h-32",children:(0,it.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500"})}):(0,it.jsxs)("div",{className:"space-y-3",children:[t.map((t=>(0,it.jsx)("div",{className:"border rounded-lg p-4 cursor-pointer transition-colors ".concat((null===l||void 0===l?void 0:l.id)===t.id?"border-cyan-500 bg-cyan-500/10":"border-gray-600 hover:bg-gray-700"),onClick:()=>e(Hl(t)),children:(0,it.jsxs)("div",{className:"flex items-start justify-between",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h4",{className:"text-lg font-medium text-white",children:t.name}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:t.description}),(0,it.jsxs)("p",{className:"mt-1 text-xs text-gray-500",children:["Created: ",new Date(t.created_at).toLocaleDateString()]})]}),(0,it.jsx)(jt,{className:"h-6 w-6 text-gray-400"})]})},t.id))),0===t.length&&(0,it.jsxs)("div",{className:"text-center py-8",children:[(0,it.jsx)(jt,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,it.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-300",children:"No workflows available"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Create a workflow to get started."})]})]})]})}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white mb-4",children:"Execute Workflow"}),l?(0,it.jsxs)("div",{className:"space-y-4",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h4",{className:"text-md font-medium text-white",children:l.name}),(0,it.jsx)("p",{className:"text-sm text-gray-400",children:l.description})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Input Parameters (JSON)"}),(0,it.jsx)("textarea",{className:"block w-full h-32 px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent",placeholder:'{"key": "value"}',value:JSON.stringify(i,null,2),onChange:e=>{try{s(JSON.parse(e.target.value||"{}"))}catch(t){}}})]}),(0,it.jsxs)("button",{onClick:()=>{l&&(e(Vl({workflowId:l.id,inputs:i})),s({}))},disabled:r,className:"w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 disabled:opacity-50",children:[(0,it.jsx)(ai,{className:"h-4 w-4 mr-2"}),r?"Executing...":"Execute Workflow"]})]}):(0,it.jsxs)("div",{className:"text-center py-8",children:[(0,it.jsx)(ai,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,it.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-300",children:"Select a workflow"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Choose a workflow from the list to execute it."})]}),o&&(0,it.jsx)("div",{className:"mt-4 bg-red-50 border border-red-200 rounded-md p-4",children:(0,it.jsx)("div",{className:"text-sm text-red-700",children:o})})]})})]}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white mb-4",children:"Execution History"}),0===n.length?(0,it.jsxs)("div",{className:"text-center py-8",children:[(0,it.jsx)(Yl,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,it.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-300",children:"No executions yet"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Execute a workflow to see the history here."})]}):(0,it.jsx)("div",{className:"space-y-4",children:n.map((t=>(0,it.jsxs)("div",{className:"border border-gray-600 rounded-lg p-4",children:[(0,it.jsxs)("div",{className:"flex items-start justify-between",children:[(0,it.jsxs)("div",{className:"flex-1",children:[(0,it.jsxs)("div",{className:"flex items-center space-x-2",children:[u(t.status),(0,it.jsx)("h4",{className:"text-lg font-medium text-white",children:t.workflow_id}),(0,it.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(c(t.status)),children:t.status})]}),(0,it.jsxs)("p",{className:"mt-1 text-sm text-gray-400",children:["Execution ID: ",t.execution_id]}),t.started_at&&(0,it.jsxs)("p",{className:"mt-1 text-xs text-gray-500",children:["Started: ",new Date(t.started_at).toLocaleString()]}),t.completed_at&&(0,it.jsxs)("p",{className:"mt-1 text-xs text-gray-500",children:["Completed: ",new Date(t.completed_at).toLocaleString()]})]}),(0,it.jsx)("button",{onClick:()=>e($l(t.execution_id)),className:"text-cyan-400 hover:text-cyan-300 text-sm",children:"Refresh"})]}),t.outputs&&Object.keys(t.outputs).length>0&&(0,it.jsxs)("div",{className:"mt-3",children:[(0,it.jsx)("h5",{className:"text-sm font-medium text-gray-300",children:"Outputs:"}),(0,it.jsx)("pre",{className:"mt-1 text-xs text-gray-400 bg-gray-700 p-2 rounded overflow-x-auto",children:JSON.stringify(t.outputs,null,2)})]})]},t.execution_id)))})]})})]})},li=zr("envoy/fetchConfigs",(async()=>(await hl.get("/v1/envoy/configs")).data.configs)),ii=zr("envoy/fetchNodes",(async()=>(await hl.get("/v1/envoy/nodes")).data.nodes)),si=zr("envoy/createConfig",(async e=>(await hl.post("/v1/envoy/configs",e)).data)),ui=zr("envoy/updateConfig",(async e=>(await hl.put("/v1/envoy/configs/".concat(e.id),e)).data)),ci=Lr({name:"envoy",initialState:{configs:[],nodes:[],selectedConfig:null,loading:!1,error:null},reducers:{setSelectedConfig:(e,t)=>{e.selectedConfig=t.payload}},extraReducers:e=>{e.addCase(li.pending,(e=>{e.loading=!0,e.error=null})).addCase(li.fulfilled,((e,t)=>{e.loading=!1,e.configs=t.payload})).addCase(li.rejected,((e,t)=>{e.loading=!1,e.error=t.error.message||"Failed to fetch Envoy configs"})).addCase(ii.fulfilled,((e,t)=>{e.nodes=t.payload})).addCase(si.fulfilled,((e,t)=>{e.configs.push(t.payload)})).addCase(ui.fulfilled,((e,t)=>{const n=e.configs.findIndex((e=>e.id===t.payload.id));-1!==n&&(e.configs[n]=t.payload)}))}}),{setSelectedConfig:di}=ci.actions,fi=ci.reducer,pi=["title","titleId"];function hi(e,t){let{title:n,titleId:r}=e,o=pt(e,pi);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},o),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))}const mi=a.forwardRef(hi),gi=["title","titleId"];function vi(e,t){let{title:n,titleId:r}=e,o=pt(e,gi);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},o),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))}const yi=a.forwardRef(vi);function xi(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function bi(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function wi(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?bi(Object(n),!0).forEach((function(t){xi(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):bi(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ji(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n,r,a={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}function Ni(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function ki(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Si(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Ei(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Si(Object(n),!0).forEach((function(t){ki(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Si(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Ci(e){return function t(){for(var n=this,r=arguments.length,a=new Array(r),o=0;o<r;o++)a[o]=arguments[o];return a.length>=e.length?e.apply(this,a):function(){for(var e=arguments.length,r=new Array(e),o=0;o<e;o++)r[o]=arguments[o];return t.apply(n,[].concat(a,r))}}}function Oi(e){return{}.toString.call(e).includes("Object")}function Pi(e){return"function"===typeof e}var _i=Ci((function(e,t){throw new Error(e[t]||e.default)}))({initialIsRequired:"initial state is required",initialType:"initial state should be an object",initialContent:"initial state shouldn't be an empty object",handlerType:"handler should be an object or a function",handlersType:"all handlers should be a functions",selectorType:"selector should be a function",changeType:"provided value of changes should be an object",changeField:'it seams you want to change a field in the state which is not specified in the "initial" state',default:"an unknown error accured in `state-local` package"}),Ri={changes:function(e,t){return Oi(t)||_i("changeType"),Object.keys(t).some((function(t){return n=e,r=t,!Object.prototype.hasOwnProperty.call(n,r);var n,r}))&&_i("changeField"),t},selector:function(e){Pi(e)||_i("selectorType")},handler:function(e){Pi(e)||Oi(e)||_i("handlerType"),Oi(e)&&Object.values(e).some((function(e){return!Pi(e)}))&&_i("handlersType")},initial:function(e){var t;e||_i("initialIsRequired"),Oi(e)||_i("initialType"),t=e,Object.keys(t).length||_i("initialContent")}};function Li(e,t){return Pi(t)?t(e.current):t}function Ti(e,t){return e.current=Ei(Ei({},e.current),t),t}function Ai(e,t,n){return Pi(t)?t(e.current):Object.keys(n).forEach((function(n){var r;return null===(r=t[n])||void 0===r?void 0:r.call(t,e.current[n])})),n}var Mi={create:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};Ri.initial(e),Ri.handler(t);var n={current:e},r=Ci(Ai)(n,t),a=Ci(Ti)(n),o=Ci(Ri.changes)(e),l=Ci(Li)(n);return[function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(e){return e};return Ri.selector(e),e(n.current)},function(e){!function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){return t.reduceRight((function(e,t){return t(e)}),e)}}(r,a,o,l)(e)}]}};const Ii=Mi;const Di={paths:{vs:"https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs"}};const zi=function(e){return function t(){for(var n=this,r=arguments.length,a=new Array(r),o=0;o<r;o++)a[o]=arguments[o];return a.length>=e.length?e.apply(this,a):function(){for(var e=arguments.length,r=new Array(e),o=0;o<e;o++)r[o]=arguments[o];return t.apply(n,[].concat(a,r))}}};const Fi=function(e){return{}.toString.call(e).includes("Object")};var Ui={configIsRequired:"the configuration object is required",configType:"the configuration object should be an object",default:"an unknown error accured in `@monaco-editor/loader` package",deprecation:"Deprecation warning!\n    You are using deprecated way of configuration.\n\n    Instead of using\n      monaco.config({ urls: { monacoBase: '...' } })\n    use\n      monaco.config({ paths: { vs: '...' } })\n\n    For more please check the link https://github.com/suren-atoyan/monaco-loader#config\n  "},Bi=zi((function(e,t){throw new Error(e[t]||e.default)}))(Ui),Wi={config:function(e){return e||Bi("configIsRequired"),Fi(e)||Bi("configType"),e.urls?(console.warn(Ui.deprecation),{paths:{vs:e.urls.monacoBase}}):e}};const Vi=Wi;const $i=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){return t.reduceRight((function(e,t){return t(e)}),e)}};const qi=function e(t,n){return Object.keys(n).forEach((function(r){n[r]instanceof Object&&t[r]&&Object.assign(n[r],e(t[r],n[r]))})),wi(wi({},t),n)};var Hi={type:"cancelation",msg:"operation is manually canceled"};const Qi=function(e){var t=!1,n=new Promise((function(n,r){e.then((function(e){return t?r(Hi):n(e)})),e.catch(r)}));return n.cancel=function(){return t=!0},n};var Ki=function(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"===typeof Symbol||!(Symbol.iterator in Object(e)))return;var n=[],r=!0,a=!1,o=void 0;try{for(var l,i=e[Symbol.iterator]();!(r=(l=i.next()).done)&&(n.push(l.value),!t||n.length!==t);r=!0);}catch(s){a=!0,o=s}finally{try{r||null==i.return||i.return()}finally{if(a)throw o}}return n}(e,t)||function(e,t){if(!e)return;if("string"===typeof e)return Ni(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Ni(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(Ii.create({config:Di,isInitialized:!1,resolve:null,reject:null,monaco:null}),2),Ji=Ki[0],Xi=Ki[1];function Zi(e){return document.body.appendChild(e)}function Yi(e){var t=Ji((function(e){return{config:e.config,reject:e.reject}})),n=function(e){var t=document.createElement("script");return e&&(t.src=e),t}("".concat(t.config.paths.vs,"/loader.js"));return n.onload=function(){return e()},n.onerror=t.reject,n}function Gi(){var e=Ji((function(e){return{config:e.config,resolve:e.resolve,reject:e.reject}})),t=window.require;t.config(e.config),t(["vs/editor/editor.main"],(function(t){es(t),e.resolve(t)}),(function(t){e.reject(t)}))}function es(e){Ji().monaco||Xi({monaco:e})}var ts=new Promise((function(e,t){return Xi({resolve:e,reject:t})})),ns={config:function(e){var t=Vi.config(e),n=t.monaco,r=ji(t,["monaco"]);Xi((function(e){return{config:qi(e.config,r),monaco:n}}))},init:function(){var e=Ji((function(e){return{monaco:e.monaco,isInitialized:e.isInitialized,resolve:e.resolve}}));if(!e.isInitialized){if(Xi({isInitialized:!0}),e.monaco)return e.resolve(e.monaco),Qi(ts);if(window.monaco&&window.monaco.editor)return es(window.monaco),e.resolve(window.monaco),Qi(ts);$i(Zi,Yi)(Gi)}return Qi(ts)},__getMonacoInstance:function(){return Ji((function(e){return e.monaco}))}};const rs=ns;var as={wrapper:{display:"flex",position:"relative",textAlign:"initial"},fullWidth:{width:"100%"},hide:{display:"none"}},os={container:{display:"flex",height:"100%",width:"100%",justifyContent:"center",alignItems:"center"}};var ls=function(e){let{children:t}=e;return a.createElement("div",{style:os.container},t)},is=ls;var ss=function(e){let{width:t,height:n,isEditorReady:r,loading:o,_ref:l,className:i,wrapperProps:s}=e;return a.createElement("section",lt({style:lt(lt({},as.wrapper),{},{width:t,height:n})},s),!r&&a.createElement(is,null,o),a.createElement("div",{ref:l,style:lt(lt({},as.fullWidth),!r&&as.hide),className:i}))},us=(0,a.memo)(ss);var cs=function(e){(0,a.useEffect)(e,[])};var ds=function(e,t){let n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],r=(0,a.useRef)(!0);(0,a.useEffect)(r.current||!n?()=>{r.current=!1}:e,t)};function fs(){}function ps(e,t,n,r){return function(e,t){return e.editor.getModel(hs(e,t))}(e,r)||function(e,t,n,r){return e.editor.createModel(t,n,r?hs(e,r):void 0)}(e,t,n,r)}function hs(e,t){return e.Uri.parse(t)}var ms=function(e){let{original:t,modified:n,language:r,originalLanguage:o,modifiedLanguage:l,originalModelPath:i,modifiedModelPath:s,keepCurrentOriginalModel:u=!1,keepCurrentModifiedModel:c=!1,theme:d="light",loading:f="Loading...",options:p={},height:h="100%",width:m="100%",className:g,wrapperProps:v={},beforeMount:y=fs,onMount:x=fs}=e,[b,w]=(0,a.useState)(!1),[j,N]=(0,a.useState)(!0),k=(0,a.useRef)(null),S=(0,a.useRef)(null),E=(0,a.useRef)(null),C=(0,a.useRef)(x),O=(0,a.useRef)(y),P=(0,a.useRef)(!1);cs((()=>{let e=rs.init();return e.then((e=>(S.current=e)&&N(!1))).catch((e=>"cancelation"!==(null===e||void 0===e?void 0:e.type)&&console.error("Monaco initialization: error:",e))),()=>k.current?function(){var e,t,n,r;let a=null===(e=k.current)||void 0===e?void 0:e.getModel();u||null!==a&&void 0!==a&&null!==(t=a.original)&&void 0!==t&&t.dispose(),c||null!==a&&void 0!==a&&null!==(n=a.modified)&&void 0!==n&&n.dispose(),null===(r=k.current)||void 0===r||r.dispose()}():e.cancel()})),ds((()=>{if(k.current&&S.current){let e=k.current.getOriginalEditor(),n=ps(S.current,t||"",o||r||"text",i||"");n!==e.getModel()&&e.setModel(n)}}),[i],b),ds((()=>{if(k.current&&S.current){let e=k.current.getModifiedEditor(),t=ps(S.current,n||"",l||r||"text",s||"");t!==e.getModel()&&e.setModel(t)}}),[s],b),ds((()=>{let e=k.current.getModifiedEditor();e.getOption(S.current.editor.EditorOption.readOnly)?e.setValue(n||""):n!==e.getValue()&&(e.executeEdits("",[{range:e.getModel().getFullModelRange(),text:n||"",forceMoveMarkers:!0}]),e.pushUndoStop())}),[n],b),ds((()=>{var e;null===(e=k.current)||void 0===e||null===(e=e.getModel())||void 0===e||e.original.setValue(t||"")}),[t],b),ds((()=>{let{original:e,modified:t}=k.current.getModel();S.current.editor.setModelLanguage(e,o||r||"text"),S.current.editor.setModelLanguage(t,l||r||"text")}),[r,o,l],b),ds((()=>{var e;null===(e=S.current)||void 0===e||e.editor.setTheme(d)}),[d],b),ds((()=>{var e;null===(e=k.current)||void 0===e||e.updateOptions(p)}),[p],b);let _=(0,a.useCallback)((()=>{var e;if(!S.current)return;O.current(S.current);let a=ps(S.current,t||"",o||r||"text",i||""),u=ps(S.current,n||"",l||r||"text",s||"");null===(e=k.current)||void 0===e||e.setModel({original:a,modified:u})}),[r,n,l,t,o,i,s]),R=(0,a.useCallback)((()=>{var e;!P.current&&E.current&&(k.current=S.current.editor.createDiffEditor(E.current,lt({automaticLayout:!0},p)),_(),null!==(e=S.current)&&void 0!==e&&e.editor.setTheme(d),w(!0),P.current=!0)}),[p,d,_]);return(0,a.useEffect)((()=>{b&&C.current(k.current,S.current)}),[b]),(0,a.useEffect)((()=>{!j&&!b&&R()}),[j,b,R]),a.createElement(us,{width:m,height:h,isEditorReady:b,loading:f,_ref:E,className:g,wrapperProps:v})};(0,a.memo)(ms);var gs=function(e){let t=(0,a.useRef)();return(0,a.useEffect)((()=>{t.current=e}),[e]),t.current},vs=new Map;var ys=function(e){let{defaultValue:t,defaultLanguage:n,defaultPath:r,value:o,language:l,path:i,theme:s="light",line:u,loading:c="Loading...",options:d={},overrideServices:f={},saveViewState:p=!0,keepCurrentModel:h=!1,width:m="100%",height:g="100%",className:v,wrapperProps:y={},beforeMount:x=fs,onMount:b=fs,onChange:w,onValidate:j=fs}=e,[N,k]=(0,a.useState)(!1),[S,E]=(0,a.useState)(!0),C=(0,a.useRef)(null),O=(0,a.useRef)(null),P=(0,a.useRef)(null),_=(0,a.useRef)(b),R=(0,a.useRef)(x),L=(0,a.useRef)(),T=(0,a.useRef)(o),A=gs(i),M=(0,a.useRef)(!1),I=(0,a.useRef)(!1);cs((()=>{let e=rs.init();return e.then((e=>(C.current=e)&&E(!1))).catch((e=>"cancelation"!==(null===e||void 0===e?void 0:e.type)&&console.error("Monaco initialization: error:",e))),()=>O.current?function(){var e,t;null!==(e=L.current)&&void 0!==e&&e.dispose(),h?p&&vs.set(i,O.current.saveViewState()):null===(t=O.current.getModel())||void 0===t||t.dispose(),O.current.dispose()}():e.cancel()})),ds((()=>{var e,a,s,u;let c=ps(C.current,t||o||"",n||l||"",i||r||"");c!==(null===(e=O.current)||void 0===e?void 0:e.getModel())&&(p&&vs.set(A,null===(a=O.current)||void 0===a?void 0:a.saveViewState()),null!==(s=O.current)&&void 0!==s&&s.setModel(c),p&&(null===(u=O.current)||void 0===u||u.restoreViewState(vs.get(i))))}),[i],N),ds((()=>{var e;null===(e=O.current)||void 0===e||e.updateOptions(d)}),[d],N),ds((()=>{!O.current||void 0===o||(O.current.getOption(C.current.editor.EditorOption.readOnly)?O.current.setValue(o):o!==O.current.getValue()&&(I.current=!0,O.current.executeEdits("",[{range:O.current.getModel().getFullModelRange(),text:o,forceMoveMarkers:!0}]),O.current.pushUndoStop(),I.current=!1))}),[o],N),ds((()=>{var e,t;let n=null===(e=O.current)||void 0===e?void 0:e.getModel();n&&l&&(null===(t=C.current)||void 0===t||t.editor.setModelLanguage(n,l))}),[l],N),ds((()=>{var e;void 0!==u&&(null===(e=O.current)||void 0===e||e.revealLine(u))}),[u],N),ds((()=>{var e;null===(e=C.current)||void 0===e||e.editor.setTheme(s)}),[s],N);let D=(0,a.useCallback)((()=>{if(P.current&&C.current&&!M.current){var e;R.current(C.current);let a=i||r,c=ps(C.current,o||t||"",n||l||"",a||"");O.current=null===(e=C.current)||void 0===e?void 0:e.editor.create(P.current,lt({model:c,automaticLayout:!0},d),f),p&&O.current.restoreViewState(vs.get(a)),C.current.editor.setTheme(s),void 0!==u&&O.current.revealLine(u),k(!0),M.current=!0}}),[t,n,r,o,l,i,d,f,p,s,u]);return(0,a.useEffect)((()=>{N&&_.current(O.current,C.current)}),[N]),(0,a.useEffect)((()=>{!S&&!N&&D()}),[S,N,D]),T.current=o,(0,a.useEffect)((()=>{var e,t;N&&w&&(null!==(e=L.current)&&void 0!==e&&e.dispose(),L.current=null===(t=O.current)||void 0===t?void 0:t.onDidChangeModelContent((e=>{I.current||w(O.current.getValue(),e)})))}),[N,w]),(0,a.useEffect)((()=>{if(N){let e=C.current.editor.onDidChangeMarkers((e=>{var t;let n=null===(t=O.current.getModel())||void 0===t?void 0:t.uri;if(n&&e.find((e=>e.path===n.path))){let e=C.current.editor.getModelMarkers({resource:n});null===j||void 0===j||j(e)}}));return()=>{null===e||void 0===e||e.dispose()}}return()=>{}}),[N,j]),a.createElement(us,{width:m,height:g,isEditorReady:N,loading:c,_ref:P,className:v,wrapperProps:y})},xs=(0,a.memo)(ys);const bs=()=>{const e=R(),{configs:t,nodes:n,selectedConfig:r,loading:o,error:l}=j((e=>e.envoy)),[i,s]=(0,a.useState)(!1),[u,c]=(0,a.useState)({node_id:"",cluster_name:"",config:"",version:"1.0.0"});(0,a.useEffect)((()=>{e(li()),e(ii())}),[e]);return(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsxs)("div",{className:"flex items-center justify-between",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h1",{className:"text-2xl font-bold text-white",children:"Envoy Configuration"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Manage Envoy proxy configurations and deployments"})]}),(0,it.jsxs)("button",{onClick:()=>s(!0),className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500",children:[(0,it.jsx)(mi,{className:"h-4 w-4 mr-2"}),"New Config"]})]}),(0,it.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white mb-4",children:"Configurations"}),o?(0,it.jsx)("div",{className:"flex items-center justify-center h-32",children:(0,it.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500"})}):(0,it.jsxs)("div",{className:"space-y-3",children:[t.map((t=>(0,it.jsx)("div",{className:"border rounded-lg p-4 cursor-pointer transition-colors ".concat((null===r||void 0===r?void 0:r.id)===t.id?"border-cyan-500 bg-cyan-500/10":"border-gray-600 hover:bg-gray-700"),onClick:()=>e(di(t)),children:(0,it.jsxs)("div",{className:"flex items-start justify-between",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h4",{className:"text-lg font-medium text-white",children:t.cluster_name}),(0,it.jsxs)("p",{className:"mt-1 text-sm text-gray-400",children:["Node: ",t.node_id]}),(0,it.jsxs)("p",{className:"mt-1 text-xs text-gray-500",children:["Version: ",t.version]})]}),(0,it.jsx)(yi,{className:"h-6 w-6 text-gray-400"})]})},t.id))),0===t.length&&(0,it.jsxs)("div",{className:"text-center py-8",children:[(0,it.jsx)(St,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,it.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-300",children:"No configurations"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Create your first Envoy configuration."})]})]})]})}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white mb-4",children:"Configuration Editor"}),r?(0,it.jsxs)("div",{className:"space-y-4",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Cluster Name"}),(0,it.jsx)("input",{type:"text",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:r.cluster_name,readOnly:!0})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Node ID"}),(0,it.jsx)("input",{type:"text",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:r.node_id,readOnly:!0})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Configuration (YAML)"}),(0,it.jsx)("div",{className:"mt-1 border border-gray-600 rounded-md overflow-hidden",children:(0,it.jsx)(xs,{height:"300px",defaultLanguage:"yaml",value:r.config,theme:"vs-dark",options:{readOnly:!0,minimap:{enabled:!1},scrollBeyondLastLine:!1,fontSize:14,fontFamily:"JetBrains Mono, monospace",lineNumbers:"on",folding:!0,wordWrap:"on"}})})]})]}):(0,it.jsxs)("div",{className:"text-center py-8",children:[(0,it.jsx)(yi,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,it.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-300",children:"Select a configuration"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Choose a configuration from the list to view and edit it."})]})]})})]}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white mb-4",children:"Connected Envoy Nodes"}),0===n.length?(0,it.jsxs)("div",{className:"text-center py-8",children:[(0,it.jsx)(St,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,it.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-300",children:"No nodes connected"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Envoy nodes will appear here when they connect to the control plane."})]}):(0,it.jsx)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3",children:n.map((e=>(0,it.jsxs)("div",{className:"border border-gray-600 rounded-lg p-4",children:[(0,it.jsxs)("div",{className:"flex items-center justify-between",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h4",{className:"text-lg font-medium text-white",children:e.id}),(0,it.jsxs)("p",{className:"text-sm text-gray-400",children:["Cluster: ",e.cluster]}),(0,it.jsxs)("p",{className:"text-xs text-gray-500",children:["Version: ",e.version]})]}),(0,it.jsx)("div",{className:"w-3 h-3 rounded-full ".concat("connected"===e.status?"bg-green-400":"bg-red-400")})]}),(0,it.jsxs)("p",{className:"mt-2 text-xs text-gray-500",children:["Last seen: ",new Date(e.last_seen).toLocaleString()]})]},e.id)))})]})}),i&&(0,it.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,it.jsx)("div",{className:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-gray-800",children:(0,it.jsxs)("div",{className:"mt-3",children:[(0,it.jsx)("h3",{className:"text-lg font-medium text-white mb-4",children:"Create New Configuration"}),(0,it.jsxs)("div",{className:"space-y-4",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Node ID"}),(0,it.jsx)("input",{type:"text",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:u.node_id,onChange:e=>c(lt(lt({},u),{},{node_id:e.target.value}))})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Cluster Name"}),(0,it.jsx)("input",{type:"text",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:u.cluster_name,onChange:e=>c(lt(lt({},u),{},{cluster_name:e.target.value}))})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Configuration"}),(0,it.jsx)("div",{className:"mt-1 border border-gray-600 rounded-md overflow-hidden",children:(0,it.jsx)(xs,{height:"200px",defaultLanguage:"yaml",value:u.config,onChange:e=>c(lt(lt({},u),{},{config:e||""})),theme:"vs-dark",options:{minimap:{enabled:!1},scrollBeyondLastLine:!1,fontSize:14,fontFamily:"JetBrains Mono, monospace",lineNumbers:"on",folding:!0,wordWrap:"on"}})})]})]}),(0,it.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,it.jsx)("button",{onClick:()=>s(!1),className:"px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700",children:"Cancel"}),(0,it.jsx)("button",{onClick:()=>{e(si(u)),c({node_id:"",cluster_name:"",config:"",version:"1.0.0"}),s(!1)},className:"px-4 py-2 bg-cyan-600 text-white rounded-md hover:bg-cyan-700",children:"Create"})]})]})})})]})},ws=zr("autoscaler/fetchStatus",(async()=>(await hl.get("/v1/autoscaler/status")).data)),js=zr("autoscaler/updateConfig",(async e=>(await hl.put("/v1/autoscaler/config",e)).data)),Ns=zr("autoscaler/fetchEvents",(async()=>(await hl.get("/v1/autoscaler/events")).data.events)),ks=zr("autoscaler/fetchMetrics",(async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"1h";return(await hl.get("/v1/autoscaler/metrics?range=".concat(e))).data})),Ss=Lr({name:"autoscaler",initialState:{status:null,events:[],loading:!1,error:null,metrics:{cpu_usage:[],memory_usage:[],replica_count:[]}},reducers:{clearEvents:e=>{e.events=[]},addEvent:(e,t)=>{e.events.unshift(t.payload),e.events.length>100&&(e.events=e.events.slice(0,100))}},extraReducers:e=>{e.addCase(ws.pending,(e=>{e.loading=!0,e.error=null})).addCase(ws.fulfilled,((e,t)=>{e.loading=!1,e.status=t.payload})).addCase(ws.rejected,((e,t)=>{e.loading=!1,e.error=t.error.message||"Failed to fetch autoscaler status"})).addCase(js.pending,(e=>{e.loading=!0,e.error=null})).addCase(js.fulfilled,((e,t)=>{e.loading=!1,e.status=lt(lt({},e.status),t.payload)})).addCase(js.rejected,((e,t)=>{e.loading=!1,e.error=t.error.message||"Failed to update autoscaler config"})).addCase(Ns.fulfilled,((e,t)=>{e.events=t.payload})).addCase(ks.fulfilled,((e,t)=>{e.metrics=t.payload}))}}),{clearEvents:Es,addEvent:Cs}=Ss.actions,Os=Ss.reducer,Ps=["title","titleId"];function _s(e,t){let{title:n,titleId:r}=e,o=pt(e,Ps);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},o),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-15 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a2.25 2.25 0 0 0 2.25-2.25V6.75a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 6.75v10.5a2.25 2.25 0 0 0 2.25 2.25Zm.75-12h9v9h-9v-9Z"}))}const Rs=a.forwardRef(_s),Ls=["title","titleId"];function Ts(e,t){let{title:n,titleId:r}=e,o=pt(e,Ls);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},o),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 6 9 12.75l4.286-4.286a11.948 11.948 0 0 1 4.306 6.43l.776 2.898m0 0 3.182-5.511m-3.182 5.51-5.511-3.181"}))}const As=a.forwardRef(Ts),Ms=()=>{const e=R(),{status:t,events:n,loading:r,error:o,metrics:l}=j((e=>e.autoscaler));(0,a.useEffect)((()=>{e(ws()),e(ks("1h"))}),[e]);return(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h1",{className:"text-2xl font-bold text-white",children:"Autoscaler Dashboard"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Monitor and configure automatic scaling for your applications"})]}),(0,it.jsxs)("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4",children:[(0,it.jsx)("div",{className:"bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,it.jsx)("div",{className:"p-5",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(Cl,{className:"h-6 w-6 text-blue-400"})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400 truncate",children:"Current Replicas"}),(0,it.jsx)("dd",{className:"text-lg font-medium text-white",children:(null===t||void 0===t?void 0:t.current_replicas)||0})]})})]})})}),(0,it.jsx)("div",{className:"bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,it.jsx)("div",{className:"p-5",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(Ot,{className:"h-6 w-6 text-green-400"})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400 truncate",children:"Desired Replicas"}),(0,it.jsx)("dd",{className:"text-lg font-medium text-white",children:(null===t||void 0===t?void 0:t.desired_replicas)||0})]})})]})})}),(0,it.jsx)("div",{className:"bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,it.jsx)("div",{className:"p-5",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(Rs,{className:"h-6 w-6 text-yellow-400"})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400 truncate",children:"CPU Utilization"}),(0,it.jsxs)("dd",{className:"text-lg font-medium text-white",children:[(null===t||void 0===t?void 0:t.current_cpu_utilization)||0,"%"]})]})})]})})}),(0,it.jsx)("div",{className:"bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,it.jsx)("div",{className:"p-5",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(null!==t&&void 0!==t&&t.enabled?"bg-green-400":"bg-red-400")})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400 truncate",children:"Status"}),(0,it.jsx)("dd",{className:"text-lg font-medium text-white",children:null!==t&&void 0!==t&&t.enabled?"Enabled":"Disabled"})]})})]})})})]}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white mb-4",children:"Autoscaler Configuration"}),t&&(0,it.jsxs)("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Min Replicas"}),(0,it.jsx)("div",{className:"mt-1",children:(0,it.jsx)("input",{type:"number",className:"block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:t.min_replicas,readOnly:!0})})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Max Replicas"}),(0,it.jsx)("div",{className:"mt-1",children:(0,it.jsx)("input",{type:"number",className:"block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:t.max_replicas,readOnly:!0})})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Target CPU Utilization (%)"}),(0,it.jsx)("div",{className:"mt-1",children:(0,it.jsx)("input",{type:"number",className:"block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:t.target_cpu_utilization,readOnly:!0})})]}),(0,it.jsx)("div",{className:"flex items-end",children:(0,it.jsxs)("button",{onClick:()=>{t&&e(js({enabled:!t.enabled}))},disabled:r,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 ".concat(t.enabled?"bg-red-600 hover:bg-red-700 focus:ring-red-500":"bg-green-600 hover:bg-green-700 focus:ring-green-500"),children:[t.enabled?"Disable":"Enable"," Autoscaler"]})})]}),o&&(0,it.jsx)("div",{className:"mt-4 bg-red-50 border border-red-200 rounded-md p-4",children:(0,it.jsx)("div",{className:"text-sm text-red-700",children:o})})]})}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white mb-4",children:"Recent Scaling Events"}),0===n.length?(0,it.jsxs)("div",{className:"text-center py-8",children:[(0,it.jsx)(Ot,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,it.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-300",children:"No scaling events"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Scaling events will appear here when they occur."})]}):(0,it.jsx)("div",{className:"space-y-4",children:n.slice(0,10).map(((e,t)=>(0,it.jsx)("div",{className:"border border-gray-600 rounded-lg p-4",children:(0,it.jsxs)("div",{className:"flex items-start justify-between",children:[(0,it.jsxs)("div",{className:"flex items-center space-x-3",children:[e.to_replicas>e.from_replicas?(0,it.jsx)(Ot,{className:"h-5 w-5 text-green-400"}):(0,it.jsx)(As,{className:"h-5 w-5 text-red-400"}),(0,it.jsxs)("div",{children:[(0,it.jsxs)("p",{className:"text-sm font-medium text-white",children:["Scaled from ",e.from_replicas," to ",e.to_replicas," replicas"]}),(0,it.jsx)("p",{className:"text-sm text-gray-400",children:e.reason}),(0,it.jsx)("p",{className:"text-xs text-gray-500",children:e.message})]})]}),(0,it.jsx)("span",{className:"text-xs text-gray-400",children:new Date(e.timestamp).toLocaleString()})]})},t)))})]})})]})},Is=zr("audit/fetchEvents",(async e=>(await hl.post("/v1/audit/query",e)).data)),Ds=Lr({name:"audit",initialState:{events:[],loading:!1,error:null,totalCount:0,query:{}},reducers:{setQuery:(e,t)=>{e.query=t.payload},clearEvents:e=>{e.events=[],e.totalCount=0}},extraReducers:e=>{e.addCase(Is.pending,(e=>{e.loading=!0,e.error=null})).addCase(Is.fulfilled,((e,t)=>{e.loading=!1,e.events=t.payload.events,e.totalCount=t.payload.total_count})).addCase(Is.rejected,((e,t)=>{e.loading=!1,e.error=t.error.message||"Failed to fetch audit events"}))}}),{setQuery:zs,clearEvents:Fs}=Ds.actions,Us=Ds.reducer,Bs=()=>{const e=R(),{events:t,loading:n,error:r,totalCount:o,query:l}=j((e=>e.audit)),[i,s]=(0,a.useState)({user_id:"",action:"",resource:"",start_time:"",end_time:""});(0,a.useEffect)((()=>{e(Is({limit:50}))}),[e]);return(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h1",{className:"text-2xl font-bold text-white",children:"Audit Trail"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Tamper-proof audit logs powered by ImmuDB"})]}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white mb-4",children:"Filters"}),(0,it.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-3",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"User ID"}),(0,it.jsx)("input",{type:"text",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:i.user_id,onChange:e=>s(lt(lt({},i),{},{user_id:e.target.value})),placeholder:"Filter by user..."})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Action"}),(0,it.jsx)("input",{type:"text",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:i.action,onChange:e=>s(lt(lt({},i),{},{action:e.target.value})),placeholder:"Filter by action..."})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Resource"}),(0,it.jsx)("input",{type:"text",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:i.resource,onChange:e=>s(lt(lt({},i),{},{resource:e.target.value})),placeholder:"Filter by resource..."})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Start Date"}),(0,it.jsx)("input",{type:"datetime-local",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:i.start_time,onChange:e=>s(lt(lt({},i),{},{start_time:e.target.value}))})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"End Date"}),(0,it.jsx)("input",{type:"datetime-local",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:i.end_time,onChange:e=>s(lt(lt({},i),{},{end_time:e.target.value}))})]}),(0,it.jsx)("div",{className:"flex items-end",children:(0,it.jsxs)("button",{onClick:()=>{const t=lt(lt({},i),{},{limit:50});e(zs(t)),e(Is(t))},disabled:n,className:"w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 disabled:opacity-50",children:[(0,it.jsx)(xt,{className:"h-4 w-4 mr-2"}),"Search"]})})]})]})}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white",children:"Audit Events"}),(0,it.jsxs)("span",{className:"text-sm text-gray-400",children:[o," events found"]})]}),r&&(0,it.jsx)("div",{className:"mb-4 bg-red-50 border border-red-200 rounded-md p-4",children:(0,it.jsx)("div",{className:"text-sm text-red-700",children:r})}),n?(0,it.jsx)("div",{className:"flex items-center justify-center h-32",children:(0,it.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500"})}):(0,it.jsx)("div",{className:"space-y-4",children:0===t.length?(0,it.jsxs)("div",{className:"text-center py-12",children:[(0,it.jsx)(Rt,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,it.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-300",children:"No audit events found"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Try adjusting your search criteria."})]}):t.map((e=>{return(0,it.jsx)("div",{className:"border border-gray-600 rounded-lg p-4",children:(0,it.jsxs)("div",{className:"flex items-start justify-between",children:[(0,it.jsxs)("div",{className:"flex-1",children:[(0,it.jsxs)("div",{className:"flex items-center space-x-2",children:[e.success?(0,it.jsx)(Dl,{className:"h-5 w-5 text-green-400"}):(0,it.jsx)(ti,{className:"h-5 w-5 text-red-400"}),(0,it.jsx)("span",{className:"font-medium ".concat((t=e.action,t.includes("create")||t.includes("add")?"text-green-400":t.includes("delete")||t.includes("remove")?"text-red-400":t.includes("update")||t.includes("modify")?"text-yellow-400":"text-blue-400")),children:e.action}),(0,it.jsx)("span",{className:"text-gray-400",children:"on"}),(0,it.jsx)("span",{className:"text-white",children:e.resource})]}),(0,it.jsxs)("div",{className:"mt-2 text-sm text-gray-400",children:[(0,it.jsxs)("p",{children:["User: ",e.user_id]}),(0,it.jsxs)("p",{children:["Resource ID: ",e.resource_id]}),(0,it.jsxs)("p",{children:["IP: ",e.ip_address]}),e.error_msg&&(0,it.jsxs)("p",{className:"text-red-400",children:["Error: ",e.error_msg]})]}),Object.keys(e.details).length>0&&(0,it.jsx)("div",{className:"mt-3",children:(0,it.jsxs)("details",{className:"group",children:[(0,it.jsx)("summary",{className:"cursor-pointer text-sm text-cyan-400 hover:text-cyan-300",children:"View Details"}),(0,it.jsx)("pre",{className:"mt-2 text-xs text-gray-400 bg-gray-700 p-2 rounded overflow-x-auto",children:JSON.stringify(e.details,null,2)})]})})]}),(0,it.jsxs)("div",{className:"text-right text-sm text-gray-400",children:[(0,it.jsx)("p",{children:new Date(e.timestamp).toLocaleDateString()}),(0,it.jsx)("p",{children:new Date(e.timestamp).toLocaleTimeString()})]})]})},e.id);var t}))})]})})]})},Ws=zr("database/fetchStats",(async()=>(await hl.get("/v1/database/stats")).data)),Vs=zr("database/executeQuery",(async e=>(await hl.post("/v1/database/query",{query:e})).data)),$s=Lr({name:"database",initialState:{stats:null,loading:!1,error:null,queryResult:null,queryLoading:!1},reducers:{clearQueryResult:e=>{e.queryResult=null}},extraReducers:e=>{e.addCase(Ws.pending,(e=>{e.loading=!0,e.error=null})).addCase(Ws.fulfilled,((e,t)=>{e.loading=!1,e.stats=t.payload})).addCase(Ws.rejected,((e,t)=>{e.loading=!1,e.error=t.error.message||"Failed to fetch database stats"})).addCase(Vs.pending,(e=>{e.queryLoading=!0,e.error=null})).addCase(Vs.fulfilled,((e,t)=>{e.queryLoading=!1,e.queryResult=t.payload})).addCase(Vs.rejected,((e,t)=>{e.queryLoading=!1,e.error=t.error.message||"Query execution failed"}))}}),{clearQueryResult:qs}=$s.actions,Hs=$s.reducer,Qs=["title","titleId"];function Ks(e,t){let{title:n,titleId:r}=e,o=pt(e,Qs);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},o),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))}const Js=a.forwardRef(Ks),Xs=()=>{var e,t,n;const r=R(),{stats:o,loading:l,error:i,queryResult:s,queryLoading:u}=j((e=>e.database)),[c,d]=(0,a.useState)("");(0,a.useEffect)((()=>{r(Ws())}),[r]);return(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h1",{className:"text-2xl font-bold text-white",children:"Database Administration"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Monitor database health and execute queries"})]}),(0,it.jsxs)("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4",children:[(0,it.jsx)("div",{className:"bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,it.jsx)("div",{className:"p-5",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(At,{className:"h-6 w-6 text-blue-400"})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400 truncate",children:"Total Resources"}),(0,it.jsx)("dd",{className:"text-lg font-medium text-white",children:(null===o||void 0===o||null===(e=o.total_resources)||void 0===e?void 0:e.toLocaleString())||0})]})})]})})}),(0,it.jsx)("div",{className:"bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,it.jsx)("div",{className:"p-5",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(At,{className:"h-6 w-6 text-green-400"})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400 truncate",children:"Workflows"}),(0,it.jsx)("dd",{className:"text-lg font-medium text-white",children:(null===o||void 0===o||null===(t=o.total_workflows)||void 0===t?void 0:t.toLocaleString())||0})]})})]})})}),(0,it.jsx)("div",{className:"bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,it.jsx)("div",{className:"p-5",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(At,{className:"h-6 w-6 text-purple-400"})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400 truncate",children:"Envoy Configs"}),(0,it.jsx)("dd",{className:"text-lg font-medium text-white",children:(null===o||void 0===o||null===(n=o.total_envoy_configs)||void 0===n?void 0:n.toLocaleString())||0})]})})]})})}),(0,it.jsx)("div",{className:"bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,it.jsx)("div",{className:"p-5",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(At,{className:"h-6 w-6 text-yellow-400"})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400 truncate",children:"Cache Hit Rate"}),(0,it.jsx)("dd",{className:"text-lg font-medium text-white",children:null!==o&&void 0!==o&&o.cache_hit_rate?"".concat(o.cache_hit_rate,"%"):"N/A"})]})})]})})})]}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white mb-4",children:"Database Information"}),l?(0,it.jsx)("div",{className:"flex items-center justify-center h-32",children:(0,it.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500"})}):(0,it.jsxs)("dl",{className:"grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400",children:"Database Size"}),(0,it.jsx)("dd",{className:"mt-1 text-sm text-white",children:(null===o||void 0===o?void 0:o.database_size)||"Unknown"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400",children:"Cache Size"}),(0,it.jsx)("dd",{className:"mt-1 text-sm text-white",children:(null===o||void 0===o?void 0:o.cache_size)||"Unknown"})]})]})]})}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white mb-4",children:"SQL Query Interface"}),(0,it.jsxs)("div",{className:"space-y-4",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"SQL Query"}),(0,it.jsx)("textarea",{className:"block w-full h-32 px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white font-mono text-sm focus:outline-none focus:ring-2 focus:ring-cyan-500",value:c,onChange:e=>d(e.target.value),placeholder:"Enter your SQL query here..."})]}),(0,it.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,it.jsxs)("button",{onClick:()=>{c.trim()&&r(Vs(c))},disabled:u||!c.trim(),className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 disabled:opacity-50",children:[(0,it.jsx)(ai,{className:"h-4 w-4 mr-2"}),u?"Executing...":"Execute Query"]}),s&&(0,it.jsxs)("button",{onClick:()=>{r(qs())},className:"inline-flex items-center px-4 py-2 border border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-300 bg-gray-700 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500",children:[(0,it.jsx)(Js,{className:"h-4 w-4 mr-2"}),"Clear Result"]})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Sample Queries"}),(0,it.jsx)("div",{className:"space-y-2",children:["SELECT * FROM resource_cache LIMIT 10","SELECT provider, COUNT(*) as count FROM resource_cache GROUP BY provider",'SELECT * FROM workflow_executions WHERE status = "running"',"SELECT * FROM envoy_configs ORDER BY updated_at DESC LIMIT 5"].map(((e,t)=>(0,it.jsx)("button",{onClick:()=>d(e),className:"block w-full text-left px-3 py-2 text-sm text-gray-300 bg-gray-700 hover:bg-gray-600 rounded border border-gray-600",children:e},t)))})]})]}),i&&(0,it.jsx)("div",{className:"mt-4 bg-red-50 border border-red-200 rounded-md p-4",children:(0,it.jsx)("div",{className:"text-sm text-red-700",children:i})}),s&&(0,it.jsxs)("div",{className:"mt-6",children:[(0,it.jsx)("h4",{className:"text-md font-medium text-white mb-3",children:"Query Results"}),(0,it.jsx)("div",{className:"bg-gray-700 rounded-md p-4 overflow-x-auto",children:(0,it.jsx)("pre",{className:"text-sm text-gray-300",children:JSON.stringify(s,null,2)})})]})]})})]})};function Zs(){const{isAuthenticated:e,isLoading:t}=ct();return t?(0,it.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center",children:(0,it.jsxs)("div",{className:"text-center",children:[(0,it.jsx)("div",{className:"animate-spin rounded-full h-16 w-16 border-b-2 border-white mx-auto mb-4"}),(0,it.jsx)("div",{className:"text-white text-lg",children:"Loading CAINuro Orchestrator..."})]})}):e?(0,it.jsx)("div",{className:"min-h-screen bg-theme-bg-container",children:(0,it.jsx)(Kt,{children:(0,it.jsxs)($e,{children:[(0,it.jsx)(We,{path:"/",element:(0,it.jsx)(dt,{children:(0,it.jsx)(Xt,{})})}),(0,it.jsx)(We,{path:"/dashboard",element:(0,it.jsx)(dt,{children:(0,it.jsx)(Xt,{})})}),(0,it.jsx)(We,{path:"/search",element:(0,it.jsx)(dt,{requiredPermission:{action:"READ",resource:"discovery"},children:(0,it.jsx)(Rl,{})})}),(0,it.jsx)(We,{path:"/discovery",element:(0,it.jsx)(dt,{requiredPermission:{action:"READ",resource:"discovery"},children:(0,it.jsx)(Bl,{})})}),(0,it.jsx)(We,{path:"/workflows",element:(0,it.jsx)(dt,{requiredPermission:{action:"EXECUTE",resource:"workflows"},children:(0,it.jsx)(oi,{})})}),(0,it.jsx)(We,{path:"/envoy",element:(0,it.jsx)(dt,{requiredPermission:{action:"READ",resource:"envoy"},children:(0,it.jsx)(bs,{})})}),(0,it.jsx)(We,{path:"/autoscaler",element:(0,it.jsx)(dt,{requiredPermission:{action:"READ",resource:"autoscaler"},children:(0,it.jsx)(Ms,{})})}),(0,it.jsx)(We,{path:"/audit",element:(0,it.jsx)(dt,{requiredPermission:{action:"READ",resource:"audit"},children:(0,it.jsx)(Bs,{})})}),(0,it.jsx)(We,{path:"/database",element:(0,it.jsx)(dt,{requiredPermission:{action:"READ",resource:"database"},children:(0,it.jsx)(Xs,{})})})]})})}):(0,it.jsx)(ft,{})}const Ys=function(){return(0,it.jsx)(ut,{children:(0,it.jsx)(Zs,{})})},Gs=function(e){var t,n=_r(),r=e||{},a=r.reducer,o=void 0===a?void 0:a,l=r.middleware,i=void 0===l?n():l,s=r.devTools,u=void 0===s||s,c=r.preloadedState,d=void 0===c?void 0:c,f=r.enhancers,p=void 0===f?void 0:f;if("function"===typeof o)t=o;else{if(!Sr(o))throw new Error('"reducer" is a required argument, and must be a function or an object of functions that can be passed to combineReducers');t=or(o)}var h=i;"function"===typeof h&&(h=h(n));var m=ir.apply(void 0,h),g=lr;u&&(g=kr(wr({trace:!1},"object"===typeof u&&u)));var v=new Or(m),y=v;return Array.isArray(p)?y=pr([m],p):"function"===typeof p&&(y=p(v)),ar(t,d,g.apply(void 0,y))}({reducer:{search:wl,workflow:Jl,envoy:fi,autoscaler:Os,audit:Us,database:Hs},middleware:e=>e({serializableCheck:{ignoredActions:["persist/PERSIST"]}})}),eu=e=>{e&&e instanceof Function&&n.e(488).then(n.bind(n,488)).then((t=>{let{getCLS:n,getFID:r,getFCP:a,getLCP:o,getTTFB:l}=t;n(e),r(e),a(e),o(e),l(e)}))};l.createRoot(document.getElementById("root")).render((0,it.jsx)(a.StrictMode,{children:(0,it.jsx)(C,{store:Gs,children:(0,it.jsx)(Xe,{children:(0,it.jsx)(Ys,{})})})})),eu()})();
//# sourceMappingURL=main.38999409.js.map