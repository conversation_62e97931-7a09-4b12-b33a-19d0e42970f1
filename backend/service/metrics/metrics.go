package metrics

import (
	"context"
	"fmt"
	"math"
	"math/rand"
	"time"

	"go.uber.org/zap"
)

// Service represents the metrics service
type Service struct {
	logger *zap.Logger
}

// DataPoint represents a single metric data point
type DataPoint struct {
	Timestamp int64   `json:"timestamp"`
	Value     float64 `json:"value"`
}

// Metric represents a metric with its data points
type Metric struct {
	Name       string            `json:"name"`
	Labels     map[string]string `json:"labels"`
	DataPoints []DataPoint       `json:"data_points"`
	Unit       string            `json:"unit"`
}

// Query represents a metrics query
type Query struct {
	Expression string `json:"expression"`
	StartTime  int64  `json:"start_time_ms"`
	EndTime    int64  `json:"end_time_ms"`
	Step       int64  `json:"step_ms"`
}

// QueryResult represents the result of a metrics query
type QueryResult struct {
	Query   string   `json:"query"`
	Metrics []Metric `json:"metrics"`
}

// New creates a new metrics service
func New(logger *zap.Logger) *Service {
	return &Service{
		logger: logger,
	}
}

// QueryMetrics executes metrics queries and returns results
func (s *Service) QueryMetrics(ctx context.Context, queries []Query) (map[string]QueryResult, error) {
	s.logger.Info("Executing metrics queries", zap.Int("count", len(queries)))

	results := make(map[string]QueryResult)

	for _, query := range queries {
		result, err := s.executeQuery(ctx, query)
		if err != nil {
			s.logger.Error("Failed to execute query", zap.String("expression", query.Expression), zap.Error(err))
			continue
		}
		results[query.Expression] = result
	}

	return results, nil
}

// executeQuery executes a single metrics query
func (s *Service) executeQuery(ctx context.Context, query Query) (QueryResult, error) {
	// Generate mock time series data based on the query expression
	metrics := s.generateMockMetrics(query)

	return QueryResult{
		Query:   query.Expression,
		Metrics: metrics,
	}, nil
}

// generateMockMetrics generates realistic mock metrics data
func (s *Service) generateMockMetrics(query Query) []Metric {
	var metrics []Metric

	// Determine step size (default to 1 minute if not specified)
	step := query.Step
	if step == 0 {
		step = 60000 // 1 minute in milliseconds
	}

	// Generate data points based on query expression
	switch {
	case contains(query.Expression, "cpu"):
		metrics = append(metrics, s.generateCPUMetrics(query.StartTime, query.EndTime, step))
	case contains(query.Expression, "memory"):
		metrics = append(metrics, s.generateMemoryMetrics(query.StartTime, query.EndTime, step))
	case contains(query.Expression, "network"):
		metrics = append(metrics, s.generateNetworkMetrics(query.StartTime, query.EndTime, step))
	case contains(query.Expression, "disk"):
		metrics = append(metrics, s.generateDiskMetrics(query.StartTime, query.EndTime, step))
	case contains(query.Expression, "request"):
		metrics = append(metrics, s.generateRequestMetrics(query.StartTime, query.EndTime, step))
	case contains(query.Expression, "error"):
		metrics = append(metrics, s.generateErrorMetrics(query.StartTime, query.EndTime, step))
	case contains(query.Expression, "discovery"):
		metrics = append(metrics, s.generateDiscoveryMetrics(query.StartTime, query.EndTime, step))
	case contains(query.Expression, "workflow"):
		metrics = append(metrics, s.generateWorkflowMetrics(query.StartTime, query.EndTime, step))
	case contains(query.Expression, "envoy"):
		metrics = append(metrics, s.generateEnvoyMetrics(query.StartTime, query.EndTime, step))
	default:
		// Generic metric
		metrics = append(metrics, s.generateGenericMetrics(query.StartTime, query.EndTime, step, query.Expression))
	}

	return metrics
}

// generateCPUMetrics generates CPU usage metrics
func (s *Service) generateCPUMetrics(start, end, step int64) Metric {
	dataPoints := s.generateTimeSeriesData(start, end, step, 20, 90, 0.1) // 20-90% CPU with some noise

	return Metric{
		Name: "cpu_usage_percent",
		Labels: map[string]string{
			"instance": "cainuro-orchestrator",
			"job":      "orchestrator",
		},
		DataPoints: dataPoints,
		Unit:       "percent",
	}
}

// generateMemoryMetrics generates memory usage metrics
func (s *Service) generateMemoryMetrics(start, end, step int64) Metric {
	dataPoints := s.generateTimeSeriesData(start, end, step, 40, 85, 0.05) // 40-85% memory with less noise

	return Metric{
		Name: "memory_usage_percent",
		Labels: map[string]string{
			"instance": "cainuro-orchestrator",
			"job":      "orchestrator",
		},
		DataPoints: dataPoints,
		Unit:       "percent",
	}
}

// generateNetworkMetrics generates network I/O metrics
func (s *Service) generateNetworkMetrics(start, end, step int64) Metric {
	dataPoints := s.generateTimeSeriesData(start, end, step, 100, 1000, 0.2) // 100-1000 MB/s with noise

	return Metric{
		Name: "network_io_bytes_per_second",
		Labels: map[string]string{
			"instance":  "cainuro-orchestrator",
			"direction": "transmit",
		},
		DataPoints: dataPoints,
		Unit:       "bytes_per_second",
	}
}

// generateDiskMetrics generates disk I/O metrics
func (s *Service) generateDiskMetrics(start, end, step int64) Metric {
	dataPoints := s.generateTimeSeriesData(start, end, step, 50, 500, 0.15) // 50-500 IOPS with noise

	return Metric{
		Name: "disk_io_operations_per_second",
		Labels: map[string]string{
			"instance": "cainuro-orchestrator",
			"device":   "/dev/sda1",
		},
		DataPoints: dataPoints,
		Unit:       "operations_per_second",
	}
}

// generateRequestMetrics generates HTTP request metrics
func (s *Service) generateRequestMetrics(start, end, step int64) Metric {
	dataPoints := s.generateTimeSeriesData(start, end, step, 10, 100, 0.3) // 10-100 requests/sec with noise

	return Metric{
		Name: "http_requests_per_second",
		Labels: map[string]string{
			"method": "GET",
			"status": "200",
		},
		DataPoints: dataPoints,
		Unit:       "requests_per_second",
	}
}

// generateErrorMetrics generates error rate metrics
func (s *Service) generateErrorMetrics(start, end, step int64) Metric {
	dataPoints := s.generateTimeSeriesData(start, end, step, 0, 5, 0.1) // 0-5% error rate

	return Metric{
		Name: "error_rate_percent",
		Labels: map[string]string{
			"service": "orchestrator",
		},
		DataPoints: dataPoints,
		Unit:       "percent",
	}
}

// generateDiscoveryMetrics generates discovery job metrics
func (s *Service) generateDiscoveryMetrics(start, end, step int64) Metric {
	dataPoints := s.generateTimeSeriesData(start, end, step, 0, 10, 0.5) // 0-10 discovery jobs

	return Metric{
		Name: "cainuro_discovery_jobs_total",
		Labels: map[string]string{
			"provider": "aws",
			"status":   "completed",
		},
		DataPoints: dataPoints,
		Unit:       "count",
	}
}

// generateWorkflowMetrics generates workflow execution metrics
func (s *Service) generateWorkflowMetrics(start, end, step int64) Metric {
	dataPoints := s.generateTimeSeriesData(start, end, step, 0, 5, 0.3) // 0-5 workflows

	return Metric{
		Name: "cainuro_workflow_executions_total",
		Labels: map[string]string{
			"workflow": "cloud-search",
			"status":   "success",
		},
		DataPoints: dataPoints,
		Unit:       "count",
	}
}

// generateEnvoyMetrics generates Envoy proxy metrics
func (s *Service) generateEnvoyMetrics(start, end, step int64) Metric {
	dataPoints := s.generateTimeSeriesData(start, end, step, 50, 200, 0.2) // 50-200 connections

	return Metric{
		Name: "envoy_cluster_upstream_cx_active",
		Labels: map[string]string{
			"cluster": "web-cluster",
		},
		DataPoints: dataPoints,
		Unit:       "connections",
	}
}

// generateGenericMetrics generates generic metrics for unknown queries
func (s *Service) generateGenericMetrics(start, end, step int64, expression string) Metric {
	dataPoints := s.generateTimeSeriesData(start, end, step, 0, 100, 0.2)

	return Metric{
		Name: expression,
		Labels: map[string]string{
			"source": "mock",
		},
		DataPoints: dataPoints,
		Unit:       "value",
	}
}

// generateTimeSeriesData generates realistic time series data
func (s *Service) generateTimeSeriesData(start, end, step int64, min, max, noise float64) []DataPoint {
	var dataPoints []DataPoint

	// Generate a base trend
	duration := float64(end - start)
	baseValue := (min + max) / 2

	for timestamp := start; timestamp <= end; timestamp += step {
		// Calculate position in time series (0.0 to 1.0)
		position := float64(timestamp-start) / duration

		// Generate a sine wave for realistic variation
		sineComponent := math.Sin(position*4*math.Pi) * (max-min) * 0.2

		// Add some random noise
		noiseComponent := (rand.Float64() - 0.5) * (max - min) * noise

		// Calculate final value
		value := baseValue + sineComponent + noiseComponent

		// Ensure value stays within bounds
		if value < min {
			value = min
		}
		if value > max {
			value = max
		}

		dataPoints = append(dataPoints, DataPoint{
			Timestamp: timestamp,
			Value:     math.Round(value*100) / 100, // Round to 2 decimal places
		})
	}

	return dataPoints
}

// GetSystemMetrics returns current system metrics
func (s *Service) GetSystemMetrics(ctx context.Context) (map[string]interface{}, error) {
	now := time.Now().UnixMilli()
	oneHourAgo := now - (60 * 60 * 1000)

	// Generate current metrics snapshot
	metrics := map[string]interface{}{
		"cpu_usage":         rand.Float64()*40 + 30,  // 30-70%
		"memory_usage":      rand.Float64()*30 + 50,  // 50-80%
		"disk_usage":        rand.Float64()*20 + 60,  // 60-80%
		"network_in_mbps":   rand.Float64()*100 + 50, // 50-150 Mbps
		"network_out_mbps":  rand.Float64()*80 + 20,  // 20-100 Mbps
		"active_connections": int(rand.Float64()*100 + 50), // 50-150 connections
		"discovery_jobs":    int(rand.Float64()*5 + 1),     // 1-6 jobs
		"workflow_executions": int(rand.Float64()*3 + 1),   // 1-4 executions
		"error_rate":        rand.Float64() * 2,            // 0-2%
		"uptime_seconds":    int(rand.Float64()*86400 + 3600), // 1-25 hours
	}

	s.logger.Debug("Generated system metrics snapshot")
	return metrics, nil
}

// Health checks the health of the metrics service
func (s *Service) Health(ctx context.Context) error {
	// Simple health check - try to generate some metrics
	_, err := s.GetSystemMetrics(ctx)
	return err
}

// Helper function to check if a string contains a substring
func contains(s, substr string) bool {
	return len(s) >= len(substr) && s[:len(substr)] == substr || 
		   len(s) > len(substr) && s[len(s)-len(substr):] == substr ||
		   (len(s) > len(substr) && findSubstring(s, substr))
}

func findSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
