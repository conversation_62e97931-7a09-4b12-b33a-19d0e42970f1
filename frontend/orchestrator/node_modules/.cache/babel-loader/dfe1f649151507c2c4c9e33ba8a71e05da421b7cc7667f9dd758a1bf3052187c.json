{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport verifyPlainObject from '../utils/verifyPlainObject';\nimport { createInvalidArgFactory } from './invalidArgFactory';\nexport function defaultMergeProps(stateProps, dispatchProps, ownProps) {\n  // @ts-ignore\n  return _extends({}, ownProps, stateProps, dispatchProps);\n}\nexport function wrapMergePropsFunc(mergeProps) {\n  return function initMergePropsProxy(dispatch, {\n    displayName,\n    areMergedPropsEqual\n  }) {\n    let hasRunOnce = false;\n    let mergedProps;\n    return function mergePropsProxy(stateProps, dispatchProps, ownProps) {\n      const nextMergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n      if (hasRunOnce) {\n        if (!areMergedPropsEqual(nextMergedProps, mergedProps)) mergedProps = nextMergedProps;\n      } else {\n        hasRunOnce = true;\n        mergedProps = nextMergedProps;\n        if (process.env.NODE_ENV !== 'production') verifyPlainObject(mergedProps, displayName, 'mergeProps');\n      }\n      return mergedProps;\n    };\n  };\n}\nexport function mergePropsFactory(mergeProps) {\n  return !mergeProps ? () => defaultMergeProps : typeof mergeProps === 'function' ? wrapMergePropsFunc(mergeProps) : createInvalidArgFactory(mergeProps, 'mergeProps');\n}", "map": {"version": 3, "names": ["_extends", "verifyPlainObject", "createInvalidArgFactory", "defaultMergeProps", "stateProps", "dispatchProps", "ownProps", "wrapMergePropsFunc", "mergeProps", "initMergePropsProxy", "dispatch", "displayName", "areMergedPropsEqual", "hasRunOnce", "mergedProps", "mergePropsProxy", "nextMergedProps", "process", "env", "NODE_ENV", "mergePropsFactory"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/node_modules/react-redux/es/connect/mergeProps.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport verifyPlainObject from '../utils/verifyPlainObject';\nimport { createInvalidArgFactory } from './invalidArgFactory';\nexport function defaultMergeProps(stateProps, dispatchProps, ownProps) {\n  // @ts-ignore\n  return _extends({}, ownProps, stateProps, dispatchProps);\n}\nexport function wrapMergePropsFunc(mergeProps) {\n  return function initMergePropsProxy(dispatch, {\n    displayName,\n    areMergedPropsEqual\n  }) {\n    let hasRunOnce = false;\n    let mergedProps;\n    return function mergePropsProxy(stateProps, dispatchProps, ownProps) {\n      const nextMergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n\n      if (hasRunOnce) {\n        if (!areMergedPropsEqual(nextMergedProps, mergedProps)) mergedProps = nextMergedProps;\n      } else {\n        hasRunOnce = true;\n        mergedProps = nextMergedProps;\n        if (process.env.NODE_ENV !== 'production') verifyPlainObject(mergedProps, displayName, 'mergeProps');\n      }\n\n      return mergedProps;\n    };\n  };\n}\nexport function mergePropsFactory(mergeProps) {\n  return !mergeProps ? () => defaultMergeProps : typeof mergeProps === 'function' ? wrapMergePropsFunc(mergeProps) : createInvalidArgFactory(mergeProps, 'mergeProps');\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,iBAAiB,MAAM,4BAA4B;AAC1D,SAASC,uBAAuB,QAAQ,qBAAqB;AAC7D,OAAO,SAASC,iBAAiBA,CAACC,UAAU,EAAEC,aAAa,EAAEC,QAAQ,EAAE;EACrE;EACA,OAAON,QAAQ,CAAC,CAAC,CAAC,EAAEM,QAAQ,EAAEF,UAAU,EAAEC,aAAa,CAAC;AAC1D;AACA,OAAO,SAASE,kBAAkBA,CAACC,UAAU,EAAE;EAC7C,OAAO,SAASC,mBAAmBA,CAACC,QAAQ,EAAE;IAC5CC,WAAW;IACXC;EACF,CAAC,EAAE;IACD,IAAIC,UAAU,GAAG,KAAK;IACtB,IAAIC,WAAW;IACf,OAAO,SAASC,eAAeA,CAACX,UAAU,EAAEC,aAAa,EAAEC,QAAQ,EAAE;MACnE,MAAMU,eAAe,GAAGR,UAAU,CAACJ,UAAU,EAAEC,aAAa,EAAEC,QAAQ,CAAC;MAEvE,IAAIO,UAAU,EAAE;QACd,IAAI,CAACD,mBAAmB,CAACI,eAAe,EAAEF,WAAW,CAAC,EAAEA,WAAW,GAAGE,eAAe;MACvF,CAAC,MAAM;QACLH,UAAU,GAAG,IAAI;QACjBC,WAAW,GAAGE,eAAe;QAC7B,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAElB,iBAAiB,CAACa,WAAW,EAAEH,WAAW,EAAE,YAAY,CAAC;MACtG;MAEA,OAAOG,WAAW;IACpB,CAAC;EACH,CAAC;AACH;AACA,OAAO,SAASM,iBAAiBA,CAACZ,UAAU,EAAE;EAC5C,OAAO,CAACA,UAAU,GAAG,MAAML,iBAAiB,GAAG,OAAOK,UAAU,KAAK,UAAU,GAAGD,kBAAkB,CAACC,UAAU,CAAC,GAAGN,uBAAuB,CAACM,UAAU,EAAE,YAAY,CAAC;AACtK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}