// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.17.3
// source: k8s/v1/k8s.proto

package k8sv1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	K8SAPI_DescribePod_FullMethodName         = "/clutch.k8s.v1.K8sAPI/DescribePod"
	K8SAPI_ListPods_FullMethodName            = "/clutch.k8s.v1.K8sAPI/ListPods"
	K8SAPI_DeletePod_FullMethodName           = "/clutch.k8s.v1.K8sAPI/DeletePod"
	K8SAPI_UpdatePod_FullMethodName           = "/clutch.k8s.v1.K8sAPI/UpdatePod"
	K8SAPI_GetPodLogs_FullMethodName          = "/clutch.k8s.v1.K8sAPI/GetPodLogs"
	K8SAPI_ResizeHPA_FullMethodName           = "/clutch.k8s.v1.K8sAPI/ResizeHPA"
	K8SAPI_DeleteHPA_FullMethodName           = "/clutch.k8s.v1.K8sAPI/DeleteHPA"
	K8SAPI_DescribeDeployment_FullMethodName  = "/clutch.k8s.v1.K8sAPI/DescribeDeployment"
	K8SAPI_ListDeployments_FullMethodName     = "/clutch.k8s.v1.K8sAPI/ListDeployments"
	K8SAPI_UpdateDeployment_FullMethodName    = "/clutch.k8s.v1.K8sAPI/UpdateDeployment"
	K8SAPI_DeleteDeployment_FullMethodName    = "/clutch.k8s.v1.K8sAPI/DeleteDeployment"
	K8SAPI_DescribeService_FullMethodName     = "/clutch.k8s.v1.K8sAPI/DescribeService"
	K8SAPI_ListServices_FullMethodName        = "/clutch.k8s.v1.K8sAPI/ListServices"
	K8SAPI_DeleteService_FullMethodName       = "/clutch.k8s.v1.K8sAPI/DeleteService"
	K8SAPI_DescribeStatefulSet_FullMethodName = "/clutch.k8s.v1.K8sAPI/DescribeStatefulSet"
	K8SAPI_ListStatefulSets_FullMethodName    = "/clutch.k8s.v1.K8sAPI/ListStatefulSets"
	K8SAPI_UpdateStatefulSet_FullMethodName   = "/clutch.k8s.v1.K8sAPI/UpdateStatefulSet"
	K8SAPI_DeleteStatefulSet_FullMethodName   = "/clutch.k8s.v1.K8sAPI/DeleteStatefulSet"
	K8SAPI_DescribeCronJob_FullMethodName     = "/clutch.k8s.v1.K8sAPI/DescribeCronJob"
	K8SAPI_ListCronJobs_FullMethodName        = "/clutch.k8s.v1.K8sAPI/ListCronJobs"
	K8SAPI_DeleteCronJob_FullMethodName       = "/clutch.k8s.v1.K8sAPI/DeleteCronJob"
	K8SAPI_ListConfigMaps_FullMethodName      = "/clutch.k8s.v1.K8sAPI/ListConfigMaps"
	K8SAPI_DescribeConfigMap_FullMethodName   = "/clutch.k8s.v1.K8sAPI/DescribeConfigMap"
	K8SAPI_DeleteConfigMap_FullMethodName     = "/clutch.k8s.v1.K8sAPI/DeleteConfigMap"
	K8SAPI_DescribeJob_FullMethodName         = "/clutch.k8s.v1.K8sAPI/DescribeJob"
	K8SAPI_ListJobs_FullMethodName            = "/clutch.k8s.v1.K8sAPI/ListJobs"
	K8SAPI_DeleteJob_FullMethodName           = "/clutch.k8s.v1.K8sAPI/DeleteJob"
	K8SAPI_CreateJob_FullMethodName           = "/clutch.k8s.v1.K8sAPI/CreateJob"
	K8SAPI_DescribeNamespace_FullMethodName   = "/clutch.k8s.v1.K8sAPI/DescribeNamespace"
	K8SAPI_ListEvents_FullMethodName          = "/clutch.k8s.v1.K8sAPI/ListEvents"
	K8SAPI_DescribeNode_FullMethodName        = "/clutch.k8s.v1.K8sAPI/DescribeNode"
	K8SAPI_UpdateNode_FullMethodName          = "/clutch.k8s.v1.K8sAPI/UpdateNode"
	K8SAPI_ListNamespaceEvents_FullMethodName = "/clutch.k8s.v1.K8sAPI/ListNamespaceEvents"
)

// K8SAPIClient is the client API for K8SAPI service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type K8SAPIClient interface {
	DescribePod(ctx context.Context, in *DescribePodRequest, opts ...grpc.CallOption) (*DescribePodResponse, error)
	ListPods(ctx context.Context, in *ListPodsRequest, opts ...grpc.CallOption) (*ListPodsResponse, error)
	DeletePod(ctx context.Context, in *DeletePodRequest, opts ...grpc.CallOption) (*DeletePodResponse, error)
	UpdatePod(ctx context.Context, in *UpdatePodRequest, opts ...grpc.CallOption) (*UpdatePodResponse, error)
	GetPodLogs(ctx context.Context, in *GetPodLogsRequest, opts ...grpc.CallOption) (*GetPodLogsResponse, error)
	ResizeHPA(ctx context.Context, in *ResizeHPARequest, opts ...grpc.CallOption) (*ResizeHPAResponse, error)
	DeleteHPA(ctx context.Context, in *DeleteHPARequest, opts ...grpc.CallOption) (*DeleteHPAResponse, error)
	DescribeDeployment(ctx context.Context, in *DescribeDeploymentRequest, opts ...grpc.CallOption) (*DescribeDeploymentResponse, error)
	ListDeployments(ctx context.Context, in *ListDeploymentsRequest, opts ...grpc.CallOption) (*ListDeploymentsResponse, error)
	UpdateDeployment(ctx context.Context, in *UpdateDeploymentRequest, opts ...grpc.CallOption) (*UpdateDeploymentResponse, error)
	DeleteDeployment(ctx context.Context, in *DeleteDeploymentRequest, opts ...grpc.CallOption) (*DeleteDeploymentResponse, error)
	DescribeService(ctx context.Context, in *DescribeServiceRequest, opts ...grpc.CallOption) (*DescribeServiceResponse, error)
	ListServices(ctx context.Context, in *ListServicesRequest, opts ...grpc.CallOption) (*ListServicesResponse, error)
	DeleteService(ctx context.Context, in *DeleteServiceRequest, opts ...grpc.CallOption) (*DeleteServiceResponse, error)
	DescribeStatefulSet(ctx context.Context, in *DescribeStatefulSetRequest, opts ...grpc.CallOption) (*DescribeStatefulSetResponse, error)
	ListStatefulSets(ctx context.Context, in *ListStatefulSetsRequest, opts ...grpc.CallOption) (*ListStatefulSetsResponse, error)
	UpdateStatefulSet(ctx context.Context, in *UpdateStatefulSetRequest, opts ...grpc.CallOption) (*UpdateStatefulSetResponse, error)
	DeleteStatefulSet(ctx context.Context, in *DeleteStatefulSetRequest, opts ...grpc.CallOption) (*DeleteStatefulSetResponse, error)
	DescribeCronJob(ctx context.Context, in *DescribeCronJobRequest, opts ...grpc.CallOption) (*DescribeCronJobResponse, error)
	ListCronJobs(ctx context.Context, in *ListCronJobsRequest, opts ...grpc.CallOption) (*ListCronJobsResponse, error)
	DeleteCronJob(ctx context.Context, in *DeleteCronJobRequest, opts ...grpc.CallOption) (*DeleteCronJobResponse, error)
	ListConfigMaps(ctx context.Context, in *ListConfigMapsRequest, opts ...grpc.CallOption) (*ListConfigMapsResponse, error)
	DescribeConfigMap(ctx context.Context, in *DescribeConfigMapRequest, opts ...grpc.CallOption) (*DescribeConfigMapResponse, error)
	DeleteConfigMap(ctx context.Context, in *DeleteConfigMapRequest, opts ...grpc.CallOption) (*DeleteConfigMapResponse, error)
	DescribeJob(ctx context.Context, in *DescribeJobRequest, opts ...grpc.CallOption) (*DescribeJobResponse, error)
	ListJobs(ctx context.Context, in *ListJobsRequest, opts ...grpc.CallOption) (*ListJobsResponse, error)
	DeleteJob(ctx context.Context, in *DeleteJobRequest, opts ...grpc.CallOption) (*DeleteJobResponse, error)
	CreateJob(ctx context.Context, in *CreateJobRequest, opts ...grpc.CallOption) (*CreateJobResponse, error)
	DescribeNamespace(ctx context.Context, in *DescribeNamespaceRequest, opts ...grpc.CallOption) (*DescribeNamespaceResponse, error)
	ListEvents(ctx context.Context, in *ListEventsRequest, opts ...grpc.CallOption) (*ListEventsResponse, error)
	DescribeNode(ctx context.Context, in *DescribeNodeRequest, opts ...grpc.CallOption) (*DescribeNodeResponse, error)
	UpdateNode(ctx context.Context, in *UpdateNodeRequest, opts ...grpc.CallOption) (*UpdateNodeResponse, error)
	ListNamespaceEvents(ctx context.Context, in *ListNamespaceEventsRequest, opts ...grpc.CallOption) (*ListNamespaceEventsResponse, error)
}

type k8SAPIClient struct {
	cc grpc.ClientConnInterface
}

func NewK8SAPIClient(cc grpc.ClientConnInterface) K8SAPIClient {
	return &k8SAPIClient{cc}
}

func (c *k8SAPIClient) DescribePod(ctx context.Context, in *DescribePodRequest, opts ...grpc.CallOption) (*DescribePodResponse, error) {
	out := new(DescribePodResponse)
	err := c.cc.Invoke(ctx, K8SAPI_DescribePod_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SAPIClient) ListPods(ctx context.Context, in *ListPodsRequest, opts ...grpc.CallOption) (*ListPodsResponse, error) {
	out := new(ListPodsResponse)
	err := c.cc.Invoke(ctx, K8SAPI_ListPods_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SAPIClient) DeletePod(ctx context.Context, in *DeletePodRequest, opts ...grpc.CallOption) (*DeletePodResponse, error) {
	out := new(DeletePodResponse)
	err := c.cc.Invoke(ctx, K8SAPI_DeletePod_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SAPIClient) UpdatePod(ctx context.Context, in *UpdatePodRequest, opts ...grpc.CallOption) (*UpdatePodResponse, error) {
	out := new(UpdatePodResponse)
	err := c.cc.Invoke(ctx, K8SAPI_UpdatePod_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SAPIClient) GetPodLogs(ctx context.Context, in *GetPodLogsRequest, opts ...grpc.CallOption) (*GetPodLogsResponse, error) {
	out := new(GetPodLogsResponse)
	err := c.cc.Invoke(ctx, K8SAPI_GetPodLogs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SAPIClient) ResizeHPA(ctx context.Context, in *ResizeHPARequest, opts ...grpc.CallOption) (*ResizeHPAResponse, error) {
	out := new(ResizeHPAResponse)
	err := c.cc.Invoke(ctx, K8SAPI_ResizeHPA_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SAPIClient) DeleteHPA(ctx context.Context, in *DeleteHPARequest, opts ...grpc.CallOption) (*DeleteHPAResponse, error) {
	out := new(DeleteHPAResponse)
	err := c.cc.Invoke(ctx, K8SAPI_DeleteHPA_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SAPIClient) DescribeDeployment(ctx context.Context, in *DescribeDeploymentRequest, opts ...grpc.CallOption) (*DescribeDeploymentResponse, error) {
	out := new(DescribeDeploymentResponse)
	err := c.cc.Invoke(ctx, K8SAPI_DescribeDeployment_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SAPIClient) ListDeployments(ctx context.Context, in *ListDeploymentsRequest, opts ...grpc.CallOption) (*ListDeploymentsResponse, error) {
	out := new(ListDeploymentsResponse)
	err := c.cc.Invoke(ctx, K8SAPI_ListDeployments_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SAPIClient) UpdateDeployment(ctx context.Context, in *UpdateDeploymentRequest, opts ...grpc.CallOption) (*UpdateDeploymentResponse, error) {
	out := new(UpdateDeploymentResponse)
	err := c.cc.Invoke(ctx, K8SAPI_UpdateDeployment_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SAPIClient) DeleteDeployment(ctx context.Context, in *DeleteDeploymentRequest, opts ...grpc.CallOption) (*DeleteDeploymentResponse, error) {
	out := new(DeleteDeploymentResponse)
	err := c.cc.Invoke(ctx, K8SAPI_DeleteDeployment_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SAPIClient) DescribeService(ctx context.Context, in *DescribeServiceRequest, opts ...grpc.CallOption) (*DescribeServiceResponse, error) {
	out := new(DescribeServiceResponse)
	err := c.cc.Invoke(ctx, K8SAPI_DescribeService_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SAPIClient) ListServices(ctx context.Context, in *ListServicesRequest, opts ...grpc.CallOption) (*ListServicesResponse, error) {
	out := new(ListServicesResponse)
	err := c.cc.Invoke(ctx, K8SAPI_ListServices_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SAPIClient) DeleteService(ctx context.Context, in *DeleteServiceRequest, opts ...grpc.CallOption) (*DeleteServiceResponse, error) {
	out := new(DeleteServiceResponse)
	err := c.cc.Invoke(ctx, K8SAPI_DeleteService_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SAPIClient) DescribeStatefulSet(ctx context.Context, in *DescribeStatefulSetRequest, opts ...grpc.CallOption) (*DescribeStatefulSetResponse, error) {
	out := new(DescribeStatefulSetResponse)
	err := c.cc.Invoke(ctx, K8SAPI_DescribeStatefulSet_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SAPIClient) ListStatefulSets(ctx context.Context, in *ListStatefulSetsRequest, opts ...grpc.CallOption) (*ListStatefulSetsResponse, error) {
	out := new(ListStatefulSetsResponse)
	err := c.cc.Invoke(ctx, K8SAPI_ListStatefulSets_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SAPIClient) UpdateStatefulSet(ctx context.Context, in *UpdateStatefulSetRequest, opts ...grpc.CallOption) (*UpdateStatefulSetResponse, error) {
	out := new(UpdateStatefulSetResponse)
	err := c.cc.Invoke(ctx, K8SAPI_UpdateStatefulSet_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SAPIClient) DeleteStatefulSet(ctx context.Context, in *DeleteStatefulSetRequest, opts ...grpc.CallOption) (*DeleteStatefulSetResponse, error) {
	out := new(DeleteStatefulSetResponse)
	err := c.cc.Invoke(ctx, K8SAPI_DeleteStatefulSet_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SAPIClient) DescribeCronJob(ctx context.Context, in *DescribeCronJobRequest, opts ...grpc.CallOption) (*DescribeCronJobResponse, error) {
	out := new(DescribeCronJobResponse)
	err := c.cc.Invoke(ctx, K8SAPI_DescribeCronJob_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SAPIClient) ListCronJobs(ctx context.Context, in *ListCronJobsRequest, opts ...grpc.CallOption) (*ListCronJobsResponse, error) {
	out := new(ListCronJobsResponse)
	err := c.cc.Invoke(ctx, K8SAPI_ListCronJobs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SAPIClient) DeleteCronJob(ctx context.Context, in *DeleteCronJobRequest, opts ...grpc.CallOption) (*DeleteCronJobResponse, error) {
	out := new(DeleteCronJobResponse)
	err := c.cc.Invoke(ctx, K8SAPI_DeleteCronJob_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SAPIClient) ListConfigMaps(ctx context.Context, in *ListConfigMapsRequest, opts ...grpc.CallOption) (*ListConfigMapsResponse, error) {
	out := new(ListConfigMapsResponse)
	err := c.cc.Invoke(ctx, K8SAPI_ListConfigMaps_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SAPIClient) DescribeConfigMap(ctx context.Context, in *DescribeConfigMapRequest, opts ...grpc.CallOption) (*DescribeConfigMapResponse, error) {
	out := new(DescribeConfigMapResponse)
	err := c.cc.Invoke(ctx, K8SAPI_DescribeConfigMap_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SAPIClient) DeleteConfigMap(ctx context.Context, in *DeleteConfigMapRequest, opts ...grpc.CallOption) (*DeleteConfigMapResponse, error) {
	out := new(DeleteConfigMapResponse)
	err := c.cc.Invoke(ctx, K8SAPI_DeleteConfigMap_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SAPIClient) DescribeJob(ctx context.Context, in *DescribeJobRequest, opts ...grpc.CallOption) (*DescribeJobResponse, error) {
	out := new(DescribeJobResponse)
	err := c.cc.Invoke(ctx, K8SAPI_DescribeJob_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SAPIClient) ListJobs(ctx context.Context, in *ListJobsRequest, opts ...grpc.CallOption) (*ListJobsResponse, error) {
	out := new(ListJobsResponse)
	err := c.cc.Invoke(ctx, K8SAPI_ListJobs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SAPIClient) DeleteJob(ctx context.Context, in *DeleteJobRequest, opts ...grpc.CallOption) (*DeleteJobResponse, error) {
	out := new(DeleteJobResponse)
	err := c.cc.Invoke(ctx, K8SAPI_DeleteJob_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SAPIClient) CreateJob(ctx context.Context, in *CreateJobRequest, opts ...grpc.CallOption) (*CreateJobResponse, error) {
	out := new(CreateJobResponse)
	err := c.cc.Invoke(ctx, K8SAPI_CreateJob_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SAPIClient) DescribeNamespace(ctx context.Context, in *DescribeNamespaceRequest, opts ...grpc.CallOption) (*DescribeNamespaceResponse, error) {
	out := new(DescribeNamespaceResponse)
	err := c.cc.Invoke(ctx, K8SAPI_DescribeNamespace_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SAPIClient) ListEvents(ctx context.Context, in *ListEventsRequest, opts ...grpc.CallOption) (*ListEventsResponse, error) {
	out := new(ListEventsResponse)
	err := c.cc.Invoke(ctx, K8SAPI_ListEvents_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SAPIClient) DescribeNode(ctx context.Context, in *DescribeNodeRequest, opts ...grpc.CallOption) (*DescribeNodeResponse, error) {
	out := new(DescribeNodeResponse)
	err := c.cc.Invoke(ctx, K8SAPI_DescribeNode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SAPIClient) UpdateNode(ctx context.Context, in *UpdateNodeRequest, opts ...grpc.CallOption) (*UpdateNodeResponse, error) {
	out := new(UpdateNodeResponse)
	err := c.cc.Invoke(ctx, K8SAPI_UpdateNode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SAPIClient) ListNamespaceEvents(ctx context.Context, in *ListNamespaceEventsRequest, opts ...grpc.CallOption) (*ListNamespaceEventsResponse, error) {
	out := new(ListNamespaceEventsResponse)
	err := c.cc.Invoke(ctx, K8SAPI_ListNamespaceEvents_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// K8SAPIServer is the server API for K8SAPI service.
// All implementations should embed UnimplementedK8SAPIServer
// for forward compatibility
type K8SAPIServer interface {
	DescribePod(context.Context, *DescribePodRequest) (*DescribePodResponse, error)
	ListPods(context.Context, *ListPodsRequest) (*ListPodsResponse, error)
	DeletePod(context.Context, *DeletePodRequest) (*DeletePodResponse, error)
	UpdatePod(context.Context, *UpdatePodRequest) (*UpdatePodResponse, error)
	GetPodLogs(context.Context, *GetPodLogsRequest) (*GetPodLogsResponse, error)
	ResizeHPA(context.Context, *ResizeHPARequest) (*ResizeHPAResponse, error)
	DeleteHPA(context.Context, *DeleteHPARequest) (*DeleteHPAResponse, error)
	DescribeDeployment(context.Context, *DescribeDeploymentRequest) (*DescribeDeploymentResponse, error)
	ListDeployments(context.Context, *ListDeploymentsRequest) (*ListDeploymentsResponse, error)
	UpdateDeployment(context.Context, *UpdateDeploymentRequest) (*UpdateDeploymentResponse, error)
	DeleteDeployment(context.Context, *DeleteDeploymentRequest) (*DeleteDeploymentResponse, error)
	DescribeService(context.Context, *DescribeServiceRequest) (*DescribeServiceResponse, error)
	ListServices(context.Context, *ListServicesRequest) (*ListServicesResponse, error)
	DeleteService(context.Context, *DeleteServiceRequest) (*DeleteServiceResponse, error)
	DescribeStatefulSet(context.Context, *DescribeStatefulSetRequest) (*DescribeStatefulSetResponse, error)
	ListStatefulSets(context.Context, *ListStatefulSetsRequest) (*ListStatefulSetsResponse, error)
	UpdateStatefulSet(context.Context, *UpdateStatefulSetRequest) (*UpdateStatefulSetResponse, error)
	DeleteStatefulSet(context.Context, *DeleteStatefulSetRequest) (*DeleteStatefulSetResponse, error)
	DescribeCronJob(context.Context, *DescribeCronJobRequest) (*DescribeCronJobResponse, error)
	ListCronJobs(context.Context, *ListCronJobsRequest) (*ListCronJobsResponse, error)
	DeleteCronJob(context.Context, *DeleteCronJobRequest) (*DeleteCronJobResponse, error)
	ListConfigMaps(context.Context, *ListConfigMapsRequest) (*ListConfigMapsResponse, error)
	DescribeConfigMap(context.Context, *DescribeConfigMapRequest) (*DescribeConfigMapResponse, error)
	DeleteConfigMap(context.Context, *DeleteConfigMapRequest) (*DeleteConfigMapResponse, error)
	DescribeJob(context.Context, *DescribeJobRequest) (*DescribeJobResponse, error)
	ListJobs(context.Context, *ListJobsRequest) (*ListJobsResponse, error)
	DeleteJob(context.Context, *DeleteJobRequest) (*DeleteJobResponse, error)
	CreateJob(context.Context, *CreateJobRequest) (*CreateJobResponse, error)
	DescribeNamespace(context.Context, *DescribeNamespaceRequest) (*DescribeNamespaceResponse, error)
	ListEvents(context.Context, *ListEventsRequest) (*ListEventsResponse, error)
	DescribeNode(context.Context, *DescribeNodeRequest) (*DescribeNodeResponse, error)
	UpdateNode(context.Context, *UpdateNodeRequest) (*UpdateNodeResponse, error)
	ListNamespaceEvents(context.Context, *ListNamespaceEventsRequest) (*ListNamespaceEventsResponse, error)
}

// UnimplementedK8SAPIServer should be embedded to have forward compatible implementations.
type UnimplementedK8SAPIServer struct {
}

func (UnimplementedK8SAPIServer) DescribePod(context.Context, *DescribePodRequest) (*DescribePodResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DescribePod not implemented")
}
func (UnimplementedK8SAPIServer) ListPods(context.Context, *ListPodsRequest) (*ListPodsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPods not implemented")
}
func (UnimplementedK8SAPIServer) DeletePod(context.Context, *DeletePodRequest) (*DeletePodResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePod not implemented")
}
func (UnimplementedK8SAPIServer) UpdatePod(context.Context, *UpdatePodRequest) (*UpdatePodResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePod not implemented")
}
func (UnimplementedK8SAPIServer) GetPodLogs(context.Context, *GetPodLogsRequest) (*GetPodLogsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPodLogs not implemented")
}
func (UnimplementedK8SAPIServer) ResizeHPA(context.Context, *ResizeHPARequest) (*ResizeHPAResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResizeHPA not implemented")
}
func (UnimplementedK8SAPIServer) DeleteHPA(context.Context, *DeleteHPARequest) (*DeleteHPAResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteHPA not implemented")
}
func (UnimplementedK8SAPIServer) DescribeDeployment(context.Context, *DescribeDeploymentRequest) (*DescribeDeploymentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DescribeDeployment not implemented")
}
func (UnimplementedK8SAPIServer) ListDeployments(context.Context, *ListDeploymentsRequest) (*ListDeploymentsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDeployments not implemented")
}
func (UnimplementedK8SAPIServer) UpdateDeployment(context.Context, *UpdateDeploymentRequest) (*UpdateDeploymentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDeployment not implemented")
}
func (UnimplementedK8SAPIServer) DeleteDeployment(context.Context, *DeleteDeploymentRequest) (*DeleteDeploymentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDeployment not implemented")
}
func (UnimplementedK8SAPIServer) DescribeService(context.Context, *DescribeServiceRequest) (*DescribeServiceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DescribeService not implemented")
}
func (UnimplementedK8SAPIServer) ListServices(context.Context, *ListServicesRequest) (*ListServicesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListServices not implemented")
}
func (UnimplementedK8SAPIServer) DeleteService(context.Context, *DeleteServiceRequest) (*DeleteServiceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteService not implemented")
}
func (UnimplementedK8SAPIServer) DescribeStatefulSet(context.Context, *DescribeStatefulSetRequest) (*DescribeStatefulSetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DescribeStatefulSet not implemented")
}
func (UnimplementedK8SAPIServer) ListStatefulSets(context.Context, *ListStatefulSetsRequest) (*ListStatefulSetsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListStatefulSets not implemented")
}
func (UnimplementedK8SAPIServer) UpdateStatefulSet(context.Context, *UpdateStatefulSetRequest) (*UpdateStatefulSetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateStatefulSet not implemented")
}
func (UnimplementedK8SAPIServer) DeleteStatefulSet(context.Context, *DeleteStatefulSetRequest) (*DeleteStatefulSetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteStatefulSet not implemented")
}
func (UnimplementedK8SAPIServer) DescribeCronJob(context.Context, *DescribeCronJobRequest) (*DescribeCronJobResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DescribeCronJob not implemented")
}
func (UnimplementedK8SAPIServer) ListCronJobs(context.Context, *ListCronJobsRequest) (*ListCronJobsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCronJobs not implemented")
}
func (UnimplementedK8SAPIServer) DeleteCronJob(context.Context, *DeleteCronJobRequest) (*DeleteCronJobResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteCronJob not implemented")
}
func (UnimplementedK8SAPIServer) ListConfigMaps(context.Context, *ListConfigMapsRequest) (*ListConfigMapsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListConfigMaps not implemented")
}
func (UnimplementedK8SAPIServer) DescribeConfigMap(context.Context, *DescribeConfigMapRequest) (*DescribeConfigMapResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DescribeConfigMap not implemented")
}
func (UnimplementedK8SAPIServer) DeleteConfigMap(context.Context, *DeleteConfigMapRequest) (*DeleteConfigMapResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteConfigMap not implemented")
}
func (UnimplementedK8SAPIServer) DescribeJob(context.Context, *DescribeJobRequest) (*DescribeJobResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DescribeJob not implemented")
}
func (UnimplementedK8SAPIServer) ListJobs(context.Context, *ListJobsRequest) (*ListJobsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListJobs not implemented")
}
func (UnimplementedK8SAPIServer) DeleteJob(context.Context, *DeleteJobRequest) (*DeleteJobResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteJob not implemented")
}
func (UnimplementedK8SAPIServer) CreateJob(context.Context, *CreateJobRequest) (*CreateJobResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateJob not implemented")
}
func (UnimplementedK8SAPIServer) DescribeNamespace(context.Context, *DescribeNamespaceRequest) (*DescribeNamespaceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DescribeNamespace not implemented")
}
func (UnimplementedK8SAPIServer) ListEvents(context.Context, *ListEventsRequest) (*ListEventsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListEvents not implemented")
}
func (UnimplementedK8SAPIServer) DescribeNode(context.Context, *DescribeNodeRequest) (*DescribeNodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DescribeNode not implemented")
}
func (UnimplementedK8SAPIServer) UpdateNode(context.Context, *UpdateNodeRequest) (*UpdateNodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNode not implemented")
}
func (UnimplementedK8SAPIServer) ListNamespaceEvents(context.Context, *ListNamespaceEventsRequest) (*ListNamespaceEventsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListNamespaceEvents not implemented")
}

// UnsafeK8SAPIServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to K8SAPIServer will
// result in compilation errors.
type UnsafeK8SAPIServer interface {
	mustEmbedUnimplementedK8SAPIServer()
}

func RegisterK8SAPIServer(s grpc.ServiceRegistrar, srv K8SAPIServer) {
	s.RegisterService(&K8SAPI_ServiceDesc, srv)
}

func _K8SAPI_DescribePod_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DescribePodRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SAPIServer).DescribePod(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SAPI_DescribePod_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SAPIServer).DescribePod(ctx, req.(*DescribePodRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SAPI_ListPods_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPodsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SAPIServer).ListPods(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SAPI_ListPods_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SAPIServer).ListPods(ctx, req.(*ListPodsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SAPI_DeletePod_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePodRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SAPIServer).DeletePod(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SAPI_DeletePod_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SAPIServer).DeletePod(ctx, req.(*DeletePodRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SAPI_UpdatePod_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePodRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SAPIServer).UpdatePod(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SAPI_UpdatePod_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SAPIServer).UpdatePod(ctx, req.(*UpdatePodRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SAPI_GetPodLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPodLogsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SAPIServer).GetPodLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SAPI_GetPodLogs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SAPIServer).GetPodLogs(ctx, req.(*GetPodLogsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SAPI_ResizeHPA_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResizeHPARequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SAPIServer).ResizeHPA(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SAPI_ResizeHPA_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SAPIServer).ResizeHPA(ctx, req.(*ResizeHPARequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SAPI_DeleteHPA_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteHPARequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SAPIServer).DeleteHPA(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SAPI_DeleteHPA_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SAPIServer).DeleteHPA(ctx, req.(*DeleteHPARequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SAPI_DescribeDeployment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DescribeDeploymentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SAPIServer).DescribeDeployment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SAPI_DescribeDeployment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SAPIServer).DescribeDeployment(ctx, req.(*DescribeDeploymentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SAPI_ListDeployments_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDeploymentsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SAPIServer).ListDeployments(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SAPI_ListDeployments_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SAPIServer).ListDeployments(ctx, req.(*ListDeploymentsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SAPI_UpdateDeployment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDeploymentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SAPIServer).UpdateDeployment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SAPI_UpdateDeployment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SAPIServer).UpdateDeployment(ctx, req.(*UpdateDeploymentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SAPI_DeleteDeployment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDeploymentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SAPIServer).DeleteDeployment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SAPI_DeleteDeployment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SAPIServer).DeleteDeployment(ctx, req.(*DeleteDeploymentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SAPI_DescribeService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DescribeServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SAPIServer).DescribeService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SAPI_DescribeService_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SAPIServer).DescribeService(ctx, req.(*DescribeServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SAPI_ListServices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListServicesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SAPIServer).ListServices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SAPI_ListServices_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SAPIServer).ListServices(ctx, req.(*ListServicesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SAPI_DeleteService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SAPIServer).DeleteService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SAPI_DeleteService_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SAPIServer).DeleteService(ctx, req.(*DeleteServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SAPI_DescribeStatefulSet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DescribeStatefulSetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SAPIServer).DescribeStatefulSet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SAPI_DescribeStatefulSet_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SAPIServer).DescribeStatefulSet(ctx, req.(*DescribeStatefulSetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SAPI_ListStatefulSets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListStatefulSetsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SAPIServer).ListStatefulSets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SAPI_ListStatefulSets_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SAPIServer).ListStatefulSets(ctx, req.(*ListStatefulSetsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SAPI_UpdateStatefulSet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateStatefulSetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SAPIServer).UpdateStatefulSet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SAPI_UpdateStatefulSet_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SAPIServer).UpdateStatefulSet(ctx, req.(*UpdateStatefulSetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SAPI_DeleteStatefulSet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteStatefulSetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SAPIServer).DeleteStatefulSet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SAPI_DeleteStatefulSet_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SAPIServer).DeleteStatefulSet(ctx, req.(*DeleteStatefulSetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SAPI_DescribeCronJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DescribeCronJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SAPIServer).DescribeCronJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SAPI_DescribeCronJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SAPIServer).DescribeCronJob(ctx, req.(*DescribeCronJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SAPI_ListCronJobs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCronJobsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SAPIServer).ListCronJobs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SAPI_ListCronJobs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SAPIServer).ListCronJobs(ctx, req.(*ListCronJobsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SAPI_DeleteCronJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteCronJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SAPIServer).DeleteCronJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SAPI_DeleteCronJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SAPIServer).DeleteCronJob(ctx, req.(*DeleteCronJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SAPI_ListConfigMaps_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListConfigMapsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SAPIServer).ListConfigMaps(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SAPI_ListConfigMaps_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SAPIServer).ListConfigMaps(ctx, req.(*ListConfigMapsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SAPI_DescribeConfigMap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DescribeConfigMapRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SAPIServer).DescribeConfigMap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SAPI_DescribeConfigMap_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SAPIServer).DescribeConfigMap(ctx, req.(*DescribeConfigMapRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SAPI_DeleteConfigMap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteConfigMapRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SAPIServer).DeleteConfigMap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SAPI_DeleteConfigMap_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SAPIServer).DeleteConfigMap(ctx, req.(*DeleteConfigMapRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SAPI_DescribeJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DescribeJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SAPIServer).DescribeJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SAPI_DescribeJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SAPIServer).DescribeJob(ctx, req.(*DescribeJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SAPI_ListJobs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListJobsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SAPIServer).ListJobs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SAPI_ListJobs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SAPIServer).ListJobs(ctx, req.(*ListJobsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SAPI_DeleteJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SAPIServer).DeleteJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SAPI_DeleteJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SAPIServer).DeleteJob(ctx, req.(*DeleteJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SAPI_CreateJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SAPIServer).CreateJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SAPI_CreateJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SAPIServer).CreateJob(ctx, req.(*CreateJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SAPI_DescribeNamespace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DescribeNamespaceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SAPIServer).DescribeNamespace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SAPI_DescribeNamespace_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SAPIServer).DescribeNamespace(ctx, req.(*DescribeNamespaceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SAPI_ListEvents_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListEventsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SAPIServer).ListEvents(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SAPI_ListEvents_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SAPIServer).ListEvents(ctx, req.(*ListEventsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SAPI_DescribeNode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DescribeNodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SAPIServer).DescribeNode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SAPI_DescribeNode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SAPIServer).DescribeNode(ctx, req.(*DescribeNodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SAPI_UpdateNode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SAPIServer).UpdateNode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SAPI_UpdateNode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SAPIServer).UpdateNode(ctx, req.(*UpdateNodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SAPI_ListNamespaceEvents_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListNamespaceEventsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SAPIServer).ListNamespaceEvents(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SAPI_ListNamespaceEvents_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SAPIServer).ListNamespaceEvents(ctx, req.(*ListNamespaceEventsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// K8SAPI_ServiceDesc is the grpc.ServiceDesc for K8SAPI service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var K8SAPI_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "clutch.k8s.v1.K8sAPI",
	HandlerType: (*K8SAPIServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "DescribePod",
			Handler:    _K8SAPI_DescribePod_Handler,
		},
		{
			MethodName: "ListPods",
			Handler:    _K8SAPI_ListPods_Handler,
		},
		{
			MethodName: "DeletePod",
			Handler:    _K8SAPI_DeletePod_Handler,
		},
		{
			MethodName: "UpdatePod",
			Handler:    _K8SAPI_UpdatePod_Handler,
		},
		{
			MethodName: "GetPodLogs",
			Handler:    _K8SAPI_GetPodLogs_Handler,
		},
		{
			MethodName: "ResizeHPA",
			Handler:    _K8SAPI_ResizeHPA_Handler,
		},
		{
			MethodName: "DeleteHPA",
			Handler:    _K8SAPI_DeleteHPA_Handler,
		},
		{
			MethodName: "DescribeDeployment",
			Handler:    _K8SAPI_DescribeDeployment_Handler,
		},
		{
			MethodName: "ListDeployments",
			Handler:    _K8SAPI_ListDeployments_Handler,
		},
		{
			MethodName: "UpdateDeployment",
			Handler:    _K8SAPI_UpdateDeployment_Handler,
		},
		{
			MethodName: "DeleteDeployment",
			Handler:    _K8SAPI_DeleteDeployment_Handler,
		},
		{
			MethodName: "DescribeService",
			Handler:    _K8SAPI_DescribeService_Handler,
		},
		{
			MethodName: "ListServices",
			Handler:    _K8SAPI_ListServices_Handler,
		},
		{
			MethodName: "DeleteService",
			Handler:    _K8SAPI_DeleteService_Handler,
		},
		{
			MethodName: "DescribeStatefulSet",
			Handler:    _K8SAPI_DescribeStatefulSet_Handler,
		},
		{
			MethodName: "ListStatefulSets",
			Handler:    _K8SAPI_ListStatefulSets_Handler,
		},
		{
			MethodName: "UpdateStatefulSet",
			Handler:    _K8SAPI_UpdateStatefulSet_Handler,
		},
		{
			MethodName: "DeleteStatefulSet",
			Handler:    _K8SAPI_DeleteStatefulSet_Handler,
		},
		{
			MethodName: "DescribeCronJob",
			Handler:    _K8SAPI_DescribeCronJob_Handler,
		},
		{
			MethodName: "ListCronJobs",
			Handler:    _K8SAPI_ListCronJobs_Handler,
		},
		{
			MethodName: "DeleteCronJob",
			Handler:    _K8SAPI_DeleteCronJob_Handler,
		},
		{
			MethodName: "ListConfigMaps",
			Handler:    _K8SAPI_ListConfigMaps_Handler,
		},
		{
			MethodName: "DescribeConfigMap",
			Handler:    _K8SAPI_DescribeConfigMap_Handler,
		},
		{
			MethodName: "DeleteConfigMap",
			Handler:    _K8SAPI_DeleteConfigMap_Handler,
		},
		{
			MethodName: "DescribeJob",
			Handler:    _K8SAPI_DescribeJob_Handler,
		},
		{
			MethodName: "ListJobs",
			Handler:    _K8SAPI_ListJobs_Handler,
		},
		{
			MethodName: "DeleteJob",
			Handler:    _K8SAPI_DeleteJob_Handler,
		},
		{
			MethodName: "CreateJob",
			Handler:    _K8SAPI_CreateJob_Handler,
		},
		{
			MethodName: "DescribeNamespace",
			Handler:    _K8SAPI_DescribeNamespace_Handler,
		},
		{
			MethodName: "ListEvents",
			Handler:    _K8SAPI_ListEvents_Handler,
		},
		{
			MethodName: "DescribeNode",
			Handler:    _K8SAPI_DescribeNode_Handler,
		},
		{
			MethodName: "UpdateNode",
			Handler:    _K8SAPI_UpdateNode_Handler,
		},
		{
			MethodName: "ListNamespaceEvents",
			Handler:    _K8SAPI_ListNamespaceEvents_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "k8s/v1/k8s.proto",
}
