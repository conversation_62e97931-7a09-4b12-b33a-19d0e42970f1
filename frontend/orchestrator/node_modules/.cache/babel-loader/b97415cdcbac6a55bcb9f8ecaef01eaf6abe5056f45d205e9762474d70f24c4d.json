{"ast": null, "code": "import _objectSpread from\"/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState}from'react';import{useAuth}from'../auth/AuthContext';import{UserCircleIcon,KeyIcon,CogIcon,ShieldCheckIcon,CheckCircleIcon,ExclamationTriangleIcon}from'@heroicons/react/24/outline';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const UserProfile=()=>{var _user$preferences,_user$preferences2,_user$preferences3,_user$name,_user$roles,_user$roles2,_user$groups;const{user}=useAuth();const[activeTab,setActiveTab]=useState('profile');const[passwordForm,setPasswordForm]=useState({currentPassword:'',newPassword:'',confirmPassword:''});const[preferences,setPreferences]=useState({theme:(user===null||user===void 0?void 0:(_user$preferences=user.preferences)===null||_user$preferences===void 0?void 0:_user$preferences.theme)||'dark',timezone:(user===null||user===void 0?void 0:(_user$preferences2=user.preferences)===null||_user$preferences2===void 0?void 0:_user$preferences2.timezone)||'UTC',language:(user===null||user===void 0?void 0:(_user$preferences3=user.preferences)===null||_user$preferences3===void 0?void 0:_user$preferences3.language)||'en',notifications:true});const[passwordMessage,setPasswordMessage]=useState(null);const[loading,setLoading]=useState(false);const handlePasswordChange=async e=>{e.preventDefault();if(passwordForm.newPassword!==passwordForm.confirmPassword){setPasswordMessage({type:'error',text:'New passwords do not match'});return;}if(passwordForm.newPassword.length<4){setPasswordMessage({type:'error',text:'Password must be at least 4 characters long'});return;}setLoading(true);try{const response=await fetch('/v1/auth/change-password',{method:'POST',headers:{'Content-Type':'application/json'},credentials:'include',body:JSON.stringify({current_password:passwordForm.currentPassword,new_password:passwordForm.newPassword})});if(response.ok){setPasswordMessage({type:'success',text:'Password changed successfully'});setPasswordForm({currentPassword:'',newPassword:'',confirmPassword:''});}else{const errorData=await response.json();setPasswordMessage({type:'error',text:errorData.error||'Failed to change password'});}}catch(error){setPasswordMessage({type:'error',text:'Failed to change password'});}finally{setLoading(false);}};const handlePreferencesUpdate=async e=>{e.preventDefault();// In a real implementation, this would update user preferences\nconsole.log('Updating preferences:',preferences);};const tabs=[{id:'profile',name:'Profile',icon:UserCircleIcon},{id:'security',name:'Security',icon:ShieldCheckIcon},{id:'preferences',name:'Preferences',icon:CogIcon}];return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white shadow rounded-lg p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"h-16 w-16 rounded-full bg-gray-300 flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-xl font-medium text-gray-700\",children:user===null||user===void 0?void 0:(_user$name=user.name)===null||_user$name===void 0?void 0:_user$name.split(' ').map(n=>n[0]).join('').toUpperCase()})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-900\",children:user===null||user===void 0?void 0:user.name}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:user===null||user===void 0?void 0:user.email}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 mt-1\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\",children:[/*#__PURE__*/_jsx(CheckCircleIcon,{className:\"h-3 w-3 mr-1\"}),\"Active\"]}),user===null||user===void 0?void 0:(_user$roles=user.roles)===null||_user$roles===void 0?void 0:_user$roles.map(role=>/*#__PURE__*/_jsx(\"span\",{className:\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",children:role},role))]})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white shadow rounded-lg\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"border-b border-gray-200\",children:/*#__PURE__*/_jsx(\"nav\",{className:\"-mb-px flex space-x-8 px-6\",children:tabs.map(tab=>{const Icon=tab.icon;return/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setActiveTab(tab.id),className:\"py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 \".concat(activeTab===tab.id?'border-blue-500 text-blue-600':'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),children:[/*#__PURE__*/_jsx(Icon,{className:\"h-4 w-4\"}),/*#__PURE__*/_jsx(\"span\",{children:tab.name})]},tab.id);})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-6\",children:[activeTab==='profile'&&/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-4\",children:\"Profile Information\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-6\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Username\"}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-1 text-sm text-gray-900\",children:user===null||user===void 0?void 0:user.username})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Email\"}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-1 text-sm text-gray-900\",children:user===null||user===void 0?void 0:user.email})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Full Name\"}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-1 text-sm text-gray-900\",children:user===null||user===void 0?void 0:user.name})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"User ID\"}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-1 text-sm text-gray-900 font-mono\",children:user===null||user===void 0?void 0:user.id})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-4\",children:\"Roles & Permissions\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Roles\"}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-1 flex flex-wrap gap-2\",children:user===null||user===void 0?void 0:(_user$roles2=user.roles)===null||_user$roles2===void 0?void 0:_user$roles2.map(role=>/*#__PURE__*/_jsx(\"span\",{className:\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800\",children:role},role))})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Groups\"}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-1 flex flex-wrap gap-2\",children:user===null||user===void 0?void 0:(_user$groups=user.groups)===null||_user$groups===void 0?void 0:_user$groups.map(group=>/*#__PURE__*/_jsx(\"span\",{className:\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800\",children:group},group))})]})]})]})]}),activeTab==='security'&&/*#__PURE__*/_jsx(\"div\",{className:\"space-y-6\",children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-4\",children:\"Change Password\"}),passwordMessage&&/*#__PURE__*/_jsx(\"div\",{className:\"mb-4 p-4 rounded-md \".concat(passwordMessage.type==='success'?'bg-green-50 text-green-800':'bg-red-50 text-red-800'),children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex\",children:[passwordMessage.type==='success'?/*#__PURE__*/_jsx(CheckCircleIcon,{className:\"h-5 w-5 mr-2\"}):/*#__PURE__*/_jsx(ExclamationTriangleIcon,{className:\"h-5 w-5 mr-2\"}),passwordMessage.text]})}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handlePasswordChange,className:\"space-y-4 max-w-md\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Current Password\"}),/*#__PURE__*/_jsx(\"input\",{type:\"password\",required:true,value:passwordForm.currentPassword,onChange:e=>setPasswordForm(_objectSpread(_objectSpread({},passwordForm),{},{currentPassword:e.target.value})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"New Password\"}),/*#__PURE__*/_jsx(\"input\",{type:\"password\",required:true,value:passwordForm.newPassword,onChange:e=>setPasswordForm(_objectSpread(_objectSpread({},passwordForm),{},{newPassword:e.target.value})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Confirm New Password\"}),/*#__PURE__*/_jsx(\"input\",{type:\"password\",required:true,value:passwordForm.confirmPassword,onChange:e=>setPasswordForm(_objectSpread(_objectSpread({},passwordForm),{},{confirmPassword:e.target.value})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\"})]}),/*#__PURE__*/_jsxs(\"button\",{type:\"submit\",disabled:loading,className:\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50\",children:[/*#__PURE__*/_jsx(KeyIcon,{className:\"h-4 w-4 mr-2\"}),loading?'Changing...':'Change Password']})]})]})}),activeTab==='preferences'&&/*#__PURE__*/_jsx(\"div\",{className:\"space-y-6\",children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-4\",children:\"User Preferences\"}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handlePreferencesUpdate,className:\"space-y-4 max-w-md\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Theme\"}),/*#__PURE__*/_jsxs(\"select\",{value:preferences.theme,onChange:e=>setPreferences(_objectSpread(_objectSpread({},preferences),{},{theme:e.target.value})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"light\",children:\"Light\"}),/*#__PURE__*/_jsx(\"option\",{value:\"dark\",children:\"Dark\"}),/*#__PURE__*/_jsx(\"option\",{value:\"auto\",children:\"Auto\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Timezone\"}),/*#__PURE__*/_jsxs(\"select\",{value:preferences.timezone,onChange:e=>setPreferences(_objectSpread(_objectSpread({},preferences),{},{timezone:e.target.value})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"UTC\",children:\"UTC\"}),/*#__PURE__*/_jsx(\"option\",{value:\"America/New_York\",children:\"Eastern Time\"}),/*#__PURE__*/_jsx(\"option\",{value:\"America/Chicago\",children:\"Central Time\"}),/*#__PURE__*/_jsx(\"option\",{value:\"America/Denver\",children:\"Mountain Time\"}),/*#__PURE__*/_jsx(\"option\",{value:\"America/Los_Angeles\",children:\"Pacific Time\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Language\"}),/*#__PURE__*/_jsxs(\"select\",{value:preferences.language,onChange:e=>setPreferences(_objectSpread(_objectSpread({},preferences),{},{language:e.target.value})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"en\",children:\"English\"}),/*#__PURE__*/_jsx(\"option\",{value:\"es\",children:\"Spanish\"}),/*#__PURE__*/_jsx(\"option\",{value:\"fr\",children:\"French\"}),/*#__PURE__*/_jsx(\"option\",{value:\"de\",children:\"German\"})]})]}),/*#__PURE__*/_jsxs(\"button\",{type:\"submit\",className:\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700\",children:[/*#__PURE__*/_jsx(CogIcon,{className:\"h-4 w-4 mr-2\"}),\"Update Preferences\"]})]})]})})]})]})]});};export default UserProfile;", "map": {"version": 3, "names": ["React", "useState", "useAuth", "UserCircleIcon", "KeyIcon", "CogIcon", "ShieldCheckIcon", "CheckCircleIcon", "ExclamationTriangleIcon", "jsx", "_jsx", "jsxs", "_jsxs", "UserProfile", "_user$preferences", "_user$preferences2", "_user$preferences3", "_user$name", "_user$roles", "_user$roles2", "_user$groups", "user", "activeTab", "setActiveTab", "passwordForm", "setPasswordForm", "currentPassword", "newPassword", "confirmPassword", "preferences", "setPreferences", "theme", "timezone", "language", "notifications", "passwordMessage", "setPasswordMessage", "loading", "setLoading", "handlePasswordChange", "e", "preventDefault", "type", "text", "length", "response", "fetch", "method", "headers", "credentials", "body", "JSON", "stringify", "current_password", "new_password", "ok", "errorData", "json", "error", "handlePreferencesUpdate", "console", "log", "tabs", "id", "name", "icon", "className", "children", "split", "map", "n", "join", "toUpperCase", "email", "roles", "role", "tab", "Icon", "onClick", "concat", "username", "groups", "group", "onSubmit", "required", "value", "onChange", "_objectSpread", "target", "disabled"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/UserProfile.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useAuth } from '../auth/AuthContext';\nimport {\n  UserCircleIcon,\n  KeyIcon,\n  CogIcon,\n  ShieldCheckIcon,\n  ClockIcon,\n  CheckCircleIcon,\n  ExclamationTriangleIcon,\n} from '@heroicons/react/24/outline';\n\ninterface PasswordChangeForm {\n  currentPassword: string;\n  newPassword: string;\n  confirmPassword: string;\n}\n\ninterface PreferencesForm {\n  theme: string;\n  timezone: string;\n  language: string;\n  notifications: boolean;\n}\n\nconst UserProfile: React.FC = () => {\n  const { user } = useAuth();\n  const [activeTab, setActiveTab] = useState('profile');\n  const [passwordForm, setPasswordForm] = useState<PasswordChangeForm>({\n    currentPassword: '',\n    newPassword: '',\n    confirmPassword: '',\n  });\n  const [preferences, setPreferences] = useState<PreferencesForm>({\n    theme: user?.preferences?.theme || 'dark',\n    timezone: user?.preferences?.timezone || 'UTC',\n    language: user?.preferences?.language || 'en',\n    notifications: true,\n  });\n  const [passwordMessage, setPasswordMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);\n  const [loading, setLoading] = useState(false);\n\n  const handlePasswordChange = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (passwordForm.newPassword !== passwordForm.confirmPassword) {\n      setPasswordMessage({ type: 'error', text: 'New passwords do not match' });\n      return;\n    }\n\n    if (passwordForm.newPassword.length < 4) {\n      setPasswordMessage({ type: 'error', text: 'Password must be at least 4 characters long' });\n      return;\n    }\n\n    setLoading(true);\n    try {\n      const response = await fetch('/v1/auth/change-password', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include',\n        body: JSON.stringify({\n          current_password: passwordForm.currentPassword,\n          new_password: passwordForm.newPassword,\n        }),\n      });\n\n      if (response.ok) {\n        setPasswordMessage({ type: 'success', text: 'Password changed successfully' });\n        setPasswordForm({\n          currentPassword: '',\n          newPassword: '',\n          confirmPassword: '',\n        });\n      } else {\n        const errorData = await response.json();\n        setPasswordMessage({ type: 'error', text: errorData.error || 'Failed to change password' });\n      }\n    } catch (error) {\n      setPasswordMessage({ type: 'error', text: 'Failed to change password' });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handlePreferencesUpdate = async (e: React.FormEvent) => {\n    e.preventDefault();\n    // In a real implementation, this would update user preferences\n    console.log('Updating preferences:', preferences);\n  };\n\n  const tabs = [\n    { id: 'profile', name: 'Profile', icon: UserCircleIcon },\n    { id: 'security', name: 'Security', icon: ShieldCheckIcon },\n    { id: 'preferences', name: 'Preferences', icon: CogIcon },\n  ];\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <div className=\"flex items-center space-x-4\">\n          <div className=\"h-16 w-16 rounded-full bg-gray-300 flex items-center justify-center\">\n            <span className=\"text-xl font-medium text-gray-700\">\n              {user?.name?.split(' ').map(n => n[0]).join('').toUpperCase()}\n            </span>\n          </div>\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">{user?.name}</h1>\n            <p className=\"text-gray-600\">{user?.email}</p>\n            <div className=\"flex items-center space-x-2 mt-1\">\n              <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n                <CheckCircleIcon className=\"h-3 w-3 mr-1\" />\n                Active\n              </span>\n              {user?.roles?.map((role) => (\n                <span\n                  key={role}\n                  className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\"\n                >\n                  {role}\n                </span>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Tabs */}\n      <div className=\"bg-white shadow rounded-lg\">\n        <div className=\"border-b border-gray-200\">\n          <nav className=\"-mb-px flex space-x-8 px-6\">\n            {tabs.map((tab) => {\n              const Icon = tab.icon;\n              return (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id)}\n                  className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${\n                    activeTab === tab.id\n                      ? 'border-blue-500 text-blue-600'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }`}\n                >\n                  <Icon className=\"h-4 w-4\" />\n                  <span>{tab.name}</span>\n                </button>\n              );\n            })}\n          </nav>\n        </div>\n\n        <div className=\"p-6\">\n          {/* Profile Tab */}\n          {activeTab === 'profile' && (\n            <div className=\"space-y-6\">\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Profile Information</h3>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Username</label>\n                    <div className=\"mt-1 text-sm text-gray-900\">{user?.username}</div>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Email</label>\n                    <div className=\"mt-1 text-sm text-gray-900\">{user?.email}</div>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Full Name</label>\n                    <div className=\"mt-1 text-sm text-gray-900\">{user?.name}</div>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">User ID</label>\n                    <div className=\"mt-1 text-sm text-gray-900 font-mono\">{user?.id}</div>\n                  </div>\n                </div>\n              </div>\n\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Roles & Permissions</h3>\n                <div className=\"space-y-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Roles</label>\n                    <div className=\"mt-1 flex flex-wrap gap-2\">\n                      {user?.roles?.map((role) => (\n                        <span\n                          key={role}\n                          className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800\"\n                        >\n                          {role}\n                        </span>\n                      ))}\n                    </div>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Groups</label>\n                    <div className=\"mt-1 flex flex-wrap gap-2\">\n                      {user?.groups?.map((group) => (\n                        <span\n                          key={group}\n                          className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800\"\n                        >\n                          {group}\n                        </span>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Security Tab */}\n          {activeTab === 'security' && (\n            <div className=\"space-y-6\">\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Change Password</h3>\n                {passwordMessage && (\n                  <div className={`mb-4 p-4 rounded-md ${\n                    passwordMessage.type === 'success' ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'\n                  }`}>\n                    <div className=\"flex\">\n                      {passwordMessage.type === 'success' ? (\n                        <CheckCircleIcon className=\"h-5 w-5 mr-2\" />\n                      ) : (\n                        <ExclamationTriangleIcon className=\"h-5 w-5 mr-2\" />\n                      )}\n                      {passwordMessage.text}\n                    </div>\n                  </div>\n                )}\n                <form onSubmit={handlePasswordChange} className=\"space-y-4 max-w-md\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Current Password</label>\n                    <input\n                      type=\"password\"\n                      required\n                      value={passwordForm.currentPassword}\n                      onChange={(e) => setPasswordForm({...passwordForm, currentPassword: e.target.value})}\n                      className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">New Password</label>\n                    <input\n                      type=\"password\"\n                      required\n                      value={passwordForm.newPassword}\n                      onChange={(e) => setPasswordForm({...passwordForm, newPassword: e.target.value})}\n                      className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Confirm New Password</label>\n                    <input\n                      type=\"password\"\n                      required\n                      value={passwordForm.confirmPassword}\n                      onChange={(e) => setPasswordForm({...passwordForm, confirmPassword: e.target.value})}\n                      className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                    />\n                  </div>\n                  <button\n                    type=\"submit\"\n                    disabled={loading}\n                    className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50\"\n                  >\n                    <KeyIcon className=\"h-4 w-4 mr-2\" />\n                    {loading ? 'Changing...' : 'Change Password'}\n                  </button>\n                </form>\n              </div>\n            </div>\n          )}\n\n          {/* Preferences Tab */}\n          {activeTab === 'preferences' && (\n            <div className=\"space-y-6\">\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">User Preferences</h3>\n                <form onSubmit={handlePreferencesUpdate} className=\"space-y-4 max-w-md\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Theme</label>\n                    <select\n                      value={preferences.theme}\n                      onChange={(e) => setPreferences({...preferences, theme: e.target.value})}\n                      className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                    >\n                      <option value=\"light\">Light</option>\n                      <option value=\"dark\">Dark</option>\n                      <option value=\"auto\">Auto</option>\n                    </select>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Timezone</label>\n                    <select\n                      value={preferences.timezone}\n                      onChange={(e) => setPreferences({...preferences, timezone: e.target.value})}\n                      className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                    >\n                      <option value=\"UTC\">UTC</option>\n                      <option value=\"America/New_York\">Eastern Time</option>\n                      <option value=\"America/Chicago\">Central Time</option>\n                      <option value=\"America/Denver\">Mountain Time</option>\n                      <option value=\"America/Los_Angeles\">Pacific Time</option>\n                    </select>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700\">Language</label>\n                    <select\n                      value={preferences.language}\n                      onChange={(e) => setPreferences({...preferences, language: e.target.value})}\n                      className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                    >\n                      <option value=\"en\">English</option>\n                      <option value=\"es\">Spanish</option>\n                      <option value=\"fr\">French</option>\n                      <option value=\"de\">German</option>\n                    </select>\n                  </div>\n                  <button\n                    type=\"submit\"\n                    className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700\"\n                  >\n                    <CogIcon className=\"h-4 w-4 mr-2\" />\n                    Update Preferences\n                  </button>\n                </form>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default UserProfile;\n"], "mappings": "yJAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,OAAO,KAAQ,qBAAqB,CAC7C,OACEC,cAAc,CACdC,OAAO,CACPC,OAAO,CACPC,eAAe,CAEfC,eAAe,CACfC,uBAAuB,KAClB,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAerC,KAAM,CAAAC,WAAqB,CAAGA,CAAA,GAAM,KAAAC,iBAAA,CAAAC,kBAAA,CAAAC,kBAAA,CAAAC,UAAA,CAAAC,WAAA,CAAAC,YAAA,CAAAC,YAAA,CAClC,KAAM,CAAEC,IAAK,CAAC,CAAGnB,OAAO,CAAC,CAAC,CAC1B,KAAM,CAACoB,SAAS,CAAEC,YAAY,CAAC,CAAGtB,QAAQ,CAAC,SAAS,CAAC,CACrD,KAAM,CAACuB,YAAY,CAAEC,eAAe,CAAC,CAAGxB,QAAQ,CAAqB,CACnEyB,eAAe,CAAE,EAAE,CACnBC,WAAW,CAAE,EAAE,CACfC,eAAe,CAAE,EACnB,CAAC,CAAC,CACF,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAG7B,QAAQ,CAAkB,CAC9D8B,KAAK,CAAE,CAAAV,IAAI,SAAJA,IAAI,kBAAAP,iBAAA,CAAJO,IAAI,CAAEQ,WAAW,UAAAf,iBAAA,iBAAjBA,iBAAA,CAAmBiB,KAAK,GAAI,MAAM,CACzCC,QAAQ,CAAE,CAAAX,IAAI,SAAJA,IAAI,kBAAAN,kBAAA,CAAJM,IAAI,CAAEQ,WAAW,UAAAd,kBAAA,iBAAjBA,kBAAA,CAAmBiB,QAAQ,GAAI,KAAK,CAC9CC,QAAQ,CAAE,CAAAZ,IAAI,SAAJA,IAAI,kBAAAL,kBAAA,CAAJK,IAAI,CAAEQ,WAAW,UAAAb,kBAAA,iBAAjBA,kBAAA,CAAmBiB,QAAQ,GAAI,IAAI,CAC7CC,aAAa,CAAE,IACjB,CAAC,CAAC,CACF,KAAM,CAACC,eAAe,CAAEC,kBAAkB,CAAC,CAAGnC,QAAQ,CAAqD,IAAI,CAAC,CAChH,KAAM,CAACoC,OAAO,CAAEC,UAAU,CAAC,CAAGrC,QAAQ,CAAC,KAAK,CAAC,CAE7C,KAAM,CAAAsC,oBAAoB,CAAG,KAAO,CAAAC,CAAkB,EAAK,CACzDA,CAAC,CAACC,cAAc,CAAC,CAAC,CAElB,GAAIjB,YAAY,CAACG,WAAW,GAAKH,YAAY,CAACI,eAAe,CAAE,CAC7DQ,kBAAkB,CAAC,CAAEM,IAAI,CAAE,OAAO,CAAEC,IAAI,CAAE,4BAA6B,CAAC,CAAC,CACzE,OACF,CAEA,GAAInB,YAAY,CAACG,WAAW,CAACiB,MAAM,CAAG,CAAC,CAAE,CACvCR,kBAAkB,CAAC,CAAEM,IAAI,CAAE,OAAO,CAAEC,IAAI,CAAE,6CAA8C,CAAC,CAAC,CAC1F,OACF,CAEAL,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAO,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,0BAA0B,CAAE,CACvDC,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDC,WAAW,CAAE,SAAS,CACtBC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CACnBC,gBAAgB,CAAE7B,YAAY,CAACE,eAAe,CAC9C4B,YAAY,CAAE9B,YAAY,CAACG,WAC7B,CAAC,CACH,CAAC,CAAC,CAEF,GAAIkB,QAAQ,CAACU,EAAE,CAAE,CACfnB,kBAAkB,CAAC,CAAEM,IAAI,CAAE,SAAS,CAAEC,IAAI,CAAE,+BAAgC,CAAC,CAAC,CAC9ElB,eAAe,CAAC,CACdC,eAAe,CAAE,EAAE,CACnBC,WAAW,CAAE,EAAE,CACfC,eAAe,CAAE,EACnB,CAAC,CAAC,CACJ,CAAC,IAAM,CACL,KAAM,CAAA4B,SAAS,CAAG,KAAM,CAAAX,QAAQ,CAACY,IAAI,CAAC,CAAC,CACvCrB,kBAAkB,CAAC,CAAEM,IAAI,CAAE,OAAO,CAAEC,IAAI,CAAEa,SAAS,CAACE,KAAK,EAAI,2BAA4B,CAAC,CAAC,CAC7F,CACF,CAAE,MAAOA,KAAK,CAAE,CACdtB,kBAAkB,CAAC,CAAEM,IAAI,CAAE,OAAO,CAAEC,IAAI,CAAE,2BAA4B,CAAC,CAAC,CAC1E,CAAC,OAAS,CACRL,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAqB,uBAAuB,CAAG,KAAO,CAAAnB,CAAkB,EAAK,CAC5DA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClB;AACAmB,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAEhC,WAAW,CAAC,CACnD,CAAC,CAED,KAAM,CAAAiC,IAAI,CAAG,CACX,CAAEC,EAAE,CAAE,SAAS,CAAEC,IAAI,CAAE,SAAS,CAAEC,IAAI,CAAE9D,cAAe,CAAC,CACxD,CAAE4D,EAAE,CAAE,UAAU,CAAEC,IAAI,CAAE,UAAU,CAAEC,IAAI,CAAE3D,eAAgB,CAAC,CAC3D,CAAEyD,EAAE,CAAE,aAAa,CAAEC,IAAI,CAAE,aAAa,CAAEC,IAAI,CAAE5D,OAAQ,CAAC,CAC1D,CAED,mBACEO,KAAA,QAAKsD,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExBzD,IAAA,QAAKwD,SAAS,CAAC,gCAAgC,CAAAC,QAAA,cAC7CvD,KAAA,QAAKsD,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CzD,IAAA,QAAKwD,SAAS,CAAC,qEAAqE,CAAAC,QAAA,cAClFzD,IAAA,SAAMwD,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAChD9C,IAAI,SAAJA,IAAI,kBAAAJ,UAAA,CAAJI,IAAI,CAAE2C,IAAI,UAAA/C,UAAA,iBAAVA,UAAA,CAAYmD,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,EAAIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC,CACzD,CAAC,CACJ,CAAC,cACN5D,KAAA,QAAAuD,QAAA,eACEzD,IAAA,OAAIwD,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAE9C,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE2C,IAAI,CAAK,CAAC,cAClEtD,IAAA,MAAGwD,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAE9C,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEoD,KAAK,CAAI,CAAC,cAC9C7D,KAAA,QAAKsD,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/CvD,KAAA,SAAMsD,SAAS,CAAC,qGAAqG,CAAAC,QAAA,eACnHzD,IAAA,CAACH,eAAe,EAAC2D,SAAS,CAAC,cAAc,CAAE,CAAC,SAE9C,EAAM,CAAC,CACN7C,IAAI,SAAJA,IAAI,kBAAAH,WAAA,CAAJG,IAAI,CAAEqD,KAAK,UAAAxD,WAAA,iBAAXA,WAAA,CAAamD,GAAG,CAAEM,IAAI,eACrBjE,IAAA,SAEEwD,SAAS,CAAC,mGAAmG,CAAAC,QAAA,CAE5GQ,IAAI,EAHAA,IAID,CACP,CAAC,EACC,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAGN/D,KAAA,QAAKsD,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzCzD,IAAA,QAAKwD,SAAS,CAAC,0BAA0B,CAAAC,QAAA,cACvCzD,IAAA,QAAKwD,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CACxCL,IAAI,CAACO,GAAG,CAAEO,GAAG,EAAK,CACjB,KAAM,CAAAC,IAAI,CAAGD,GAAG,CAACX,IAAI,CACrB,mBACErD,KAAA,WAEEkE,OAAO,CAAEA,CAAA,GAAMvD,YAAY,CAACqD,GAAG,CAACb,EAAE,CAAE,CACpCG,SAAS,yEAAAa,MAAA,CACPzD,SAAS,GAAKsD,GAAG,CAACb,EAAE,CAChB,+BAA+B,CAC/B,4EAA4E,CAC/E,CAAAI,QAAA,eAEHzD,IAAA,CAACmE,IAAI,EAACX,SAAS,CAAC,SAAS,CAAE,CAAC,cAC5BxD,IAAA,SAAAyD,QAAA,CAAOS,GAAG,CAACZ,IAAI,CAAO,CAAC,GATlBY,GAAG,CAACb,EAUH,CAAC,CAEb,CAAC,CAAC,CACC,CAAC,CACH,CAAC,cAENnD,KAAA,QAAKsD,SAAS,CAAC,KAAK,CAAAC,QAAA,EAEjB7C,SAAS,GAAK,SAAS,eACtBV,KAAA,QAAKsD,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBvD,KAAA,QAAAuD,QAAA,eACEzD,IAAA,OAAIwD,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,qBAAmB,CAAI,CAAC,cAC/EvD,KAAA,QAAKsD,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDvD,KAAA,QAAAuD,QAAA,eACEzD,IAAA,UAAOwD,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,UAAQ,CAAO,CAAC,cAC3EzD,IAAA,QAAKwD,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAE9C,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE2D,QAAQ,CAAM,CAAC,EAC/D,CAAC,cACNpE,KAAA,QAAAuD,QAAA,eACEzD,IAAA,UAAOwD,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,OAAK,CAAO,CAAC,cACxEzD,IAAA,QAAKwD,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAE9C,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEoD,KAAK,CAAM,CAAC,EAC5D,CAAC,cACN7D,KAAA,QAAAuD,QAAA,eACEzD,IAAA,UAAOwD,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,WAAS,CAAO,CAAC,cAC5EzD,IAAA,QAAKwD,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAE9C,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE2C,IAAI,CAAM,CAAC,EAC3D,CAAC,cACNpD,KAAA,QAAAuD,QAAA,eACEzD,IAAA,UAAOwD,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,SAAO,CAAO,CAAC,cAC1EzD,IAAA,QAAKwD,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAE9C,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE0C,EAAE,CAAM,CAAC,EACnE,CAAC,EACH,CAAC,EACH,CAAC,cAENnD,KAAA,QAAAuD,QAAA,eACEzD,IAAA,OAAIwD,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,qBAAmB,CAAI,CAAC,cAC/EvD,KAAA,QAAKsD,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBvD,KAAA,QAAAuD,QAAA,eACEzD,IAAA,UAAOwD,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,OAAK,CAAO,CAAC,cACxEzD,IAAA,QAAKwD,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CACvC9C,IAAI,SAAJA,IAAI,kBAAAF,YAAA,CAAJE,IAAI,CAAEqD,KAAK,UAAAvD,YAAA,iBAAXA,YAAA,CAAakD,GAAG,CAAEM,IAAI,eACrBjE,IAAA,SAEEwD,SAAS,CAAC,+FAA+F,CAAAC,QAAA,CAExGQ,IAAI,EAHAA,IAID,CACP,CAAC,CACC,CAAC,EACH,CAAC,cACN/D,KAAA,QAAAuD,QAAA,eACEzD,IAAA,UAAOwD,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,QAAM,CAAO,CAAC,cACzEzD,IAAA,QAAKwD,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CACvC9C,IAAI,SAAJA,IAAI,kBAAAD,YAAA,CAAJC,IAAI,CAAE4D,MAAM,UAAA7D,YAAA,iBAAZA,YAAA,CAAciD,GAAG,CAAEa,KAAK,eACvBxE,IAAA,SAEEwD,SAAS,CAAC,iGAAiG,CAAAC,QAAA,CAE1Ge,KAAK,EAHDA,KAID,CACP,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,EACH,CACN,CAGA5D,SAAS,GAAK,UAAU,eACvBZ,IAAA,QAAKwD,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxBvD,KAAA,QAAAuD,QAAA,eACEzD,IAAA,OAAIwD,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,CAC1EhC,eAAe,eACdzB,IAAA,QAAKwD,SAAS,wBAAAa,MAAA,CACZ5C,eAAe,CAACO,IAAI,GAAK,SAAS,CAAG,4BAA4B,CAAG,wBAAwB,CAC3F,CAAAyB,QAAA,cACDvD,KAAA,QAAKsD,SAAS,CAAC,MAAM,CAAAC,QAAA,EAClBhC,eAAe,CAACO,IAAI,GAAK,SAAS,cACjChC,IAAA,CAACH,eAAe,EAAC2D,SAAS,CAAC,cAAc,CAAE,CAAC,cAE5CxD,IAAA,CAACF,uBAAuB,EAAC0D,SAAS,CAAC,cAAc,CAAE,CACpD,CACA/B,eAAe,CAACQ,IAAI,EAClB,CAAC,CACH,CACN,cACD/B,KAAA,SAAMuE,QAAQ,CAAE5C,oBAAqB,CAAC2B,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eAClEvD,KAAA,QAAAuD,QAAA,eACEzD,IAAA,UAAOwD,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,kBAAgB,CAAO,CAAC,cACnFzD,IAAA,UACEgC,IAAI,CAAC,UAAU,CACf0C,QAAQ,MACRC,KAAK,CAAE7D,YAAY,CAACE,eAAgB,CACpC4D,QAAQ,CAAG9C,CAAC,EAAKf,eAAe,CAAA8D,aAAA,CAAAA,aAAA,IAAK/D,YAAY,MAAEE,eAAe,CAAEc,CAAC,CAACgD,MAAM,CAACH,KAAK,EAAC,CAAE,CACrFnB,SAAS,CAAC,4HAA4H,CACvI,CAAC,EACC,CAAC,cACNtD,KAAA,QAAAuD,QAAA,eACEzD,IAAA,UAAOwD,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,cAAY,CAAO,CAAC,cAC/EzD,IAAA,UACEgC,IAAI,CAAC,UAAU,CACf0C,QAAQ,MACRC,KAAK,CAAE7D,YAAY,CAACG,WAAY,CAChC2D,QAAQ,CAAG9C,CAAC,EAAKf,eAAe,CAAA8D,aAAA,CAAAA,aAAA,IAAK/D,YAAY,MAAEG,WAAW,CAAEa,CAAC,CAACgD,MAAM,CAACH,KAAK,EAAC,CAAE,CACjFnB,SAAS,CAAC,4HAA4H,CACvI,CAAC,EACC,CAAC,cACNtD,KAAA,QAAAuD,QAAA,eACEzD,IAAA,UAAOwD,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,sBAAoB,CAAO,CAAC,cACvFzD,IAAA,UACEgC,IAAI,CAAC,UAAU,CACf0C,QAAQ,MACRC,KAAK,CAAE7D,YAAY,CAACI,eAAgB,CACpC0D,QAAQ,CAAG9C,CAAC,EAAKf,eAAe,CAAA8D,aAAA,CAAAA,aAAA,IAAK/D,YAAY,MAAEI,eAAe,CAAEY,CAAC,CAACgD,MAAM,CAACH,KAAK,EAAC,CAAE,CACrFnB,SAAS,CAAC,4HAA4H,CACvI,CAAC,EACC,CAAC,cACNtD,KAAA,WACE8B,IAAI,CAAC,QAAQ,CACb+C,QAAQ,CAAEpD,OAAQ,CAClB6B,SAAS,CAAC,oKAAoK,CAAAC,QAAA,eAE9KzD,IAAA,CAACN,OAAO,EAAC8D,SAAS,CAAC,cAAc,CAAE,CAAC,CACnC7B,OAAO,CAAG,aAAa,CAAG,iBAAiB,EACtC,CAAC,EACL,CAAC,EACJ,CAAC,CACH,CACN,CAGAf,SAAS,GAAK,aAAa,eAC1BZ,IAAA,QAAKwD,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxBvD,KAAA,QAAAuD,QAAA,eACEzD,IAAA,OAAIwD,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,kBAAgB,CAAI,CAAC,cAC5EvD,KAAA,SAAMuE,QAAQ,CAAExB,uBAAwB,CAACO,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACrEvD,KAAA,QAAAuD,QAAA,eACEzD,IAAA,UAAOwD,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,OAAK,CAAO,CAAC,cACxEvD,KAAA,WACEyE,KAAK,CAAExD,WAAW,CAACE,KAAM,CACzBuD,QAAQ,CAAG9C,CAAC,EAAKV,cAAc,CAAAyD,aAAA,CAAAA,aAAA,IAAK1D,WAAW,MAAEE,KAAK,CAAES,CAAC,CAACgD,MAAM,CAACH,KAAK,EAAC,CAAE,CACzEnB,SAAS,CAAC,4HAA4H,CAAAC,QAAA,eAEtIzD,IAAA,WAAQ2E,KAAK,CAAC,OAAO,CAAAlB,QAAA,CAAC,OAAK,CAAQ,CAAC,cACpCzD,IAAA,WAAQ2E,KAAK,CAAC,MAAM,CAAAlB,QAAA,CAAC,MAAI,CAAQ,CAAC,cAClCzD,IAAA,WAAQ2E,KAAK,CAAC,MAAM,CAAAlB,QAAA,CAAC,MAAI,CAAQ,CAAC,EAC5B,CAAC,EACN,CAAC,cACNvD,KAAA,QAAAuD,QAAA,eACEzD,IAAA,UAAOwD,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,UAAQ,CAAO,CAAC,cAC3EvD,KAAA,WACEyE,KAAK,CAAExD,WAAW,CAACG,QAAS,CAC5BsD,QAAQ,CAAG9C,CAAC,EAAKV,cAAc,CAAAyD,aAAA,CAAAA,aAAA,IAAK1D,WAAW,MAAEG,QAAQ,CAAEQ,CAAC,CAACgD,MAAM,CAACH,KAAK,EAAC,CAAE,CAC5EnB,SAAS,CAAC,4HAA4H,CAAAC,QAAA,eAEtIzD,IAAA,WAAQ2E,KAAK,CAAC,KAAK,CAAAlB,QAAA,CAAC,KAAG,CAAQ,CAAC,cAChCzD,IAAA,WAAQ2E,KAAK,CAAC,kBAAkB,CAAAlB,QAAA,CAAC,cAAY,CAAQ,CAAC,cACtDzD,IAAA,WAAQ2E,KAAK,CAAC,iBAAiB,CAAAlB,QAAA,CAAC,cAAY,CAAQ,CAAC,cACrDzD,IAAA,WAAQ2E,KAAK,CAAC,gBAAgB,CAAAlB,QAAA,CAAC,eAAa,CAAQ,CAAC,cACrDzD,IAAA,WAAQ2E,KAAK,CAAC,qBAAqB,CAAAlB,QAAA,CAAC,cAAY,CAAQ,CAAC,EACnD,CAAC,EACN,CAAC,cACNvD,KAAA,QAAAuD,QAAA,eACEzD,IAAA,UAAOwD,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,UAAQ,CAAO,CAAC,cAC3EvD,KAAA,WACEyE,KAAK,CAAExD,WAAW,CAACI,QAAS,CAC5BqD,QAAQ,CAAG9C,CAAC,EAAKV,cAAc,CAAAyD,aAAA,CAAAA,aAAA,IAAK1D,WAAW,MAAEI,QAAQ,CAAEO,CAAC,CAACgD,MAAM,CAACH,KAAK,EAAC,CAAE,CAC5EnB,SAAS,CAAC,4HAA4H,CAAAC,QAAA,eAEtIzD,IAAA,WAAQ2E,KAAK,CAAC,IAAI,CAAAlB,QAAA,CAAC,SAAO,CAAQ,CAAC,cACnCzD,IAAA,WAAQ2E,KAAK,CAAC,IAAI,CAAAlB,QAAA,CAAC,SAAO,CAAQ,CAAC,cACnCzD,IAAA,WAAQ2E,KAAK,CAAC,IAAI,CAAAlB,QAAA,CAAC,QAAM,CAAQ,CAAC,cAClCzD,IAAA,WAAQ2E,KAAK,CAAC,IAAI,CAAAlB,QAAA,CAAC,QAAM,CAAQ,CAAC,EAC5B,CAAC,EACN,CAAC,cACNvD,KAAA,WACE8B,IAAI,CAAC,QAAQ,CACbwB,SAAS,CAAC,gJAAgJ,CAAAC,QAAA,eAE1JzD,IAAA,CAACL,OAAO,EAAC6D,SAAS,CAAC,cAAc,CAAE,CAAC,qBAEtC,EAAQ,CAAC,EACL,CAAC,EACJ,CAAC,CACH,CACN,EACE,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAArD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}