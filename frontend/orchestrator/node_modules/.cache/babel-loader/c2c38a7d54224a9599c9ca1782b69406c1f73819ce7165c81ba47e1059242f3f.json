{"ast": null, "code": "import loader from './loader/index.js';\nexport { default } from './loader/index.js';", "map": {"version": 3, "names": ["loader", "default"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/node_modules/@monaco-editor/loader/lib/es/index.js"], "sourcesContent": ["import loader from './loader/index.js';\nexport { default } from './loader/index.js';\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,mBAAmB;AACtC,SAASC,OAAO,QAAQ,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}