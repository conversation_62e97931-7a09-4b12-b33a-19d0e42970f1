[{"/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/index.tsx": "1", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/reportWebVitals.ts": "2", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/store.ts": "3", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/App.tsx": "4", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/searchSlice.ts": "5", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/workflowSlice.ts": "6", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/envoySlice.ts": "7", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/auditSlice.ts": "8", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/autoscalerSlice.ts": "9", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/databaseSlice.ts": "10", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/components/Layout.tsx": "11", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/SearchDashboard.tsx": "12", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/Dashboard.tsx": "13", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/WorkflowExecutor.tsx": "14", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/AutoscalerDashboard.tsx": "15", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/DiscoveryWizard.tsx": "16", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/EnvoyConfigEditor.tsx": "17", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/AuditViewer.tsx": "18", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/DatabaseAdmin.tsx": "19", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/auth/AuthContext.tsx": "20", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/auth/LoginPage.tsx": "21", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/AdminDashboard.tsx": "22", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/UserManagement.tsx": "23", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/UserProfile.tsx": "24", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/Settings.tsx": "25"}, {"size": 778, "mtime": 1749058599021, "results": "26", "hashOfConfig": "27"}, {"size": 370, "mtime": 1749058755887, "results": "28", "hashOfConfig": "27"}, {"size": 863, "mtime": 1749063617349, "results": "29", "hashOfConfig": "27"}, {"size": 4419, "mtime": 1749088926411, "results": "30", "hashOfConfig": "27"}, {"size": 2725, "mtime": 1749063531345, "results": "31", "hashOfConfig": "27"}, {"size": 3481, "mtime": 1749063548264, "results": "32", "hashOfConfig": "27"}, {"size": 2849, "mtime": 1749063582338, "results": "33", "hashOfConfig": "27"}, {"size": 1957, "mtime": 1749063595368, "results": "34", "hashOfConfig": "27"}, {"size": 3681, "mtime": 1749063567934, "results": "35", "hashOfConfig": "27"}, {"size": 2204, "mtime": 1749063606689, "results": "36", "hashOfConfig": "27"}, {"size": 9584, "mtime": 1749088897316, "results": "37", "hashOfConfig": "27"}, {"size": 8594, "mtime": 1749063749914, "results": "38", "hashOfConfig": "27"}, {"size": 8985, "mtime": 1749058679704, "results": "39", "hashOfConfig": "27"}, {"size": 10204, "mtime": 1749063787790, "results": "40", "hashOfConfig": "27"}, {"size": 9190, "mtime": 1749063823453, "results": "41", "hashOfConfig": "27"}, {"size": 17002, "mtime": 1749064442452, "results": "42", "hashOfConfig": "27"}, {"size": 11841, "mtime": 1749064586885, "results": "43", "hashOfConfig": "27"}, {"size": 9026, "mtime": 1749063897136, "results": "44", "hashOfConfig": "27"}, {"size": 8828, "mtime": 1749063931077, "results": "45", "hashOfConfig": "27"}, {"size": 10592, "mtime": 1749089058339, "results": "46", "hashOfConfig": "27"}, {"size": 6612, "mtime": 1749087527402, "results": "47", "hashOfConfig": "27"}, {"size": 10001, "mtime": 1749088650792, "results": "48", "hashOfConfig": "27"}, {"size": 21180, "mtime": 1749089633479, "results": "49", "hashOfConfig": "27"}, {"size": 14398, "mtime": 1749088750639, "results": "50", "hashOfConfig": "27"}, {"size": 18639, "mtime": 1749089016084, "results": "51", "hashOfConfig": "27"}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ojadns", {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/index.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/reportWebVitals.ts", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/store.ts", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/App.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/searchSlice.ts", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/workflowSlice.ts", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/envoySlice.ts", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/auditSlice.ts", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/autoscalerSlice.ts", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/databaseSlice.ts", ["127"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/components/Layout.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/SearchDashboard.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/Dashboard.tsx", ["128"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/WorkflowExecutor.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/AutoscalerDashboard.tsx", ["129"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/DiscoveryWizard.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/EnvoyConfigEditor.tsx", ["130"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/AuditViewer.tsx", ["131"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/DatabaseAdmin.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/auth/AuthContext.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/auth/LoginPage.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/AdminDashboard.tsx", ["132", "133"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/UserManagement.tsx", ["134"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/UserProfile.tsx", ["135"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/Settings.tsx", ["136", "137", "138", "139"], [], {"ruleId": "140", "severity": 1, "message": "141", "line": 1, "column": 41, "nodeType": "142", "messageId": "143", "endLine": 1, "endColumn": 54}, {"ruleId": "140", "severity": 1, "message": "144", "line": 3, "column": 3, "nodeType": "142", "messageId": "143", "endLine": 3, "endColumn": 15}, {"ruleId": "140", "severity": 1, "message": "145", "line": 18, "column": 43, "nodeType": "142", "messageId": "143", "endLine": 18, "endColumn": 50}, {"ruleId": "140", "severity": 1, "message": "146", "line": 19, "column": 52, "nodeType": "142", "messageId": "143", "endLine": 19, "endColumn": 57}, {"ruleId": "140", "severity": 1, "message": "147", "line": 14, "column": 47, "nodeType": "142", "messageId": "143", "endLine": 14, "endColumn": 52}, {"ruleId": "140", "severity": 1, "message": "148", "line": 10, "column": 3, "nodeType": "142", "messageId": "143", "endLine": 10, "endColumn": 26}, {"ruleId": "140", "severity": 1, "message": "149", "line": 11, "column": 3, "nodeType": "142", "messageId": "143", "endLine": 11, "endColumn": 18}, {"ruleId": "140", "severity": 1, "message": "150", "line": 4, "column": 3, "nodeType": "142", "messageId": "143", "endLine": 4, "endColumn": 12}, {"ruleId": "140", "severity": 1, "message": "151", "line": 8, "column": 3, "nodeType": "142", "messageId": "143", "endLine": 8, "endColumn": 12}, {"ruleId": "140", "severity": 1, "message": "152", "line": 8, "column": 3, "nodeType": "142", "messageId": "143", "endLine": 8, "endColumn": 18}, {"ruleId": "140", "severity": 1, "message": "153", "line": 9, "column": 3, "nodeType": "142", "messageId": "143", "endLine": 9, "endColumn": 12}, {"ruleId": "140", "severity": 1, "message": "154", "line": 42, "column": 11, "nodeType": "142", "messageId": "143", "endLine": 42, "endColumn": 15}, {"ruleId": "155", "severity": 1, "message": "156", "line": 74, "column": 6, "nodeType": "157", "endLine": 74, "endColumn": 8, "suggestions": "158"}, "@typescript-eslint/no-unused-vars", "'PayloadAction' is defined but never used.", "Identifier", "unusedVar", "'ChartBarIcon' is defined but never used.", "'metrics' is assigned a value but never used.", "'error' is assigned a value but never used.", "'query' is assigned a value but never used.", "'ExclamationTriangleIcon' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'UsersIcon' is defined but never used.", "'ClockIcon' is defined but never used.", "'CircleStackIcon' is defined but never used.", "'CloudIcon' is defined but never used.", "'user' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchSettings'. Either include it or remove the dependency array.", "ArrayExpression", ["159"], {"desc": "160", "fix": "161"}, "Update the dependencies array to be: [fetchSettings]", {"range": "162", "text": "163"}, [1787, 1789], "[fetchSettings]"]