// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v3.17.3
// source: featureflag/v1/featureflag.proto

package featureflagv1

import (
	_ "github.com/lyft/clutch/backend/api/api/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetFlagsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetFlagsRequest) Reset() {
	*x = GetFlagsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_featureflag_v1_featureflag_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFlagsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFlagsRequest) ProtoMessage() {}

func (x *GetFlagsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_featureflag_v1_featureflag_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFlagsRequest.ProtoReflect.Descriptor instead.
func (*GetFlagsRequest) Descriptor() ([]byte, []int) {
	return file_featureflag_v1_featureflag_proto_rawDescGZIP(), []int{0}
}

type Flag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Type:
	//
	//	*Flag_BooleanValue
	Type isFlag_Type `protobuf_oneof:"type"`
}

func (x *Flag) Reset() {
	*x = Flag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_featureflag_v1_featureflag_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Flag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Flag) ProtoMessage() {}

func (x *Flag) ProtoReflect() protoreflect.Message {
	mi := &file_featureflag_v1_featureflag_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Flag.ProtoReflect.Descriptor instead.
func (*Flag) Descriptor() ([]byte, []int) {
	return file_featureflag_v1_featureflag_proto_rawDescGZIP(), []int{1}
}

func (m *Flag) GetType() isFlag_Type {
	if m != nil {
		return m.Type
	}
	return nil
}

func (x *Flag) GetBooleanValue() bool {
	if x, ok := x.GetType().(*Flag_BooleanValue); ok {
		return x.BooleanValue
	}
	return false
}

type isFlag_Type interface {
	isFlag_Type()
}

type Flag_BooleanValue struct {
	BooleanValue bool `protobuf:"varint,1,opt,name=boolean_value,json=booleanValue,proto3,oneof"`
}

func (*Flag_BooleanValue) isFlag_Type() {}

type GetFlagsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Map of ID to flag.
	Flags map[string]*Flag `protobuf:"bytes,1,rep,name=flags,proto3" json:"flags,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetFlagsResponse) Reset() {
	*x = GetFlagsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_featureflag_v1_featureflag_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFlagsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFlagsResponse) ProtoMessage() {}

func (x *GetFlagsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_featureflag_v1_featureflag_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFlagsResponse.ProtoReflect.Descriptor instead.
func (*GetFlagsResponse) Descriptor() ([]byte, []int) {
	return file_featureflag_v1_featureflag_proto_rawDescGZIP(), []int{2}
}

func (x *GetFlagsResponse) GetFlags() map[string]*Flag {
	if x != nil {
		return x.Flags
	}
	return nil
}

var File_featureflag_v1_featureflag_proto protoreflect.FileDescriptor

var file_featureflag_v1_featureflag_proto_rawDesc = []byte{
	0x0a, 0x20, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x66, 0x6c, 0x61, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x66, 0x6c, 0x61, 0x67, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x15, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x66, 0x6c, 0x61, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f,
	0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x11, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x46, 0x6c, 0x61, 0x67, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x22, 0x35, 0x0a, 0x04, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x25, 0x0a, 0x0d,
	0x62, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x0c, 0x62, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x42, 0x06, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0xb3, 0x01, 0x0a, 0x10,
	0x47, 0x65, 0x74, 0x46, 0x6c, 0x61, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x48, 0x0a, 0x05, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x32, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x66, 0x6c, 0x61, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x6c, 0x61, 0x67, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x46, 0x6c, 0x61, 0x67, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x05, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x1a, 0x55, 0x0a, 0x0a, 0x46, 0x6c,
	0x61, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x31, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x6c, 0x75, 0x74,
	0x63, 0x68, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x66, 0x6c, 0x61, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x46, 0x6c, 0x61, 0x67, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x32, 0x99, 0x01, 0x0a, 0x0e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x46, 0x6c, 0x61,
	0x67, 0x41, 0x50, 0x49, 0x12, 0x86, 0x01, 0x0a, 0x08, 0x47, 0x65, 0x74, 0x46, 0x6c, 0x61, 0x67,
	0x73, 0x12, 0x26, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x66, 0x6c, 0x61, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x6c, 0x61,
	0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x63, 0x6c, 0x75, 0x74,
	0x63, 0x68, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x66, 0x6c, 0x61, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x6c, 0x61, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x29, 0xaa, 0xe1, 0x1c, 0x02, 0x08, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d,
	0x3a, 0x01, 0x2a, 0x22, 0x18, 0x2f, 0x76, 0x31, 0x2f, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x66, 0x6c, 0x61, 0x67, 0x2f, 0x67, 0x65, 0x74, 0x46, 0x6c, 0x61, 0x67, 0x73, 0x42, 0x41, 0x5a,
	0x3f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6c, 0x79, 0x66, 0x74,
	0x2f, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2f, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x66, 0x6c, 0x61, 0x67, 0x2f,
	0x76, 0x31, 0x3b, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x66, 0x6c, 0x61, 0x67, 0x76, 0x31,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_featureflag_v1_featureflag_proto_rawDescOnce sync.Once
	file_featureflag_v1_featureflag_proto_rawDescData = file_featureflag_v1_featureflag_proto_rawDesc
)

func file_featureflag_v1_featureflag_proto_rawDescGZIP() []byte {
	file_featureflag_v1_featureflag_proto_rawDescOnce.Do(func() {
		file_featureflag_v1_featureflag_proto_rawDescData = protoimpl.X.CompressGZIP(file_featureflag_v1_featureflag_proto_rawDescData)
	})
	return file_featureflag_v1_featureflag_proto_rawDescData
}

var file_featureflag_v1_featureflag_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_featureflag_v1_featureflag_proto_goTypes = []interface{}{
	(*GetFlagsRequest)(nil),  // 0: clutch.featureflag.v1.GetFlagsRequest
	(*Flag)(nil),             // 1: clutch.featureflag.v1.Flag
	(*GetFlagsResponse)(nil), // 2: clutch.featureflag.v1.GetFlagsResponse
	nil,                      // 3: clutch.featureflag.v1.GetFlagsResponse.FlagsEntry
}
var file_featureflag_v1_featureflag_proto_depIdxs = []int32{
	3, // 0: clutch.featureflag.v1.GetFlagsResponse.flags:type_name -> clutch.featureflag.v1.GetFlagsResponse.FlagsEntry
	1, // 1: clutch.featureflag.v1.GetFlagsResponse.FlagsEntry.value:type_name -> clutch.featureflag.v1.Flag
	0, // 2: clutch.featureflag.v1.FeatureFlagAPI.GetFlags:input_type -> clutch.featureflag.v1.GetFlagsRequest
	2, // 3: clutch.featureflag.v1.FeatureFlagAPI.GetFlags:output_type -> clutch.featureflag.v1.GetFlagsResponse
	3, // [3:4] is the sub-list for method output_type
	2, // [2:3] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_featureflag_v1_featureflag_proto_init() }
func file_featureflag_v1_featureflag_proto_init() {
	if File_featureflag_v1_featureflag_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_featureflag_v1_featureflag_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFlagsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_featureflag_v1_featureflag_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Flag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_featureflag_v1_featureflag_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFlagsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_featureflag_v1_featureflag_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*Flag_BooleanValue)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_featureflag_v1_featureflag_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_featureflag_v1_featureflag_proto_goTypes,
		DependencyIndexes: file_featureflag_v1_featureflag_proto_depIdxs,
		MessageInfos:      file_featureflag_v1_featureflag_proto_msgTypes,
	}.Build()
	File_featureflag_v1_featureflag_proto = out.File
	file_featureflag_v1_featureflag_proto_rawDesc = nil
	file_featureflag_v1_featureflag_proto_goTypes = nil
	file_featureflag_v1_featureflag_proto_depIdxs = nil
}
