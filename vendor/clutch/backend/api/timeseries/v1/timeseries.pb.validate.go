// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: timeseries/v1/timeseries.proto

package timeseriesv1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on TimeRange with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TimeRange) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TimeRange with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TimeRangeMultiError, or nil
// if none found.
func (m *TimeRange) ValidateAll() error {
	return m.validate(true)
}

func (m *TimeRange) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetStartMillis() <= 0 {
		err := TimeRangeValidationError{
			field:  "StartMillis",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetEndMillis() <= 0 {
		err := TimeRangeValidationError{
			field:  "EndMillis",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return TimeRangeMultiError(errors)
	}

	return nil
}

// TimeRangeMultiError is an error wrapping multiple validation errors returned
// by TimeRange.ValidateAll() if the designated constraints aren't met.
type TimeRangeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TimeRangeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TimeRangeMultiError) AllErrors() []error { return m }

// TimeRangeValidationError is the validation error returned by
// TimeRange.Validate if the designated constraints aren't met.
type TimeRangeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TimeRangeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TimeRangeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TimeRangeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TimeRangeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TimeRangeValidationError) ErrorName() string { return "TimeRangeValidationError" }

// Error satisfies the builtin error interface
func (e TimeRangeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTimeRange.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TimeRangeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TimeRangeValidationError{}

// Validate checks the field values on Point with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Point) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Point with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in PointMultiError, or nil if none found.
func (m *Point) ValidateAll() error {
	return m.validate(true)
}

func (m *Point) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPb()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PointValidationError{
					field:  "Pb",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PointValidationError{
					field:  "Pb",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPb()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PointValidationError{
				field:  "Pb",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Description

	// no validation rules for Href

	oneofTimestampPresent := false
	switch v := m.Timestamp.(type) {
	case *Point_Range:
		if v == nil {
			err := PointValidationError{
				field:  "Timestamp",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		oneofTimestampPresent = true

		if all {
			switch v := interface{}(m.GetRange()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PointValidationError{
						field:  "Range",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PointValidationError{
						field:  "Range",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRange()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PointValidationError{
					field:  "Range",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *Point_Millis:
		if v == nil {
			err := PointValidationError{
				field:  "Timestamp",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		oneofTimestampPresent = true
		// no validation rules for Millis
	default:
		_ = v // ensures v is used
	}
	if !oneofTimestampPresent {
		err := PointValidationError{
			field:  "Timestamp",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return PointMultiError(errors)
	}

	return nil
}

// PointMultiError is an error wrapping multiple validation errors returned by
// Point.ValidateAll() if the designated constraints aren't met.
type PointMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PointMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PointMultiError) AllErrors() []error { return m }

// PointValidationError is the validation error returned by Point.Validate if
// the designated constraints aren't met.
type PointValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PointValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PointValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PointValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PointValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PointValidationError) ErrorName() string { return "PointValidationError" }

// Error satisfies the builtin error interface
func (e PointValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPoint.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PointValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PointValidationError{}
