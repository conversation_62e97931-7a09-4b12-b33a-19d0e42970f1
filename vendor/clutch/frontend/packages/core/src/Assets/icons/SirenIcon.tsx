import * as React from "react";

import type { SVGProps } from "../global";
import { StyledSVG } from "../global";

const SirenIcon = ({ size, ...props }: SVGProps) => (
  <StyledSVG
    size={size}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M11.809 5.4346L11.8087 5.31235V5.3122C11.8072 4.81758 11.8058 4.30286 11.8033 3.8182C11.801 3.36891 12.1619 3.00272 12.6112 3.00001C13.0593 2.99732 13.4272 3.3585 13.428 3.8066L13.4284 4.00561V4.00624C13.4292 4.47227 13.4301 4.95869 13.4313 5.42342C13.4324 5.87282 13.0684 6.23834 12.619 6.24004C12.1725 6.24172 11.8103 5.88107 11.809 5.4346ZM7.4048 8.22144L7.30752 8.14858L7.30731 8.14843L7.30672 8.14798C6.91087 7.85148 6.49894 7.54294 6.11044 7.25324C5.75026 6.98466 5.675 6.47606 5.94323 6.11562C6.21075 5.75613 6.7206 5.67967 7.07896 5.9487L7.23919 6.069C7.61171 6.34867 8.00049 6.64055 8.37215 6.91915C8.73174 7.1887 8.80459 7.6994 8.53548 8.05933C8.26814 8.41691 7.76215 8.4891 7.4048 8.22144ZM17.7162 8.14875L17.6192 8.22144C17.2618 8.4891 16.7558 8.41691 16.4885 8.05933C16.2194 7.6994 16.2922 7.1887 16.6518 6.91915C17.0232 6.64072 17.4118 6.34902 17.7841 6.0695L17.945 5.9487C18.3033 5.67967 18.8132 5.75613 19.0807 6.11562C19.349 6.47606 19.2737 6.98466 18.9135 7.25324C18.5247 7.54319 18.1124 7.85202 17.7162 8.14875ZM6.4346 12.7601L6.31246 12.7605C5.81776 12.7619 5.30294 12.7634 4.8182 12.7658C4.36891 12.7681 4.00272 12.4072 4.00001 11.9579C3.99732 11.5098 4.3585 11.1419 4.8066 11.1411L5.00562 11.1408H5.00624C5.47227 11.1399 5.9587 11.1391 6.42342 11.1379C6.87282 11.1368 7.23835 11.5008 7.24004 11.9502C7.24172 12.3967 6.88107 12.7588 6.4346 12.7601ZM19.9065 12.7605L20.0286 12.7601C20.4751 12.7588 20.8358 12.3967 20.8341 11.9502C20.8324 11.5008 20.4669 11.1368 20.0175 11.1379C19.5525 11.1391 19.0659 11.1399 18.5997 11.1408L18.4006 11.1411C17.9525 11.1419 17.5914 11.5098 17.5941 11.9579C17.5968 12.4072 17.963 12.7681 18.4122 12.7658C18.897 12.7634 19.4118 12.7619 19.9065 12.7605Z"
      fill="#FB5990"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.13735 15.9744C8.05178 15.9744 7.96882 15.9859 7.88886 16.0048V10.9602C7.88886 8.82222 9.8899 7.08301 12.3497 7.08301C14.8096 7.08301 16.8106 8.82222 16.8106 10.9602V16.0043C16.7309 15.9859 16.648 15.9744 16.5622 15.9744H8.13735ZM6.26672 18.7315C6.26672 17.6769 6.94092 16.8158 7.76974 16.8158H16.9298C17.7586 16.8158 18.4328 17.6769 18.4328 18.7315V19.7376C18.4328 19.915 18.3187 20.0601 18.1793 20.0601H6.52018C6.38078 20.0601 6.26672 19.915 6.26672 19.7376V18.7315Z"
      fill="#FB5990"
    />
  </StyledSVG>
);
export default SirenIcon;
