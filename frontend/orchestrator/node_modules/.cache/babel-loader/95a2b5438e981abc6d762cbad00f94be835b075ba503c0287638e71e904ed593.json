{"ast": null, "code": "import _objectSpread from\"/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{createContext,useContext,useEffect,useState}from'react';// Authentication types based on Clutch implementation\nimport{jsx as _jsx,Fragment as _Fragment}from\"react/jsx-runtime\";// Create auth context\nconst AuthContext=/*#__PURE__*/createContext(undefined);// Auth provider component\nexport const AuthProvider=_ref=>{let{children}=_ref;const[authState,setAuthState]=useState({isAuthenticated:false,isLoading:true,user:null,token:null,error:null});// Initialize authentication on mount\nuseEffect(()=>{initializeAuth();},[]);// Initialize authentication\nconst initializeAuth=async()=>{try{setAuthState(prev=>_objectSpread(_objectSpread({},prev),{},{isLoading:true,error:null}));// Check if user is already authenticated\nconst response=await fetch('/v1/auth/user',{credentials:'include'});if(response.ok){const user=await response.json();setAuthState(prev=>_objectSpread(_objectSpread({},prev),{},{isAuthenticated:true,user,isLoading:false,error:null}));}else{// No existing session, just set to not authenticated\nsetAuthState(prev=>_objectSpread(_objectSpread({},prev),{},{isAuthenticated:false,isLoading:false,error:null}));}}catch(error){console.error('Auth initialization failed:',error);setAuthState(prev=>_objectSpread(_objectSpread({},prev),{},{isAuthenticated:false,isLoading:false,error:null// Don't show error on initial load\n}));}};// Login function with username and password\nconst login=async function(){let username=arguments.length>0&&arguments[0]!==undefined?arguments[0]:'admin';let password=arguments.length>1&&arguments[1]!==undefined?arguments[1]:'admin';try{setAuthState(prev=>_objectSpread(_objectSpread({},prev),{},{isLoading:true,error:null}));// Login with username and password\nconst response=await fetch('/v1/auth/login',{method:'POST',headers:{'Content-Type':'application/json'},credentials:'include',body:JSON.stringify({username,password})});if(!response.ok){const errorData=await response.json();throw new Error(errorData.error||'Login failed');}const data=await response.json();setAuthState(prev=>_objectSpread(_objectSpread({},prev),{},{isAuthenticated:true,user:data.user,token:null,// Using cookie-based auth\nisLoading:false,error:null}));}catch(error){console.error('Login failed:',error);setAuthState(prev=>_objectSpread(_objectSpread({},prev),{},{isLoading:false,error:error instanceof Error?error.message:'Login failed'}));}};// Logout function\nconst logout=async()=>{try{setAuthState(prev=>_objectSpread(_objectSpread({},prev),{},{isLoading:true}));const response=await fetch('/v1/auth/logout',{method:'POST',credentials:'include'});setAuthState({isAuthenticated:false,isLoading:false,user:null,token:null,error:null});// Optionally redirect to logout URL if provided\nif(response.ok){const data=await response.json();if(data.logout_url&&data.logout_url!==window.location.origin){window.location.href=data.logout_url;return;}}// Reload page to clear any cached state\nwindow.location.reload();}catch(error){console.error('Logout failed:',error);setAuthState(prev=>_objectSpread(_objectSpread({},prev),{},{isLoading:false,error:'Logout failed'}));}};// Check permission function (client-side first, then server-side)\nconst checkPermission=async(action,resource)=>{try{var _data$decision;// If user is not authenticated, deny access\nif(!authState.user){return false;}// Check client-side permissions first\nconst hasClientPermission=checkClientPermission(authState.user,action,resource);if(hasClientPermission!==null){return hasClientPermission;}// Fallback to server-side check\nconst response=await fetch('/v1/auth/check',{method:'POST',headers:{'Content-Type':'application/json'},credentials:'include',body:JSON.stringify({action,resource})});if(!response.ok){return false;}const data=await response.json();return((_data$decision=data.decision)===null||_data$decision===void 0?void 0:_data$decision.allowed)||false;}catch(error){var _authState$user,_authState$user$roles;console.error('Permission check failed:',error);// For admin users, default to true if server check fails\nif((_authState$user=authState.user)!==null&&_authState$user!==void 0&&(_authState$user$roles=_authState$user.roles)!==null&&_authState$user$roles!==void 0&&_authState$user$roles.includes('admin')){return true;}return false;}};// Client-side permission checking\nconst checkClientPermission=(user,action,resource)=>{var _user$roles,_user$roles2,_user$roles3;if(!user||!user.permissions){return false;}// Admin users with \"*\" permission have access to everything\nif(user.permissions.includes('*')){return true;}// Check for exact permission match\nconst exactPermission=\"\".concat(resource,\":\").concat(action.toLowerCase());if(user.permissions.includes(exactPermission)){return true;}// Check for wildcard resource permissions\nconst wildcardPermission=\"*:\".concat(action.toLowerCase());if(user.permissions.includes(wildcardPermission)){return true;}// Check for resource-level permissions\nconst resourcePermission=\"\".concat(resource,\":*\");if(user.permissions.includes(resourcePermission)){return true;}// Check role-based permissions\nif((_user$roles=user.roles)!==null&&_user$roles!==void 0&&_user$roles.includes('admin')){return true;}if((_user$roles2=user.roles)!==null&&_user$roles2!==void 0&&_user$roles2.includes('operator')&&(action.toLowerCase()==='read'||action.toLowerCase()==='execute')){return true;}if((_user$roles3=user.roles)!==null&&_user$roles3!==void 0&&_user$roles3.includes('viewer')&&action.toLowerCase()==='read'){return true;}// Default deny\nreturn false;};// Refresh token function\nconst refreshToken=async()=>{try{const response=await fetch('/v1/auth/refresh',{method:'POST',credentials:'include'});if(response.ok){const tokenData=await response.json();// Get updated user info\nconst userResponse=await fetch('/v1/auth/user',{credentials:'include'});if(userResponse.ok){const user=await userResponse.json();setAuthState(prev=>_objectSpread(_objectSpread({},prev),{},{isAuthenticated:true,user,token:tokenData,isLoading:false,error:null}));}}else{// Silently fail refresh - user needs to login again\nsetAuthState(prev=>_objectSpread(_objectSpread({},prev),{},{isAuthenticated:false,isLoading:false,user:null,token:null,error:null// Don't show error for failed refresh\n}));}}catch(error){console.error('Token refresh failed:',error);setAuthState(prev=>_objectSpread(_objectSpread({},prev),{},{isAuthenticated:false,isLoading:false,user:null,token:null,error:null// Don't show error for failed refresh\n}));}};// Auto-refresh token before expiry\nuseEffect(()=>{if(authState.token&&authState.isAuthenticated){const refreshInterval=setInterval(()=>{refreshToken();},50*60*1000);// Refresh every 50 minutes (token expires in 60 minutes)\nreturn()=>clearInterval(refreshInterval);}},[authState.token,authState.isAuthenticated]);const contextValue=_objectSpread(_objectSpread({},authState),{},{login,logout,checkPermission,refreshToken});return/*#__PURE__*/_jsx(AuthContext.Provider,{value:contextValue,children:children});};// Hook to use auth context\nexport const useAuth=()=>{const context=useContext(AuthContext);if(context===undefined){throw new Error('useAuth must be used within an AuthProvider');}return context;};// Higher-order component for protected routes\nexport const ProtectedRoute=_ref2=>{let{children,requiredPermission,fallback=/*#__PURE__*/_jsx(\"div\",{className:\"p-4 text-red-600\",children:\"Access Denied\"})}=_ref2;const{isAuthenticated,isLoading,checkPermission}=useAuth();const[hasPermission,setHasPermission]=useState(null);useEffect(()=>{if(isAuthenticated&&requiredPermission){checkPermission(requiredPermission.action,requiredPermission.resource).then(setHasPermission);}else if(isAuthenticated){setHasPermission(true);}},[isAuthenticated,requiredPermission,checkPermission]);if(isLoading){return/*#__PURE__*/_jsx(\"div\",{className:\"p-4\",children:\"Loading...\"});}if(!isAuthenticated){return/*#__PURE__*/_jsx(\"div\",{className:\"p-4\",children:\"Please log in to access this page.\"});}if(requiredPermission&&hasPermission===false){return/*#__PURE__*/_jsx(_Fragment,{children:fallback});}if(requiredPermission&&hasPermission===null){return/*#__PURE__*/_jsx(\"div\",{className:\"p-4\",children:\"Checking permissions...\"});}return/*#__PURE__*/_jsx(_Fragment,{children:children});};", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "jsx", "_jsx", "Fragment", "_Fragment", "AuthContext", "undefined", "<PERSON>th<PERSON><PERSON><PERSON>", "_ref", "children", "authState", "setAuthState", "isAuthenticated", "isLoading", "user", "token", "error", "initializeAuth", "prev", "_objectSpread", "response", "fetch", "credentials", "ok", "json", "console", "login", "username", "arguments", "length", "password", "method", "headers", "body", "JSON", "stringify", "errorData", "Error", "data", "message", "logout", "logout_url", "window", "location", "origin", "href", "reload", "checkPermission", "action", "resource", "_data$decision", "hasClientPermission", "checkClientPermission", "decision", "allowed", "_authState$user", "_authState$user$roles", "roles", "includes", "_user$roles", "_user$roles2", "_user$roles3", "permissions", "exactPermission", "concat", "toLowerCase", "wildcardPermission", "resourcePermission", "refreshToken", "tokenData", "userResponse", "refreshInterval", "setInterval", "clearInterval", "contextValue", "Provider", "value", "useAuth", "context", "ProtectedRoute", "_ref2", "requiredPermission", "fallback", "className", "hasPermission", "setHasPermission", "then"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/auth/AuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';\n\n// Authentication types based on Clutch implementation\ninterface User {\n  sub: string;\n  email: string;\n  name: string;\n  groups: string[];\n  roles: string[];\n  permissions: string[];\n  preferences: {\n    theme: string;\n    timezone: string;\n    language: string;\n  };\n}\n\ninterface AuthToken {\n  access_token: string;\n  refresh_token?: string;\n  token_type: string;\n  expires_in: number;\n}\n\ninterface AuthState {\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  user: User | null;\n  token: AuthToken | null;\n  error: string | null;\n}\n\ninterface AuthContextType extends AuthState {\n  login: (username?: string, password?: string) => Promise<void>;\n  logout: () => Promise<void>;\n  checkPermission: (action: string, resource: string) => Promise<boolean>;\n  refreshToken: () => Promise<void>;\n}\n\n// Create auth context\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\n// Auth provider component\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [authState, setAuthState] = useState<AuthState>({\n    isAuthenticated: false,\n    isLoading: true,\n    user: null,\n    token: null,\n    error: null,\n  });\n\n  // Initialize authentication on mount\n  useEffect(() => {\n    initializeAuth();\n  }, []);\n\n  // Initialize authentication\n  const initializeAuth = async () => {\n    try {\n      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));\n\n      // Check if user is already authenticated\n      const response = await fetch('/v1/auth/user', {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const user = await response.json();\n        setAuthState(prev => ({\n          ...prev,\n          isAuthenticated: true,\n          user,\n          isLoading: false,\n          error: null,\n        }));\n      } else {\n        // No existing session, just set to not authenticated\n        setAuthState(prev => ({\n          ...prev,\n          isAuthenticated: false,\n          isLoading: false,\n          error: null,\n        }));\n      }\n    } catch (error) {\n      console.error('Auth initialization failed:', error);\n      setAuthState(prev => ({\n        ...prev,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null, // Don't show error on initial load\n      }));\n    }\n  };\n\n  // Login function with username and password\n  const login = async (username: string = 'admin', password: string = 'admin') => {\n    try {\n      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));\n\n      // Login with username and password\n      const response = await fetch('/v1/auth/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include',\n        body: JSON.stringify({\n          username,\n          password,\n        }),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || 'Login failed');\n      }\n\n      const data = await response.json();\n\n      setAuthState(prev => ({\n        ...prev,\n        isAuthenticated: true,\n        user: data.user,\n        token: null, // Using cookie-based auth\n        isLoading: false,\n        error: null,\n      }));\n    } catch (error) {\n      console.error('Login failed:', error);\n      setAuthState(prev => ({\n        ...prev,\n        isLoading: false,\n        error: error instanceof Error ? error.message : 'Login failed',\n      }));\n    }\n  };\n\n  // Logout function\n  const logout = async () => {\n    try {\n      setAuthState(prev => ({ ...prev, isLoading: true }));\n\n      const response = await fetch('/v1/auth/logout', {\n        method: 'POST',\n        credentials: 'include',\n      });\n\n      setAuthState({\n        isAuthenticated: false,\n        isLoading: false,\n        user: null,\n        token: null,\n        error: null,\n      });\n\n      // Optionally redirect to logout URL if provided\n      if (response.ok) {\n        const data = await response.json();\n        if (data.logout_url && data.logout_url !== window.location.origin) {\n          window.location.href = data.logout_url;\n          return;\n        }\n      }\n      \n      // Reload page to clear any cached state\n      window.location.reload();\n    } catch (error) {\n      console.error('Logout failed:', error);\n      setAuthState(prev => ({\n        ...prev,\n        isLoading: false,\n        error: 'Logout failed',\n      }));\n    }\n  };\n\n  // Check permission function (client-side first, then server-side)\n  const checkPermission = async (action: string, resource: string): Promise<boolean> => {\n    try {\n      // If user is not authenticated, deny access\n      if (!authState.user) {\n        return false;\n      }\n\n      // Check client-side permissions first\n      const hasClientPermission = checkClientPermission(authState.user, action, resource);\n      if (hasClientPermission !== null) {\n        return hasClientPermission;\n      }\n\n      // Fallback to server-side check\n      const response = await fetch('/v1/auth/check', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include',\n        body: JSON.stringify({ action, resource }),\n      });\n\n      if (!response.ok) {\n        return false;\n      }\n\n      const data = await response.json();\n      return data.decision?.allowed || false;\n    } catch (error) {\n      console.error('Permission check failed:', error);\n      // For admin users, default to true if server check fails\n      if (authState.user?.roles?.includes('admin')) {\n        return true;\n      }\n      return false;\n    }\n  };\n\n  // Client-side permission checking\n  const checkClientPermission = (user: User, action: string, resource: string): boolean | null => {\n    if (!user || !user.permissions) {\n      return false;\n    }\n\n    // Admin users with \"*\" permission have access to everything\n    if (user.permissions.includes('*')) {\n      return true;\n    }\n\n    // Check for exact permission match\n    const exactPermission = `${resource}:${action.toLowerCase()}`;\n    if (user.permissions.includes(exactPermission)) {\n      return true;\n    }\n\n    // Check for wildcard resource permissions\n    const wildcardPermission = `*:${action.toLowerCase()}`;\n    if (user.permissions.includes(wildcardPermission)) {\n      return true;\n    }\n\n    // Check for resource-level permissions\n    const resourcePermission = `${resource}:*`;\n    if (user.permissions.includes(resourcePermission)) {\n      return true;\n    }\n\n    // Check role-based permissions\n    if (user.roles?.includes('admin')) {\n      return true;\n    }\n\n    if (user.roles?.includes('operator') && (action.toLowerCase() === 'read' || action.toLowerCase() === 'execute')) {\n      return true;\n    }\n\n    if (user.roles?.includes('viewer') && action.toLowerCase() === 'read') {\n      return true;\n    }\n\n    // Default deny\n    return false;\n  };\n\n  // Refresh token function\n  const refreshToken = async () => {\n    try {\n      const response = await fetch('/v1/auth/refresh', {\n        method: 'POST',\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const tokenData = await response.json();\n\n        // Get updated user info\n        const userResponse = await fetch('/v1/auth/user', {\n          credentials: 'include',\n        });\n\n        if (userResponse.ok) {\n          const user = await userResponse.json();\n          setAuthState(prev => ({\n            ...prev,\n            isAuthenticated: true,\n            user,\n            token: tokenData,\n            isLoading: false,\n            error: null,\n          }));\n        }\n      } else {\n        // Silently fail refresh - user needs to login again\n        setAuthState(prev => ({\n          ...prev,\n          isAuthenticated: false,\n          isLoading: false,\n          user: null,\n          token: null,\n          error: null, // Don't show error for failed refresh\n        }));\n      }\n    } catch (error) {\n      console.error('Token refresh failed:', error);\n      setAuthState(prev => ({\n        ...prev,\n        isAuthenticated: false,\n        isLoading: false,\n        user: null,\n        token: null,\n        error: null, // Don't show error for failed refresh\n      }));\n    }\n  };\n\n  // Auto-refresh token before expiry\n  useEffect(() => {\n    if (authState.token && authState.isAuthenticated) {\n      const refreshInterval = setInterval(() => {\n        refreshToken();\n      }, 50 * 60 * 1000); // Refresh every 50 minutes (token expires in 60 minutes)\n\n      return () => clearInterval(refreshInterval);\n    }\n  }, [authState.token, authState.isAuthenticated]);\n\n  const contextValue: AuthContextType = {\n    ...authState,\n    login,\n    logout,\n    checkPermission,\n    refreshToken,\n  };\n\n  return (\n    <AuthContext.Provider value={contextValue}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\n// Hook to use auth context\nexport const useAuth = (): AuthContextType => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\n// Higher-order component for protected routes\ninterface ProtectedRouteProps {\n  children: ReactNode;\n  requiredPermission?: { action: string; resource: string };\n  fallback?: ReactNode;\n}\n\nexport const ProtectedRoute: React.FC<ProtectedRouteProps> = ({\n  children,\n  requiredPermission,\n  fallback = <div className=\"p-4 text-red-600\">Access Denied</div>,\n}) => {\n  const { isAuthenticated, isLoading, checkPermission } = useAuth();\n  const [hasPermission, setHasPermission] = useState<boolean | null>(null);\n\n  useEffect(() => {\n    if (isAuthenticated && requiredPermission) {\n      checkPermission(requiredPermission.action, requiredPermission.resource)\n        .then(setHasPermission);\n    } else if (isAuthenticated) {\n      setHasPermission(true);\n    }\n  }, [isAuthenticated, requiredPermission, checkPermission]);\n\n  if (isLoading) {\n    return <div className=\"p-4\">Loading...</div>;\n  }\n\n  if (!isAuthenticated) {\n    return <div className=\"p-4\">Please log in to access this page.</div>;\n  }\n\n  if (requiredPermission && hasPermission === false) {\n    return <>{fallback}</>;\n  }\n\n  if (requiredPermission && hasPermission === null) {\n    return <div className=\"p-4\">Checking permissions...</div>;\n  }\n\n  return <>{children}</>;\n};\n"], "mappings": "yJAAA,MAAO,CAAAA,KAAK,EAAIC,aAAa,CAAEC,UAAU,CAAEC,SAAS,CAAEC,QAAQ,KAAmB,OAAO,CAExF;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,yBAqCA;AACA,KAAM,CAAAC,WAAW,cAAGR,aAAa,CAA8BS,SAAS,CAAC,CAEzE;AAKA,MAAO,MAAM,CAAAC,YAAyC,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACpE,KAAM,CAACE,SAAS,CAAEC,YAAY,CAAC,CAAGX,QAAQ,CAAY,CACpDY,eAAe,CAAE,KAAK,CACtBC,SAAS,CAAE,IAAI,CACfC,IAAI,CAAE,IAAI,CACVC,KAAK,CAAE,IAAI,CACXC,KAAK,CAAE,IACT,CAAC,CAAC,CAEF;AACAjB,SAAS,CAAC,IAAM,CACdkB,cAAc,CAAC,CAAC,CAClB,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAA,cAAc,CAAG,KAAAA,CAAA,GAAY,CACjC,GAAI,CACFN,YAAY,CAACO,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAEL,SAAS,CAAE,IAAI,CAAEG,KAAK,CAAE,IAAI,EAAG,CAAC,CAEjE;AACA,KAAM,CAAAI,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,eAAe,CAAE,CAC5CC,WAAW,CAAE,SACf,CAAC,CAAC,CAEF,GAAIF,QAAQ,CAACG,EAAE,CAAE,CACf,KAAM,CAAAT,IAAI,CAAG,KAAM,CAAAM,QAAQ,CAACI,IAAI,CAAC,CAAC,CAClCb,YAAY,CAACO,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACZD,IAAI,MACPN,eAAe,CAAE,IAAI,CACrBE,IAAI,CACJD,SAAS,CAAE,KAAK,CAChBG,KAAK,CAAE,IAAI,EACX,CAAC,CACL,CAAC,IAAM,CACL;AACAL,YAAY,CAACO,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACZD,IAAI,MACPN,eAAe,CAAE,KAAK,CACtBC,SAAS,CAAE,KAAK,CAChBG,KAAK,CAAE,IAAI,EACX,CAAC,CACL,CACF,CAAE,MAAOA,KAAK,CAAE,CACdS,OAAO,CAACT,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnDL,YAAY,CAACO,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACZD,IAAI,MACPN,eAAe,CAAE,KAAK,CACtBC,SAAS,CAAE,KAAK,CAChBG,KAAK,CAAE,IAAM;AAAA,EACb,CAAC,CACL,CACF,CAAC,CAED;AACA,KAAM,CAAAU,KAAK,CAAG,cAAAA,CAAA,CAAkE,IAA3D,CAAAC,QAAgB,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAtB,SAAA,CAAAsB,SAAA,IAAG,OAAO,IAAE,CAAAE,QAAgB,CAAAF,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAtB,SAAA,CAAAsB,SAAA,IAAG,OAAO,CACzE,GAAI,CACFjB,YAAY,CAACO,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAEL,SAAS,CAAE,IAAI,CAAEG,KAAK,CAAE,IAAI,EAAG,CAAC,CAEjE;AACA,KAAM,CAAAI,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,gBAAgB,CAAE,CAC7CU,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDV,WAAW,CAAE,SAAS,CACtBW,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CACnBR,QAAQ,CACRG,QACF,CAAC,CACH,CAAC,CAAC,CAEF,GAAI,CAACV,QAAQ,CAACG,EAAE,CAAE,CAChB,KAAM,CAAAa,SAAS,CAAG,KAAM,CAAAhB,QAAQ,CAACI,IAAI,CAAC,CAAC,CACvC,KAAM,IAAI,CAAAa,KAAK,CAACD,SAAS,CAACpB,KAAK,EAAI,cAAc,CAAC,CACpD,CAEA,KAAM,CAAAsB,IAAI,CAAG,KAAM,CAAAlB,QAAQ,CAACI,IAAI,CAAC,CAAC,CAElCb,YAAY,CAACO,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACZD,IAAI,MACPN,eAAe,CAAE,IAAI,CACrBE,IAAI,CAAEwB,IAAI,CAACxB,IAAI,CACfC,KAAK,CAAE,IAAI,CAAE;AACbF,SAAS,CAAE,KAAK,CAChBG,KAAK,CAAE,IAAI,EACX,CAAC,CACL,CAAE,MAAOA,KAAK,CAAE,CACdS,OAAO,CAACT,KAAK,CAAC,eAAe,CAAEA,KAAK,CAAC,CACrCL,YAAY,CAACO,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACZD,IAAI,MACPL,SAAS,CAAE,KAAK,CAChBG,KAAK,CAAEA,KAAK,WAAY,CAAAqB,KAAK,CAAGrB,KAAK,CAACuB,OAAO,CAAG,cAAc,EAC9D,CAAC,CACL,CACF,CAAC,CAED;AACA,KAAM,CAAAC,MAAM,CAAG,KAAAA,CAAA,GAAY,CACzB,GAAI,CACF7B,YAAY,CAACO,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAEL,SAAS,CAAE,IAAI,EAAG,CAAC,CAEpD,KAAM,CAAAO,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,iBAAiB,CAAE,CAC9CU,MAAM,CAAE,MAAM,CACdT,WAAW,CAAE,SACf,CAAC,CAAC,CAEFX,YAAY,CAAC,CACXC,eAAe,CAAE,KAAK,CACtBC,SAAS,CAAE,KAAK,CAChBC,IAAI,CAAE,IAAI,CACVC,KAAK,CAAE,IAAI,CACXC,KAAK,CAAE,IACT,CAAC,CAAC,CAEF;AACA,GAAII,QAAQ,CAACG,EAAE,CAAE,CACf,KAAM,CAAAe,IAAI,CAAG,KAAM,CAAAlB,QAAQ,CAACI,IAAI,CAAC,CAAC,CAClC,GAAIc,IAAI,CAACG,UAAU,EAAIH,IAAI,CAACG,UAAU,GAAKC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAE,CACjEF,MAAM,CAACC,QAAQ,CAACE,IAAI,CAAGP,IAAI,CAACG,UAAU,CACtC,OACF,CACF,CAEA;AACAC,MAAM,CAACC,QAAQ,CAACG,MAAM,CAAC,CAAC,CAC1B,CAAE,MAAO9B,KAAK,CAAE,CACdS,OAAO,CAACT,KAAK,CAAC,gBAAgB,CAAEA,KAAK,CAAC,CACtCL,YAAY,CAACO,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACZD,IAAI,MACPL,SAAS,CAAE,KAAK,CAChBG,KAAK,CAAE,eAAe,EACtB,CAAC,CACL,CACF,CAAC,CAED;AACA,KAAM,CAAA+B,eAAe,CAAG,KAAAA,CAAOC,MAAc,CAAEC,QAAgB,GAAuB,CACpF,GAAI,KAAAC,cAAA,CACF;AACA,GAAI,CAACxC,SAAS,CAACI,IAAI,CAAE,CACnB,MAAO,MAAK,CACd,CAEA;AACA,KAAM,CAAAqC,mBAAmB,CAAGC,qBAAqB,CAAC1C,SAAS,CAACI,IAAI,CAAEkC,MAAM,CAAEC,QAAQ,CAAC,CACnF,GAAIE,mBAAmB,GAAK,IAAI,CAAE,CAChC,MAAO,CAAAA,mBAAmB,CAC5B,CAEA;AACA,KAAM,CAAA/B,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,gBAAgB,CAAE,CAC7CU,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDV,WAAW,CAAE,SAAS,CACtBW,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CAAEa,MAAM,CAAEC,QAAS,CAAC,CAC3C,CAAC,CAAC,CAEF,GAAI,CAAC7B,QAAQ,CAACG,EAAE,CAAE,CAChB,MAAO,MAAK,CACd,CAEA,KAAM,CAAAe,IAAI,CAAG,KAAM,CAAAlB,QAAQ,CAACI,IAAI,CAAC,CAAC,CAClC,MAAO,EAAA0B,cAAA,CAAAZ,IAAI,CAACe,QAAQ,UAAAH,cAAA,iBAAbA,cAAA,CAAeI,OAAO,GAAI,KAAK,CACxC,CAAE,MAAOtC,KAAK,CAAE,KAAAuC,eAAA,CAAAC,qBAAA,CACd/B,OAAO,CAACT,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChD;AACA,IAAAuC,eAAA,CAAI7C,SAAS,CAACI,IAAI,UAAAyC,eAAA,YAAAC,qBAAA,CAAdD,eAAA,CAAgBE,KAAK,UAAAD,qBAAA,WAArBA,qBAAA,CAAuBE,QAAQ,CAAC,OAAO,CAAC,CAAE,CAC5C,MAAO,KAAI,CACb,CACA,MAAO,MAAK,CACd,CACF,CAAC,CAED;AACA,KAAM,CAAAN,qBAAqB,CAAGA,CAACtC,IAAU,CAAEkC,MAAc,CAAEC,QAAgB,GAAqB,KAAAU,WAAA,CAAAC,YAAA,CAAAC,YAAA,CAC9F,GAAI,CAAC/C,IAAI,EAAI,CAACA,IAAI,CAACgD,WAAW,CAAE,CAC9B,MAAO,MAAK,CACd,CAEA;AACA,GAAIhD,IAAI,CAACgD,WAAW,CAACJ,QAAQ,CAAC,GAAG,CAAC,CAAE,CAClC,MAAO,KAAI,CACb,CAEA;AACA,KAAM,CAAAK,eAAe,IAAAC,MAAA,CAAMf,QAAQ,MAAAe,MAAA,CAAIhB,MAAM,CAACiB,WAAW,CAAC,CAAC,CAAE,CAC7D,GAAInD,IAAI,CAACgD,WAAW,CAACJ,QAAQ,CAACK,eAAe,CAAC,CAAE,CAC9C,MAAO,KAAI,CACb,CAEA;AACA,KAAM,CAAAG,kBAAkB,MAAAF,MAAA,CAAQhB,MAAM,CAACiB,WAAW,CAAC,CAAC,CAAE,CACtD,GAAInD,IAAI,CAACgD,WAAW,CAACJ,QAAQ,CAACQ,kBAAkB,CAAC,CAAE,CACjD,MAAO,KAAI,CACb,CAEA;AACA,KAAM,CAAAC,kBAAkB,IAAAH,MAAA,CAAMf,QAAQ,MAAI,CAC1C,GAAInC,IAAI,CAACgD,WAAW,CAACJ,QAAQ,CAACS,kBAAkB,CAAC,CAAE,CACjD,MAAO,KAAI,CACb,CAEA;AACA,IAAAR,WAAA,CAAI7C,IAAI,CAAC2C,KAAK,UAAAE,WAAA,WAAVA,WAAA,CAAYD,QAAQ,CAAC,OAAO,CAAC,CAAE,CACjC,MAAO,KAAI,CACb,CAEA,GAAI,CAAAE,YAAA,CAAA9C,IAAI,CAAC2C,KAAK,UAAAG,YAAA,WAAVA,YAAA,CAAYF,QAAQ,CAAC,UAAU,CAAC,GAAKV,MAAM,CAACiB,WAAW,CAAC,CAAC,GAAK,MAAM,EAAIjB,MAAM,CAACiB,WAAW,CAAC,CAAC,GAAK,SAAS,CAAC,CAAE,CAC/G,MAAO,KAAI,CACb,CAEA,GAAI,CAAAJ,YAAA,CAAA/C,IAAI,CAAC2C,KAAK,UAAAI,YAAA,WAAVA,YAAA,CAAYH,QAAQ,CAAC,QAAQ,CAAC,EAAIV,MAAM,CAACiB,WAAW,CAAC,CAAC,GAAK,MAAM,CAAE,CACrE,MAAO,KAAI,CACb,CAEA;AACA,MAAO,MAAK,CACd,CAAC,CAED;AACA,KAAM,CAAAG,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CACF,KAAM,CAAAhD,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,kBAAkB,CAAE,CAC/CU,MAAM,CAAE,MAAM,CACdT,WAAW,CAAE,SACf,CAAC,CAAC,CAEF,GAAIF,QAAQ,CAACG,EAAE,CAAE,CACf,KAAM,CAAA8C,SAAS,CAAG,KAAM,CAAAjD,QAAQ,CAACI,IAAI,CAAC,CAAC,CAEvC;AACA,KAAM,CAAA8C,YAAY,CAAG,KAAM,CAAAjD,KAAK,CAAC,eAAe,CAAE,CAChDC,WAAW,CAAE,SACf,CAAC,CAAC,CAEF,GAAIgD,YAAY,CAAC/C,EAAE,CAAE,CACnB,KAAM,CAAAT,IAAI,CAAG,KAAM,CAAAwD,YAAY,CAAC9C,IAAI,CAAC,CAAC,CACtCb,YAAY,CAACO,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACZD,IAAI,MACPN,eAAe,CAAE,IAAI,CACrBE,IAAI,CACJC,KAAK,CAAEsD,SAAS,CAChBxD,SAAS,CAAE,KAAK,CAChBG,KAAK,CAAE,IAAI,EACX,CAAC,CACL,CACF,CAAC,IAAM,CACL;AACAL,YAAY,CAACO,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACZD,IAAI,MACPN,eAAe,CAAE,KAAK,CACtBC,SAAS,CAAE,KAAK,CAChBC,IAAI,CAAE,IAAI,CACVC,KAAK,CAAE,IAAI,CACXC,KAAK,CAAE,IAAM;AAAA,EACb,CAAC,CACL,CACF,CAAE,MAAOA,KAAK,CAAE,CACdS,OAAO,CAACT,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7CL,YAAY,CAACO,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACZD,IAAI,MACPN,eAAe,CAAE,KAAK,CACtBC,SAAS,CAAE,KAAK,CAChBC,IAAI,CAAE,IAAI,CACVC,KAAK,CAAE,IAAI,CACXC,KAAK,CAAE,IAAM;AAAA,EACb,CAAC,CACL,CACF,CAAC,CAED;AACAjB,SAAS,CAAC,IAAM,CACd,GAAIW,SAAS,CAACK,KAAK,EAAIL,SAAS,CAACE,eAAe,CAAE,CAChD,KAAM,CAAA2D,eAAe,CAAGC,WAAW,CAAC,IAAM,CACxCJ,YAAY,CAAC,CAAC,CAChB,CAAC,CAAE,EAAE,CAAG,EAAE,CAAG,IAAI,CAAC,CAAE;AAEpB,MAAO,IAAMK,aAAa,CAACF,eAAe,CAAC,CAC7C,CACF,CAAC,CAAE,CAAC7D,SAAS,CAACK,KAAK,CAAEL,SAAS,CAACE,eAAe,CAAC,CAAC,CAEhD,KAAM,CAAA8D,YAA6B,CAAAvD,aAAA,CAAAA,aAAA,IAC9BT,SAAS,MACZgB,KAAK,CACLc,MAAM,CACNO,eAAe,CACfqB,YAAY,EACb,CAED,mBACElE,IAAA,CAACG,WAAW,CAACsE,QAAQ,EAACC,KAAK,CAAEF,YAAa,CAAAjE,QAAA,CACvCA,QAAQ,CACW,CAAC,CAE3B,CAAC,CAED;AACA,MAAO,MAAM,CAAAoE,OAAO,CAAGA,CAAA,GAAuB,CAC5C,KAAM,CAAAC,OAAO,CAAGhF,UAAU,CAACO,WAAW,CAAC,CACvC,GAAIyE,OAAO,GAAKxE,SAAS,CAAE,CACzB,KAAM,IAAI,CAAA+B,KAAK,CAAC,6CAA6C,CAAC,CAChE,CACA,MAAO,CAAAyC,OAAO,CAChB,CAAC,CAED;AAOA,MAAO,MAAM,CAAAC,cAA6C,CAAGC,KAAA,EAIvD,IAJwD,CAC5DvE,QAAQ,CACRwE,kBAAkB,CAClBC,QAAQ,cAAGhF,IAAA,QAAKiF,SAAS,CAAC,kBAAkB,CAAA1E,QAAA,CAAC,eAAa,CAAK,CACjE,CAAC,CAAAuE,KAAA,CACC,KAAM,CAAEpE,eAAe,CAAEC,SAAS,CAAEkC,eAAgB,CAAC,CAAG8B,OAAO,CAAC,CAAC,CACjE,KAAM,CAACO,aAAa,CAAEC,gBAAgB,CAAC,CAAGrF,QAAQ,CAAiB,IAAI,CAAC,CAExED,SAAS,CAAC,IAAM,CACd,GAAIa,eAAe,EAAIqE,kBAAkB,CAAE,CACzClC,eAAe,CAACkC,kBAAkB,CAACjC,MAAM,CAAEiC,kBAAkB,CAAChC,QAAQ,CAAC,CACpEqC,IAAI,CAACD,gBAAgB,CAAC,CAC3B,CAAC,IAAM,IAAIzE,eAAe,CAAE,CAC1ByE,gBAAgB,CAAC,IAAI,CAAC,CACxB,CACF,CAAC,CAAE,CAACzE,eAAe,CAAEqE,kBAAkB,CAAElC,eAAe,CAAC,CAAC,CAE1D,GAAIlC,SAAS,CAAE,CACb,mBAAOX,IAAA,QAAKiF,SAAS,CAAC,KAAK,CAAA1E,QAAA,CAAC,YAAU,CAAK,CAAC,CAC9C,CAEA,GAAI,CAACG,eAAe,CAAE,CACpB,mBAAOV,IAAA,QAAKiF,SAAS,CAAC,KAAK,CAAA1E,QAAA,CAAC,oCAAkC,CAAK,CAAC,CACtE,CAEA,GAAIwE,kBAAkB,EAAIG,aAAa,GAAK,KAAK,CAAE,CACjD,mBAAOlF,IAAA,CAAAE,SAAA,EAAAK,QAAA,CAAGyE,QAAQ,CAAG,CAAC,CACxB,CAEA,GAAID,kBAAkB,EAAIG,aAAa,GAAK,IAAI,CAAE,CAChD,mBAAOlF,IAAA,QAAKiF,SAAS,CAAC,KAAK,CAAA1E,QAAA,CAAC,yBAAuB,CAAK,CAAC,CAC3D,CAEA,mBAAOP,IAAA,CAAAE,SAAA,EAAAK,QAAA,CAAGA,QAAQ,CAAG,CAAC,CACxB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}