{"ast": null, "code": "// Default to a dummy \"batch\" implementation that just runs the callback\nfunction defaultNoopBatch(callback) {\n  callback();\n}\nlet batch = defaultNoopBatch; // Allow injecting another batching function later\n\nexport const setBatch = newBatch => batch = newBatch; // Supply a getter just to skip dealing with ESM bindings\n\nexport const getBatch = () => batch;", "map": {"version": 3, "names": ["defaultNoopBatch", "callback", "batch", "setBatch", "newBatch", "getBatch"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/node_modules/react-redux/es/utils/batch.js"], "sourcesContent": ["// Default to a dummy \"batch\" implementation that just runs the callback\nfunction defaultNoopBatch(callback) {\n  callback();\n}\n\nlet batch = defaultNoopBatch; // Allow injecting another batching function later\n\nexport const setBatch = newBatch => batch = newBatch; // Supply a getter just to skip dealing with ESM bindings\n\nexport const getBatch = () => batch;"], "mappings": "AAAA;AACA,SAASA,gBAAgBA,CAACC,QAAQ,EAAE;EAClCA,QAAQ,CAAC,CAAC;AACZ;AAEA,IAAIC,KAAK,GAAGF,gBAAgB,CAAC,CAAC;;AAE9B,OAAO,MAAMG,QAAQ,GAAGC,QAAQ,IAAIF,KAAK,GAAGE,QAAQ,CAAC,CAAC;;AAEtD,OAAO,MAAMC,QAAQ,GAAGA,CAAA,KAAMH,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}