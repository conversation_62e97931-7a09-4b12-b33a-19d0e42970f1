import * as React from "react";

import type { SVGProps } from "../global";
import { StyledSVG } from "../global";

const RocketIcon = ({ size, ...props }: SVGProps) => (
  <StyledSVG
    size={size}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M3.92413 10.6881L6.27587 11.6692C6.01071 12.3427 5.7992 13.0364 5.64333 13.7436L5.55413 14.1516L8.92564 17.5458L9.33111 17.4581C10.0341 17.3012 10.7235 17.0884 11.3929 16.8216L12.3681 19.1878C12.3777 19.2112 12.3929 19.2319 12.4123 19.248C12.4318 19.2641 12.4549 19.275 12.4797 19.2799C12.5044 19.2847 12.5299 19.2833 12.554 19.2758C12.578 19.2683 12.5999 19.2548 12.6175 19.2367L14.0914 17.7538C14.3319 17.5118 14.5193 17.2218 14.6415 16.9024C14.7638 16.5831 14.8181 16.2415 14.8009 15.8997L14.7482 14.9471C17.4771 12.9196 20.2383 9.36425 20.9905 3.15927C21.0118 3.0037 20.9972 2.84527 20.9476 2.69634C20.8981 2.5474 20.8151 2.41199 20.705 2.30064C20.5949 2.1893 20.4608 2.10504 20.313 2.05442C20.1653 2.00381 20.0079 1.98821 19.8531 2.00884C13.6879 2.77171 10.1522 5.55192 8.13699 8.28929L7.19224 8.24034C6.85326 8.22178 6.51418 8.2748 6.19679 8.396C5.8794 8.5172 5.59074 8.70389 5.34936 8.94406L3.87547 10.427C3.85505 10.4444 3.83958 10.467 3.83063 10.4924C3.82168 10.5179 3.81957 10.5452 3.82451 10.5717C3.82945 10.5982 3.84126 10.6229 3.85876 10.6434C3.87625 10.6638 3.8988 10.6792 3.92413 10.6881ZM13.3798 7.07359C13.6352 6.81749 13.9603 6.64328 14.3141 6.57296C14.6679 6.50264 15.0344 6.53937 15.3674 6.6785C15.7005 6.81763 15.9851 7.05294 16.1853 7.35469C16.3855 7.65645 16.4924 8.01113 16.4924 8.37394C16.4924 8.73675 16.3855 9.09143 16.1853 9.39319C15.9851 9.69495 15.7005 9.93025 15.3674 10.0694C15.0344 10.2085 14.6679 10.2452 14.3141 10.1749C13.9603 10.1046 13.6352 9.9304 13.3798 9.67429C13.2097 9.50374 13.0747 9.30108 12.9826 9.07794C12.8905 8.8548 12.8431 8.61556 12.8431 8.37394C12.8431 8.13232 12.8905 7.89309 12.9826 7.66994C13.0747 7.4468 13.2097 7.24414 13.3798 7.07359ZM3.48419 17.8171C3.09393 17.6793 2.67484 17.6455 2.26777 17.7191C2.23159 17.727 2.19402 17.7255 2.15856 17.7148C2.12311 17.7041 2.09093 17.6846 2.06503 17.658C2.03293 17.6258 2.01143 17.5845 2.00345 17.5396C1.99547 17.4947 2.0014 17.4484 2.02043 17.4071C2.45226 16.4728 3.59772 14.5942 5.67172 16.1118C5.68255 16.1216 5.69122 16.1335 5.69715 16.1469C5.70308 16.1602 5.70615 16.1747 5.70615 16.1893C5.70615 16.2039 5.70308 16.2184 5.69715 16.2318C5.69122 16.2451 5.68255 16.2571 5.67172 16.2668C5.39405 16.4868 5.17174 16.7695 5.02276 17.092C4.87378 17.4144 4.80232 17.7676 4.81414 18.123C4.81565 18.1647 4.83279 18.2043 4.86213 18.2338C4.89146 18.2633 4.93082 18.2806 4.97227 18.2821C5.32401 18.2963 5.67415 18.2274 5.99457 18.0807C6.31499 17.934 6.59674 17.7138 6.81718 17.4377C6.82687 17.4257 6.83909 17.4161 6.85294 17.4095C6.86679 17.4029 6.88193 17.3994 6.89726 17.3994C6.91259 17.3994 6.92773 17.4029 6.94158 17.4095C6.95543 17.4161 6.96764 17.4257 6.97734 17.4377C7.26522 17.7824 8.05792 18.9083 7.18008 19.9955C6.79684 20.459 6.24773 20.7521 5.65144 20.8114C4.79995 20.9032 3.22671 21.1827 2.58404 21.9211C2.55824 21.9518 2.52463 21.975 2.48679 21.988C2.44895 22.001 2.4083 22.0035 2.36918 21.9951C2.33005 21.9868 2.29392 21.9679 2.26464 21.9404C2.23535 21.913 2.21401 21.8781 2.20289 21.8395C1.97583 21.0603 1.62307 19.3183 3.48419 17.8171Z"
      fill="#727FE1"
    />
  </StyledSVG>
);

export default RocketIcon;
