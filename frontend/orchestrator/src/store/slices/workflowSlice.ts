import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import axios from 'axios';

export interface Workflow {
  id: string;
  name: string;
  description: string;
  created_at: string;
}

export interface WorkflowExecution {
  execution_id: string;
  workflow_id: string;
  status: 'running' | 'completed' | 'failed';
  started_at?: string;
  completed_at?: string;
  outputs?: Record<string, any>;
}

export interface WorkflowState {
  workflows: Workflow[];
  executions: WorkflowExecution[];
  loading: boolean;
  error: string | null;
  selectedWorkflow: Workflow | null;
}

const initialState: WorkflowState = {
  workflows: [],
  executions: [],
  loading: false,
  error: null,
  selectedWorkflow: null,
};

// Async thunks
export const fetchWorkflows = createAsyncThunk(
  'workflow/fetchWorkflows',
  async () => {
    const response = await axios.get('/v1/workflows');
    return response.data.workflows;
  }
);

export const executeWorkflow = createAsyncThunk(
  'workflow/executeWorkflow',
  async (params: { workflowId: string; inputs: Record<string, any> }) => {
    const response = await axios.post('/v1/workflows/execute', {
      workflow_id: params.workflowId,
      inputs: params.inputs,
    });
    return response.data;
  }
);

export const getWorkflowStatus = createAsyncThunk(
  'workflow/getWorkflowStatus',
  async (executionId: string) => {
    const response = await axios.get(`/v1/workflows/${executionId}/status`);
    return response.data;
  }
);

const workflowSlice = createSlice({
  name: 'workflow',
  initialState,
  reducers: {
    setSelectedWorkflow: (state, action: PayloadAction<Workflow | null>) => {
      state.selectedWorkflow = action.payload;
    },
    clearExecutions: (state) => {
      state.executions = [];
    },
    updateExecutionStatus: (state, action: PayloadAction<{ executionId: string; status: string }>) => {
      const execution = state.executions.find(e => e.execution_id === action.payload.executionId);
      if (execution) {
        execution.status = action.payload.status as any;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchWorkflows.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchWorkflows.fulfilled, (state, action) => {
        state.loading = false;
        state.workflows = action.payload;
      })
      .addCase(fetchWorkflows.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch workflows';
      })
      .addCase(executeWorkflow.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(executeWorkflow.fulfilled, (state, action) => {
        state.loading = false;
        state.executions.push(action.payload);
      })
      .addCase(executeWorkflow.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to execute workflow';
      })
      .addCase(getWorkflowStatus.fulfilled, (state, action) => {
        const index = state.executions.findIndex(e => e.execution_id === action.payload.workflow_id);
        if (index !== -1) {
          state.executions[index] = { ...state.executions[index], ...action.payload };
        }
      });
  },
});

export const { setSelectedWorkflow, clearExecutions, updateExecutionStatus } = workflowSlice.actions;
export default workflowSlice.reducer;
