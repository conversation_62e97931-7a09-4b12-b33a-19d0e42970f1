// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v3.17.3
// source: sourcecontrol/v1/sourcecontrol.proto

package sourcecontrolv1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "github.com/lyft/clutch/backend/api/api/v1"
	v1 "github.com/lyft/clutch/backend/api/sourcecontrol/github/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	anypb "google.golang.org/protobuf/types/known/anypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Visibility int32

const (
	Visibility_UNSPECIFIED Visibility = 0
	Visibility_PUBLIC      Visibility = 1
	Visibility_PRIVATE     Visibility = 2
)

// Enum value maps for Visibility.
var (
	Visibility_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "PUBLIC",
		2: "PRIVATE",
	}
	Visibility_value = map[string]int32{
		"UNSPECIFIED": 0,
		"PUBLIC":      1,
		"PRIVATE":     2,
	}
)

func (x Visibility) Enum() *Visibility {
	p := new(Visibility)
	*p = x
	return p
}

func (x Visibility) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Visibility) Descriptor() protoreflect.EnumDescriptor {
	return file_sourcecontrol_v1_sourcecontrol_proto_enumTypes[0].Descriptor()
}

func (Visibility) Type() protoreflect.EnumType {
	return &file_sourcecontrol_v1_sourcecontrol_proto_enumTypes[0]
}

func (x Visibility) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Visibility.Descriptor instead.
func (Visibility) EnumDescriptor() ([]byte, []int) {
	return file_sourcecontrol_v1_sourcecontrol_proto_rawDescGZIP(), []int{0}
}

type GetRepositoryOptionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetRepositoryOptionsRequest) Reset() {
	*x = GetRepositoryOptionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sourcecontrol_v1_sourcecontrol_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRepositoryOptionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRepositoryOptionsRequest) ProtoMessage() {}

func (x *GetRepositoryOptionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_sourcecontrol_v1_sourcecontrol_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRepositoryOptionsRequest.ProtoReflect.Descriptor instead.
func (*GetRepositoryOptionsRequest) Descriptor() ([]byte, []int) {
	return file_sourcecontrol_v1_sourcecontrol_proto_rawDescGZIP(), []int{0}
}

// An entity encompasses members and organizations who are both owners of or contributors to source code.
type Entity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name     string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	PhotoUrl string `protobuf:"bytes,2,opt,name=photo_url,json=photoUrl,proto3" json:"photo_url,omitempty"`
}

func (x *Entity) Reset() {
	*x = Entity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sourcecontrol_v1_sourcecontrol_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Entity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Entity) ProtoMessage() {}

func (x *Entity) ProtoReflect() protoreflect.Message {
	mi := &file_sourcecontrol_v1_sourcecontrol_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Entity.ProtoReflect.Descriptor instead.
func (*Entity) Descriptor() ([]byte, []int) {
	return file_sourcecontrol_v1_sourcecontrol_proto_rawDescGZIP(), []int{1}
}

func (x *Entity) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Entity) GetPhotoUrl() string {
	if x != nil {
		return x.PhotoUrl
	}
	return ""
}

type GetRepositoryOptionsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AvailableOwners   []*Entity    `protobuf:"bytes,1,rep,name=available_owners,json=availableOwners,proto3" json:"available_owners,omitempty"`
	VisibilityOptions []Visibility `protobuf:"varint,2,rep,packed,name=visibility_options,json=visibilityOptions,proto3,enum=clutch.sourcecontrol.v1.Visibility" json:"visibility_options,omitempty"`
}

func (x *GetRepositoryOptionsResponse) Reset() {
	*x = GetRepositoryOptionsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sourcecontrol_v1_sourcecontrol_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRepositoryOptionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRepositoryOptionsResponse) ProtoMessage() {}

func (x *GetRepositoryOptionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_sourcecontrol_v1_sourcecontrol_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRepositoryOptionsResponse.ProtoReflect.Descriptor instead.
func (*GetRepositoryOptionsResponse) Descriptor() ([]byte, []int) {
	return file_sourcecontrol_v1_sourcecontrol_proto_rawDescGZIP(), []int{2}
}

func (x *GetRepositoryOptionsResponse) GetAvailableOwners() []*Entity {
	if x != nil {
		return x.AvailableOwners
	}
	return nil
}

func (x *GetRepositoryOptionsResponse) GetVisibilityOptions() []Visibility {
	if x != nil {
		return x.VisibilityOptions
	}
	return nil
}

type CreateRepositoryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Owner       string `protobuf:"bytes,1,opt,name=owner,proto3" json:"owner,omitempty"`
	Name        string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// Types that are assignable to Options:
	//
	//	*CreateRepositoryRequest_CustomOptions
	//	*CreateRepositoryRequest_GithubOptions
	Options isCreateRepositoryRequest_Options `protobuf_oneof:"options"`
}

func (x *CreateRepositoryRequest) Reset() {
	*x = CreateRepositoryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sourcecontrol_v1_sourcecontrol_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRepositoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRepositoryRequest) ProtoMessage() {}

func (x *CreateRepositoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_sourcecontrol_v1_sourcecontrol_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRepositoryRequest.ProtoReflect.Descriptor instead.
func (*CreateRepositoryRequest) Descriptor() ([]byte, []int) {
	return file_sourcecontrol_v1_sourcecontrol_proto_rawDescGZIP(), []int{3}
}

func (x *CreateRepositoryRequest) GetOwner() string {
	if x != nil {
		return x.Owner
	}
	return ""
}

func (x *CreateRepositoryRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateRepositoryRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (m *CreateRepositoryRequest) GetOptions() isCreateRepositoryRequest_Options {
	if m != nil {
		return m.Options
	}
	return nil
}

func (x *CreateRepositoryRequest) GetCustomOptions() *anypb.Any {
	if x, ok := x.GetOptions().(*CreateRepositoryRequest_CustomOptions); ok {
		return x.CustomOptions
	}
	return nil
}

func (x *CreateRepositoryRequest) GetGithubOptions() *v1.CreateRepositoryOptions {
	if x, ok := x.GetOptions().(*CreateRepositoryRequest_GithubOptions); ok {
		return x.GithubOptions
	}
	return nil
}

type isCreateRepositoryRequest_Options interface {
	isCreateRepositoryRequest_Options()
}

type CreateRepositoryRequest_CustomOptions struct {
	CustomOptions *anypb.Any `protobuf:"bytes,4,opt,name=custom_options,json=customOptions,proto3,oneof"`
}

type CreateRepositoryRequest_GithubOptions struct {
	GithubOptions *v1.CreateRepositoryOptions `protobuf:"bytes,5,opt,name=github_options,json=githubOptions,proto3,oneof"`
}

func (*CreateRepositoryRequest_CustomOptions) isCreateRepositoryRequest_Options() {}

func (*CreateRepositoryRequest_GithubOptions) isCreateRepositoryRequest_Options() {}

type CreateRepositoryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *CreateRepositoryResponse) Reset() {
	*x = CreateRepositoryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sourcecontrol_v1_sourcecontrol_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRepositoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRepositoryResponse) ProtoMessage() {}

func (x *CreateRepositoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_sourcecontrol_v1_sourcecontrol_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRepositoryResponse.ProtoReflect.Descriptor instead.
func (*CreateRepositoryResponse) Descriptor() ([]byte, []int) {
	return file_sourcecontrol_v1_sourcecontrol_proto_rawDescGZIP(), []int{4}
}

func (x *CreateRepositoryResponse) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

var File_sourcecontrol_v1_sourcecontrol_proto protoreflect.FileDescriptor

var file_sourcecontrol_v1_sourcecontrol_proto_rawDesc = []byte{
	0x0a, 0x24, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2f,
	0x76, 0x31, 0x2f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x76, 0x31, 0x1a,
	0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x61,
	0x6e, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2f, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2f, 0x76, 0x31, 0x2f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x1d, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f,
	0x72, 0x79, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x22, 0x39, 0x0a, 0x06, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x55, 0x72, 0x6c, 0x22, 0xbe, 0x01, 0x0a, 0x1c,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4a, 0x0a, 0x10,
	0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x76, 0x31,
	0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x0f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x12, 0x52, 0x0a, 0x12, 0x76, 0x69, 0x73, 0x69,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x56,
	0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x11, 0x76, 0x69, 0x73, 0x69, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xa8, 0x02, 0x0a,
	0x17, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x05, 0x6f, 0x77, 0x6e, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01,
	0x52, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3d, 0x0a, 0x0e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x41, 0x6e, 0x79, 0x48, 0x00, 0x52, 0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x60, 0x0a, 0x0e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x5f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e,
	0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x63, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x48, 0x00, 0x52, 0x0d, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x0e, 0x0a, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0x2c, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x75, 0x72, 0x6c, 0x2a, 0x36, 0x0a, 0x0a, 0x56, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x50, 0x55, 0x42, 0x4c, 0x49, 0x43, 0x10, 0x01,
	0x12, 0x0b, 0x0a, 0x07, 0x50, 0x52, 0x49, 0x56, 0x41, 0x54, 0x45, 0x10, 0x02, 0x32, 0x80, 0x03,
	0x0a, 0x10, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x41,
	0x50, 0x49, 0x12, 0xbc, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x6f, 0x72, 0x79, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x34, 0x2e, 0x63, 0x6c,
	0x75, 0x74, 0x63, 0x68, 0x2e, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x6f, 0x72, 0x79, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x35, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52,
	0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x37, 0xaa, 0xe1, 0x1c, 0x02, 0x08, 0x02,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2b, 0x3a, 0x01, 0x2a, 0x22, 0x26, 0x2f, 0x76, 0x31, 0x2f, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2f, 0x67, 0x65, 0x74,
	0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0xac, 0x01, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x30, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63,
	0x68, 0x2e, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x6f, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x33, 0xaa, 0xe1, 0x1c,
	0x02, 0x08, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x27, 0x3a, 0x01, 0x2a, 0x22, 0x22, 0x2f, 0x76,
	0x31, 0x2f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2f,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x6f, 0x72, 0x79,
	0x42, 0x45, 0x5a, 0x43, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6c,
	0x79, 0x66, 0x74, 0x2f, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2f, 0x62, 0x61, 0x63, 0x6b, 0x65,
	0x6e, 0x64, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x63, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x2f, 0x76, 0x31, 0x3b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_sourcecontrol_v1_sourcecontrol_proto_rawDescOnce sync.Once
	file_sourcecontrol_v1_sourcecontrol_proto_rawDescData = file_sourcecontrol_v1_sourcecontrol_proto_rawDesc
)

func file_sourcecontrol_v1_sourcecontrol_proto_rawDescGZIP() []byte {
	file_sourcecontrol_v1_sourcecontrol_proto_rawDescOnce.Do(func() {
		file_sourcecontrol_v1_sourcecontrol_proto_rawDescData = protoimpl.X.CompressGZIP(file_sourcecontrol_v1_sourcecontrol_proto_rawDescData)
	})
	return file_sourcecontrol_v1_sourcecontrol_proto_rawDescData
}

var file_sourcecontrol_v1_sourcecontrol_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_sourcecontrol_v1_sourcecontrol_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_sourcecontrol_v1_sourcecontrol_proto_goTypes = []interface{}{
	(Visibility)(0),                      // 0: clutch.sourcecontrol.v1.Visibility
	(*GetRepositoryOptionsRequest)(nil),  // 1: clutch.sourcecontrol.v1.GetRepositoryOptionsRequest
	(*Entity)(nil),                       // 2: clutch.sourcecontrol.v1.Entity
	(*GetRepositoryOptionsResponse)(nil), // 3: clutch.sourcecontrol.v1.GetRepositoryOptionsResponse
	(*CreateRepositoryRequest)(nil),      // 4: clutch.sourcecontrol.v1.CreateRepositoryRequest
	(*CreateRepositoryResponse)(nil),     // 5: clutch.sourcecontrol.v1.CreateRepositoryResponse
	(*anypb.Any)(nil),                    // 6: google.protobuf.Any
	(*v1.CreateRepositoryOptions)(nil),   // 7: clutch.sourcecontrol.github.v1.CreateRepositoryOptions
}
var file_sourcecontrol_v1_sourcecontrol_proto_depIdxs = []int32{
	2, // 0: clutch.sourcecontrol.v1.GetRepositoryOptionsResponse.available_owners:type_name -> clutch.sourcecontrol.v1.Entity
	0, // 1: clutch.sourcecontrol.v1.GetRepositoryOptionsResponse.visibility_options:type_name -> clutch.sourcecontrol.v1.Visibility
	6, // 2: clutch.sourcecontrol.v1.CreateRepositoryRequest.custom_options:type_name -> google.protobuf.Any
	7, // 3: clutch.sourcecontrol.v1.CreateRepositoryRequest.github_options:type_name -> clutch.sourcecontrol.github.v1.CreateRepositoryOptions
	1, // 4: clutch.sourcecontrol.v1.SourceControlAPI.GetRepositoryOptions:input_type -> clutch.sourcecontrol.v1.GetRepositoryOptionsRequest
	4, // 5: clutch.sourcecontrol.v1.SourceControlAPI.CreateRepository:input_type -> clutch.sourcecontrol.v1.CreateRepositoryRequest
	3, // 6: clutch.sourcecontrol.v1.SourceControlAPI.GetRepositoryOptions:output_type -> clutch.sourcecontrol.v1.GetRepositoryOptionsResponse
	5, // 7: clutch.sourcecontrol.v1.SourceControlAPI.CreateRepository:output_type -> clutch.sourcecontrol.v1.CreateRepositoryResponse
	6, // [6:8] is the sub-list for method output_type
	4, // [4:6] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_sourcecontrol_v1_sourcecontrol_proto_init() }
func file_sourcecontrol_v1_sourcecontrol_proto_init() {
	if File_sourcecontrol_v1_sourcecontrol_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_sourcecontrol_v1_sourcecontrol_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRepositoryOptionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sourcecontrol_v1_sourcecontrol_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Entity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sourcecontrol_v1_sourcecontrol_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRepositoryOptionsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sourcecontrol_v1_sourcecontrol_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRepositoryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sourcecontrol_v1_sourcecontrol_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRepositoryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_sourcecontrol_v1_sourcecontrol_proto_msgTypes[3].OneofWrappers = []interface{}{
		(*CreateRepositoryRequest_CustomOptions)(nil),
		(*CreateRepositoryRequest_GithubOptions)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_sourcecontrol_v1_sourcecontrol_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_sourcecontrol_v1_sourcecontrol_proto_goTypes,
		DependencyIndexes: file_sourcecontrol_v1_sourcecontrol_proto_depIdxs,
		EnumInfos:         file_sourcecontrol_v1_sourcecontrol_proto_enumTypes,
		MessageInfos:      file_sourcecontrol_v1_sourcecontrol_proto_msgTypes,
	}.Build()
	File_sourcecontrol_v1_sourcecontrol_proto = out.File
	file_sourcecontrol_v1_sourcecontrol_proto_rawDesc = nil
	file_sourcecontrol_v1_sourcecontrol_proto_goTypes = nil
	file_sourcecontrol_v1_sourcecontrol_proto_depIdxs = nil
}
