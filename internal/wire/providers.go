package wire

import (
	"github.com/cainuro/orchestrator/backend/service/audit"
	"github.com/cainuro/orchestrator/backend/service/autoscaler"
	"github.com/cainuro/orchestrator/backend/service/dbadmin"
	"github.com/cainuro/orchestrator/backend/service/envoycontrolplane"
	"github.com/cainuro/orchestrator/backend/service/search"
	"github.com/cainuro/orchestrator/backend/service/workflow"
	"github.com/cainuro/orchestrator/internal/audit"
	"github.com/cainuro/orchestrator/internal/cache"
	"github.com/cainuro/orchestrator/internal/config"
	"github.com/cainuro/orchestrator/internal/db"
	"github.com/google/wire"
	"go.uber.org/zap"
)

// ProviderSet contains all the providers for dependency injection
var ProviderSet = wire.NewSet(
	// Configuration
	ProvideLogger,
	ProvideConfig,

	// Database and Storage
	ProvideGenjiStore,
	ProvideRistrettoCache,
	ProvideImmuAuditSink,

	// Services
	ProvideSearchService,
	ProvideEnvoyControlPlaneService,
	ProvideAutoscalerService,
	ProvideWorkflowService,
	ProvideDBAdminService,
	ProvideAuditService,

	// Server
	ProvideServer,
)

// ProvideLogger provides a zap logger instance
func ProvideLogger() (*zap.Logger, error) {
	logger, err := zap.NewProduction()
	if err != nil {
		return nil, err
	}
	return logger, nil
}

// ProvideConfig provides the application configuration
func ProvideConfig(configPath string) (*config.Config, error) {
	return config.Load(configPath)
}

// ProvideGenjiStore provides a Genji database store
func ProvideGenjiStore(cfg *config.Config, logger *zap.Logger) (*db.GenjiStore, error) {
	return db.NewGenjiStore(cfg.DB.Path)
}

// ProvideRistrettoCache provides a Ristretto cache instance
func ProvideRistrettoCache(cfg *config.Config, logger *zap.Logger) (*cache.RistrettoCache, error) {
	cacheConfig := cache.CacheConfig{
		MaxSize: cfg.Cache.Size,
	}
	return cache.NewRistrettoCache(cacheConfig, logger)
}

// ProvideImmuAuditSink provides an ImmuDB audit sink
func ProvideImmuAuditSink(cfg *config.Config, logger *zap.Logger) (*audit.ImmuSink, error) {
	auditConfig := audit.ImmuConfig{
		Host:     "localhost",
		Port:     3322,
		Username: "immudb",
		Password: "immudb",
		Database: "cainuro_audit",
		DataDir:  cfg.Audit.Path,
	}
	return audit.NewImmuSink(auditConfig, logger)
}

// ProvideSearchService provides the search/discovery service
func ProvideSearchService(
	cfg *config.Config,
	logger *zap.Logger,
	cache *cache.RistrettoCache,
	store *db.GenjiStore,
) (search.Service, error) {
	return search.NewService(cfg, logger, cache, store)
}

// ProvideEnvoyControlPlaneService provides the Envoy control plane service
func ProvideEnvoyControlPlaneService(
	cfg *config.Config,
	logger *zap.Logger,
	cache *cache.RistrettoCache,
	store *db.GenjiStore,
) (envoycontrolplane.Service, error) {
	return envoycontrolplane.NewService(cfg, logger, cache, store)
}

// ProvideAutoscalerService provides the autoscaler service
func ProvideAutoscalerService(
	cfg *config.Config,
	logger *zap.Logger,
	cache *cache.RistrettoCache,
) (autoscaler.Service, error) {
	return autoscaler.NewService(cfg, logger, cache)
}

// ProvideWorkflowService provides the workflow execution service
func ProvideWorkflowService(
	cfg *config.Config,
	logger *zap.Logger,
	store *db.GenjiStore,
	cache *cache.RistrettoCache,
) (workflow.Service, error) {
	return workflow.NewService(cfg, logger, store, cache)
}

// ProvideDBAdminService provides the database administration service
func ProvideDBAdminService(
	cfg *config.Config,
	logger *zap.Logger,
	store *db.GenjiStore,
) (dbadmin.Service, error) {
	return dbadmin.NewService(cfg, logger, store)
}

// ProvideAuditService provides the audit service
func ProvideAuditService(
	cfg *config.Config,
	logger *zap.Logger,
	auditSink *audit.ImmuSink,
) (audit.Service, error) {
	return audit.NewService(cfg, logger, auditSink)
}

// Server represents the main server with all services
type Server struct {
	Config                   *config.Config
	Logger                   *zap.Logger
	GenjiStore               *db.GenjiStore
	RistrettoCache           *cache.RistrettoCache
	ImmuAuditSink            *audit.ImmuSink
	SearchService            search.Service
	EnvoyControlPlaneService envoycontrolplane.Service
	AutoscalerService        autoscaler.Service
	WorkflowService          workflow.Service
	DBAdminService           dbadmin.Service
	AuditService             audit.Service
}

// ProvideServer provides the main server instance
func ProvideServer(
	cfg *config.Config,
	logger *zap.Logger,
	genjiStore *db.GenjiStore,
	ristrettoCache *cache.RistrettoCache,
	immuAuditSink *audit.ImmuSink,
	searchService search.Service,
	envoyControlPlaneService envoycontrolplane.Service,
	autoscalerService autoscaler.Service,
	workflowService workflow.Service,
	dbAdminService dbadmin.Service,
	auditService audit.Service,
) *Server {
	return &Server{
		Config:                   cfg,
		Logger:                   logger,
		GenjiStore:               genjiStore,
		RistrettoCache:           ristrettoCache,
		ImmuAuditSink:            immuAuditSink,
		SearchService:            searchService,
		EnvoyControlPlaneService: envoyControlPlaneService,
		AutoscalerService:        autoscalerService,
		WorkflowService:          workflowService,
		DBAdminService:           dbAdminService,
		AuditService:             auditService,
	}
}

// Cleanup function for graceful shutdown
func (s *Server) Cleanup() error {
	var errors []error

	if err := s.GenjiStore.Close(); err != nil {
		errors = append(errors, err)
	}

	if err := s.RistrettoCache.Close(); err != nil {
		errors = append(errors, err)
	}

	if err := s.ImmuAuditSink.Close(); err != nil {
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return errors[0] // Return first error
	}

	return nil
}
