/*! For license information please see main.c7f4c705.js.LICENSE.txt */
(()=>{"use strict";var e={43:(e,t,n)=>{e.exports=n(202)},82:(e,t)=>{var n,r=Symbol.for("react.element"),a=Symbol.for("react.portal"),l=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),s=Symbol.for("react.provider"),c=Symbol.for("react.context"),u=Symbol.for("react.server_context"),d=Symbol.for("react.forward_ref"),f=Symbol.for("react.suspense"),m=Symbol.for("react.suspense_list"),h=Symbol.for("react.memo"),p=Symbol.for("react.lazy"),g=Symbol.for("react.offscreen");function x(e){if("object"===typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case l:case i:case o:case f:case m:return e;default:switch(e=e&&e.$$typeof){case u:case c:case d:case p:case h:case s:return e;default:return t}}case a:return t}}}n=Symbol.for("react.module.reference")},86:(e,t,n)=>{n(82)},153:(e,t,n)=>{var r=n(43),a=Symbol.for("react.element"),l=Symbol.for("react.fragment"),o=Object.prototype.hasOwnProperty,i=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,s={key:!0,ref:!0,__self:!0,__source:!0};function c(e,t,n){var r,l={},c=null,u=null;for(r in void 0!==n&&(c=""+n),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(u=t.ref),t)o.call(t,r)&&!s.hasOwnProperty(r)&&(l[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===l[r]&&(l[r]=t[r]);return{$$typeof:a,type:e,key:c,ref:u,props:l,_owner:i.current}}t.Fragment=l,t.jsx=c,t.jsxs=c},202:(e,t)=>{var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),i=Symbol.for("react.provider"),s=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),m=Symbol.iterator;var h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},p=Object.assign,g={};function x(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}function y(){}function v(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}x.prototype.isReactComponent={},x.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},x.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},y.prototype=x.prototype;var b=v.prototype=new y;b.constructor=v,p(b,x.prototype),b.isPureReactComponent=!0;var w=Array.isArray,j=Object.prototype.hasOwnProperty,N={current:null},k={key:!0,ref:!0,__self:!0,__source:!0};function S(e,t,r){var a,l={},o=null,i=null;if(null!=t)for(a in void 0!==t.ref&&(i=t.ref),void 0!==t.key&&(o=""+t.key),t)j.call(t,a)&&!k.hasOwnProperty(a)&&(l[a]=t[a]);var s=arguments.length-2;if(1===s)l.children=r;else if(1<s){for(var c=Array(s),u=0;u<s;u++)c[u]=arguments[u+2];l.children=c}if(e&&e.defaultProps)for(a in s=e.defaultProps)void 0===l[a]&&(l[a]=s[a]);return{$$typeof:n,type:e,key:o,ref:i,props:l,_owner:N.current}}function E(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var C=/\/+/g;function O(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function P(e,t,a,l,o){var i=typeof e;"undefined"!==i&&"boolean"!==i||(e=null);var s=!1;if(null===e)s=!0;else switch(i){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case n:case r:s=!0}}if(s)return o=o(s=e),e=""===l?"."+O(s,0):l,w(o)?(a="",null!=e&&(a=e.replace(C,"$&/")+"/"),P(o,t,a,"",(function(e){return e}))):null!=o&&(E(o)&&(o=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(o,a+(!o.key||s&&s.key===o.key?"":(""+o.key).replace(C,"$&/")+"/")+e)),t.push(o)),1;if(s=0,l=""===l?".":l+":",w(e))for(var c=0;c<e.length;c++){var u=l+O(i=e[c],c);s+=P(i,t,a,u,o)}else if(u=function(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=m&&e[m]||e["@@iterator"])?e:null}(e),"function"===typeof u)for(e=u.call(e),c=0;!(i=e.next()).done;)s+=P(i=i.value,t,a,u=l+O(i,c++),o);else if("object"===i)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function _(e,t,n){if(null==e)return e;var r=[],a=0;return P(e,r,"","",(function(e){return t.call(n,e,a++)})),r}function R(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var L={current:null},T={transition:null},A={ReactCurrentDispatcher:L,ReactCurrentBatchConfig:T,ReactCurrentOwner:N};function M(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:_,forEach:function(e,t,n){_(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return _(e,(function(){t++})),t},toArray:function(e){return _(e,(function(e){return e}))||[]},only:function(e){if(!E(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=x,t.Fragment=a,t.Profiler=o,t.PureComponent=v,t.StrictMode=l,t.Suspense=u,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=A,t.act=M,t.cloneElement=function(e,t,r){if(null===e||void 0===e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var a=p({},e.props),l=e.key,o=e.ref,i=e._owner;if(null!=t){if(void 0!==t.ref&&(o=t.ref,i=N.current),void 0!==t.key&&(l=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(c in t)j.call(t,c)&&!k.hasOwnProperty(c)&&(a[c]=void 0===t[c]&&void 0!==s?s[c]:t[c])}var c=arguments.length-2;if(1===c)a.children=r;else if(1<c){s=Array(c);for(var u=0;u<c;u++)s[u]=arguments[u+2];a.children=s}return{$$typeof:n,type:e.type,key:l,ref:o,props:a,_owner:i}},t.createContext=function(e){return(e={$$typeof:s,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:i,_context:e},e.Consumer=e},t.createElement=S,t.createFactory=function(e){var t=S.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:c,render:e}},t.isValidElement=E,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:R}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=T.transition;T.transition={};try{e()}finally{T.transition=t}},t.unstable_act=M,t.useCallback=function(e,t){return L.current.useCallback(e,t)},t.useContext=function(e){return L.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return L.current.useDeferredValue(e)},t.useEffect=function(e,t){return L.current.useEffect(e,t)},t.useId=function(){return L.current.useId()},t.useImperativeHandle=function(e,t,n){return L.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return L.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return L.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return L.current.useMemo(e,t)},t.useReducer=function(e,t,n){return L.current.useReducer(e,t,n)},t.useRef=function(e){return L.current.useRef(e)},t.useState=function(e){return L.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return L.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return L.current.useTransition()},t.version="18.3.1"},219:(e,t,n)=>{var r=n(763),a={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},l={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},o={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},i={};function s(e){return r.isMemo(e)?o:i[e.$$typeof]||a}i[r.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},i[r.Memo]=o;var c=Object.defineProperty,u=Object.getOwnPropertyNames,d=Object.getOwnPropertySymbols,f=Object.getOwnPropertyDescriptor,m=Object.getPrototypeOf,h=Object.prototype;e.exports=function e(t,n,r){if("string"!==typeof n){if(h){var a=m(n);a&&a!==h&&e(t,a,r)}var o=u(n);d&&(o=o.concat(d(n)));for(var i=s(t),p=s(n),g=0;g<o.length;++g){var x=o[g];if(!l[x]&&(!r||!r[x])&&(!p||!p[x])&&(!i||!i[x])){var y=f(n,x);try{c(t,x,y)}catch(v){}}}}return t}},234:(e,t)=>{function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<l(a,t)))break e;e[r]=t,e[n]=a,n=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,o=a>>>1;r<o;){var i=2*(r+1)-1,s=e[i],c=i+1,u=e[c];if(0>l(s,n))c<a&&0>l(u,s)?(e[r]=u,e[c]=n,r=c):(e[r]=s,e[i]=n,r=i);else{if(!(c<a&&0>l(u,n)))break e;e[r]=u,e[c]=n,r=c}}}return t}function l(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"===typeof performance&&"function"===typeof performance.now){var o=performance;t.unstable_now=function(){return o.now()}}else{var i=Date,s=i.now();t.unstable_now=function(){return i.now()-s}}var c=[],u=[],d=1,f=null,m=3,h=!1,p=!1,g=!1,x="function"===typeof setTimeout?setTimeout:null,y="function"===typeof clearTimeout?clearTimeout:null,v="undefined"!==typeof setImmediate?setImmediate:null;function b(e){for(var t=r(u);null!==t;){if(null===t.callback)a(u);else{if(!(t.startTime<=e))break;a(u),t.sortIndex=t.expirationTime,n(c,t)}t=r(u)}}function w(e){if(g=!1,b(e),!p)if(null!==r(c))p=!0,T(j);else{var t=r(u);null!==t&&A(w,t.startTime-e)}}function j(e,n){p=!1,g&&(g=!1,y(E),E=-1),h=!0;var l=m;try{for(b(n),f=r(c);null!==f&&(!(f.expirationTime>n)||e&&!P());){var o=f.callback;if("function"===typeof o){f.callback=null,m=f.priorityLevel;var i=o(f.expirationTime<=n);n=t.unstable_now(),"function"===typeof i?f.callback=i:f===r(c)&&a(c),b(n)}else a(c);f=r(c)}if(null!==f)var s=!0;else{var d=r(u);null!==d&&A(w,d.startTime-n),s=!1}return s}finally{f=null,m=l,h=!1}}"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var N,k=!1,S=null,E=-1,C=5,O=-1;function P(){return!(t.unstable_now()-O<C)}function _(){if(null!==S){var e=t.unstable_now();O=e;var n=!0;try{n=S(!0,e)}finally{n?N():(k=!1,S=null)}}else k=!1}if("function"===typeof v)N=function(){v(_)};else if("undefined"!==typeof MessageChannel){var R=new MessageChannel,L=R.port2;R.port1.onmessage=_,N=function(){L.postMessage(null)}}else N=function(){x(_,0)};function T(e){S=e,k||(k=!0,N())}function A(e,n){E=x((function(){e(t.unstable_now())}),n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){p||h||(p=!0,T(j))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):C=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return m},t.unstable_getFirstCallbackNode=function(){return r(c)},t.unstable_next=function(e){switch(m){case 1:case 2:case 3:var t=3;break;default:t=m}var n=m;m=t;try{return e()}finally{m=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=m;m=e;try{return t()}finally{m=n}},t.unstable_scheduleCallback=function(e,a,l){var o=t.unstable_now();switch("object"===typeof l&&null!==l?l="number"===typeof(l=l.delay)&&0<l?o+l:o:l=o,e){case 1:var i=-1;break;case 2:i=250;break;case 5:i=1073741823;break;case 4:i=1e4;break;default:i=5e3}return e={id:d++,callback:a,priorityLevel:e,startTime:l,expirationTime:i=l+i,sortIndex:-1},l>o?(e.sortIndex=l,n(u,e),null===r(c)&&e===r(u)&&(g?(y(E),E=-1):g=!0,A(w,l-o))):(e.sortIndex=i,n(c,e),p||h||(p=!0,T(j))),e},t.unstable_shouldYield=P,t.unstable_wrapCallback=function(e){var t=m;return function(){var n=m;m=t;try{return e.apply(this,arguments)}finally{m=n}}}},330:(e,t,n)=>{var r=n(43);var a="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t},l=r.useState,o=r.useEffect,i=r.useLayoutEffect,s=r.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!a(e,n)}catch(r){return!0}}var u="undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=l({inst:{value:n,getSnapshot:t}}),a=r[0].inst,u=r[1];return i((function(){a.value=n,a.getSnapshot=t,c(a)&&u({inst:a})}),[e,n,t]),o((function(){return c(a)&&u({inst:a}),e((function(){c(a)&&u({inst:a})}))}),[e]),s(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:u},391:(e,t,n)=>{var r=n(950);t.createRoot=r.createRoot,t.hydrateRoot=r.hydrateRoot},443:(e,t,n)=>{e.exports=n(717)},461:(e,t,n)=>{e.exports=n(330)},579:(e,t,n)=>{e.exports=n(153)},717:(e,t,n)=>{var r=n(43),a=n(461);var l="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t},o=a.useSyncExternalStore,i=r.useRef,s=r.useEffect,c=r.useMemo,u=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,a){var d=i(null);if(null===d.current){var f={hasValue:!1,value:null};d.current=f}else f=d.current;d=c((function(){function e(e){if(!s){if(s=!0,o=e,e=r(e),void 0!==a&&f.hasValue){var t=f.value;if(a(t,e))return i=t}return i=e}if(t=i,l(o,e))return t;var n=r(e);return void 0!==a&&a(t,n)?(o=e,t):(o=e,i=n)}var o,i,s=!1,c=void 0===n?null:n;return[function(){return e(t())},null===c?void 0:function(){return e(c())}]}),[t,n,r,a]);var m=o(e,d[0],d[1]);return s((function(){f.hasValue=!0,f.value=m}),[m]),u(m),m}},730:(e,t,n)=>{var r=n(43),a=n(853);function l(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var o=new Set,i={};function s(e,t){c(e,t),c(e+"Capture",t)}function c(e,t){for(i[e]=t,e=0;e<t.length;e++)o.add(t[e])}var u=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),d=Object.prototype.hasOwnProperty,f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,m={},h={};function p(e,t,n,r,a,l,o){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=a,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=l,this.removeEmptyString=o}var g={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach((function(e){g[e]=new p(e,0,!1,e,null,!1,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t=e[0];g[t]=new p(t,1,!1,e[1],null,!1,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){g[e]=new p(e,2,!1,e.toLowerCase(),null,!1,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){g[e]=new p(e,2,!1,e,null,!1,!1)})),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach((function(e){g[e]=new p(e,3,!1,e.toLowerCase(),null,!1,!1)})),["checked","multiple","muted","selected"].forEach((function(e){g[e]=new p(e,3,!0,e,null,!1,!1)})),["capture","download"].forEach((function(e){g[e]=new p(e,4,!1,e,null,!1,!1)})),["cols","rows","size","span"].forEach((function(e){g[e]=new p(e,6,!1,e,null,!1,!1)})),["rowSpan","start"].forEach((function(e){g[e]=new p(e,5,!1,e.toLowerCase(),null,!1,!1)}));var x=/[\-:]([a-z])/g;function y(e){return e[1].toUpperCase()}function v(e,t,n,r){var a=g.hasOwnProperty(t)?g[t]:null;(null!==a?0!==a.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null===t||"undefined"===typeof t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,a,r)&&(n=null),r||null===a?function(e){return!!d.call(h,e)||!d.call(m,e)&&(f.test(e)?h[e]=!0:(m[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):a.mustUseProperty?e[a.propertyName]=null===n?3!==a.type&&"":n:(t=a.attributeName,r=a.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(a=a.type)||4===a&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach((function(e){var t=e.replace(x,y);g[t]=new p(t,1,!1,e,null,!1,!1)})),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach((function(e){var t=e.replace(x,y);g[t]=new p(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(x,y);g[t]=new p(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)})),["tabIndex","crossOrigin"].forEach((function(e){g[e]=new p(e,1,!1,e.toLowerCase(),null,!1,!1)})),g.xlinkHref=new p("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach((function(e){g[e]=new p(e,1,!1,e.toLowerCase(),null,!0,!0)}));var b=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,w=Symbol.for("react.element"),j=Symbol.for("react.portal"),N=Symbol.for("react.fragment"),k=Symbol.for("react.strict_mode"),S=Symbol.for("react.profiler"),E=Symbol.for("react.provider"),C=Symbol.for("react.context"),O=Symbol.for("react.forward_ref"),P=Symbol.for("react.suspense"),_=Symbol.for("react.suspense_list"),R=Symbol.for("react.memo"),L=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var T=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var A=Symbol.iterator;function M(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=A&&e[A]||e["@@iterator"])?e:null}var I,D=Object.assign;function F(e){if(void 0===I)try{throw Error()}catch(dn){var t=dn.stack.trim().match(/\n( *(at )?)/);I=t&&t[1]||""}return"\n"+I+e}var z=!1;function U(e,t){if(!e||z)return"";z=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(pn){var r=pn}Reflect.construct(e,[],t)}else{try{t.call()}catch(pn){r=pn}e.call(t.prototype)}else{try{throw Error()}catch(pn){r=pn}e()}}catch(pn){if(pn&&r&&"string"===typeof pn.stack){for(var a=pn.stack.split("\n"),l=r.stack.split("\n"),o=a.length-1,i=l.length-1;1<=o&&0<=i&&a[o]!==l[i];)i--;for(;1<=o&&0<=i;o--,i--)if(a[o]!==l[i]){if(1!==o||1!==i)do{if(o--,0>--i||a[o]!==l[i]){var s="\n"+a[o].replace(" at new "," at ");return e.displayName&&s.includes("<anonymous>")&&(s=s.replace("<anonymous>",e.displayName)),s}}while(1<=o&&0<=i);break}}}finally{z=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?F(e):""}function B(e){switch(e.tag){case 5:return F(e.type);case 16:return F("Lazy");case 13:return F("Suspense");case 19:return F("SuspenseList");case 0:case 2:case 15:return e=U(e.type,!1);case 11:return e=U(e.type.render,!1);case 1:return e=U(e.type,!0);default:return""}}function W(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case N:return"Fragment";case j:return"Portal";case S:return"Profiler";case k:return"StrictMode";case P:return"Suspense";case _:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case C:return(e.displayName||"Context")+".Consumer";case E:return(e._context.displayName||"Context")+".Provider";case O:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case R:return null!==(t=e.displayName||null)?t:W(e.type)||"Memo";case L:t=e._payload,e=e._init;try{return W(e(t))}catch(dn){}}return null}function V(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return W(t);case 8:return t===k?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"===typeof t)return t.displayName||t.name||null;if("string"===typeof t)return t}return null}function q(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function $(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function H(e){e._valueTracker||(e._valueTracker=function(e){var t=$(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var a=n.get,l=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,l.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function Q(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=$(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function K(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(vn){return e.body}}function J(e,t){var n=t.checked;return D({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function X(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=q(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function Z(e,t){null!=(t=t.checked)&&v(e,"checked",t,!1)}function G(e,t){Z(e,t);var n=q(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,q(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function Y(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&K(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+q(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(l(91));return D({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ae(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(l(92));if(te(n)){if(1<n.length)throw Error(l(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:q(n)}}function le(e,t){var n=q(t.value),r=q(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function oe(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function ie(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function se(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?ie(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ce,ue=function(e){return"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(t,n,r,a){MSApp.execUnsafeLocalFunction((function(){return e(t,n)}))}:e}((function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ce=ce||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ce.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}}));function de(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var fe={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},me=["Webkit","ms","Moz","O"];function he(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||fe.hasOwnProperty(e)&&fe[e]?(""+t).trim():t+"px"}function pe(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),a=he(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,a):e[n]=a}}Object.keys(fe).forEach((function(e){me.forEach((function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),fe[t]=fe[e]}))}));var ge=D({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function xe(e,t){if(t){if(ge[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(l(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(l(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(l(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(l(62))}}function ye(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ve=null;function be(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var we=null,je=null,Ne=null;function ke(e){if(e=_a(e)){if("function"!==typeof we)throw Error(l(280));var t=e.stateNode;t&&(t=La(t),we(e.stateNode,e.type,t))}}function Se(e){je?Ne?Ne.push(e):Ne=[e]:je=e}function Ee(){if(je){var e=je,t=Ne;if(Ne=je=null,ke(e),t)for(e=0;e<t.length;e++)ke(t[e])}}function Ce(e,t){return e(t)}function Oe(){}var Pe=!1;function _e(e,t,n){if(Pe)return e(t,n);Pe=!0;try{return Ce(e,t,n)}finally{Pe=!1,(null!==je||null!==Ne)&&(Oe(),Ee())}}function Re(e,t){var n=e.stateNode;if(null===n)return null;var r=La(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(l(231,t,typeof n));return n}var Le=!1;if(u)try{var Te={};Object.defineProperty(Te,"passive",{get:function(){Le=!0}}),window.addEventListener("test",Te,Te),window.removeEventListener("test",Te,Te)}catch(cn){Le=!1}function Ae(e,t,n,r,a,l,o,i,s){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(bn){this.onError(bn)}}var Me=!1,Ie=null,De=!1,Fe=null,ze={onError:function(e){Me=!0,Ie=e}};function Ue(e,t,n,r,a,l,o,i,s){Me=!1,Ie=null,Ae.apply(ze,arguments)}function Be(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function We(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function Ve(e){if(Be(e)!==e)throw Error(l(188))}function qe(e){return e=function(e){var t=e.alternate;if(!t){if(null===(t=Be(e)))throw Error(l(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var o=a.alternate;if(null===o){if(null!==(r=a.return)){n=r;continue}break}if(a.child===o.child){for(o=a.child;o;){if(o===n)return Ve(a),e;if(o===r)return Ve(a),t;o=o.sibling}throw Error(l(188))}if(n.return!==r.return)n=a,r=o;else{for(var i=!1,s=a.child;s;){if(s===n){i=!0,n=a,r=o;break}if(s===r){i=!0,r=a,n=o;break}s=s.sibling}if(!i){for(s=o.child;s;){if(s===n){i=!0,n=o,r=a;break}if(s===r){i=!0,r=o,n=a;break}s=s.sibling}if(!i)throw Error(l(189))}}if(n.alternate!==r)throw Error(l(190))}if(3!==n.tag)throw Error(l(188));return n.stateNode.current===n?e:t}(e),null!==e?$e(e):null}function $e(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=$e(e);if(null!==t)return t;e=e.sibling}return null}var He=a.unstable_scheduleCallback,Qe=a.unstable_cancelCallback,Ke=a.unstable_shouldYield,Je=a.unstable_requestPaint,Xe=a.unstable_now,Ze=a.unstable_getCurrentPriorityLevel,Ge=a.unstable_ImmediatePriority,Ye=a.unstable_UserBlockingPriority,et=a.unstable_NormalPriority,tt=a.unstable_LowPriority,nt=a.unstable_IdlePriority,rt=null,at=null;var lt=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(ot(e)/it|0)|0},ot=Math.log,it=Math.LN2;var st=64,ct=4194304;function ut(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function dt(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,a=e.suspendedLanes,l=e.pingedLanes,o=268435455&n;if(0!==o){var i=o&~a;0!==i?r=ut(i):0!==(l&=o)&&(r=ut(l))}else 0!==(o=n&~a)?r=ut(o):0!==l&&(r=ut(l));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&a)&&((a=r&-r)>=(l=t&-t)||16===a&&0!==(4194240&l)))return t;if(0!==(4&r)&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)a=1<<(n=31-lt(t)),r|=e[n],t&=~a;return r}function ft(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function mt(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function ht(){var e=st;return 0===(4194240&(st<<=1))&&(st=64),e}function pt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function gt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-lt(t)]=n}function xt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-lt(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}var yt=0;function vt(e){return 1<(e&=-e)?4<e?0!==(268435455&e)?16:536870912:4:1}var bt,wt,jt,Nt,kt,St=!1,Et=[],Ct=null,Ot=null,Pt=null,_t=new Map,Rt=new Map,Lt=[],Tt="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function At(e,t){switch(e){case"focusin":case"focusout":Ct=null;break;case"dragenter":case"dragleave":Ot=null;break;case"mouseover":case"mouseout":Pt=null;break;case"pointerover":case"pointerout":_t.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Rt.delete(t.pointerId)}}function Mt(e,t,n,r,a,l){return null===e||e.nativeEvent!==l?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:l,targetContainers:[a]},null!==t&&(null!==(t=_a(t))&&wt(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function It(e){var t=Pa(e.target);if(null!==t){var n=Be(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=We(n)))return e.blockedOn=t,void kt(e.priority,(function(){jt(n)}))}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Dt(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Kt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=_a(n))&&wt(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);ve=r,n.target.dispatchEvent(r),ve=null,t.shift()}return!0}function Ft(e,t,n){Dt(e)&&n.delete(t)}function zt(){St=!1,null!==Ct&&Dt(Ct)&&(Ct=null),null!==Ot&&Dt(Ot)&&(Ot=null),null!==Pt&&Dt(Pt)&&(Pt=null),_t.forEach(Ft),Rt.forEach(Ft)}function Ut(e,t){e.blockedOn===t&&(e.blockedOn=null,St||(St=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,zt)))}function Bt(e){function t(t){return Ut(t,e)}if(0<Et.length){Ut(Et[0],e);for(var n=1;n<Et.length;n++){var r=Et[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==Ct&&Ut(Ct,e),null!==Ot&&Ut(Ot,e),null!==Pt&&Ut(Pt,e),_t.forEach(t),Rt.forEach(t),n=0;n<Lt.length;n++)(r=Lt[n]).blockedOn===e&&(r.blockedOn=null);for(;0<Lt.length&&null===(n=Lt[0]).blockedOn;)It(n),null===n.blockedOn&&Lt.shift()}var Wt=b.ReactCurrentBatchConfig,Vt=!0;function qt(e,t,n,r){var a=yt,l=Wt.transition;Wt.transition=null;try{yt=1,Ht(e,t,n,r)}finally{yt=a,Wt.transition=l}}function $t(e,t,n,r){var a=yt,l=Wt.transition;Wt.transition=null;try{yt=4,Ht(e,t,n,r)}finally{yt=a,Wt.transition=l}}function Ht(e,t,n,r){if(Vt){var a=Kt(e,t,n,r);if(null===a)ta(e,t,r,Qt,n),At(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return Ct=Mt(Ct,e,t,n,r,a),!0;case"dragenter":return Ot=Mt(Ot,e,t,n,r,a),!0;case"mouseover":return Pt=Mt(Pt,e,t,n,r,a),!0;case"pointerover":var l=a.pointerId;return _t.set(l,Mt(_t.get(l)||null,e,t,n,r,a)),!0;case"gotpointercapture":return l=a.pointerId,Rt.set(l,Mt(Rt.get(l)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(At(e,r),4&t&&-1<Tt.indexOf(e)){for(;null!==a;){var l=_a(a);if(null!==l&&bt(l),null===(l=Kt(e,t,n,r))&&ta(e,t,r,Qt,n),l===a)break;a=l}null!==a&&r.stopPropagation()}else ta(e,t,r,null,n)}}var Qt=null;function Kt(e,t,n,r){if(Qt=null,null!==(e=Pa(e=be(r))))if(null===(t=Be(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=We(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Qt=e,null}function Jt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Ze()){case Ge:return 1;case Ye:return 4;case et:case tt:return 16;case nt:return 536870912;default:return 16}default:return 16}}var Xt=null,Zt=null,Gt=null;function Yt(){if(Gt)return Gt;var e,t,n=Zt,r=n.length,a="value"in Xt?Xt.value:Xt.textContent,l=a.length;for(e=0;e<r&&n[e]===a[e];e++);var o=r-e;for(t=1;t<=o&&n[r-t]===a[l-t];t++);return Gt=a.slice(e,1<t?1-t:void 0)}function en(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function tn(){return!0}function nn(){return!1}function ln(e){function t(t,n,r,a,l){for(var o in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=l,this.currentTarget=null,e)e.hasOwnProperty(o)&&(t=e[o],this[o]=t?t(a):a[o]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?tn:nn,this.isPropagationStopped=nn,this}return D(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=tn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=tn)},persist:function(){},isPersistent:tn}),t}var on,sn,fn,mn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},hn=ln(mn),yn=D({},mn,{view:0,detail:0}),wn=ln(yn),jn=D({},yn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Mn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==fn&&(fn&&"mousemove"===e.type?(on=e.screenX-fn.screenX,sn=e.screenY-fn.screenY):sn=on=0,fn=e),on)},movementY:function(e){return"movementY"in e?e.movementY:sn}}),Nn=ln(jn),kn=ln(D({},jn,{dataTransfer:0})),Sn=ln(D({},yn,{relatedTarget:0})),En=ln(D({},mn,{animationName:0,elapsedTime:0,pseudoElement:0})),Cn=D({},mn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),On=ln(Cn),Pn=ln(D({},mn,{data:0})),_n={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Rn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Ln={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function An(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=Ln[e])&&!!t[e]}function Mn(){return An}var In=D({},yn,{key:function(e){if(e.key){var t=_n[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=en(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?Rn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Mn,charCode:function(e){return"keypress"===e.type?en(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?en(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),Dn=ln(In),zn=ln(D({},jn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Un=ln(D({},yn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Mn})),Bn=ln(D({},mn,{propertyName:0,elapsedTime:0,pseudoElement:0})),Wn=D({},jn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Vn=ln(Wn),qn=[9,13,27,32],$n=u&&"CompositionEvent"in window,Hn=null;u&&"documentMode"in document&&(Hn=document.documentMode);var Qn=u&&"TextEvent"in window&&!Hn,Kn=u&&(!$n||Hn&&8<Hn&&11>=Hn),Jn=String.fromCharCode(32),Xn=!1;function Zn(e,t){switch(e){case"keyup":return-1!==qn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Gn(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Yn=!1;var er={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function tr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!er[e.type]:"textarea"===t}function nr(e,t,n,r){Se(r),0<(t=ra(t,"onChange")).length&&(n=new hn("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var rr=null,ar=null;function lr(e){Jr(e,0)}function or(e){if(Q(Ra(e)))return e}function ir(e,t){if("change"===e)return t}var sr=!1;if(u){var cr;if(u){var ur="oninput"in document;if(!ur){var dr=document.createElement("div");dr.setAttribute("oninput","return;"),ur="function"===typeof dr.oninput}cr=ur}else cr=!1;sr=cr&&(!document.documentMode||9<document.documentMode)}function fr(){rr&&(rr.detachEvent("onpropertychange",mr),ar=rr=null)}function mr(e){if("value"===e.propertyName&&or(ar)){var t=[];nr(t,ar,e,be(e)),_e(lr,t)}}function hr(e,t,n){"focusin"===e?(fr(),ar=n,(rr=t).attachEvent("onpropertychange",mr)):"focusout"===e&&fr()}function pr(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return or(ar)}function gr(e,t){if("click"===e)return or(t)}function xr(e,t){if("input"===e||"change"===e)return or(t)}var yr="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function vr(e,t){if(yr(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!d.call(t,a)||!yr(e[a],t[a]))return!1}return!0}function br(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function wr(e,t){var n,r=br(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=br(r)}}function jr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?jr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function Nr(){for(var e=window,t=K();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(gn){n=!1}if(!n)break;t=K((e=t.contentWindow).document)}return t}function kr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function Sr(e){var t=Nr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&jr(n.ownerDocument.documentElement,n)){if(null!==r&&kr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var a=n.textContent.length,l=Math.min(r.start,a);r=void 0===r.end?l:Math.min(r.end,a),!e.extend&&l>r&&(a=r,r=l,l=a),a=wr(n,l);var o=wr(n,r);a&&o&&(1!==e.rangeCount||e.anchorNode!==a.node||e.anchorOffset!==a.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&((t=t.createRange()).setStart(a.node,a.offset),e.removeAllRanges(),l>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"===typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Er=u&&"documentMode"in document&&11>=document.documentMode,Cr=null,Or=null,Pr=null,_r=!1;function Rr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;_r||null==Cr||Cr!==K(r)||("selectionStart"in(r=Cr)&&kr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},Pr&&vr(Pr,r)||(Pr=r,0<(r=ra(Or,"onSelect")).length&&(t=new hn("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Cr)))}function Lr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Tr={animationend:Lr("Animation","AnimationEnd"),animationiteration:Lr("Animation","AnimationIteration"),animationstart:Lr("Animation","AnimationStart"),transitionend:Lr("Transition","TransitionEnd")},Ar={},Mr={};function Ir(e){if(Ar[e])return Ar[e];if(!Tr[e])return e;var t,n=Tr[e];for(t in n)if(n.hasOwnProperty(t)&&t in Mr)return Ar[e]=n[t];return e}u&&(Mr=document.createElement("div").style,"AnimationEvent"in window||(delete Tr.animationend.animation,delete Tr.animationiteration.animation,delete Tr.animationstart.animation),"TransitionEvent"in window||delete Tr.transitionend.transition);var Dr=Ir("animationend"),Fr=Ir("animationiteration"),zr=Ir("animationstart"),Ur=Ir("transitionend"),Br=new Map,Wr="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Vr(e,t){Br.set(e,t),s(t,[e])}for(var qr=0;qr<Wr.length;qr++){var $r=Wr[qr];Vr($r.toLowerCase(),"on"+($r[0].toUpperCase()+$r.slice(1)))}Vr(Dr,"onAnimationEnd"),Vr(Fr,"onAnimationIteration"),Vr(zr,"onAnimationStart"),Vr("dblclick","onDoubleClick"),Vr("focusin","onFocus"),Vr("focusout","onBlur"),Vr(Ur,"onTransitionEnd"),c("onMouseEnter",["mouseout","mouseover"]),c("onMouseLeave",["mouseout","mouseover"]),c("onPointerEnter",["pointerout","pointerover"]),c("onPointerLeave",["pointerout","pointerover"]),s("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),s("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),s("onBeforeInput",["compositionend","keypress","textInput","paste"]),s("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Hr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Qr=new Set("cancel close invalid load scroll toggle".split(" ").concat(Hr));function Kr(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,a,o,i,s,c){if(Ue.apply(this,arguments),Me){if(!Me)throw Error(l(198));var u=Ie;Me=!1,Ie=null,De||(De=!0,Fe=u)}}(r,t,void 0,e),e.currentTarget=null}function Jr(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var l=void 0;if(t)for(var o=r.length-1;0<=o;o--){var i=r[o],s=i.instance,c=i.currentTarget;if(i=i.listener,s!==l&&a.isPropagationStopped())break e;Kr(a,i,c),l=s}else for(o=0;o<r.length;o++){if(s=(i=r[o]).instance,c=i.currentTarget,i=i.listener,s!==l&&a.isPropagationStopped())break e;Kr(a,i,c),l=s}}}if(De)throw e=Fe,De=!1,Fe=null,e}function Xr(e,t){var n=t[Ea];void 0===n&&(n=t[Ea]=new Set);var r=e+"__bubble";n.has(r)||(ea(t,e,2,!1),n.add(r))}function Zr(e,t,n){var r=0;t&&(r|=4),ea(n,e,r,t)}var Gr="_reactListening"+Math.random().toString(36).slice(2);function Yr(e){if(!e[Gr]){e[Gr]=!0,o.forEach((function(t){"selectionchange"!==t&&(Qr.has(t)||Zr(t,!1,e),Zr(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Gr]||(t[Gr]=!0,Zr("selectionchange",!1,t))}}function ea(e,t,n,r){switch(Jt(t)){case 1:var a=qt;break;case 4:a=$t;break;default:a=Ht}n=a.bind(null,t,n,e),a=void 0,!Le||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function ta(e,t,n,r,a){var l=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var o=r.tag;if(3===o||4===o){var i=r.stateNode.containerInfo;if(i===a||8===i.nodeType&&i.parentNode===a)break;if(4===o)for(o=r.return;null!==o;){var s=o.tag;if((3===s||4===s)&&((s=o.stateNode.containerInfo)===a||8===s.nodeType&&s.parentNode===a))return;o=o.return}for(;null!==i;){if(null===(o=Pa(i)))return;if(5===(s=o.tag)||6===s){r=l=o;continue e}i=i.parentNode}}r=r.return}_e((function(){var r=l,a=be(n),o=[];e:{var i=Br.get(e);if(void 0!==i){var s=hn,c=e;switch(e){case"keypress":if(0===en(n))break e;case"keydown":case"keyup":s=Dn;break;case"focusin":c="focus",s=Sn;break;case"focusout":c="blur",s=Sn;break;case"beforeblur":case"afterblur":s=Sn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":s=Nn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":s=kn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":s=Un;break;case Dr:case Fr:case zr:s=En;break;case Ur:s=Bn;break;case"scroll":s=wn;break;case"wheel":s=Vn;break;case"copy":case"cut":case"paste":s=On;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":s=zn}var u=0!==(4&t),d=!u&&"scroll"===e,f=u?null!==i?i+"Capture":null:i;u=[];for(var m,h=r;null!==h;){var p=(m=h).stateNode;if(5===m.tag&&null!==p&&(m=p,null!==f&&(null!=(p=Re(h,f))&&u.push(na(h,p,m)))),d)break;h=h.return}0<u.length&&(i=new s(i,c,null,n,a),o.push({event:i,listeners:u}))}}if(0===(7&t)){if(s="mouseout"===e||"pointerout"===e,(!(i="mouseover"===e||"pointerover"===e)||n===ve||!(c=n.relatedTarget||n.fromElement)||!Pa(c)&&!c[Sa])&&(s||i)&&(i=a.window===a?a:(i=a.ownerDocument)?i.defaultView||i.parentWindow:window,s?(s=r,null!==(c=(c=n.relatedTarget||n.toElement)?Pa(c):null)&&(c!==(d=Be(c))||5!==c.tag&&6!==c.tag)&&(c=null)):(s=null,c=r),s!==c)){if(u=Nn,p="onMouseLeave",f="onMouseEnter",h="mouse","pointerout"!==e&&"pointerover"!==e||(u=zn,p="onPointerLeave",f="onPointerEnter",h="pointer"),d=null==s?i:Ra(s),m=null==c?i:Ra(c),(i=new u(p,h+"leave",s,n,a)).target=d,i.relatedTarget=m,p=null,Pa(a)===r&&((u=new u(f,h+"enter",c,n,a)).target=m,u.relatedTarget=d,p=u),d=p,s&&c)e:{for(f=c,h=0,m=u=s;m;m=aa(m))h++;for(m=0,p=f;p;p=aa(p))m++;for(;0<h-m;)u=aa(u),h--;for(;0<m-h;)f=aa(f),m--;for(;h--;){if(u===f||null!==f&&u===f.alternate)break e;u=aa(u),f=aa(f)}u=null}else u=null;null!==s&&la(o,i,s,u,!1),null!==c&&null!==d&&la(o,d,c,u,!0)}if("select"===(s=(i=r?Ra(r):window).nodeName&&i.nodeName.toLowerCase())||"input"===s&&"file"===i.type)var g=ir;else if(tr(i))if(sr)g=xr;else{g=pr;var x=hr}else(s=i.nodeName)&&"input"===s.toLowerCase()&&("checkbox"===i.type||"radio"===i.type)&&(g=gr);switch(g&&(g=g(e,r))?nr(o,g,n,a):(x&&x(e,i,r),"focusout"===e&&(x=i._wrapperState)&&x.controlled&&"number"===i.type&&ee(i,"number",i.value)),x=r?Ra(r):window,e){case"focusin":(tr(x)||"true"===x.contentEditable)&&(Cr=x,Or=r,Pr=null);break;case"focusout":Pr=Or=Cr=null;break;case"mousedown":_r=!0;break;case"contextmenu":case"mouseup":case"dragend":_r=!1,Rr(o,n,a);break;case"selectionchange":if(Er)break;case"keydown":case"keyup":Rr(o,n,a)}var y;if($n)e:{switch(e){case"compositionstart":var v="onCompositionStart";break e;case"compositionend":v="onCompositionEnd";break e;case"compositionupdate":v="onCompositionUpdate";break e}v=void 0}else Yn?Zn(e,n)&&(v="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(v="onCompositionStart");v&&(Kn&&"ko"!==n.locale&&(Yn||"onCompositionStart"!==v?"onCompositionEnd"===v&&Yn&&(y=Yt()):(Zt="value"in(Xt=a)?Xt.value:Xt.textContent,Yn=!0)),0<(x=ra(r,v)).length&&(v=new Pn(v,e,null,n,a),o.push({event:v,listeners:x}),y?v.data=y:null!==(y=Gn(n))&&(v.data=y))),(y=Qn?function(e,t){switch(e){case"compositionend":return Gn(t);case"keypress":return 32!==t.which?null:(Xn=!0,Jn);case"textInput":return(e=t.data)===Jn&&Xn?null:e;default:return null}}(e,n):function(e,t){if(Yn)return"compositionend"===e||!$n&&Zn(e,t)?(e=Yt(),Gt=Zt=Xt=null,Yn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Kn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=ra(r,"onBeforeInput")).length&&(a=new Pn("onBeforeInput","beforeinput",null,n,a),o.push({event:a,listeners:r}),a.data=y))}Jr(o,t)}))}function na(e,t,n){return{instance:e,listener:t,currentTarget:n}}function ra(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,l=a.stateNode;5===a.tag&&null!==l&&(a=l,null!=(l=Re(e,n))&&r.unshift(na(e,l,a)),null!=(l=Re(e,t))&&r.push(na(e,l,a))),e=e.return}return r}function aa(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function la(e,t,n,r,a){for(var l=t._reactName,o=[];null!==n&&n!==r;){var i=n,s=i.alternate,c=i.stateNode;if(null!==s&&s===r)break;5===i.tag&&null!==c&&(i=c,a?null!=(s=Re(n,l))&&o.unshift(na(n,s,i)):a||null!=(s=Re(n,l))&&o.push(na(n,s,i))),n=n.return}0!==o.length&&e.push({event:t,listeners:o})}var oa=/\r\n?/g,ia=/\u0000|\uFFFD/g;function sa(e){return("string"===typeof e?e:""+e).replace(oa,"\n").replace(ia,"")}function ca(e,t,n){if(t=sa(t),sa(e)!==t&&n)throw Error(l(425))}function ua(){}var da=null,fa=null;function ma(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ha="function"===typeof setTimeout?setTimeout:void 0,pa="function"===typeof clearTimeout?clearTimeout:void 0,ga="function"===typeof Promise?Promise:void 0,xa="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof ga?function(e){return ga.resolve(null).then(e).catch(ya)}:ha;function ya(e){setTimeout((function(){throw e}))}function va(e,t){var n=t,r=0;do{var a=n.nextSibling;if(e.removeChild(n),a&&8===a.nodeType)if("/$"===(n=a.data)){if(0===r)return e.removeChild(a),void Bt(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=a}while(n);Bt(t)}function ba(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function wa(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var ja=Math.random().toString(36).slice(2),Na="__reactFiber$"+ja,ka="__reactProps$"+ja,Sa="__reactContainer$"+ja,Ea="__reactEvents$"+ja,Ca="__reactListeners$"+ja,Oa="__reactHandles$"+ja;function Pa(e){var t=e[Na];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Sa]||n[Na]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=wa(e);null!==e;){if(n=e[Na])return n;e=wa(e)}return t}n=(e=n).parentNode}return null}function _a(e){return!(e=e[Na]||e[Sa])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function Ra(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(l(33))}function La(e){return e[ka]||null}var Ta=[],Aa=-1;function Ma(e){return{current:e}}function Ia(e){0>Aa||(e.current=Ta[Aa],Ta[Aa]=null,Aa--)}function Da(e,t){Aa++,Ta[Aa]=e.current,e.current=t}var Fa={},za=Ma(Fa),Ua=Ma(!1),Ba=Fa;function Wa(e,t){var n=e.type.contextTypes;if(!n)return Fa;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var a,l={};for(a in n)l[a]=t[a];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function Va(e){return null!==(e=e.childContextTypes)&&void 0!==e}function qa(){Ia(Ua),Ia(za)}function $a(e,t,n){if(za.current!==Fa)throw Error(l(168));Da(za,t),Da(Ua,n)}function Ha(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var a in r=r.getChildContext())if(!(a in t))throw Error(l(108,V(e)||"Unknown",a));return D({},n,r)}function Qa(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Fa,Ba=za.current,Da(za,e),Da(Ua,Ua.current),!0}function Ka(e,t,n){var r=e.stateNode;if(!r)throw Error(l(169));n?(e=Ha(e,t,Ba),r.__reactInternalMemoizedMergedChildContext=e,Ia(Ua),Ia(za),Da(za,e)):Ia(Ua),Da(Ua,n)}var Ja=null,Xa=!1,Za=!1;function Ga(e){null===Ja?Ja=[e]:Ja.push(e)}function Ya(){if(!Za&&null!==Ja){Za=!0;var e=0,t=yt;try{var n=Ja;for(yt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}Ja=null,Xa=!1}catch(a){throw null!==Ja&&(Ja=Ja.slice(e+1)),He(Ge,Ya),a}finally{yt=t,Za=!1}}return null}var el=[],tl=0,nl=null,rl=0,al=[],ll=0,ol=null,il=1,sl="";function cl(e,t){el[tl++]=rl,el[tl++]=nl,nl=e,rl=t}function ul(e,t,n){al[ll++]=il,al[ll++]=sl,al[ll++]=ol,ol=e;var r=il;e=sl;var a=32-lt(r)-1;r&=~(1<<a),n+=1;var l=32-lt(t)+a;if(30<l){var o=a-a%5;l=(r&(1<<o)-1).toString(32),r>>=o,a-=o,il=1<<32-lt(t)+a|n<<a|r,sl=l+e}else il=1<<l|n<<a|r,sl=e}function dl(e){null!==e.return&&(cl(e,1),ul(e,1,0))}function fl(e){for(;e===nl;)nl=el[--tl],el[tl]=null,rl=el[--tl],el[tl]=null;for(;e===ol;)ol=al[--ll],al[ll]=null,sl=al[--ll],al[ll]=null,il=al[--ll],al[ll]=null}var ml=null,hl=null,pl=!1,gl=null;function xl(e,t){var n=Wc(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function yl(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,ml=e,hl=ba(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,ml=e,hl=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==ol?{id:il,overflow:sl}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Wc(18,null,null,0)).stateNode=t,n.return=e,e.child=n,ml=e,hl=null,!0);default:return!1}}function vl(e){return 0!==(1&e.mode)&&0===(128&e.flags)}function bl(e){if(pl){var t=hl;if(t){var n=t;if(!yl(e,t)){if(vl(e))throw Error(l(418));t=ba(n.nextSibling);var r=ml;t&&yl(e,t)?xl(r,n):(e.flags=-4097&e.flags|2,pl=!1,ml=e)}}else{if(vl(e))throw Error(l(418));e.flags=-4097&e.flags|2,pl=!1,ml=e}}}function wl(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;ml=e}function jl(e){if(e!==ml)return!1;if(!pl)return wl(e),pl=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!ma(e.type,e.memoizedProps)),t&&(t=hl)){if(vl(e))throw Nl(),Error(l(418));for(;t;)xl(e,t),t=ba(t.nextSibling)}if(wl(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(l(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){hl=ba(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}hl=null}}else hl=ml?ba(e.stateNode.nextSibling):null;return!0}function Nl(){for(var e=hl;e;)e=ba(e.nextSibling)}function kl(){hl=ml=null,pl=!1}function Sl(e){null===gl?gl=[e]:gl.push(e)}var El=b.ReactCurrentBatchConfig;function Cl(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(l(309));var r=n.stateNode}if(!r)throw Error(l(147,e));var a=r,o=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===o?t.ref:(t=function(e){var t=a.refs;null===e?delete t[o]:t[o]=e},t._stringRef=o,t)}if("string"!==typeof e)throw Error(l(284));if(!n._owner)throw Error(l(290,e))}return e}function Ol(e,t){throw e=Object.prototype.toString.call(t),Error(l(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Pl(e){return(0,e._init)(e._payload)}function _l(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function a(e,t){return(e=qc(e,t)).index=0,e.sibling=null,e}function o(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function i(t){return e&&null===t.alternate&&(t.flags|=2),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=Kc(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function c(e,t,n,r){var l=n.type;return l===N?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===l||"object"===typeof l&&null!==l&&l.$$typeof===L&&Pl(l)===t.type)?((r=a(t,n.props)).ref=Cl(e,t,n),r.return=e,r):((r=$c(n.type,n.key,n.props,null,e.mode,r)).ref=Cl(e,t,n),r.return=e,r)}function u(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Jc(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function d(e,t,n,r,l){return null===t||7!==t.tag?((t=Hc(n,e.mode,r,l)).return=e,t):((t=a(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t)return(t=Kc(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case w:return(n=$c(t.type,t.key,t.props,null,e.mode,n)).ref=Cl(e,null,t),n.return=e,n;case j:return(t=Jc(t,e.mode,n)).return=e,t;case L:return f(e,(0,t._init)(t._payload),n)}if(te(t)||M(t))return(t=Hc(t,e.mode,n,null)).return=e,t;Ol(e,t)}return null}function m(e,t,n,r){var a=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n)return null!==a?null:s(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case w:return n.key===a?c(e,t,n,r):null;case j:return n.key===a?u(e,t,n,r):null;case L:return m(e,t,(a=n._init)(n._payload),r)}if(te(n)||M(n))return null!==a?null:d(e,t,n,r,null);Ol(e,n)}return null}function h(e,t,n,r,a){if("string"===typeof r&&""!==r||"number"===typeof r)return s(t,e=e.get(n)||null,""+r,a);if("object"===typeof r&&null!==r){switch(r.$$typeof){case w:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case j:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case L:return h(e,t,n,(0,r._init)(r._payload),a)}if(te(r)||M(r))return d(t,e=e.get(n)||null,r,a,null);Ol(t,r)}return null}function p(a,l,i,s){for(var c=null,u=null,d=l,p=l=0,g=null;null!==d&&p<i.length;p++){d.index>p?(g=d,d=null):g=d.sibling;var x=m(a,d,i[p],s);if(null===x){null===d&&(d=g);break}e&&d&&null===x.alternate&&t(a,d),l=o(x,l,p),null===u?c=x:u.sibling=x,u=x,d=g}if(p===i.length)return n(a,d),pl&&cl(a,p),c;if(null===d){for(;p<i.length;p++)null!==(d=f(a,i[p],s))&&(l=o(d,l,p),null===u?c=d:u.sibling=d,u=d);return pl&&cl(a,p),c}for(d=r(a,d);p<i.length;p++)null!==(g=h(d,a,p,i[p],s))&&(e&&null!==g.alternate&&d.delete(null===g.key?p:g.key),l=o(g,l,p),null===u?c=g:u.sibling=g,u=g);return e&&d.forEach((function(e){return t(a,e)})),pl&&cl(a,p),c}function g(a,i,s,c){var u=M(s);if("function"!==typeof u)throw Error(l(150));if(null==(s=u.call(s)))throw Error(l(151));for(var d=u=null,p=i,g=i=0,x=null,y=s.next();null!==p&&!y.done;g++,y=s.next()){p.index>g?(x=p,p=null):x=p.sibling;var v=m(a,p,y.value,c);if(null===v){null===p&&(p=x);break}e&&p&&null===v.alternate&&t(a,p),i=o(v,i,g),null===d?u=v:d.sibling=v,d=v,p=x}if(y.done)return n(a,p),pl&&cl(a,g),u;if(null===p){for(;!y.done;g++,y=s.next())null!==(y=f(a,y.value,c))&&(i=o(y,i,g),null===d?u=y:d.sibling=y,d=y);return pl&&cl(a,g),u}for(p=r(a,p);!y.done;g++,y=s.next())null!==(y=h(p,a,g,y.value,c))&&(e&&null!==y.alternate&&p.delete(null===y.key?g:y.key),i=o(y,i,g),null===d?u=y:d.sibling=y,d=y);return e&&p.forEach((function(e){return t(a,e)})),pl&&cl(a,g),u}return function e(r,l,o,s){if("object"===typeof o&&null!==o&&o.type===N&&null===o.key&&(o=o.props.children),"object"===typeof o&&null!==o){switch(o.$$typeof){case w:e:{for(var c=o.key,u=l;null!==u;){if(u.key===c){if((c=o.type)===N){if(7===u.tag){n(r,u.sibling),(l=a(u,o.props.children)).return=r,r=l;break e}}else if(u.elementType===c||"object"===typeof c&&null!==c&&c.$$typeof===L&&Pl(c)===u.type){n(r,u.sibling),(l=a(u,o.props)).ref=Cl(r,u,o),l.return=r,r=l;break e}n(r,u);break}t(r,u),u=u.sibling}o.type===N?((l=Hc(o.props.children,r.mode,s,o.key)).return=r,r=l):((s=$c(o.type,o.key,o.props,null,r.mode,s)).ref=Cl(r,l,o),s.return=r,r=s)}return i(r);case j:e:{for(u=o.key;null!==l;){if(l.key===u){if(4===l.tag&&l.stateNode.containerInfo===o.containerInfo&&l.stateNode.implementation===o.implementation){n(r,l.sibling),(l=a(l,o.children||[])).return=r,r=l;break e}n(r,l);break}t(r,l),l=l.sibling}(l=Jc(o,r.mode,s)).return=r,r=l}return i(r);case L:return e(r,l,(u=o._init)(o._payload),s)}if(te(o))return p(r,l,o,s);if(M(o))return g(r,l,o,s);Ol(r,o)}return"string"===typeof o&&""!==o||"number"===typeof o?(o=""+o,null!==l&&6===l.tag?(n(r,l.sibling),(l=a(l,o)).return=r,r=l):(n(r,l),(l=Kc(o,r.mode,s)).return=r,r=l),i(r)):n(r,l)}}var Rl=_l(!0),Ll=_l(!1),Tl=Ma(null),Al=null,Ml=null,Il=null;function Dl(){Il=Ml=Al=null}function Fl(e){var t=Tl.current;Ia(Tl),e._currentValue=t}function zl(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Ul(e,t){Al=e,Il=Ml=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(_i=!0),e.firstContext=null)}function Bl(e){var t=e._currentValue;if(Il!==e)if(e={context:e,memoizedValue:t,next:null},null===Ml){if(null===Al)throw Error(l(308));Ml=e,Al.dependencies={lanes:0,firstContext:e}}else Ml=Ml.next=e;return t}var Wl=null;function Vl(e){null===Wl?Wl=[e]:Wl.push(e)}function ql(e,t,n,r){var a=t.interleaved;return null===a?(n.next=n,Vl(t)):(n.next=a.next,a.next=n),t.interleaved=n,$l(e,r)}function $l(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var Hl=!1;function Ql(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Kl(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Jl(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Xl(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&zs)){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,$l(e,n)}return null===(a=r.interleaved)?(t.next=t,Vl(r)):(t.next=a.next,a.next=t),r.interleaved=t,$l(e,n)}function Zl(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194240&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,xt(e,n)}}function Gl(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,l=null;if(null!==(n=n.firstBaseUpdate)){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===l?a=l=o:l=l.next=o,n=n.next}while(null!==n);null===l?a=l=t:l=l.next=t}else a=l=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:l,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Yl(e,t,n,r){var a=e.updateQueue;Hl=!1;var l=a.firstBaseUpdate,o=a.lastBaseUpdate,i=a.shared.pending;if(null!==i){a.shared.pending=null;var s=i,c=s.next;s.next=null,null===o?l=c:o.next=c,o=s;var u=e.alternate;null!==u&&((i=(u=u.updateQueue).lastBaseUpdate)!==o&&(null===i?u.firstBaseUpdate=c:i.next=c,u.lastBaseUpdate=s))}if(null!==l){var d=a.baseState;for(o=0,u=c=s=null,i=l;;){var f=i.lane,m=i.eventTime;if((r&f)===f){null!==u&&(u=u.next={eventTime:m,lane:0,tag:i.tag,payload:i.payload,callback:i.callback,next:null});e:{var h=e,p=i;switch(f=t,m=n,p.tag){case 1:if("function"===typeof(h=p.payload)){d=h.call(m,d,f);break e}d=h;break e;case 3:h.flags=-65537&h.flags|128;case 0:if(null===(f="function"===typeof(h=p.payload)?h.call(m,d,f):h)||void 0===f)break e;d=D({},d,f);break e;case 2:Hl=!0}}null!==i.callback&&0!==i.lane&&(e.flags|=64,null===(f=a.effects)?a.effects=[i]:f.push(i))}else m={eventTime:m,lane:f,tag:i.tag,payload:i.payload,callback:i.callback,next:null},null===u?(c=u=m,s=d):u=u.next=m,o|=f;if(null===(i=i.next)){if(null===(i=a.shared.pending))break;i=(f=i).next,f.next=null,a.lastBaseUpdate=f,a.shared.pending=null}}if(null===u&&(s=d),a.baseState=s,a.firstBaseUpdate=c,a.lastBaseUpdate=u,null!==(t=a.shared.interleaved)){a=t;do{o|=a.lane,a=a.next}while(a!==t)}else null===l&&(a.shared.lanes=0);Qs|=o,e.lanes=o,e.memoizedState=d}}function eo(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],a=r.callback;if(null!==a){if(r.callback=null,r=n,"function"!==typeof a)throw Error(l(191,a));a.call(r)}}}var to={},no=Ma(to),ro=Ma(to),ao=Ma(to);function lo(e){if(e===to)throw Error(l(174));return e}function oo(e,t){switch(Da(ao,t),Da(ro,e),Da(no,to),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:se(null,"");break;default:t=se(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}Ia(no),Da(no,t)}function io(){Ia(no),Ia(ro),Ia(ao)}function so(e){lo(ao.current);var t=lo(no.current),n=se(t,e.type);t!==n&&(Da(ro,e),Da(no,n))}function co(e){ro.current===e&&(Ia(no),Ia(ro))}var uo=Ma(0);function fo(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var mo=[];function ho(){for(var e=0;e<mo.length;e++)mo[e]._workInProgressVersionPrimary=null;mo.length=0}var po=b.ReactCurrentDispatcher,go=b.ReactCurrentBatchConfig,xo=0,yo=null,vo=null,bo=null,wo=!1,jo=!1,No=0,ko=0;function So(){throw Error(l(321))}function Eo(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!yr(e[n],t[n]))return!1;return!0}function Co(e,t,n,r,a,o){if(xo=o,yo=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,po.current=null===e||null===e.memoizedState?ui:di,e=n(r,a),jo){o=0;do{if(jo=!1,No=0,25<=o)throw Error(l(301));o+=1,bo=vo=null,t.updateQueue=null,po.current=fi,e=n(r,a)}while(jo)}if(po.current=ci,t=null!==vo&&null!==vo.next,xo=0,bo=vo=yo=null,wo=!1,t)throw Error(l(300));return e}function Oo(){var e=0!==No;return No=0,e}function Po(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===bo?yo.memoizedState=bo=e:bo=bo.next=e,bo}function _o(){if(null===vo){var e=yo.alternate;e=null!==e?e.memoizedState:null}else e=vo.next;var t=null===bo?yo.memoizedState:bo.next;if(null!==t)bo=t,vo=e;else{if(null===e)throw Error(l(310));e={memoizedState:(vo=e).memoizedState,baseState:vo.baseState,baseQueue:vo.baseQueue,queue:vo.queue,next:null},null===bo?yo.memoizedState=bo=e:bo=bo.next=e}return bo}function Ro(e,t){return"function"===typeof t?t(e):t}function Lo(e){var t=_o(),n=t.queue;if(null===n)throw Error(l(311));n.lastRenderedReducer=e;var r=vo,a=r.baseQueue,o=n.pending;if(null!==o){if(null!==a){var i=a.next;a.next=o.next,o.next=i}r.baseQueue=a=o,n.pending=null}if(null!==a){o=a.next,r=r.baseState;var s=i=null,c=null,u=o;do{var d=u.lane;if((xo&d)===d)null!==c&&(c=c.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var f={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};null===c?(s=c=f,i=r):c=c.next=f,yo.lanes|=d,Qs|=d}u=u.next}while(null!==u&&u!==o);null===c?i=r:c.next=s,yr(r,t.memoizedState)||(_i=!0),t.memoizedState=r,t.baseState=i,t.baseQueue=c,n.lastRenderedState=r}if(null!==(e=n.interleaved)){a=e;do{o=a.lane,yo.lanes|=o,Qs|=o,a=a.next}while(a!==e)}else null===a&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function To(e){var t=_o(),n=t.queue;if(null===n)throw Error(l(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,o=t.memoizedState;if(null!==a){n.pending=null;var i=a=a.next;do{o=e(o,i.action),i=i.next}while(i!==a);yr(o,t.memoizedState)||(_i=!0),t.memoizedState=o,null===t.baseQueue&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function Ao(){}function Mo(e,t){var n=yo,r=_o(),a=t(),o=!yr(r.memoizedState,a);if(o&&(r.memoizedState=a,_i=!0),r=r.queue,Qo(Fo.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||null!==bo&&1&bo.memoizedState.tag){if(n.flags|=2048,Wo(9,Do.bind(null,n,r,a,t),void 0,null),null===Us)throw Error(l(349));0!==(30&xo)||Io(n,t,a)}return a}function Io(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=yo.updateQueue)?(t={lastEffect:null,stores:null},yo.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Do(e,t,n,r){t.value=n,t.getSnapshot=r,zo(t)&&Uo(e)}function Fo(e,t,n){return n((function(){zo(t)&&Uo(e)}))}function zo(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!yr(e,n)}catch(gn){return!0}}function Uo(e){var t=$l(e,1);null!==t&&mc(t,e,1,-1)}function Bo(e){var t=Po();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Ro,lastRenderedState:e},t.queue=e,e=e.dispatch=li.bind(null,yo,e),[t.memoizedState,e]}function Wo(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=yo.updateQueue)?(t={lastEffect:null,stores:null},yo.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function Vo(){return _o().memoizedState}function qo(e,t,n,r){var a=Po();yo.flags|=e,a.memoizedState=Wo(1|t,n,void 0,void 0===r?null:r)}function $o(e,t,n,r){var a=_o();r=void 0===r?null:r;var l=void 0;if(null!==vo){var o=vo.memoizedState;if(l=o.destroy,null!==r&&Eo(r,o.deps))return void(a.memoizedState=Wo(t,n,l,r))}yo.flags|=e,a.memoizedState=Wo(1|t,n,l,r)}function Ho(e,t){return qo(8390656,8,e,t)}function Qo(e,t){return $o(2048,8,e,t)}function Ko(e,t){return $o(4,2,e,t)}function Jo(e,t){return $o(4,4,e,t)}function Xo(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Zo(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,$o(4,4,Xo.bind(null,t,e),n)}function Go(){}function Yo(e,t){var n=_o();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&Eo(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function ei(e,t){var n=_o();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&Eo(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function ti(e,t,n){return 0===(21&xo)?(e.baseState&&(e.baseState=!1,_i=!0),e.memoizedState=n):(yr(n,t)||(n=ht(),yo.lanes|=n,Qs|=n,e.baseState=!0),t)}function ni(e,t){var n=yt;yt=0!==n&&4>n?n:4,e(!0);var r=go.transition;go.transition={};try{e(!1),t()}finally{yt=n,go.transition=r}}function ri(){return _o().memoizedState}function ai(e,t,n){var r=fc(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},oi(e))ii(t,n);else if(null!==(n=ql(e,t,n,r))){mc(n,e,r,dc()),si(n,t,r)}}function li(e,t,n){var r=fc(e),a={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(oi(e))ii(t,a);else{var l=e.alternate;if(0===e.lanes&&(null===l||0===l.lanes)&&null!==(l=t.lastRenderedReducer))try{var o=t.lastRenderedState,i=l(o,n);if(a.hasEagerState=!0,a.eagerState=i,yr(i,o)){var s=t.interleaved;return null===s?(a.next=a,Vl(t)):(a.next=s.next,s.next=a),void(t.interleaved=a)}}catch(pn){}null!==(n=ql(e,t,a,r))&&(mc(n,e,r,a=dc()),si(n,t,r))}}function oi(e){var t=e.alternate;return e===yo||null!==t&&t===yo}function ii(e,t){jo=wo=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function si(e,t,n){if(0!==(4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,xt(e,n)}}var ci={readContext:Bl,useCallback:So,useContext:So,useEffect:So,useImperativeHandle:So,useInsertionEffect:So,useLayoutEffect:So,useMemo:So,useReducer:So,useRef:So,useState:So,useDebugValue:So,useDeferredValue:So,useTransition:So,useMutableSource:So,useSyncExternalStore:So,useId:So,unstable_isNewReconciler:!1},ui={readContext:Bl,useCallback:function(e,t){return Po().memoizedState=[e,void 0===t?null:t],e},useContext:Bl,useEffect:Ho,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,qo(4194308,4,Xo.bind(null,t,e),n)},useLayoutEffect:function(e,t){return qo(4194308,4,e,t)},useInsertionEffect:function(e,t){return qo(4,2,e,t)},useMemo:function(e,t){var n=Po();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Po();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=ai.bind(null,yo,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},Po().memoizedState=e},useState:Bo,useDebugValue:Go,useDeferredValue:function(e){return Po().memoizedState=e},useTransition:function(){var e=Bo(!1),t=e[0];return e=ni.bind(null,e[1]),Po().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=yo,a=Po();if(pl){if(void 0===n)throw Error(l(407));n=n()}else{if(n=t(),null===Us)throw Error(l(349));0!==(30&xo)||Io(r,t,n)}a.memoizedState=n;var o={value:n,getSnapshot:t};return a.queue=o,Ho(Fo.bind(null,r,o,e),[e]),r.flags|=2048,Wo(9,Do.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=Po(),t=Us.identifierPrefix;if(pl){var n=sl;t=":"+t+"R"+(n=(il&~(1<<32-lt(il)-1)).toString(32)+n),0<(n=No++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=ko++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},di={readContext:Bl,useCallback:Yo,useContext:Bl,useEffect:Qo,useImperativeHandle:Zo,useInsertionEffect:Ko,useLayoutEffect:Jo,useMemo:ei,useReducer:Lo,useRef:Vo,useState:function(){return Lo(Ro)},useDebugValue:Go,useDeferredValue:function(e){return ti(_o(),vo.memoizedState,e)},useTransition:function(){return[Lo(Ro)[0],_o().memoizedState]},useMutableSource:Ao,useSyncExternalStore:Mo,useId:ri,unstable_isNewReconciler:!1},fi={readContext:Bl,useCallback:Yo,useContext:Bl,useEffect:Qo,useImperativeHandle:Zo,useInsertionEffect:Ko,useLayoutEffect:Jo,useMemo:ei,useReducer:To,useRef:Vo,useState:function(){return To(Ro)},useDebugValue:Go,useDeferredValue:function(e){var t=_o();return null===vo?t.memoizedState=e:ti(t,vo.memoizedState,e)},useTransition:function(){return[To(Ro)[0],_o().memoizedState]},useMutableSource:Ao,useSyncExternalStore:Mo,useId:ri,unstable_isNewReconciler:!1};function mi(e,t){if(e&&e.defaultProps){for(var n in t=D({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function hi(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:D({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var pi={isMounted:function(e){return!!(e=e._reactInternals)&&Be(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=dc(),a=fc(e),l=Jl(r,a);l.payload=t,void 0!==n&&null!==n&&(l.callback=n),null!==(t=Xl(e,l,a))&&(mc(t,e,a,r),Zl(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=dc(),a=fc(e),l=Jl(r,a);l.tag=1,l.payload=t,void 0!==n&&null!==n&&(l.callback=n),null!==(t=Xl(e,l,a))&&(mc(t,e,a,r),Zl(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=dc(),r=fc(e),a=Jl(n,r);a.tag=2,void 0!==t&&null!==t&&(a.callback=t),null!==(t=Xl(e,a,r))&&(mc(t,e,r,n),Zl(t,e,r))}};function gi(e,t,n,r,a,l,o){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,l,o):!t.prototype||!t.prototype.isPureReactComponent||(!vr(n,r)||!vr(a,l))}function xi(e,t,n){var r=!1,a=Fa,l=t.contextType;return"object"===typeof l&&null!==l?l=Bl(l):(a=Va(t)?Ba:za.current,l=(r=null!==(r=t.contextTypes)&&void 0!==r)?Wa(e,a):Fa),t=new t(n,l),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=pi,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=a,e.__reactInternalMemoizedMaskedChildContext=l),t}function yi(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&pi.enqueueReplaceState(t,t.state,null)}function vi(e,t,n,r){var a=e.stateNode;a.props=n,a.state=e.memoizedState,a.refs={},Ql(e);var l=t.contextType;"object"===typeof l&&null!==l?a.context=Bl(l):(l=Va(t)?Ba:za.current,a.context=Wa(e,l)),a.state=e.memoizedState,"function"===typeof(l=t.getDerivedStateFromProps)&&(hi(e,t,l,n),a.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof a.getSnapshotBeforeUpdate||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||(t=a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),t!==a.state&&pi.enqueueReplaceState(a,a.state,null),Yl(e,n,a,r),a.state=e.memoizedState),"function"===typeof a.componentDidMount&&(e.flags|=4194308)}function bi(e,t){try{var n="",r=t;do{n+=B(r),r=r.return}while(r);var a=n}catch(un){a="\nError generating stack: "+un.message+"\n"+un.stack}return{value:e,source:t,stack:a,digest:null}}function wi(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function ji(e,t){try{console.error(t.value)}catch(dn){setTimeout((function(){throw dn}))}}var Ni="function"===typeof WeakMap?WeakMap:Map;function ki(e,t,n){(n=Jl(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){tc||(tc=!0,nc=r),ji(0,t)},n}function Si(e,t,n){(n=Jl(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"===typeof r){var a=t.value;n.payload=function(){return r(a)},n.callback=function(){ji(0,t)}}var l=e.stateNode;return null!==l&&"function"===typeof l.componentDidCatch&&(n.callback=function(){ji(0,t),"function"!==typeof r&&(null===rc?rc=new Set([this]):rc.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function Ei(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new Ni;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(a.add(n),e=Ic.bind(null,e,t,n),t.then(e,e))}function Ci(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function Oi(e,t,n,r,a){return 0===(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Jl(-1,1)).tag=2,Xl(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=a,e)}var Pi=b.ReactCurrentOwner,_i=!1;function Ri(e,t,n,r){t.child=null===e?Ll(t,null,n,r):Rl(t,e.child,n,r)}function Li(e,t,n,r,a){n=n.render;var l=t.ref;return Ul(t,a),r=Co(e,t,n,r,l,a),n=Oo(),null===e||_i?(pl&&n&&dl(t),t.flags|=1,Ri(e,t,r,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,ts(e,t,a))}function Ti(e,t,n,r,a){if(null===e){var l=n.type;return"function"!==typeof l||Vc(l)||void 0!==l.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=$c(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=l,Ai(e,t,l,r,a))}if(l=e.child,0===(e.lanes&a)){var o=l.memoizedProps;if((n=null!==(n=n.compare)?n:vr)(o,r)&&e.ref===t.ref)return ts(e,t,a)}return t.flags|=1,(e=qc(l,r)).ref=t.ref,e.return=t,t.child=e}function Ai(e,t,n,r,a){if(null!==e){var l=e.memoizedProps;if(vr(l,r)&&e.ref===t.ref){if(_i=!1,t.pendingProps=r=l,0===(e.lanes&a))return t.lanes=e.lanes,ts(e,t,a);0!==(131072&e.flags)&&(_i=!0)}}return Di(e,t,n,r,a)}function Mi(e,t,n){var r=t.pendingProps,a=r.children,l=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(0===(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Da(qs,Vs),Vs|=n;else{if(0===(1073741824&n))return e=null!==l?l.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Da(qs,Vs),Vs|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==l?l.baseLanes:n,Da(qs,Vs),Vs|=r}else null!==l?(r=l.baseLanes|n,t.memoizedState=null):r=n,Da(qs,Vs),Vs|=r;return Ri(e,t,a,n),t.child}function Ii(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Di(e,t,n,r,a){var l=Va(n)?Ba:za.current;return l=Wa(t,l),Ul(t,a),n=Co(e,t,n,r,l,a),r=Oo(),null===e||_i?(pl&&r&&dl(t),t.flags|=1,Ri(e,t,n,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,ts(e,t,a))}function Fi(e,t,n,r,a){if(Va(n)){var l=!0;Qa(t)}else l=!1;if(Ul(t,a),null===t.stateNode)es(e,t),xi(t,n,r),vi(t,n,r,a),r=!0;else if(null===e){var o=t.stateNode,i=t.memoizedProps;o.props=i;var s=o.context,c=n.contextType;"object"===typeof c&&null!==c?c=Bl(c):c=Wa(t,c=Va(n)?Ba:za.current);var u=n.getDerivedStateFromProps,d="function"===typeof u||"function"===typeof o.getSnapshotBeforeUpdate;d||"function"!==typeof o.UNSAFE_componentWillReceiveProps&&"function"!==typeof o.componentWillReceiveProps||(i!==r||s!==c)&&yi(t,o,r,c),Hl=!1;var f=t.memoizedState;o.state=f,Yl(t,r,o,a),s=t.memoizedState,i!==r||f!==s||Ua.current||Hl?("function"===typeof u&&(hi(t,n,u,r),s=t.memoizedState),(i=Hl||gi(t,n,i,r,f,s,c))?(d||"function"!==typeof o.UNSAFE_componentWillMount&&"function"!==typeof o.componentWillMount||("function"===typeof o.componentWillMount&&o.componentWillMount(),"function"===typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount()),"function"===typeof o.componentDidMount&&(t.flags|=4194308)):("function"===typeof o.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=s),o.props=r,o.state=s,o.context=c,r=i):("function"===typeof o.componentDidMount&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,Kl(e,t),i=t.memoizedProps,c=t.type===t.elementType?i:mi(t.type,i),o.props=c,d=t.pendingProps,f=o.context,"object"===typeof(s=n.contextType)&&null!==s?s=Bl(s):s=Wa(t,s=Va(n)?Ba:za.current);var m=n.getDerivedStateFromProps;(u="function"===typeof m||"function"===typeof o.getSnapshotBeforeUpdate)||"function"!==typeof o.UNSAFE_componentWillReceiveProps&&"function"!==typeof o.componentWillReceiveProps||(i!==d||f!==s)&&yi(t,o,r,s),Hl=!1,f=t.memoizedState,o.state=f,Yl(t,r,o,a);var h=t.memoizedState;i!==d||f!==h||Ua.current||Hl?("function"===typeof m&&(hi(t,n,m,r),h=t.memoizedState),(c=Hl||gi(t,n,c,r,f,h,s)||!1)?(u||"function"!==typeof o.UNSAFE_componentWillUpdate&&"function"!==typeof o.componentWillUpdate||("function"===typeof o.componentWillUpdate&&o.componentWillUpdate(r,h,s),"function"===typeof o.UNSAFE_componentWillUpdate&&o.UNSAFE_componentWillUpdate(r,h,s)),"function"===typeof o.componentDidUpdate&&(t.flags|=4),"function"===typeof o.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof o.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof o.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=h),o.props=r,o.state=h,o.context=s,r=c):("function"!==typeof o.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof o.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return zi(e,t,n,r,l,a)}function zi(e,t,n,r,a,l){Ii(e,t);var o=0!==(128&t.flags);if(!r&&!o)return a&&Ka(t,n,!1),ts(e,t,l);r=t.stateNode,Pi.current=t;var i=o&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&o?(t.child=Rl(t,e.child,null,l),t.child=Rl(t,null,i,l)):Ri(e,t,i,l),t.memoizedState=r.state,a&&Ka(t,n,!0),t.child}function Ui(e){var t=e.stateNode;t.pendingContext?$a(0,t.pendingContext,t.pendingContext!==t.context):t.context&&$a(0,t.context,!1),oo(e,t.containerInfo)}function Bi(e,t,n,r,a){return kl(),Sl(a),t.flags|=256,Ri(e,t,n,r),t.child}var Wi,Vi,qi,$i,Hi={dehydrated:null,treeContext:null,retryLane:0};function Qi(e){return{baseLanes:e,cachePool:null,transitions:null}}function Ki(e,t,n){var r,a=t.pendingProps,o=uo.current,i=!1,s=0!==(128&t.flags);if((r=s)||(r=(null===e||null!==e.memoizedState)&&0!==(2&o)),r?(i=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(o|=1),Da(uo,1&o),null===e)return bl(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0===(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(s=a.children,e=a.fallback,i?(a=t.mode,i=t.child,s={mode:"hidden",children:s},0===(1&a)&&null!==i?(i.childLanes=0,i.pendingProps=s):i=Qc(s,a,0,null),e=Hc(e,a,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Qi(n),t.memoizedState=Hi,e):Ji(t,s));if(null!==(o=e.memoizedState)&&null!==(r=o.dehydrated))return function(e,t,n,r,a,o,i){if(n)return 256&t.flags?(t.flags&=-257,Xi(e,t,i,r=wi(Error(l(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(o=r.fallback,a=t.mode,r=Qc({mode:"visible",children:r.children},a,0,null),(o=Hc(o,a,i,null)).flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,0!==(1&t.mode)&&Rl(t,e.child,null,i),t.child.memoizedState=Qi(i),t.memoizedState=Hi,o);if(0===(1&t.mode))return Xi(e,t,i,null);if("$!"===a.data){if(r=a.nextSibling&&a.nextSibling.dataset)var s=r.dgst;return r=s,Xi(e,t,i,r=wi(o=Error(l(419)),r,void 0))}if(s=0!==(i&e.childLanes),_i||s){if(null!==(r=Us)){switch(i&-i){case 4:a=2;break;case 16:a=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:a=32;break;case 536870912:a=268435456;break;default:a=0}0!==(a=0!==(a&(r.suspendedLanes|i))?0:a)&&a!==o.retryLane&&(o.retryLane=a,$l(e,a),mc(r,e,a,-1))}return Ec(),Xi(e,t,i,r=wi(Error(l(421))))}return"$?"===a.data?(t.flags|=128,t.child=e.child,t=Fc.bind(null,e),a._reactRetry=t,null):(e=o.treeContext,hl=ba(a.nextSibling),ml=t,pl=!0,gl=null,null!==e&&(al[ll++]=il,al[ll++]=sl,al[ll++]=ol,il=e.id,sl=e.overflow,ol=t),t=Ji(t,r.children),t.flags|=4096,t)}(e,t,s,a,r,o,n);if(i){i=a.fallback,s=t.mode,r=(o=e.child).sibling;var c={mode:"hidden",children:a.children};return 0===(1&s)&&t.child!==o?((a=t.child).childLanes=0,a.pendingProps=c,t.deletions=null):(a=qc(o,c)).subtreeFlags=14680064&o.subtreeFlags,null!==r?i=qc(r,i):(i=Hc(i,s,n,null)).flags|=2,i.return=t,a.return=t,a.sibling=i,t.child=a,a=i,i=t.child,s=null===(s=e.child.memoizedState)?Qi(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},i.memoizedState=s,i.childLanes=e.childLanes&~n,t.memoizedState=Hi,a}return e=(i=e.child).sibling,a=qc(i,{mode:"visible",children:a.children}),0===(1&t.mode)&&(a.lanes=n),a.return=t,a.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=a,t.memoizedState=null,a}function Ji(e,t){return(t=Qc({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Xi(e,t,n,r){return null!==r&&Sl(r),Rl(t,e.child,null,n),(e=Ji(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Zi(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),zl(e.return,t,n)}function Gi(e,t,n,r,a){var l=e.memoizedState;null===l?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(l.isBackwards=t,l.rendering=null,l.renderingStartTime=0,l.last=r,l.tail=n,l.tailMode=a)}function Yi(e,t,n){var r=t.pendingProps,a=r.revealOrder,l=r.tail;if(Ri(e,t,r.children,n),0!==(2&(r=uo.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Zi(e,n,t);else if(19===e.tag)Zi(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Da(uo,r),0===(1&t.mode))t.memoizedState=null;else switch(a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===fo(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),Gi(t,!1,a,n,l);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===fo(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}Gi(t,!0,n,null,l);break;case"together":Gi(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function es(e,t){0===(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function ts(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Qs|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(l(153));if(null!==t.child){for(n=qc(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=qc(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function ns(e,t){if(!pl)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function rs(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=14680064&a.subtreeFlags,r|=14680064&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function as(e,t,n){var r=t.pendingProps;switch(fl(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return rs(t),null;case 1:case 17:return Va(t.type)&&qa(),rs(t),null;case 3:return r=t.stateNode,io(),Ia(Ua),Ia(za),ho(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(jl(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,null!==gl&&(xc(gl),gl=null))),Vi(e,t),rs(t),null;case 5:co(t);var a=lo(ao.current);if(n=t.type,null!==e&&null!=t.stateNode)qi(e,t,n,r,a),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(l(166));return rs(t),null}if(e=lo(no.current),jl(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[Na]=t,r[ka]=o,e=0!==(1&t.mode),n){case"dialog":Xr("cancel",r),Xr("close",r);break;case"iframe":case"object":case"embed":Xr("load",r);break;case"video":case"audio":for(a=0;a<Hr.length;a++)Xr(Hr[a],r);break;case"source":Xr("error",r);break;case"img":case"image":case"link":Xr("error",r),Xr("load",r);break;case"details":Xr("toggle",r);break;case"input":X(r,o),Xr("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},Xr("invalid",r);break;case"textarea":ae(r,o),Xr("invalid",r)}for(var s in xe(n,o),a=null,o)if(o.hasOwnProperty(s)){var c=o[s];"children"===s?"string"===typeof c?r.textContent!==c&&(!0!==o.suppressHydrationWarning&&ca(r.textContent,c,e),a=["children",c]):"number"===typeof c&&r.textContent!==""+c&&(!0!==o.suppressHydrationWarning&&ca(r.textContent,c,e),a=["children",""+c]):i.hasOwnProperty(s)&&null!=c&&"onScroll"===s&&Xr("scroll",r)}switch(n){case"input":H(r),Y(r,o,!0);break;case"textarea":H(r),oe(r);break;case"select":case"option":break;default:"function"===typeof o.onClick&&(r.onclick=ua)}r=a,t.updateQueue=r,null!==r&&(t.flags|=4)}else{s=9===a.nodeType?a:a.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=ie(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=s.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof r.is?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),"select"===n&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[Na]=t,e[ka]=r,Wi(e,t,!1,!1),t.stateNode=e;e:{switch(s=ye(n,r),n){case"dialog":Xr("cancel",e),Xr("close",e),a=r;break;case"iframe":case"object":case"embed":Xr("load",e),a=r;break;case"video":case"audio":for(a=0;a<Hr.length;a++)Xr(Hr[a],e);a=r;break;case"source":Xr("error",e),a=r;break;case"img":case"image":case"link":Xr("error",e),Xr("load",e),a=r;break;case"details":Xr("toggle",e),a=r;break;case"input":X(e,r),a=J(e,r),Xr("invalid",e);break;case"option":default:a=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},a=D({},r,{value:void 0}),Xr("invalid",e);break;case"textarea":ae(e,r),a=re(e,r),Xr("invalid",e)}for(o in xe(n,a),c=a)if(c.hasOwnProperty(o)){var u=c[o];"style"===o?pe(e,u):"dangerouslySetInnerHTML"===o?null!=(u=u?u.__html:void 0)&&ue(e,u):"children"===o?"string"===typeof u?("textarea"!==n||""!==u)&&de(e,u):"number"===typeof u&&de(e,""+u):"suppressContentEditableWarning"!==o&&"suppressHydrationWarning"!==o&&"autoFocus"!==o&&(i.hasOwnProperty(o)?null!=u&&"onScroll"===o&&Xr("scroll",e):null!=u&&v(e,o,u,s))}switch(n){case"input":H(e),Y(e,r,!1);break;case"textarea":H(e),oe(e);break;case"option":null!=r.value&&e.setAttribute("value",""+q(r.value));break;case"select":e.multiple=!!r.multiple,null!=(o=r.value)?ne(e,!!r.multiple,o,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof a.onClick&&(e.onclick=ua)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return rs(t),null;case 6:if(e&&null!=t.stateNode)$i(e,t,e.memoizedProps,r);else{if("string"!==typeof r&&null===t.stateNode)throw Error(l(166));if(n=lo(ao.current),lo(no.current),jl(t)){if(r=t.stateNode,n=t.memoizedProps,r[Na]=t,(o=r.nodeValue!==n)&&null!==(e=ml))switch(e.tag){case 3:ca(r.nodeValue,n,0!==(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&ca(r.nodeValue,n,0!==(1&e.mode))}o&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[Na]=t,t.stateNode=r}return rs(t),null;case 13:if(Ia(uo),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(pl&&null!==hl&&0!==(1&t.mode)&&0===(128&t.flags))Nl(),kl(),t.flags|=98560,o=!1;else if(o=jl(t),null!==r&&null!==r.dehydrated){if(null===e){if(!o)throw Error(l(318));if(!(o=null!==(o=t.memoizedState)?o.dehydrated:null))throw Error(l(317));o[Na]=t}else kl(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;rs(t),o=!1}else null!==gl&&(xc(gl),gl=null),o=!0;if(!o)return 65536&t.flags?t:null}return 0!==(128&t.flags)?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,0!==(1&t.mode)&&(null===e||0!==(1&uo.current)?0===$s&&($s=3):Ec())),null!==t.updateQueue&&(t.flags|=4),rs(t),null);case 4:return io(),Vi(e,t),null===e&&Yr(t.stateNode.containerInfo),rs(t),null;case 10:return Fl(t.type._context),rs(t),null;case 19:if(Ia(uo),null===(o=t.memoizedState))return rs(t),null;if(r=0!==(128&t.flags),null===(s=o.rendering))if(r)ns(o,!1);else{if(0!==$s||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(s=fo(e))){for(t.flags|=128,ns(o,!1),null!==(r=s.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(o=n).flags&=14680066,null===(s=o.alternate)?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=s.childLanes,o.lanes=s.lanes,o.child=s.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=s.memoizedProps,o.memoizedState=s.memoizedState,o.updateQueue=s.updateQueue,o.type=s.type,e=s.dependencies,o.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Da(uo,1&uo.current|2),t.child}e=e.sibling}null!==o.tail&&Xe()>Ys&&(t.flags|=128,r=!0,ns(o,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=fo(s))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),ns(o,!0),null===o.tail&&"hidden"===o.tailMode&&!s.alternate&&!pl)return rs(t),null}else 2*Xe()-o.renderingStartTime>Ys&&1073741824!==n&&(t.flags|=128,r=!0,ns(o,!1),t.lanes=4194304);o.isBackwards?(s.sibling=t.child,t.child=s):(null!==(n=o.last)?n.sibling=s:t.child=s,o.last=s)}return null!==o.tail?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=Xe(),t.sibling=null,n=uo.current,Da(uo,r?1&n|2:1&n),t):(rs(t),null);case 22:case 23:return jc(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&0!==(1&t.mode)?0!==(1073741824&Vs)&&(rs(t),6&t.subtreeFlags&&(t.flags|=8192)):rs(t),null;case 24:case 25:return null}throw Error(l(156,t.tag))}function ls(e,t){switch(fl(t),t.tag){case 1:return Va(t.type)&&qa(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return io(),Ia(Ua),Ia(za),ho(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 5:return co(t),null;case 13:if(Ia(uo),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(l(340));kl()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return Ia(uo),null;case 4:return io(),null;case 10:return Fl(t.type._context),null;case 22:case 23:return jc(),null;default:return null}}Wi=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Vi=function(){},qi=function(e,t,n,r){var a=e.memoizedProps;if(a!==r){e=t.stateNode,lo(no.current);var l,o=null;switch(n){case"input":a=J(e,a),r=J(e,r),o=[];break;case"select":a=D({},a,{value:void 0}),r=D({},r,{value:void 0}),o=[];break;case"textarea":a=re(e,a),r=re(e,r),o=[];break;default:"function"!==typeof a.onClick&&"function"===typeof r.onClick&&(e.onclick=ua)}for(u in xe(n,r),n=null,a)if(!r.hasOwnProperty(u)&&a.hasOwnProperty(u)&&null!=a[u])if("style"===u){var s=a[u];for(l in s)s.hasOwnProperty(l)&&(n||(n={}),n[l]="")}else"dangerouslySetInnerHTML"!==u&&"children"!==u&&"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&"autoFocus"!==u&&(i.hasOwnProperty(u)?o||(o=[]):(o=o||[]).push(u,null));for(u in r){var c=r[u];if(s=null!=a?a[u]:void 0,r.hasOwnProperty(u)&&c!==s&&(null!=c||null!=s))if("style"===u)if(s){for(l in s)!s.hasOwnProperty(l)||c&&c.hasOwnProperty(l)||(n||(n={}),n[l]="");for(l in c)c.hasOwnProperty(l)&&s[l]!==c[l]&&(n||(n={}),n[l]=c[l])}else n||(o||(o=[]),o.push(u,n)),n=c;else"dangerouslySetInnerHTML"===u?(c=c?c.__html:void 0,s=s?s.__html:void 0,null!=c&&s!==c&&(o=o||[]).push(u,c)):"children"===u?"string"!==typeof c&&"number"!==typeof c||(o=o||[]).push(u,""+c):"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&(i.hasOwnProperty(u)?(null!=c&&"onScroll"===u&&Xr("scroll",e),o||s===c||(o=[])):(o=o||[]).push(u,c))}n&&(o=o||[]).push("style",n);var u=o;(t.updateQueue=u)&&(t.flags|=4)}},$i=function(e,t,n,r){n!==r&&(t.flags|=4)};var os=!1,is=!1,ss="function"===typeof WeakSet?WeakSet:Set,cs=null;function us(e,t){var n=e.ref;if(null!==n)if("function"===typeof n)try{n(null)}catch(gn){Mc(e,t,gn)}else n.current=null}function ds(e,t,n){try{n()}catch(gn){Mc(e,t,gn)}}var fs=!1;function ms(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var a=r=r.next;do{if((a.tag&e)===e){var l=a.destroy;a.destroy=void 0,void 0!==l&&ds(t,n,l)}a=a.next}while(a!==r)}}function hs(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function ps(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"===typeof t?t(e):t.current=e}}function gs(e){var t=e.alternate;null!==t&&(e.alternate=null,gs(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[Na],delete t[ka],delete t[Ea],delete t[Ca],delete t[Oa])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function xs(e){return 5===e.tag||3===e.tag||4===e.tag}function ys(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||xs(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function vs(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=ua));else if(4!==r&&null!==(e=e.child))for(vs(e,t,n),e=e.sibling;null!==e;)vs(e,t,n),e=e.sibling}function bs(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(bs(e,t,n),e=e.sibling;null!==e;)bs(e,t,n),e=e.sibling}var ws=null,js=!1;function Ns(e,t,n){for(n=n.child;null!==n;)ks(e,t,n),n=n.sibling}function ks(e,t,n){if(at&&"function"===typeof at.onCommitFiberUnmount)try{at.onCommitFiberUnmount(rt,n)}catch(xn){}switch(n.tag){case 5:is||us(n,t);case 6:var r=ws,a=js;ws=null,Ns(e,t,n),js=a,null!==(ws=r)&&(js?(e=ws,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):ws.removeChild(n.stateNode));break;case 18:null!==ws&&(js?(e=ws,n=n.stateNode,8===e.nodeType?va(e.parentNode,n):1===e.nodeType&&va(e,n),Bt(e)):va(ws,n.stateNode));break;case 4:r=ws,a=js,ws=n.stateNode.containerInfo,js=!0,Ns(e,t,n),ws=r,js=a;break;case 0:case 11:case 14:case 15:if(!is&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){a=r=r.next;do{var l=a,o=l.destroy;l=l.tag,void 0!==o&&(0!==(2&l)||0!==(4&l))&&ds(n,t,o),a=a.next}while(a!==r)}Ns(e,t,n);break;case 1:if(!is&&(us(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(xn){Mc(n,t,xn)}Ns(e,t,n);break;case 21:Ns(e,t,n);break;case 22:1&n.mode?(is=(r=is)||null!==n.memoizedState,Ns(e,t,n),is=r):Ns(e,t,n);break;default:Ns(e,t,n)}}function Ss(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new ss),t.forEach((function(t){var r=zc.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}}function Es(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r];try{var o=e,i=t,s=i;e:for(;null!==s;){switch(s.tag){case 5:ws=s.stateNode,js=!1;break e;case 3:case 4:ws=s.stateNode.containerInfo,js=!0;break e}s=s.return}if(null===ws)throw Error(l(160));ks(o,i,a),ws=null,js=!1;var c=a.alternate;null!==c&&(c.return=null),a.return=null}catch(pn){Mc(a,t,pn)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)Cs(t,e),t=t.sibling}function Cs(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Es(t,e),Os(e),4&r){try{ms(3,e,e.return),hs(3,e)}catch(an){Mc(e,e.return,an)}try{ms(5,e,e.return)}catch(an){Mc(e,e.return,an)}}break;case 1:Es(t,e),Os(e),512&r&&null!==n&&us(n,n.return);break;case 5:if(Es(t,e),Os(e),512&r&&null!==n&&us(n,n.return),32&e.flags){var a=e.stateNode;try{de(a,"")}catch(an){Mc(e,e.return,an)}}if(4&r&&null!=(a=e.stateNode)){var o=e.memoizedProps,i=null!==n?n.memoizedProps:o,s=e.type,c=e.updateQueue;if(e.updateQueue=null,null!==c)try{"input"===s&&"radio"===o.type&&null!=o.name&&Z(a,o),ye(s,i);var u=ye(s,o);for(i=0;i<c.length;i+=2){var d=c[i],f=c[i+1];"style"===d?pe(a,f):"dangerouslySetInnerHTML"===d?ue(a,f):"children"===d?de(a,f):v(a,d,f,u)}switch(s){case"input":G(a,o);break;case"textarea":le(a,o);break;case"select":var m=a._wrapperState.wasMultiple;a._wrapperState.wasMultiple=!!o.multiple;var h=o.value;null!=h?ne(a,!!o.multiple,h,!1):m!==!!o.multiple&&(null!=o.defaultValue?ne(a,!!o.multiple,o.defaultValue,!0):ne(a,!!o.multiple,o.multiple?[]:"",!1))}a[ka]=o}catch(an){Mc(e,e.return,an)}}break;case 6:if(Es(t,e),Os(e),4&r){if(null===e.stateNode)throw Error(l(162));a=e.stateNode,o=e.memoizedProps;try{a.nodeValue=o}catch(an){Mc(e,e.return,an)}}break;case 3:if(Es(t,e),Os(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Bt(t.containerInfo)}catch(an){Mc(e,e.return,an)}break;case 4:default:Es(t,e),Os(e);break;case 13:Es(t,e),Os(e),8192&(a=e.child).flags&&(o=null!==a.memoizedState,a.stateNode.isHidden=o,!o||null!==a.alternate&&null!==a.alternate.memoizedState||(Gs=Xe())),4&r&&Ss(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(is=(u=is)||d,Es(t,e),is=u):Es(t,e),Os(e),8192&r){if(u=null!==e.memoizedState,(e.stateNode.isHidden=u)&&!d&&0!==(1&e.mode))for(cs=e,d=e.child;null!==d;){for(f=cs=d;null!==cs;){switch(h=(m=cs).child,m.tag){case 0:case 11:case 14:case 15:ms(4,m,m.return);break;case 1:us(m,m.return);var p=m.stateNode;if("function"===typeof p.componentWillUnmount){r=m,n=m.return;try{t=r,p.props=t.memoizedProps,p.state=t.memoizedState,p.componentWillUnmount()}catch(an){Mc(r,n,an)}}break;case 5:us(m,m.return);break;case 22:if(null!==m.memoizedState){Ls(f);continue}}null!==h?(h.return=m,cs=h):Ls(f)}d=d.sibling}e:for(d=null,f=e;;){if(5===f.tag){if(null===d){d=f;try{a=f.stateNode,u?"function"===typeof(o=a.style).setProperty?o.setProperty("display","none","important"):o.display="none":(s=f.stateNode,i=void 0!==(c=f.memoizedProps.style)&&null!==c&&c.hasOwnProperty("display")?c.display:null,s.style.display=he("display",i))}catch(an){Mc(e,e.return,an)}}}else if(6===f.tag){if(null===d)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(an){Mc(e,e.return,an)}}else if((22!==f.tag&&23!==f.tag||null===f.memoizedState||f===e)&&null!==f.child){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;null===f.sibling;){if(null===f.return||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:Es(t,e),Os(e),4&r&&Ss(e);case 21:}}function Os(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(xs(n)){var r=n;break e}n=n.return}throw Error(l(160))}switch(r.tag){case 5:var a=r.stateNode;32&r.flags&&(de(a,""),r.flags&=-33),bs(e,ys(e),a);break;case 3:case 4:var o=r.stateNode.containerInfo;vs(e,ys(e),o);break;default:throw Error(l(161))}}catch(Tn){Mc(e,e.return,Tn)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function Ps(e,t,n){cs=e,_s(e,t,n)}function _s(e,t,n){for(var r=0!==(1&e.mode);null!==cs;){var a=cs,l=a.child;if(22===a.tag&&r){var o=null!==a.memoizedState||os;if(!o){var i=a.alternate,s=null!==i&&null!==i.memoizedState||is;i=os;var c=is;if(os=o,(is=s)&&!c)for(cs=a;null!==cs;)s=(o=cs).child,22===o.tag&&null!==o.memoizedState?Ts(a):null!==s?(s.return=o,cs=s):Ts(a);for(;null!==l;)cs=l,_s(l,t,n),l=l.sibling;cs=a,os=i,is=c}Rs(e)}else 0!==(8772&a.subtreeFlags)&&null!==l?(l.return=a,cs=l):Rs(e)}}function Rs(e){for(;null!==cs;){var t=cs;if(0!==(8772&t.flags)){var n=t.alternate;try{if(0!==(8772&t.flags))switch(t.tag){case 0:case 11:case 15:is||hs(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!is)if(null===n)r.componentDidMount();else{var a=t.elementType===t.type?n.memoizedProps:mi(t.type,n.memoizedProps);r.componentDidUpdate(a,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;null!==o&&eo(t,o,r);break;case 3:var i=t.updateQueue;if(null!==i){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}eo(t,i,n)}break;case 5:var s=t.stateNode;if(null===n&&4&t.flags){n=s;var c=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":c.autoFocus&&n.focus();break;case"img":c.src&&(n.src=c.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var u=t.alternate;if(null!==u){var d=u.memoizedState;if(null!==d){var f=d.dehydrated;null!==f&&Bt(f)}}}break;default:throw Error(l(163))}is||512&t.flags&&ps(t)}catch(rn){Mc(t,t.return,rn)}}if(t===e){cs=null;break}if(null!==(n=t.sibling)){n.return=t.return,cs=n;break}cs=t.return}}function Ls(e){for(;null!==cs;){var t=cs;if(t===e){cs=null;break}var n=t.sibling;if(null!==n){n.return=t.return,cs=n;break}cs=t.return}}function Ts(e){for(;null!==cs;){var t=cs;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{hs(4,t)}catch(Tn){Mc(t,n,Tn)}break;case 1:var r=t.stateNode;if("function"===typeof r.componentDidMount){var a=t.return;try{r.componentDidMount()}catch(Tn){Mc(t,a,Tn)}}var l=t.return;try{ps(t)}catch(Tn){Mc(t,l,Tn)}break;case 5:var o=t.return;try{ps(t)}catch(Tn){Mc(t,o,Tn)}}}catch(Tn){Mc(t,t.return,Tn)}if(t===e){cs=null;break}var i=t.sibling;if(null!==i){i.return=t.return,cs=i;break}cs=t.return}}var As,Ms=Math.ceil,Is=b.ReactCurrentDispatcher,Ds=b.ReactCurrentOwner,Fs=b.ReactCurrentBatchConfig,zs=0,Us=null,Bs=null,Ws=0,Vs=0,qs=Ma(0),$s=0,Hs=null,Qs=0,Ks=0,Js=0,Xs=null,Zs=null,Gs=0,Ys=1/0,ec=null,tc=!1,nc=null,rc=null,ac=!1,lc=null,oc=0,ic=0,sc=null,cc=-1,uc=0;function dc(){return 0!==(6&zs)?Xe():-1!==cc?cc:cc=Xe()}function fc(e){return 0===(1&e.mode)?1:0!==(2&zs)&&0!==Ws?Ws&-Ws:null!==El.transition?(0===uc&&(uc=ht()),uc):0!==(e=yt)?e:e=void 0===(e=window.event)?16:Jt(e.type)}function mc(e,t,n,r){if(50<ic)throw ic=0,sc=null,Error(l(185));gt(e,n,r),0!==(2&zs)&&e===Us||(e===Us&&(0===(2&zs)&&(Ks|=n),4===$s&&yc(e,Ws)),hc(e,r),1===n&&0===zs&&0===(1&t.mode)&&(Ys=Xe()+500,Xa&&Ya()))}function hc(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,l=e.pendingLanes;0<l;){var o=31-lt(l),i=1<<o,s=a[o];-1===s?0!==(i&n)&&0===(i&r)||(a[o]=ft(i,t)):s<=t&&(e.expiredLanes|=i),l&=~i}}(e,t);var r=dt(e,e===Us?Ws:0);if(0===r)null!==n&&Qe(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&Qe(n),1===t)0===e.tag?function(e){Xa=!0,Ga(e)}(vc.bind(null,e)):Ga(vc.bind(null,e)),xa((function(){0===(6&zs)&&Ya()})),n=null;else{switch(vt(r)){case 1:n=Ge;break;case 4:n=Ye;break;case 16:default:n=et;break;case 536870912:n=nt}n=Uc(n,pc.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function pc(e,t){if(cc=-1,uc=0,0!==(6&zs))throw Error(l(327));var n=e.callbackNode;if(Tc()&&e.callbackNode!==n)return null;var r=dt(e,e===Us?Ws:0);if(0===r)return null;if(0!==(30&r)||0!==(r&e.expiredLanes)||t)t=Cc(e,r);else{t=r;var a=zs;zs|=2;var o=Sc();for(Us===e&&Ws===t||(ec=null,Ys=Xe()+500,Nc(e,t));;)try{Pc();break}catch(xn){kc(e,xn)}Dl(),Is.current=o,zs=a,null!==Bs?t=0:(Us=null,Ws=0,t=$s)}if(0!==t){if(2===t&&(0!==(a=mt(e))&&(r=a,t=gc(e,a))),1===t)throw n=Hs,Nc(e,0),yc(e,r),hc(e,Xe()),n;if(6===t)yc(e,r);else{if(a=e.current.alternate,0===(30&r)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var a=n[r],l=a.getSnapshot;a=a.value;try{if(!yr(l(),a))return!1}catch(i){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(a)&&(2===(t=Cc(e,r))&&(0!==(o=mt(e))&&(r=o,t=gc(e,o))),1===t))throw n=Hs,Nc(e,0),yc(e,r),hc(e,Xe()),n;switch(e.finishedWork=a,e.finishedLanes=r,t){case 0:case 1:throw Error(l(345));case 2:case 5:Lc(e,Zs,ec);break;case 3:if(yc(e,r),(130023424&r)===r&&10<(t=Gs+500-Xe())){if(0!==dt(e,0))break;if(((a=e.suspendedLanes)&r)!==r){dc(),e.pingedLanes|=e.suspendedLanes&a;break}e.timeoutHandle=ha(Lc.bind(null,e,Zs,ec),t);break}Lc(e,Zs,ec);break;case 4:if(yc(e,r),(4194240&r)===r)break;for(t=e.eventTimes,a=-1;0<r;){var i=31-lt(r);o=1<<i,(i=t[i])>a&&(a=i),r&=~o}if(r=a,10<(r=(120>(r=Xe()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Ms(r/1960))-r)){e.timeoutHandle=ha(Lc.bind(null,e,Zs,ec),r);break}Lc(e,Zs,ec);break;default:throw Error(l(329))}}}return hc(e,Xe()),e.callbackNode===n?pc.bind(null,e):null}function gc(e,t){var n=Xs;return e.current.memoizedState.isDehydrated&&(Nc(e,t).flags|=256),2!==(e=Cc(e,t))&&(t=Zs,Zs=n,null!==t&&xc(t)),e}function xc(e){null===Zs?Zs=e:Zs.push.apply(Zs,e)}function yc(e,t){for(t&=~Js,t&=~Ks,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-lt(t),r=1<<n;e[n]=-1,t&=~r}}function vc(e){if(0!==(6&zs))throw Error(l(327));Tc();var t=dt(e,0);if(0===(1&t))return hc(e,Xe()),null;var n=Cc(e,t);if(0!==e.tag&&2===n){var r=mt(e);0!==r&&(t=r,n=gc(e,r))}if(1===n)throw n=Hs,Nc(e,0),yc(e,t),hc(e,Xe()),n;if(6===n)throw Error(l(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Lc(e,Zs,ec),hc(e,Xe()),null}function bc(e,t){var n=zs;zs|=1;try{return e(t)}finally{0===(zs=n)&&(Ys=Xe()+500,Xa&&Ya())}}function wc(e){null!==lc&&0===lc.tag&&0===(6&zs)&&Tc();var t=zs;zs|=1;var n=Fs.transition,r=yt;try{if(Fs.transition=null,yt=1,e)return e()}finally{yt=r,Fs.transition=n,0===(6&(zs=t))&&Ya()}}function jc(){Vs=qs.current,Ia(qs)}function Nc(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,pa(n)),null!==Bs)for(n=Bs.return;null!==n;){var r=n;switch(fl(r),r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&qa();break;case 3:io(),Ia(Ua),Ia(za),ho();break;case 5:co(r);break;case 4:io();break;case 13:case 19:Ia(uo);break;case 10:Fl(r.type._context);break;case 22:case 23:jc()}n=n.return}if(Us=e,Bs=e=qc(e.current,null),Ws=Vs=t,$s=0,Hs=null,Js=Ks=Qs=0,Zs=Xs=null,null!==Wl){for(t=0;t<Wl.length;t++)if(null!==(r=(n=Wl[t]).interleaved)){n.interleaved=null;var a=r.next,l=n.pending;if(null!==l){var o=l.next;l.next=a,r.next=o}n.pending=r}Wl=null}return e}function kc(e,t){for(;;){var n=Bs;try{if(Dl(),po.current=ci,wo){for(var r=yo.memoizedState;null!==r;){var a=r.queue;null!==a&&(a.pending=null),r=r.next}wo=!1}if(xo=0,bo=vo=yo=null,jo=!1,No=0,Ds.current=null,null===n||null===n.return){$s=1,Hs=t,Bs=null;break}e:{var o=e,i=n.return,s=n,c=t;if(t=Ws,s.flags|=32768,null!==c&&"object"===typeof c&&"function"===typeof c.then){var u=c,d=s,f=d.tag;if(0===(1&d.mode)&&(0===f||11===f||15===f)){var m=d.alternate;m?(d.updateQueue=m.updateQueue,d.memoizedState=m.memoizedState,d.lanes=m.lanes):(d.updateQueue=null,d.memoizedState=null)}var h=Ci(i);if(null!==h){h.flags&=-257,Oi(h,i,s,0,t),1&h.mode&&Ei(o,u,t),c=u;var p=(t=h).updateQueue;if(null===p){var g=new Set;g.add(c),t.updateQueue=g}else p.add(c);break e}if(0===(1&t)){Ei(o,u,t),Ec();break e}c=Error(l(426))}else if(pl&&1&s.mode){var x=Ci(i);if(null!==x){0===(65536&x.flags)&&(x.flags|=256),Oi(x,i,s,0,t),Sl(bi(c,s));break e}}o=c=bi(c,s),4!==$s&&($s=2),null===Xs?Xs=[o]:Xs.push(o),o=i;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t,Gl(o,ki(0,c,t));break e;case 1:s=c;var y=o.type,v=o.stateNode;if(0===(128&o.flags)&&("function"===typeof y.getDerivedStateFromError||null!==v&&"function"===typeof v.componentDidCatch&&(null===rc||!rc.has(v)))){o.flags|=65536,t&=-t,o.lanes|=t,Gl(o,Si(o,s,t));break e}}o=o.return}while(null!==o)}Rc(n)}catch(b){t=b,Bs===n&&null!==n&&(Bs=n=n.return);continue}break}}function Sc(){var e=Is.current;return Is.current=ci,null===e?ci:e}function Ec(){0!==$s&&3!==$s&&2!==$s||($s=4),null===Us||0===(268435455&Qs)&&0===(268435455&Ks)||yc(Us,Ws)}function Cc(e,t){var n=zs;zs|=2;var r=Sc();for(Us===e&&Ws===t||(ec=null,Nc(e,t));;)try{Oc();break}catch(a){kc(e,a)}if(Dl(),zs=n,Is.current=r,null!==Bs)throw Error(l(261));return Us=null,Ws=0,$s}function Oc(){for(;null!==Bs;)_c(Bs)}function Pc(){for(;null!==Bs&&!Ke();)_c(Bs)}function _c(e){var t=As(e.alternate,e,Vs);e.memoizedProps=e.pendingProps,null===t?Rc(e):Bs=t,Ds.current=null}function Rc(e){var t=e;do{var n=t.alternate;if(e=t.return,0===(32768&t.flags)){if(null!==(n=as(n,t,Vs)))return void(Bs=n)}else{if(null!==(n=ls(n,t)))return n.flags&=32767,void(Bs=n);if(null===e)return $s=6,void(Bs=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(Bs=t);Bs=t=e}while(null!==t);0===$s&&($s=5)}function Lc(e,t,n){var r=yt,a=Fs.transition;try{Fs.transition=null,yt=1,function(e,t,n,r){do{Tc()}while(null!==lc);if(0!==(6&zs))throw Error(l(327));n=e.finishedWork;var a=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(l(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var a=31-lt(n),l=1<<a;t[a]=0,r[a]=-1,e[a]=-1,n&=~l}}(e,o),e===Us&&(Bs=Us=null,Ws=0),0===(2064&n.subtreeFlags)&&0===(2064&n.flags)||ac||(ac=!0,Uc(et,(function(){return Tc(),null}))),o=0!==(15990&n.flags),0!==(15990&n.subtreeFlags)||o){o=Fs.transition,Fs.transition=null;var i=yt;yt=1;var s=zs;zs|=4,Ds.current=null,function(e,t){if(da=Vt,kr(e=Nr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch(Fn){n=null;break e}var i=0,s=-1,c=-1,u=0,d=0,f=e,m=null;t:for(;;){for(var h;f!==n||0!==a&&3!==f.nodeType||(s=i+a),f!==o||0!==r&&3!==f.nodeType||(c=i+r),3===f.nodeType&&(i+=f.nodeValue.length),null!==(h=f.firstChild);)m=f,f=h;for(;;){if(f===e)break t;if(m===n&&++u===a&&(s=i),m===o&&++d===r&&(c=i),null!==(h=f.nextSibling))break;m=(f=m).parentNode}f=h}n=-1===s||-1===c?null:{start:s,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(fa={focusedElem:e,selectionRange:n},Vt=!1,cs=t;null!==cs;)if(e=(t=cs).child,0!==(1028&t.subtreeFlags)&&null!==e)e.return=t,cs=e;else for(;null!==cs;){t=cs;try{var p=t.alternate;if(0!==(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==p){var g=p.memoizedProps,x=p.memoizedState,y=t.stateNode,v=y.getSnapshotBeforeUpdate(t.elementType===t.type?g:mi(t.type,g),x);y.__reactInternalSnapshotBeforeUpdate=v}break;case 3:var b=t.stateNode.containerInfo;1===b.nodeType?b.textContent="":9===b.nodeType&&b.documentElement&&b.removeChild(b.documentElement);break;default:throw Error(l(163))}}catch(Fn){Mc(t,t.return,Fn)}if(null!==(e=t.sibling)){e.return=t.return,cs=e;break}cs=t.return}p=fs,fs=!1}(e,n),Cs(n,e),Sr(fa),Vt=!!da,fa=da=null,e.current=n,Ps(n,e,a),Je(),zs=s,yt=i,Fs.transition=o}else e.current=n;if(ac&&(ac=!1,lc=e,oc=a),o=e.pendingLanes,0===o&&(rc=null),function(e){if(at&&"function"===typeof at.onCommitFiberRoot)try{at.onCommitFiberRoot(rt,e,void 0,128===(128&e.current.flags))}catch(t){}}(n.stateNode),hc(e,Xe()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)a=t[n],r(a.value,{componentStack:a.stack,digest:a.digest});if(tc)throw tc=!1,e=nc,nc=null,e;0!==(1&oc)&&0!==e.tag&&Tc(),o=e.pendingLanes,0!==(1&o)?e===sc?ic++:(ic=0,sc=e):ic=0,Ya()}(e,t,n,r)}finally{Fs.transition=a,yt=r}return null}function Tc(){if(null!==lc){var e=vt(oc),t=Fs.transition,n=yt;try{if(Fs.transition=null,yt=16>e?16:e,null===lc)var r=!1;else{if(e=lc,lc=null,oc=0,0!==(6&zs))throw Error(l(331));var a=zs;for(zs|=4,cs=e.current;null!==cs;){var o=cs,i=o.child;if(0!==(16&cs.flags)){var s=o.deletions;if(null!==s){for(var c=0;c<s.length;c++){var u=s[c];for(cs=u;null!==cs;){var d=cs;switch(d.tag){case 0:case 11:case 15:ms(8,d,o)}var f=d.child;if(null!==f)f.return=d,cs=f;else for(;null!==cs;){var m=(d=cs).sibling,h=d.return;if(gs(d),d===u){cs=null;break}if(null!==m){m.return=h,cs=m;break}cs=h}}}var p=o.alternate;if(null!==p){var g=p.child;if(null!==g){p.child=null;do{var x=g.sibling;g.sibling=null,g=x}while(null!==g)}}cs=o}}if(0!==(2064&o.subtreeFlags)&&null!==i)i.return=o,cs=i;else e:for(;null!==cs;){if(0!==(2048&(o=cs).flags))switch(o.tag){case 0:case 11:case 15:ms(9,o,o.return)}var y=o.sibling;if(null!==y){y.return=o.return,cs=y;break e}cs=o.return}}var v=e.current;for(cs=v;null!==cs;){var b=(i=cs).child;if(0!==(2064&i.subtreeFlags)&&null!==b)b.return=i,cs=b;else e:for(i=v;null!==cs;){if(0!==(2048&(s=cs).flags))try{switch(s.tag){case 0:case 11:case 15:hs(9,s)}}catch(j){Mc(s,s.return,j)}if(s===i){cs=null;break e}var w=s.sibling;if(null!==w){w.return=s.return,cs=w;break e}cs=s.return}}if(zs=a,Ya(),at&&"function"===typeof at.onPostCommitFiberRoot)try{at.onPostCommitFiberRoot(rt,e)}catch(j){}r=!0}return r}finally{yt=n,Fs.transition=t}}return!1}function Ac(e,t,n){e=Xl(e,t=ki(0,t=bi(n,t),1),1),t=dc(),null!==e&&(gt(e,1,t),hc(e,t))}function Mc(e,t,n){if(3===e.tag)Ac(e,e,n);else for(;null!==t;){if(3===t.tag){Ac(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===rc||!rc.has(r))){t=Xl(t,e=Si(t,e=bi(n,e),1),1),e=dc(),null!==t&&(gt(t,1,e),hc(t,e));break}}t=t.return}}function Ic(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=dc(),e.pingedLanes|=e.suspendedLanes&n,Us===e&&(Ws&n)===n&&(4===$s||3===$s&&(130023424&Ws)===Ws&&500>Xe()-Gs?Nc(e,0):Js|=n),hc(e,t)}function Dc(e,t){0===t&&(0===(1&e.mode)?t=1:(t=ct,0===(130023424&(ct<<=1))&&(ct=4194304)));var n=dc();null!==(e=$l(e,t))&&(gt(e,t,n),hc(e,n))}function Fc(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Dc(e,n)}function zc(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(l(314))}null!==r&&r.delete(t),Dc(e,n)}function Uc(e,t){return He(e,t)}function Bc(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Wc(e,t,n,r){return new Bc(e,t,n,r)}function Vc(e){return!(!(e=e.prototype)||!e.isReactComponent)}function qc(e,t){var n=e.alternate;return null===n?((n=Wc(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function $c(e,t,n,r,a,o){var i=2;if(r=e,"function"===typeof e)Vc(e)&&(i=1);else if("string"===typeof e)i=5;else e:switch(e){case N:return Hc(n.children,a,o,t);case k:i=8,a|=8;break;case S:return(e=Wc(12,n,t,2|a)).elementType=S,e.lanes=o,e;case P:return(e=Wc(13,n,t,a)).elementType=P,e.lanes=o,e;case _:return(e=Wc(19,n,t,a)).elementType=_,e.lanes=o,e;case T:return Qc(n,a,o,t);default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case E:i=10;break e;case C:i=9;break e;case O:i=11;break e;case R:i=14;break e;case L:i=16,r=null;break e}throw Error(l(130,null==e?e:typeof e,""))}return(t=Wc(i,n,t,a)).elementType=e,t.type=r,t.lanes=o,t}function Hc(e,t,n,r){return(e=Wc(7,e,r,t)).lanes=n,e}function Qc(e,t,n,r){return(e=Wc(22,e,r,t)).elementType=T,e.lanes=n,e.stateNode={isHidden:!1},e}function Kc(e,t,n){return(e=Wc(6,e,null,t)).lanes=n,e}function Jc(e,t,n){return(t=Wc(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Xc(e,t,n,r,a){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=pt(0),this.expirationTimes=pt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=pt(0),this.identifierPrefix=r,this.onRecoverableError=a,this.mutableSourceEagerHydrationData=null}function Zc(e,t,n,r,a,l,o,i,s){return e=new Xc(e,t,n,i,s),1===t?(t=1,!0===l&&(t|=8)):t=0,l=Wc(3,null,null,t),e.current=l,l.stateNode=e,l.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ql(l),e}function Gc(e){if(!e)return Fa;e:{if(Be(e=e._reactInternals)!==e||1!==e.tag)throw Error(l(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Va(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(l(171))}if(1===e.tag){var n=e.type;if(Va(n))return Ha(e,n,t)}return t}function Yc(e,t,n,r,a,l,o,i,s){return(e=Zc(n,r,!0,e,0,l,0,i,s)).context=Gc(null),n=e.current,(l=Jl(r=dc(),a=fc(n))).callback=void 0!==t&&null!==t?t:null,Xl(n,l,a),e.current.lanes=a,gt(e,a,r),hc(e,r),e}function eu(e,t,n,r){var a=t.current,l=dc(),o=fc(a);return n=Gc(n),null===t.context?t.context=n:t.pendingContext=n,(t=Jl(l,o)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Xl(a,t,o))&&(mc(e,a,o,l),Zl(e,a,o)),o}function tu(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function nu(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function ru(e,t){nu(e,t),(e=e.alternate)&&nu(e,t)}As=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||Ua.current)_i=!0;else{if(0===(e.lanes&n)&&0===(128&t.flags))return _i=!1,function(e,t,n){switch(t.tag){case 3:Ui(t),kl();break;case 5:so(t);break;case 1:Va(t.type)&&Qa(t);break;case 4:oo(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,a=t.memoizedProps.value;Da(Tl,r._currentValue),r._currentValue=a;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(Da(uo,1&uo.current),t.flags|=128,null):0!==(n&t.child.childLanes)?Ki(e,t,n):(Da(uo,1&uo.current),null!==(e=ts(e,t,n))?e.sibling:null);Da(uo,1&uo.current);break;case 19:if(r=0!==(n&t.childLanes),0!==(128&e.flags)){if(r)return Yi(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),Da(uo,uo.current),r)break;return null;case 22:case 23:return t.lanes=0,Mi(e,t,n)}return ts(e,t,n)}(e,t,n);_i=0!==(131072&e.flags)}else _i=!1,pl&&0!==(1048576&t.flags)&&ul(t,rl,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;es(e,t),e=t.pendingProps;var a=Wa(t,za.current);Ul(t,n),a=Co(null,t,r,e,a,n);var o=Oo();return t.flags|=1,"object"===typeof a&&null!==a&&"function"===typeof a.render&&void 0===a.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Va(r)?(o=!0,Qa(t)):o=!1,t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,Ql(t),a.updater=pi,t.stateNode=a,a._reactInternals=t,vi(t,r,e,n),t=zi(null,t,r,!0,o,n)):(t.tag=0,pl&&o&&dl(t),Ri(null,t,a,n),t=t.child),t;case 16:r=t.elementType;e:{switch(es(e,t),e=t.pendingProps,r=(a=r._init)(r._payload),t.type=r,a=t.tag=function(e){if("function"===typeof e)return Vc(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===O)return 11;if(e===R)return 14}return 2}(r),e=mi(r,e),a){case 0:t=Di(null,t,r,e,n);break e;case 1:t=Fi(null,t,r,e,n);break e;case 11:t=Li(null,t,r,e,n);break e;case 14:t=Ti(null,t,r,mi(r.type,e),n);break e}throw Error(l(306,r,""))}return t;case 0:return r=t.type,a=t.pendingProps,Di(e,t,r,a=t.elementType===r?a:mi(r,a),n);case 1:return r=t.type,a=t.pendingProps,Fi(e,t,r,a=t.elementType===r?a:mi(r,a),n);case 3:e:{if(Ui(t),null===e)throw Error(l(387));r=t.pendingProps,a=(o=t.memoizedState).element,Kl(e,t),Yl(t,r,null,n);var i=t.memoizedState;if(r=i.element,o.isDehydrated){if(o={element:r,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=o,t.memoizedState=o,256&t.flags){t=Bi(e,t,r,n,a=bi(Error(l(423)),t));break e}if(r!==a){t=Bi(e,t,r,n,a=bi(Error(l(424)),t));break e}for(hl=ba(t.stateNode.containerInfo.firstChild),ml=t,pl=!0,gl=null,n=Ll(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(kl(),r===a){t=ts(e,t,n);break e}Ri(e,t,r,n)}t=t.child}return t;case 5:return so(t),null===e&&bl(t),r=t.type,a=t.pendingProps,o=null!==e?e.memoizedProps:null,i=a.children,ma(r,a)?i=null:null!==o&&ma(r,o)&&(t.flags|=32),Ii(e,t),Ri(e,t,i,n),t.child;case 6:return null===e&&bl(t),null;case 13:return Ki(e,t,n);case 4:return oo(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=Rl(t,null,r,n):Ri(e,t,r,n),t.child;case 11:return r=t.type,a=t.pendingProps,Li(e,t,r,a=t.elementType===r?a:mi(r,a),n);case 7:return Ri(e,t,t.pendingProps,n),t.child;case 8:case 12:return Ri(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,a=t.pendingProps,o=t.memoizedProps,i=a.value,Da(Tl,r._currentValue),r._currentValue=i,null!==o)if(yr(o.value,i)){if(o.children===a.children&&!Ua.current){t=ts(e,t,n);break e}}else for(null!==(o=t.child)&&(o.return=t);null!==o;){var s=o.dependencies;if(null!==s){i=o.child;for(var c=s.firstContext;null!==c;){if(c.context===r){if(1===o.tag){(c=Jl(-1,n&-n)).tag=2;var u=o.updateQueue;if(null!==u){var d=(u=u.shared).pending;null===d?c.next=c:(c.next=d.next,d.next=c),u.pending=c}}o.lanes|=n,null!==(c=o.alternate)&&(c.lanes|=n),zl(o.return,n,t),s.lanes|=n;break}c=c.next}}else if(10===o.tag)i=o.type===t.type?null:o.child;else if(18===o.tag){if(null===(i=o.return))throw Error(l(341));i.lanes|=n,null!==(s=i.alternate)&&(s.lanes|=n),zl(i,n,t),i=o.sibling}else i=o.child;if(null!==i)i.return=o;else for(i=o;null!==i;){if(i===t){i=null;break}if(null!==(o=i.sibling)){o.return=i.return,i=o;break}i=i.return}o=i}Ri(e,t,a.children,n),t=t.child}return t;case 9:return a=t.type,r=t.pendingProps.children,Ul(t,n),r=r(a=Bl(a)),t.flags|=1,Ri(e,t,r,n),t.child;case 14:return a=mi(r=t.type,t.pendingProps),Ti(e,t,r,a=mi(r.type,a),n);case 15:return Ai(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:mi(r,a),es(e,t),t.tag=1,Va(r)?(e=!0,Qa(t)):e=!1,Ul(t,n),xi(t,r,a),vi(t,r,a,n),zi(null,t,r,!0,e,n);case 19:return Yi(e,t,n);case 22:return Mi(e,t,n)}throw Error(l(156,t.tag))};var au="function"===typeof reportError?reportError:function(e){console.error(e)};function lu(e){this._internalRoot=e}function ou(e){this._internalRoot=e}function iu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function su(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function cu(){}function uu(e,t,n,r,a){var l=n._reactRootContainer;if(l){var o=l;if("function"===typeof a){var i=a;a=function(){var e=tu(o);i.call(e)}}eu(t,o,e,a)}else o=function(e,t,n,r,a){if(a){if("function"===typeof r){var l=r;r=function(){var e=tu(o);l.call(e)}}var o=Yc(t,r,e,0,null,!1,0,"",cu);return e._reactRootContainer=o,e[Sa]=o.current,Yr(8===e.nodeType?e.parentNode:e),wc(),o}for(;a=e.lastChild;)e.removeChild(a);if("function"===typeof r){var i=r;r=function(){var e=tu(s);i.call(e)}}var s=Zc(e,0,!1,null,0,!1,0,"",cu);return e._reactRootContainer=s,e[Sa]=s.current,Yr(8===e.nodeType?e.parentNode:e),wc((function(){eu(t,s,n,r)})),s}(n,t,e,a,r);return tu(o)}ou.prototype.render=lu.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(l(409));eu(e,t,null,null)},ou.prototype.unmount=lu.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;wc((function(){eu(null,e,null,null)})),t[Sa]=null}},ou.prototype.unstable_scheduleHydration=function(e){if(e){var t=Nt();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Lt.length&&0!==t&&t<Lt[n].priority;n++);Lt.splice(n,0,e),0===n&&It(e)}},bt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=ut(t.pendingLanes);0!==n&&(xt(t,1|n),hc(t,Xe()),0===(6&zs)&&(Ys=Xe()+500,Ya()))}break;case 13:wc((function(){var t=$l(e,1);if(null!==t){var n=dc();mc(t,e,1,n)}})),ru(e,1)}},wt=function(e){if(13===e.tag){var t=$l(e,134217728);if(null!==t)mc(t,e,134217728,dc());ru(e,134217728)}},jt=function(e){if(13===e.tag){var t=fc(e),n=$l(e,t);if(null!==n)mc(n,e,t,dc());ru(e,t)}},Nt=function(){return yt},kt=function(e,t){var n=yt;try{return yt=e,t()}finally{yt=n}},we=function(e,t,n){switch(t){case"input":if(G(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=La(r);if(!a)throw Error(l(90));Q(r),G(r,a)}}}break;case"textarea":le(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},Ce=bc,Oe=wc;var du={usingClientEntryPoint:!1,Events:[_a,Ra,La,Se,Ee,bc]},fu={findFiberByHostInstance:Pa,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},mu={bundleType:fu.bundleType,version:fu.version,rendererPackageName:fu.rendererPackageName,rendererConfig:fu.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:b.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=qe(e))?null:e.stateNode},findFiberByHostInstance:fu.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var hu=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!hu.isDisabled&&hu.supportsFiber)try{rt=hu.inject(mu),at=hu}catch(cn){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=du,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!iu(t))throw Error(l(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:j,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!iu(e))throw Error(l(299));var n=!1,r="",a=au;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(a=t.onRecoverableError)),t=Zc(e,1,!1,null,0,n,0,r,a),e[Sa]=t.current,Yr(8===e.nodeType?e.parentNode:e),new lu(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(l(188));throw e=Object.keys(e).join(","),Error(l(268,e))}return e=null===(e=qe(t))?null:e.stateNode},t.flushSync=function(e){return wc(e)},t.hydrate=function(e,t,n){if(!su(t))throw Error(l(200));return uu(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!iu(e))throw Error(l(405));var r=null!=n&&n.hydratedSources||null,a=!1,o="",i=au;if(null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(a=!0),void 0!==n.identifierPrefix&&(o=n.identifierPrefix),void 0!==n.onRecoverableError&&(i=n.onRecoverableError)),t=Yc(t,null,e,1,null!=n?n:null,a,0,o,i),e[Sa]=t.current,Yr(e),r)for(e=0;e<r.length;e++)a=(a=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,a]:t.mutableSourceEagerHydrationData.push(n,a);return new ou(t)},t.render=function(e,t,n){if(!su(t))throw Error(l(200));return uu(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!su(e))throw Error(l(40));return!!e._reactRootContainer&&(wc((function(){uu(null,null,e,!1,(function(){e._reactRootContainer=null,e[Sa]=null}))})),!0)},t.unstable_batchedUpdates=bc,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!su(n))throw Error(l(200));if(null==e||void 0===e._reactInternals)throw Error(l(38));return uu(e,t,n,!1,r)},t.version="18.3.1-next-f1338f8080-20240426"},763:(e,t,n)=>{e.exports=n(983)},853:(e,t,n)=>{e.exports=n(234)},950:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(730)},983:(e,t)=>{var n="function"===typeof Symbol&&Symbol.for,r=n?Symbol.for("react.element"):60103,a=n?Symbol.for("react.portal"):60106,l=n?Symbol.for("react.fragment"):60107,o=n?Symbol.for("react.strict_mode"):60108,i=n?Symbol.for("react.profiler"):60114,s=n?Symbol.for("react.provider"):60109,c=n?Symbol.for("react.context"):60110,u=n?Symbol.for("react.async_mode"):60111,d=n?Symbol.for("react.concurrent_mode"):60111,f=n?Symbol.for("react.forward_ref"):60112,m=n?Symbol.for("react.suspense"):60113,h=n?Symbol.for("react.suspense_list"):60120,p=n?Symbol.for("react.memo"):60115,g=n?Symbol.for("react.lazy"):60116,x=n?Symbol.for("react.block"):60121,y=n?Symbol.for("react.fundamental"):60117,v=n?Symbol.for("react.responder"):60118,b=n?Symbol.for("react.scope"):60119;function w(e){if("object"===typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case u:case d:case l:case i:case o:case m:return e;default:switch(e=e&&e.$$typeof){case c:case f:case g:case p:case s:return e;default:return t}}case a:return t}}}function j(e){return w(e)===d}t.AsyncMode=u,t.ConcurrentMode=d,t.ContextConsumer=c,t.ContextProvider=s,t.Element=r,t.ForwardRef=f,t.Fragment=l,t.Lazy=g,t.Memo=p,t.Portal=a,t.Profiler=i,t.StrictMode=o,t.Suspense=m,t.isAsyncMode=function(e){return j(e)||w(e)===u},t.isConcurrentMode=j,t.isContextConsumer=function(e){return w(e)===c},t.isContextProvider=function(e){return w(e)===s},t.isElement=function(e){return"object"===typeof e&&null!==e&&e.$$typeof===r},t.isForwardRef=function(e){return w(e)===f},t.isFragment=function(e){return w(e)===l},t.isLazy=function(e){return w(e)===g},t.isMemo=function(e){return w(e)===p},t.isPortal=function(e){return w(e)===a},t.isProfiler=function(e){return w(e)===i},t.isStrictMode=function(e){return w(e)===o},t.isSuspense=function(e){return w(e)===m},t.isValidElementType=function(e){return"string"===typeof e||"function"===typeof e||e===l||e===d||e===i||e===o||e===m||e===h||"object"===typeof e&&null!==e&&(e.$$typeof===g||e.$$typeof===p||e.$$typeof===s||e.$$typeof===c||e.$$typeof===f||e.$$typeof===y||e.$$typeof===v||e.$$typeof===b||e.$$typeof===x)},t.typeOf=w}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var l=t[r]={exports:{}};return e[r](l,l.exports,n),l.exports}n.m=e,(()=>{var e,t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__;n.t=function(r,a){if(1&a&&(r=this(r)),8&a)return r;if("object"===typeof r&&r){if(4&a&&r.__esModule)return r;if(16&a&&"function"===typeof r.then)return r}var l=Object.create(null);n.r(l);var o={};e=e||[null,t({}),t([]),t(t)];for(var i=2&a&&r;"object"==typeof i&&!~e.indexOf(i);i=t(i))Object.getOwnPropertyNames(i).forEach((e=>o[e]=()=>r[e]));return o.default=()=>r,n.d(l,o),l}})(),n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.f={},n.e=e=>Promise.all(Object.keys(n.f).reduce(((t,r)=>(n.f[r](e,t),t)),[])),n.u=e=>"static/js/"+e+".322e7791.chunk.js",n.miniCssF=e=>{},n.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={},t="cainuro-orchestrator-ui:";n.l=(r,a,l,o)=>{if(e[r])e[r].push(a);else{var i,s;if(void 0!==l)for(var c=document.getElementsByTagName("script"),u=0;u<c.length;u++){var d=c[u];if(d.getAttribute("src")==r||d.getAttribute("data-webpack")==t+l){i=d;break}}i||(s=!0,(i=document.createElement("script")).charset="utf-8",i.timeout=120,n.nc&&i.setAttribute("nonce",n.nc),i.setAttribute("data-webpack",t+l),i.src=r),e[r]=[a];var f=(t,n)=>{i.onerror=i.onload=null,clearTimeout(m);var a=e[r];if(delete e[r],i.parentNode&&i.parentNode.removeChild(i),a&&a.forEach((e=>e(n))),t)return t(n)},m=setTimeout(f.bind(null,void 0,{type:"timeout",target:i}),12e4);i.onerror=f.bind(null,i.onerror),i.onload=f.bind(null,i.onload),s&&document.head.appendChild(i)}}})(),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.p="/",(()=>{var e={792:0};n.f.j=(t,r)=>{var a=n.o(e,t)?e[t]:void 0;if(0!==a)if(a)r.push(a[2]);else{var l=new Promise(((n,r)=>a=e[t]=[n,r]));r.push(a[2]=l);var o=n.p+n.u(t),i=new Error;n.l(o,(r=>{if(n.o(e,t)&&(0!==(a=e[t])&&(e[t]=void 0),a)){var l=r&&("load"===r.type?"missing":r.type),o=r&&r.target&&r.target.src;i.message="Loading chunk "+t+" failed.\n("+l+": "+o+")",i.name="ChunkLoadError",i.type=l,i.request=o,a[1](i)}}),"chunk-"+t,t)}};var t=(t,r)=>{var a,l,o=r[0],i=r[1],s=r[2],c=0;if(o.some((t=>0!==e[t]))){for(a in i)n.o(i,a)&&(n.m[a]=i[a]);if(s)s(n)}for(t&&t(r);c<o.length;c++)l=o[c],n.o(e,l)&&e[l]&&e[l][0](),e[l]=0},r=self.webpackChunkcainuro_orchestrator_ui=self.webpackChunkcainuro_orchestrator_ui||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})();var r={};n.r(r),n.d(r,{hasBrowserEnv:()=>Ga,hasStandardBrowserEnv:()=>el,hasStandardBrowserWebWorkerEnv:()=>tl,navigator:()=>Ya,origin:()=>nl});var a=n(43),l=n.t(a,2),o=n(391),i=n(461),s=n(443),c=n(950),u=n.t(c,2);let d=function(e){e()};const f=()=>d,m=Symbol.for("react-redux-context"),h="undefined"!==typeof globalThis?globalThis:{};function p(){var e;if(!a.createContext)return{};const t=null!=(e=h[m])?e:h[m]=new Map;let n=t.get(a.createContext);return n||(n=a.createContext(null),t.set(a.createContext,n)),n}const g=p();function x(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:g;return function(){return(0,a.useContext)(e)}}const y=x();let v=()=>{throw new Error("uSES not initialized!")};const b=(e,t)=>e===t;function w(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:g;const t=e===g?y:x(e);return function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{equalityFn:r=b,stabilityCheck:l,noopCheck:o}="function"===typeof n?{equalityFn:n}:n;const{store:i,subscription:s,getServerState:c,stabilityCheck:u,noopCheck:d}=t(),f=((0,a.useRef)(!0),(0,a.useCallback)({[e.name]:t=>e(t)}[e.name],[e,u,l])),m=v(s.addNestedSub,i.getState,c||i.getState,f,r);return(0,a.useDebugValue)(m),m}}const j=w();n(219),n(86);const N={notify(){},get:()=>[]};function k(e,t){let n,r=N,a=0,l=!1;function o(){c.onStateChange&&c.onStateChange()}function i(){a++,n||(n=t?t.addNestedSub(o):e.subscribe(o),r=function(){const e=f();let t=null,n=null;return{clear(){t=null,n=null},notify(){e((()=>{let e=t;for(;e;)e.callback(),e=e.next}))},get(){let e=[],n=t;for(;n;)e.push(n),n=n.next;return e},subscribe(e){let r=!0,a=n={callback:e,next:null,prev:n};return a.prev?a.prev.next=a:t=a,function(){r&&null!==t&&(r=!1,a.next?a.next.prev=a.prev:n=a.prev,a.prev?a.prev.next=a.next:t=a.next)}}}}())}function s(){a--,n&&0===a&&(n(),n=void 0,r.clear(),r=N)}const c={addNestedSub:function(e){i();const t=r.subscribe(e);let n=!1;return()=>{n||(n=!0,t(),s())}},notifyNestedSubs:function(){r.notify()},handleChangeWrapper:o,isSubscribed:function(){return l},trySubscribe:function(){l||(l=!0,i())},tryUnsubscribe:function(){l&&(l=!1,s())},getListeners:()=>r};return c}const S=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement)?a.useLayoutEffect:a.useEffect;let E=null;const C=function(e){let{store:t,context:n,children:r,serverState:l,stabilityCheck:o="once",noopCheck:i="once"}=e;const s=a.useMemo((()=>{const e=k(t);return{store:t,subscription:e,getServerState:l?()=>l:void 0,stabilityCheck:o,noopCheck:i}}),[t,l,o,i]),c=a.useMemo((()=>t.getState()),[t]);S((()=>{const{subscription:e}=s;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),c!==t.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}}),[s,c]);const u=n||g;return a.createElement(u.Provider,{value:s},r)};function O(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:g;const t=e===g?y:x(e);return function(){const{store:e}=t();return e}}const P=O();function _(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:g;const t=e===g?P:O(e);return function(){return t().dispatch}}const R=_();var L,T;function A(){return A=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},A.apply(this,arguments)}(e=>{v=e})(s.useSyncExternalStoreWithSelector),(e=>{E=e})(i.useSyncExternalStore),L=c.unstable_batchedUpdates,d=L,function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"}(T||(T={}));const M="popstate";function I(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}function D(e,t){if(!e){"undefined"!==typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function F(e,t){return{usr:e.state,key:e.key,idx:t}}function z(e,t,n,r){return void 0===n&&(n=null),A({pathname:"string"===typeof e?e:e.pathname,search:"",hash:""},"string"===typeof t?B(t):t,{state:n,key:t&&t.key||r||Math.random().toString(36).substr(2,8)})}function U(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function B(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function W(e,t,n,r){void 0===r&&(r={});let{window:a=document.defaultView,v5Compat:l=!1}=r,o=a.history,i=T.Pop,s=null,c=u();function u(){return(o.state||{idx:null}).idx}function d(){i=T.Pop;let e=u(),t=null==e?null:e-c;c=e,s&&s({action:i,location:m.location,delta:t})}function f(e){let t="null"!==a.location.origin?a.location.origin:a.location.href,n="string"===typeof e?e:U(e);return n=n.replace(/ $/,"%20"),I(t,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,t)}null==c&&(c=0,o.replaceState(A({},o.state,{idx:c}),""));let m={get action(){return i},get location(){return e(a,o)},listen(e){if(s)throw new Error("A history only accepts one active listener");return a.addEventListener(M,d),s=e,()=>{a.removeEventListener(M,d),s=null}},createHref:e=>t(a,e),createURL:f,encodeLocation(e){let t=f(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){i=T.Push;let r=z(m.location,e,t);n&&n(r,e),c=u()+1;let d=F(r,c),f=m.createHref(r);try{o.pushState(d,"",f)}catch(h){if(h instanceof DOMException&&"DataCloneError"===h.name)throw h;a.location.assign(f)}l&&s&&s({action:i,location:m.location,delta:1})},replace:function(e,t){i=T.Replace;let r=z(m.location,e,t);n&&n(r,e),c=u();let a=F(r,c),d=m.createHref(r);o.replaceState(a,"",d),l&&s&&s({action:i,location:m.location,delta:0})},go:e=>o.go(e)};return m}var V;!function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"}(V||(V={}));new Set(["lazy","caseSensitive","path","id","index","children"]);function q(e,t,n){return void 0===n&&(n="/"),$(e,t,n,!1)}function $(e,t,n,r){let a=le(("string"===typeof t?B(t):t).pathname||"/",n);if(null==a)return null;let l=H(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every(((e,n)=>e===t[n]));return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(l);let o=null;for(let i=0;null==o&&i<l.length;++i){let e=ae(a);o=ne(l[i],e,r)}return o}function H(e,t,n,r){void 0===t&&(t=[]),void 0===n&&(n=[]),void 0===r&&(r="");let a=(e,a,l)=>{let o={relativePath:void 0===l?e.path||"":l,caseSensitive:!0===e.caseSensitive,childrenIndex:a,route:e};o.relativePath.startsWith("/")&&(I(o.relativePath.startsWith(r),'Absolute route path "'+o.relativePath+'" nested under path "'+r+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),o.relativePath=o.relativePath.slice(r.length));let i=ue([r,o.relativePath]),s=n.concat(o);e.children&&e.children.length>0&&(I(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+i+'".'),H(e.children,t,s,i)),(null!=e.path||e.index)&&t.push({path:i,score:te(i,e.index),routesMeta:s})};return e.forEach(((e,t)=>{var n;if(""!==e.path&&null!=(n=e.path)&&n.includes("?"))for(let r of Q(e.path))a(e,t,r);else a(e,t)})),t}function Q(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,a=n.endsWith("?"),l=n.replace(/\?$/,"");if(0===r.length)return a?[l,""]:[l];let o=Q(r.join("/")),i=[];return i.push(...o.map((e=>""===e?l:[l,e].join("/")))),a&&i.push(...o),i.map((t=>e.startsWith("/")&&""===t?"/":t))}const K=/^:[\w-]+$/,J=3,X=2,Z=1,G=10,Y=-2,ee=e=>"*"===e;function te(e,t){let n=e.split("/"),r=n.length;return n.some(ee)&&(r+=Y),t&&(r+=X),n.filter((e=>!ee(e))).reduce(((e,t)=>e+(K.test(t)?J:""===t?Z:G)),r)}function ne(e,t,n){void 0===n&&(n=!1);let{routesMeta:r}=e,a={},l="/",o=[];for(let i=0;i<r.length;++i){let e=r[i],s=i===r.length-1,c="/"===l?t:t.slice(l.length)||"/",u=re({path:e.relativePath,caseSensitive:e.caseSensitive,end:s},c),d=e.route;if(!u&&s&&n&&!r[r.length-1].route.index&&(u=re({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},c)),!u)return null;Object.assign(a,u.params),o.push({params:a,pathname:ue([l,u.pathname]),pathnameBase:de(ue([l,u.pathnameBase])),route:d}),"/"!==u.pathnameBase&&(l=ue([l,u.pathnameBase]))}return o}function re(e,t){"string"===typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=function(e,t,n){void 0===t&&(t=!1);void 0===n&&(n=!0);D("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)")));e.endsWith("*")?(r.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))");let l=new RegExp(a,t?void 0:"i");return[l,r]}(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let l=a[0],o=l.replace(/(.)\/+$/,"$1"),i=a.slice(1),s=r.reduce(((e,t,n)=>{let{paramName:r,isOptional:a}=t;if("*"===r){let e=i[n]||"";o=l.slice(0,l.length-e.length).replace(/(.)\/+$/,"$1")}const s=i[n];return e[r]=a&&!s?void 0:(s||"").replace(/%2F/g,"/"),e}),{});return{params:s,pathname:l,pathnameBase:o,pattern:e}}function ae(e){try{return e.split("/").map((e=>decodeURIComponent(e).replace(/\//g,"%2F"))).join("/")}catch(t){return D(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function le(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function oe(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function ie(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}function se(e,t){let n=ie(e);return t?n.map(((e,t)=>t===n.length-1?e.pathname:e.pathnameBase)):n.map((e=>e.pathnameBase))}function ce(e,t,n,r){let a;void 0===r&&(r=!1),"string"===typeof e?a=B(e):(a=A({},e),I(!a.pathname||!a.pathname.includes("?"),oe("?","pathname","search",a)),I(!a.pathname||!a.pathname.includes("#"),oe("#","pathname","hash",a)),I(!a.search||!a.search.includes("#"),oe("#","search","hash",a)));let l,o=""===e||""===a.pathname,i=o?"/":a.pathname;if(null==i)l=n;else{let e=t.length-1;if(!r&&i.startsWith("..")){let t=i.split("/");for(;".."===t[0];)t.shift(),e-=1;a.pathname=t.join("/")}l=e>=0?t[e]:"/"}let s=function(e,t){void 0===t&&(t="/");let{pathname:n,search:r="",hash:a=""}="string"===typeof e?B(e):e,l=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)})),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:l,search:fe(r),hash:me(a)}}(a,l),c=i&&"/"!==i&&i.endsWith("/"),u=(o||"."===i)&&n.endsWith("/");return s.pathname.endsWith("/")||!c&&!u||(s.pathname+="/"),s}const ue=e=>e.join("/").replace(/\/\/+/g,"/"),de=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),fe=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",me=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";Error;function he(e){return null!=e&&"number"===typeof e.status&&"string"===typeof e.statusText&&"boolean"===typeof e.internal&&"data"in e}const pe=["post","put","patch","delete"],ge=(new Set(pe),["get",...pe]);new Set(ge),new Set([301,302,303,307,308]),new Set([307,308]);Symbol("deferred");function xe(){return xe=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},xe.apply(this,arguments)}const ye=a.createContext(null);const ve=a.createContext(null);const be=a.createContext(null);const we=a.createContext(null);const je=a.createContext({outlet:null,matches:[],isDataRoute:!1});const Ne=a.createContext(null);function ke(){return null!=a.useContext(we)}function Se(){return ke()||I(!1),a.useContext(we).location}function Ee(e){a.useContext(be).static||a.useLayoutEffect(e)}function Ce(){let{isDataRoute:e}=a.useContext(je);return e?function(){let{router:e}=De(Me.UseNavigateStable),t=ze(Ie.UseNavigateStable),n=a.useRef(!1);return Ee((()=>{n.current=!0})),a.useCallback((function(r,a){void 0===a&&(a={}),n.current&&("number"===typeof r?e.navigate(r):e.navigate(r,xe({fromRouteId:t},a)))}),[e,t])}():function(){ke()||I(!1);let e=a.useContext(ye),{basename:t,future:n,navigator:r}=a.useContext(be),{matches:l}=a.useContext(je),{pathname:o}=Se(),i=JSON.stringify(se(l,n.v7_relativeSplatPath)),s=a.useRef(!1);return Ee((()=>{s.current=!0})),a.useCallback((function(n,a){if(void 0===a&&(a={}),!s.current)return;if("number"===typeof n)return void r.go(n);let l=ce(n,JSON.parse(i),o,"path"===a.relative);null==e&&"/"!==t&&(l.pathname="/"===l.pathname?t:ue([t,l.pathname])),(a.replace?r.replace:r.push)(l,a.state,a)}),[t,r,i,o,e])}()}function Oe(e,t){let{relative:n}=void 0===t?{}:t,{future:r}=a.useContext(be),{matches:l}=a.useContext(je),{pathname:o}=Se(),i=JSON.stringify(se(l,r.v7_relativeSplatPath));return a.useMemo((()=>ce(e,JSON.parse(i),o,"path"===n)),[e,i,o,n])}function Pe(e,t,n,r){ke()||I(!1);let{navigator:l}=a.useContext(be),{matches:o}=a.useContext(je),i=o[o.length-1],s=i?i.params:{},c=(i&&i.pathname,i?i.pathnameBase:"/");i&&i.route;let u,d=Se();if(t){var f;let e="string"===typeof t?B(t):t;"/"===c||(null==(f=e.pathname)?void 0:f.startsWith(c))||I(!1),u=e}else u=d;let m=u.pathname||"/",h=m;if("/"!==c){let e=c.replace(/^\//,"").split("/");h="/"+m.replace(/^\//,"").split("/").slice(e.length).join("/")}let p=q(e,{pathname:h});let g=Ae(p&&p.map((e=>Object.assign({},e,{params:Object.assign({},s,e.params),pathname:ue([c,l.encodeLocation?l.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?c:ue([c,l.encodeLocation?l.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),o,n,r);return t&&g?a.createElement(we.Provider,{value:{location:xe({pathname:"/",search:"",hash:"",state:null,key:"default"},u),navigationType:T.Pop}},g):g}function _e(){let e=function(){var e;let t=a.useContext(Ne),n=Fe(Ie.UseRouteError),r=ze(Ie.UseRouteError);if(void 0!==t)return t;return null==(e=n.errors)?void 0:e[r]}(),t=he(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r="rgba(200,200,200, 0.5)",l={padding:"0.5rem",backgroundColor:r};return a.createElement(a.Fragment,null,a.createElement("h2",null,"Unexpected Application Error!"),a.createElement("h3",{style:{fontStyle:"italic"}},t),n?a.createElement("pre",{style:l},n):null,null)}const Re=a.createElement(_e,null);class Le extends a.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?a.createElement(je.Provider,{value:this.props.routeContext},a.createElement(Ne.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Te(e){let{routeContext:t,match:n,children:r}=e,l=a.useContext(ye);return l&&l.static&&l.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(l.staticContext._deepestRenderedBoundaryId=n.route.id),a.createElement(je.Provider,{value:t},r)}function Ae(e,t,n,r){var l;if(void 0===t&&(t=[]),void 0===n&&(n=null),void 0===r&&(r=null),null==e){var o;if(!n)return null;if(n.errors)e=n.matches;else{if(!(null!=(o=r)&&o.v7_partialHydration&&0===t.length&&!n.initialized&&n.matches.length>0))return null;e=n.matches}}let i=e,s=null==(l=n)?void 0:l.errors;if(null!=s){let e=i.findIndex((e=>e.route.id&&void 0!==(null==s?void 0:s[e.route.id])));e>=0||I(!1),i=i.slice(0,Math.min(i.length,e+1))}let c=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let a=0;a<i.length;a++){let e=i[a];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(u=a),e.route.id){let{loaderData:t,errors:r}=n,a=e.route.loader&&void 0===t[e.route.id]&&(!r||void 0===r[e.route.id]);if(e.route.lazy||a){c=!0,i=u>=0?i.slice(0,u+1):[i[0]];break}}}return i.reduceRight(((e,r,l)=>{let o,d=!1,f=null,m=null;var h;n&&(o=s&&r.route.id?s[r.route.id]:void 0,f=r.route.errorElement||Re,c&&(u<0&&0===l?(h="route-fallback",!1||Ue[h]||(Ue[h]=!0),d=!0,m=null):u===l&&(d=!0,m=r.route.hydrateFallbackElement||null)));let p=t.concat(i.slice(0,l+1)),g=()=>{let t;return t=o?f:d?m:r.route.Component?a.createElement(r.route.Component,null):r.route.element?r.route.element:e,a.createElement(Te,{match:r,routeContext:{outlet:e,matches:p,isDataRoute:null!=n},children:t})};return n&&(r.route.ErrorBoundary||r.route.errorElement||0===l)?a.createElement(Le,{location:n.location,revalidation:n.revalidation,component:f,error:o,children:g(),routeContext:{outlet:null,matches:p,isDataRoute:!0}}):g()}),null)}var Me=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(Me||{}),Ie=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Ie||{});function De(e){let t=a.useContext(ye);return t||I(!1),t}function Fe(e){let t=a.useContext(ve);return t||I(!1),t}function ze(e){let t=function(){let e=a.useContext(je);return e||I(!1),e}(),n=t.matches[t.matches.length-1];return n.route.id||I(!1),n.route.id}const Ue={};function Be(e,t){null==e||e.v7_startTransition,void 0===(null==e?void 0:e.v7_relativeSplatPath)&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}l.startTransition;function We(e){I(!1)}function Ve(e){let{basename:t="/",children:n=null,location:r,navigationType:l=T.Pop,navigator:o,static:i=!1,future:s}=e;ke()&&I(!1);let c=t.replace(/^\/*/,"/"),u=a.useMemo((()=>({basename:c,navigator:o,static:i,future:xe({v7_relativeSplatPath:!1},s)})),[c,s,o,i]);"string"===typeof r&&(r=B(r));let{pathname:d="/",search:f="",hash:m="",state:h=null,key:p="default"}=r,g=a.useMemo((()=>{let e=le(d,c);return null==e?null:{location:{pathname:e,search:f,hash:m,state:h,key:p},navigationType:l}}),[c,d,f,m,h,p,l]);return null==g?null:a.createElement(be.Provider,{value:u},a.createElement(we.Provider,{children:n,value:g}))}function qe(e){let{children:t,location:n}=e;return Pe($e(t),n)}new Promise((()=>{}));a.Component;function $e(e,t){void 0===t&&(t=[]);let n=[];return a.Children.forEach(e,((e,r)=>{if(!a.isValidElement(e))return;let l=[...t,r];if(e.type===a.Fragment)return void n.push.apply(n,$e(e.props.children,l));e.type!==We&&I(!1),e.props.index&&e.props.children&&I(!1);let o={id:e.props.id||l.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(o.children=$e(e.props.children,l)),n.push(o)})),n}function He(){return He=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},He.apply(this,arguments)}function Qe(e,t){if(null==e)return{};var n,r,a={},l=Object.keys(e);for(r=0;r<l.length;r++)n=l[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);const Ke=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"];try{window.__reactRouterVersion="6"}catch(vc){}new Map;const Je=l.startTransition;u.flushSync,l.useId;function Xe(e){let{basename:t,children:n,future:r,window:l}=e,o=a.useRef();var i;null==o.current&&(o.current=(void 0===(i={window:l,v5Compat:!0})&&(i={}),W((function(e,t){let{pathname:n,search:r,hash:a}=e.location;return z("",{pathname:n,search:r,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){return"string"===typeof t?t:U(t)}),null,i)));let s=o.current,[c,u]=a.useState({action:s.action,location:s.location}),{v7_startTransition:d}=r||{},f=a.useCallback((e=>{d&&Je?Je((()=>u(e))):u(e)}),[u,d]);return a.useLayoutEffect((()=>s.listen(f)),[s,f]),a.useEffect((()=>Be(r)),[r]),a.createElement(Ve,{basename:t,children:n,location:c.location,navigationType:c.action,navigator:s,future:r})}const Ze="undefined"!==typeof window&&"undefined"!==typeof window.document&&"undefined"!==typeof window.document.createElement,Ge=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Ye=a.forwardRef((function(e,t){let n,{onClick:r,relative:l,reloadDocument:o,replace:i,state:s,target:c,to:u,preventScrollReset:d,viewTransition:f}=e,m=Qe(e,Ke),{basename:h}=a.useContext(be),p=!1;if("string"===typeof u&&Ge.test(u)&&(n=u,Ze))try{let e=new URL(window.location.href),t=u.startsWith("//")?new URL(e.protocol+u):new URL(u),n=le(t.pathname,h);t.origin===e.origin&&null!=n?u=n+t.search+t.hash:p=!0}catch(vc){}let g=function(e,t){let{relative:n}=void 0===t?{}:t;ke()||I(!1);let{basename:r,navigator:l}=a.useContext(be),{hash:o,pathname:i,search:s}=Oe(e,{relative:n}),c=i;return"/"!==r&&(c="/"===i?r:ue([r,i])),l.createHref({pathname:c,search:s,hash:o})}(u,{relative:l}),x=function(e,t){let{target:n,replace:r,state:l,preventScrollReset:o,relative:i,viewTransition:s}=void 0===t?{}:t,c=Ce(),u=Se(),d=Oe(e,{relative:i});return a.useCallback((t=>{if(function(e,t){return 0===e.button&&(!t||"_self"===t)&&!function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e)}(t,n)){t.preventDefault();let n=void 0!==r?r:U(u)===U(d);c(e,{replace:n,state:l,preventScrollReset:o,relative:i,viewTransition:s})}}),[u,c,d,r,l,n,e,o,i,s])}(u,{replace:i,state:s,target:c,preventScrollReset:d,relative:l,viewTransition:f});return a.createElement("a",He({},m,{href:n||g,onClick:p||o?r:function(e){r&&r(e),e.defaultPrevented||x(e)},ref:t,target:c}))}));var et,tt;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(et||(et={})),function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"}(tt||(tt={}));function nt(e){return nt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},nt(e)}function rt(e){var t=function(e,t){if("object"!=nt(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=nt(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==nt(t)?t:t+""}function at(e,t,n){return(t=rt(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function lt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ot(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?lt(Object(n),!0).forEach((function(t){at(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):lt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var it=n(579);const st=(0,a.createContext)(void 0),ct=e=>{let{children:t}=e;const[n,r]=(0,a.useState)({isAuthenticated:!1,isLoading:!0,user:null,token:null,error:null});(0,a.useEffect)((()=>{l()}),[]);const l=async()=>{try{r((e=>ot(ot({},e),{},{isLoading:!0,error:null})));const e=await fetch("/v1/auth/user",{credentials:"include"});if(e.ok){const t=await e.json();r((e=>ot(ot({},e),{},{isAuthenticated:!0,user:t,isLoading:!1,error:null})))}else r((e=>ot(ot({},e),{},{isAuthenticated:!1,isLoading:!1,error:null})))}catch(e){console.error("Auth initialization failed:",e),r((e=>ot(ot({},e),{},{isAuthenticated:!1,isLoading:!1,error:null})))}},o=(e,t,n)=>{var r,a,l;if(!e||!e.permissions)return!1;if(e.permissions.includes("*"))return!0;const o="".concat(n,":").concat(t.toLowerCase());if(e.permissions.includes(o))return!0;const i="*:".concat(t.toLowerCase());if(e.permissions.includes(i))return!0;const s="".concat(n,":*");return!!e.permissions.includes(s)||(!(null===(r=e.roles)||void 0===r||!r.includes("admin"))||(!(null===(a=e.roles)||void 0===a||!a.includes("operator")||"read"!==t.toLowerCase()&&"execute"!==t.toLowerCase())||!(null===(l=e.roles)||void 0===l||!l.includes("viewer")||"read"!==t.toLowerCase())))},i=async()=>{try{const e=await fetch("/v1/auth/refresh",{method:"POST",credentials:"include"});if(e.ok){const t=await e.json(),n=await fetch("/v1/auth/user",{credentials:"include"});if(n.ok){const e=await n.json();r((n=>ot(ot({},n),{},{isAuthenticated:!0,user:e,token:t,isLoading:!1,error:null})))}}else r((e=>ot(ot({},e),{},{isAuthenticated:!1,isLoading:!1,user:null,token:null,error:null})))}catch(e){console.error("Token refresh failed:",e),r((e=>ot(ot({},e),{},{isAuthenticated:!1,isLoading:!1,user:null,token:null,error:null})))}};(0,a.useEffect)((()=>{if(n.token&&n.isAuthenticated){const e=setInterval((()=>{i()}),3e6);return()=>clearInterval(e)}}),[n.token,n.isAuthenticated]);const s=ot(ot({},n),{},{login:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"admin",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"admin";try{r((e=>ot(ot({},e),{},{isLoading:!0,error:null})));const n=await fetch("/v1/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({username:e,password:t})});if(!n.ok){const e=await n.json();throw new Error(e.error||"Login failed")}const a=await n.json();r((e=>ot(ot({},e),{},{isAuthenticated:!0,user:a.user,token:null,isLoading:!1,error:null})))}catch(n){console.error("Login failed:",n),r((e=>ot(ot({},e),{},{isLoading:!1,error:n instanceof Error?n.message:"Login failed"})))}},logout:async()=>{try{r((e=>ot(ot({},e),{},{isLoading:!0})));const e=await fetch("/v1/auth/logout",{method:"POST",credentials:"include"});if(r({isAuthenticated:!1,isLoading:!1,user:null,token:null,error:null}),e.ok){const t=await e.json();if(t.logout_url&&t.logout_url!==window.location.origin)return void(window.location.href=t.logout_url)}window.location.reload()}catch(e){console.error("Logout failed:",e),r((e=>ot(ot({},e),{},{isLoading:!1,error:"Logout failed"})))}},checkPermission:async(e,t)=>{try{var r;if(!n.user)return!1;const a=o(n.user,e,t);if(null!==a)return a;const l=await fetch("/v1/auth/check",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({action:e,resource:t})});if(!l.ok)return!1;return(null===(r=(await l.json()).decision)||void 0===r?void 0:r.allowed)||!1}catch(i){var a,l;return console.error("Permission check failed:",i),!(null===(a=n.user)||void 0===a||null===(l=a.roles)||void 0===l||!l.includes("admin"))}},refreshToken:i});return(0,it.jsx)(st.Provider,{value:s,children:t})},ut=()=>{const e=(0,a.useContext)(st);if(void 0===e)throw new Error("useAuth must be used within an AuthProvider");return e},dt=e=>{let{children:t,requiredPermission:n,fallback:r=(0,it.jsx)("div",{className:"p-4 text-red-600",children:"Access Denied"})}=e;const{isAuthenticated:l,isLoading:o,checkPermission:i}=ut(),[s,c]=(0,a.useState)(null);return(0,a.useEffect)((()=>{l&&n?i(n.action,n.resource).then(c):l&&c(!0)}),[l,n,i]),o?(0,it.jsx)("div",{className:"p-4",children:"Loading..."}):l?n&&!1===s?(0,it.jsx)(it.Fragment,{children:r}):n&&null===s?(0,it.jsx)("div",{className:"p-4",children:"Checking permissions..."}):(0,it.jsx)(it.Fragment,{children:t}):(0,it.jsx)("div",{className:"p-4",children:"Please log in to access this page."})},ft=()=>{const{login:e,isLoading:t,error:n}=ut(),[r,l]=a.useState("admin"),[o,i]=a.useState("admin");return(0,it.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center p-4",children:(0,it.jsxs)("div",{className:"max-w-md w-full bg-white/10 backdrop-blur-lg rounded-2xl shadow-2xl p-8 border border-white/20",children:[(0,it.jsxs)("div",{className:"text-center mb-8",children:[(0,it.jsx)("div",{className:"text-6xl mb-4",children:"\ud83c\udf1f"}),(0,it.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"CAINuro Orchestrator"}),(0,it.jsx)("p",{className:"text-blue-200",children:"Enterprise Cloud Resource Orchestration Platform"})]}),(0,it.jsxs)("form",{onSubmit:t=>{t.preventDefault(),e(r,o)},className:"mb-6",children:[(0,it.jsx)("div",{className:"bg-blue-500/20 border border-blue-500/50 rounded-lg p-4 mb-4",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"text-blue-400 mr-2",children:"\u2139\ufe0f"}),(0,it.jsxs)("div",{className:"text-blue-200 text-sm",children:[(0,it.jsx)("strong",{children:"Default Credentials:"})," admin/admin or user/user"]})]})}),n&&(0,it.jsx)("div",{className:"bg-red-500/20 border border-red-500/50 rounded-lg p-4 mb-4",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"text-red-400 mr-2",children:"\u26a0\ufe0f"}),(0,it.jsx)("div",{className:"text-red-200 text-sm",children:n})]})}),(0,it.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{htmlFor:"username",className:"block text-sm font-medium text-white mb-2",children:"Username"}),(0,it.jsx)("input",{id:"username",type:"text",value:r,onChange:e=>l(e.target.value),className:"w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter username",required:!0})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-white mb-2",children:"Password"}),(0,it.jsx)("input",{id:"password",type:"password",value:o,onChange:e=>i(e.target.value),className:"w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter password",required:!0})]})]}),(0,it.jsx)("button",{type:"submit",disabled:t,className:"w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:from-gray-600 disabled:to-gray-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105 disabled:scale-100 disabled:cursor-not-allowed shadow-lg",children:t?(0,it.jsxs)("div",{className:"flex items-center justify-center",children:[(0,it.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Signing in..."]}):(0,it.jsxs)("div",{className:"flex items-center justify-center",children:[(0,it.jsx)("div",{className:"mr-2",children:"\ud83d\udd10"}),"Sign In"]})})]}),(0,it.jsxs)("div",{className:"border-t border-white/20 pt-6",children:[(0,it.jsx)("div",{className:"text-center mb-4",children:(0,it.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"\ud83d\ude80 Authentication Features"})}),(0,it.jsxs)("div",{className:"grid grid-cols-1 gap-3 text-sm",children:[(0,it.jsxs)("div",{className:"flex items-center text-green-300",children:[(0,it.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full mr-3"}),"OIDC/OAuth2 Integration"]}),(0,it.jsxs)("div",{className:"flex items-center text-green-300",children:[(0,it.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full mr-3"}),"JWT Token Management"]}),(0,it.jsxs)("div",{className:"flex items-center text-green-300",children:[(0,it.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full mr-3"}),"Role-Based Access Control (RBAC)"]}),(0,it.jsxs)("div",{className:"flex items-center text-green-300",children:[(0,it.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full mr-3"}),"Session Management"]}),(0,it.jsxs)("div",{className:"flex items-center text-green-300",children:[(0,it.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full mr-3"}),"Multi-Factor Authentication"]}),(0,it.jsxs)("div",{className:"flex items-center text-green-300",children:[(0,it.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full mr-3"}),"Audit Logging"]})]})]}),(0,it.jsx)("div",{className:"mt-6 pt-6 border-t border-white/20",children:(0,it.jsxs)("div",{className:"text-center",children:[(0,it.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"\ud83c\udf10 Platform Capabilities"}),(0,it.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-xs text-blue-200",children:[(0,it.jsx)("div",{children:"\ud83d\udd0d Multi-Cloud Discovery"}),(0,it.jsx)("div",{children:"\u26a1 Workflow Automation"}),(0,it.jsx)("div",{children:"\u2638\ufe0f Kubernetes Management"}),(0,it.jsx)("div",{children:"\ud83d\udca5 Chaos Engineering"}),(0,it.jsx)("div",{children:"\ud83e\udd16 ChatOps Integration"}),(0,it.jsx)("div",{children:"\ud83d\udcca Real-time Monitoring"}),(0,it.jsx)("div",{children:"\ud83d\udd78\ufe0f Topology Mapping"}),(0,it.jsx)("div",{children:"\ud83d\udc19 GitHub Integration"}),(0,it.jsx)("div",{children:"\ud83d\udd17 URL Management"}),(0,it.jsx)("div",{children:"\ud83d\udccb Project Management"}),(0,it.jsx)("div",{children:"\ud83d\udcdd Feedback Collection"}),(0,it.jsx)("div",{children:"\ud83d\udee1\ufe0f Security Scanning"})]})]})}),(0,it.jsx)("div",{className:"mt-6 text-center",children:(0,it.jsx)("p",{className:"text-xs text-blue-300",children:"Powered by CAINuro \u2022 Enterprise Grade \u2022 Production Ready"})})]})})};function mt(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(r=0;r<l.length;r++)n=l[r],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}const ht=["title","titleId"];function pt(e,t){let{title:n,titleId:r}=e,l=mt(e,ht);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"}))}const gt=a.forwardRef(pt),xt=["title","titleId"];function yt(e,t){let{title:n,titleId:r}=e,l=mt(e,xt);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))}const vt=a.forwardRef(yt),bt=["title","titleId"];function wt(e,t){let{title:n,titleId:r}=e,l=mt(e,bt);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 12a7.5 7.5 0 0 0 15 0m-15 0a7.5 7.5 0 1 1 15 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077 1.41-.513m14.095-5.13 1.41-.513M5.106 17.785l1.15-.964m11.49-9.642 1.149-.964M7.501 19.795l.75-1.3m7.5-12.99.75-1.3m-6.063 16.658.26-1.477m2.605-14.772.26-1.477m0 17.726-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205 12 12m6.894 5.785-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495"}))}const jt=a.forwardRef(wt),Nt=["title","titleId"];function kt(e,t){let{title:n,titleId:r}=e,l=mt(e,Nt);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418"}))}const St=a.forwardRef(kt),Et=["title","titleId"];function Ct(e,t){let{title:n,titleId:r}=e,l=mt(e,Et);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 18 9 11.25l4.306 4.306a11.95 11.95 0 0 1 5.814-5.518l2.74-1.22m0 0-5.94-2.281m5.94 2.28-2.28 5.941"}))}const Ot=a.forwardRef(Ct),Pt=["title","titleId"];function _t(e,t){let{title:n,titleId:r}=e,l=mt(e,Pt);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}))}const Rt=a.forwardRef(_t),Lt=["title","titleId"];function Tt(e,t){let{title:n,titleId:r}=e,l=mt(e,Lt);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125"}))}const At=a.forwardRef(Tt),Mt=["title","titleId"];function It(e,t){let{title:n,titleId:r}=e,l=mt(e,Mt);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"}))}const Dt=a.forwardRef(It),Ft=["title","titleId"];function zt(e,t){let{title:n,titleId:r}=e,l=mt(e,Ft);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M17.982 18.725A7.488 7.488 0 0 0 12 15.75a7.488 7.488 0 0 0-5.982 2.975m11.963 0a9 9 0 1 0-11.963 0m11.963 0A8.966 8.966 0 0 1 12 21a8.966 8.966 0 0 1-5.982-2.275M15 9.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}const Ut=a.forwardRef(zt),Bt=["title","titleId"];function Wt(e,t){let{title:n,titleId:r}=e,l=mt(e,Bt);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))}const Vt=a.forwardRef(Wt),qt=["title","titleId"];function $t(e,t){let{title:n,titleId:r}=e,l=mt(e,qt);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 9V5.25A2.25 2.25 0 0 0 13.5 3h-6a2.25 2.25 0 0 0-2.25 2.25v13.5A2.25 2.25 0 0 0 7.5 21h6a2.25 2.25 0 0 0 2.25-2.25V15m3 0 3-3m0 0-3-3m3 3H9"}))}const Ht=a.forwardRef($t),Qt=["title","titleId"];function Kt(e,t){let{title:n,titleId:r}=e,l=mt(e,Qt);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"}))}const Jt=a.forwardRef(Kt),Xt=[{name:"Dashboard",href:"/dashboard",icon:gt},{name:"Search",href:"/search",icon:vt},{name:"Discovery Wizard",href:"/discovery",icon:vt},{name:"Workflows",href:"/workflows",icon:jt},{name:"Envoy Config",href:"/envoy",icon:St},{name:"Autoscaler",href:"/autoscaler",icon:Ot},{name:"Audit",href:"/audit",icon:Rt},{name:"Database",href:"/database",icon:At}],Zt=[{name:"Admin Dashboard",href:"/admin",icon:gt},{name:"User Management",href:"/admin/users",icon:Dt},{name:"Settings",href:"/admin/settings",icon:jt}],Gt=[{name:"Profile",href:"/profile",icon:Ut}];function Yt(e){var t,n,r;let{children:l}=e;const[o,i]=(0,a.useState)(!1),[s,c]=(0,a.useState)(!1),u=Se(),{user:d,logout:f}=ut(),m=[...Xt,...null!==d&&void 0!==d&&null!==(t=d.roles)&&void 0!==t&&t.includes("admin")?Zt:[],...Gt];return(0,it.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,it.jsxs)("div",{className:"fixed inset-0 z-50 lg:hidden ".concat(o?"block":"hidden"),children:[(0,it.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75",onClick:()=>i(!1)}),(0,it.jsxs)("div",{className:"fixed inset-y-0 left-0 flex w-64 flex-col bg-gray-800",children:[(0,it.jsxs)("div",{className:"flex h-16 items-center justify-between px-4",children:[(0,it.jsx)("div",{className:"flex items-center",children:(0,it.jsx)("span",{className:"text-xl font-bold text-cyan-400",children:"\ud83d\ude80 CAINuro"})}),(0,it.jsx)("button",{type:"button",className:"text-gray-300 hover:text-white",onClick:()=>i(!1),children:(0,it.jsx)(Vt,{className:"h-6 w-6"})})]}),(0,it.jsx)("nav",{className:"flex-1 space-y-1 px-2 py-4",children:m.map((e=>{const t=u.pathname===e.href;return(0,it.jsxs)(Ye,{to:e.href,className:"group flex items-center px-2 py-2 text-sm font-medium rounded-md ".concat(t?"bg-cyan-600 text-white":"text-gray-300 hover:bg-gray-700 hover:text-white"),onClick:()=>i(!1),children:[(0,it.jsx)(e.icon,{className:"mr-3 h-6 w-6 flex-shrink-0"}),e.name]},e.name)}))})]})]}),(0,it.jsx)("div",{className:"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col",children:(0,it.jsxs)("div",{className:"flex flex-col flex-grow bg-gray-800 pt-5 pb-4 overflow-y-auto",children:[(0,it.jsx)("div",{className:"flex items-center flex-shrink-0 px-4",children:(0,it.jsx)("span",{className:"text-xl font-bold text-cyan-400",children:"\ud83d\ude80 CAINuro Orchestrator"})}),(0,it.jsx)("div",{className:"mt-5 flex-1 flex flex-col",children:(0,it.jsx)("nav",{className:"flex-1 px-2 space-y-1",children:m.map((e=>{const t=u.pathname===e.href;return(0,it.jsxs)(Ye,{to:e.href,className:"group flex items-center px-2 py-2 text-sm font-medium rounded-md ".concat(t?"bg-cyan-600 text-white":"text-gray-300 hover:bg-gray-700 hover:text-white"),children:[(0,it.jsx)(e.icon,{className:"mr-3 h-6 w-6 flex-shrink-0"}),e.name]},e.name)}))})}),(0,it.jsx)("div",{className:"flex-shrink-0 flex border-t border-gray-700 p-4",children:(0,it.jsxs)("div",{className:"flex items-center w-full",children:[(0,it.jsx)(Ut,{className:"h-8 w-8 text-gray-400"}),(0,it.jsxs)("div",{className:"ml-3 flex-1",children:[(0,it.jsx)("p",{className:"text-sm font-medium text-gray-300",children:(null===d||void 0===d?void 0:d.name)||(null===d||void 0===d?void 0:d.email)||"User"}),(0,it.jsx)("p",{className:"text-xs text-gray-400",children:(null===d||void 0===d||null===(n=d.roles)||void 0===n?void 0:n.join(", "))||"Loading..."})]}),(0,it.jsx)("button",{onClick:f,className:"ml-2 p-1 text-gray-400 hover:text-white",title:"Logout",children:(0,it.jsx)(Ht,{className:"h-5 w-5"})})]})})]})}),(0,it.jsxs)("div",{className:"lg:pl-64 flex flex-col flex-1",children:[(0,it.jsxs)("div",{className:"sticky top-0 z-10 flex-shrink-0 flex h-16 bg-gray-800 shadow",children:[(0,it.jsx)("button",{type:"button",className:"px-4 border-r border-gray-700 text-gray-300 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-cyan-500 lg:hidden",onClick:()=>i(!0),children:(0,it.jsx)(Jt,{className:"h-6 w-6"})}),(0,it.jsxs)("div",{className:"flex-1 px-4 flex justify-between",children:[(0,it.jsx)("div",{className:"flex-1 flex",children:(0,it.jsx)("div",{className:"w-full flex md:ml-0",children:(0,it.jsxs)("div",{className:"relative w-full text-gray-400 focus-within:text-gray-600",children:[(0,it.jsx)("div",{className:"absolute inset-y-0 left-0 flex items-center pointer-events-none",children:(0,it.jsx)(vt,{className:"h-5 w-5"})}),(0,it.jsx)("input",{className:"block w-full h-full pl-8 pr-3 py-2 border-transparent text-gray-300 placeholder-gray-400 bg-gray-700 focus:outline-none focus:bg-gray-600 focus:border-transparent focus:ring-0 focus:text-gray-100 sm:text-sm",placeholder:"Search resources...",type:"search"})]})})}),(0,it.jsx)("div",{className:"ml-4 flex items-center md:ml-6",children:(0,it.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,it.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"All Systems Operational"}),(0,it.jsx)("div",{className:"w-3 h-3 bg-green-400 rounded-full animate-pulse"}),(0,it.jsxs)("div",{className:"relative",children:[(0,it.jsxs)("button",{onClick:()=>c(!s),className:"flex items-center space-x-2 text-gray-300 hover:text-white p-2 rounded-md",children:[(0,it.jsx)(Ut,{className:"h-6 w-6"}),(0,it.jsx)("span",{className:"text-sm font-medium",children:(null===d||void 0===d?void 0:d.name)||(null===d||void 0===d?void 0:d.email)||"User"})]}),s&&(0,it.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50",children:[(0,it.jsxs)("div",{className:"px-4 py-2 border-b border-gray-200",children:[(0,it.jsx)("p",{className:"text-sm font-medium text-gray-900",children:(null===d||void 0===d?void 0:d.name)||"User"}),(0,it.jsx)("p",{className:"text-sm text-gray-500",children:null===d||void 0===d?void 0:d.email}),(0,it.jsx)("p",{className:"text-xs text-gray-400",children:null===d||void 0===d||null===(r=d.roles)||void 0===r?void 0:r.join(", ")})]}),(0,it.jsxs)(Ye,{to:"/profile",onClick:()=>c(!1),className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[(0,it.jsx)(Ut,{className:"inline h-4 w-4 mr-2"}),"Profile"]}),(0,it.jsxs)("button",{onClick:()=>{c(!1),f()},className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[(0,it.jsx)(Ht,{className:"inline h-4 w-4 mr-2"}),"Sign out"]})]})]})]})})]})]}),(0,it.jsx)("main",{className:"flex-1",children:(0,it.jsx)("div",{className:"py-6",children:(0,it.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 md:px-8",children:l})})})]})]})}const en={audit:Rt,autoscaler:Ot,db_admin:At,discovery:vt,envoy_control_plane:St,workflow:jt};function tn(){const[e,t]=(0,a.useState)(null),[n,r]=(0,a.useState)(!0);return(0,a.useEffect)((()=>{const e=async()=>{try{const e=await fetch("/health"),n=await e.json();t(n)}catch(e){console.error("Failed to fetch health status:",e)}finally{r(!1)}};e();const n=setInterval(e,3e4);return()=>clearInterval(n)}),[]),n?(0,it.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,it.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-cyan-500"})}):(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h1",{className:"text-3xl font-bold text-white",children:"CAINuro Orchestrator Dashboard"}),(0,it.jsx)("p",{className:"mt-2 text-gray-400",children:"Real-time system status and service monitoring"})]}),(0,it.jsx)("div",{className:"bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,it.jsx)("div",{className:"px-4 py-5 sm:p-6",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center ".concat("healthy"===(null===e||void 0===e?void 0:e.status)?"bg-green-100":"bg-red-100"),children:(0,it.jsx)("div",{className:"w-4 h-4 rounded-full ".concat("healthy"===(null===e||void 0===e?void 0:e.status)?"bg-green-500":"bg-red-500")})})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400 truncate",children:"System Status"}),(0,it.jsxs)("dd",{className:"flex items-baseline",children:[(0,it.jsx)("div",{className:"text-2xl font-semibold text-white",children:"healthy"===(null===e||void 0===e?void 0:e.status)?"All Systems Operational":"System Issues Detected"}),(0,it.jsxs)("div",{className:"ml-2 flex items-baseline text-sm font-semibold text-green-400",children:[null===e||void 0===e?void 0:e.mode," Mode"]})]})]})})]})})}),(0,it.jsx)("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3",children:(null===e||void 0===e?void 0:e.services)&&Object.entries(e.services).map((e=>{let[t,n]=e;const r=en[t]||jt,a="active"===n;return(0,it.jsx)("div",{className:"bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,it.jsx)("div",{className:"p-5",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(r,{className:"h-6 w-6 ".concat(a?"text-green-400":"text-red-400")})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400 truncate",children:t.replace(/_/g," ").replace(/\b\w/g,(e=>e.toUpperCase()))}),(0,it.jsx)("dd",{className:"text-lg font-medium text-white",children:(0,it.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(a?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:n})})]})})]})})},t)}))}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white",children:"Quick Actions"}),(0,it.jsxs)("div",{className:"mt-5 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4",children:[(0,it.jsxs)("button",{className:"relative group bg-gray-700 p-3 focus:outline-none focus:ring-2 focus:ring-cyan-500 rounded-lg hover:bg-gray-600 transition-colors",children:[(0,it.jsx)("div",{children:(0,it.jsx)("span",{className:"rounded-lg inline-flex p-3 bg-cyan-600 text-white",children:(0,it.jsx)(vt,{className:"h-6 w-6"})})}),(0,it.jsxs)("div",{className:"mt-8",children:[(0,it.jsxs)("h3",{className:"text-lg font-medium text-white",children:[(0,it.jsx)("span",{className:"absolute inset-0"}),"Discover Resources"]}),(0,it.jsx)("p",{className:"mt-2 text-sm text-gray-400",children:"Search across AWS, GCP, and Azure"})]})]}),(0,it.jsxs)("button",{className:"relative group bg-gray-700 p-3 focus:outline-none focus:ring-2 focus:ring-cyan-500 rounded-lg hover:bg-gray-600 transition-colors",children:[(0,it.jsx)("div",{children:(0,it.jsx)("span",{className:"rounded-lg inline-flex p-3 bg-purple-600 text-white",children:(0,it.jsx)(jt,{className:"h-6 w-6"})})}),(0,it.jsxs)("div",{className:"mt-8",children:[(0,it.jsxs)("h3",{className:"text-lg font-medium text-white",children:[(0,it.jsx)("span",{className:"absolute inset-0"}),"Run Workflow"]}),(0,it.jsx)("p",{className:"mt-2 text-sm text-gray-400",children:"Execute automation workflows"})]})]}),(0,it.jsxs)("button",{className:"relative group bg-gray-700 p-3 focus:outline-none focus:ring-2 focus:ring-cyan-500 rounded-lg hover:bg-gray-600 transition-colors",children:[(0,it.jsx)("div",{children:(0,it.jsx)("span",{className:"rounded-lg inline-flex p-3 bg-green-600 text-white",children:(0,it.jsx)(St,{className:"h-6 w-6"})})}),(0,it.jsxs)("div",{className:"mt-8",children:[(0,it.jsxs)("h3",{className:"text-lg font-medium text-white",children:[(0,it.jsx)("span",{className:"absolute inset-0"}),"Envoy Config"]}),(0,it.jsx)("p",{className:"mt-2 text-sm text-gray-400",children:"Manage Envoy configurations"})]})]}),(0,it.jsxs)("button",{className:"relative group bg-gray-700 p-3 focus:outline-none focus:ring-2 focus:ring-cyan-500 rounded-lg hover:bg-gray-600 transition-colors",children:[(0,it.jsx)("div",{children:(0,it.jsx)("span",{className:"rounded-lg inline-flex p-3 bg-orange-600 text-white",children:(0,it.jsx)(Rt,{className:"h-6 w-6"})})}),(0,it.jsxs)("div",{className:"mt-8",children:[(0,it.jsxs)("h3",{className:"text-lg font-medium text-white",children:[(0,it.jsx)("span",{className:"absolute inset-0"}),"Audit Logs"]}),(0,it.jsx)("p",{className:"mt-2 text-sm text-gray-400",children:"View tamper-proof audit trail"})]})]})]})]})}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white",children:"System Information"}),(0,it.jsxs)("dl",{className:"mt-5 grid grid-cols-1 gap-5 sm:grid-cols-3",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400",children:"Version"}),(0,it.jsx)("dd",{className:"mt-1 text-sm text-white",children:null===e||void 0===e?void 0:e.version})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400",children:"Mode"}),(0,it.jsx)("dd",{className:"mt-1 text-sm text-white",children:null===e||void 0===e?void 0:e.mode})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400",children:"Services"}),(0,it.jsxs)("dd",{className:"mt-1 text-sm text-white",children:[null!==e&&void 0!==e&&e.services?Object.keys(e.services).length:0," active"]})]})]})]})})]})}function nn(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];throw Error("[Immer] minified error nr: "+e+(n.length?" "+n.map((function(e){return"'"+e+"'"})).join(","):"")+". Find the full error at: https://bit.ly/3cXEKWf")}function rn(e){return!!e&&!!e[Qn]}function an(e){var t;return!!e&&(function(e){if(!e||"object"!=typeof e)return!1;var t=Object.getPrototypeOf(e);if(null===t)return!0;var n=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return n===Object||"function"==typeof n&&Function.toString.call(n)===Kn}(e)||Array.isArray(e)||!!e[Hn]||!!(null===(t=e.constructor)||void 0===t?void 0:t[Hn])||fn(e)||mn(e))}function ln(e,t,n){void 0===n&&(n=!1),0===on(e)?(n?Object.keys:Jn)(e).forEach((function(r){n&&"symbol"==typeof r||t(r,e[r],e)})):e.forEach((function(n,r){return t(r,n,e)}))}function on(e){var t=e[Qn];return t?t.i>3?t.i-4:t.i:Array.isArray(e)?1:fn(e)?2:mn(e)?3:0}function sn(e,t){return 2===on(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function cn(e,t){return 2===on(e)?e.get(t):e[t]}function un(e,t,n){var r=on(e);2===r?e.set(t,n):3===r?e.add(n):e[t]=n}function dn(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t}function fn(e){return Wn&&e instanceof Map}function mn(e){return Vn&&e instanceof Set}function hn(e){return e.o||e.t}function pn(e){if(Array.isArray(e))return Array.prototype.slice.call(e);var t=Xn(e);delete t[Qn];for(var n=Jn(t),r=0;r<n.length;r++){var a=n[r],l=t[a];!1===l.writable&&(l.writable=!0,l.configurable=!0),(l.get||l.set)&&(t[a]={configurable:!0,writable:!0,enumerable:l.enumerable,value:e[a]})}return Object.create(Object.getPrototypeOf(e),t)}function gn(e,t){return void 0===t&&(t=!1),yn(e)||rn(e)||!an(e)||(on(e)>1&&(e.set=e.add=e.clear=e.delete=xn),Object.freeze(e),t&&ln(e,(function(e,t){return gn(t,!0)}),!0)),e}function xn(){nn(2)}function yn(e){return null==e||"object"!=typeof e||Object.isFrozen(e)}function vn(e){var t=Zn[e];return t||nn(18,e),t}function bn(e,t){Zn[e]||(Zn[e]=t)}function wn(){return Un}function jn(e,t){t&&(vn("Patches"),e.u=[],e.s=[],e.v=t)}function Nn(e){kn(e),e.p.forEach(En),e.p=null}function kn(e){e===Un&&(Un=e.l)}function Sn(e){return Un={p:[],l:Un,h:e,m:!0,_:0}}function En(e){var t=e[Qn];0===t.i||1===t.i?t.j():t.g=!0}function Cn(e,t){t._=t.p.length;var n=t.p[0],r=void 0!==e&&e!==n;return t.h.O||vn("ES5").S(t,e,r),r?(n[Qn].P&&(Nn(t),nn(4)),an(e)&&(e=On(t,e),t.l||_n(t,e)),t.u&&vn("Patches").M(n[Qn].t,e,t.u,t.s)):e=On(t,n,[]),Nn(t),t.u&&t.v(t.u,t.s),e!==$n?e:void 0}function On(e,t,n){if(yn(t))return t;var r=t[Qn];if(!r)return ln(t,(function(a,l){return Pn(e,r,t,a,l,n)}),!0),t;if(r.A!==e)return t;if(!r.P)return _n(e,r.t,!0),r.t;if(!r.I){r.I=!0,r.A._--;var a=4===r.i||5===r.i?r.o=pn(r.k):r.o,l=a,o=!1;3===r.i&&(l=new Set(a),a.clear(),o=!0),ln(l,(function(t,l){return Pn(e,r,a,t,l,n,o)})),_n(e,a,!1),n&&e.u&&vn("Patches").N(r,n,e.u,e.s)}return r.o}function Pn(e,t,n,r,a,l,o){if(rn(a)){var i=On(e,a,l&&t&&3!==t.i&&!sn(t.R,r)?l.concat(r):void 0);if(un(n,r,i),!rn(i))return;e.m=!1}else o&&n.add(a);if(an(a)&&!yn(a)){if(!e.h.D&&e._<1)return;On(e,a),t&&t.A.l||_n(e,a)}}function _n(e,t,n){void 0===n&&(n=!1),!e.l&&e.h.D&&e.m&&gn(t,n)}function Rn(e,t){var n=e[Qn];return(n?hn(n):e)[t]}function Ln(e,t){if(t in e)for(var n=Object.getPrototypeOf(e);n;){var r=Object.getOwnPropertyDescriptor(n,t);if(r)return r;n=Object.getPrototypeOf(n)}}function Tn(e){e.P||(e.P=!0,e.l&&Tn(e.l))}function An(e){e.o||(e.o=pn(e.t))}function Mn(e,t,n){var r=fn(t)?vn("MapSet").F(t,n):mn(t)?vn("MapSet").T(t,n):e.O?function(e,t){var n=Array.isArray(e),r={i:n?1:0,A:t?t.A:wn(),P:!1,I:!1,R:{},l:t,t:e,k:null,o:null,j:null,C:!1},a=r,l=Gn;n&&(a=[r],l=Yn);var o=Proxy.revocable(a,l),i=o.revoke,s=o.proxy;return r.k=s,r.j=i,s}(t,n):vn("ES5").J(t,n);return(n?n.A:wn()).p.push(r),r}function In(e){return rn(e)||nn(22,e),function e(t){if(!an(t))return t;var n,r=t[Qn],a=on(t);if(r){if(!r.P&&(r.i<4||!vn("ES5").K(r)))return r.t;r.I=!0,n=Dn(t,a),r.I=!1}else n=Dn(t,a);return ln(n,(function(t,a){r&&cn(r.t,t)===a||un(n,t,e(a))})),3===a?new Set(n):n}(e)}function Dn(e,t){switch(t){case 2:return new Map(e);case 3:return Array.from(e)}return pn(e)}function Fn(){function e(e,t){var n=a[e];return n?n.enumerable=t:a[e]=n={configurable:!0,enumerable:t,get:function(){var t=this[Qn];return Gn.get(t,e)},set:function(t){var n=this[Qn];Gn.set(n,e,t)}},n}function t(e){for(var t=e.length-1;t>=0;t--){var a=e[t][Qn];if(!a.P)switch(a.i){case 5:r(a)&&Tn(a);break;case 4:n(a)&&Tn(a)}}}function n(e){for(var t=e.t,n=e.k,r=Jn(n),a=r.length-1;a>=0;a--){var l=r[a];if(l!==Qn){var o=t[l];if(void 0===o&&!sn(t,l))return!0;var i=n[l],s=i&&i[Qn];if(s?s.t!==o:!dn(i,o))return!0}}var c=!!t[Qn];return r.length!==Jn(t).length+(c?0:1)}function r(e){var t=e.k;if(t.length!==e.t.length)return!0;var n=Object.getOwnPropertyDescriptor(t,t.length-1);if(n&&!n.get)return!0;for(var r=0;r<t.length;r++)if(!t.hasOwnProperty(r))return!0;return!1}var a={};bn("ES5",{J:function(t,n){var r=Array.isArray(t),a=function(t,n){if(t){for(var r=Array(n.length),a=0;a<n.length;a++)Object.defineProperty(r,""+a,e(a,!0));return r}var l=Xn(n);delete l[Qn];for(var o=Jn(l),i=0;i<o.length;i++){var s=o[i];l[s]=e(s,t||!!l[s].enumerable)}return Object.create(Object.getPrototypeOf(n),l)}(r,t),l={i:r?5:4,A:n?n.A:wn(),P:!1,I:!1,R:{},l:n,t:t,k:a,o:null,g:!1,C:!1};return Object.defineProperty(a,Qn,{value:l,writable:!0}),a},S:function(e,n,a){a?rn(n)&&n[Qn].A===e&&t(e.p):(e.u&&function e(t){if(t&&"object"==typeof t){var n=t[Qn];if(n){var a=n.t,l=n.k,o=n.R,i=n.i;if(4===i)ln(l,(function(t){t!==Qn&&(void 0!==a[t]||sn(a,t)?o[t]||e(l[t]):(o[t]=!0,Tn(n)))})),ln(a,(function(e){void 0!==l[e]||sn(l,e)||(o[e]=!1,Tn(n))}));else if(5===i){if(r(n)&&(Tn(n),o.length=!0),l.length<a.length)for(var s=l.length;s<a.length;s++)o[s]=!1;else for(var c=a.length;c<l.length;c++)o[c]=!0;for(var u=Math.min(l.length,a.length),d=0;d<u;d++)l.hasOwnProperty(d)||(o[d]=!0),void 0===o[d]&&e(l[d])}}}}(e.p[0]),t(e.p))},K:function(e){return 4===e.i?n(e):r(e)}})}var zn,Un,Bn="undefined"!=typeof Symbol&&"symbol"==typeof Symbol("x"),Wn="undefined"!=typeof Map,Vn="undefined"!=typeof Set,qn="undefined"!=typeof Proxy&&void 0!==Proxy.revocable&&"undefined"!=typeof Reflect,$n=Bn?Symbol.for("immer-nothing"):((zn={})["immer-nothing"]=!0,zn),Hn=Bn?Symbol.for("immer-draftable"):"__$immer_draftable",Qn=Bn?Symbol.for("immer-state"):"__$immer_state",Kn=("undefined"!=typeof Symbol&&Symbol.iterator,""+Object.prototype.constructor),Jn="undefined"!=typeof Reflect&&Reflect.ownKeys?Reflect.ownKeys:void 0!==Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:Object.getOwnPropertyNames,Xn=Object.getOwnPropertyDescriptors||function(e){var t={};return Jn(e).forEach((function(n){t[n]=Object.getOwnPropertyDescriptor(e,n)})),t},Zn={},Gn={get:function(e,t){if(t===Qn)return e;var n=hn(e);if(!sn(n,t))return function(e,t,n){var r,a=Ln(t,n);return a?"value"in a?a.value:null===(r=a.get)||void 0===r?void 0:r.call(e.k):void 0}(e,n,t);var r=n[t];return e.I||!an(r)?r:r===Rn(e.t,t)?(An(e),e.o[t]=Mn(e.A.h,r,e)):r},has:function(e,t){return t in hn(e)},ownKeys:function(e){return Reflect.ownKeys(hn(e))},set:function(e,t,n){var r=Ln(hn(e),t);if(null==r?void 0:r.set)return r.set.call(e.k,n),!0;if(!e.P){var a=Rn(hn(e),t),l=null==a?void 0:a[Qn];if(l&&l.t===n)return e.o[t]=n,e.R[t]=!1,!0;if(dn(n,a)&&(void 0!==n||sn(e.t,t)))return!0;An(e),Tn(e)}return e.o[t]===n&&(void 0!==n||t in e.o)||Number.isNaN(n)&&Number.isNaN(e.o[t])||(e.o[t]=n,e.R[t]=!0),!0},deleteProperty:function(e,t){return void 0!==Rn(e.t,t)||t in e.t?(e.R[t]=!1,An(e),Tn(e)):delete e.R[t],e.o&&delete e.o[t],!0},getOwnPropertyDescriptor:function(e,t){var n=hn(e),r=Reflect.getOwnPropertyDescriptor(n,t);return r?{writable:!0,configurable:1!==e.i||"length"!==t,enumerable:r.enumerable,value:n[t]}:r},defineProperty:function(){nn(11)},getPrototypeOf:function(e){return Object.getPrototypeOf(e.t)},setPrototypeOf:function(){nn(12)}},Yn={};ln(Gn,(function(e,t){Yn[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}})),Yn.deleteProperty=function(e,t){return Yn.set.call(this,e,t,void 0)},Yn.set=function(e,t,n){return Gn.set.call(this,e[0],t,n,e[0])};var er=function(){function e(e){var t=this;this.O=qn,this.D=!0,this.produce=function(e,n,r){if("function"==typeof e&&"function"!=typeof n){var a=n;n=e;var l=t;return function(e){var t=this;void 0===e&&(e=a);for(var r=arguments.length,o=Array(r>1?r-1:0),i=1;i<r;i++)o[i-1]=arguments[i];return l.produce(e,(function(e){var r;return(r=n).call.apply(r,[t,e].concat(o))}))}}var o;if("function"!=typeof n&&nn(6),void 0!==r&&"function"!=typeof r&&nn(7),an(e)){var i=Sn(t),s=Mn(t,e,void 0),c=!0;try{o=n(s),c=!1}finally{c?Nn(i):kn(i)}return"undefined"!=typeof Promise&&o instanceof Promise?o.then((function(e){return jn(i,r),Cn(e,i)}),(function(e){throw Nn(i),e})):(jn(i,r),Cn(o,i))}if(!e||"object"!=typeof e){if(void 0===(o=n(e))&&(o=e),o===$n&&(o=void 0),t.D&&gn(o,!0),r){var u=[],d=[];vn("Patches").M(e,o,u,d),r(u,d)}return o}nn(21,e)},this.produceWithPatches=function(e,n){if("function"==typeof e)return function(n){for(var r=arguments.length,a=Array(r>1?r-1:0),l=1;l<r;l++)a[l-1]=arguments[l];return t.produceWithPatches(n,(function(t){return e.apply(void 0,[t].concat(a))}))};var r,a,l=t.produce(e,n,(function(e,t){r=e,a=t}));return"undefined"!=typeof Promise&&l instanceof Promise?l.then((function(e){return[e,r,a]})):[l,r,a]},"boolean"==typeof(null==e?void 0:e.useProxies)&&this.setUseProxies(e.useProxies),"boolean"==typeof(null==e?void 0:e.autoFreeze)&&this.setAutoFreeze(e.autoFreeze)}var t=e.prototype;return t.createDraft=function(e){an(e)||nn(8),rn(e)&&(e=In(e));var t=Sn(this),n=Mn(this,e,void 0);return n[Qn].C=!0,kn(t),n},t.finishDraft=function(e,t){var n=(e&&e[Qn]).A;return jn(n,t),Cn(void 0,n)},t.setAutoFreeze=function(e){this.D=e},t.setUseProxies=function(e){e&&!qn&&nn(20),this.O=e},t.applyPatches=function(e,t){var n;for(n=t.length-1;n>=0;n--){var r=t[n];if(0===r.path.length&&"replace"===r.op){e=r.value;break}}n>-1&&(t=t.slice(n+1));var a=vn("Patches").$;return rn(e)?a(e,t):this.produce(e,(function(e){return a(e,t)}))},e}(),tr=new er,nr=tr.produce;tr.produceWithPatches.bind(tr),tr.setAutoFreeze.bind(tr),tr.setUseProxies.bind(tr),tr.applyPatches.bind(tr),tr.createDraft.bind(tr),tr.finishDraft.bind(tr);const rr=nr;function ar(e){return"Minified Redux error #"+e+"; visit https://redux.js.org/Errors?code="+e+" for the full message or use the non-minified dev environment for full errors. "}var lr="function"===typeof Symbol&&Symbol.observable||"@@observable",or=function(){return Math.random().toString(36).substring(7).split("").join(".")},ir={INIT:"@@redux/INIT"+or(),REPLACE:"@@redux/REPLACE"+or(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+or()}};function sr(e){if("object"!==typeof e||null===e)return!1;for(var t=e;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}function cr(e,t,n){var r;if("function"===typeof t&&"function"===typeof n||"function"===typeof n&&"function"===typeof arguments[3])throw new Error(ar(0));if("function"===typeof t&&"undefined"===typeof n&&(n=t,t=void 0),"undefined"!==typeof n){if("function"!==typeof n)throw new Error(ar(1));return n(cr)(e,t)}if("function"!==typeof e)throw new Error(ar(2));var a=e,l=t,o=[],i=o,s=!1;function c(){i===o&&(i=o.slice())}function u(){if(s)throw new Error(ar(3));return l}function d(e){if("function"!==typeof e)throw new Error(ar(4));if(s)throw new Error(ar(5));var t=!0;return c(),i.push(e),function(){if(t){if(s)throw new Error(ar(6));t=!1,c();var n=i.indexOf(e);i.splice(n,1),o=null}}}function f(e){if(!sr(e))throw new Error(ar(7));if("undefined"===typeof e.type)throw new Error(ar(8));if(s)throw new Error(ar(9));try{s=!0,l=a(l,e)}finally{s=!1}for(var t=o=i,n=0;n<t.length;n++){(0,t[n])()}return e}return f({type:ir.INIT}),(r={dispatch:f,subscribe:d,getState:u,replaceReducer:function(e){if("function"!==typeof e)throw new Error(ar(10));a=e,f({type:ir.REPLACE})}})[lr]=function(){var e,t=d;return(e={subscribe:function(e){if("object"!==typeof e||null===e)throw new Error(ar(11));function n(){e.next&&e.next(u())}return n(),{unsubscribe:t(n)}}})[lr]=function(){return this},e},r}function ur(e){for(var t=Object.keys(e),n={},r=0;r<t.length;r++){var a=t[r];0,"function"===typeof e[a]&&(n[a]=e[a])}var l,o=Object.keys(n);try{!function(e){Object.keys(e).forEach((function(t){var n=e[t];if("undefined"===typeof n(void 0,{type:ir.INIT}))throw new Error(ar(12));if("undefined"===typeof n(void 0,{type:ir.PROBE_UNKNOWN_ACTION()}))throw new Error(ar(13))}))}(n)}catch(vc){l=vc}return function(e,t){if(void 0===e&&(e={}),l)throw l;for(var r=!1,a={},i=0;i<o.length;i++){var s=o[i],c=n[s],u=e[s],d=c(u,t);if("undefined"===typeof d){t&&t.type;throw new Error(ar(14))}a[s]=d,r=r||d!==u}return(r=r||o.length!==Object.keys(e).length)?a:e}}function dr(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return 0===t.length?function(e){return e}:1===t.length?t[0]:t.reduce((function(e,t){return function(){return e(t.apply(void 0,arguments))}}))}function fr(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){return function(){var n=e.apply(void 0,arguments),r=function(){throw new Error(ar(15))},a={getState:n.getState,dispatch:function(){return r.apply(void 0,arguments)}},l=t.map((function(e){return e(a)}));return r=dr.apply(void 0,l)(n.dispatch),ot(ot({},n),{},{dispatch:r})}}}function mr(e){return function(t){var n=t.dispatch,r=t.getState;return function(t){return function(a){return"function"===typeof a?a(n,r,e):t(a)}}}}var hr=mr();hr.withExtraArgument=mr;const pr=hr;var gr=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!==typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),xr=function(e,t){var n,r,a,l,o={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return l={next:i(0),throw:i(1),return:i(2)},"function"===typeof Symbol&&(l[Symbol.iterator]=function(){return this}),l;function i(l){return function(i){return function(l){if(n)throw new TypeError("Generator is already executing.");for(;o;)try{if(n=1,r&&(a=2&l[0]?r.return:l[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,l[1])).done)return a;switch(r=0,a&&(l=[2&l[0],a.value]),l[0]){case 0:case 1:a=l;break;case 4:return o.label++,{value:l[1],done:!1};case 5:o.label++,r=l[1],l=[0];continue;case 7:l=o.ops.pop(),o.trys.pop();continue;default:if(!(a=(a=o.trys).length>0&&a[a.length-1])&&(6===l[0]||2===l[0])){o=0;continue}if(3===l[0]&&(!a||l[1]>a[0]&&l[1]<a[3])){o.label=l[1];break}if(6===l[0]&&o.label<a[1]){o.label=a[1],a=l;break}if(a&&o.label<a[2]){o.label=a[2],o.ops.push(l);break}a[2]&&o.ops.pop(),o.trys.pop();continue}l=t.call(e,o)}catch(vc){l=[6,vc],r=0}finally{n=a=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}([l,i])}}},yr=function(e,t){for(var n=0,r=t.length,a=e.length;n<r;n++,a++)e[a]=t[n];return e},vr=Object.defineProperty,br=Object.defineProperties,wr=Object.getOwnPropertyDescriptors,jr=Object.getOwnPropertySymbols,Nr=Object.prototype.hasOwnProperty,kr=Object.prototype.propertyIsEnumerable,Sr=function(e,t,n){return t in e?vr(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n},Er=function(e,t){for(var n in t||(t={}))Nr.call(t,n)&&Sr(e,n,t[n]);if(jr)for(var r=0,a=jr(t);r<a.length;r++){n=a[r];kr.call(t,n)&&Sr(e,n,t[n])}return e},Cr=function(e,t){return br(e,wr(t))},Or=function(e,t,n){return new Promise((function(r,a){var l=function(e){try{i(n.next(e))}catch(vc){a(vc)}},o=function(e){try{i(n.throw(e))}catch(vc){a(vc)}},i=function(e){return e.done?r(e.value):Promise.resolve(e.value).then(l,o)};i((n=n.apply(e,t)).next())}))},Pr="undefined"!==typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!==arguments.length)return"object"===typeof arguments[0]?dr:dr.apply(null,arguments)};"undefined"!==typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;function _r(e){if("object"!==typeof e||null===e)return!1;var t=Object.getPrototypeOf(e);if(null===t)return!0;for(var n=t;null!==Object.getPrototypeOf(n);)n=Object.getPrototypeOf(n);return t===n}function Rr(e,t){function n(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];if(t){var a=t.apply(void 0,n);if(!a)throw new Error("prepareAction did not return an object");return Er(Er({type:e,payload:a.payload},"meta"in a&&{meta:a.meta}),"error"in a&&{error:a.error})}return{type:e,payload:n[0]}}return n.toString=function(){return""+e},n.type=e,n.match=function(t){return t.type===e},n}var Lr=function(e){function t(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var a=e.apply(this,n)||this;return Object.setPrototypeOf(a,t.prototype),a}return gr(t,e),Object.defineProperty(t,Symbol.species,{get:function(){return t},enumerable:!1,configurable:!0}),t.prototype.concat=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e.prototype.concat.apply(this,t)},t.prototype.prepend=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return 1===e.length&&Array.isArray(e[0])?new(t.bind.apply(t,yr([void 0],e[0].concat(this)))):new(t.bind.apply(t,yr([void 0],e.concat(this))))},t}(Array),Tr=function(e){function t(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var a=e.apply(this,n)||this;return Object.setPrototypeOf(a,t.prototype),a}return gr(t,e),Object.defineProperty(t,Symbol.species,{get:function(){return t},enumerable:!1,configurable:!0}),t.prototype.concat=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e.prototype.concat.apply(this,t)},t.prototype.prepend=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return 1===e.length&&Array.isArray(e[0])?new(t.bind.apply(t,yr([void 0],e[0].concat(this)))):new(t.bind.apply(t,yr([void 0],e.concat(this))))},t}(Array);function Ar(e){return an(e)?rr(e,(function(){})):e}function Mr(){return function(e){return function(e){void 0===e&&(e={});var t=e.thunk,n=void 0===t||t,r=(e.immutableCheck,e.serializableCheck,e.actionCreatorCheck,new Lr);n&&(!function(e){return"boolean"===typeof e}(n)?r.push(pr.withExtraArgument(n.extraArgument)):r.push(pr));0;return r}(e)}}function Ir(e){var t,n={},r=[],a={addCase:function(e,t){var r="string"===typeof e?e:e.type;if(!r)throw new Error("`builder.addCase` cannot be called with an empty action type");if(r in n)throw new Error("`builder.addCase` cannot be called with two reducers for the same action type");return n[r]=t,a},addMatcher:function(e,t){return r.push({matcher:e,reducer:t}),a},addDefaultCase:function(e){return t=e,a}};return e(a),[n,r,t]}function Dr(e){var t=e.name;if(!t)throw new Error("`name` is a required option for createSlice");var n,r="function"==typeof e.initialState?e.initialState:Ar(e.initialState),a=e.reducers||{},l=Object.keys(a),o={},i={},s={};function c(){var t="function"===typeof e.extraReducers?Ir(e.extraReducers):[e.extraReducers],n=t[0],a=void 0===n?{}:n,l=t[1],o=void 0===l?[]:l,s=t[2],c=void 0===s?void 0:s,u=Er(Er({},a),i);return function(e,t,n,r){void 0===n&&(n=[]);var a,l="function"===typeof t?Ir(t):[t,n,r],o=l[0],i=l[1],s=l[2];if(function(e){return"function"===typeof e}(e))a=function(){return Ar(e())};else{var c=Ar(e);a=function(){return c}}function u(e,t){void 0===e&&(e=a());var n=yr([o[t.type]],i.filter((function(e){return(0,e.matcher)(t)})).map((function(e){return e.reducer})));return 0===n.filter((function(e){return!!e})).length&&(n=[s]),n.reduce((function(e,n){if(n){var r;if(rn(e))return void 0===(r=n(e,t))?e:r;if(an(e))return rr(e,(function(e){return n(e,t)}));if(void 0===(r=n(e,t))){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return r}return e}),e)}return u.getInitialState=a,u}(r,(function(e){for(var t in u)e.addCase(t,u[t]);for(var n=0,r=o;n<r.length;n++){var a=r[n];e.addMatcher(a.matcher,a.reducer)}c&&e.addDefaultCase(c)}))}return l.forEach((function(e){var n,r,l=a[e],c=t+"/"+e;"reducer"in l?(n=l.reducer,r=l.prepare):n=l,o[e]=n,i[c]=n,s[e]=r?Rr(c,r):Rr(c)})),{name:t,reducer:function(e,t){return n||(n=c()),n(e,t)},actions:s,caseReducers:o,getInitialState:function(){return n||(n=c()),n.getInitialState()}}}var Fr=function(e){void 0===e&&(e=21);for(var t="",n=e;n--;)t+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return t},zr=["name","message","stack","code"],Ur=function(e,t){this.payload=e,this.meta=t},Br=function(e,t){this.payload=e,this.meta=t},Wr=function(e){if("object"===typeof e&&null!==e){for(var t={},n=0,r=zr;n<r.length;n++){var a=r[n];"string"===typeof e[a]&&(t[a]=e[a])}return t}return{message:String(e)}},Vr=function(){function e(e,t,n){var r=Rr(e+"/fulfilled",(function(e,t,n,r){return{payload:e,meta:Cr(Er({},r||{}),{arg:n,requestId:t,requestStatus:"fulfilled"})}})),a=Rr(e+"/pending",(function(e,t,n){return{payload:void 0,meta:Cr(Er({},n||{}),{arg:t,requestId:e,requestStatus:"pending"})}})),l=Rr(e+"/rejected",(function(e,t,r,a,l){return{payload:a,error:(n&&n.serializeError||Wr)(e||"Rejected"),meta:Cr(Er({},l||{}),{arg:r,requestId:t,rejectedWithValue:!!a,requestStatus:"rejected",aborted:"AbortError"===(null==e?void 0:e.name),condition:"ConditionError"===(null==e?void 0:e.name)})}})),o="undefined"!==typeof AbortController?AbortController:function(){function e(){this.signal={aborted:!1,addEventListener:function(){},dispatchEvent:function(){return!1},onabort:function(){},removeEventListener:function(){},reason:void 0,throwIfAborted:function(){}}}return e.prototype.abort=function(){0},e}();return Object.assign((function(e){return function(i,s,c){var u,d=(null==n?void 0:n.idGenerator)?n.idGenerator(e):Fr(),f=new o;function m(e){u=e,f.abort()}var h=function(){return Or(this,null,(function(){var o,h,p,g,x,y;return xr(this,(function(v){switch(v.label){case 0:return v.trys.push([0,4,,5]),g=null==(o=null==n?void 0:n.condition)?void 0:o.call(n,e,{getState:s,extra:c}),null===(b=g)||"object"!==typeof b||"function"!==typeof b.then?[3,2]:[4,g];case 1:g=v.sent(),v.label=2;case 2:if(!1===g||f.signal.aborted)throw{name:"ConditionError",message:"Aborted due to condition callback returning false."};return x=new Promise((function(e,t){return f.signal.addEventListener("abort",(function(){return t({name:"AbortError",message:u||"Aborted"})}))})),i(a(d,e,null==(h=null==n?void 0:n.getPendingMeta)?void 0:h.call(n,{requestId:d,arg:e},{getState:s,extra:c}))),[4,Promise.race([x,Promise.resolve(t(e,{dispatch:i,getState:s,extra:c,requestId:d,signal:f.signal,abort:m,rejectWithValue:function(e,t){return new Ur(e,t)},fulfillWithValue:function(e,t){return new Br(e,t)}})).then((function(t){if(t instanceof Ur)throw t;return t instanceof Br?r(t.payload,d,e,t.meta):r(t,d,e)}))])];case 3:return p=v.sent(),[3,5];case 4:return y=v.sent(),p=y instanceof Ur?l(null,d,e,y.payload,y.meta):l(y,d,e),[3,5];case 5:return n&&!n.dispatchConditionRejection&&l.match(p)&&p.meta.condition||i(p),[2,p]}var b}))}))}();return Object.assign(h,{abort:m,requestId:d,arg:e,unwrap:function(){return h.then(qr)}})}}),{pending:a,rejected:l,fulfilled:r,typePrefix:e})}return e.withTypes=function(){return e},e}();function qr(e){if(e.meta&&e.meta.rejectedWithValue)throw e.payload;if(e.error)throw e.error;return e.payload}Object.assign;var $r="listenerMiddleware";Rr($r+"/add"),Rr($r+"/removeAll"),Rr($r+"/remove");"function"===typeof queueMicrotask&&queueMicrotask.bind("undefined"!==typeof window?window:"undefined"!==typeof n.g?n.g:globalThis);var Hr,Qr=function(e){return function(t){setTimeout(t,e)}};"undefined"!==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:Qr(10);function Kr(e,t){return function(){return e.apply(t,arguments)}}Fn();const{toString:Jr}=Object.prototype,{getPrototypeOf:Xr}=Object,{iterator:Zr,toStringTag:Gr}=Symbol,Yr=(ea=Object.create(null),e=>{const t=Jr.call(e);return ea[t]||(ea[t]=t.slice(8,-1).toLowerCase())});var ea;const ta=e=>(e=e.toLowerCase(),t=>Yr(t)===e),na=e=>t=>typeof t===e,{isArray:ra}=Array,aa=na("undefined");const la=ta("ArrayBuffer");const oa=na("string"),ia=na("function"),sa=na("number"),ca=e=>null!==e&&"object"===typeof e,ua=e=>{if("object"!==Yr(e))return!1;const t=Xr(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Gr in e)&&!(Zr in e)},da=ta("Date"),fa=ta("File"),ma=ta("Blob"),ha=ta("FileList"),pa=ta("URLSearchParams"),[ga,xa,ya,va]=["ReadableStream","Request","Response","Headers"].map(ta);function ba(e,t){let n,r,{allOwnKeys:a=!1}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(null!==e&&"undefined"!==typeof e)if("object"!==typeof e&&(e=[e]),ra(e))for(n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else{const r=a?Object.getOwnPropertyNames(e):Object.keys(e),l=r.length;let o;for(n=0;n<l;n++)o=r[n],t.call(null,e[o],o,e)}}function wa(e,t){t=t.toLowerCase();const n=Object.keys(e);let r,a=n.length;for(;a-- >0;)if(r=n[a],t===r.toLowerCase())return r;return null}const ja="undefined"!==typeof globalThis?globalThis:"undefined"!==typeof self?self:"undefined"!==typeof window?window:global,Na=e=>!aa(e)&&e!==ja;const ka=(Sa="undefined"!==typeof Uint8Array&&Xr(Uint8Array),e=>Sa&&e instanceof Sa);var Sa;const Ea=ta("HTMLFormElement"),Ca=(e=>{let{hasOwnProperty:t}=e;return(e,n)=>t.call(e,n)})(Object.prototype),Oa=ta("RegExp"),Pa=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};ba(n,((n,a)=>{let l;!1!==(l=t(n,a,e))&&(r[a]=l||n)})),Object.defineProperties(e,r)};const _a=ta("AsyncFunction"),Ra=((e,t)=>{return e?setImmediate:t?(n="axios@".concat(Math.random()),r=[],ja.addEventListener("message",(e=>{let{source:t,data:a}=e;t===ja&&a===n&&r.length&&r.shift()()}),!1),e=>{r.push(e),ja.postMessage(n,"*")}):e=>setTimeout(e);var n,r})("function"===typeof setImmediate,ia(ja.postMessage)),La="undefined"!==typeof queueMicrotask?queueMicrotask.bind(ja):"undefined"!==typeof process&&process.nextTick||Ra,Ta={isArray:ra,isArrayBuffer:la,isBuffer:function(e){return null!==e&&!aa(e)&&null!==e.constructor&&!aa(e.constructor)&&ia(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"===typeof FormData&&e instanceof FormData||ia(e.append)&&("formdata"===(t=Yr(e))||"object"===t&&ia(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&la(e.buffer),t},isString:oa,isNumber:sa,isBoolean:e=>!0===e||!1===e,isObject:ca,isPlainObject:ua,isReadableStream:ga,isRequest:xa,isResponse:ya,isHeaders:va,isUndefined:aa,isDate:da,isFile:fa,isBlob:ma,isRegExp:Oa,isFunction:ia,isStream:e=>ca(e)&&ia(e.pipe),isURLSearchParams:pa,isTypedArray:ka,isFileList:ha,forEach:ba,merge:function e(){const{caseless:t}=Na(this)&&this||{},n={},r=(r,a)=>{const l=t&&wa(n,a)||a;ua(n[l])&&ua(r)?n[l]=e(n[l],r):ua(r)?n[l]=e({},r):ra(r)?n[l]=r.slice():n[l]=r};for(let a=0,l=arguments.length;a<l;a++)arguments[a]&&ba(arguments[a],r);return n},extend:function(e,t,n){let{allOwnKeys:r}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return ba(t,((t,r)=>{n&&ia(t)?e[r]=Kr(t,n):e[r]=t}),{allOwnKeys:r}),e},trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,r)=>{let a,l,o;const i={};if(t=t||{},null==e)return t;do{for(a=Object.getOwnPropertyNames(e),l=a.length;l-- >0;)o=a[l],r&&!r(o,e,t)||i[o]||(t[o]=e[o],i[o]=!0);e=!1!==n&&Xr(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:Yr,kindOfTest:ta,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return-1!==r&&r===n},toArray:e=>{if(!e)return null;if(ra(e))return e;let t=e.length;if(!sa(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{const n=(e&&e[Zr]).call(e);let r;for(;(r=n.next())&&!r.done;){const n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let n;const r=[];for(;null!==(n=e.exec(t));)r.push(n);return r},isHTMLForm:Ea,hasOwnProperty:Ca,hasOwnProp:Ca,reduceDescriptors:Pa,freezeMethods:e=>{Pa(e,((t,n)=>{if(ia(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=e[n];ia(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))}))},toObjectSet:(e,t)=>{const n={},r=e=>{e.forEach((e=>{n[e]=!0}))};return ra(e)?r(e):r(String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function(e,t,n){return t.toUpperCase()+n})),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:wa,global:ja,isContextDefined:Na,isSpecCompliantForm:function(e){return!!(e&&ia(e.append)&&"FormData"===e[Gr]&&e[Zr])},toJSONObject:e=>{const t=new Array(10),n=(e,r)=>{if(ca(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[r]=e;const a=ra(e)?[]:{};return ba(e,((e,t)=>{const l=n(e,r+1);!aa(l)&&(a[t]=l)})),t[r]=void 0,a}}return e};return n(e,0)},isAsyncFn:_a,isThenable:e=>e&&(ca(e)||ia(e))&&ia(e.then)&&ia(e.catch),setImmediate:Ra,asap:La,isIterable:e=>null!=e&&ia(e[Zr])};function Aa(e,t,n,r,a){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),a&&(this.response=a,this.status=a.status?a.status:null)}Ta.inherits(Aa,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:Ta.toJSONObject(this.config),code:this.code,status:this.status}}});const Ma=Aa.prototype,Ia={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((e=>{Ia[e]={value:e}})),Object.defineProperties(Aa,Ia),Object.defineProperty(Ma,"isAxiosError",{value:!0}),Aa.from=(e,t,n,r,a,l)=>{const o=Object.create(Ma);return Ta.toFlatObject(e,o,(function(e){return e!==Error.prototype}),(e=>"isAxiosError"!==e)),Aa.call(o,e.message,t,n,r,a),o.cause=e,o.name=e.name,l&&Object.assign(o,l),o};const Da=Aa;function Fa(e){return Ta.isPlainObject(e)||Ta.isArray(e)}function za(e){return Ta.endsWith(e,"[]")?e.slice(0,-2):e}function Ua(e,t,n){return e?e.concat(t).map((function(e,t){return e=za(e),!n&&t?"["+e+"]":e})).join(n?".":""):t}const Ba=Ta.toFlatObject(Ta,{},null,(function(e){return/^is[A-Z]/.test(e)}));const Wa=function(e,t,n){if(!Ta.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const r=(n=Ta.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(e,t){return!Ta.isUndefined(t[e])}))).metaTokens,a=n.visitor||c,l=n.dots,o=n.indexes,i=(n.Blob||"undefined"!==typeof Blob&&Blob)&&Ta.isSpecCompliantForm(t);if(!Ta.isFunction(a))throw new TypeError("visitor must be a function");function s(e){if(null===e)return"";if(Ta.isDate(e))return e.toISOString();if(!i&&Ta.isBlob(e))throw new Da("Blob is not supported. Use a Buffer instead.");return Ta.isArrayBuffer(e)||Ta.isTypedArray(e)?i&&"function"===typeof Blob?new Blob([e]):Buffer.from(e):e}function c(e,n,a){let i=e;if(e&&!a&&"object"===typeof e)if(Ta.endsWith(n,"{}"))n=r?n:n.slice(0,-2),e=JSON.stringify(e);else if(Ta.isArray(e)&&function(e){return Ta.isArray(e)&&!e.some(Fa)}(e)||(Ta.isFileList(e)||Ta.endsWith(n,"[]"))&&(i=Ta.toArray(e)))return n=za(n),i.forEach((function(e,r){!Ta.isUndefined(e)&&null!==e&&t.append(!0===o?Ua([n],r,l):null===o?n:n+"[]",s(e))})),!1;return!!Fa(e)||(t.append(Ua(a,n,l),s(e)),!1)}const u=[],d=Object.assign(Ba,{defaultVisitor:c,convertValue:s,isVisitable:Fa});if(!Ta.isObject(e))throw new TypeError("data must be an object");return function e(n,r){if(!Ta.isUndefined(n)){if(-1!==u.indexOf(n))throw Error("Circular reference detected in "+r.join("."));u.push(n),Ta.forEach(n,(function(n,l){!0===(!(Ta.isUndefined(n)||null===n)&&a.call(t,n,Ta.isString(l)?l.trim():l,r,d))&&e(n,r?r.concat(l):[l])})),u.pop()}}(e),t};function Va(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,(function(e){return t[e]}))}function qa(e,t){this._pairs=[],e&&Wa(e,this,t)}const $a=qa.prototype;$a.append=function(e,t){this._pairs.push([e,t])},$a.toString=function(e){const t=e?function(t){return e.call(this,t,Va)}:Va;return this._pairs.map((function(e){return t(e[0])+"="+t(e[1])}),"").join("&")};const Ha=qa;function Qa(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Ka(e,t,n){if(!t)return e;const r=n&&n.encode||Qa;Ta.isFunction(n)&&(n={serialize:n});const a=n&&n.serialize;let l;if(l=a?a(t,n):Ta.isURLSearchParams(t)?t.toString():new Ha(t,n).toString(r),l){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+l}return e}const Ja=class{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){Ta.forEach(this.handlers,(function(t){null!==t&&e(t)}))}},Xa={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Za={isBrowser:!0,classes:{URLSearchParams:"undefined"!==typeof URLSearchParams?URLSearchParams:Ha,FormData:"undefined"!==typeof FormData?FormData:null,Blob:"undefined"!==typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},Ga="undefined"!==typeof window&&"undefined"!==typeof document,Ya="object"===typeof navigator&&navigator||void 0,el=Ga&&(!Ya||["ReactNative","NativeScript","NS"].indexOf(Ya.product)<0),tl="undefined"!==typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"===typeof self.importScripts,nl=Ga&&window.location.href||"http://localhost",rl=ot(ot({},r),Za);const al=function(e){function t(e,n,r,a){let l=e[a++];if("__proto__"===l)return!0;const o=Number.isFinite(+l),i=a>=e.length;if(l=!l&&Ta.isArray(r)?r.length:l,i)return Ta.hasOwnProp(r,l)?r[l]=[r[l],n]:r[l]=n,!o;r[l]&&Ta.isObject(r[l])||(r[l]=[]);return t(e,n,r[l],a)&&Ta.isArray(r[l])&&(r[l]=function(e){const t={},n=Object.keys(e);let r;const a=n.length;let l;for(r=0;r<a;r++)l=n[r],t[l]=e[l];return t}(r[l])),!o}if(Ta.isFormData(e)&&Ta.isFunction(e.entries)){const n={};return Ta.forEachEntry(e,((e,r)=>{t(function(e){return Ta.matchAll(/\w+|\[(\w*)]/g,e).map((e=>"[]"===e[0]?"":e[1]||e[0]))}(e),r,n,0)})),n}return null};const ll={transitional:Xa,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const n=t.getContentType()||"",r=n.indexOf("application/json")>-1,a=Ta.isObject(e);a&&Ta.isHTMLForm(e)&&(e=new FormData(e));if(Ta.isFormData(e))return r?JSON.stringify(al(e)):e;if(Ta.isArrayBuffer(e)||Ta.isBuffer(e)||Ta.isStream(e)||Ta.isFile(e)||Ta.isBlob(e)||Ta.isReadableStream(e))return e;if(Ta.isArrayBufferView(e))return e.buffer;if(Ta.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let l;if(a){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return Wa(e,new rl.classes.URLSearchParams,Object.assign({visitor:function(e,t,n,r){return rl.isNode&&Ta.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((l=Ta.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return Wa(l?{"files[]":e}:e,t&&new t,this.formSerializer)}}return a||r?(t.setContentType("application/json",!1),function(e,t,n){if(Ta.isString(e))try{return(t||JSON.parse)(e),Ta.trim(e)}catch(vc){if("SyntaxError"!==vc.name)throw vc}return(n||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||ll.transitional,n=t&&t.forcedJSONParsing,r="json"===this.responseType;if(Ta.isResponse(e)||Ta.isReadableStream(e))return e;if(e&&Ta.isString(e)&&(n&&!this.responseType||r)){const n=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(vc){if(n){if("SyntaxError"===vc.name)throw Da.from(vc,Da.ERR_BAD_RESPONSE,this,null,this.response);throw vc}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:rl.classes.FormData,Blob:rl.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};Ta.forEach(["delete","get","head","post","put","patch"],(e=>{ll.headers[e]={}}));const ol=ll,il=Ta.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),sl=Symbol("internals");function cl(e){return e&&String(e).trim().toLowerCase()}function ul(e){return!1===e||null==e?e:Ta.isArray(e)?e.map(ul):String(e)}function dl(e,t,n,r,a){return Ta.isFunction(r)?r.call(this,t,n):(a&&(t=n),Ta.isString(t)?Ta.isString(r)?-1!==t.indexOf(r):Ta.isRegExp(r)?r.test(t):void 0:void 0)}class fl{constructor(e){e&&this.set(e)}set(e,t,n){const r=this;function a(e,t,n){const a=cl(t);if(!a)throw new Error("header name must be a non-empty string");const l=Ta.findKey(r,a);(!l||void 0===r[l]||!0===n||void 0===n&&!1!==r[l])&&(r[l||t]=ul(e))}const l=(e,t)=>Ta.forEach(e,((e,n)=>a(e,n,t)));if(Ta.isPlainObject(e)||e instanceof this.constructor)l(e,t);else if(Ta.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim()))l((e=>{const t={};let n,r,a;return e&&e.split("\n").forEach((function(e){a=e.indexOf(":"),n=e.substring(0,a).trim().toLowerCase(),r=e.substring(a+1).trim(),!n||t[n]&&il[n]||("set-cookie"===n?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)})),t})(e),t);else if(Ta.isObject(e)&&Ta.isIterable(e)){let n,r,a={};for(const t of e){if(!Ta.isArray(t))throw TypeError("Object iterator must return a key-value pair");a[r=t[0]]=(n=a[r])?Ta.isArray(n)?[...n,t[1]]:[n,t[1]]:t[1]}l(a,t)}else null!=e&&a(t,e,n);return this}get(e,t){if(e=cl(e)){const n=Ta.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}(e);if(Ta.isFunction(t))return t.call(this,e,n);if(Ta.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=cl(e)){const n=Ta.findKey(this,e);return!(!n||void 0===this[n]||t&&!dl(0,this[n],n,t))}return!1}delete(e,t){const n=this;let r=!1;function a(e){if(e=cl(e)){const a=Ta.findKey(n,e);!a||t&&!dl(0,n[a],a,t)||(delete n[a],r=!0)}}return Ta.isArray(e)?e.forEach(a):a(e),r}clear(e){const t=Object.keys(this);let n=t.length,r=!1;for(;n--;){const a=t[n];e&&!dl(0,this[a],a,e,!0)||(delete this[a],r=!0)}return r}normalize(e){const t=this,n={};return Ta.forEach(this,((r,a)=>{const l=Ta.findKey(n,a);if(l)return t[l]=ul(r),void delete t[a];const o=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((e,t,n)=>t.toUpperCase()+n))}(a):String(a).trim();o!==a&&delete t[a],t[o]=ul(r),n[o]=!0})),this}concat(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.constructor.concat(this,...t)}toJSON(e){const t=Object.create(null);return Ta.forEach(this,((n,r)=>{null!=n&&!1!==n&&(t[r]=e&&Ta.isArray(n)?n.join(", "):n)})),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((e=>{let[t,n]=e;return t+": "+n})).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e){const t=new this(e);for(var n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];return r.forEach((e=>t.set(e))),t}static accessor(e){const t=(this[sl]=this[sl]={accessors:{}}).accessors,n=this.prototype;function r(e){const r=cl(e);t[r]||(!function(e,t){const n=Ta.toCamelCase(" "+t);["get","set","has"].forEach((r=>{Object.defineProperty(e,r+n,{value:function(e,n,a){return this[r].call(this,t,e,n,a)},configurable:!0})}))}(n,e),t[r]=!0)}return Ta.isArray(e)?e.forEach(r):r(e),this}}fl.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),Ta.reduceDescriptors(fl.prototype,((e,t)=>{let{value:n}=e,r=t[0].toUpperCase()+t.slice(1);return{get:()=>n,set(e){this[r]=e}}})),Ta.freezeMethods(fl);const ml=fl;function hl(e,t){const n=this||ol,r=t||n,a=ml.from(r.headers);let l=r.data;return Ta.forEach(e,(function(e){l=e.call(n,l,a.normalize(),t?t.status:void 0)})),a.normalize(),l}function pl(e){return!(!e||!e.__CANCEL__)}function gl(e,t,n){Da.call(this,null==e?"canceled":e,Da.ERR_CANCELED,t,n),this.name="CanceledError"}Ta.inherits(gl,Da,{__CANCEL__:!0});const xl=gl;function yl(e,t,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new Da("Request failed with status code "+n.status,[Da.ERR_BAD_REQUEST,Da.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}const vl=function(e,t){e=e||10;const n=new Array(e),r=new Array(e);let a,l=0,o=0;return t=void 0!==t?t:1e3,function(i){const s=Date.now(),c=r[o];a||(a=s),n[l]=i,r[l]=s;let u=o,d=0;for(;u!==l;)d+=n[u++],u%=e;if(l=(l+1)%e,l===o&&(o=(o+1)%e),s-a<t)return;const f=c&&s-c;return f?Math.round(1e3*d/f):void 0}};const bl=function(e,t){let n,r,a=0,l=1e3/t;const o=function(t){let l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Date.now();a=l,n=null,r&&(clearTimeout(r),r=null),e.apply(null,t)};return[function(){const e=Date.now(),t=e-a;for(var i=arguments.length,s=new Array(i),c=0;c<i;c++)s[c]=arguments[c];t>=l?o(s,e):(n=s,r||(r=setTimeout((()=>{r=null,o(n)}),l-t)))},()=>n&&o(n)]},wl=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3,r=0;const a=vl(50,250);return bl((n=>{const l=n.loaded,o=n.lengthComputable?n.total:void 0,i=l-r,s=a(i);r=l;e({loaded:l,total:o,progress:o?l/o:void 0,bytes:i,rate:s||void 0,estimated:s&&o&&l<=o?(o-l)/s:void 0,event:n,lengthComputable:null!=o,[t?"download":"upload"]:!0})}),n)},jl=(e,t)=>{const n=null!=e;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},Nl=e=>function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return Ta.asap((()=>e(...n)))},kl=rl.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,rl.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(rl.origin),rl.navigator&&/(msie|trident)/i.test(rl.navigator.userAgent)):()=>!0,Sl=rl.hasStandardBrowserEnv?{write(e,t,n,r,a,l){const o=[e+"="+encodeURIComponent(t)];Ta.isNumber(n)&&o.push("expires="+new Date(n).toGMTString()),Ta.isString(r)&&o.push("path="+r),Ta.isString(a)&&o.push("domain="+a),!0===l&&o.push("secure"),document.cookie=o.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function El(e,t,n){let r=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t);return e&&(r||0==n)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const Cl=e=>e instanceof ml?ot({},e):e;function Ol(e,t){t=t||{};const n={};function r(e,t,n,r){return Ta.isPlainObject(e)&&Ta.isPlainObject(t)?Ta.merge.call({caseless:r},e,t):Ta.isPlainObject(t)?Ta.merge({},t):Ta.isArray(t)?t.slice():t}function a(e,t,n,a){return Ta.isUndefined(t)?Ta.isUndefined(e)?void 0:r(void 0,e,0,a):r(e,t,0,a)}function l(e,t){if(!Ta.isUndefined(t))return r(void 0,t)}function o(e,t){return Ta.isUndefined(t)?Ta.isUndefined(e)?void 0:r(void 0,e):r(void 0,t)}function i(n,a,l){return l in t?r(n,a):l in e?r(void 0,n):void 0}const s={url:l,method:l,data:l,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,withXSRFToken:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:i,headers:(e,t,n)=>a(Cl(e),Cl(t),0,!0)};return Ta.forEach(Object.keys(Object.assign({},e,t)),(function(r){const l=s[r]||a,o=l(e[r],t[r],r);Ta.isUndefined(o)&&l!==i||(n[r]=o)})),n}const Pl=e=>{const t=Ol({},e);let n,{data:r,withXSRFToken:a,xsrfHeaderName:l,xsrfCookieName:o,headers:i,auth:s}=t;if(t.headers=i=ml.from(i),t.url=Ka(El(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),s&&i.set("Authorization","Basic "+btoa((s.username||"")+":"+(s.password?unescape(encodeURIComponent(s.password)):""))),Ta.isFormData(r))if(rl.hasStandardBrowserEnv||rl.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if(!1!==(n=i.getContentType())){const[e,...t]=n?n.split(";").map((e=>e.trim())).filter(Boolean):[];i.setContentType([e||"multipart/form-data",...t].join("; "))}if(rl.hasStandardBrowserEnv&&(a&&Ta.isFunction(a)&&(a=a(t)),a||!1!==a&&kl(t.url))){const e=l&&o&&Sl.read(o);e&&i.set(l,e)}return t},_l="undefined"!==typeof XMLHttpRequest&&function(e){return new Promise((function(t,n){const r=Pl(e);let a=r.data;const l=ml.from(r.headers).normalize();let o,i,s,c,u,{responseType:d,onUploadProgress:f,onDownloadProgress:m}=r;function h(){c&&c(),u&&u(),r.cancelToken&&r.cancelToken.unsubscribe(o),r.signal&&r.signal.removeEventListener("abort",o)}let p=new XMLHttpRequest;function g(){if(!p)return;const r=ml.from("getAllResponseHeaders"in p&&p.getAllResponseHeaders());yl((function(e){t(e),h()}),(function(e){n(e),h()}),{data:d&&"text"!==d&&"json"!==d?p.response:p.responseText,status:p.status,statusText:p.statusText,headers:r,config:e,request:p}),p=null}p.open(r.method.toUpperCase(),r.url,!0),p.timeout=r.timeout,"onloadend"in p?p.onloadend=g:p.onreadystatechange=function(){p&&4===p.readyState&&(0!==p.status||p.responseURL&&0===p.responseURL.indexOf("file:"))&&setTimeout(g)},p.onabort=function(){p&&(n(new Da("Request aborted",Da.ECONNABORTED,e,p)),p=null)},p.onerror=function(){n(new Da("Network Error",Da.ERR_NETWORK,e,p)),p=null},p.ontimeout=function(){let t=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const a=r.transitional||Xa;r.timeoutErrorMessage&&(t=r.timeoutErrorMessage),n(new Da(t,a.clarifyTimeoutError?Da.ETIMEDOUT:Da.ECONNABORTED,e,p)),p=null},void 0===a&&l.setContentType(null),"setRequestHeader"in p&&Ta.forEach(l.toJSON(),(function(e,t){p.setRequestHeader(t,e)})),Ta.isUndefined(r.withCredentials)||(p.withCredentials=!!r.withCredentials),d&&"json"!==d&&(p.responseType=r.responseType),m&&([s,u]=wl(m,!0),p.addEventListener("progress",s)),f&&p.upload&&([i,c]=wl(f),p.upload.addEventListener("progress",i),p.upload.addEventListener("loadend",c)),(r.cancelToken||r.signal)&&(o=t=>{p&&(n(!t||t.type?new xl(null,e,p):t),p.abort(),p=null)},r.cancelToken&&r.cancelToken.subscribe(o),r.signal&&(r.signal.aborted?o():r.signal.addEventListener("abort",o)));const x=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(r.url);x&&-1===rl.protocols.indexOf(x)?n(new Da("Unsupported protocol "+x+":",Da.ERR_BAD_REQUEST,e)):p.send(a||null)}))},Rl=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let n,r=new AbortController;const a=function(e){if(!n){n=!0,o();const t=e instanceof Error?e:this.reason;r.abort(t instanceof Da?t:new xl(t instanceof Error?t.message:t))}};let l=t&&setTimeout((()=>{l=null,a(new Da("timeout ".concat(t," of ms exceeded"),Da.ETIMEDOUT))}),t);const o=()=>{e&&(l&&clearTimeout(l),l=null,e.forEach((e=>{e.unsubscribe?e.unsubscribe(a):e.removeEventListener("abort",a)})),e=null)};e.forEach((e=>e.addEventListener("abort",a)));const{signal:i}=r;return i.unsubscribe=()=>Ta.asap(o),i}};function Ll(e,t){this.v=e,this.k=t}function Tl(e){return function(){return new Al(e.apply(this,arguments))}}function Al(e){var t,n;function r(t,n){try{var l=e[t](n),o=l.value,i=o instanceof Ll;Promise.resolve(i?o.v:o).then((function(n){if(i){var s="return"===t?"return":"next";if(!o.k||n.done)return r(s,n);n=e[s](n).value}a(l.done?"return":"normal",n)}),(function(e){r("throw",e)}))}catch(e){a("throw",e)}}function a(e,a){switch(e){case"return":t.resolve({value:a,done:!0});break;case"throw":t.reject(a);break;default:t.resolve({value:a,done:!1})}(t=t.next)?r(t.key,t.arg):n=null}this._invoke=function(e,a){return new Promise((function(l,o){var i={key:e,arg:a,resolve:l,reject:o,next:null};n?n=n.next=i:(t=n=i,r(e,a))}))},"function"!=typeof e.return&&(this.return=void 0)}function Ml(e){return new Ll(e,0)}function Il(e){var t={},n=!1;function r(t,r){return n=!0,r=new Promise((function(n){n(e[t](r))})),{done:!1,value:new Ll(r,1)}}return t["undefined"!=typeof Symbol&&Symbol.iterator||"@@iterator"]=function(){return this},t.next=function(e){return n?(n=!1,e):r("next",e)},"function"==typeof e.throw&&(t.throw=function(e){if(n)throw n=!1,e;return r("throw",e)}),"function"==typeof e.return&&(t.return=function(e){return n?(n=!1,e):r("return",e)}),t}function Dl(e){var t,n,r,a=2;for("undefined"!=typeof Symbol&&(n=Symbol.asyncIterator,r=Symbol.iterator);a--;){if(n&&null!=(t=e[n]))return t.call(e);if(r&&null!=(t=e[r]))return new Fl(t.call(e));n="@@asyncIterator",r="@@iterator"}throw new TypeError("Object is not async iterable")}function Fl(e){function t(e){if(Object(e)!==e)return Promise.reject(new TypeError(e+" is not an object."));var t=e.done;return Promise.resolve(e.value).then((function(e){return{value:e,done:t}}))}return Fl=function(e){this.s=e,this.n=e.next},Fl.prototype={s:null,n:null,next:function(){return t(this.n.apply(this.s,arguments))},return:function(e){var n=this.s.return;return void 0===n?Promise.resolve({value:e,done:!0}):t(n.apply(this.s,arguments))},throw:function(e){var n=this.s.return;return void 0===n?Promise.reject(e):t(n.apply(this.s,arguments))}},new Fl(e)}Al.prototype["function"==typeof Symbol&&Symbol.asyncIterator||"@@asyncIterator"]=function(){return this},Al.prototype.next=function(e){return this._invoke("next",e)},Al.prototype.throw=function(e){return this._invoke("throw",e)},Al.prototype.return=function(e){return this._invoke("return",e)};const zl=function*(e,t){let n=e.byteLength;if(!t||n<t)return void(yield e);let r,a=0;for(;a<n;)r=a+t,yield e.slice(a,r),a=r},Ul=function(){var e=Tl((function*(e,t){var n,r=!1,a=!1;try{for(var l,o=Dl(Bl(e));r=!(l=yield Ml(o.next())).done;r=!1){const e=l.value;yield*Il(Dl(zl(e,t)))}}catch(i){a=!0,n=i}finally{try{r&&null!=o.return&&(yield Ml(o.return()))}finally{if(a)throw n}}}));return function(t,n){return e.apply(this,arguments)}}(),Bl=function(){var e=Tl((function*(e){if(e[Symbol.asyncIterator])return void(yield*Il(Dl(e)));const t=e.getReader();try{for(;;){const{done:e,value:n}=yield Ml(t.read());if(e)break;yield n}}finally{yield Ml(t.cancel())}}));return function(t){return e.apply(this,arguments)}}(),Wl=(e,t,n,r)=>{const a=Ul(e,t);let l,o=0,i=e=>{l||(l=!0,r&&r(e))};return new ReadableStream({async pull(e){try{const{done:t,value:r}=await a.next();if(t)return i(),void e.close();let l=r.byteLength;if(n){let e=o+=l;n(e)}e.enqueue(new Uint8Array(r))}catch(t){throw i(t),t}},cancel:e=>(i(e),a.return())},{highWaterMark:2})},Vl="function"===typeof fetch&&"function"===typeof Request&&"function"===typeof Response,ql=Vl&&"function"===typeof ReadableStream,$l=Vl&&("function"===typeof TextEncoder?(Hl=new TextEncoder,e=>Hl.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer()));var Hl;const Ql=function(e){try{for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return!!e(...n)}catch(vc){return!1}},Kl=ql&&Ql((()=>{let e=!1;const t=new Request(rl.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t})),Jl=ql&&Ql((()=>Ta.isReadableStream(new Response("").body))),Xl={stream:Jl&&(e=>e.body)};var Zl;Vl&&(Zl=new Response,["text","arrayBuffer","blob","formData","stream"].forEach((e=>{!Xl[e]&&(Xl[e]=Ta.isFunction(Zl[e])?t=>t[e]():(t,n)=>{throw new Da("Response type '".concat(e,"' is not supported"),Da.ERR_NOT_SUPPORT,n)})})));const Gl=async(e,t)=>{const n=Ta.toFiniteNumber(e.getContentLength());return null==n?(async e=>{if(null==e)return 0;if(Ta.isBlob(e))return e.size;if(Ta.isSpecCompliantForm(e)){const t=new Request(rl.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return Ta.isArrayBufferView(e)||Ta.isArrayBuffer(e)?e.byteLength:(Ta.isURLSearchParams(e)&&(e+=""),Ta.isString(e)?(await $l(e)).byteLength:void 0)})(t):n},Yl=Vl&&(async e=>{let{url:t,method:n,data:r,signal:a,cancelToken:l,timeout:o,onDownloadProgress:i,onUploadProgress:s,responseType:c,headers:u,withCredentials:d="same-origin",fetchOptions:f}=Pl(e);c=c?(c+"").toLowerCase():"text";let m,h=Rl([a,l&&l.toAbortSignal()],o);const p=h&&h.unsubscribe&&(()=>{h.unsubscribe()});let g;try{if(s&&Kl&&"get"!==n&&"head"!==n&&0!==(g=await Gl(u,r))){let e,n=new Request(t,{method:"POST",body:r,duplex:"half"});if(Ta.isFormData(r)&&(e=n.headers.get("content-type"))&&u.setContentType(e),n.body){const[e,t]=jl(g,wl(Nl(s)));r=Wl(n.body,65536,e,t)}}Ta.isString(d)||(d=d?"include":"omit");const a="credentials"in Request.prototype;m=new Request(t,ot(ot({},f),{},{signal:h,method:n.toUpperCase(),headers:u.normalize().toJSON(),body:r,duplex:"half",credentials:a?d:void 0}));let l=await fetch(m);const o=Jl&&("stream"===c||"response"===c);if(Jl&&(i||o&&p)){const e={};["status","statusText","headers"].forEach((t=>{e[t]=l[t]}));const t=Ta.toFiniteNumber(l.headers.get("content-length")),[n,r]=i&&jl(t,wl(Nl(i),!0))||[];l=new Response(Wl(l.body,65536,n,(()=>{r&&r(),p&&p()})),e)}c=c||"text";let x=await Xl[Ta.findKey(Xl,c)||"text"](l,e);return!o&&p&&p(),await new Promise(((t,n)=>{yl(t,n,{data:x,headers:ml.from(l.headers),status:l.status,statusText:l.statusText,config:e,request:m})}))}catch(x){if(p&&p(),x&&"TypeError"===x.name&&/Load failed|fetch/i.test(x.message))throw Object.assign(new Da("Network Error",Da.ERR_NETWORK,e,m),{cause:x.cause||x});throw Da.from(x,x&&x.code,e,m)}}),eo={http:null,xhr:_l,fetch:Yl};Ta.forEach(eo,((e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(vc){}Object.defineProperty(e,"adapterName",{value:t})}}));const to=e=>"- ".concat(e),no=e=>Ta.isFunction(e)||null===e||!1===e,ro=e=>{e=Ta.isArray(e)?e:[e];const{length:t}=e;let n,r;const a={};for(let l=0;l<t;l++){let t;if(n=e[l],r=n,!no(n)&&(r=eo[(t=String(n)).toLowerCase()],void 0===r))throw new Da("Unknown adapter '".concat(t,"'"));if(r)break;a[t||"#"+l]=r}if(!r){const e=Object.entries(a).map((e=>{let[t,n]=e;return"adapter ".concat(t," ")+(!1===n?"is not supported by the environment":"is not available in the build")}));let n=t?e.length>1?"since :\n"+e.map(to).join("\n"):" "+to(e[0]):"as no adapter specified";throw new Da("There is no suitable adapter to dispatch the request "+n,"ERR_NOT_SUPPORT")}return r};function ao(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new xl(null,e)}function lo(e){ao(e),e.headers=ml.from(e.headers),e.data=hl.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);return ro(e.adapter||ol.adapter)(e).then((function(t){return ao(e),t.data=hl.call(e,e.transformResponse,t),t.headers=ml.from(t.headers),t}),(function(t){return pl(t)||(ao(e),t&&t.response&&(t.response.data=hl.call(e,e.transformResponse,t.response),t.response.headers=ml.from(t.response.headers))),Promise.reject(t)}))}const oo="1.9.0",io={};["object","boolean","number","function","string","symbol"].forEach(((e,t)=>{io[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}}));const so={};io.transitional=function(e,t,n){function r(e,t){return"[Axios v1.9.0] Transitional option '"+e+"'"+t+(n?". "+n:"")}return(n,a,l)=>{if(!1===e)throw new Da(r(a," has been removed"+(t?" in "+t:"")),Da.ERR_DEPRECATED);return t&&!so[a]&&(so[a]=!0,console.warn(r(a," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,a,l)}},io.spelling=function(e){return(t,n)=>(console.warn("".concat(n," is likely a misspelling of ").concat(e)),!0)};const co={assertOptions:function(e,t,n){if("object"!==typeof e)throw new Da("options must be an object",Da.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let a=r.length;for(;a-- >0;){const l=r[a],o=t[l];if(o){const t=e[l],n=void 0===t||o(t,l,e);if(!0!==n)throw new Da("option "+l+" must be "+n,Da.ERR_BAD_OPTION_VALUE)}else if(!0!==n)throw new Da("Unknown option "+l,Da.ERR_BAD_OPTION)}},validators:io},uo=co.validators;class fo{constructor(e){this.defaults=e||{},this.interceptors={request:new Ja,response:new Ja}}async request(e,t){try{return await this._request(e,t)}catch(n){if(n instanceof Error){let e={};Error.captureStackTrace?Error.captureStackTrace(e):e=new Error;const t=e.stack?e.stack.replace(/^.+\n/,""):"";try{n.stack?t&&!String(n.stack).endsWith(t.replace(/^.+\n.+\n/,""))&&(n.stack+="\n"+t):n.stack=t}catch(vc){}}throw n}}_request(e,t){"string"===typeof e?(t=t||{}).url=e:t=e||{},t=Ol(this.defaults,t);const{transitional:n,paramsSerializer:r,headers:a}=t;void 0!==n&&co.assertOptions(n,{silentJSONParsing:uo.transitional(uo.boolean),forcedJSONParsing:uo.transitional(uo.boolean),clarifyTimeoutError:uo.transitional(uo.boolean)},!1),null!=r&&(Ta.isFunction(r)?t.paramsSerializer={serialize:r}:co.assertOptions(r,{encode:uo.function,serialize:uo.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),co.assertOptions(t,{baseUrl:uo.spelling("baseURL"),withXsrfToken:uo.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let l=a&&Ta.merge(a.common,a[t.method]);a&&Ta.forEach(["delete","get","head","post","put","patch","common"],(e=>{delete a[e]})),t.headers=ml.concat(l,a);const o=[];let i=!0;this.interceptors.request.forEach((function(e){"function"===typeof e.runWhen&&!1===e.runWhen(t)||(i=i&&e.synchronous,o.unshift(e.fulfilled,e.rejected))}));const s=[];let c;this.interceptors.response.forEach((function(e){s.push(e.fulfilled,e.rejected)}));let u,d=0;if(!i){const e=[lo.bind(this),void 0];for(e.unshift.apply(e,o),e.push.apply(e,s),u=e.length,c=Promise.resolve(t);d<u;)c=c.then(e[d++],e[d++]);return c}u=o.length;let f=t;for(d=0;d<u;){const e=o[d++],t=o[d++];try{f=e(f)}catch(m){t.call(this,m);break}}try{c=lo.call(this,f)}catch(m){return Promise.reject(m)}for(d=0,u=s.length;d<u;)c=c.then(s[d++],s[d++]);return c}getUri(e){return Ka(El((e=Ol(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}}Ta.forEach(["delete","get","head","options"],(function(e){fo.prototype[e]=function(t,n){return this.request(Ol(n||{},{method:e,url:t,data:(n||{}).data}))}})),Ta.forEach(["post","put","patch"],(function(e){function t(t){return function(n,r,a){return this.request(Ol(a||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}fo.prototype[e]=t(),fo.prototype[e+"Form"]=t(!0)}));const mo=fo;class ho{constructor(e){if("function"!==typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise((function(e){t=e}));const n=this;this.promise.then((e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null})),this.promise.then=e=>{let t;const r=new Promise((e=>{n.subscribe(e),t=e})).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e((function(e,r,a){n.reason||(n.reason=new xl(e,r,a),t(n.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;const t=new ho((function(t){e=t}));return{token:t,cancel:e}}}const po=ho;const go={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(go).forEach((e=>{let[t,n]=e;go[n]=t}));const xo=go;const yo=function e(t){const n=new mo(t),r=Kr(mo.prototype.request,n);return Ta.extend(r,mo.prototype,n,{allOwnKeys:!0}),Ta.extend(r,n,null,{allOwnKeys:!0}),r.create=function(n){return e(Ol(t,n))},r}(ol);yo.Axios=mo,yo.CanceledError=xl,yo.CancelToken=po,yo.isCancel=pl,yo.VERSION=oo,yo.toFormData=Wa,yo.AxiosError=Da,yo.Cancel=yo.CanceledError,yo.all=function(e){return Promise.all(e)},yo.spread=function(e){return function(t){return e.apply(null,t)}},yo.isAxiosError=function(e){return Ta.isObject(e)&&!0===e.isAxiosError},yo.mergeConfig=Ol,yo.AxiosHeaders=ml,yo.formToJSON=e=>al(Ta.isHTMLForm(e)?new FormData(e):e),yo.getAdapter=ro,yo.HttpStatusCode=xo,yo.default=yo;const vo=yo,bo=Vr("search/searchResources",(async e=>(await vo.post("/v1/discovery/search",e)).data)),wo=Vr("search/getResource",(async e=>(await vo.get("/v1/discovery/resources/".concat(e))).data.resource)),jo=Dr({name:"search",initialState:{resources:[],loading:!1,error:null,totalCount:0,query:"",provider:""},reducers:{setQuery:(e,t)=>{e.query=t.payload},setProvider:(e,t)=>{e.provider=t.payload},clearResults:e=>{e.resources=[],e.totalCount=0,e.error=null}},extraReducers:e=>{e.addCase(bo.pending,(e=>{e.loading=!0,e.error=null})).addCase(bo.fulfilled,((e,t)=>{e.loading=!1,e.resources=t.payload.resources,e.totalCount=t.payload.total_count})).addCase(bo.rejected,((e,t)=>{e.loading=!1,e.error=t.error.message||"Search failed"})).addCase(wo.pending,(e=>{e.loading=!0,e.error=null})).addCase(wo.fulfilled,((e,t)=>{e.loading=!1;const n=e.resources.findIndex((e=>e.id===t.payload.id));-1!==n&&(e.resources[n]=t.payload)})).addCase(wo.rejected,((e,t)=>{e.loading=!1,e.error=t.error.message||"Failed to get resource"}))}}),{setQuery:No,setProvider:ko,clearResults:So}=jo.actions,Eo=jo.reducer,Co=["title","titleId"];function Oo(e,t){let{title:n,titleId:r}=e,l=mt(e,Co);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 15a4.5 4.5 0 0 0 4.5 4.5H18a3.75 3.75 0 0 0 1.332-7.257 3 3 0 0 0-3.758-3.848 5.25 5.25 0 0 0-10.233 2.33A4.502 4.502 0 0 0 2.25 15Z"}))}const Po=a.forwardRef(Oo),_o=["title","titleId"];function Ro(e,t){let{title:n,titleId:r}=e,l=mt(e,_o);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 17.25v-.228a4.5 4.5 0 0 0-.12-1.03l-2.268-9.64a3.375 3.375 0 0 0-3.285-2.602H7.923a3.375 3.375 0 0 0-3.285 2.602l-2.268 9.64a4.5 4.5 0 0 0-.12 1.03v.228m19.5 0a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3m19.5 0a3 3 0 0 0-3-3H5.25a3 3 0 0 0-3 3m16.5 0h.008v.008h-.008v-.008Zm-3 0h.008v.008h-.008v-.008Z"}))}const Lo=a.forwardRef(Ro),To=["title","titleId"];function Ao(e,t){let{title:n,titleId:r}=e,l=mt(e,To);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.568 3H5.25A2.25 2.25 0 0 0 3 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 0 0 5.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 0 0 9.568 3Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 6h.008v.008H6V6Z"}))}const Mo=a.forwardRef(Ao),Io=()=>{const e=R(),{resources:t,loading:n,error:r,totalCount:l,query:o,provider:i}=j((e=>e.search)),[s,c]=(0,a.useState)(o),[u,d]=(0,a.useState)(i),f=()=>{e(No(s)),e(ko(u)),e(bo({query:s,provider:u,limit:50}))};(0,a.useEffect)((()=>{e(bo({limit:20}))}),[e]);const m=e=>{switch(e.toLowerCase()){case"aws":return"bg-orange-100 text-orange-800";case"gcp":return"bg-blue-100 text-blue-800";case"azure":return"bg-cyan-100 text-cyan-800";default:return"bg-gray-100 text-gray-800"}};return(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h1",{className:"text-2xl font-bold text-white",children:"Resource Discovery"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Search and discover resources across all your cloud providers"})]}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsx)("div",{className:"px-4 py-5 sm:p-6",children:(0,it.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-4",children:[(0,it.jsxs)("div",{className:"sm:col-span-2",children:[(0,it.jsx)("label",{htmlFor:"search",className:"block text-sm font-medium text-gray-300",children:"Search Query"}),(0,it.jsxs)("div",{className:"mt-1 relative rounded-md shadow-sm",children:[(0,it.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,it.jsx)(vt,{className:"h-5 w-5 text-gray-400"})}),(0,it.jsx)("input",{type:"text",id:"search",className:"block w-full pl-10 pr-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent",placeholder:"Search by name, type, or tags...",value:s,onChange:e=>c(e.target.value),onKeyPress:e=>"Enter"===e.key&&f()})]})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{htmlFor:"provider",className:"block text-sm font-medium text-gray-300",children:"Provider"}),(0,it.jsx)("select",{id:"provider",className:"mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-600 bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent rounded-md",value:u,onChange:e=>d(e.target.value),children:[{value:"",label:"All Providers"},{value:"aws",label:"AWS"},{value:"gcp",label:"GCP"},{value:"azure",label:"Azure"}].map((e=>(0,it.jsx)("option",{value:e.value,children:e.label},e.value)))})]}),(0,it.jsxs)("div",{className:"flex items-end space-x-2",children:[(0,it.jsx)("button",{type:"button",onClick:f,disabled:n,className:"flex-1 bg-cyan-600 border border-transparent rounded-md py-2 px-4 inline-flex justify-center text-sm font-medium text-white hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 disabled:opacity-50",children:n?"Searching...":"Search"}),(0,it.jsx)("button",{type:"button",onClick:()=>{c(""),d(""),e(So())},className:"bg-gray-600 border border-transparent rounded-md py-2 px-4 inline-flex justify-center text-sm font-medium text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500",children:"Clear"})]})]})})}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white",children:"Search Results"}),(0,it.jsxs)("span",{className:"text-sm text-gray-400",children:[l," resources found"]})]}),r&&(0,it.jsx)("div",{className:"mb-4 bg-red-50 border border-red-200 rounded-md p-4",children:(0,it.jsx)("div",{className:"text-sm text-red-700",children:r})}),n?(0,it.jsx)("div",{className:"flex items-center justify-center h-32",children:(0,it.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500"})}):(0,it.jsx)("div",{className:"space-y-4",children:0===t.length?(0,it.jsxs)("div",{className:"text-center py-12",children:[(0,it.jsx)(Po,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,it.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-300",children:"No resources found"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Try adjusting your search criteria or run a discovery scan."})]}):t.map((e=>(0,it.jsxs)("div",{className:"border border-gray-600 rounded-lg p-4 hover:bg-gray-700 transition-colors",children:[(0,it.jsx)("div",{className:"flex items-start justify-between",children:(0,it.jsxs)("div",{className:"flex-1",children:[(0,it.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,it.jsx)(Lo,{className:"h-5 w-5 text-gray-400"}),(0,it.jsx)("h4",{className:"text-lg font-medium text-white",children:e.name}),(0,it.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(m(e.provider)),children:e.provider.toUpperCase()})]}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:e.type}),(0,it.jsx)("p",{className:"mt-1 text-xs text-gray-500 font-mono",children:e.id})]})}),Object.keys(e.tags).length>0&&(0,it.jsxs)("div",{className:"mt-3",children:[(0,it.jsxs)("div",{className:"flex items-center space-x-1 mb-2",children:[(0,it.jsx)(Mo,{className:"h-4 w-4 text-gray-400"}),(0,it.jsx)("span",{className:"text-sm text-gray-400",children:"Tags"})]}),(0,it.jsx)("div",{className:"flex flex-wrap gap-1",children:Object.entries(e.tags).map((e=>{let[t,n]=e;return(0,it.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-600 text-gray-200",children:[t,": ",n]},t)}))})]})]},e.id)))})]})})]})},Do=["title","titleId"];function Fo(e,t){let{title:n,titleId:r}=e,l=mt(e,Do);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 5.25a3 3 0 0 1 3 3m3 0a6 6 0 0 1-7.029 5.912c-.563-.097-1.159.026-1.563.43L10.5 17.25H8.25v2.25H6v2.25H2.25v-2.818c0-.597.237-1.17.659-1.591l6.499-6.499c.404-.404.527-1 .43-1.563A6 6 0 1 1 21.75 8.25Z"}))}const zo=a.forwardRef(Fo),Uo=["title","titleId"];function Bo(e,t){let{title:n,titleId:r}=e,l=mt(e,Uo);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const Wo=a.forwardRef(Bo),Vo=["title","titleId"];function qo(e,t){let{title:n,titleId:r}=e,l=mt(e,Vo);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))}const $o=a.forwardRef(qo),Ho=()=>{const e=R(),[t,n]=(0,a.useState)(1),[r,l]=(0,a.useState)({provider:"aws"}),[o,i]=(0,a.useState)(!1),[s,c]=(0,a.useState)(null),u=[{id:1,name:"Choose Provider",description:"Select your cloud provider"},{id:2,name:"Add Credentials",description:"Configure authentication"},{id:3,name:"Configure Discovery",description:"Set discovery parameters"},{id:4,name:"Launch Discovery",description:"Start resource discovery"}],d=[{id:"aws",name:"Amazon Web Services",icon:"\ud83d\udfe0",description:"Discover EC2, S3, RDS, and other AWS resources",fields:["accessKey","secretKey","region"]},{id:"gcp",name:"Google Cloud Platform",icon:"\ud83d\udd35",description:"Discover Compute Engine, Cloud Storage, and GCP resources",fields:["projectId"]},{id:"azure",name:"Microsoft Azure",icon:"\ud83d\udd37",description:"Discover Virtual Machines, Storage, and Azure resources",fields:["subscriptionId","tenantId"]}],f=(e,t)=>{l((n=>ot(ot({},n),{},{[e]:t})))},m=async()=>{i(!0),c(null),setTimeout((()=>{c("success"),i(!1)}),2e3)},h=()=>{e(bo({provider:r.provider,limit:100})),n(4)};return(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h1",{className:"text-2xl font-bold text-white",children:"Discovery Wizard"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Multi-step wizard to add credentials and launch discovery workflows"})]}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsx)("div",{className:"px-4 py-5 sm:p-6",children:(0,it.jsx)("nav",{"aria-label":"Progress",children:(0,it.jsx)("ol",{className:"flex items-center",children:u.map(((e,n)=>(0,it.jsxs)("li",{className:"".concat(n!==u.length-1?"pr-8 sm:pr-20":""," relative"),children:[(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"relative flex h-8 w-8 items-center justify-center rounded-full ".concat(e.id<=t?"bg-cyan-600 text-white":"border-2 border-gray-600 bg-gray-800 text-gray-400"),children:e.id<t?(0,it.jsx)(Wo,{className:"h-5 w-5"}):(0,it.jsx)("span",{className:"text-sm font-medium",children:e.id})}),(0,it.jsxs)("div",{className:"ml-4 min-w-0 flex-1",children:[(0,it.jsx)("p",{className:"text-sm font-medium ".concat(e.id<=t?"text-white":"text-gray-400"),children:e.name}),(0,it.jsx)("p",{className:"text-sm text-gray-400",children:e.description})]})]}),n!==u.length-1&&(0,it.jsx)("div",{className:"absolute top-4 left-4 -ml-px mt-0.5 h-full w-0.5 bg-gray-600"})]},e.id)))})})})}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsx)("div",{className:"px-4 py-5 sm:p-6",children:(()=>{switch(t){case 1:return(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsxs)("div",{className:"text-center",children:[(0,it.jsx)(Po,{className:"mx-auto h-12 w-12 text-cyan-400"}),(0,it.jsx)("h3",{className:"mt-2 text-lg font-medium text-white",children:"Choose Cloud Provider"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Select the cloud provider you want to discover resources from"})]}),(0,it.jsx)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-3",children:d.map((e=>(0,it.jsx)("button",{onClick:()=>{return t=e.id,l({provider:t}),void n(2);var t},className:"relative rounded-lg border border-gray-600 bg-gray-800 p-6 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-cyan-500 transition-colors",children:(0,it.jsxs)("div",{className:"text-center",children:[(0,it.jsx)("div",{className:"text-3xl mb-3",children:e.icon}),(0,it.jsx)("h4",{className:"text-lg font-medium text-white",children:e.name}),(0,it.jsx)("p",{className:"mt-2 text-sm text-gray-400",children:e.description})]})},e.id)))})]});case 2:const e=d.find((e=>e.id===r.provider));return(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsxs)("div",{className:"text-center",children:[(0,it.jsx)(zo,{className:"mx-auto h-12 w-12 text-cyan-400"}),(0,it.jsxs)("h3",{className:"mt-2 text-lg font-medium text-white",children:["Configure ",null===e||void 0===e?void 0:e.name," Credentials"]}),(0,it.jsxs)("p",{className:"mt-1 text-sm text-gray-400",children:["Enter your authentication credentials for ",null===e||void 0===e?void 0:e.name]})]}),(0,it.jsxs)("div",{className:"space-y-4",children:["aws"===r.provider&&(0,it.jsxs)(it.Fragment,{children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Access Key ID"}),(0,it.jsx)("input",{type:"text",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:r.accessKey||"",onChange:e=>f("accessKey",e.target.value),placeholder:"AKIA..."})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Secret Access Key"}),(0,it.jsx)("input",{type:"password",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:r.secretKey||"",onChange:e=>f("secretKey",e.target.value),placeholder:"\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Region"}),(0,it.jsxs)("select",{className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:r.region||"",onChange:e=>f("region",e.target.value),children:[(0,it.jsx)("option",{value:"",children:"Select a region"}),(0,it.jsx)("option",{value:"us-east-1",children:"US East (N. Virginia)"}),(0,it.jsx)("option",{value:"us-west-2",children:"US West (Oregon)"}),(0,it.jsx)("option",{value:"eu-west-1",children:"Europe (Ireland)"}),(0,it.jsx)("option",{value:"ap-southeast-1",children:"Asia Pacific (Singapore)"})]})]})]}),"gcp"===r.provider&&(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Project ID"}),(0,it.jsx)("input",{type:"text",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:r.projectId||"",onChange:e=>f("projectId",e.target.value),placeholder:"my-gcp-project"})]}),"azure"===r.provider&&(0,it.jsxs)(it.Fragment,{children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Subscription ID"}),(0,it.jsx)("input",{type:"text",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:r.subscriptionId||"",onChange:e=>f("subscriptionId",e.target.value),placeholder:"xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Tenant ID"}),(0,it.jsx)("input",{type:"text",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:r.tenantId||"",onChange:e=>f("tenantId",e.target.value),placeholder:"xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"})]})]})]}),(0,it.jsxs)("div",{className:"flex justify-between",children:[(0,it.jsx)("button",{onClick:()=>n(1),className:"px-4 py-2 border border-gray-600 rounded-md text-gray-300 hover:bg-gray-700",children:"Back"}),(0,it.jsx)("button",{onClick:m,disabled:o,className:"px-4 py-2 bg-cyan-600 text-white rounded-md hover:bg-cyan-700 disabled:opacity-50",children:o?"Validating...":"Validate & Continue"})]}),"success"===s&&(0,it.jsxs)("div",{className:"flex items-center space-x-2 text-green-400",children:[(0,it.jsx)(Wo,{className:"h-5 w-5"}),(0,it.jsx)("span",{children:"Credentials validated successfully!"}),(0,it.jsx)("button",{onClick:()=>n(3),className:"ml-4 px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700",children:"Continue"})]}),"error"===s&&(0,it.jsxs)("div",{className:"flex items-center space-x-2 text-red-400",children:[(0,it.jsx)($o,{className:"h-5 w-5"}),(0,it.jsx)("span",{children:"Credential validation failed. Please check your credentials."})]})]});case 3:return(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsxs)("div",{className:"text-center",children:[(0,it.jsx)(vt,{className:"mx-auto h-12 w-12 text-cyan-400"}),(0,it.jsx)("h3",{className:"mt-2 text-lg font-medium text-white",children:"Configure Discovery"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Set parameters for resource discovery"})]}),(0,it.jsxs)("div",{className:"space-y-4",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Discovery Scope"}),(0,it.jsxs)("select",{className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",children:[(0,it.jsx)("option",{children:"All Resources"}),(0,it.jsx)("option",{children:"Compute Only"}),(0,it.jsx)("option",{children:"Storage Only"}),(0,it.jsx)("option",{children:"Network Only"})]})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Resource Tags Filter"}),(0,it.jsx)("input",{type:"text",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",placeholder:"Environment=production,Team=platform"})]}),(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("input",{type:"checkbox",className:"h-4 w-4 text-cyan-600 focus:ring-cyan-500 border-gray-600 rounded bg-gray-700",defaultChecked:!0}),(0,it.jsx)("label",{className:"ml-2 block text-sm text-gray-300",children:"Cache results for faster subsequent searches"})]})]}),(0,it.jsxs)("div",{className:"flex justify-between",children:[(0,it.jsx)("button",{onClick:()=>n(2),className:"px-4 py-2 border border-gray-600 rounded-md text-gray-300 hover:bg-gray-700",children:"Back"}),(0,it.jsx)("button",{onClick:h,className:"px-4 py-2 bg-cyan-600 text-white rounded-md hover:bg-cyan-700",children:"Launch Discovery"})]})]});case 4:return(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsxs)("div",{className:"text-center",children:[(0,it.jsx)(Wo,{className:"mx-auto h-12 w-12 text-green-400"}),(0,it.jsx)("h3",{className:"mt-2 text-lg font-medium text-white",children:"Discovery Launched!"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Resource discovery is now running in the background"})]}),(0,it.jsxs)("div",{className:"bg-gray-700 rounded-lg p-4",children:[(0,it.jsx)("h4",{className:"text-md font-medium text-white mb-2",children:"Discovery Status"}),(0,it.jsxs)("div",{className:"space-y-2",children:[(0,it.jsxs)("div",{className:"flex justify-between",children:[(0,it.jsx)("span",{className:"text-gray-300",children:"Provider:"}),(0,it.jsx)("span",{className:"text-white",children:r.provider.toUpperCase()})]}),(0,it.jsxs)("div",{className:"flex justify-between",children:[(0,it.jsx)("span",{className:"text-gray-300",children:"Status:"}),(0,it.jsx)("span",{className:"text-green-400",children:"Running"})]}),(0,it.jsxs)("div",{className:"flex justify-between",children:[(0,it.jsx)("span",{className:"text-gray-300",children:"Resources Found:"}),(0,it.jsx)("span",{className:"text-white",children:"42"})]})]})]}),(0,it.jsx)("div",{className:"flex justify-center",children:(0,it.jsx)("button",{onClick:()=>window.location.href="/search",className:"px-6 py-2 bg-cyan-600 text-white rounded-md hover:bg-cyan-700",children:"View Results"})})]});default:return null}})()})})]})},Qo=Vr("workflow/fetchWorkflows",(async()=>(await vo.get("/v1/workflows")).data.workflows)),Ko=Vr("workflow/executeWorkflow",(async e=>(await vo.post("/v1/workflows/execute",{workflow_id:e.workflowId,inputs:e.inputs})).data)),Jo=Vr("workflow/getWorkflowStatus",(async e=>(await vo.get("/v1/workflows/".concat(e,"/status"))).data)),Xo=Dr({name:"workflow",initialState:{workflows:[],executions:[],loading:!1,error:null,selectedWorkflow:null},reducers:{setSelectedWorkflow:(e,t)=>{e.selectedWorkflow=t.payload},clearExecutions:e=>{e.executions=[]},updateExecutionStatus:(e,t)=>{const n=e.executions.find((e=>e.execution_id===t.payload.executionId));n&&(n.status=t.payload.status)}},extraReducers:e=>{e.addCase(Qo.pending,(e=>{e.loading=!0,e.error=null})).addCase(Qo.fulfilled,((e,t)=>{e.loading=!1,e.workflows=t.payload})).addCase(Qo.rejected,((e,t)=>{e.loading=!1,e.error=t.error.message||"Failed to fetch workflows"})).addCase(Ko.pending,(e=>{e.loading=!0,e.error=null})).addCase(Ko.fulfilled,((e,t)=>{e.loading=!1,e.executions.push(t.payload)})).addCase(Ko.rejected,((e,t)=>{e.loading=!1,e.error=t.error.message||"Failed to execute workflow"})).addCase(Jo.fulfilled,((e,t)=>{const n=e.executions.findIndex((e=>e.execution_id===t.payload.workflow_id));-1!==n&&(e.executions[n]=ot(ot({},e.executions[n]),t.payload))}))}}),{setSelectedWorkflow:Zo,clearExecutions:Go,updateExecutionStatus:Yo}=Xo.actions,ei=Xo.reducer,ti=["title","titleId"];function ni(e,t){let{title:n,titleId:r}=e,l=mt(e,ti);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const ri=a.forwardRef(ni),ai=["title","titleId"];function li(e,t){let{title:n,titleId:r}=e,l=mt(e,ai);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const oi=a.forwardRef(li),ii=["title","titleId"];function si(e,t){let{title:n,titleId:r}=e,l=mt(e,ii);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z"}))}const ci=a.forwardRef(si),ui=()=>{const e=R(),{workflows:t,executions:n,loading:r,error:l,selectedWorkflow:o}=j((e=>e.workflow)),[i,s]=(0,a.useState)({});(0,a.useEffect)((()=>{e(Qo())}),[e]);const c=e=>{switch(e){case"running":return(0,it.jsx)(ri,{className:"h-5 w-5 text-yellow-400"});case"completed":return(0,it.jsx)(Wo,{className:"h-5 w-5 text-green-400"});case"failed":return(0,it.jsx)(oi,{className:"h-5 w-5 text-red-400"});default:return(0,it.jsx)(jt,{className:"h-5 w-5 text-gray-400"})}},u=e=>{switch(e){case"running":return"bg-yellow-100 text-yellow-800";case"completed":return"bg-green-100 text-green-800";case"failed":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}};return(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h1",{className:"text-2xl font-bold text-white",children:"Workflow Executor"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Execute and monitor automated workflows across your infrastructure"})]}),(0,it.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white mb-4",children:"Available Workflows"}),r?(0,it.jsx)("div",{className:"flex items-center justify-center h-32",children:(0,it.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500"})}):(0,it.jsxs)("div",{className:"space-y-3",children:[t.map((t=>(0,it.jsx)("div",{className:"border rounded-lg p-4 cursor-pointer transition-colors ".concat((null===o||void 0===o?void 0:o.id)===t.id?"border-cyan-500 bg-cyan-500/10":"border-gray-600 hover:bg-gray-700"),onClick:()=>e(Zo(t)),children:(0,it.jsxs)("div",{className:"flex items-start justify-between",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h4",{className:"text-lg font-medium text-white",children:t.name}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:t.description}),(0,it.jsxs)("p",{className:"mt-1 text-xs text-gray-500",children:["Created: ",new Date(t.created_at).toLocaleDateString()]})]}),(0,it.jsx)(jt,{className:"h-6 w-6 text-gray-400"})]})},t.id))),0===t.length&&(0,it.jsxs)("div",{className:"text-center py-8",children:[(0,it.jsx)(jt,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,it.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-300",children:"No workflows available"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Create a workflow to get started."})]})]})]})}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white mb-4",children:"Execute Workflow"}),o?(0,it.jsxs)("div",{className:"space-y-4",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h4",{className:"text-md font-medium text-white",children:o.name}),(0,it.jsx)("p",{className:"text-sm text-gray-400",children:o.description})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Input Parameters (JSON)"}),(0,it.jsx)("textarea",{className:"block w-full h-32 px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent",placeholder:'{"key": "value"}',value:JSON.stringify(i,null,2),onChange:e=>{try{s(JSON.parse(e.target.value||"{}"))}catch(t){}}})]}),(0,it.jsxs)("button",{onClick:()=>{o&&(e(Ko({workflowId:o.id,inputs:i})),s({}))},disabled:r,className:"w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 disabled:opacity-50",children:[(0,it.jsx)(ci,{className:"h-4 w-4 mr-2"}),r?"Executing...":"Execute Workflow"]})]}):(0,it.jsxs)("div",{className:"text-center py-8",children:[(0,it.jsx)(ci,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,it.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-300",children:"Select a workflow"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Choose a workflow from the list to execute it."})]}),l&&(0,it.jsx)("div",{className:"mt-4 bg-red-50 border border-red-200 rounded-md p-4",children:(0,it.jsx)("div",{className:"text-sm text-red-700",children:l})})]})})]}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white mb-4",children:"Execution History"}),0===n.length?(0,it.jsxs)("div",{className:"text-center py-8",children:[(0,it.jsx)(ri,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,it.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-300",children:"No executions yet"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Execute a workflow to see the history here."})]}):(0,it.jsx)("div",{className:"space-y-4",children:n.map((t=>(0,it.jsxs)("div",{className:"border border-gray-600 rounded-lg p-4",children:[(0,it.jsxs)("div",{className:"flex items-start justify-between",children:[(0,it.jsxs)("div",{className:"flex-1",children:[(0,it.jsxs)("div",{className:"flex items-center space-x-2",children:[c(t.status),(0,it.jsx)("h4",{className:"text-lg font-medium text-white",children:t.workflow_id}),(0,it.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(u(t.status)),children:t.status})]}),(0,it.jsxs)("p",{className:"mt-1 text-sm text-gray-400",children:["Execution ID: ",t.execution_id]}),t.started_at&&(0,it.jsxs)("p",{className:"mt-1 text-xs text-gray-500",children:["Started: ",new Date(t.started_at).toLocaleString()]}),t.completed_at&&(0,it.jsxs)("p",{className:"mt-1 text-xs text-gray-500",children:["Completed: ",new Date(t.completed_at).toLocaleString()]})]}),(0,it.jsx)("button",{onClick:()=>e(Jo(t.execution_id)),className:"text-cyan-400 hover:text-cyan-300 text-sm",children:"Refresh"})]}),t.outputs&&Object.keys(t.outputs).length>0&&(0,it.jsxs)("div",{className:"mt-3",children:[(0,it.jsx)("h5",{className:"text-sm font-medium text-gray-300",children:"Outputs:"}),(0,it.jsx)("pre",{className:"mt-1 text-xs text-gray-400 bg-gray-700 p-2 rounded overflow-x-auto",children:JSON.stringify(t.outputs,null,2)})]})]},t.execution_id)))})]})})]})},di=Vr("envoy/fetchConfigs",(async()=>(await vo.get("/v1/envoy/configs")).data.configs)),fi=Vr("envoy/fetchNodes",(async()=>(await vo.get("/v1/envoy/nodes")).data.nodes)),mi=Vr("envoy/createConfig",(async e=>(await vo.post("/v1/envoy/configs",e)).data)),hi=Vr("envoy/updateConfig",(async e=>(await vo.put("/v1/envoy/configs/".concat(e.id),e)).data)),pi=Dr({name:"envoy",initialState:{configs:[],nodes:[],selectedConfig:null,loading:!1,error:null},reducers:{setSelectedConfig:(e,t)=>{e.selectedConfig=t.payload}},extraReducers:e=>{e.addCase(di.pending,(e=>{e.loading=!0,e.error=null})).addCase(di.fulfilled,((e,t)=>{e.loading=!1,e.configs=t.payload})).addCase(di.rejected,((e,t)=>{e.loading=!1,e.error=t.error.message||"Failed to fetch Envoy configs"})).addCase(fi.fulfilled,((e,t)=>{e.nodes=t.payload})).addCase(mi.fulfilled,((e,t)=>{e.configs.push(t.payload)})).addCase(hi.fulfilled,((e,t)=>{const n=e.configs.findIndex((e=>e.id===t.payload.id));-1!==n&&(e.configs[n]=t.payload)}))}}),{setSelectedConfig:gi}=pi.actions,xi=pi.reducer,yi=["title","titleId"];function vi(e,t){let{title:n,titleId:r}=e,l=mt(e,yi);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))}const bi=a.forwardRef(vi),wi=["title","titleId"];function ji(e,t){let{title:n,titleId:r}=e,l=mt(e,wi);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))}const Ni=a.forwardRef(ji);function ki(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Si(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Ei(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Si(Object(n),!0).forEach((function(t){ki(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Si(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Ci(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n,r,a={},l=Object.keys(e);for(r=0;r<l.length;r++)n=l[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(r=0;r<l.length;r++)n=l[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}function Oi(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Pi(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _i(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Ri(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?_i(Object(n),!0).forEach((function(t){Pi(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):_i(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Li(e){return function t(){for(var n=this,r=arguments.length,a=new Array(r),l=0;l<r;l++)a[l]=arguments[l];return a.length>=e.length?e.apply(this,a):function(){for(var e=arguments.length,r=new Array(e),l=0;l<e;l++)r[l]=arguments[l];return t.apply(n,[].concat(a,r))}}}function Ti(e){return{}.toString.call(e).includes("Object")}function Ai(e){return"function"===typeof e}var Mi=Li((function(e,t){throw new Error(e[t]||e.default)}))({initialIsRequired:"initial state is required",initialType:"initial state should be an object",initialContent:"initial state shouldn't be an empty object",handlerType:"handler should be an object or a function",handlersType:"all handlers should be a functions",selectorType:"selector should be a function",changeType:"provided value of changes should be an object",changeField:'it seams you want to change a field in the state which is not specified in the "initial" state',default:"an unknown error accured in `state-local` package"}),Ii={changes:function(e,t){return Ti(t)||Mi("changeType"),Object.keys(t).some((function(t){return n=e,r=t,!Object.prototype.hasOwnProperty.call(n,r);var n,r}))&&Mi("changeField"),t},selector:function(e){Ai(e)||Mi("selectorType")},handler:function(e){Ai(e)||Ti(e)||Mi("handlerType"),Ti(e)&&Object.values(e).some((function(e){return!Ai(e)}))&&Mi("handlersType")},initial:function(e){var t;e||Mi("initialIsRequired"),Ti(e)||Mi("initialType"),t=e,Object.keys(t).length||Mi("initialContent")}};function Di(e,t){return Ai(t)?t(e.current):t}function Fi(e,t){return e.current=Ri(Ri({},e.current),t),t}function zi(e,t,n){return Ai(t)?t(e.current):Object.keys(n).forEach((function(n){var r;return null===(r=t[n])||void 0===r?void 0:r.call(t,e.current[n])})),n}var Ui={create:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};Ii.initial(e),Ii.handler(t);var n={current:e},r=Li(zi)(n,t),a=Li(Fi)(n),l=Li(Ii.changes)(e),o=Li(Di)(n);return[function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(e){return e};return Ii.selector(e),e(n.current)},function(e){!function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){return t.reduceRight((function(e,t){return t(e)}),e)}}(r,a,l,o)(e)}]}};const Bi=Ui;const Wi={paths:{vs:"https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs"}};const Vi=function(e){return function t(){for(var n=this,r=arguments.length,a=new Array(r),l=0;l<r;l++)a[l]=arguments[l];return a.length>=e.length?e.apply(this,a):function(){for(var e=arguments.length,r=new Array(e),l=0;l<e;l++)r[l]=arguments[l];return t.apply(n,[].concat(a,r))}}};const qi=function(e){return{}.toString.call(e).includes("Object")};var $i={configIsRequired:"the configuration object is required",configType:"the configuration object should be an object",default:"an unknown error accured in `@monaco-editor/loader` package",deprecation:"Deprecation warning!\n    You are using deprecated way of configuration.\n\n    Instead of using\n      monaco.config({ urls: { monacoBase: '...' } })\n    use\n      monaco.config({ paths: { vs: '...' } })\n\n    For more please check the link https://github.com/suren-atoyan/monaco-loader#config\n  "},Hi=Vi((function(e,t){throw new Error(e[t]||e.default)}))($i),Qi={config:function(e){return e||Hi("configIsRequired"),qi(e)||Hi("configType"),e.urls?(console.warn($i.deprecation),{paths:{vs:e.urls.monacoBase}}):e}};const Ki=Qi;const Ji=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){return t.reduceRight((function(e,t){return t(e)}),e)}};const Xi=function e(t,n){return Object.keys(n).forEach((function(r){n[r]instanceof Object&&t[r]&&Object.assign(n[r],e(t[r],n[r]))})),Ei(Ei({},t),n)};var Zi={type:"cancelation",msg:"operation is manually canceled"};const Gi=function(e){var t=!1,n=new Promise((function(n,r){e.then((function(e){return t?r(Zi):n(e)})),e.catch(r)}));return n.cancel=function(){return t=!0},n};var Yi=function(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"===typeof Symbol||!(Symbol.iterator in Object(e)))return;var n=[],r=!0,a=!1,l=void 0;try{for(var o,i=e[Symbol.iterator]();!(r=(o=i.next()).done)&&(n.push(o.value),!t||n.length!==t);r=!0);}catch(s){a=!0,l=s}finally{try{r||null==i.return||i.return()}finally{if(a)throw l}}return n}(e,t)||function(e,t){if(!e)return;if("string"===typeof e)return Oi(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Oi(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(Bi.create({config:Wi,isInitialized:!1,resolve:null,reject:null,monaco:null}),2),es=Yi[0],ts=Yi[1];function ns(e){return document.body.appendChild(e)}function rs(e){var t=es((function(e){return{config:e.config,reject:e.reject}})),n=function(e){var t=document.createElement("script");return e&&(t.src=e),t}("".concat(t.config.paths.vs,"/loader.js"));return n.onload=function(){return e()},n.onerror=t.reject,n}function as(){var e=es((function(e){return{config:e.config,resolve:e.resolve,reject:e.reject}})),t=window.require;t.config(e.config),t(["vs/editor/editor.main"],(function(t){ls(t),e.resolve(t)}),(function(t){e.reject(t)}))}function ls(e){es().monaco||ts({monaco:e})}var os=new Promise((function(e,t){return ts({resolve:e,reject:t})})),is={config:function(e){var t=Ki.config(e),n=t.monaco,r=Ci(t,["monaco"]);ts((function(e){return{config:Xi(e.config,r),monaco:n}}))},init:function(){var e=es((function(e){return{monaco:e.monaco,isInitialized:e.isInitialized,resolve:e.resolve}}));if(!e.isInitialized){if(ts({isInitialized:!0}),e.monaco)return e.resolve(e.monaco),Gi(os);if(window.monaco&&window.monaco.editor)return ls(window.monaco),e.resolve(window.monaco),Gi(os);Ji(ns,rs)(as)}return Gi(os)},__getMonacoInstance:function(){return es((function(e){return e.monaco}))}};const ss=is;var cs={wrapper:{display:"flex",position:"relative",textAlign:"initial"},fullWidth:{width:"100%"},hide:{display:"none"}},us={container:{display:"flex",height:"100%",width:"100%",justifyContent:"center",alignItems:"center"}};var ds=function(e){let{children:t}=e;return a.createElement("div",{style:us.container},t)},fs=ds;var ms=function(e){let{width:t,height:n,isEditorReady:r,loading:l,_ref:o,className:i,wrapperProps:s}=e;return a.createElement("section",ot({style:ot(ot({},cs.wrapper),{},{width:t,height:n})},s),!r&&a.createElement(fs,null,l),a.createElement("div",{ref:o,style:ot(ot({},cs.fullWidth),!r&&cs.hide),className:i}))},hs=(0,a.memo)(ms);var ps=function(e){(0,a.useEffect)(e,[])};var gs=function(e,t){let n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],r=(0,a.useRef)(!0);(0,a.useEffect)(r.current||!n?()=>{r.current=!1}:e,t)};function xs(){}function ys(e,t,n,r){return function(e,t){return e.editor.getModel(vs(e,t))}(e,r)||function(e,t,n,r){return e.editor.createModel(t,n,r?vs(e,r):void 0)}(e,t,n,r)}function vs(e,t){return e.Uri.parse(t)}var bs=function(e){let{original:t,modified:n,language:r,originalLanguage:l,modifiedLanguage:o,originalModelPath:i,modifiedModelPath:s,keepCurrentOriginalModel:c=!1,keepCurrentModifiedModel:u=!1,theme:d="light",loading:f="Loading...",options:m={},height:h="100%",width:p="100%",className:g,wrapperProps:x={},beforeMount:y=xs,onMount:v=xs}=e,[b,w]=(0,a.useState)(!1),[j,N]=(0,a.useState)(!0),k=(0,a.useRef)(null),S=(0,a.useRef)(null),E=(0,a.useRef)(null),C=(0,a.useRef)(v),O=(0,a.useRef)(y),P=(0,a.useRef)(!1);ps((()=>{let e=ss.init();return e.then((e=>(S.current=e)&&N(!1))).catch((e=>"cancelation"!==(null===e||void 0===e?void 0:e.type)&&console.error("Monaco initialization: error:",e))),()=>k.current?function(){var e,t,n,r;let a=null===(e=k.current)||void 0===e?void 0:e.getModel();c||null!==a&&void 0!==a&&null!==(t=a.original)&&void 0!==t&&t.dispose(),u||null!==a&&void 0!==a&&null!==(n=a.modified)&&void 0!==n&&n.dispose(),null===(r=k.current)||void 0===r||r.dispose()}():e.cancel()})),gs((()=>{if(k.current&&S.current){let e=k.current.getOriginalEditor(),n=ys(S.current,t||"",l||r||"text",i||"");n!==e.getModel()&&e.setModel(n)}}),[i],b),gs((()=>{if(k.current&&S.current){let e=k.current.getModifiedEditor(),t=ys(S.current,n||"",o||r||"text",s||"");t!==e.getModel()&&e.setModel(t)}}),[s],b),gs((()=>{let e=k.current.getModifiedEditor();e.getOption(S.current.editor.EditorOption.readOnly)?e.setValue(n||""):n!==e.getValue()&&(e.executeEdits("",[{range:e.getModel().getFullModelRange(),text:n||"",forceMoveMarkers:!0}]),e.pushUndoStop())}),[n],b),gs((()=>{var e;null===(e=k.current)||void 0===e||null===(e=e.getModel())||void 0===e||e.original.setValue(t||"")}),[t],b),gs((()=>{let{original:e,modified:t}=k.current.getModel();S.current.editor.setModelLanguage(e,l||r||"text"),S.current.editor.setModelLanguage(t,o||r||"text")}),[r,l,o],b),gs((()=>{var e;null===(e=S.current)||void 0===e||e.editor.setTheme(d)}),[d],b),gs((()=>{var e;null===(e=k.current)||void 0===e||e.updateOptions(m)}),[m],b);let _=(0,a.useCallback)((()=>{var e;if(!S.current)return;O.current(S.current);let a=ys(S.current,t||"",l||r||"text",i||""),c=ys(S.current,n||"",o||r||"text",s||"");null===(e=k.current)||void 0===e||e.setModel({original:a,modified:c})}),[r,n,o,t,l,i,s]),R=(0,a.useCallback)((()=>{var e;!P.current&&E.current&&(k.current=S.current.editor.createDiffEditor(E.current,ot({automaticLayout:!0},m)),_(),null!==(e=S.current)&&void 0!==e&&e.editor.setTheme(d),w(!0),P.current=!0)}),[m,d,_]);return(0,a.useEffect)((()=>{b&&C.current(k.current,S.current)}),[b]),(0,a.useEffect)((()=>{!j&&!b&&R()}),[j,b,R]),a.createElement(hs,{width:p,height:h,isEditorReady:b,loading:f,_ref:E,className:g,wrapperProps:x})};(0,a.memo)(bs);var ws=function(e){let t=(0,a.useRef)();return(0,a.useEffect)((()=>{t.current=e}),[e]),t.current},js=new Map;var Ns=function(e){let{defaultValue:t,defaultLanguage:n,defaultPath:r,value:l,language:o,path:i,theme:s="light",line:c,loading:u="Loading...",options:d={},overrideServices:f={},saveViewState:m=!0,keepCurrentModel:h=!1,width:p="100%",height:g="100%",className:x,wrapperProps:y={},beforeMount:v=xs,onMount:b=xs,onChange:w,onValidate:j=xs}=e,[N,k]=(0,a.useState)(!1),[S,E]=(0,a.useState)(!0),C=(0,a.useRef)(null),O=(0,a.useRef)(null),P=(0,a.useRef)(null),_=(0,a.useRef)(b),R=(0,a.useRef)(v),L=(0,a.useRef)(),T=(0,a.useRef)(l),A=ws(i),M=(0,a.useRef)(!1),I=(0,a.useRef)(!1);ps((()=>{let e=ss.init();return e.then((e=>(C.current=e)&&E(!1))).catch((e=>"cancelation"!==(null===e||void 0===e?void 0:e.type)&&console.error("Monaco initialization: error:",e))),()=>O.current?function(){var e,t;null!==(e=L.current)&&void 0!==e&&e.dispose(),h?m&&js.set(i,O.current.saveViewState()):null===(t=O.current.getModel())||void 0===t||t.dispose(),O.current.dispose()}():e.cancel()})),gs((()=>{var e,a,s,c;let u=ys(C.current,t||l||"",n||o||"",i||r||"");u!==(null===(e=O.current)||void 0===e?void 0:e.getModel())&&(m&&js.set(A,null===(a=O.current)||void 0===a?void 0:a.saveViewState()),null!==(s=O.current)&&void 0!==s&&s.setModel(u),m&&(null===(c=O.current)||void 0===c||c.restoreViewState(js.get(i))))}),[i],N),gs((()=>{var e;null===(e=O.current)||void 0===e||e.updateOptions(d)}),[d],N),gs((()=>{!O.current||void 0===l||(O.current.getOption(C.current.editor.EditorOption.readOnly)?O.current.setValue(l):l!==O.current.getValue()&&(I.current=!0,O.current.executeEdits("",[{range:O.current.getModel().getFullModelRange(),text:l,forceMoveMarkers:!0}]),O.current.pushUndoStop(),I.current=!1))}),[l],N),gs((()=>{var e,t;let n=null===(e=O.current)||void 0===e?void 0:e.getModel();n&&o&&(null===(t=C.current)||void 0===t||t.editor.setModelLanguage(n,o))}),[o],N),gs((()=>{var e;void 0!==c&&(null===(e=O.current)||void 0===e||e.revealLine(c))}),[c],N),gs((()=>{var e;null===(e=C.current)||void 0===e||e.editor.setTheme(s)}),[s],N);let D=(0,a.useCallback)((()=>{if(P.current&&C.current&&!M.current){var e;R.current(C.current);let a=i||r,u=ys(C.current,l||t||"",n||o||"",a||"");O.current=null===(e=C.current)||void 0===e?void 0:e.editor.create(P.current,ot({model:u,automaticLayout:!0},d),f),m&&O.current.restoreViewState(js.get(a)),C.current.editor.setTheme(s),void 0!==c&&O.current.revealLine(c),k(!0),M.current=!0}}),[t,n,r,l,o,i,d,f,m,s,c]);return(0,a.useEffect)((()=>{N&&_.current(O.current,C.current)}),[N]),(0,a.useEffect)((()=>{!S&&!N&&D()}),[S,N,D]),T.current=l,(0,a.useEffect)((()=>{var e,t;N&&w&&(null!==(e=L.current)&&void 0!==e&&e.dispose(),L.current=null===(t=O.current)||void 0===t?void 0:t.onDidChangeModelContent((e=>{I.current||w(O.current.getValue(),e)})))}),[N,w]),(0,a.useEffect)((()=>{if(N){let e=C.current.editor.onDidChangeMarkers((e=>{var t;let n=null===(t=O.current.getModel())||void 0===t?void 0:t.uri;if(n&&e.find((e=>e.path===n.path))){let e=C.current.editor.getModelMarkers({resource:n});null===j||void 0===j||j(e)}}));return()=>{null===e||void 0===e||e.dispose()}}return()=>{}}),[N,j]),a.createElement(hs,{width:p,height:g,isEditorReady:N,loading:u,_ref:P,className:x,wrapperProps:y})},ks=(0,a.memo)(Ns);const Ss=()=>{const e=R(),{configs:t,nodes:n,selectedConfig:r,loading:l,error:o}=j((e=>e.envoy)),[i,s]=(0,a.useState)(!1),[c,u]=(0,a.useState)({node_id:"",cluster_name:"",config:"",version:"1.0.0"});(0,a.useEffect)((()=>{e(di()),e(fi())}),[e]);return(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsxs)("div",{className:"flex items-center justify-between",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h1",{className:"text-2xl font-bold text-white",children:"Envoy Configuration"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Manage Envoy proxy configurations and deployments"})]}),(0,it.jsxs)("button",{onClick:()=>s(!0),className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500",children:[(0,it.jsx)(bi,{className:"h-4 w-4 mr-2"}),"New Config"]})]}),(0,it.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white mb-4",children:"Configurations"}),l?(0,it.jsx)("div",{className:"flex items-center justify-center h-32",children:(0,it.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500"})}):(0,it.jsxs)("div",{className:"space-y-3",children:[t.map((t=>(0,it.jsx)("div",{className:"border rounded-lg p-4 cursor-pointer transition-colors ".concat((null===r||void 0===r?void 0:r.id)===t.id?"border-cyan-500 bg-cyan-500/10":"border-gray-600 hover:bg-gray-700"),onClick:()=>e(gi(t)),children:(0,it.jsxs)("div",{className:"flex items-start justify-between",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h4",{className:"text-lg font-medium text-white",children:t.cluster_name}),(0,it.jsxs)("p",{className:"mt-1 text-sm text-gray-400",children:["Node: ",t.node_id]}),(0,it.jsxs)("p",{className:"mt-1 text-xs text-gray-500",children:["Version: ",t.version]})]}),(0,it.jsx)(Ni,{className:"h-6 w-6 text-gray-400"})]})},t.id))),0===t.length&&(0,it.jsxs)("div",{className:"text-center py-8",children:[(0,it.jsx)(St,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,it.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-300",children:"No configurations"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Create your first Envoy configuration."})]})]})]})}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white mb-4",children:"Configuration Editor"}),r?(0,it.jsxs)("div",{className:"space-y-4",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Cluster Name"}),(0,it.jsx)("input",{type:"text",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:r.cluster_name,readOnly:!0})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Node ID"}),(0,it.jsx)("input",{type:"text",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:r.node_id,readOnly:!0})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Configuration (YAML)"}),(0,it.jsx)("div",{className:"mt-1 border border-gray-600 rounded-md overflow-hidden",children:(0,it.jsx)(ks,{height:"300px",defaultLanguage:"yaml",value:r.config,theme:"vs-dark",options:{readOnly:!0,minimap:{enabled:!1},scrollBeyondLastLine:!1,fontSize:14,fontFamily:"JetBrains Mono, monospace",lineNumbers:"on",folding:!0,wordWrap:"on"}})})]})]}):(0,it.jsxs)("div",{className:"text-center py-8",children:[(0,it.jsx)(Ni,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,it.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-300",children:"Select a configuration"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Choose a configuration from the list to view and edit it."})]})]})})]}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white mb-4",children:"Connected Envoy Nodes"}),0===n.length?(0,it.jsxs)("div",{className:"text-center py-8",children:[(0,it.jsx)(St,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,it.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-300",children:"No nodes connected"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Envoy nodes will appear here when they connect to the control plane."})]}):(0,it.jsx)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3",children:n.map((e=>(0,it.jsxs)("div",{className:"border border-gray-600 rounded-lg p-4",children:[(0,it.jsxs)("div",{className:"flex items-center justify-between",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h4",{className:"text-lg font-medium text-white",children:e.id}),(0,it.jsxs)("p",{className:"text-sm text-gray-400",children:["Cluster: ",e.cluster]}),(0,it.jsxs)("p",{className:"text-xs text-gray-500",children:["Version: ",e.version]})]}),(0,it.jsx)("div",{className:"w-3 h-3 rounded-full ".concat("connected"===e.status?"bg-green-400":"bg-red-400")})]}),(0,it.jsxs)("p",{className:"mt-2 text-xs text-gray-500",children:["Last seen: ",new Date(e.last_seen).toLocaleString()]})]},e.id)))})]})}),i&&(0,it.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,it.jsx)("div",{className:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-gray-800",children:(0,it.jsxs)("div",{className:"mt-3",children:[(0,it.jsx)("h3",{className:"text-lg font-medium text-white mb-4",children:"Create New Configuration"}),(0,it.jsxs)("div",{className:"space-y-4",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Node ID"}),(0,it.jsx)("input",{type:"text",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:c.node_id,onChange:e=>u(ot(ot({},c),{},{node_id:e.target.value}))})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Cluster Name"}),(0,it.jsx)("input",{type:"text",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:c.cluster_name,onChange:e=>u(ot(ot({},c),{},{cluster_name:e.target.value}))})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Configuration"}),(0,it.jsx)("div",{className:"mt-1 border border-gray-600 rounded-md overflow-hidden",children:(0,it.jsx)(ks,{height:"200px",defaultLanguage:"yaml",value:c.config,onChange:e=>u(ot(ot({},c),{},{config:e||""})),theme:"vs-dark",options:{minimap:{enabled:!1},scrollBeyondLastLine:!1,fontSize:14,fontFamily:"JetBrains Mono, monospace",lineNumbers:"on",folding:!0,wordWrap:"on"}})})]})]}),(0,it.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,it.jsx)("button",{onClick:()=>s(!1),className:"px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700",children:"Cancel"}),(0,it.jsx)("button",{onClick:()=>{e(mi(c)),u({node_id:"",cluster_name:"",config:"",version:"1.0.0"}),s(!1)},className:"px-4 py-2 bg-cyan-600 text-white rounded-md hover:bg-cyan-700",children:"Create"})]})]})})})]})},Es=Vr("autoscaler/fetchStatus",(async()=>(await vo.get("/v1/autoscaler/status")).data)),Cs=Vr("autoscaler/updateConfig",(async e=>(await vo.put("/v1/autoscaler/config",e)).data)),Os=Vr("autoscaler/fetchEvents",(async()=>(await vo.get("/v1/autoscaler/events")).data.events)),Ps=Vr("autoscaler/fetchMetrics",(async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"1h";return(await vo.get("/v1/autoscaler/metrics?range=".concat(e))).data})),_s=Dr({name:"autoscaler",initialState:{status:null,events:[],loading:!1,error:null,metrics:{cpu_usage:[],memory_usage:[],replica_count:[]}},reducers:{clearEvents:e=>{e.events=[]},addEvent:(e,t)=>{e.events.unshift(t.payload),e.events.length>100&&(e.events=e.events.slice(0,100))}},extraReducers:e=>{e.addCase(Es.pending,(e=>{e.loading=!0,e.error=null})).addCase(Es.fulfilled,((e,t)=>{e.loading=!1,e.status=t.payload})).addCase(Es.rejected,((e,t)=>{e.loading=!1,e.error=t.error.message||"Failed to fetch autoscaler status"})).addCase(Cs.pending,(e=>{e.loading=!0,e.error=null})).addCase(Cs.fulfilled,((e,t)=>{e.loading=!1,e.status=ot(ot({},e.status),t.payload)})).addCase(Cs.rejected,((e,t)=>{e.loading=!1,e.error=t.error.message||"Failed to update autoscaler config"})).addCase(Os.fulfilled,((e,t)=>{e.events=t.payload})).addCase(Ps.fulfilled,((e,t)=>{e.metrics=t.payload}))}}),{clearEvents:Rs,addEvent:Ls}=_s.actions,Ts=_s.reducer,As=["title","titleId"];function Ms(e,t){let{title:n,titleId:r}=e,l=mt(e,As);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-15 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a2.25 2.25 0 0 0 2.25-2.25V6.75a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 6.75v10.5a2.25 2.25 0 0 0 2.25 2.25Zm.75-12h9v9h-9v-9Z"}))}const Is=a.forwardRef(Ms),Ds=["title","titleId"];function Fs(e,t){let{title:n,titleId:r}=e,l=mt(e,Ds);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 6 9 12.75l4.286-4.286a11.948 11.948 0 0 1 4.306 6.43l.776 2.898m0 0 3.182-5.511m-3.182 5.51-5.511-3.181"}))}const zs=a.forwardRef(Fs),Us=()=>{const e=R(),{status:t,events:n,loading:r,error:l,metrics:o}=j((e=>e.autoscaler));(0,a.useEffect)((()=>{e(Es()),e(Ps("1h"))}),[e]);return(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h1",{className:"text-2xl font-bold text-white",children:"Autoscaler Dashboard"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Monitor and configure automatic scaling for your applications"})]}),(0,it.jsxs)("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4",children:[(0,it.jsx)("div",{className:"bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,it.jsx)("div",{className:"p-5",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(Lo,{className:"h-6 w-6 text-blue-400"})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400 truncate",children:"Current Replicas"}),(0,it.jsx)("dd",{className:"text-lg font-medium text-white",children:(null===t||void 0===t?void 0:t.current_replicas)||0})]})})]})})}),(0,it.jsx)("div",{className:"bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,it.jsx)("div",{className:"p-5",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(Ot,{className:"h-6 w-6 text-green-400"})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400 truncate",children:"Desired Replicas"}),(0,it.jsx)("dd",{className:"text-lg font-medium text-white",children:(null===t||void 0===t?void 0:t.desired_replicas)||0})]})})]})})}),(0,it.jsx)("div",{className:"bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,it.jsx)("div",{className:"p-5",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(Is,{className:"h-6 w-6 text-yellow-400"})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400 truncate",children:"CPU Utilization"}),(0,it.jsxs)("dd",{className:"text-lg font-medium text-white",children:[(null===t||void 0===t?void 0:t.current_cpu_utilization)||0,"%"]})]})})]})})}),(0,it.jsx)("div",{className:"bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,it.jsx)("div",{className:"p-5",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(null!==t&&void 0!==t&&t.enabled?"bg-green-400":"bg-red-400")})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400 truncate",children:"Status"}),(0,it.jsx)("dd",{className:"text-lg font-medium text-white",children:null!==t&&void 0!==t&&t.enabled?"Enabled":"Disabled"})]})})]})})})]}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white mb-4",children:"Autoscaler Configuration"}),t&&(0,it.jsxs)("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Min Replicas"}),(0,it.jsx)("div",{className:"mt-1",children:(0,it.jsx)("input",{type:"number",className:"block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:t.min_replicas,readOnly:!0})})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Max Replicas"}),(0,it.jsx)("div",{className:"mt-1",children:(0,it.jsx)("input",{type:"number",className:"block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:t.max_replicas,readOnly:!0})})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Target CPU Utilization (%)"}),(0,it.jsx)("div",{className:"mt-1",children:(0,it.jsx)("input",{type:"number",className:"block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:t.target_cpu_utilization,readOnly:!0})})]}),(0,it.jsx)("div",{className:"flex items-end",children:(0,it.jsxs)("button",{onClick:()=>{t&&e(Cs({enabled:!t.enabled}))},disabled:r,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 ".concat(t.enabled?"bg-red-600 hover:bg-red-700 focus:ring-red-500":"bg-green-600 hover:bg-green-700 focus:ring-green-500"),children:[t.enabled?"Disable":"Enable"," Autoscaler"]})})]}),l&&(0,it.jsx)("div",{className:"mt-4 bg-red-50 border border-red-200 rounded-md p-4",children:(0,it.jsx)("div",{className:"text-sm text-red-700",children:l})})]})}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white mb-4",children:"Recent Scaling Events"}),0===n.length?(0,it.jsxs)("div",{className:"text-center py-8",children:[(0,it.jsx)(Ot,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,it.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-300",children:"No scaling events"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Scaling events will appear here when they occur."})]}):(0,it.jsx)("div",{className:"space-y-4",children:n.slice(0,10).map(((e,t)=>(0,it.jsx)("div",{className:"border border-gray-600 rounded-lg p-4",children:(0,it.jsxs)("div",{className:"flex items-start justify-between",children:[(0,it.jsxs)("div",{className:"flex items-center space-x-3",children:[e.to_replicas>e.from_replicas?(0,it.jsx)(Ot,{className:"h-5 w-5 text-green-400"}):(0,it.jsx)(zs,{className:"h-5 w-5 text-red-400"}),(0,it.jsxs)("div",{children:[(0,it.jsxs)("p",{className:"text-sm font-medium text-white",children:["Scaled from ",e.from_replicas," to ",e.to_replicas," replicas"]}),(0,it.jsx)("p",{className:"text-sm text-gray-400",children:e.reason}),(0,it.jsx)("p",{className:"text-xs text-gray-500",children:e.message})]})]}),(0,it.jsx)("span",{className:"text-xs text-gray-400",children:new Date(e.timestamp).toLocaleString()})]})},t)))})]})})]})},Bs=Vr("audit/fetchEvents",(async e=>(await vo.post("/v1/audit/query",e)).data)),Ws=Dr({name:"audit",initialState:{events:[],loading:!1,error:null,totalCount:0,query:{}},reducers:{setQuery:(e,t)=>{e.query=t.payload},clearEvents:e=>{e.events=[],e.totalCount=0}},extraReducers:e=>{e.addCase(Bs.pending,(e=>{e.loading=!0,e.error=null})).addCase(Bs.fulfilled,((e,t)=>{e.loading=!1,e.events=t.payload.events,e.totalCount=t.payload.total_count})).addCase(Bs.rejected,((e,t)=>{e.loading=!1,e.error=t.error.message||"Failed to fetch audit events"}))}}),{setQuery:Vs,clearEvents:qs}=Ws.actions,$s=Ws.reducer,Hs=()=>{const e=R(),{events:t,loading:n,error:r,totalCount:l,query:o}=j((e=>e.audit)),[i,s]=(0,a.useState)({user_id:"",action:"",resource:"",start_time:"",end_time:""});(0,a.useEffect)((()=>{e(Bs({limit:50}))}),[e]);return(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h1",{className:"text-2xl font-bold text-white",children:"Audit Trail"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Tamper-proof audit logs powered by ImmuDB"})]}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white mb-4",children:"Filters"}),(0,it.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-3",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"User ID"}),(0,it.jsx)("input",{type:"text",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:i.user_id,onChange:e=>s(ot(ot({},i),{},{user_id:e.target.value})),placeholder:"Filter by user..."})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Action"}),(0,it.jsx)("input",{type:"text",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:i.action,onChange:e=>s(ot(ot({},i),{},{action:e.target.value})),placeholder:"Filter by action..."})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Resource"}),(0,it.jsx)("input",{type:"text",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:i.resource,onChange:e=>s(ot(ot({},i),{},{resource:e.target.value})),placeholder:"Filter by resource..."})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"Start Date"}),(0,it.jsx)("input",{type:"datetime-local",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:i.start_time,onChange:e=>s(ot(ot({},i),{},{start_time:e.target.value}))})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300",children:"End Date"}),(0,it.jsx)("input",{type:"datetime-local",className:"mt-1 block w-full px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-cyan-500",value:i.end_time,onChange:e=>s(ot(ot({},i),{},{end_time:e.target.value}))})]}),(0,it.jsx)("div",{className:"flex items-end",children:(0,it.jsxs)("button",{onClick:()=>{const t=ot(ot({},i),{},{limit:50});e(Vs(t)),e(Bs(t))},disabled:n,className:"w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 disabled:opacity-50",children:[(0,it.jsx)(vt,{className:"h-4 w-4 mr-2"}),"Search"]})})]})]})}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white",children:"Audit Events"}),(0,it.jsxs)("span",{className:"text-sm text-gray-400",children:[l," events found"]})]}),r&&(0,it.jsx)("div",{className:"mb-4 bg-red-50 border border-red-200 rounded-md p-4",children:(0,it.jsx)("div",{className:"text-sm text-red-700",children:r})}),n?(0,it.jsx)("div",{className:"flex items-center justify-center h-32",children:(0,it.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500"})}):(0,it.jsx)("div",{className:"space-y-4",children:0===t.length?(0,it.jsxs)("div",{className:"text-center py-12",children:[(0,it.jsx)(Rt,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,it.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-300",children:"No audit events found"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Try adjusting your search criteria."})]}):t.map((e=>{return(0,it.jsx)("div",{className:"border border-gray-600 rounded-lg p-4",children:(0,it.jsxs)("div",{className:"flex items-start justify-between",children:[(0,it.jsxs)("div",{className:"flex-1",children:[(0,it.jsxs)("div",{className:"flex items-center space-x-2",children:[e.success?(0,it.jsx)(Wo,{className:"h-5 w-5 text-green-400"}):(0,it.jsx)(oi,{className:"h-5 w-5 text-red-400"}),(0,it.jsx)("span",{className:"font-medium ".concat((t=e.action,t.includes("create")||t.includes("add")?"text-green-400":t.includes("delete")||t.includes("remove")?"text-red-400":t.includes("update")||t.includes("modify")?"text-yellow-400":"text-blue-400")),children:e.action}),(0,it.jsx)("span",{className:"text-gray-400",children:"on"}),(0,it.jsx)("span",{className:"text-white",children:e.resource})]}),(0,it.jsxs)("div",{className:"mt-2 text-sm text-gray-400",children:[(0,it.jsxs)("p",{children:["User: ",e.user_id]}),(0,it.jsxs)("p",{children:["Resource ID: ",e.resource_id]}),(0,it.jsxs)("p",{children:["IP: ",e.ip_address]}),e.error_msg&&(0,it.jsxs)("p",{className:"text-red-400",children:["Error: ",e.error_msg]})]}),Object.keys(e.details).length>0&&(0,it.jsx)("div",{className:"mt-3",children:(0,it.jsxs)("details",{className:"group",children:[(0,it.jsx)("summary",{className:"cursor-pointer text-sm text-cyan-400 hover:text-cyan-300",children:"View Details"}),(0,it.jsx)("pre",{className:"mt-2 text-xs text-gray-400 bg-gray-700 p-2 rounded overflow-x-auto",children:JSON.stringify(e.details,null,2)})]})})]}),(0,it.jsxs)("div",{className:"text-right text-sm text-gray-400",children:[(0,it.jsx)("p",{children:new Date(e.timestamp).toLocaleDateString()}),(0,it.jsx)("p",{children:new Date(e.timestamp).toLocaleTimeString()})]})]})},e.id);var t}))})]})})]})},Qs=Vr("database/fetchStats",(async()=>(await vo.get("/v1/database/stats")).data)),Ks=Vr("database/executeQuery",(async e=>(await vo.post("/v1/database/query",{query:e})).data)),Js=Dr({name:"database",initialState:{stats:null,loading:!1,error:null,queryResult:null,queryLoading:!1},reducers:{clearQueryResult:e=>{e.queryResult=null}},extraReducers:e=>{e.addCase(Qs.pending,(e=>{e.loading=!0,e.error=null})).addCase(Qs.fulfilled,((e,t)=>{e.loading=!1,e.stats=t.payload})).addCase(Qs.rejected,((e,t)=>{e.loading=!1,e.error=t.error.message||"Failed to fetch database stats"})).addCase(Ks.pending,(e=>{e.queryLoading=!0,e.error=null})).addCase(Ks.fulfilled,((e,t)=>{e.queryLoading=!1,e.queryResult=t.payload})).addCase(Ks.rejected,((e,t)=>{e.queryLoading=!1,e.error=t.error.message||"Query execution failed"}))}}),{clearQueryResult:Xs}=Js.actions,Zs=Js.reducer,Gs=["title","titleId"];function Ys(e,t){let{title:n,titleId:r}=e,l=mt(e,Gs);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))}const ec=a.forwardRef(Ys),tc=()=>{var e,t,n;const r=R(),{stats:l,loading:o,error:i,queryResult:s,queryLoading:c}=j((e=>e.database)),[u,d]=(0,a.useState)("");(0,a.useEffect)((()=>{r(Qs())}),[r]);return(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h1",{className:"text-2xl font-bold text-white",children:"Database Administration"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-400",children:"Monitor database health and execute queries"})]}),(0,it.jsxs)("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4",children:[(0,it.jsx)("div",{className:"bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,it.jsx)("div",{className:"p-5",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(At,{className:"h-6 w-6 text-blue-400"})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400 truncate",children:"Total Resources"}),(0,it.jsx)("dd",{className:"text-lg font-medium text-white",children:(null===l||void 0===l||null===(e=l.total_resources)||void 0===e?void 0:e.toLocaleString())||0})]})})]})})}),(0,it.jsx)("div",{className:"bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,it.jsx)("div",{className:"p-5",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(At,{className:"h-6 w-6 text-green-400"})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400 truncate",children:"Workflows"}),(0,it.jsx)("dd",{className:"text-lg font-medium text-white",children:(null===l||void 0===l||null===(t=l.total_workflows)||void 0===t?void 0:t.toLocaleString())||0})]})})]})})}),(0,it.jsx)("div",{className:"bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,it.jsx)("div",{className:"p-5",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(At,{className:"h-6 w-6 text-purple-400"})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400 truncate",children:"Envoy Configs"}),(0,it.jsx)("dd",{className:"text-lg font-medium text-white",children:(null===l||void 0===l||null===(n=l.total_envoy_configs)||void 0===n?void 0:n.toLocaleString())||0})]})})]})})}),(0,it.jsx)("div",{className:"bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,it.jsx)("div",{className:"p-5",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(At,{className:"h-6 w-6 text-yellow-400"})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400 truncate",children:"Cache Hit Rate"}),(0,it.jsx)("dd",{className:"text-lg font-medium text-white",children:null!==l&&void 0!==l&&l.cache_hit_rate?"".concat(l.cache_hit_rate,"%"):"N/A"})]})})]})})})]}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white mb-4",children:"Database Information"}),o?(0,it.jsx)("div",{className:"flex items-center justify-center h-32",children:(0,it.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500"})}):(0,it.jsxs)("dl",{className:"grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400",children:"Database Size"}),(0,it.jsx)("dd",{className:"mt-1 text-sm text-white",children:(null===l||void 0===l?void 0:l.database_size)||"Unknown"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-400",children:"Cache Size"}),(0,it.jsx)("dd",{className:"mt-1 text-sm text-white",children:(null===l||void 0===l?void 0:l.cache_size)||"Unknown"})]})]})]})}),(0,it.jsx)("div",{className:"bg-gray-800 shadow rounded-lg",children:(0,it.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,it.jsx)("h3",{className:"text-lg leading-6 font-medium text-white mb-4",children:"SQL Query Interface"}),(0,it.jsxs)("div",{className:"space-y-4",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"SQL Query"}),(0,it.jsx)("textarea",{className:"block w-full h-32 px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white font-mono text-sm focus:outline-none focus:ring-2 focus:ring-cyan-500",value:u,onChange:e=>d(e.target.value),placeholder:"Enter your SQL query here..."})]}),(0,it.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,it.jsxs)("button",{onClick:()=>{u.trim()&&r(Ks(u))},disabled:c||!u.trim(),className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 disabled:opacity-50",children:[(0,it.jsx)(ci,{className:"h-4 w-4 mr-2"}),c?"Executing...":"Execute Query"]}),s&&(0,it.jsxs)("button",{onClick:()=>{r(Xs())},className:"inline-flex items-center px-4 py-2 border border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-300 bg-gray-700 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500",children:[(0,it.jsx)(ec,{className:"h-4 w-4 mr-2"}),"Clear Result"]})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Sample Queries"}),(0,it.jsx)("div",{className:"space-y-2",children:["SELECT * FROM resource_cache LIMIT 10","SELECT provider, COUNT(*) as count FROM resource_cache GROUP BY provider",'SELECT * FROM workflow_executions WHERE status = "running"',"SELECT * FROM envoy_configs ORDER BY updated_at DESC LIMIT 5"].map(((e,t)=>(0,it.jsx)("button",{onClick:()=>d(e),className:"block w-full text-left px-3 py-2 text-sm text-gray-300 bg-gray-700 hover:bg-gray-600 rounded border border-gray-600",children:e},t)))})]})]}),i&&(0,it.jsx)("div",{className:"mt-4 bg-red-50 border border-red-200 rounded-md p-4",children:(0,it.jsx)("div",{className:"text-sm text-red-700",children:i})}),s&&(0,it.jsxs)("div",{className:"mt-6",children:[(0,it.jsx)("h4",{className:"text-md font-medium text-white mb-3",children:"Query Results"}),(0,it.jsx)("div",{className:"bg-gray-700 rounded-md p-4 overflow-x-auto",children:(0,it.jsx)("pre",{className:"text-sm text-gray-300",children:JSON.stringify(s,null,2)})})]})]})})]})},nc=()=>{const{user:e}=ut(),[t,n]=(0,a.useState)(null),[r,l]=(0,a.useState)([]),[o,i]=(0,a.useState)(!0);(0,a.useEffect)((()=>{s()}),[]);const s=async()=>{try{const e=await fetch("/v1/admin/stats",{credentials:"include"});if(e.ok){const t=await e.json();n(t)}const t=await fetch("/v1/admin/activity",{credentials:"include"});if(t.ok){const e=await t.json();l(e.activities||[])}}catch(e){console.error("Failed to fetch admin data:",e)}finally{i(!1)}},c=e=>{switch(e){case"user_login":return(0,it.jsx)(Dt,{className:"h-5 w-5 text-blue-500"});case"user_created":return(0,it.jsx)(Dt,{className:"h-5 w-5 text-green-500"});case"connection_added":return(0,it.jsx)(Lo,{className:"h-5 w-5 text-purple-500"});case"workflow_executed":return(0,it.jsx)(jt,{className:"h-5 w-5 text-orange-500"});default:return(0,it.jsx)(ri,{className:"h-5 w-5 text-gray-500"})}},u=e=>{switch(e){case"user_login":return"bg-blue-50 border-blue-200";case"user_created":return"bg-green-50 border-green-200";case"connection_added":return"bg-purple-50 border-purple-200";case"workflow_executed":return"bg-orange-50 border-orange-200";default:return"bg-gray-50 border-gray-200"}};return o?(0,it.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,it.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,it.jsxs)("div",{className:"flex items-center justify-between",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Admin Dashboard"}),(0,it.jsxs)("p",{className:"text-gray-600",children:["Welcome back, ",null===e||void 0===e?void 0:e.name]})]}),(0,it.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,it.jsx)(Rt,{className:"h-8 w-8 text-green-500"}),(0,it.jsx)("span",{className:"text-sm font-medium text-green-600",children:"System Healthy"})]})]})}),(0,it.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,it.jsxs)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:[(0,it.jsx)("div",{className:"p-5",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(Dt,{className:"h-6 w-6 text-gray-400"})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Total Users"}),(0,it.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:(null===t||void 0===t?void 0:t.totalUsers)||0})]})})]})}),(0,it.jsx)("div",{className:"bg-gray-50 px-5 py-3",children:(0,it.jsxs)("div",{className:"text-sm",children:[(0,it.jsx)("span",{className:"text-green-600 font-medium",children:(null===t||void 0===t?void 0:t.activeUsers)||0}),(0,it.jsx)("span",{className:"text-gray-500",children:" active"})]})})]}),(0,it.jsxs)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:[(0,it.jsx)("div",{className:"p-5",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(Lo,{className:"h-6 w-6 text-gray-400"})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Connections"}),(0,it.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:(null===t||void 0===t?void 0:t.totalConnections)||0})]})})]})}),(0,it.jsx)("div",{className:"bg-gray-50 px-5 py-3",children:(0,it.jsxs)("div",{className:"text-sm",children:[(0,it.jsx)("span",{className:"text-green-600 font-medium",children:(null===t||void 0===t?void 0:t.activeConnections)||0}),(0,it.jsx)("span",{className:"text-gray-500",children:" active"})]})})]}),(0,it.jsxs)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:[(0,it.jsx)("div",{className:"p-5",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(jt,{className:"h-6 w-6 text-gray-400"})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Workflows"}),(0,it.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:(null===t||void 0===t?void 0:t.totalWorkflows)||0})]})})]})}),(0,it.jsx)("div",{className:"bg-gray-50 px-5 py-3",children:(0,it.jsxs)("div",{className:"text-sm",children:[(0,it.jsx)("span",{className:"text-blue-600 font-medium",children:(null===t||void 0===t?void 0:t.runningWorkflows)||0}),(0,it.jsx)("span",{className:"text-gray-500",children:" running"})]})})]}),(0,it.jsxs)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:[(0,it.jsx)("div",{className:"p-5",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0",children:(0,it.jsx)(gt,{className:"h-6 w-6 text-gray-400"})}),(0,it.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,it.jsxs)("dl",{children:[(0,it.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"System Uptime"}),(0,it.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:(null===t||void 0===t?void 0:t.systemUptime)||"N/A"})]})})]})}),(0,it.jsx)("div",{className:"bg-gray-50 px-5 py-3",children:(0,it.jsx)("div",{className:"text-sm",children:(0,it.jsxs)("span",{className:"text-gray-500",children:["Last backup: ",(null===t||void 0===t?void 0:t.lastBackup)||"N/A"]})})})]})]}),(0,it.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,it.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,it.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Recent Activity"})}),(0,it.jsx)("div",{className:"divide-y divide-gray-200",children:r.length>0?r.map((e=>(0,it.jsx)("div",{className:"px-6 py-4",children:(0,it.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,it.jsx)("div",{className:"flex-shrink-0 p-2 rounded-full border ".concat(u(e.type)),children:c(e.type)}),(0,it.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,it.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.description}),(0,it.jsxs)("p",{className:"text-sm text-gray-500",children:["by ",e.user]})]}),(0,it.jsx)("div",{className:"flex-shrink-0 text-sm text-gray-500",children:e.timestamp})]})},e.id))):(0,it.jsxs)("div",{className:"px-6 py-8 text-center",children:[(0,it.jsx)(ri,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,it.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No recent activity"}),(0,it.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Activity will appear here as users interact with the system."})]})})]}),(0,it.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:[(0,it.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Quick Actions"}),(0,it.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,it.jsxs)("button",{className:"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50",children:[(0,it.jsx)(Dt,{className:"h-5 w-5 mr-2"}),"Manage Users"]}),(0,it.jsxs)("button",{className:"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50",children:[(0,it.jsx)(Lo,{className:"h-5 w-5 mr-2"}),"View Connections"]}),(0,it.jsxs)("button",{className:"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50",children:[(0,it.jsx)(jt,{className:"h-5 w-5 mr-2"}),"System Settings"]})]})]})]})},rc=["title","titleId"];function ac(e,t){let{title:n,titleId:r}=e,l=mt(e,rc);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}const lc=a.forwardRef(ac),oc=["title","titleId"];function ic(e,t){let{title:n,titleId:r}=e,l=mt(e,oc);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"}))}const sc=a.forwardRef(ic),cc=()=>{const{user:e}=ut(),[t,n]=(0,a.useState)([]),[r,l]=(0,a.useState)(!0),[o,i]=(0,a.useState)(""),[s,c]=(0,a.useState)(!1),[u,d]=(0,a.useState)(!1),[f,m]=(0,a.useState)(!1),[h,p]=(0,a.useState)(null),[g,x]=(0,a.useState)({username:"",email:"",name:"",password:"",roles:["viewer"],groups:["cainuro-users"]}),[y,v]=(0,a.useState)({email:"",name:"",roles:["viewer"],groups:["cainuro-users"],active:!0,password:""});(0,a.useEffect)((()=>{b()}),[]);const b=async()=>{try{const e=await fetch("/v1/users",{credentials:"include"});if(e.ok){const t=await e.json();n(t.users||[])}}catch(e){console.error("Failed to fetch users:",e)}finally{l(!1)}},w=t.filter((e=>e.username.toLowerCase().includes(o.toLowerCase())||e.email.toLowerCase().includes(o.toLowerCase())||e.name.toLowerCase().includes(o.toLowerCase()))),j=e=>{switch(e){case"admin":return"bg-red-100 text-red-800";case"operator":return"bg-blue-100 text-blue-800";case"viewer":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}};return r?(0,it.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,it.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,it.jsxs)("div",{className:"flex items-center justify-between",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"User Management"}),(0,it.jsx)("p",{className:"text-gray-600",children:"Manage system users and their permissions"})]}),(0,it.jsxs)("button",{onClick:()=>c(!0),className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700",children:[(0,it.jsx)(bi,{className:"h-4 w-4 mr-2"}),"Create User"]})]})}),(0,it.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,it.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,it.jsxs)("div",{className:"flex-1 relative",children:[(0,it.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,it.jsx)(vt,{className:"h-5 w-5 text-gray-400"})}),(0,it.jsx)("input",{type:"text",placeholder:"Search users...",value:o,onChange:e=>i(e.target.value),className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"})]}),(0,it.jsxs)("div",{className:"text-sm text-gray-500",children:[w.length," of ",t.length," users"]})]})}),(0,it.jsxs)("div",{className:"bg-white shadow rounded-lg overflow-hidden",children:[(0,it.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,it.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Users"})}),(0,it.jsx)("div",{className:"overflow-x-auto",children:(0,it.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,it.jsx)("thead",{className:"bg-gray-50",children:(0,it.jsxs)("tr",{children:[(0,it.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,it.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Roles"}),(0,it.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,it.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Last Login"}),(0,it.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,it.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:w.map((t=>(0,it.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,it.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("div",{className:"flex-shrink-0 h-10 w-10",children:(0,it.jsx)("div",{className:"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center",children:(0,it.jsx)("span",{className:"text-sm font-medium text-gray-700",children:t.name.split(" ").map((e=>e[0])).join("").toUpperCase()})})}),(0,it.jsxs)("div",{className:"ml-4",children:[(0,it.jsx)("div",{className:"text-sm font-medium text-gray-900",children:t.name}),(0,it.jsx)("div",{className:"text-sm text-gray-500",children:t.email}),(0,it.jsxs)("div",{className:"text-xs text-gray-400",children:["@",t.username]})]})]})}),(0,it.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,it.jsx)("div",{className:"flex flex-wrap gap-1",children:t.roles.map((e=>(0,it.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(j(e)),children:e},e)))})}),(0,it.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,it.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(t.active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:t.active?(0,it.jsxs)(it.Fragment,{children:[(0,it.jsx)(Wo,{className:"h-3 w-3 mr-1"}),"Active"]}):(0,it.jsxs)(it.Fragment,{children:[(0,it.jsx)(oi,{className:"h-3 w-3 mr-1"}),"Inactive"]})})}),(0,it.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:t.last_login?new Date(t.last_login).toLocaleDateString():"Never"}),(0,it.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,it.jsxs)("div",{className:"flex space-x-2",children:[(0,it.jsx)("button",{onClick:()=>p(t),className:"text-blue-600 hover:text-blue-900",title:"View User",children:(0,it.jsx)(lc,{className:"h-4 w-4"})}),(0,it.jsx)("button",{onClick:()=>(e=>{p(e),v({email:e.email,name:e.name,roles:e.roles,groups:e.groups,active:e.active||!0,password:""}),d(!0)})(t),className:"text-yellow-600 hover:text-yellow-900",title:"Edit User",children:(0,it.jsx)(sc,{className:"h-4 w-4"})}),t.username!==(null===e||void 0===e?void 0:e.username)&&t.id!==(null===e||void 0===e?void 0:e.id)&&(0,it.jsx)("button",{onClick:()=>(e=>{p(e),m(!0)})(t),className:"text-red-600 hover:text-red-900",title:"Delete User",children:(0,it.jsx)(ec,{className:"h-4 w-4"})})]})})]},t.id)))})]})})]}),s&&(0,it.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,it.jsx)("div",{className:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white",children:(0,it.jsxs)("div",{className:"mt-3",children:[(0,it.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Create New User"}),(0,it.jsxs)("form",{onSubmit:async e=>{e.preventDefault();try{(await fetch("/v1/users",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(g)})).ok&&(c(!1),x({username:"",email:"",name:"",password:"",roles:["viewer"],groups:["cainuro-users"]}),b())}catch(t){console.error("Failed to create user:",t)}},className:"space-y-4",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Username"}),(0,it.jsx)("input",{type:"text",required:!0,value:g.username,onChange:e=>x(ot(ot({},g),{},{username:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Email"}),(0,it.jsx)("input",{type:"email",required:!0,value:g.email,onChange:e=>x(ot(ot({},g),{},{email:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Full Name"}),(0,it.jsx)("input",{type:"text",required:!0,value:g.name,onChange:e=>x(ot(ot({},g),{},{name:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,it.jsx)("input",{type:"password",required:!0,value:g.password,onChange:e=>x(ot(ot({},g),{},{password:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,it.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,it.jsx)("button",{type:"button",onClick:()=>c(!1),className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Cancel"}),(0,it.jsx)("button",{type:"submit",className:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700",children:"Create User"})]})]})]})})}),u&&h&&(0,it.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,it.jsx)("div",{className:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white",children:(0,it.jsxs)("div",{className:"mt-3",children:[(0,it.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:["Edit User: ",h.name]}),(0,it.jsxs)("form",{onSubmit:async e=>{if(e.preventDefault(),h)try{const e={email:y.email,name:y.name,roles:y.roles,groups:y.groups,active:y.active};y.password&&(e.password=y.password);(await fetch("/v1/users/".concat(h.id),{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(e)})).ok&&(d(!1),p(null),b())}catch(t){console.error("Failed to update user:",t)}},className:"space-y-4",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Email"}),(0,it.jsx)("input",{type:"email",required:!0,value:y.email,onChange:e=>v(ot(ot({},y),{},{email:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Full Name"}),(0,it.jsx)("input",{type:"text",required:!0,value:y.name,onChange:e=>v(ot(ot({},y),{},{name:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Roles"}),(0,it.jsxs)("select",{multiple:!0,value:y.roles,onChange:e=>v(ot(ot({},y),{},{roles:Array.from(e.target.selectedOptions,(e=>e.value))})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,it.jsx)("option",{value:"admin",children:"Admin"}),(0,it.jsx)("option",{value:"operator",children:"Operator"}),(0,it.jsx)("option",{value:"viewer",children:"Viewer"})]})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"New Password (optional)"}),(0,it.jsx)("input",{type:"password",value:y.password,onChange:e=>v(ot(ot({},y),{},{password:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Leave blank to keep current password"})]}),(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("input",{type:"checkbox",checked:y.active,onChange:e=>v(ot(ot({},y),{},{active:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,it.jsx)("label",{className:"ml-2 block text-sm text-gray-900",children:"Active User"})]}),(0,it.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,it.jsx)("button",{type:"button",onClick:()=>d(!1),className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Cancel"}),(0,it.jsx)("button",{type:"submit",className:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700",children:"Update User"})]})]})]})})}),f&&h&&(0,it.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,it.jsx)("div",{className:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white",children:(0,it.jsxs)("div",{className:"mt-3",children:[(0,it.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Delete User"}),(0,it.jsxs)("p",{className:"text-sm text-gray-500 mb-4",children:["Are you sure you want to delete ",(0,it.jsx)("strong",{children:h.name})," (",h.email,")? This action cannot be undone."]}),(0,it.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,it.jsx)("button",{onClick:()=>m(!1),className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Cancel"}),(0,it.jsx)("button",{onClick:async()=>{if(h)try{(await fetch("/v1/users/".concat(h.id),{method:"DELETE",credentials:"include"})).ok&&(m(!1),p(null),b())}catch(e){console.error("Failed to delete user:",e)}},className:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700",children:"Delete User"})]})]})})})]})},uc=()=>{var e,t,n,r,l,o,i;const{user:s}=ut(),[c,u]=(0,a.useState)("profile"),[d,f]=(0,a.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[m,h]=(0,a.useState)({theme:(null===s||void 0===s||null===(e=s.preferences)||void 0===e?void 0:e.theme)||"dark",timezone:(null===s||void 0===s||null===(t=s.preferences)||void 0===t?void 0:t.timezone)||"UTC",language:(null===s||void 0===s||null===(n=s.preferences)||void 0===n?void 0:n.language)||"en",notifications:!0}),[p,g]=(0,a.useState)(null),[x,y]=(0,a.useState)(!1),v=[{id:"profile",name:"Profile",icon:Ut},{id:"security",name:"Security",icon:Rt},{id:"preferences",name:"Preferences",icon:jt}];return(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,it.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,it.jsx)("div",{className:"h-16 w-16 rounded-full bg-gray-300 flex items-center justify-center",children:(0,it.jsx)("span",{className:"text-xl font-medium text-gray-700",children:null===s||void 0===s||null===(r=s.name)||void 0===r?void 0:r.split(" ").map((e=>e[0])).join("").toUpperCase()})}),(0,it.jsxs)("div",{children:[(0,it.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:null===s||void 0===s?void 0:s.name}),(0,it.jsx)("p",{className:"text-gray-600",children:null===s||void 0===s?void 0:s.email}),(0,it.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,it.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[(0,it.jsx)(Wo,{className:"h-3 w-3 mr-1"}),"Active"]}),null===s||void 0===s||null===(l=s.roles)||void 0===l?void 0:l.map((e=>(0,it.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:e},e)))]})]})]})}),(0,it.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,it.jsx)("div",{className:"border-b border-gray-200",children:(0,it.jsx)("nav",{className:"-mb-px flex space-x-8 px-6",children:v.map((e=>{const t=e.icon;return(0,it.jsxs)("button",{onClick:()=>u(e.id),className:"py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ".concat(c===e.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[(0,it.jsx)(t,{className:"h-4 w-4"}),(0,it.jsx)("span",{children:e.name})]},e.id)}))})}),(0,it.jsxs)("div",{className:"p-6",children:["profile"===c&&(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Profile Information"}),(0,it.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Username"}),(0,it.jsx)("div",{className:"mt-1 text-sm text-gray-900",children:null===s||void 0===s?void 0:s.username})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Email"}),(0,it.jsx)("div",{className:"mt-1 text-sm text-gray-900",children:null===s||void 0===s?void 0:s.email})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Full Name"}),(0,it.jsx)("div",{className:"mt-1 text-sm text-gray-900",children:null===s||void 0===s?void 0:s.name})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"User ID"}),(0,it.jsx)("div",{className:"mt-1 text-sm text-gray-900 font-mono",children:null===s||void 0===s?void 0:s.id})]})]})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Roles & Permissions"}),(0,it.jsxs)("div",{className:"space-y-4",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Roles"}),(0,it.jsx)("div",{className:"mt-1 flex flex-wrap gap-2",children:null===s||void 0===s||null===(o=s.roles)||void 0===o?void 0:o.map((e=>(0,it.jsx)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800",children:e},e)))})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Groups"}),(0,it.jsx)("div",{className:"mt-1 flex flex-wrap gap-2",children:null===s||void 0===s||null===(i=s.groups)||void 0===i?void 0:i.map((e=>(0,it.jsx)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800",children:e},e)))})]})]})]})]}),"security"===c&&(0,it.jsx)("div",{className:"space-y-6",children:(0,it.jsxs)("div",{children:[(0,it.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Change Password"}),p&&(0,it.jsx)("div",{className:"mb-4 p-4 rounded-md ".concat("success"===p.type?"bg-green-50 text-green-800":"bg-red-50 text-red-800"),children:(0,it.jsxs)("div",{className:"flex",children:["success"===p.type?(0,it.jsx)(Wo,{className:"h-5 w-5 mr-2"}):(0,it.jsx)($o,{className:"h-5 w-5 mr-2"}),p.text]})}),(0,it.jsxs)("form",{onSubmit:async e=>{if(e.preventDefault(),d.newPassword===d.confirmPassword)if(d.newPassword.length<4)g({type:"error",text:"Password must be at least 4 characters long"});else{y(!0);try{const e=await fetch("/v1/auth/change-password",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({current_password:d.currentPassword,new_password:d.newPassword})});if(e.ok)g({type:"success",text:"Password changed successfully"}),f({currentPassword:"",newPassword:"",confirmPassword:""});else{const t=await e.json();g({type:"error",text:t.error||"Failed to change password"})}}catch(t){g({type:"error",text:"Failed to change password"})}finally{y(!1)}}else g({type:"error",text:"New passwords do not match"})},className:"space-y-4 max-w-md",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Current Password"}),(0,it.jsx)("input",{type:"password",required:!0,value:d.currentPassword,onChange:e=>f(ot(ot({},d),{},{currentPassword:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"New Password"}),(0,it.jsx)("input",{type:"password",required:!0,value:d.newPassword,onChange:e=>f(ot(ot({},d),{},{newPassword:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Confirm New Password"}),(0,it.jsx)("input",{type:"password",required:!0,value:d.confirmPassword,onChange:e=>f(ot(ot({},d),{},{confirmPassword:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,it.jsxs)("button",{type:"submit",disabled:x,className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50",children:[(0,it.jsx)(zo,{className:"h-4 w-4 mr-2"}),x?"Changing...":"Change Password"]})]})]})}),"preferences"===c&&(0,it.jsx)("div",{className:"space-y-6",children:(0,it.jsxs)("div",{children:[(0,it.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"User Preferences"}),(0,it.jsxs)("form",{onSubmit:async e=>{e.preventDefault(),console.log("Updating preferences:",m)},className:"space-y-4 max-w-md",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Theme"}),(0,it.jsxs)("select",{value:m.theme,onChange:e=>h(ot(ot({},m),{},{theme:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,it.jsx)("option",{value:"light",children:"Light"}),(0,it.jsx)("option",{value:"dark",children:"Dark"}),(0,it.jsx)("option",{value:"auto",children:"Auto"})]})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Timezone"}),(0,it.jsxs)("select",{value:m.timezone,onChange:e=>h(ot(ot({},m),{},{timezone:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,it.jsx)("option",{value:"UTC",children:"UTC"}),(0,it.jsx)("option",{value:"America/New_York",children:"Eastern Time"}),(0,it.jsx)("option",{value:"America/Chicago",children:"Central Time"}),(0,it.jsx)("option",{value:"America/Denver",children:"Mountain Time"}),(0,it.jsx)("option",{value:"America/Los_Angeles",children:"Pacific Time"})]})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Language"}),(0,it.jsxs)("select",{value:m.language,onChange:e=>h(ot(ot({},m),{},{language:e.target.value})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,it.jsx)("option",{value:"en",children:"English"}),(0,it.jsx)("option",{value:"es",children:"Spanish"}),(0,it.jsx)("option",{value:"fr",children:"French"}),(0,it.jsx)("option",{value:"de",children:"German"})]})]}),(0,it.jsxs)("button",{type:"submit",className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700",children:[(0,it.jsx)(jt,{className:"h-4 w-4 mr-2"}),"Update Preferences"]})]})]})})]})]})]})},dc=["title","titleId"];function fc(e,t){let{title:n,titleId:r}=e,l=mt(e,dc);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"}))}const mc=a.forwardRef(fc),hc=()=>{const{user:e}=ut(),[t,n]=(0,a.useState)("authentication"),[r,l]=(0,a.useState)({authentication:{sessionTimeout:24,maxLoginAttempts:5,passwordMinLength:4,requireMFA:!1},security:{enableAuditLogging:!0,enableRateLimiting:!0,allowedOrigins:["http://localhost:3000","http://localhost:8080"],encryptionEnabled:!0},notifications:{emailEnabled:!1,slackEnabled:!1,webhookUrl:""},system:{logLevel:"info",maxConnections:1e3,backupEnabled:!0,backupInterval:"daily"}}),[o,i]=(0,a.useState)(!1),[s,c]=(0,a.useState)(null);(0,a.useEffect)((()=>{u()}),[]);const u=async()=>{try{const e=await fetch("/v1/admin/settings",{credentials:"include"});if(e.ok){const t=await e.json();l(t.settings||r)}}catch(e){console.error("Failed to fetch settings:",e)}},d=[{id:"authentication",name:"Authentication",icon:Rt},{id:"security",name:"Security",icon:zo},{id:"notifications",name:"Notifications",icon:mc},{id:"system",name:"System",icon:Lo}];return(0,it.jsxs)("div",{className:"space-y-6",children:[(0,it.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,it.jsxs)("div",{className:"flex items-center justify-between",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"System Settings"}),(0,it.jsx)("p",{className:"text-gray-600",children:"Configure system-wide settings and preferences"})]}),(0,it.jsxs)("button",{onClick:async()=>{i(!0);try{(await fetch("/v1/admin/settings",{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({settings:r})})).ok?c({type:"success",text:"Settings saved successfully"}):c({type:"error",text:"Failed to save settings"})}catch(e){c({type:"error",text:"Failed to save settings"})}finally{i(!1)}},disabled:o,className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50",children:[(0,it.jsx)(jt,{className:"h-4 w-4 mr-2"}),o?"Saving...":"Save Settings"]})]})}),s&&(0,it.jsx)("div",{className:"p-4 rounded-md ".concat("success"===s.type?"bg-green-50 text-green-800":"bg-red-50 text-red-800"),children:(0,it.jsxs)("div",{className:"flex",children:["success"===s.type?(0,it.jsx)(Wo,{className:"h-5 w-5 mr-2"}):(0,it.jsx)($o,{className:"h-5 w-5 mr-2"}),s.text]})}),(0,it.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,it.jsx)("div",{className:"border-b border-gray-200",children:(0,it.jsx)("nav",{className:"-mb-px flex space-x-8 px-6",children:d.map((e=>{const r=e.icon;return(0,it.jsxs)("button",{onClick:()=>n(e.id),className:"py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ".concat(t===e.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[(0,it.jsx)(r,{className:"h-4 w-4"}),(0,it.jsx)("span",{children:e.name})]},e.id)}))})}),(0,it.jsxs)("div",{className:"p-6",children:["authentication"===t&&(0,it.jsx)("div",{className:"space-y-6",children:(0,it.jsxs)("div",{children:[(0,it.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Authentication Settings"}),(0,it.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Session Timeout (hours)"}),(0,it.jsx)("input",{type:"number",value:r.authentication.sessionTimeout,onChange:e=>l(ot(ot({},r),{},{authentication:ot(ot({},r.authentication),{},{sessionTimeout:parseInt(e.target.value)})})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Max Login Attempts"}),(0,it.jsx)("input",{type:"number",value:r.authentication.maxLoginAttempts,onChange:e=>l(ot(ot({},r),{},{authentication:ot(ot({},r.authentication),{},{maxLoginAttempts:parseInt(e.target.value)})})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Password Min Length"}),(0,it.jsx)("input",{type:"number",value:r.authentication.passwordMinLength,onChange:e=>l(ot(ot({},r),{},{authentication:ot(ot({},r.authentication),{},{passwordMinLength:parseInt(e.target.value)})})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("input",{type:"checkbox",checked:r.authentication.requireMFA,onChange:e=>l(ot(ot({},r),{},{authentication:ot(ot({},r.authentication),{},{requireMFA:e.target.checked})})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,it.jsx)("label",{className:"ml-2 block text-sm text-gray-900",children:"Require Multi-Factor Authentication"})]})]})]})}),"security"===t&&(0,it.jsx)("div",{className:"space-y-6",children:(0,it.jsxs)("div",{children:[(0,it.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Security Settings"}),(0,it.jsxs)("div",{className:"space-y-4",children:[(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("input",{type:"checkbox",checked:r.security.enableAuditLogging,onChange:e=>l(ot(ot({},r),{},{security:ot(ot({},r.security),{},{enableAuditLogging:e.target.checked})})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,it.jsx)("label",{className:"ml-2 block text-sm text-gray-900",children:"Enable Audit Logging"})]}),(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("input",{type:"checkbox",checked:r.security.enableRateLimiting,onChange:e=>l(ot(ot({},r),{},{security:ot(ot({},r.security),{},{enableRateLimiting:e.target.checked})})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,it.jsx)("label",{className:"ml-2 block text-sm text-gray-900",children:"Enable Rate Limiting"})]}),(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("input",{type:"checkbox",checked:r.security.encryptionEnabled,onChange:e=>l(ot(ot({},r),{},{security:ot(ot({},r.security),{},{encryptionEnabled:e.target.checked})})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,it.jsx)("label",{className:"ml-2 block text-sm text-gray-900",children:"Enable Data Encryption"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Allowed Origins (one per line)"}),(0,it.jsx)("textarea",{value:r.security.allowedOrigins.join("\n"),onChange:e=>l(ot(ot({},r),{},{security:ot(ot({},r.security),{},{allowedOrigins:e.target.value.split("\n").filter((e=>e.trim()))})})),rows:4,className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]})]})}),"notifications"===t&&(0,it.jsx)("div",{className:"space-y-6",children:(0,it.jsxs)("div",{children:[(0,it.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Notification Settings"}),(0,it.jsxs)("div",{className:"space-y-4",children:[(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("input",{type:"checkbox",checked:r.notifications.emailEnabled,onChange:e=>l(ot(ot({},r),{},{notifications:ot(ot({},r.notifications),{},{emailEnabled:e.target.checked})})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,it.jsx)("label",{className:"ml-2 block text-sm text-gray-900",children:"Enable Email Notifications"})]}),(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("input",{type:"checkbox",checked:r.notifications.slackEnabled,onChange:e=>l(ot(ot({},r),{},{notifications:ot(ot({},r.notifications),{},{slackEnabled:e.target.checked})})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,it.jsx)("label",{className:"ml-2 block text-sm text-gray-900",children:"Enable Slack Notifications"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Webhook URL"}),(0,it.jsx)("input",{type:"url",value:r.notifications.webhookUrl,onChange:e=>l(ot(ot({},r),{},{notifications:ot(ot({},r.notifications),{},{webhookUrl:e.target.value})})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"https://hooks.slack.com/services/..."})]})]})]})}),"system"===t&&(0,it.jsx)("div",{className:"space-y-6",children:(0,it.jsxs)("div",{children:[(0,it.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"System Settings"}),(0,it.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Log Level"}),(0,it.jsxs)("select",{value:r.system.logLevel,onChange:e=>l(ot(ot({},r),{},{system:ot(ot({},r.system),{},{logLevel:e.target.value})})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,it.jsx)("option",{value:"debug",children:"Debug"}),(0,it.jsx)("option",{value:"info",children:"Info"}),(0,it.jsx)("option",{value:"warn",children:"Warning"}),(0,it.jsx)("option",{value:"error",children:"Error"})]})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Max Connections"}),(0,it.jsx)("input",{type:"number",value:r.system.maxConnections,onChange:e=>l(ot(ot({},r),{},{system:ot(ot({},r.system),{},{maxConnections:parseInt(e.target.value)})})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,it.jsxs)("div",{children:[(0,it.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Backup Interval"}),(0,it.jsxs)("select",{value:r.system.backupInterval,onChange:e=>l(ot(ot({},r),{},{system:ot(ot({},r.system),{},{backupInterval:e.target.value})})),className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,it.jsx)("option",{value:"hourly",children:"Hourly"}),(0,it.jsx)("option",{value:"daily",children:"Daily"}),(0,it.jsx)("option",{value:"weekly",children:"Weekly"}),(0,it.jsx)("option",{value:"monthly",children:"Monthly"})]})]}),(0,it.jsxs)("div",{className:"flex items-center",children:[(0,it.jsx)("input",{type:"checkbox",checked:r.system.backupEnabled,onChange:e=>l(ot(ot({},r),{},{system:ot(ot({},r.system),{},{backupEnabled:e.target.checked})})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,it.jsx)("label",{className:"ml-2 block text-sm text-gray-900",children:"Enable Automatic Backups"})]})]})]})})]})]})]})};function pc(){const{isAuthenticated:e,isLoading:t}=ut();return t?(0,it.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center",children:(0,it.jsxs)("div",{className:"text-center",children:[(0,it.jsx)("div",{className:"animate-spin rounded-full h-16 w-16 border-b-2 border-white mx-auto mb-4"}),(0,it.jsx)("div",{className:"text-white text-lg",children:"Loading CAINuro Orchestrator..."})]})}):e?(0,it.jsx)("div",{className:"min-h-screen bg-theme-bg-container",children:(0,it.jsx)(Yt,{children:(0,it.jsxs)(qe,{children:[(0,it.jsx)(We,{path:"/",element:(0,it.jsx)(dt,{children:(0,it.jsx)(tn,{})})}),(0,it.jsx)(We,{path:"/dashboard",element:(0,it.jsx)(dt,{children:(0,it.jsx)(tn,{})})}),(0,it.jsx)(We,{path:"/search",element:(0,it.jsx)(dt,{requiredPermission:{action:"READ",resource:"discovery"},children:(0,it.jsx)(Io,{})})}),(0,it.jsx)(We,{path:"/discovery",element:(0,it.jsx)(dt,{requiredPermission:{action:"READ",resource:"discovery"},children:(0,it.jsx)(Ho,{})})}),(0,it.jsx)(We,{path:"/workflows",element:(0,it.jsx)(dt,{requiredPermission:{action:"EXECUTE",resource:"workflows"},children:(0,it.jsx)(ui,{})})}),(0,it.jsx)(We,{path:"/envoy",element:(0,it.jsx)(dt,{requiredPermission:{action:"READ",resource:"envoy"},children:(0,it.jsx)(Ss,{})})}),(0,it.jsx)(We,{path:"/autoscaler",element:(0,it.jsx)(dt,{requiredPermission:{action:"READ",resource:"autoscaler"},children:(0,it.jsx)(Us,{})})}),(0,it.jsx)(We,{path:"/audit",element:(0,it.jsx)(dt,{requiredPermission:{action:"READ",resource:"audit"},children:(0,it.jsx)(Hs,{})})}),(0,it.jsx)(We,{path:"/database",element:(0,it.jsx)(dt,{requiredPermission:{action:"READ",resource:"database"},children:(0,it.jsx)(tc,{})})}),(0,it.jsx)(We,{path:"/admin",element:(0,it.jsx)(dt,{requiredPermission:{action:"READ",resource:"admin"},children:(0,it.jsx)(nc,{})})}),(0,it.jsx)(We,{path:"/admin/users",element:(0,it.jsx)(dt,{requiredPermission:{action:"READ",resource:"admin"},children:(0,it.jsx)(cc,{})})}),(0,it.jsx)(We,{path:"/admin/settings",element:(0,it.jsx)(dt,{requiredPermission:{action:"READ",resource:"admin"},children:(0,it.jsx)(hc,{})})}),(0,it.jsx)(We,{path:"/profile",element:(0,it.jsx)(dt,{children:(0,it.jsx)(uc,{})})})]})})}):(0,it.jsx)(ft,{})}const gc=function(){return(0,it.jsx)(ct,{children:(0,it.jsx)(pc,{})})},xc=function(e){var t,n=Mr(),r=e||{},a=r.reducer,l=void 0===a?void 0:a,o=r.middleware,i=void 0===o?n():o,s=r.devTools,c=void 0===s||s,u=r.preloadedState,d=void 0===u?void 0:u,f=r.enhancers,m=void 0===f?void 0:f;if("function"===typeof l)t=l;else{if(!_r(l))throw new Error('"reducer" is a required argument, and must be a function or an object of functions that can be passed to combineReducers');t=ur(l)}var h=i;"function"===typeof h&&(h=h(n));var p=fr.apply(void 0,h),g=dr;c&&(g=Pr(Er({trace:!1},"object"===typeof c&&c)));var x=new Tr(p),y=x;return Array.isArray(m)?y=yr([p],m):"function"===typeof m&&(y=m(x)),cr(t,d,g.apply(void 0,y))}({reducer:{search:Eo,workflow:ei,envoy:xi,autoscaler:Ts,audit:$s,database:Zs},middleware:e=>e({serializableCheck:{ignoredActions:["persist/PERSIST"]}})}),yc=e=>{e&&e instanceof Function&&n.e(488).then(n.bind(n,488)).then((t=>{let{getCLS:n,getFID:r,getFCP:a,getLCP:l,getTTFB:o}=t;n(e),r(e),a(e),l(e),o(e)}))};o.createRoot(document.getElementById("root")).render((0,it.jsx)(a.StrictMode,{children:(0,it.jsx)(C,{store:xc,children:(0,it.jsx)(Xe,{children:(0,it.jsx)(gc,{})})})})),yc()})();
//# sourceMappingURL=main.c7f4c705.js.map