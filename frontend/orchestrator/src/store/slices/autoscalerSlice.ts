import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import axios from 'axios';

export interface AutoscalerStatus {
  enabled: boolean;
  current_replicas: number;
  desired_replicas: number;
  min_replicas: number;
  max_replicas: number;
  target_cpu_utilization: number;
  current_cpu_utilization: number;
  last_scale_time?: string;
}

export interface ScalingEvent {
  timestamp: string;
  from_replicas: number;
  to_replicas: number;
  reason: string;
  message: string;
}

export interface AutoscalerState {
  status: AutoscalerStatus | null;
  events: ScalingEvent[];
  loading: boolean;
  error: string | null;
  metrics: {
    cpu_usage: Array<{ timestamp: string; value: number }>;
    memory_usage: Array<{ timestamp: string; value: number }>;
    replica_count: Array<{ timestamp: string; value: number }>;
  };
}

const initialState: AutoscalerState = {
  status: null,
  events: [],
  loading: false,
  error: null,
  metrics: {
    cpu_usage: [],
    memory_usage: [],
    replica_count: [],
  },
};

// Async thunks
export const fetchAutoscalerStatus = createAsyncThunk(
  'autoscaler/fetchStatus',
  async () => {
    const response = await axios.get('/v1/autoscaler/status');
    return response.data;
  }
);

export const updateAutoscalerConfig = createAsyncThunk(
  'autoscaler/updateConfig',
  async (config: Partial<AutoscalerStatus>) => {
    const response = await axios.put('/v1/autoscaler/config', config);
    return response.data;
  }
);

export const fetchScalingEvents = createAsyncThunk(
  'autoscaler/fetchEvents',
  async () => {
    const response = await axios.get('/v1/autoscaler/events');
    return response.data.events;
  }
);

export const fetchMetrics = createAsyncThunk(
  'autoscaler/fetchMetrics',
  async (timeRange: string = '1h') => {
    const response = await axios.get(`/v1/autoscaler/metrics?range=${timeRange}`);
    return response.data;
  }
);

const autoscalerSlice = createSlice({
  name: 'autoscaler',
  initialState,
  reducers: {
    clearEvents: (state) => {
      state.events = [];
    },
    addEvent: (state, action: PayloadAction<ScalingEvent>) => {
      state.events.unshift(action.payload);
      // Keep only the last 100 events
      if (state.events.length > 100) {
        state.events = state.events.slice(0, 100);
      }
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchAutoscalerStatus.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAutoscalerStatus.fulfilled, (state, action) => {
        state.loading = false;
        state.status = action.payload;
      })
      .addCase(fetchAutoscalerStatus.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch autoscaler status';
      })
      .addCase(updateAutoscalerConfig.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateAutoscalerConfig.fulfilled, (state, action) => {
        state.loading = false;
        state.status = { ...state.status, ...action.payload };
      })
      .addCase(updateAutoscalerConfig.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to update autoscaler config';
      })
      .addCase(fetchScalingEvents.fulfilled, (state, action) => {
        state.events = action.payload;
      })
      .addCase(fetchMetrics.fulfilled, (state, action) => {
        state.metrics = action.payload;
      });
  },
});

export const { clearEvents, addEvent } = autoscalerSlice.actions;
export default autoscalerSlice.reducer;
