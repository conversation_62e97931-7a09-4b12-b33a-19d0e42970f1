import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';

// Authentication types based on Clutch implementation
interface User {
  sub: string;
  email: string;
  name: string;
  groups: string[];
  roles: string[];
  permissions: string[];
  preferences: {
    theme: string;
    timezone: string;
    language: string;
  };
}

interface AuthToken {
  access_token: string;
  refresh_token?: string;
  token_type: string;
  expires_in: number;
}

interface AuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: User | null;
  token: AuthToken | null;
  error: string | null;
}

interface AuthContextType extends AuthState {
  login: (username?: string, password?: string) => Promise<void>;
  logout: () => Promise<void>;
  checkPermission: (action: string, resource: string) => Promise<boolean>;
  refreshToken: () => Promise<void>;
}

// Create auth context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth provider component
interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    isLoading: true,
    user: null,
    token: null,
    error: null,
  });

  // Initialize authentication on mount
  useEffect(() => {
    initializeAuth();
  }, []);

  // Initialize authentication
  const initializeAuth = async () => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

      // Check if user is already authenticated
      const response = await fetch('/v1/auth/user', {
        credentials: 'include',
      });

      if (response.ok) {
        const user = await response.json();
        setAuthState(prev => ({
          ...prev,
          isAuthenticated: true,
          user,
          isLoading: false,
          error: null,
        }));
      } else {
        // No existing session, just set to not authenticated
        setAuthState(prev => ({
          ...prev,
          isAuthenticated: false,
          isLoading: false,
          error: null,
        }));
      }
    } catch (error) {
      console.error('Auth initialization failed:', error);
      setAuthState(prev => ({
        ...prev,
        isAuthenticated: false,
        isLoading: false,
        error: null, // Don't show error on initial load
      }));
    }
  };

  // Login function with username and password
  const login = async (username: string = 'admin', password: string = 'admin') => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

      // Login with username and password
      const response = await fetch('/v1/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          username,
          password,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Login failed');
      }

      const data = await response.json();

      setAuthState(prev => ({
        ...prev,
        isAuthenticated: true,
        user: data.user,
        token: null, // Using cookie-based auth
        isLoading: false,
        error: null,
      }));
    } catch (error) {
      console.error('Login failed:', error);
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Login failed',
      }));
    }
  };

  // Logout function
  const logout = async () => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true }));

      const response = await fetch('/v1/auth/logout', {
        method: 'POST',
        credentials: 'include',
      });

      setAuthState({
        isAuthenticated: false,
        isLoading: false,
        user: null,
        token: null,
        error: null,
      });

      // Optionally redirect to logout URL if provided
      if (response.ok) {
        const data = await response.json();
        if (data.logout_url && data.logout_url !== window.location.origin) {
          window.location.href = data.logout_url;
          return;
        }
      }
      
      // Reload page to clear any cached state
      window.location.reload();
    } catch (error) {
      console.error('Logout failed:', error);
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Logout failed',
      }));
    }
  };

  // Check permission function (client-side first, then server-side)
  const checkPermission = async (action: string, resource: string): Promise<boolean> => {
    try {
      // If user is not authenticated, deny access
      if (!authState.user) {
        return false;
      }

      // Check client-side permissions first
      const hasClientPermission = checkClientPermission(authState.user, action, resource);
      if (hasClientPermission !== null) {
        return hasClientPermission;
      }

      // Fallback to server-side check
      const response = await fetch('/v1/auth/check', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ action, resource }),
      });

      if (!response.ok) {
        return false;
      }

      const data = await response.json();
      return data.decision?.allowed || false;
    } catch (error) {
      console.error('Permission check failed:', error);
      // For admin users, default to true if server check fails
      if (authState.user?.roles?.includes('admin')) {
        return true;
      }
      return false;
    }
  };

  // Client-side permission checking
  const checkClientPermission = (user: User, action: string, resource: string): boolean | null => {
    if (!user || !user.permissions) {
      return false;
    }

    // Admin users with "*" permission have access to everything
    if (user.permissions.includes('*')) {
      return true;
    }

    // Check for exact permission match
    const exactPermission = `${resource}:${action.toLowerCase()}`;
    if (user.permissions.includes(exactPermission)) {
      return true;
    }

    // Check for wildcard resource permissions
    const wildcardPermission = `*:${action.toLowerCase()}`;
    if (user.permissions.includes(wildcardPermission)) {
      return true;
    }

    // Check for resource-level permissions
    const resourcePermission = `${resource}:*`;
    if (user.permissions.includes(resourcePermission)) {
      return true;
    }

    // Check role-based permissions
    if (user.roles?.includes('admin')) {
      return true;
    }

    if (user.roles?.includes('operator') && (action.toLowerCase() === 'read' || action.toLowerCase() === 'execute')) {
      return true;
    }

    if (user.roles?.includes('viewer') && action.toLowerCase() === 'read') {
      return true;
    }

    // Default deny
    return false;
  };

  // Refresh token function
  const refreshToken = async () => {
    try {
      const response = await fetch('/v1/auth/refresh', {
        method: 'POST',
        credentials: 'include',
      });

      if (response.ok) {
        const tokenData = await response.json();

        // Get updated user info
        const userResponse = await fetch('/v1/auth/user', {
          credentials: 'include',
        });

        if (userResponse.ok) {
          const user = await userResponse.json();
          setAuthState(prev => ({
            ...prev,
            isAuthenticated: true,
            user,
            token: tokenData,
            isLoading: false,
            error: null,
          }));
        }
      } else {
        // Silently fail refresh - user needs to login again
        setAuthState(prev => ({
          ...prev,
          isAuthenticated: false,
          isLoading: false,
          user: null,
          token: null,
          error: null, // Don't show error for failed refresh
        }));
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
      setAuthState(prev => ({
        ...prev,
        isAuthenticated: false,
        isLoading: false,
        user: null,
        token: null,
        error: null, // Don't show error for failed refresh
      }));
    }
  };

  // Auto-refresh token before expiry
  useEffect(() => {
    if (authState.token && authState.isAuthenticated) {
      const refreshInterval = setInterval(() => {
        refreshToken();
      }, 50 * 60 * 1000); // Refresh every 50 minutes (token expires in 60 minutes)

      return () => clearInterval(refreshInterval);
    }
  }, [authState.token, authState.isAuthenticated]);

  const contextValue: AuthContextType = {
    ...authState,
    login,
    logout,
    checkPermission,
    refreshToken,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// Hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Higher-order component for protected routes
interface ProtectedRouteProps {
  children: ReactNode;
  requiredPermission?: { action: string; resource: string };
  fallback?: ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredPermission,
  fallback = <div className="p-4 text-red-600">Access Denied</div>,
}) => {
  const { isAuthenticated, isLoading, checkPermission } = useAuth();
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);

  useEffect(() => {
    if (isAuthenticated && requiredPermission) {
      checkPermission(requiredPermission.action, requiredPermission.resource)
        .then(setHasPermission);
    } else if (isAuthenticated) {
      setHasPermission(true);
    }
  }, [isAuthenticated, requiredPermission, checkPermission]);

  if (isLoading) {
    return <div className="p-4">Loading...</div>;
  }

  if (!isAuthenticated) {
    return <div className="p-4">Please log in to access this page.</div>;
  }

  if (requiredPermission && hasPermission === false) {
    return <>{fallback}</>;
  }

  if (requiredPermission && hasPermission === null) {
    return <div className="p-4">Checking permissions...</div>;
  }

  return <>{children}</>;
};
