{"ast": null, "code": "import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\nimport axios from 'axios';\nconst initialState = {\n  workflows: [],\n  executions: [],\n  loading: false,\n  error: null,\n  selectedWorkflow: null\n};\n\n// Async thunks\nexport const fetchWorkflows = createAsyncThunk('workflow/fetchWorkflows', async () => {\n  const response = await axios.get('/v1/workflows');\n  return response.data.workflows;\n});\nexport const executeWorkflow = createAsyncThunk('workflow/executeWorkflow', async params => {\n  const response = await axios.post('/v1/workflows/execute', {\n    workflow_id: params.workflowId,\n    inputs: params.inputs\n  });\n  return response.data;\n});\nexport const getWorkflowStatus = createAsyncThunk('workflow/getWorkflowStatus', async executionId => {\n  const response = await axios.get(`/v1/workflows/${executionId}/status`);\n  return response.data;\n});\nconst workflowSlice = createSlice({\n  name: 'workflow',\n  initialState,\n  reducers: {\n    setSelectedWorkflow: (state, action) => {\n      state.selectedWorkflow = action.payload;\n    },\n    clearExecutions: state => {\n      state.executions = [];\n    },\n    updateExecutionStatus: (state, action) => {\n      const execution = state.executions.find(e => e.execution_id === action.payload.executionId);\n      if (execution) {\n        execution.status = action.payload.status;\n      }\n    }\n  },\n  extraReducers: builder => {\n    builder.addCase(fetchWorkflows.pending, state => {\n      state.loading = true;\n      state.error = null;\n    }).addCase(fetchWorkflows.fulfilled, (state, action) => {\n      state.loading = false;\n      state.workflows = action.payload;\n    }).addCase(fetchWorkflows.rejected, (state, action) => {\n      state.loading = false;\n      state.error = action.error.message || 'Failed to fetch workflows';\n    }).addCase(executeWorkflow.pending, state => {\n      state.loading = true;\n      state.error = null;\n    }).addCase(executeWorkflow.fulfilled, (state, action) => {\n      state.loading = false;\n      state.executions.push(action.payload);\n    }).addCase(executeWorkflow.rejected, (state, action) => {\n      state.loading = false;\n      state.error = action.error.message || 'Failed to execute workflow';\n    }).addCase(getWorkflowStatus.fulfilled, (state, action) => {\n      const index = state.executions.findIndex(e => e.execution_id === action.payload.workflow_id);\n      if (index !== -1) {\n        state.executions[index] = {\n          ...state.executions[index],\n          ...action.payload\n        };\n      }\n    });\n  }\n});\nexport const {\n  setSelectedWorkflow,\n  clearExecutions,\n  updateExecutionStatus\n} = workflowSlice.actions;\nexport default workflowSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "axios", "initialState", "workflows", "executions", "loading", "error", "selectedWorkflow", "fetchWorkflows", "response", "get", "data", "executeWorkflow", "params", "post", "workflow_id", "workflowId", "inputs", "getWorkflowStatus", "executionId", "workflowSlice", "name", "reducers", "setSelectedWorkflow", "state", "action", "payload", "clearExecutions", "updateExecutionStatus", "execution", "find", "e", "execution_id", "status", "extraReducers", "builder", "addCase", "pending", "fulfilled", "rejected", "message", "push", "index", "findIndex", "actions", "reducer"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/workflowSlice.ts"], "sourcesContent": ["import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';\nimport axios from 'axios';\n\nexport interface Workflow {\n  id: string;\n  name: string;\n  description: string;\n  created_at: string;\n}\n\nexport interface WorkflowExecution {\n  execution_id: string;\n  workflow_id: string;\n  status: 'running' | 'completed' | 'failed';\n  started_at?: string;\n  completed_at?: string;\n  outputs?: Record<string, any>;\n}\n\nexport interface WorkflowState {\n  workflows: Workflow[];\n  executions: WorkflowExecution[];\n  loading: boolean;\n  error: string | null;\n  selectedWorkflow: Workflow | null;\n}\n\nconst initialState: WorkflowState = {\n  workflows: [],\n  executions: [],\n  loading: false,\n  error: null,\n  selectedWorkflow: null,\n};\n\n// Async thunks\nexport const fetchWorkflows = createAsyncThunk(\n  'workflow/fetchWorkflows',\n  async () => {\n    const response = await axios.get('/v1/workflows');\n    return response.data.workflows;\n  }\n);\n\nexport const executeWorkflow = createAsyncThunk(\n  'workflow/executeWorkflow',\n  async (params: { workflowId: string; inputs: Record<string, any> }) => {\n    const response = await axios.post('/v1/workflows/execute', {\n      workflow_id: params.workflowId,\n      inputs: params.inputs,\n    });\n    return response.data;\n  }\n);\n\nexport const getWorkflowStatus = createAsyncThunk(\n  'workflow/getWorkflowStatus',\n  async (executionId: string) => {\n    const response = await axios.get(`/v1/workflows/${executionId}/status`);\n    return response.data;\n  }\n);\n\nconst workflowSlice = createSlice({\n  name: 'workflow',\n  initialState,\n  reducers: {\n    setSelectedWorkflow: (state, action: PayloadAction<Workflow | null>) => {\n      state.selectedWorkflow = action.payload;\n    },\n    clearExecutions: (state) => {\n      state.executions = [];\n    },\n    updateExecutionStatus: (state, action: PayloadAction<{ executionId: string; status: string }>) => {\n      const execution = state.executions.find(e => e.execution_id === action.payload.executionId);\n      if (execution) {\n        execution.status = action.payload.status as any;\n      }\n    },\n  },\n  extraReducers: (builder) => {\n    builder\n      .addCase(fetchWorkflows.pending, (state) => {\n        state.loading = true;\n        state.error = null;\n      })\n      .addCase(fetchWorkflows.fulfilled, (state, action) => {\n        state.loading = false;\n        state.workflows = action.payload;\n      })\n      .addCase(fetchWorkflows.rejected, (state, action) => {\n        state.loading = false;\n        state.error = action.error.message || 'Failed to fetch workflows';\n      })\n      .addCase(executeWorkflow.pending, (state) => {\n        state.loading = true;\n        state.error = null;\n      })\n      .addCase(executeWorkflow.fulfilled, (state, action) => {\n        state.loading = false;\n        state.executions.push(action.payload);\n      })\n      .addCase(executeWorkflow.rejected, (state, action) => {\n        state.loading = false;\n        state.error = action.error.message || 'Failed to execute workflow';\n      })\n      .addCase(getWorkflowStatus.fulfilled, (state, action) => {\n        const index = state.executions.findIndex(e => e.execution_id === action.payload.workflow_id);\n        if (index !== -1) {\n          state.executions[index] = { ...state.executions[index], ...action.payload };\n        }\n      });\n  },\n});\n\nexport const { setSelectedWorkflow, clearExecutions, updateExecutionStatus } = workflowSlice.actions;\nexport default workflowSlice.reducer;\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,gBAAgB,QAAuB,kBAAkB;AAC/E,OAAOC,KAAK,MAAM,OAAO;AA0BzB,MAAMC,YAA2B,GAAG;EAClCC,SAAS,EAAE,EAAE;EACbC,UAAU,EAAE,EAAE;EACdC,OAAO,EAAE,KAAK;EACdC,KAAK,EAAE,IAAI;EACXC,gBAAgB,EAAE;AACpB,CAAC;;AAED;AACA,OAAO,MAAMC,cAAc,GAAGR,gBAAgB,CAC5C,yBAAyB,EACzB,YAAY;EACV,MAAMS,QAAQ,GAAG,MAAMR,KAAK,CAACS,GAAG,CAAC,eAAe,CAAC;EACjD,OAAOD,QAAQ,CAACE,IAAI,CAACR,SAAS;AAChC,CACF,CAAC;AAED,OAAO,MAAMS,eAAe,GAAGZ,gBAAgB,CAC7C,0BAA0B,EAC1B,MAAOa,MAA2D,IAAK;EACrE,MAAMJ,QAAQ,GAAG,MAAMR,KAAK,CAACa,IAAI,CAAC,uBAAuB,EAAE;IACzDC,WAAW,EAAEF,MAAM,CAACG,UAAU;IAC9BC,MAAM,EAAEJ,MAAM,CAACI;EACjB,CAAC,CAAC;EACF,OAAOR,QAAQ,CAACE,IAAI;AACtB,CACF,CAAC;AAED,OAAO,MAAMO,iBAAiB,GAAGlB,gBAAgB,CAC/C,4BAA4B,EAC5B,MAAOmB,WAAmB,IAAK;EAC7B,MAAMV,QAAQ,GAAG,MAAMR,KAAK,CAACS,GAAG,CAAC,iBAAiBS,WAAW,SAAS,CAAC;EACvE,OAAOV,QAAQ,CAACE,IAAI;AACtB,CACF,CAAC;AAED,MAAMS,aAAa,GAAGrB,WAAW,CAAC;EAChCsB,IAAI,EAAE,UAAU;EAChBnB,YAAY;EACZoB,QAAQ,EAAE;IACRC,mBAAmB,EAAEA,CAACC,KAAK,EAAEC,MAAsC,KAAK;MACtED,KAAK,CAACjB,gBAAgB,GAAGkB,MAAM,CAACC,OAAO;IACzC,CAAC;IACDC,eAAe,EAAGH,KAAK,IAAK;MAC1BA,KAAK,CAACpB,UAAU,GAAG,EAAE;IACvB,CAAC;IACDwB,qBAAqB,EAAEA,CAACJ,KAAK,EAAEC,MAA8D,KAAK;MAChG,MAAMI,SAAS,GAAGL,KAAK,CAACpB,UAAU,CAAC0B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,YAAY,KAAKP,MAAM,CAACC,OAAO,CAACP,WAAW,CAAC;MAC3F,IAAIU,SAAS,EAAE;QACbA,SAAS,CAACI,MAAM,GAAGR,MAAM,CAACC,OAAO,CAACO,MAAa;MACjD;IACF;EACF,CAAC;EACDC,aAAa,EAAGC,OAAO,IAAK;IAC1BA,OAAO,CACJC,OAAO,CAAC5B,cAAc,CAAC6B,OAAO,EAAGb,KAAK,IAAK;MAC1CA,KAAK,CAACnB,OAAO,GAAG,IAAI;MACpBmB,KAAK,CAAClB,KAAK,GAAG,IAAI;IACpB,CAAC,CAAC,CACD8B,OAAO,CAAC5B,cAAc,CAAC8B,SAAS,EAAE,CAACd,KAAK,EAAEC,MAAM,KAAK;MACpDD,KAAK,CAACnB,OAAO,GAAG,KAAK;MACrBmB,KAAK,CAACrB,SAAS,GAAGsB,MAAM,CAACC,OAAO;IAClC,CAAC,CAAC,CACDU,OAAO,CAAC5B,cAAc,CAAC+B,QAAQ,EAAE,CAACf,KAAK,EAAEC,MAAM,KAAK;MACnDD,KAAK,CAACnB,OAAO,GAAG,KAAK;MACrBmB,KAAK,CAAClB,KAAK,GAAGmB,MAAM,CAACnB,KAAK,CAACkC,OAAO,IAAI,2BAA2B;IACnE,CAAC,CAAC,CACDJ,OAAO,CAACxB,eAAe,CAACyB,OAAO,EAAGb,KAAK,IAAK;MAC3CA,KAAK,CAACnB,OAAO,GAAG,IAAI;MACpBmB,KAAK,CAAClB,KAAK,GAAG,IAAI;IACpB,CAAC,CAAC,CACD8B,OAAO,CAACxB,eAAe,CAAC0B,SAAS,EAAE,CAACd,KAAK,EAAEC,MAAM,KAAK;MACrDD,KAAK,CAACnB,OAAO,GAAG,KAAK;MACrBmB,KAAK,CAACpB,UAAU,CAACqC,IAAI,CAAChB,MAAM,CAACC,OAAO,CAAC;IACvC,CAAC,CAAC,CACDU,OAAO,CAACxB,eAAe,CAAC2B,QAAQ,EAAE,CAACf,KAAK,EAAEC,MAAM,KAAK;MACpDD,KAAK,CAACnB,OAAO,GAAG,KAAK;MACrBmB,KAAK,CAAClB,KAAK,GAAGmB,MAAM,CAACnB,KAAK,CAACkC,OAAO,IAAI,4BAA4B;IACpE,CAAC,CAAC,CACDJ,OAAO,CAAClB,iBAAiB,CAACoB,SAAS,EAAE,CAACd,KAAK,EAAEC,MAAM,KAAK;MACvD,MAAMiB,KAAK,GAAGlB,KAAK,CAACpB,UAAU,CAACuC,SAAS,CAACZ,CAAC,IAAIA,CAAC,CAACC,YAAY,KAAKP,MAAM,CAACC,OAAO,CAACX,WAAW,CAAC;MAC5F,IAAI2B,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBlB,KAAK,CAACpB,UAAU,CAACsC,KAAK,CAAC,GAAG;UAAE,GAAGlB,KAAK,CAACpB,UAAU,CAACsC,KAAK,CAAC;UAAE,GAAGjB,MAAM,CAACC;QAAQ,CAAC;MAC7E;IACF,CAAC,CAAC;EACN;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEH,mBAAmB;EAAEI,eAAe;EAAEC;AAAsB,CAAC,GAAGR,aAAa,CAACwB,OAAO;AACpG,eAAexB,aAAa,CAACyB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}