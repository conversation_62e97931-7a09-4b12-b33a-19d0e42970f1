package grpc

import (
	"context"
	"fmt"
	"net"

	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"

	"github.com/cainuro/orchestrator/backend/service/audit"
	"github.com/cainuro/orchestrator/backend/service/autoscaler"
	"github.com/cainuro/orchestrator/backend/service/dbadmin"
	"github.com/cainuro/orchestrator/backend/service/envoycontrolplane"
	"github.com/cainuro/orchestrator/backend/service/search"
	"github.com/cainuro/orchestrator/backend/service/workflow"
	"github.com/cainuro/orchestrator/internal/config"
	orchestratorv1 "github.com/cainuro/orchestrator/proto/orchestrator/v1"
)

// Server represents the gRPC server
type Server struct {
	orchestratorv1.UnimplementedAutoScalerServiceServer
	orchestratorv1.UnimplementedDiscoveryServiceServer
	orchestratorv1.UnimplementedWorkflowServiceServer
	orchestratorv1.UnimplementedEnvoyControlPlaneServiceServer
	orchestratorv1.UnimplementedDBAdminServiceServer
	orchestratorv1.UnimplementedAuditServiceServer

	config     *config.Config
	logger     *zap.Logger
	grpcServer *grpc.Server

	// Services
	autoscalerService autoscaler.Service
	searchService     search.Service
	workflowService   workflow.Service
	envoyService      envoycontrolplane.Service
	dbAdminService    dbadmin.Service
	auditService      audit.Service
}

// NewServer creates a new gRPC server
func NewServer(
	config *config.Config,
	logger *zap.Logger,
	autoscalerService autoscaler.Service,
	searchService search.Service,
	workflowService workflow.Service,
	envoyService envoycontrolplane.Service,
	dbAdminService dbadmin.Service,
	auditService audit.Service,
) *Server {
	grpcServer := grpc.NewServer(
		grpc.UnaryInterceptor(loggingInterceptor(logger)),
	)

	server := &Server{
		config:            config,
		logger:            logger,
		grpcServer:        grpcServer,
		autoscalerService: autoscalerService,
		searchService:     searchService,
		workflowService:   workflowService,
		envoyService:      envoyService,
		dbAdminService:    dbAdminService,
		auditService:      auditService,
	}

	// Register services
	orchestratorv1.RegisterAutoScalerServiceServer(grpcServer, server)
	orchestratorv1.RegisterDiscoveryServiceServer(grpcServer, server)
	orchestratorv1.RegisterWorkflowServiceServer(grpcServer, server)
	orchestratorv1.RegisterEnvoyControlPlaneServiceServer(grpcServer, server)
	orchestratorv1.RegisterDBAdminServiceServer(grpcServer, server)
	orchestratorv1.RegisterAuditServiceServer(grpcServer, server)

	// Enable reflection for development
	reflection.Register(grpcServer)

	return server
}

// Start starts the gRPC server
func (s *Server) Start() error {
	addr := fmt.Sprintf(":%d", s.config.Server.GRPCPort)
	lis, err := net.Listen("tcp", addr)
	if err != nil {
		return fmt.Errorf("failed to listen on %s: %w", addr, err)
	}

	s.logger.Info("Starting gRPC server", zap.String("address", addr))

	if err := s.grpcServer.Serve(lis); err != nil {
		return fmt.Errorf("failed to serve gRPC: %w", err)
	}

	return nil
}

// Stop stops the gRPC server
func (s *Server) Stop() {
	s.logger.Info("Stopping gRPC server")
	s.grpcServer.GracefulStop()
}

// AutoScaler Service Implementation
func (s *Server) GetStatus(ctx context.Context, req *orchestratorv1.Empty) (*orchestratorv1.AutoScalerStatus, error) {
	return s.autoscalerService.GetAutoscalerStatus(ctx, &orchestratorv1.GetAutoscalerStatusRequest{})
}

func (s *Server) UpdateConfig(ctx context.Context, req *orchestratorv1.UpdateAutoScalerConfigRequest) (*orchestratorv1.AutoScalerStatus, error) {
	resp, err := s.autoscalerService.UpdateAutoscalerConfig(ctx, req)
	if err != nil {
		return nil, err
	}

	// Return updated status
	return s.autoscalerService.GetAutoscalerStatus(ctx, &orchestratorv1.GetAutoscalerStatusRequest{})
}

// Discovery Service Implementation
func (s *Server) SearchResources(ctx context.Context, req *orchestratorv1.SearchResourcesRequest) (*orchestratorv1.SearchResourcesResponse, error) {
	return s.searchService.SearchResources(ctx, req)
}

func (s *Server) GetResource(ctx context.Context, req *orchestratorv1.GetResourceRequest) (*orchestratorv1.GetResourceResponse, error) {
	return s.searchService.GetResource(ctx, req)
}

// Workflow Service Implementation
func (s *Server) ListWorkflows(ctx context.Context, req *orchestratorv1.ListWorkflowsRequest) (*orchestratorv1.ListWorkflowsResponse, error) {
	return s.workflowService.ListWorkflows(ctx, req)
}

func (s *Server) ExecuteWorkflow(ctx context.Context, req *orchestratorv1.ExecuteWorkflowRequest) (*orchestratorv1.ExecuteWorkflowResponse, error) {
	return s.workflowService.ExecuteWorkflow(ctx, req)
}

func (s *Server) GetWorkflowStatus(ctx context.Context, req *orchestratorv1.GetWorkflowStatusRequest) (*orchestratorv1.GetWorkflowStatusResponse, error) {
	return s.workflowService.GetWorkflowStatus(ctx, req)
}

// Envoy Control Plane Service Implementation
func (s *Server) CreateConfig(ctx context.Context, req *orchestratorv1.CreateEnvoyConfigRequest) (*orchestratorv1.CreateEnvoyConfigResponse, error) {
	return s.envoyService.CreateConfig(ctx, req)
}

func (s *Server) ListConfigs(ctx context.Context, req *orchestratorv1.ListEnvoyConfigsRequest) (*orchestratorv1.ListEnvoyConfigsResponse, error) {
	return s.envoyService.ListConfigs(ctx, req)
}

func (s *Server) ListNodes(ctx context.Context, req *orchestratorv1.ListEnvoyNodesRequest) (*orchestratorv1.ListEnvoyNodesResponse, error) {
	return s.envoyService.ListNodes(ctx, req)
}

// Database Admin Service Implementation
func (s *Server) GetStats(ctx context.Context, req *orchestratorv1.GetDatabaseStatsRequest) (*orchestratorv1.GetDatabaseStatsResponse, error) {
	return s.dbAdminService.GetStats(ctx, req)
}

func (s *Server) ExecuteQuery(ctx context.Context, req *orchestratorv1.ExecuteQueryRequest) (*orchestratorv1.ExecuteQueryResponse, error) {
	return s.dbAdminService.ExecuteQuery(ctx, req)
}

// Audit Service Implementation
func (s *Server) QueryEvents(ctx context.Context, req *orchestratorv1.QueryAuditEventsRequest) (*orchestratorv1.QueryAuditEventsResponse, error) {
	return s.auditService.QueryEvents(ctx, req)
}

// Logging interceptor
func loggingInterceptor(logger *zap.Logger) grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		logger.Info("gRPC request", zap.String("method", info.FullMethod))

		resp, err := handler(ctx, req)

		if err != nil {
			logger.Error("gRPC request failed",
				zap.String("method", info.FullMethod),
				zap.Error(err))
		} else {
			logger.Info("gRPC request completed", zap.String("method", info.FullMethod))
		}

		return resp, err
	}
}
