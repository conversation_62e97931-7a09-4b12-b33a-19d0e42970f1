// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v3.17.3
// source: k8s/v1/k8s.proto

package k8sv1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "github.com/lyft/clutch/backend/api/api/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	status "google.golang.org/genproto/googleapis/rpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ObjectKind represent a persistent entity in the system
// For now we only support the kind Pod, we can expand on this
// list as we add support for different kinds.
type ObjectKind int32

const (
	ObjectKind_UNSPECIFIED ObjectKind = 0
	ObjectKind_UNKNOWN     ObjectKind = 1
	ObjectKind_POD         ObjectKind = 2
)

// Enum value maps for ObjectKind.
var (
	ObjectKind_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "UNKNOWN",
		2: "POD",
	}
	ObjectKind_value = map[string]int32{
		"UNSPECIFIED": 0,
		"UNKNOWN":     1,
		"POD":         2,
	}
)

func (x ObjectKind) Enum() *ObjectKind {
	p := new(ObjectKind)
	*p = x
	return p
}

func (x ObjectKind) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ObjectKind) Descriptor() protoreflect.EnumDescriptor {
	return file_k8s_v1_k8s_proto_enumTypes[0].Descriptor()
}

func (ObjectKind) Type() protoreflect.EnumType {
	return &file_k8s_v1_k8s_proto_enumTypes[0]
}

func (x ObjectKind) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ObjectKind.Descriptor instead.
func (ObjectKind) EnumDescriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{0}
}

type EventType int32

const (
	EventType_TYPE_UNSPECIFIED EventType = 0
	EventType_NORMAL           EventType = 1
	EventType_WARNING          EventType = 2
	EventType_ERROR            EventType = 3
)

// Enum value maps for EventType.
var (
	EventType_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "NORMAL",
		2: "WARNING",
		3: "ERROR",
	}
	EventType_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"NORMAL":           1,
		"WARNING":          2,
		"ERROR":            3,
	}
)

func (x EventType) Enum() *EventType {
	p := new(EventType)
	*p = x
	return p
}

func (x EventType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EventType) Descriptor() protoreflect.EnumDescriptor {
	return file_k8s_v1_k8s_proto_enumTypes[1].Descriptor()
}

func (EventType) Type() protoreflect.EnumType {
	return &file_k8s_v1_k8s_proto_enumTypes[1]
}

func (x EventType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EventType.Descriptor instead.
func (EventType) EnumDescriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{1}
}

// https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.18/#containerstate-v1-core
type Container_State int32

const (
	Container_UNSPECIFIED Container_State = 0
	Container_UNKNOWN     Container_State = 1
	Container_TERMINATED  Container_State = 2
	Container_RUNNING     Container_State = 3
	Container_WAITING     Container_State = 4
)

// Enum value maps for Container_State.
var (
	Container_State_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "UNKNOWN",
		2: "TERMINATED",
		3: "RUNNING",
		4: "WAITING",
	}
	Container_State_value = map[string]int32{
		"UNSPECIFIED": 0,
		"UNKNOWN":     1,
		"TERMINATED":  2,
		"RUNNING":     3,
		"WAITING":     4,
	}
)

func (x Container_State) Enum() *Container_State {
	p := new(Container_State)
	*p = x
	return p
}

func (x Container_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Container_State) Descriptor() protoreflect.EnumDescriptor {
	return file_k8s_v1_k8s_proto_enumTypes[2].Descriptor()
}

func (Container_State) Type() protoreflect.EnumType {
	return &file_k8s_v1_k8s_proto_enumTypes[2]
}

func (x Container_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Container_State.Descriptor instead.
func (Container_State) EnumDescriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{4, 0}
}

type PodCondition_Type int32

const (
	PodCondition_TYPE_UNSPECIFIED PodCondition_Type = 0
	PodCondition_CONTAINERS_READY PodCondition_Type = 1
	PodCondition_INITIALIZED      PodCondition_Type = 2
	PodCondition_READY            PodCondition_Type = 3
	PodCondition_POD_SCHEDULED    PodCondition_Type = 4
)

// Enum value maps for PodCondition_Type.
var (
	PodCondition_Type_name = map[int32]string{
		0: "TYPE_UNSPECIFIED",
		1: "CONTAINERS_READY",
		2: "INITIALIZED",
		3: "READY",
		4: "POD_SCHEDULED",
	}
	PodCondition_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"CONTAINERS_READY": 1,
		"INITIALIZED":      2,
		"READY":            3,
		"POD_SCHEDULED":    4,
	}
)

func (x PodCondition_Type) Enum() *PodCondition_Type {
	p := new(PodCondition_Type)
	*p = x
	return p
}

func (x PodCondition_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PodCondition_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_k8s_v1_k8s_proto_enumTypes[3].Descriptor()
}

func (PodCondition_Type) Type() protoreflect.EnumType {
	return &file_k8s_v1_k8s_proto_enumTypes[3]
}

func (x PodCondition_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PodCondition_Type.Descriptor instead.
func (PodCondition_Type) EnumDescriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{8, 0}
}

type PodCondition_Status int32

const (
	PodCondition_STATUS_UNSPECIFIED PodCondition_Status = 0
	PodCondition_TRUE               PodCondition_Status = 1
	PodCondition_FALSE              PodCondition_Status = 2
	PodCondition_UNKNOWN            PodCondition_Status = 3
)

// Enum value maps for PodCondition_Status.
var (
	PodCondition_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "TRUE",
		2: "FALSE",
		3: "UNKNOWN",
	}
	PodCondition_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"TRUE":               1,
		"FALSE":              2,
		"UNKNOWN":            3,
	}
)

func (x PodCondition_Status) Enum() *PodCondition_Status {
	p := new(PodCondition_Status)
	*p = x
	return p
}

func (x PodCondition_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PodCondition_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_k8s_v1_k8s_proto_enumTypes[4].Descriptor()
}

func (PodCondition_Status) Type() protoreflect.EnumType {
	return &file_k8s_v1_k8s_proto_enumTypes[4]
}

func (x PodCondition_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PodCondition_Status.Descriptor instead.
func (PodCondition_Status) EnumDescriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{8, 1}
}

// https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle/
type Pod_State int32

const (
	Pod_UNSPECIFIED Pod_State = 0
	Pod_UNKNOWN     Pod_State = 1
	Pod_PENDING     Pod_State = 2
	Pod_RUNNING     Pod_State = 3
	Pod_SUCCEEDED   Pod_State = 4
	Pod_FAILED      Pod_State = 5
)

// Enum value maps for Pod_State.
var (
	Pod_State_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "UNKNOWN",
		2: "PENDING",
		3: "RUNNING",
		4: "SUCCEEDED",
		5: "FAILED",
	}
	Pod_State_value = map[string]int32{
		"UNSPECIFIED": 0,
		"UNKNOWN":     1,
		"PENDING":     2,
		"RUNNING":     3,
		"SUCCEEDED":   4,
		"FAILED":      5,
	}
)

func (x Pod_State) Enum() *Pod_State {
	p := new(Pod_State)
	*p = x
	return p
}

func (x Pod_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Pod_State) Descriptor() protoreflect.EnumDescriptor {
	return file_k8s_v1_k8s_proto_enumTypes[5].Descriptor()
}

func (Pod_State) Type() protoreflect.EnumType {
	return &file_k8s_v1_k8s_proto_enumTypes[5]
}

func (x Pod_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Pod_State.Descriptor instead.
func (Pod_State) EnumDescriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{9, 0}
}

// This is mirroring upstream values here:
// https://pkg.go.dev/k8s.io/api/apps/v1#DeploymentConditionType
type Deployment_DeploymentStatus_Condition_Type int32

const (
	Deployment_DeploymentStatus_Condition_UNSPECIFIED     Deployment_DeploymentStatus_Condition_Type = 0
	Deployment_DeploymentStatus_Condition_UNKNOWN         Deployment_DeploymentStatus_Condition_Type = 1
	Deployment_DeploymentStatus_Condition_AVAILABLE       Deployment_DeploymentStatus_Condition_Type = 2
	Deployment_DeploymentStatus_Condition_PROGRESSING     Deployment_DeploymentStatus_Condition_Type = 3
	Deployment_DeploymentStatus_Condition_REPLICA_FAILURE Deployment_DeploymentStatus_Condition_Type = 4
)

// Enum value maps for Deployment_DeploymentStatus_Condition_Type.
var (
	Deployment_DeploymentStatus_Condition_Type_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "UNKNOWN",
		2: "AVAILABLE",
		3: "PROGRESSING",
		4: "REPLICA_FAILURE",
	}
	Deployment_DeploymentStatus_Condition_Type_value = map[string]int32{
		"UNSPECIFIED":     0,
		"UNKNOWN":         1,
		"AVAILABLE":       2,
		"PROGRESSING":     3,
		"REPLICA_FAILURE": 4,
	}
)

func (x Deployment_DeploymentStatus_Condition_Type) Enum() *Deployment_DeploymentStatus_Condition_Type {
	p := new(Deployment_DeploymentStatus_Condition_Type)
	*p = x
	return p
}

func (x Deployment_DeploymentStatus_Condition_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Deployment_DeploymentStatus_Condition_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_k8s_v1_k8s_proto_enumTypes[6].Descriptor()
}

func (Deployment_DeploymentStatus_Condition_Type) Type() protoreflect.EnumType {
	return &file_k8s_v1_k8s_proto_enumTypes[6]
}

func (x Deployment_DeploymentStatus_Condition_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Deployment_DeploymentStatus_Condition_Type.Descriptor instead.
func (Deployment_DeploymentStatus_Condition_Type) EnumDescriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{32, 2, 0, 0}
}

type Deployment_DeploymentStatus_Condition_ConditionStatus int32

const (
	Deployment_DeploymentStatus_Condition_CONDITION_UNSPECIFIED Deployment_DeploymentStatus_Condition_ConditionStatus = 0
	Deployment_DeploymentStatus_Condition_CONDITION_TRUE        Deployment_DeploymentStatus_Condition_ConditionStatus = 1
	Deployment_DeploymentStatus_Condition_CONDITION_FALSE       Deployment_DeploymentStatus_Condition_ConditionStatus = 2
	Deployment_DeploymentStatus_Condition_CONDITION_UNKNOWN     Deployment_DeploymentStatus_Condition_ConditionStatus = 3
)

// Enum value maps for Deployment_DeploymentStatus_Condition_ConditionStatus.
var (
	Deployment_DeploymentStatus_Condition_ConditionStatus_name = map[int32]string{
		0: "CONDITION_UNSPECIFIED",
		1: "CONDITION_TRUE",
		2: "CONDITION_FALSE",
		3: "CONDITION_UNKNOWN",
	}
	Deployment_DeploymentStatus_Condition_ConditionStatus_value = map[string]int32{
		"CONDITION_UNSPECIFIED": 0,
		"CONDITION_TRUE":        1,
		"CONDITION_FALSE":       2,
		"CONDITION_UNKNOWN":     3,
	}
)

func (x Deployment_DeploymentStatus_Condition_ConditionStatus) Enum() *Deployment_DeploymentStatus_Condition_ConditionStatus {
	p := new(Deployment_DeploymentStatus_Condition_ConditionStatus)
	*p = x
	return p
}

func (x Deployment_DeploymentStatus_Condition_ConditionStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Deployment_DeploymentStatus_Condition_ConditionStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_k8s_v1_k8s_proto_enumTypes[7].Descriptor()
}

func (Deployment_DeploymentStatus_Condition_ConditionStatus) Type() protoreflect.EnumType {
	return &file_k8s_v1_k8s_proto_enumTypes[7]
}

func (x Deployment_DeploymentStatus_Condition_ConditionStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Deployment_DeploymentStatus_Condition_ConditionStatus.Descriptor instead.
func (Deployment_DeploymentStatus_Condition_ConditionStatus) EnumDescriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{32, 2, 0, 1}
}

type Service_Type int32

const (
	Service_UNSPECIFIED   Service_Type = 0
	Service_UNKNOWN       Service_Type = 1
	Service_CLUSTER_IP    Service_Type = 2
	Service_NODE_PORT     Service_Type = 3
	Service_LOAD_BALANCER Service_Type = 4
	Service_EXTERNAL_NAME Service_Type = 5
)

// Enum value maps for Service_Type.
var (
	Service_Type_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "UNKNOWN",
		2: "CLUSTER_IP",
		3: "NODE_PORT",
		4: "LOAD_BALANCER",
		5: "EXTERNAL_NAME",
	}
	Service_Type_value = map[string]int32{
		"UNSPECIFIED":   0,
		"UNKNOWN":       1,
		"CLUSTER_IP":    2,
		"NODE_PORT":     3,
		"LOAD_BALANCER": 4,
		"EXTERNAL_NAME": 5,
	}
)

func (x Service_Type) Enum() *Service_Type {
	p := new(Service_Type)
	*p = x
	return p
}

func (x Service_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Service_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_k8s_v1_k8s_proto_enumTypes[8].Descriptor()
}

func (Service_Type) Type() protoreflect.EnumType {
	return &file_k8s_v1_k8s_proto_enumTypes[8]
}

func (x Service_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Service_Type.Descriptor instead.
func (Service_Type) EnumDescriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{50, 0}
}

type CronJob_ConcurrencyPolicy int32

const (
	CronJob_UNSPECIFIED CronJob_ConcurrencyPolicy = 0
	CronJob_UNKNOWN     CronJob_ConcurrencyPolicy = 1
	CronJob_ALLOW       CronJob_ConcurrencyPolicy = 2
	CronJob_FORBID      CronJob_ConcurrencyPolicy = 3
	CronJob_REPLACE     CronJob_ConcurrencyPolicy = 4
)

// Enum value maps for CronJob_ConcurrencyPolicy.
var (
	CronJob_ConcurrencyPolicy_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "UNKNOWN",
		2: "ALLOW",
		3: "FORBID",
		4: "REPLACE",
	}
	CronJob_ConcurrencyPolicy_value = map[string]int32{
		"UNSPECIFIED": 0,
		"UNKNOWN":     1,
		"ALLOW":       2,
		"FORBID":      3,
		"REPLACE":     4,
	}
)

func (x CronJob_ConcurrencyPolicy) Enum() *CronJob_ConcurrencyPolicy {
	p := new(CronJob_ConcurrencyPolicy)
	*p = x
	return p
}

func (x CronJob_ConcurrencyPolicy) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CronJob_ConcurrencyPolicy) Descriptor() protoreflect.EnumDescriptor {
	return file_k8s_v1_k8s_proto_enumTypes[9].Descriptor()
}

func (CronJob_ConcurrencyPolicy) Type() protoreflect.EnumType {
	return &file_k8s_v1_k8s_proto_enumTypes[9]
}

func (x CronJob_ConcurrencyPolicy) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CronJob_ConcurrencyPolicy.Descriptor instead.
func (CronJob_ConcurrencyPolicy) EnumDescriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{57, 0}
}

type ListNamespaceEventsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clientset string      `protobuf:"bytes,1,opt,name=clientset,proto3" json:"clientset,omitempty"`
	Cluster   string      `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace string      `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Types     []EventType `protobuf:"varint,4,rep,packed,name=types,proto3,enum=clutch.k8s.v1.EventType" json:"types,omitempty"` // Note object kind is Pod, in the future may support nodes as well
}

func (x *ListNamespaceEventsRequest) Reset() {
	*x = ListNamespaceEventsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListNamespaceEventsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNamespaceEventsRequest) ProtoMessage() {}

func (x *ListNamespaceEventsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNamespaceEventsRequest.ProtoReflect.Descriptor instead.
func (*ListNamespaceEventsRequest) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{0}
}

func (x *ListNamespaceEventsRequest) GetClientset() string {
	if x != nil {
		return x.Clientset
	}
	return ""
}

func (x *ListNamespaceEventsRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *ListNamespaceEventsRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *ListNamespaceEventsRequest) GetTypes() []EventType {
	if x != nil {
		return x.Types
	}
	return nil
}

type ListNamespaceEventsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Events []*Event `protobuf:"bytes,1,rep,name=events,proto3" json:"events,omitempty"`
}

func (x *ListNamespaceEventsResponse) Reset() {
	*x = ListNamespaceEventsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListNamespaceEventsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNamespaceEventsResponse) ProtoMessage() {}

func (x *ListNamespaceEventsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNamespaceEventsResponse.ProtoReflect.Descriptor instead.
func (*ListNamespaceEventsResponse) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{1}
}

func (x *ListNamespaceEventsResponse) GetEvents() []*Event {
	if x != nil {
		return x.Events
	}
	return nil
}

type DescribePodRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clientset string `protobuf:"bytes,1,opt,name=clientset,proto3" json:"clientset,omitempty"`
	Cluster   string `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace string `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Name      string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DescribePodRequest) Reset() {
	*x = DescribePodRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribePodRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribePodRequest) ProtoMessage() {}

func (x *DescribePodRequest) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribePodRequest.ProtoReflect.Descriptor instead.
func (*DescribePodRequest) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{2}
}

func (x *DescribePodRequest) GetClientset() string {
	if x != nil {
		return x.Clientset
	}
	return ""
}

func (x *DescribePodRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *DescribePodRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *DescribePodRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DescribePodResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pod *Pod `protobuf:"bytes,1,opt,name=pod,proto3" json:"pod,omitempty"`
}

func (x *DescribePodResponse) Reset() {
	*x = DescribePodResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribePodResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribePodResponse) ProtoMessage() {}

func (x *DescribePodResponse) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribePodResponse.ProtoReflect.Descriptor instead.
func (*DescribePodResponse) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{3}
}

func (x *DescribePodResponse) GetPod() *Pod {
	if x != nil {
		return x.Pod
	}
	return nil
}

// TODO(maybe): Identify with resource annotations.
type Container struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name         string          `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Image        string          `protobuf:"bytes,2,opt,name=image,proto3" json:"image,omitempty"`
	State        Container_State `protobuf:"varint,3,opt,name=state,proto3,enum=clutch.k8s.v1.Container_State" json:"state,omitempty"`
	Ready        bool            `protobuf:"varint,4,opt,name=ready,proto3" json:"ready,omitempty"`
	RestartCount int32           `protobuf:"varint,5,opt,name=restart_count,json=restartCount,proto3" json:"restart_count,omitempty"`
	// ref: https://pkg.go.dev/k8s.io/api/core/v1#ContainerState
	//
	// Types that are assignable to StateDetails:
	//
	//	*Container_StateWaiting
	//	*Container_StateRunning
	//	*Container_StateTerminated
	StateDetails isContainer_StateDetails `protobuf_oneof:"state_details"`
}

func (x *Container) Reset() {
	*x = Container{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Container) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Container) ProtoMessage() {}

func (x *Container) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Container.ProtoReflect.Descriptor instead.
func (*Container) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{4}
}

func (x *Container) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Container) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *Container) GetState() Container_State {
	if x != nil {
		return x.State
	}
	return Container_UNSPECIFIED
}

func (x *Container) GetReady() bool {
	if x != nil {
		return x.Ready
	}
	return false
}

func (x *Container) GetRestartCount() int32 {
	if x != nil {
		return x.RestartCount
	}
	return 0
}

func (m *Container) GetStateDetails() isContainer_StateDetails {
	if m != nil {
		return m.StateDetails
	}
	return nil
}

func (x *Container) GetStateWaiting() *StateWaiting {
	if x, ok := x.GetStateDetails().(*Container_StateWaiting); ok {
		return x.StateWaiting
	}
	return nil
}

func (x *Container) GetStateRunning() *StateRunning {
	if x, ok := x.GetStateDetails().(*Container_StateRunning); ok {
		return x.StateRunning
	}
	return nil
}

func (x *Container) GetStateTerminated() *StateTerminated {
	if x, ok := x.GetStateDetails().(*Container_StateTerminated); ok {
		return x.StateTerminated
	}
	return nil
}

type isContainer_StateDetails interface {
	isContainer_StateDetails()
}

type Container_StateWaiting struct {
	StateWaiting *StateWaiting `protobuf:"bytes,6,opt,name=state_waiting,json=stateWaiting,proto3,oneof"`
}

type Container_StateRunning struct {
	StateRunning *StateRunning `protobuf:"bytes,7,opt,name=state_running,json=stateRunning,proto3,oneof"`
}

type Container_StateTerminated struct {
	StateTerminated *StateTerminated `protobuf:"bytes,8,opt,name=state_terminated,json=stateTerminated,proto3,oneof"`
}

func (*Container_StateWaiting) isContainer_StateDetails() {}

func (*Container_StateRunning) isContainer_StateDetails() {}

func (*Container_StateTerminated) isContainer_StateDetails() {}

// ref: https://pkg.go.dev/k8s.io/api/core/v1#ContainerStateWaiting
type StateWaiting struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Reason  string `protobuf:"bytes,1,opt,name=reason,proto3" json:"reason,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *StateWaiting) Reset() {
	*x = StateWaiting{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StateWaiting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StateWaiting) ProtoMessage() {}

func (x *StateWaiting) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StateWaiting.ProtoReflect.Descriptor instead.
func (*StateWaiting) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{5}
}

func (x *StateWaiting) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *StateWaiting) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// ref: https://pkg.go.dev/k8s.io/api/core/v1#ContainerStateRunning
type StateRunning struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartTime *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
}

func (x *StateRunning) Reset() {
	*x = StateRunning{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StateRunning) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StateRunning) ProtoMessage() {}

func (x *StateRunning) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StateRunning.ProtoReflect.Descriptor instead.
func (*StateRunning) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{6}
}

func (x *StateRunning) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

// ref: https://pkg.go.dev/k8s.io/api/core/v1#ContainerStateTerminated
type StateTerminated struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Reason   string `protobuf:"bytes,1,opt,name=reason,proto3" json:"reason,omitempty"`
	Message  string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	ExitCode int32  `protobuf:"varint,3,opt,name=exit_code,json=exitCode,proto3" json:"exit_code,omitempty"`
	Signal   int32  `protobuf:"varint,4,opt,name=signal,proto3" json:"signal,omitempty"`
}

func (x *StateTerminated) Reset() {
	*x = StateTerminated{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StateTerminated) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StateTerminated) ProtoMessage() {}

func (x *StateTerminated) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StateTerminated.ProtoReflect.Descriptor instead.
func (*StateTerminated) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{7}
}

func (x *StateTerminated) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *StateTerminated) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *StateTerminated) GetExitCode() int32 {
	if x != nil {
		return x.ExitCode
	}
	return 0
}

func (x *StateTerminated) GetSignal() int32 {
	if x != nil {
		return x.Signal
	}
	return 0
}

type PodCondition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type   PodCondition_Type   `protobuf:"varint,1,opt,name=type,proto3,enum=clutch.k8s.v1.PodCondition_Type" json:"type,omitempty"`
	Status PodCondition_Status `protobuf:"varint,2,opt,name=status,proto3,enum=clutch.k8s.v1.PodCondition_Status" json:"status,omitempty"`
}

func (x *PodCondition) Reset() {
	*x = PodCondition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PodCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PodCondition) ProtoMessage() {}

func (x *PodCondition) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PodCondition.ProtoReflect.Descriptor instead.
func (*PodCondition) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{8}
}

func (x *PodCondition) GetType() PodCondition_Type {
	if x != nil {
		return x.Type
	}
	return PodCondition_TYPE_UNSPECIFIED
}

func (x *PodCondition) GetStatus() PodCondition_Status {
	if x != nil {
		return x.Status
	}
	return PodCondition_STATUS_UNSPECIFIED
}

type Pod struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cluster     string                 `protobuf:"bytes,1,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace   string                 `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Name        string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Containers  []*Container           `protobuf:"bytes,4,rep,name=containers,proto3" json:"containers,omitempty"`
	NodeIp      string                 `protobuf:"bytes,5,opt,name=node_ip,json=nodeIp,proto3" json:"node_ip,omitempty"`
	PodIp       string                 `protobuf:"bytes,6,opt,name=pod_ip,json=podIp,proto3" json:"pod_ip,omitempty"`
	State       Pod_State              `protobuf:"varint,7,opt,name=state,proto3,enum=clutch.k8s.v1.Pod_State" json:"state,omitempty"`
	StartTime   *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	Labels      map[string]string      `protobuf:"bytes,9,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Annotations map[string]string      `protobuf:"bytes,10,rep,name=annotations,proto3" json:"annotations,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// message indicating details about why the pod is in this state.
	// https://pkg.go.dev/k8s.io/api/core/v1#PodStatus
	StateReason string `protobuf:"bytes,11,opt,name=state_reason,json=stateReason,proto3" json:"state_reason,omitempty"`
	// current service state of pod.
	// https://pkg.go.dev/k8s.io/api/core/v1#PodCondition
	PodConditions []*PodCondition `protobuf:"bytes,12,rep,name=pod_conditions,json=podConditions,proto3" json:"pod_conditions,omitempty"`
	// one entry per init container.
	// ref: https://pkg.go.dev/k8s.io/api/core/v1#PodStatus
	InitContainers []*Container `protobuf:"bytes,13,rep,name=init_containers,json=initContainers,proto3" json:"init_containers,omitempty"`
	// status string calculated based on the same logic as kubectl
	// which summarizes the current status of the pod depending on
	// the status of its containers.
	// ref: https://github.com/kubernetes/kubernetes/blob/master/pkg/printers/internalversion/printers.go#L761
	Status string `protobuf:"bytes,14,opt,name=status,proto3" json:"status,omitempty"`
	// This is here as a workound since protobufjs currently has serialization
	// issues for well-known types like google.protobuf.Timestamp
	// Unix timestamp (milliseconds since Jan 01 1970)
	StartTimeMillis int64 `protobuf:"varint,15,opt,name=start_time_millis,json=startTimeMillis,proto3" json:"start_time_millis,omitempty"`
}

func (x *Pod) Reset() {
	*x = Pod{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Pod) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pod) ProtoMessage() {}

func (x *Pod) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pod.ProtoReflect.Descriptor instead.
func (*Pod) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{9}
}

func (x *Pod) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *Pod) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *Pod) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Pod) GetContainers() []*Container {
	if x != nil {
		return x.Containers
	}
	return nil
}

func (x *Pod) GetNodeIp() string {
	if x != nil {
		return x.NodeIp
	}
	return ""
}

func (x *Pod) GetPodIp() string {
	if x != nil {
		return x.PodIp
	}
	return ""
}

func (x *Pod) GetState() Pod_State {
	if x != nil {
		return x.State
	}
	return Pod_UNSPECIFIED
}

func (x *Pod) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *Pod) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *Pod) GetAnnotations() map[string]string {
	if x != nil {
		return x.Annotations
	}
	return nil
}

func (x *Pod) GetStateReason() string {
	if x != nil {
		return x.StateReason
	}
	return ""
}

func (x *Pod) GetPodConditions() []*PodCondition {
	if x != nil {
		return x.PodConditions
	}
	return nil
}

func (x *Pod) GetInitContainers() []*Container {
	if x != nil {
		return x.InitContainers
	}
	return nil
}

func (x *Pod) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Pod) GetStartTimeMillis() int64 {
	if x != nil {
		return x.StartTimeMillis
	}
	return 0
}

type ListOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Labels map[string]string `protobuf:"bytes,9,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// This selector string will be concatenated to the string that is
	// formed from formatting the labels.
	// This is useful in the case of having Set-based selectors rather
	// than just equality-based selectors which is how the map is formatted.
	SupplementalSelectorString string `protobuf:"bytes,10,opt,name=supplemental_selector_string,json=supplementalSelectorString,proto3" json:"supplemental_selector_string,omitempty"`
}

func (x *ListOptions) Reset() {
	*x = ListOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOptions) ProtoMessage() {}

func (x *ListOptions) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOptions.ProtoReflect.Descriptor instead.
func (*ListOptions) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{10}
}

func (x *ListOptions) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *ListOptions) GetSupplementalSelectorString() string {
	if x != nil {
		return x.SupplementalSelectorString
	}
	return ""
}

type ListPodsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clientset string       `protobuf:"bytes,1,opt,name=clientset,proto3" json:"clientset,omitempty"`
	Cluster   string       `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace string       `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Options   *ListOptions `protobuf:"bytes,4,opt,name=options,proto3" json:"options,omitempty"`
}

func (x *ListPodsRequest) Reset() {
	*x = ListPodsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPodsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPodsRequest) ProtoMessage() {}

func (x *ListPodsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPodsRequest.ProtoReflect.Descriptor instead.
func (*ListPodsRequest) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{11}
}

func (x *ListPodsRequest) GetClientset() string {
	if x != nil {
		return x.Clientset
	}
	return ""
}

func (x *ListPodsRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *ListPodsRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *ListPodsRequest) GetOptions() *ListOptions {
	if x != nil {
		return x.Options
	}
	return nil
}

type ListPodsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pods            []*Pod           `protobuf:"bytes,1,rep,name=pods,proto3" json:"pods,omitempty"`
	PartialFailures []*status.Status `protobuf:"bytes,2,rep,name=partial_failures,json=partialFailures,proto3" json:"partial_failures,omitempty"`
}

func (x *ListPodsResponse) Reset() {
	*x = ListPodsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPodsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPodsResponse) ProtoMessage() {}

func (x *ListPodsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPodsResponse.ProtoReflect.Descriptor instead.
func (*ListPodsResponse) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{12}
}

func (x *ListPodsResponse) GetPods() []*Pod {
	if x != nil {
		return x.Pods
	}
	return nil
}

func (x *ListPodsResponse) GetPartialFailures() []*status.Status {
	if x != nil {
		return x.PartialFailures
	}
	return nil
}

type DeletePodRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clientset string `protobuf:"bytes,1,opt,name=clientset,proto3" json:"clientset,omitempty"`
	Cluster   string `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace string `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Name      string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DeletePodRequest) Reset() {
	*x = DeletePodRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePodRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePodRequest) ProtoMessage() {}

func (x *DeletePodRequest) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePodRequest.ProtoReflect.Descriptor instead.
func (*DeletePodRequest) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{13}
}

func (x *DeletePodRequest) GetClientset() string {
	if x != nil {
		return x.Clientset
	}
	return ""
}

func (x *DeletePodRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *DeletePodRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *DeletePodRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DeletePodResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeletePodResponse) Reset() {
	*x = DeletePodResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePodResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePodResponse) ProtoMessage() {}

func (x *DeletePodResponse) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePodResponse.ProtoReflect.Descriptor instead.
func (*DeletePodResponse) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{14}
}

type UpdatePodRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clientset string `protobuf:"bytes,1,opt,name=clientset,proto3" json:"clientset,omitempty"`
	Cluster   string `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace string `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Name      string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// Preconditions to check before updating the pod's metadata
	//
	// Note: An empty StringValue signals that the label/annotation should not be set
	ExpectedObjectMetaFields *ExpectedObjectMetaFields `protobuf:"bytes,5,opt,name=expected_object_meta_fields,json=expectedObjectMetaFields,proto3" json:"expected_object_meta_fields,omitempty"`
	// Metadata fields to update
	ObjectMetaFields *ObjectMetaFields `protobuf:"bytes,6,opt,name=object_meta_fields,json=objectMetaFields,proto3" json:"object_meta_fields,omitempty"`
	// Metadata fields to remove
	RemoveObjectMetaFields *RemoveObjectMetaFields `protobuf:"bytes,7,opt,name=remove_object_meta_fields,json=removeObjectMetaFields,proto3" json:"remove_object_meta_fields,omitempty"`
}

func (x *UpdatePodRequest) Reset() {
	*x = UpdatePodRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePodRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePodRequest) ProtoMessage() {}

func (x *UpdatePodRequest) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePodRequest.ProtoReflect.Descriptor instead.
func (*UpdatePodRequest) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{15}
}

func (x *UpdatePodRequest) GetClientset() string {
	if x != nil {
		return x.Clientset
	}
	return ""
}

func (x *UpdatePodRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *UpdatePodRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *UpdatePodRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdatePodRequest) GetExpectedObjectMetaFields() *ExpectedObjectMetaFields {
	if x != nil {
		return x.ExpectedObjectMetaFields
	}
	return nil
}

func (x *UpdatePodRequest) GetObjectMetaFields() *ObjectMetaFields {
	if x != nil {
		return x.ObjectMetaFields
	}
	return nil
}

func (x *UpdatePodRequest) GetRemoveObjectMetaFields() *RemoveObjectMetaFields {
	if x != nil {
		return x.RemoveObjectMetaFields
	}
	return nil
}

type UpdatePodResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdatePodResponse) Reset() {
	*x = UpdatePodResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePodResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePodResponse) ProtoMessage() {}

func (x *UpdatePodResponse) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePodResponse.ProtoReflect.Descriptor instead.
func (*UpdatePodResponse) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{16}
}

type GetPodLogsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clientset string          `protobuf:"bytes,1,opt,name=clientset,proto3" json:"clientset,omitempty"`
	Cluster   string          `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace string          `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Name      string          `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Options   *PodLogsOptions `protobuf:"bytes,5,opt,name=options,proto3" json:"options,omitempty"`
}

func (x *GetPodLogsRequest) Reset() {
	*x = GetPodLogsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPodLogsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPodLogsRequest) ProtoMessage() {}

func (x *GetPodLogsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPodLogsRequest.ProtoReflect.Descriptor instead.
func (*GetPodLogsRequest) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{17}
}

func (x *GetPodLogsRequest) GetClientset() string {
	if x != nil {
		return x.Clientset
	}
	return ""
}

func (x *GetPodLogsRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *GetPodLogsRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *GetPodLogsRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetPodLogsRequest) GetOptions() *PodLogsOptions {
	if x != nil {
		return x.Options
	}
	return nil
}

type PodLogsOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The container for which to stream logs. Defaults to only container if there is one container in the pod.
	ContainerName string `protobuf:"bytes,1,opt,name=container_name,json=containerName,proto3" json:"container_name,omitempty"`
	// Return previous terminated container logs if true.
	Previous bool `protobuf:"varint,2,opt,name=previous,proto3" json:"previous,omitempty"`
	// An RFC3339 timestamp from which to show logs, e.g. 2022-11-07T19:30:38.974187286Z. This can be retrieved directly
	// from a previous API call.
	SinceTs string `protobuf:"bytes,3,opt,name=since_ts,json=sinceTs,proto3" json:"since_ts,omitempty"`
	// If set, the number of lines from the end of the logs to show. If not specified, logs are shown from the creation of
	// the container or sinceTime.
	TailNumLines int64 `protobuf:"varint,4,opt,name=tail_num_lines,json=tailNumLines,proto3" json:"tail_num_lines,omitempty"`
}

func (x *PodLogsOptions) Reset() {
	*x = PodLogsOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PodLogsOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PodLogsOptions) ProtoMessage() {}

func (x *PodLogsOptions) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PodLogsOptions.ProtoReflect.Descriptor instead.
func (*PodLogsOptions) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{18}
}

func (x *PodLogsOptions) GetContainerName() string {
	if x != nil {
		return x.ContainerName
	}
	return ""
}

func (x *PodLogsOptions) GetPrevious() bool {
	if x != nil {
		return x.Previous
	}
	return false
}

func (x *PodLogsOptions) GetSinceTs() string {
	if x != nil {
		return x.SinceTs
	}
	return ""
}

func (x *PodLogsOptions) GetTailNumLines() int64 {
	if x != nil {
		return x.TailNumLines
	}
	return 0
}

type GetPodLogsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LatestTs string        `protobuf:"bytes,1,opt,name=latest_ts,json=latestTs,proto3" json:"latest_ts,omitempty"`
	Logs     []*PodLogLine `protobuf:"bytes,2,rep,name=logs,proto3" json:"logs,omitempty"`
}

func (x *GetPodLogsResponse) Reset() {
	*x = GetPodLogsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPodLogsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPodLogsResponse) ProtoMessage() {}

func (x *GetPodLogsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPodLogsResponse.ProtoReflect.Descriptor instead.
func (*GetPodLogsResponse) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{19}
}

func (x *GetPodLogsResponse) GetLatestTs() string {
	if x != nil {
		return x.LatestTs
	}
	return ""
}

func (x *GetPodLogsResponse) GetLogs() []*PodLogLine {
	if x != nil {
		return x.Logs
	}
	return nil
}

type PodLogLine struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts string `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	S  string `protobuf:"bytes,2,opt,name=s,proto3" json:"s,omitempty"`
}

func (x *PodLogLine) Reset() {
	*x = PodLogLine{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PodLogLine) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PodLogLine) ProtoMessage() {}

func (x *PodLogLine) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PodLogLine.ProtoReflect.Descriptor instead.
func (*PodLogLine) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{20}
}

func (x *PodLogLine) GetTs() string {
	if x != nil {
		return x.Ts
	}
	return ""
}

func (x *PodLogLine) GetS() string {
	if x != nil {
		return x.S
	}
	return ""
}

type HPA struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cluster     string            `protobuf:"bytes,1,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace   string            `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Name        string            `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Sizing      *HPA_Sizing       `protobuf:"bytes,4,opt,name=sizing,proto3" json:"sizing,omitempty"`
	Labels      map[string]string `protobuf:"bytes,5,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Annotations map[string]string `protobuf:"bytes,6,rep,name=annotations,proto3" json:"annotations,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *HPA) Reset() {
	*x = HPA{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HPA) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HPA) ProtoMessage() {}

func (x *HPA) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HPA.ProtoReflect.Descriptor instead.
func (*HPA) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{21}
}

func (x *HPA) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *HPA) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *HPA) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *HPA) GetSizing() *HPA_Sizing {
	if x != nil {
		return x.Sizing
	}
	return nil
}

func (x *HPA) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *HPA) GetAnnotations() map[string]string {
	if x != nil {
		return x.Annotations
	}
	return nil
}

type ResizeHPARequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clientset     string                   `protobuf:"bytes,1,opt,name=clientset,proto3" json:"clientset,omitempty"`
	Cluster       string                   `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace     string                   `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Name          string                   `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Sizing        *ResizeHPARequest_Sizing `protobuf:"bytes,5,opt,name=sizing,proto3" json:"sizing,omitempty"`
	CurrentSizing *ResizeHPARequest_Sizing `protobuf:"bytes,6,opt,name=current_sizing,json=currentSizing,proto3" json:"current_sizing,omitempty"`
}

func (x *ResizeHPARequest) Reset() {
	*x = ResizeHPARequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResizeHPARequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResizeHPARequest) ProtoMessage() {}

func (x *ResizeHPARequest) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResizeHPARequest.ProtoReflect.Descriptor instead.
func (*ResizeHPARequest) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{22}
}

func (x *ResizeHPARequest) GetClientset() string {
	if x != nil {
		return x.Clientset
	}
	return ""
}

func (x *ResizeHPARequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *ResizeHPARequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *ResizeHPARequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ResizeHPARequest) GetSizing() *ResizeHPARequest_Sizing {
	if x != nil {
		return x.Sizing
	}
	return nil
}

func (x *ResizeHPARequest) GetCurrentSizing() *ResizeHPARequest_Sizing {
	if x != nil {
		return x.CurrentSizing
	}
	return nil
}

type ResizeHPAResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ResizeHPAResponse) Reset() {
	*x = ResizeHPAResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResizeHPAResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResizeHPAResponse) ProtoMessage() {}

func (x *ResizeHPAResponse) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResizeHPAResponse.ProtoReflect.Descriptor instead.
func (*ResizeHPAResponse) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{23}
}

type DeleteHPARequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clientset string `protobuf:"bytes,1,opt,name=clientset,proto3" json:"clientset,omitempty"`
	Cluster   string `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace string `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Name      string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DeleteHPARequest) Reset() {
	*x = DeleteHPARequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteHPARequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteHPARequest) ProtoMessage() {}

func (x *DeleteHPARequest) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteHPARequest.ProtoReflect.Descriptor instead.
func (*DeleteHPARequest) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{24}
}

func (x *DeleteHPARequest) GetClientset() string {
	if x != nil {
		return x.Clientset
	}
	return ""
}

func (x *DeleteHPARequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *DeleteHPARequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *DeleteHPARequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DeleteHPAResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteHPAResponse) Reset() {
	*x = DeleteHPAResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteHPAResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteHPAResponse) ProtoMessage() {}

func (x *DeleteHPAResponse) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteHPAResponse.ProtoReflect.Descriptor instead.
func (*DeleteHPAResponse) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{25}
}

type ExecAction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Command []string `protobuf:"bytes,1,rep,name=command,proto3" json:"command,omitempty"`
}

func (x *ExecAction) Reset() {
	*x = ExecAction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExecAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecAction) ProtoMessage() {}

func (x *ExecAction) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecAction.ProtoReflect.Descriptor instead.
func (*ExecAction) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{26}
}

func (x *ExecAction) GetCommand() []string {
	if x != nil {
		return x.Command
	}
	return nil
}

type HTTPGetAction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path        string        `protobuf:"bytes,1,opt,name=path,proto3" json:"path,omitempty"`
	Port        int32         `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
	Host        string        `protobuf:"bytes,3,opt,name=host,proto3" json:"host,omitempty"`
	Scheme      string        `protobuf:"bytes,4,opt,name=scheme,proto3" json:"scheme,omitempty"`
	HttpHeaders []*HTTPHeader `protobuf:"bytes,5,rep,name=http_headers,json=httpHeaders,proto3" json:"http_headers,omitempty"`
}

func (x *HTTPGetAction) Reset() {
	*x = HTTPGetAction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HTTPGetAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HTTPGetAction) ProtoMessage() {}

func (x *HTTPGetAction) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HTTPGetAction.ProtoReflect.Descriptor instead.
func (*HTTPGetAction) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{27}
}

func (x *HTTPGetAction) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *HTTPGetAction) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *HTTPGetAction) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *HTTPGetAction) GetScheme() string {
	if x != nil {
		return x.Scheme
	}
	return ""
}

func (x *HTTPGetAction) GetHttpHeaders() []*HTTPHeader {
	if x != nil {
		return x.HttpHeaders
	}
	return nil
}

type HTTPHeader struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  *string `protobuf:"bytes,1,opt,name=name,proto3,oneof" json:"name,omitempty"`
	Value *string `protobuf:"bytes,2,opt,name=value,proto3,oneof" json:"value,omitempty"`
}

func (x *HTTPHeader) Reset() {
	*x = HTTPHeader{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HTTPHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HTTPHeader) ProtoMessage() {}

func (x *HTTPHeader) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HTTPHeader.ProtoReflect.Descriptor instead.
func (*HTTPHeader) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{28}
}

func (x *HTTPHeader) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *HTTPHeader) GetValue() string {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return ""
}

type TCPSocketAction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Port int32  `protobuf:"varint,1,opt,name=port,proto3" json:"port,omitempty"`
	Host string `protobuf:"bytes,2,opt,name=host,proto3" json:"host,omitempty"`
}

func (x *TCPSocketAction) Reset() {
	*x = TCPSocketAction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TCPSocketAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TCPSocketAction) ProtoMessage() {}

func (x *TCPSocketAction) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TCPSocketAction.ProtoReflect.Descriptor instead.
func (*TCPSocketAction) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{29}
}

func (x *TCPSocketAction) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *TCPSocketAction) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

type GRPCAction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Port    int32  `protobuf:"varint,1,opt,name=port,proto3" json:"port,omitempty"`
	Service string `protobuf:"bytes,2,opt,name=service,proto3" json:"service,omitempty"`
}

func (x *GRPCAction) Reset() {
	*x = GRPCAction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GRPCAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GRPCAction) ProtoMessage() {}

func (x *GRPCAction) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GRPCAction.ProtoReflect.Descriptor instead.
func (*GRPCAction) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{30}
}

func (x *GRPCAction) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *GRPCAction) GetService() string {
	if x != nil {
		return x.Service
	}
	return ""
}

type Probe struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Handler:
	//
	//	*Probe_Exec
	//	*Probe_HttpGet
	//	*Probe_TcpSocket
	//	*Probe_Grpc
	Handler                       isProbe_Handler `protobuf_oneof:"handler"`
	InitialDelaySeconds           *int32          `protobuf:"varint,5,opt,name=initial_delay_seconds,json=initialDelaySeconds,proto3,oneof" json:"initial_delay_seconds,omitempty"`
	TimeoutSeconds                *int32          `protobuf:"varint,6,opt,name=timeout_seconds,json=timeoutSeconds,proto3,oneof" json:"timeout_seconds,omitempty"`
	PeriodSeconds                 *int32          `protobuf:"varint,7,opt,name=period_seconds,json=periodSeconds,proto3,oneof" json:"period_seconds,omitempty"`
	SuccessThreshold              *int32          `protobuf:"varint,8,opt,name=success_threshold,json=successThreshold,proto3,oneof" json:"success_threshold,omitempty"`
	FailureThreshold              *int32          `protobuf:"varint,9,opt,name=failure_threshold,json=failureThreshold,proto3,oneof" json:"failure_threshold,omitempty"`
	TerminationGracePeriodSeconds *int64          `protobuf:"varint,10,opt,name=termination_grace_period_seconds,json=terminationGracePeriodSeconds,proto3,oneof" json:"termination_grace_period_seconds,omitempty"`
}

func (x *Probe) Reset() {
	*x = Probe{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Probe) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Probe) ProtoMessage() {}

func (x *Probe) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Probe.ProtoReflect.Descriptor instead.
func (*Probe) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{31}
}

func (m *Probe) GetHandler() isProbe_Handler {
	if m != nil {
		return m.Handler
	}
	return nil
}

func (x *Probe) GetExec() *ExecAction {
	if x, ok := x.GetHandler().(*Probe_Exec); ok {
		return x.Exec
	}
	return nil
}

func (x *Probe) GetHttpGet() *HTTPGetAction {
	if x, ok := x.GetHandler().(*Probe_HttpGet); ok {
		return x.HttpGet
	}
	return nil
}

func (x *Probe) GetTcpSocket() *TCPSocketAction {
	if x, ok := x.GetHandler().(*Probe_TcpSocket); ok {
		return x.TcpSocket
	}
	return nil
}

func (x *Probe) GetGrpc() *GRPCAction {
	if x, ok := x.GetHandler().(*Probe_Grpc); ok {
		return x.Grpc
	}
	return nil
}

func (x *Probe) GetInitialDelaySeconds() int32 {
	if x != nil && x.InitialDelaySeconds != nil {
		return *x.InitialDelaySeconds
	}
	return 0
}

func (x *Probe) GetTimeoutSeconds() int32 {
	if x != nil && x.TimeoutSeconds != nil {
		return *x.TimeoutSeconds
	}
	return 0
}

func (x *Probe) GetPeriodSeconds() int32 {
	if x != nil && x.PeriodSeconds != nil {
		return *x.PeriodSeconds
	}
	return 0
}

func (x *Probe) GetSuccessThreshold() int32 {
	if x != nil && x.SuccessThreshold != nil {
		return *x.SuccessThreshold
	}
	return 0
}

func (x *Probe) GetFailureThreshold() int32 {
	if x != nil && x.FailureThreshold != nil {
		return *x.FailureThreshold
	}
	return 0
}

func (x *Probe) GetTerminationGracePeriodSeconds() int64 {
	if x != nil && x.TerminationGracePeriodSeconds != nil {
		return *x.TerminationGracePeriodSeconds
	}
	return 0
}

type isProbe_Handler interface {
	isProbe_Handler()
}

type Probe_Exec struct {
	Exec *ExecAction `protobuf:"bytes,1,opt,name=exec,proto3,oneof"`
}

type Probe_HttpGet struct {
	HttpGet *HTTPGetAction `protobuf:"bytes,2,opt,name=http_get,json=httpGet,proto3,oneof"`
}

type Probe_TcpSocket struct {
	TcpSocket *TCPSocketAction `protobuf:"bytes,3,opt,name=tcp_socket,json=tcpSocket,proto3,oneof"`
}

type Probe_Grpc struct {
	Grpc *GRPCAction `protobuf:"bytes,4,opt,name=grpc,proto3,oneof"`
}

func (*Probe_Exec) isProbe_Handler() {}

func (*Probe_HttpGet) isProbe_Handler() {}

func (*Probe_TcpSocket) isProbe_Handler() {}

func (*Probe_Grpc) isProbe_Handler() {}

type Deployment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cluster          string                       `protobuf:"bytes,1,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace        string                       `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Name             string                       `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Labels           map[string]string            `protobuf:"bytes,4,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Annotations      map[string]string            `protobuf:"bytes,5,rep,name=annotations,proto3" json:"annotations,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	DeploymentStatus *Deployment_DeploymentStatus `protobuf:"bytes,6,opt,name=deployment_status,json=deploymentStatus,proto3" json:"deployment_status,omitempty"`
	// This is a workound since protobufjs currently has serialization
	// issues for well-known types like google.protobuf.Timestamp
	// Unix timestamp (milliseconds since Jan 01 1970)
	CreationTimeMillis int64                      `protobuf:"varint,7,opt,name=creation_time_millis,json=creationTimeMillis,proto3" json:"creation_time_millis,omitempty"`
	DeploymentSpec     *Deployment_DeploymentSpec `protobuf:"bytes,8,opt,name=deployment_spec,json=deploymentSpec,proto3" json:"deployment_spec,omitempty"`
}

func (x *Deployment) Reset() {
	*x = Deployment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Deployment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Deployment) ProtoMessage() {}

func (x *Deployment) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Deployment.ProtoReflect.Descriptor instead.
func (*Deployment) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{32}
}

func (x *Deployment) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *Deployment) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *Deployment) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Deployment) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *Deployment) GetAnnotations() map[string]string {
	if x != nil {
		return x.Annotations
	}
	return nil
}

func (x *Deployment) GetDeploymentStatus() *Deployment_DeploymentStatus {
	if x != nil {
		return x.DeploymentStatus
	}
	return nil
}

func (x *Deployment) GetCreationTimeMillis() int64 {
	if x != nil {
		return x.CreationTimeMillis
	}
	return 0
}

func (x *Deployment) GetDeploymentSpec() *Deployment_DeploymentSpec {
	if x != nil {
		return x.DeploymentSpec
	}
	return nil
}

type DescribeDeploymentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clientset string `protobuf:"bytes,1,opt,name=clientset,proto3" json:"clientset,omitempty"`
	Cluster   string `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace string `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Name      string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DescribeDeploymentRequest) Reset() {
	*x = DescribeDeploymentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeDeploymentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeDeploymentRequest) ProtoMessage() {}

func (x *DescribeDeploymentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeDeploymentRequest.ProtoReflect.Descriptor instead.
func (*DescribeDeploymentRequest) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{33}
}

func (x *DescribeDeploymentRequest) GetClientset() string {
	if x != nil {
		return x.Clientset
	}
	return ""
}

func (x *DescribeDeploymentRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *DescribeDeploymentRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *DescribeDeploymentRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DescribeDeploymentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Deployment *Deployment `protobuf:"bytes,1,opt,name=deployment,proto3" json:"deployment,omitempty"`
}

func (x *DescribeDeploymentResponse) Reset() {
	*x = DescribeDeploymentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeDeploymentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeDeploymentResponse) ProtoMessage() {}

func (x *DescribeDeploymentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeDeploymentResponse.ProtoReflect.Descriptor instead.
func (*DescribeDeploymentResponse) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{34}
}

func (x *DescribeDeploymentResponse) GetDeployment() *Deployment {
	if x != nil {
		return x.Deployment
	}
	return nil
}

type ListDeploymentsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clientset string       `protobuf:"bytes,1,opt,name=clientset,proto3" json:"clientset,omitempty"`
	Cluster   string       `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace string       `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Options   *ListOptions `protobuf:"bytes,4,opt,name=options,proto3" json:"options,omitempty"`
}

func (x *ListDeploymentsRequest) Reset() {
	*x = ListDeploymentsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDeploymentsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDeploymentsRequest) ProtoMessage() {}

func (x *ListDeploymentsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDeploymentsRequest.ProtoReflect.Descriptor instead.
func (*ListDeploymentsRequest) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{35}
}

func (x *ListDeploymentsRequest) GetClientset() string {
	if x != nil {
		return x.Clientset
	}
	return ""
}

func (x *ListDeploymentsRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *ListDeploymentsRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *ListDeploymentsRequest) GetOptions() *ListOptions {
	if x != nil {
		return x.Options
	}
	return nil
}

type ListDeploymentsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Deployments []*Deployment `protobuf:"bytes,1,rep,name=deployments,proto3" json:"deployments,omitempty"`
}

func (x *ListDeploymentsResponse) Reset() {
	*x = ListDeploymentsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDeploymentsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDeploymentsResponse) ProtoMessage() {}

func (x *ListDeploymentsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDeploymentsResponse.ProtoReflect.Descriptor instead.
func (*ListDeploymentsResponse) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{36}
}

func (x *ListDeploymentsResponse) GetDeployments() []*Deployment {
	if x != nil {
		return x.Deployments
	}
	return nil
}

type UpdateDeploymentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clientset string                          `protobuf:"bytes,1,opt,name=clientset,proto3" json:"clientset,omitempty"`
	Cluster   string                          `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace string                          `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Name      string                          `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Fields    *UpdateDeploymentRequest_Fields `protobuf:"bytes,5,opt,name=fields,proto3" json:"fields,omitempty"`
}

func (x *UpdateDeploymentRequest) Reset() {
	*x = UpdateDeploymentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDeploymentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDeploymentRequest) ProtoMessage() {}

func (x *UpdateDeploymentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDeploymentRequest.ProtoReflect.Descriptor instead.
func (*UpdateDeploymentRequest) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{37}
}

func (x *UpdateDeploymentRequest) GetClientset() string {
	if x != nil {
		return x.Clientset
	}
	return ""
}

func (x *UpdateDeploymentRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *UpdateDeploymentRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *UpdateDeploymentRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateDeploymentRequest) GetFields() *UpdateDeploymentRequest_Fields {
	if x != nil {
		return x.Fields
	}
	return nil
}

type UpdateDeploymentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateDeploymentResponse) Reset() {
	*x = UpdateDeploymentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDeploymentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDeploymentResponse) ProtoMessage() {}

func (x *UpdateDeploymentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDeploymentResponse.ProtoReflect.Descriptor instead.
func (*UpdateDeploymentResponse) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{38}
}

type DeleteDeploymentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clientset string `protobuf:"bytes,1,opt,name=clientset,proto3" json:"clientset,omitempty"`
	Cluster   string `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace string `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Name      string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DeleteDeploymentRequest) Reset() {
	*x = DeleteDeploymentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteDeploymentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDeploymentRequest) ProtoMessage() {}

func (x *DeleteDeploymentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDeploymentRequest.ProtoReflect.Descriptor instead.
func (*DeleteDeploymentRequest) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{39}
}

func (x *DeleteDeploymentRequest) GetClientset() string {
	if x != nil {
		return x.Clientset
	}
	return ""
}

func (x *DeleteDeploymentRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *DeleteDeploymentRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *DeleteDeploymentRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DeleteDeploymentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteDeploymentResponse) Reset() {
	*x = DeleteDeploymentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteDeploymentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDeploymentResponse) ProtoMessage() {}

func (x *DeleteDeploymentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDeploymentResponse.ProtoReflect.Descriptor instead.
func (*DeleteDeploymentResponse) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{40}
}

type StatefulSet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cluster     string              `protobuf:"bytes,1,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace   string              `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Name        string              `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Labels      map[string]string   `protobuf:"bytes,4,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Annotations map[string]string   `protobuf:"bytes,5,rep,name=annotations,proto3" json:"annotations,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Status      *StatefulSet_Status `protobuf:"bytes,6,opt,name=status,proto3" json:"status,omitempty"`
	// This is a workound since protobufjs currently has serialization
	// issues for well-known types like google.protobuf.Timestamp
	// Unix timestamp (milliseconds since Jan 01 1970)
	CreationTimeMillis int64 `protobuf:"varint,7,opt,name=creation_time_millis,json=creationTimeMillis,proto3" json:"creation_time_millis,omitempty"`
}

func (x *StatefulSet) Reset() {
	*x = StatefulSet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatefulSet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatefulSet) ProtoMessage() {}

func (x *StatefulSet) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatefulSet.ProtoReflect.Descriptor instead.
func (*StatefulSet) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{41}
}

func (x *StatefulSet) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *StatefulSet) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *StatefulSet) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *StatefulSet) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *StatefulSet) GetAnnotations() map[string]string {
	if x != nil {
		return x.Annotations
	}
	return nil
}

func (x *StatefulSet) GetStatus() *StatefulSet_Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *StatefulSet) GetCreationTimeMillis() int64 {
	if x != nil {
		return x.CreationTimeMillis
	}
	return 0
}

type DescribeStatefulSetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clientset string `protobuf:"bytes,1,opt,name=clientset,proto3" json:"clientset,omitempty"`
	Cluster   string `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace string `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Name      string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DescribeStatefulSetRequest) Reset() {
	*x = DescribeStatefulSetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeStatefulSetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeStatefulSetRequest) ProtoMessage() {}

func (x *DescribeStatefulSetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeStatefulSetRequest.ProtoReflect.Descriptor instead.
func (*DescribeStatefulSetRequest) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{42}
}

func (x *DescribeStatefulSetRequest) GetClientset() string {
	if x != nil {
		return x.Clientset
	}
	return ""
}

func (x *DescribeStatefulSetRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *DescribeStatefulSetRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *DescribeStatefulSetRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DescribeStatefulSetResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StatefulSet *StatefulSet `protobuf:"bytes,1,opt,name=stateful_set,json=statefulSet,proto3" json:"stateful_set,omitempty"`
}

func (x *DescribeStatefulSetResponse) Reset() {
	*x = DescribeStatefulSetResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeStatefulSetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeStatefulSetResponse) ProtoMessage() {}

func (x *DescribeStatefulSetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeStatefulSetResponse.ProtoReflect.Descriptor instead.
func (*DescribeStatefulSetResponse) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{43}
}

func (x *DescribeStatefulSetResponse) GetStatefulSet() *StatefulSet {
	if x != nil {
		return x.StatefulSet
	}
	return nil
}

type ListStatefulSetsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clientset string       `protobuf:"bytes,1,opt,name=clientset,proto3" json:"clientset,omitempty"`
	Cluster   string       `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace string       `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Options   *ListOptions `protobuf:"bytes,4,opt,name=options,proto3" json:"options,omitempty"`
}

func (x *ListStatefulSetsRequest) Reset() {
	*x = ListStatefulSetsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListStatefulSetsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStatefulSetsRequest) ProtoMessage() {}

func (x *ListStatefulSetsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStatefulSetsRequest.ProtoReflect.Descriptor instead.
func (*ListStatefulSetsRequest) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{44}
}

func (x *ListStatefulSetsRequest) GetClientset() string {
	if x != nil {
		return x.Clientset
	}
	return ""
}

func (x *ListStatefulSetsRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *ListStatefulSetsRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *ListStatefulSetsRequest) GetOptions() *ListOptions {
	if x != nil {
		return x.Options
	}
	return nil
}

type ListStatefulSetsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StatefulSets []*StatefulSet `protobuf:"bytes,1,rep,name=stateful_sets,json=statefulSets,proto3" json:"stateful_sets,omitempty"`
}

func (x *ListStatefulSetsResponse) Reset() {
	*x = ListStatefulSetsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListStatefulSetsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStatefulSetsResponse) ProtoMessage() {}

func (x *ListStatefulSetsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStatefulSetsResponse.ProtoReflect.Descriptor instead.
func (*ListStatefulSetsResponse) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{45}
}

func (x *ListStatefulSetsResponse) GetStatefulSets() []*StatefulSet {
	if x != nil {
		return x.StatefulSets
	}
	return nil
}

type DeleteStatefulSetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clientset string `protobuf:"bytes,1,opt,name=clientset,proto3" json:"clientset,omitempty"`
	Cluster   string `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace string `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Name      string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DeleteStatefulSetRequest) Reset() {
	*x = DeleteStatefulSetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteStatefulSetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteStatefulSetRequest) ProtoMessage() {}

func (x *DeleteStatefulSetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteStatefulSetRequest.ProtoReflect.Descriptor instead.
func (*DeleteStatefulSetRequest) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{46}
}

func (x *DeleteStatefulSetRequest) GetClientset() string {
	if x != nil {
		return x.Clientset
	}
	return ""
}

func (x *DeleteStatefulSetRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *DeleteStatefulSetRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *DeleteStatefulSetRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DeleteStatefulSetResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteStatefulSetResponse) Reset() {
	*x = DeleteStatefulSetResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteStatefulSetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteStatefulSetResponse) ProtoMessage() {}

func (x *DeleteStatefulSetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteStatefulSetResponse.ProtoReflect.Descriptor instead.
func (*DeleteStatefulSetResponse) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{47}
}

type UpdateStatefulSetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clientset string                           `protobuf:"bytes,1,opt,name=clientset,proto3" json:"clientset,omitempty"`
	Cluster   string                           `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace string                           `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Name      string                           `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Fields    *UpdateStatefulSetRequest_Fields `protobuf:"bytes,5,opt,name=fields,proto3" json:"fields,omitempty"`
}

func (x *UpdateStatefulSetRequest) Reset() {
	*x = UpdateStatefulSetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateStatefulSetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateStatefulSetRequest) ProtoMessage() {}

func (x *UpdateStatefulSetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateStatefulSetRequest.ProtoReflect.Descriptor instead.
func (*UpdateStatefulSetRequest) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{48}
}

func (x *UpdateStatefulSetRequest) GetClientset() string {
	if x != nil {
		return x.Clientset
	}
	return ""
}

func (x *UpdateStatefulSetRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *UpdateStatefulSetRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *UpdateStatefulSetRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateStatefulSetRequest) GetFields() *UpdateStatefulSetRequest_Fields {
	if x != nil {
		return x.Fields
	}
	return nil
}

type UpdateStatefulSetResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateStatefulSetResponse) Reset() {
	*x = UpdateStatefulSetResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateStatefulSetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateStatefulSetResponse) ProtoMessage() {}

func (x *UpdateStatefulSetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateStatefulSetResponse.ProtoReflect.Descriptor instead.
func (*UpdateStatefulSetResponse) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{49}
}

type Service struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cluster     string            `protobuf:"bytes,1,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace   string            `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Name        string            `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Type        Service_Type      `protobuf:"varint,4,opt,name=type,proto3,enum=clutch.k8s.v1.Service_Type" json:"type,omitempty"`
	Labels      map[string]string `protobuf:"bytes,5,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Annotations map[string]string `protobuf:"bytes,6,rep,name=annotations,proto3" json:"annotations,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// service traffic routed to pods with label keys
	// and values matching this selector.
	// ref: https://pkg.go.dev/k8s.io/api/core/v1#ServiceSpec
	Selector map[string]string `protobuf:"bytes,7,rep,name=selector,proto3" json:"selector,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Service) Reset() {
	*x = Service{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Service) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Service) ProtoMessage() {}

func (x *Service) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Service.ProtoReflect.Descriptor instead.
func (*Service) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{50}
}

func (x *Service) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *Service) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *Service) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Service) GetType() Service_Type {
	if x != nil {
		return x.Type
	}
	return Service_UNSPECIFIED
}

func (x *Service) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *Service) GetAnnotations() map[string]string {
	if x != nil {
		return x.Annotations
	}
	return nil
}

func (x *Service) GetSelector() map[string]string {
	if x != nil {
		return x.Selector
	}
	return nil
}

type DescribeServiceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clientset string `protobuf:"bytes,1,opt,name=clientset,proto3" json:"clientset,omitempty"`
	Cluster   string `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace string `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Name      string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DescribeServiceRequest) Reset() {
	*x = DescribeServiceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeServiceRequest) ProtoMessage() {}

func (x *DescribeServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeServiceRequest.ProtoReflect.Descriptor instead.
func (*DescribeServiceRequest) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{51}
}

func (x *DescribeServiceRequest) GetClientset() string {
	if x != nil {
		return x.Clientset
	}
	return ""
}

func (x *DescribeServiceRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *DescribeServiceRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *DescribeServiceRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DescribeServiceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Service *Service `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
}

func (x *DescribeServiceResponse) Reset() {
	*x = DescribeServiceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeServiceResponse) ProtoMessage() {}

func (x *DescribeServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeServiceResponse.ProtoReflect.Descriptor instead.
func (*DescribeServiceResponse) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{52}
}

func (x *DescribeServiceResponse) GetService() *Service {
	if x != nil {
		return x.Service
	}
	return nil
}

type ListServicesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clientset string       `protobuf:"bytes,1,opt,name=clientset,proto3" json:"clientset,omitempty"`
	Cluster   string       `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace string       `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Options   *ListOptions `protobuf:"bytes,4,opt,name=options,proto3" json:"options,omitempty"`
}

func (x *ListServicesRequest) Reset() {
	*x = ListServicesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServicesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServicesRequest) ProtoMessage() {}

func (x *ListServicesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServicesRequest.ProtoReflect.Descriptor instead.
func (*ListServicesRequest) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{53}
}

func (x *ListServicesRequest) GetClientset() string {
	if x != nil {
		return x.Clientset
	}
	return ""
}

func (x *ListServicesRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *ListServicesRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *ListServicesRequest) GetOptions() *ListOptions {
	if x != nil {
		return x.Options
	}
	return nil
}

type ListServicesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Services []*Service `protobuf:"bytes,1,rep,name=services,proto3" json:"services,omitempty"`
}

func (x *ListServicesResponse) Reset() {
	*x = ListServicesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServicesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServicesResponse) ProtoMessage() {}

func (x *ListServicesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServicesResponse.ProtoReflect.Descriptor instead.
func (*ListServicesResponse) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{54}
}

func (x *ListServicesResponse) GetServices() []*Service {
	if x != nil {
		return x.Services
	}
	return nil
}

type DeleteServiceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clientset string `protobuf:"bytes,1,opt,name=clientset,proto3" json:"clientset,omitempty"`
	Cluster   string `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace string `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Name      string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DeleteServiceRequest) Reset() {
	*x = DeleteServiceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteServiceRequest) ProtoMessage() {}

func (x *DeleteServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteServiceRequest.ProtoReflect.Descriptor instead.
func (*DeleteServiceRequest) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{55}
}

func (x *DeleteServiceRequest) GetClientset() string {
	if x != nil {
		return x.Clientset
	}
	return ""
}

func (x *DeleteServiceRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *DeleteServiceRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *DeleteServiceRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DeleteServiceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteServiceResponse) Reset() {
	*x = DeleteServiceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteServiceResponse) ProtoMessage() {}

func (x *DeleteServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteServiceResponse.ProtoReflect.Descriptor instead.
func (*DeleteServiceResponse) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{56}
}

type CronJob struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cluster                 string                    `protobuf:"bytes,1,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace               string                    `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Name                    string                    `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Schedule                string                    `protobuf:"bytes,4,opt,name=schedule,proto3" json:"schedule,omitempty"`
	Labels                  map[string]string         `protobuf:"bytes,5,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Annotations             map[string]string         `protobuf:"bytes,6,rep,name=annotations,proto3" json:"annotations,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Suspend                 bool                      `protobuf:"varint,7,opt,name=suspend,proto3" json:"suspend,omitempty"`
	NumActiveJobs           int32                     `protobuf:"varint,8,opt,name=num_active_jobs,json=numActiveJobs,proto3" json:"num_active_jobs,omitempty"`
	ConcurrencyPolicy       CronJob_ConcurrencyPolicy `protobuf:"varint,9,opt,name=concurrency_policy,json=concurrencyPolicy,proto3,enum=clutch.k8s.v1.CronJob_ConcurrencyPolicy" json:"concurrency_policy,omitempty"`
	StartingDeadlineSeconds *wrapperspb.Int64Value    `protobuf:"bytes,10,opt,name=starting_deadline_seconds,json=startingDeadlineSeconds,proto3" json:"starting_deadline_seconds,omitempty"`
}

func (x *CronJob) Reset() {
	*x = CronJob{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CronJob) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CronJob) ProtoMessage() {}

func (x *CronJob) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CronJob.ProtoReflect.Descriptor instead.
func (*CronJob) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{57}
}

func (x *CronJob) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *CronJob) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *CronJob) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CronJob) GetSchedule() string {
	if x != nil {
		return x.Schedule
	}
	return ""
}

func (x *CronJob) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *CronJob) GetAnnotations() map[string]string {
	if x != nil {
		return x.Annotations
	}
	return nil
}

func (x *CronJob) GetSuspend() bool {
	if x != nil {
		return x.Suspend
	}
	return false
}

func (x *CronJob) GetNumActiveJobs() int32 {
	if x != nil {
		return x.NumActiveJobs
	}
	return 0
}

func (x *CronJob) GetConcurrencyPolicy() CronJob_ConcurrencyPolicy {
	if x != nil {
		return x.ConcurrencyPolicy
	}
	return CronJob_UNSPECIFIED
}

func (x *CronJob) GetStartingDeadlineSeconds() *wrapperspb.Int64Value {
	if x != nil {
		return x.StartingDeadlineSeconds
	}
	return nil
}

type DescribeCronJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clientset string `protobuf:"bytes,1,opt,name=clientset,proto3" json:"clientset,omitempty"`
	Cluster   string `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace string `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Name      string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DescribeCronJobRequest) Reset() {
	*x = DescribeCronJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeCronJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeCronJobRequest) ProtoMessage() {}

func (x *DescribeCronJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeCronJobRequest.ProtoReflect.Descriptor instead.
func (*DescribeCronJobRequest) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{58}
}

func (x *DescribeCronJobRequest) GetClientset() string {
	if x != nil {
		return x.Clientset
	}
	return ""
}

func (x *DescribeCronJobRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *DescribeCronJobRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *DescribeCronJobRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DescribeCronJobResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cronjob *CronJob `protobuf:"bytes,1,opt,name=cronjob,proto3" json:"cronjob,omitempty"`
}

func (x *DescribeCronJobResponse) Reset() {
	*x = DescribeCronJobResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeCronJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeCronJobResponse) ProtoMessage() {}

func (x *DescribeCronJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeCronJobResponse.ProtoReflect.Descriptor instead.
func (*DescribeCronJobResponse) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{59}
}

func (x *DescribeCronJobResponse) GetCronjob() *CronJob {
	if x != nil {
		return x.Cronjob
	}
	return nil
}

type ListCronJobsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clientset string       `protobuf:"bytes,1,opt,name=clientset,proto3" json:"clientset,omitempty"`
	Cluster   string       `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace string       `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Options   *ListOptions `protobuf:"bytes,4,opt,name=options,proto3" json:"options,omitempty"`
}

func (x *ListCronJobsRequest) Reset() {
	*x = ListCronJobsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCronJobsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCronJobsRequest) ProtoMessage() {}

func (x *ListCronJobsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCronJobsRequest.ProtoReflect.Descriptor instead.
func (*ListCronJobsRequest) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{60}
}

func (x *ListCronJobsRequest) GetClientset() string {
	if x != nil {
		return x.Clientset
	}
	return ""
}

func (x *ListCronJobsRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *ListCronJobsRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *ListCronJobsRequest) GetOptions() *ListOptions {
	if x != nil {
		return x.Options
	}
	return nil
}

type ListCronJobsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CronJobs []*CronJob `protobuf:"bytes,1,rep,name=cron_jobs,json=cronJobs,proto3" json:"cron_jobs,omitempty"`
}

func (x *ListCronJobsResponse) Reset() {
	*x = ListCronJobsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCronJobsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCronJobsResponse) ProtoMessage() {}

func (x *ListCronJobsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCronJobsResponse.ProtoReflect.Descriptor instead.
func (*ListCronJobsResponse) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{61}
}

func (x *ListCronJobsResponse) GetCronJobs() []*CronJob {
	if x != nil {
		return x.CronJobs
	}
	return nil
}

type DeleteCronJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clientset string `protobuf:"bytes,1,opt,name=clientset,proto3" json:"clientset,omitempty"`
	Cluster   string `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace string `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Name      string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DeleteCronJobRequest) Reset() {
	*x = DeleteCronJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteCronJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCronJobRequest) ProtoMessage() {}

func (x *DeleteCronJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCronJobRequest.ProtoReflect.Descriptor instead.
func (*DeleteCronJobRequest) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{62}
}

func (x *DeleteCronJobRequest) GetClientset() string {
	if x != nil {
		return x.Clientset
	}
	return ""
}

func (x *DeleteCronJobRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *DeleteCronJobRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *DeleteCronJobRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DeleteCronJobResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteCronJobResponse) Reset() {
	*x = DeleteCronJobResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteCronJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCronJobResponse) ProtoMessage() {}

func (x *DeleteCronJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCronJobResponse.ProtoReflect.Descriptor instead.
func (*DeleteCronJobResponse) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{63}
}

type ConfigMap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cluster     string            `protobuf:"bytes,1,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace   string            `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Name        string            `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Annotations map[string]string `protobuf:"bytes,4,rep,name=annotations,proto3" json:"annotations,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Labels      map[string]string `protobuf:"bytes,5,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// represents a configmaps Data field which is a
	// key:value map
	Data map[string]string `protobuf:"bytes,6,rep,name=data,proto3" json:"data,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// represents a configmaps BinaryData field which is
	// a map of binary data.
	BinaryData map[string][]byte `protobuf:"bytes,7,rep,name=binary_data,json=binaryData,proto3" json:"binary_data,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ConfigMap) Reset() {
	*x = ConfigMap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConfigMap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigMap) ProtoMessage() {}

func (x *ConfigMap) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigMap.ProtoReflect.Descriptor instead.
func (*ConfigMap) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{64}
}

func (x *ConfigMap) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *ConfigMap) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *ConfigMap) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ConfigMap) GetAnnotations() map[string]string {
	if x != nil {
		return x.Annotations
	}
	return nil
}

func (x *ConfigMap) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *ConfigMap) GetData() map[string]string {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ConfigMap) GetBinaryData() map[string][]byte {
	if x != nil {
		return x.BinaryData
	}
	return nil
}

type ListConfigMapsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clientset string       `protobuf:"bytes,1,opt,name=clientset,proto3" json:"clientset,omitempty"`
	Cluster   string       `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace string       `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Options   *ListOptions `protobuf:"bytes,4,opt,name=options,proto3" json:"options,omitempty"`
}

func (x *ListConfigMapsRequest) Reset() {
	*x = ListConfigMapsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListConfigMapsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListConfigMapsRequest) ProtoMessage() {}

func (x *ListConfigMapsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListConfigMapsRequest.ProtoReflect.Descriptor instead.
func (*ListConfigMapsRequest) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{65}
}

func (x *ListConfigMapsRequest) GetClientset() string {
	if x != nil {
		return x.Clientset
	}
	return ""
}

func (x *ListConfigMapsRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *ListConfigMapsRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *ListConfigMapsRequest) GetOptions() *ListOptions {
	if x != nil {
		return x.Options
	}
	return nil
}

type ListConfigMapsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConfigMaps []*ConfigMap `protobuf:"bytes,1,rep,name=config_maps,json=configMaps,proto3" json:"config_maps,omitempty"`
}

func (x *ListConfigMapsResponse) Reset() {
	*x = ListConfigMapsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListConfigMapsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListConfigMapsResponse) ProtoMessage() {}

func (x *ListConfigMapsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListConfigMapsResponse.ProtoReflect.Descriptor instead.
func (*ListConfigMapsResponse) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{66}
}

func (x *ListConfigMapsResponse) GetConfigMaps() []*ConfigMap {
	if x != nil {
		return x.ConfigMaps
	}
	return nil
}

type DescribeConfigMapRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clientset string `protobuf:"bytes,1,opt,name=clientset,proto3" json:"clientset,omitempty"`
	Cluster   string `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace string `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Name      string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DescribeConfigMapRequest) Reset() {
	*x = DescribeConfigMapRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeConfigMapRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeConfigMapRequest) ProtoMessage() {}

func (x *DescribeConfigMapRequest) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeConfigMapRequest.ProtoReflect.Descriptor instead.
func (*DescribeConfigMapRequest) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{67}
}

func (x *DescribeConfigMapRequest) GetClientset() string {
	if x != nil {
		return x.Clientset
	}
	return ""
}

func (x *DescribeConfigMapRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *DescribeConfigMapRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *DescribeConfigMapRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DescribeConfigMapResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConfigMap *ConfigMap `protobuf:"bytes,1,opt,name=config_map,json=configMap,proto3" json:"config_map,omitempty"`
}

func (x *DescribeConfigMapResponse) Reset() {
	*x = DescribeConfigMapResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeConfigMapResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeConfigMapResponse) ProtoMessage() {}

func (x *DescribeConfigMapResponse) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeConfigMapResponse.ProtoReflect.Descriptor instead.
func (*DescribeConfigMapResponse) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{68}
}

func (x *DescribeConfigMapResponse) GetConfigMap() *ConfigMap {
	if x != nil {
		return x.ConfigMap
	}
	return nil
}

type DeleteConfigMapRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clientset string `protobuf:"bytes,1,opt,name=clientset,proto3" json:"clientset,omitempty"`
	Cluster   string `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace string `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Name      string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DeleteConfigMapRequest) Reset() {
	*x = DeleteConfigMapRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteConfigMapRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteConfigMapRequest) ProtoMessage() {}

func (x *DeleteConfigMapRequest) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteConfigMapRequest.ProtoReflect.Descriptor instead.
func (*DeleteConfigMapRequest) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{69}
}

func (x *DeleteConfigMapRequest) GetClientset() string {
	if x != nil {
		return x.Clientset
	}
	return ""
}

func (x *DeleteConfigMapRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *DeleteConfigMapRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *DeleteConfigMapRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DeleteConfigMapResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteConfigMapResponse) Reset() {
	*x = DeleteConfigMapResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteConfigMapResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteConfigMapResponse) ProtoMessage() {}

func (x *DeleteConfigMapResponse) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteConfigMapResponse.ProtoReflect.Descriptor instead.
func (*DeleteConfigMapResponse) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{70}
}

type Job struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cluster     string            `protobuf:"bytes,1,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace   string            `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Name        string            `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Annotations map[string]string `protobuf:"bytes,4,rep,name=annotations,proto3" json:"annotations,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Labels      map[string]string `protobuf:"bytes,5,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Job) Reset() {
	*x = Job{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Job) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Job) ProtoMessage() {}

func (x *Job) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Job.ProtoReflect.Descriptor instead.
func (*Job) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{71}
}

func (x *Job) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *Job) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *Job) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Job) GetAnnotations() map[string]string {
	if x != nil {
		return x.Annotations
	}
	return nil
}

func (x *Job) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

type DescribeJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clientset string `protobuf:"bytes,1,opt,name=clientset,proto3" json:"clientset,omitempty"`
	Cluster   string `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace string `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Name      string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DescribeJobRequest) Reset() {
	*x = DescribeJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeJobRequest) ProtoMessage() {}

func (x *DescribeJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeJobRequest.ProtoReflect.Descriptor instead.
func (*DescribeJobRequest) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{72}
}

func (x *DescribeJobRequest) GetClientset() string {
	if x != nil {
		return x.Clientset
	}
	return ""
}

func (x *DescribeJobRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *DescribeJobRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *DescribeJobRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DescribeJobResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Job *Job `protobuf:"bytes,1,opt,name=job,proto3" json:"job,omitempty"`
}

func (x *DescribeJobResponse) Reset() {
	*x = DescribeJobResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeJobResponse) ProtoMessage() {}

func (x *DescribeJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeJobResponse.ProtoReflect.Descriptor instead.
func (*DescribeJobResponse) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{73}
}

func (x *DescribeJobResponse) GetJob() *Job {
	if x != nil {
		return x.Job
	}
	return nil
}

type ListJobsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clientset string       `protobuf:"bytes,1,opt,name=clientset,proto3" json:"clientset,omitempty"`
	Cluster   string       `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace string       `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Options   *ListOptions `protobuf:"bytes,4,opt,name=options,proto3" json:"options,omitempty"`
}

func (x *ListJobsRequest) Reset() {
	*x = ListJobsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListJobsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListJobsRequest) ProtoMessage() {}

func (x *ListJobsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListJobsRequest.ProtoReflect.Descriptor instead.
func (*ListJobsRequest) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{74}
}

func (x *ListJobsRequest) GetClientset() string {
	if x != nil {
		return x.Clientset
	}
	return ""
}

func (x *ListJobsRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *ListJobsRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *ListJobsRequest) GetOptions() *ListOptions {
	if x != nil {
		return x.Options
	}
	return nil
}

type ListJobsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Jobs []*Job `protobuf:"bytes,1,rep,name=jobs,proto3" json:"jobs,omitempty"`
}

func (x *ListJobsResponse) Reset() {
	*x = ListJobsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListJobsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListJobsResponse) ProtoMessage() {}

func (x *ListJobsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListJobsResponse.ProtoReflect.Descriptor instead.
func (*ListJobsResponse) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{75}
}

func (x *ListJobsResponse) GetJobs() []*Job {
	if x != nil {
		return x.Jobs
	}
	return nil
}

type DeleteJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clientset string `protobuf:"bytes,1,opt,name=clientset,proto3" json:"clientset,omitempty"`
	Cluster   string `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace string `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Name      string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DeleteJobRequest) Reset() {
	*x = DeleteJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[76]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteJobRequest) ProtoMessage() {}

func (x *DeleteJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[76]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteJobRequest.ProtoReflect.Descriptor instead.
func (*DeleteJobRequest) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{76}
}

func (x *DeleteJobRequest) GetClientset() string {
	if x != nil {
		return x.Clientset
	}
	return ""
}

func (x *DeleteJobRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *DeleteJobRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *DeleteJobRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DeleteJobResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteJobResponse) Reset() {
	*x = DeleteJobResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[77]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteJobResponse) ProtoMessage() {}

func (x *DeleteJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[77]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteJobResponse.ProtoReflect.Descriptor instead.
func (*DeleteJobResponse) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{77}
}

type JobConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value *structpb.Value `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *JobConfig) Reset() {
	*x = JobConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[78]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobConfig) ProtoMessage() {}

func (x *JobConfig) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[78]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobConfig.ProtoReflect.Descriptor instead.
func (*JobConfig) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{78}
}

func (x *JobConfig) GetValue() *structpb.Value {
	if x != nil {
		return x.Value
	}
	return nil
}

type CreateJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clientset string     `protobuf:"bytes,1,opt,name=clientset,proto3" json:"clientset,omitempty"`
	Cluster   string     `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace string     `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	JobConfig *JobConfig `protobuf:"bytes,4,opt,name=job_config,json=jobConfig,proto3" json:"job_config,omitempty"`
}

func (x *CreateJobRequest) Reset() {
	*x = CreateJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[79]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateJobRequest) ProtoMessage() {}

func (x *CreateJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[79]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateJobRequest.ProtoReflect.Descriptor instead.
func (*CreateJobRequest) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{79}
}

func (x *CreateJobRequest) GetClientset() string {
	if x != nil {
		return x.Clientset
	}
	return ""
}

func (x *CreateJobRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *CreateJobRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *CreateJobRequest) GetJobConfig() *JobConfig {
	if x != nil {
		return x.JobConfig
	}
	return nil
}

type CreateJobResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Job *Job `protobuf:"bytes,1,opt,name=job,proto3" json:"job,omitempty"`
}

func (x *CreateJobResponse) Reset() {
	*x = CreateJobResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[80]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateJobResponse) ProtoMessage() {}

func (x *CreateJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[80]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateJobResponse.ProtoReflect.Descriptor instead.
func (*CreateJobResponse) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{80}
}

func (x *CreateJobResponse) GetJob() *Job {
	if x != nil {
		return x.Job
	}
	return nil
}

type Namespace struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cluster     string            `protobuf:"bytes,1,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Name        string            `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Annotations map[string]string `protobuf:"bytes,3,rep,name=annotations,proto3" json:"annotations,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Labels      map[string]string `protobuf:"bytes,4,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Namespace) Reset() {
	*x = Namespace{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[81]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Namespace) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Namespace) ProtoMessage() {}

func (x *Namespace) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[81]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Namespace.ProtoReflect.Descriptor instead.
func (*Namespace) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{81}
}

func (x *Namespace) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *Namespace) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Namespace) GetAnnotations() map[string]string {
	if x != nil {
		return x.Annotations
	}
	return nil
}

func (x *Namespace) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

type DescribeNamespaceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clientset string `protobuf:"bytes,1,opt,name=clientset,proto3" json:"clientset,omitempty"`
	Cluster   string `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Name      string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DescribeNamespaceRequest) Reset() {
	*x = DescribeNamespaceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[82]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeNamespaceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeNamespaceRequest) ProtoMessage() {}

func (x *DescribeNamespaceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[82]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeNamespaceRequest.ProtoReflect.Descriptor instead.
func (*DescribeNamespaceRequest) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{82}
}

func (x *DescribeNamespaceRequest) GetClientset() string {
	if x != nil {
		return x.Clientset
	}
	return ""
}

func (x *DescribeNamespaceRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *DescribeNamespaceRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DescribeNamespaceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Namespace *Namespace `protobuf:"bytes,1,opt,name=namespace,proto3" json:"namespace,omitempty"`
}

func (x *DescribeNamespaceResponse) Reset() {
	*x = DescribeNamespaceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[83]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeNamespaceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeNamespaceResponse) ProtoMessage() {}

func (x *DescribeNamespaceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[83]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeNamespaceResponse.ProtoReflect.Descriptor instead.
func (*DescribeNamespaceResponse) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{83}
}

func (x *DescribeNamespaceResponse) GetNamespace() *Namespace {
	if x != nil {
		return x.Namespace
	}
	return nil
}

// this represents a Kubernetes event: https://pkg.go.dev/k8s.io/api/core/v1#Event
// for a given object
type Event struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Reason      string `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// these values represent info of the object
	// this event is about
	Cluster   string `protobuf:"bytes,4,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace string `protobuf:"bytes,5,opt,name=namespace,proto3" json:"namespace,omitempty"`
	// name of the object i.e pod name, deployment name, etc
	InvolvedObjectName string `protobuf:"bytes,6,opt,name=involved_object_name,json=involvedObjectName,proto3" json:"involved_object_name,omitempty"`
	// kind of the object e.g pod, deployment, service, etc
	Kind ObjectKind `protobuf:"varint,7,opt,name=kind,proto3,enum=clutch.k8s.v1.ObjectKind" json:"kind,omitempty"`
	// This creation time refers to the metadata creation time, which the server
	// marks when the object is created.
	// https://pkg.go.dev/k8s.io/apimachinery/pkg/apis/meta/v1#ObjectMeta
	CreationTimeMillis int64 `protobuf:"varint,10,opt,name=creation_time_millis,json=creationTimeMillis,proto3" json:"creation_time_millis,omitempty"`
	// type is string so that in the future it is open-ended
	Type string `protobuf:"bytes,11,opt,name=type,proto3" json:"type,omitempty"`
	// last timestamp is the most recent time the event occurred (which can be different from the first time)
	LastTimestampMillis  int64 `protobuf:"varint,12,opt,name=last_timestamp_millis,json=lastTimestampMillis,proto3" json:"last_timestamp_millis,omitempty"`
	FirstTimestampMillis int64 `protobuf:"varint,13,opt,name=first_timestamp_millis,json=firstTimestampMillis,proto3" json:"first_timestamp_millis,omitempty"`
}

func (x *Event) Reset() {
	*x = Event{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[84]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Event) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Event) ProtoMessage() {}

func (x *Event) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[84]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Event.ProtoReflect.Descriptor instead.
func (*Event) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{84}
}

func (x *Event) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Event) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *Event) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Event) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *Event) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *Event) GetInvolvedObjectName() string {
	if x != nil {
		return x.InvolvedObjectName
	}
	return ""
}

func (x *Event) GetKind() ObjectKind {
	if x != nil {
		return x.Kind
	}
	return ObjectKind_UNSPECIFIED
}

func (x *Event) GetCreationTimeMillis() int64 {
	if x != nil {
		return x.CreationTimeMillis
	}
	return 0
}

func (x *Event) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Event) GetLastTimestampMillis() int64 {
	if x != nil {
		return x.LastTimestampMillis
	}
	return 0
}

func (x *Event) GetFirstTimestampMillis() int64 {
	if x != nil {
		return x.FirstTimestampMillis
	}
	return 0
}

type ListEventsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clientset string `protobuf:"bytes,1,opt,name=clientset,proto3" json:"clientset,omitempty"`
	Cluster   string `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Namespace string `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	// name of the object i.e pod name, deployment name, etc
	ObjectName string `protobuf:"bytes,4,opt,name=object_name,json=objectName,proto3" json:"object_name,omitempty"`
	// kind of the object e.g pod, deployment, service, etc
	// https://github.com/kubernetes/community/blob/master/contributors/devel/sig-architecture/api-conventions.md#types-kinds
	Kind ObjectKind `protobuf:"varint,5,opt,name=kind,proto3,enum=clutch.k8s.v1.ObjectKind" json:"kind,omitempty"`
}

func (x *ListEventsRequest) Reset() {
	*x = ListEventsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[85]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListEventsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEventsRequest) ProtoMessage() {}

func (x *ListEventsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[85]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEventsRequest.ProtoReflect.Descriptor instead.
func (*ListEventsRequest) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{85}
}

func (x *ListEventsRequest) GetClientset() string {
	if x != nil {
		return x.Clientset
	}
	return ""
}

func (x *ListEventsRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *ListEventsRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *ListEventsRequest) GetObjectName() string {
	if x != nil {
		return x.ObjectName
	}
	return ""
}

func (x *ListEventsRequest) GetKind() ObjectKind {
	if x != nil {
		return x.Kind
	}
	return ObjectKind_UNSPECIFIED
}

type ListEventsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Events []*Event `protobuf:"bytes,1,rep,name=events,proto3" json:"events,omitempty"`
}

func (x *ListEventsResponse) Reset() {
	*x = ListEventsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[86]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListEventsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEventsResponse) ProtoMessage() {}

func (x *ListEventsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[86]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEventsResponse.ProtoReflect.Descriptor instead.
func (*ListEventsResponse) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{86}
}

func (x *ListEventsResponse) GetEvents() []*Event {
	if x != nil {
		return x.Events
	}
	return nil
}

// This message type is used to add support for nullable strings and is an
// alternative to the well-known `StringValue` type. We need it, because the
// grpc-gateway used by Clutch deserializes a null `StringValue` as an empty
// string.
type NullableString struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Kind:
	//
	//	*NullableString_Null
	//	*NullableString_Value
	Kind isNullableString_Kind `protobuf_oneof:"kind"`
}

func (x *NullableString) Reset() {
	*x = NullableString{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[87]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NullableString) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NullableString) ProtoMessage() {}

func (x *NullableString) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[87]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NullableString.ProtoReflect.Descriptor instead.
func (*NullableString) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{87}
}

func (m *NullableString) GetKind() isNullableString_Kind {
	if m != nil {
		return m.Kind
	}
	return nil
}

func (x *NullableString) GetNull() structpb.NullValue {
	if x, ok := x.GetKind().(*NullableString_Null); ok {
		return x.Null
	}
	return structpb.NullValue(0)
}

func (x *NullableString) GetValue() string {
	if x, ok := x.GetKind().(*NullableString_Value); ok {
		return x.Value
	}
	return ""
}

type isNullableString_Kind interface {
	isNullableString_Kind()
}

type NullableString_Null struct {
	Null structpb.NullValue `protobuf:"varint,1,opt,name=null,proto3,enum=google.protobuf.NullValue,oneof"`
}

type NullableString_Value struct {
	Value string `protobuf:"bytes,2,opt,name=value,proto3,oneof"`
}

func (*NullableString_Null) isNullableString_Kind() {}

func (*NullableString_Value) isNullableString_Kind() {}

// Preconditions to check before updating an object's metadata.
//
// Note: A `null` NullableString means that the label/annotation should not be set.
type ExpectedObjectMetaFields struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Labels      map[string]*NullableString `protobuf:"bytes,1,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Annotations map[string]*NullableString `protobuf:"bytes,2,rep,name=annotations,proto3" json:"annotations,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ExpectedObjectMetaFields) Reset() {
	*x = ExpectedObjectMetaFields{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[88]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpectedObjectMetaFields) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpectedObjectMetaFields) ProtoMessage() {}

func (x *ExpectedObjectMetaFields) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[88]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpectedObjectMetaFields.ProtoReflect.Descriptor instead.
func (*ExpectedObjectMetaFields) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{88}
}

func (x *ExpectedObjectMetaFields) GetLabels() map[string]*NullableString {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *ExpectedObjectMetaFields) GetAnnotations() map[string]*NullableString {
	if x != nil {
		return x.Annotations
	}
	return nil
}

// Metadata fields to update when updating an object
type ObjectMetaFields struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Labels      map[string]string `protobuf:"bytes,1,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Annotations map[string]string `protobuf:"bytes,2,rep,name=annotations,proto3" json:"annotations,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ObjectMetaFields) Reset() {
	*x = ObjectMetaFields{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[89]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ObjectMetaFields) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ObjectMetaFields) ProtoMessage() {}

func (x *ObjectMetaFields) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[89]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ObjectMetaFields.ProtoReflect.Descriptor instead.
func (*ObjectMetaFields) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{89}
}

func (x *ObjectMetaFields) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *ObjectMetaFields) GetAnnotations() map[string]string {
	if x != nil {
		return x.Annotations
	}
	return nil
}

// Metadata fields to remove when updating an object
type RemoveObjectMetaFields struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Labels      []string `protobuf:"bytes,1,rep,name=labels,proto3" json:"labels,omitempty"`
	Annotations []string `protobuf:"bytes,2,rep,name=annotations,proto3" json:"annotations,omitempty"`
}

func (x *RemoveObjectMetaFields) Reset() {
	*x = RemoveObjectMetaFields{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[90]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveObjectMetaFields) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveObjectMetaFields) ProtoMessage() {}

func (x *RemoveObjectMetaFields) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[90]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveObjectMetaFields.ProtoReflect.Descriptor instead.
func (*RemoveObjectMetaFields) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{90}
}

func (x *RemoveObjectMetaFields) GetLabels() []string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *RemoveObjectMetaFields) GetAnnotations() []string {
	if x != nil {
		return x.Annotations
	}
	return nil
}

type Node struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Cluster       string `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Unschedulable bool   `protobuf:"varint,3,opt,name=unschedulable,proto3" json:"unschedulable,omitempty"`
}

func (x *Node) Reset() {
	*x = Node{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[91]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Node) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Node) ProtoMessage() {}

func (x *Node) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[91]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Node.ProtoReflect.Descriptor instead.
func (*Node) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{91}
}

func (x *Node) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Node) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *Node) GetUnschedulable() bool {
	if x != nil {
		return x.Unschedulable
	}
	return false
}

type DescribeNodeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clientset string `protobuf:"bytes,1,opt,name=clientset,proto3" json:"clientset,omitempty"`
	Cluster   string `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Name      string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DescribeNodeRequest) Reset() {
	*x = DescribeNodeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[92]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeNodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeNodeRequest) ProtoMessage() {}

func (x *DescribeNodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[92]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeNodeRequest.ProtoReflect.Descriptor instead.
func (*DescribeNodeRequest) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{92}
}

func (x *DescribeNodeRequest) GetClientset() string {
	if x != nil {
		return x.Clientset
	}
	return ""
}

func (x *DescribeNodeRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *DescribeNodeRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DescribeNodeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Node *Node `protobuf:"bytes,1,opt,name=node,proto3" json:"node,omitempty"`
}

func (x *DescribeNodeResponse) Reset() {
	*x = DescribeNodeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[93]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeNodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeNodeResponse) ProtoMessage() {}

func (x *DescribeNodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[93]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeNodeResponse.ProtoReflect.Descriptor instead.
func (*DescribeNodeResponse) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{93}
}

func (x *DescribeNodeResponse) GetNode() *Node {
	if x != nil {
		return x.Node
	}
	return nil
}

type UpdateNodeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clientset     string `protobuf:"bytes,1,opt,name=clientset,proto3" json:"clientset,omitempty"`
	Cluster       string `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	Name          string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Unschedulable bool   `protobuf:"varint,4,opt,name=unschedulable,proto3" json:"unschedulable,omitempty"`
}

func (x *UpdateNodeRequest) Reset() {
	*x = UpdateNodeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[94]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateNodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateNodeRequest) ProtoMessage() {}

func (x *UpdateNodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[94]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateNodeRequest.ProtoReflect.Descriptor instead.
func (*UpdateNodeRequest) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{94}
}

func (x *UpdateNodeRequest) GetClientset() string {
	if x != nil {
		return x.Clientset
	}
	return ""
}

func (x *UpdateNodeRequest) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *UpdateNodeRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateNodeRequest) GetUnschedulable() bool {
	if x != nil {
		return x.Unschedulable
	}
	return false
}

type UpdateNodeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateNodeResponse) Reset() {
	*x = UpdateNodeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[95]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateNodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateNodeResponse) ProtoMessage() {}

func (x *UpdateNodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[95]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateNodeResponse.ProtoReflect.Descriptor instead.
func (*UpdateNodeResponse) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{95}
}

type HPA_Sizing struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MinReplicas     uint32 `protobuf:"varint,1,opt,name=min_replicas,json=minReplicas,proto3" json:"min_replicas,omitempty"`
	MaxReplicas     uint32 `protobuf:"varint,2,opt,name=max_replicas,json=maxReplicas,proto3" json:"max_replicas,omitempty"`
	CurrentReplicas uint32 `protobuf:"varint,3,opt,name=current_replicas,json=currentReplicas,proto3" json:"current_replicas,omitempty"`
	DesiredReplicas uint32 `protobuf:"varint,4,opt,name=desired_replicas,json=desiredReplicas,proto3" json:"desired_replicas,omitempty"`
}

func (x *HPA_Sizing) Reset() {
	*x = HPA_Sizing{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[99]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HPA_Sizing) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HPA_Sizing) ProtoMessage() {}

func (x *HPA_Sizing) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[99]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HPA_Sizing.ProtoReflect.Descriptor instead.
func (*HPA_Sizing) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{21, 0}
}

func (x *HPA_Sizing) GetMinReplicas() uint32 {
	if x != nil {
		return x.MinReplicas
	}
	return 0
}

func (x *HPA_Sizing) GetMaxReplicas() uint32 {
	if x != nil {
		return x.MaxReplicas
	}
	return 0
}

func (x *HPA_Sizing) GetCurrentReplicas() uint32 {
	if x != nil {
		return x.CurrentReplicas
	}
	return 0
}

func (x *HPA_Sizing) GetDesiredReplicas() uint32 {
	if x != nil {
		return x.DesiredReplicas
	}
	return 0
}

type ResizeHPARequest_Sizing struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Min uint32 `protobuf:"varint,1,opt,name=min,proto3" json:"min,omitempty"`
	Max uint32 `protobuf:"varint,2,opt,name=max,proto3" json:"max,omitempty"`
}

func (x *ResizeHPARequest_Sizing) Reset() {
	*x = ResizeHPARequest_Sizing{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[102]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResizeHPARequest_Sizing) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResizeHPARequest_Sizing) ProtoMessage() {}

func (x *ResizeHPARequest_Sizing) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[102]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResizeHPARequest_Sizing.ProtoReflect.Descriptor instead.
func (*ResizeHPARequest_Sizing) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{22, 0}
}

func (x *ResizeHPARequest_Sizing) GetMin() uint32 {
	if x != nil {
		return x.Min
	}
	return 0
}

func (x *ResizeHPARequest_Sizing) GetMax() uint32 {
	if x != nil {
		return x.Max
	}
	return 0
}

type Deployment_DeploymentStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Replicas             uint32                                   `protobuf:"varint,1,opt,name=replicas,proto3" json:"replicas,omitempty"`
	UpdatedReplicas      uint32                                   `protobuf:"varint,2,opt,name=updated_replicas,json=updatedReplicas,proto3" json:"updated_replicas,omitempty"`
	ReadyReplicas        uint32                                   `protobuf:"varint,3,opt,name=ready_replicas,json=readyReplicas,proto3" json:"ready_replicas,omitempty"`
	AvailableReplicas    uint32                                   `protobuf:"varint,4,opt,name=available_replicas,json=availableReplicas,proto3" json:"available_replicas,omitempty"`
	UnavailableReplicas  uint32                                   `protobuf:"varint,5,opt,name=unavailable_replicas,json=unavailableReplicas,proto3" json:"unavailable_replicas,omitempty"`
	DeploymentConditions []*Deployment_DeploymentStatus_Condition `protobuf:"bytes,6,rep,name=deployment_conditions,json=deploymentConditions,proto3" json:"deployment_conditions,omitempty"`
}

func (x *Deployment_DeploymentStatus) Reset() {
	*x = Deployment_DeploymentStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[105]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Deployment_DeploymentStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Deployment_DeploymentStatus) ProtoMessage() {}

func (x *Deployment_DeploymentStatus) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[105]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Deployment_DeploymentStatus.ProtoReflect.Descriptor instead.
func (*Deployment_DeploymentStatus) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{32, 2}
}

func (x *Deployment_DeploymentStatus) GetReplicas() uint32 {
	if x != nil {
		return x.Replicas
	}
	return 0
}

func (x *Deployment_DeploymentStatus) GetUpdatedReplicas() uint32 {
	if x != nil {
		return x.UpdatedReplicas
	}
	return 0
}

func (x *Deployment_DeploymentStatus) GetReadyReplicas() uint32 {
	if x != nil {
		return x.ReadyReplicas
	}
	return 0
}

func (x *Deployment_DeploymentStatus) GetAvailableReplicas() uint32 {
	if x != nil {
		return x.AvailableReplicas
	}
	return 0
}

func (x *Deployment_DeploymentStatus) GetUnavailableReplicas() uint32 {
	if x != nil {
		return x.UnavailableReplicas
	}
	return 0
}

func (x *Deployment_DeploymentStatus) GetDeploymentConditions() []*Deployment_DeploymentStatus_Condition {
	if x != nil {
		return x.DeploymentConditions
	}
	return nil
}

type Deployment_DeploymentSpec struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Template *Deployment_DeploymentSpec_PodTemplateSpec `protobuf:"bytes,1,opt,name=template,proto3" json:"template,omitempty"`
}

func (x *Deployment_DeploymentSpec) Reset() {
	*x = Deployment_DeploymentSpec{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[106]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Deployment_DeploymentSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Deployment_DeploymentSpec) ProtoMessage() {}

func (x *Deployment_DeploymentSpec) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[106]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Deployment_DeploymentSpec.ProtoReflect.Descriptor instead.
func (*Deployment_DeploymentSpec) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{32, 3}
}

func (x *Deployment_DeploymentSpec) GetTemplate() *Deployment_DeploymentSpec_PodTemplateSpec {
	if x != nil {
		return x.Template
	}
	return nil
}

type Deployment_DeploymentStatus_Condition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type            Deployment_DeploymentStatus_Condition_Type            `protobuf:"varint,1,opt,name=type,proto3,enum=clutch.k8s.v1.Deployment_DeploymentStatus_Condition_Type" json:"type,omitempty"`
	ConditionStatus Deployment_DeploymentStatus_Condition_ConditionStatus `protobuf:"varint,2,opt,name=condition_status,json=conditionStatus,proto3,enum=clutch.k8s.v1.Deployment_DeploymentStatus_Condition_ConditionStatus" json:"condition_status,omitempty"`
	Reason          string                                                `protobuf:"bytes,5,opt,name=reason,proto3" json:"reason,omitempty"`
	Message         string                                                `protobuf:"bytes,6,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *Deployment_DeploymentStatus_Condition) Reset() {
	*x = Deployment_DeploymentStatus_Condition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[107]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Deployment_DeploymentStatus_Condition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Deployment_DeploymentStatus_Condition) ProtoMessage() {}

func (x *Deployment_DeploymentStatus_Condition) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[107]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Deployment_DeploymentStatus_Condition.ProtoReflect.Descriptor instead.
func (*Deployment_DeploymentStatus_Condition) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{32, 2, 0}
}

func (x *Deployment_DeploymentStatus_Condition) GetType() Deployment_DeploymentStatus_Condition_Type {
	if x != nil {
		return x.Type
	}
	return Deployment_DeploymentStatus_Condition_UNSPECIFIED
}

func (x *Deployment_DeploymentStatus_Condition) GetConditionStatus() Deployment_DeploymentStatus_Condition_ConditionStatus {
	if x != nil {
		return x.ConditionStatus
	}
	return Deployment_DeploymentStatus_Condition_CONDITION_UNSPECIFIED
}

func (x *Deployment_DeploymentStatus_Condition) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *Deployment_DeploymentStatus_Condition) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type Deployment_DeploymentSpec_PodTemplateSpec struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Spec *Deployment_DeploymentSpec_PodTemplateSpec_PodSpec `protobuf:"bytes,1,opt,name=spec,proto3" json:"spec,omitempty"`
}

func (x *Deployment_DeploymentSpec_PodTemplateSpec) Reset() {
	*x = Deployment_DeploymentSpec_PodTemplateSpec{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[108]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Deployment_DeploymentSpec_PodTemplateSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Deployment_DeploymentSpec_PodTemplateSpec) ProtoMessage() {}

func (x *Deployment_DeploymentSpec_PodTemplateSpec) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[108]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Deployment_DeploymentSpec_PodTemplateSpec.ProtoReflect.Descriptor instead.
func (*Deployment_DeploymentSpec_PodTemplateSpec) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{32, 3, 0}
}

func (x *Deployment_DeploymentSpec_PodTemplateSpec) GetSpec() *Deployment_DeploymentSpec_PodTemplateSpec_PodSpec {
	if x != nil {
		return x.Spec
	}
	return nil
}

type Deployment_DeploymentSpec_PodTemplateSpec_PodSpec struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Containers []*Deployment_DeploymentSpec_PodTemplateSpec_PodSpec_Container `protobuf:"bytes,1,rep,name=containers,proto3" json:"containers,omitempty"`
}

func (x *Deployment_DeploymentSpec_PodTemplateSpec_PodSpec) Reset() {
	*x = Deployment_DeploymentSpec_PodTemplateSpec_PodSpec{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[109]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Deployment_DeploymentSpec_PodTemplateSpec_PodSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Deployment_DeploymentSpec_PodTemplateSpec_PodSpec) ProtoMessage() {}

func (x *Deployment_DeploymentSpec_PodTemplateSpec_PodSpec) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[109]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Deployment_DeploymentSpec_PodTemplateSpec_PodSpec.ProtoReflect.Descriptor instead.
func (*Deployment_DeploymentSpec_PodTemplateSpec_PodSpec) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{32, 3, 0, 0}
}

func (x *Deployment_DeploymentSpec_PodTemplateSpec_PodSpec) GetContainers() []*Deployment_DeploymentSpec_PodTemplateSpec_PodSpec_Container {
	if x != nil {
		return x.Containers
	}
	return nil
}

type Deployment_DeploymentSpec_PodTemplateSpec_PodSpec_Container struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name           string                                                                            `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Resources      *Deployment_DeploymentSpec_PodTemplateSpec_PodSpec_Container_ResourceRequirements `protobuf:"bytes,2,opt,name=resources,proto3" json:"resources,omitempty"`
	LivenessProbe  *Probe                                                                            `protobuf:"bytes,3,opt,name=liveness_probe,json=livenessProbe,proto3,oneof" json:"liveness_probe,omitempty"`
	ReadinessProbe *Probe                                                                            `protobuf:"bytes,4,opt,name=readiness_probe,json=readinessProbe,proto3,oneof" json:"readiness_probe,omitempty"`
}

func (x *Deployment_DeploymentSpec_PodTemplateSpec_PodSpec_Container) Reset() {
	*x = Deployment_DeploymentSpec_PodTemplateSpec_PodSpec_Container{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[110]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Deployment_DeploymentSpec_PodTemplateSpec_PodSpec_Container) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Deployment_DeploymentSpec_PodTemplateSpec_PodSpec_Container) ProtoMessage() {}

func (x *Deployment_DeploymentSpec_PodTemplateSpec_PodSpec_Container) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[110]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Deployment_DeploymentSpec_PodTemplateSpec_PodSpec_Container.ProtoReflect.Descriptor instead.
func (*Deployment_DeploymentSpec_PodTemplateSpec_PodSpec_Container) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{32, 3, 0, 0, 0}
}

func (x *Deployment_DeploymentSpec_PodTemplateSpec_PodSpec_Container) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Deployment_DeploymentSpec_PodTemplateSpec_PodSpec_Container) GetResources() *Deployment_DeploymentSpec_PodTemplateSpec_PodSpec_Container_ResourceRequirements {
	if x != nil {
		return x.Resources
	}
	return nil
}

func (x *Deployment_DeploymentSpec_PodTemplateSpec_PodSpec_Container) GetLivenessProbe() *Probe {
	if x != nil {
		return x.LivenessProbe
	}
	return nil
}

func (x *Deployment_DeploymentSpec_PodTemplateSpec_PodSpec_Container) GetReadinessProbe() *Probe {
	if x != nil {
		return x.ReadinessProbe
	}
	return nil
}

type Deployment_DeploymentSpec_PodTemplateSpec_PodSpec_Container_ResourceRequirements struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Limits   map[string]string `protobuf:"bytes,1,rep,name=limits,proto3" json:"limits,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Requests map[string]string `protobuf:"bytes,2,rep,name=requests,proto3" json:"requests,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Deployment_DeploymentSpec_PodTemplateSpec_PodSpec_Container_ResourceRequirements) Reset() {
	*x = Deployment_DeploymentSpec_PodTemplateSpec_PodSpec_Container_ResourceRequirements{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[111]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Deployment_DeploymentSpec_PodTemplateSpec_PodSpec_Container_ResourceRequirements) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Deployment_DeploymentSpec_PodTemplateSpec_PodSpec_Container_ResourceRequirements) ProtoMessage() {
}

func (x *Deployment_DeploymentSpec_PodTemplateSpec_PodSpec_Container_ResourceRequirements) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[111]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Deployment_DeploymentSpec_PodTemplateSpec_PodSpec_Container_ResourceRequirements.ProtoReflect.Descriptor instead.
func (*Deployment_DeploymentSpec_PodTemplateSpec_PodSpec_Container_ResourceRequirements) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{32, 3, 0, 0, 0, 0}
}

func (x *Deployment_DeploymentSpec_PodTemplateSpec_PodSpec_Container_ResourceRequirements) GetLimits() map[string]string {
	if x != nil {
		return x.Limits
	}
	return nil
}

func (x *Deployment_DeploymentSpec_PodTemplateSpec_PodSpec_Container_ResourceRequirements) GetRequests() map[string]string {
	if x != nil {
		return x.Requests
	}
	return nil
}

// Fields are merged with the existing deployment object, existing
// labels and annotations are not deleted in the update process.
// Currently this api does not support removing Fields from the deployment object.
// A two way strategic merge is done on the old and new deployment objects.
// https://kubernetes.io/docs/tasks/manage-kubernetes-objects/update-api-object-kubectl-patch/#use-a-json-merge-patch-to-update-a-deployment
type UpdateDeploymentRequest_Fields struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Labels             map[string]string                                    `protobuf:"bytes,1,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Annotations        map[string]string                                    `protobuf:"bytes,2,rep,name=annotations,proto3" json:"annotations,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	ContainerResources []*UpdateDeploymentRequest_Fields_ContainerResources `protobuf:"bytes,3,rep,name=container_resources,json=containerResources,proto3" json:"container_resources,omitempty"`
	ContainerProbes    []*UpdateDeploymentRequest_Fields_ContainerProbes    `protobuf:"bytes,4,rep,name=container_probes,json=containerProbes,proto3" json:"container_probes,omitempty"`
}

func (x *UpdateDeploymentRequest_Fields) Reset() {
	*x = UpdateDeploymentRequest_Fields{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[114]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDeploymentRequest_Fields) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDeploymentRequest_Fields) ProtoMessage() {}

func (x *UpdateDeploymentRequest_Fields) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[114]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDeploymentRequest_Fields.ProtoReflect.Descriptor instead.
func (*UpdateDeploymentRequest_Fields) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{37, 0}
}

func (x *UpdateDeploymentRequest_Fields) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *UpdateDeploymentRequest_Fields) GetAnnotations() map[string]string {
	if x != nil {
		return x.Annotations
	}
	return nil
}

func (x *UpdateDeploymentRequest_Fields) GetContainerResources() []*UpdateDeploymentRequest_Fields_ContainerResources {
	if x != nil {
		return x.ContainerResources
	}
	return nil
}

func (x *UpdateDeploymentRequest_Fields) GetContainerProbes() []*UpdateDeploymentRequest_Fields_ContainerProbes {
	if x != nil {
		return x.ContainerProbes
	}
	return nil
}

type UpdateDeploymentRequest_Fields_ContainerResources struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ContainerName string                                                                  `protobuf:"bytes,1,opt,name=container_name,json=containerName,proto3" json:"container_name,omitempty"`
	Resources     *UpdateDeploymentRequest_Fields_ContainerResources_ResourceRequirements `protobuf:"bytes,2,opt,name=resources,proto3" json:"resources,omitempty"`
}

func (x *UpdateDeploymentRequest_Fields_ContainerResources) Reset() {
	*x = UpdateDeploymentRequest_Fields_ContainerResources{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[117]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDeploymentRequest_Fields_ContainerResources) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDeploymentRequest_Fields_ContainerResources) ProtoMessage() {}

func (x *UpdateDeploymentRequest_Fields_ContainerResources) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[117]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDeploymentRequest_Fields_ContainerResources.ProtoReflect.Descriptor instead.
func (*UpdateDeploymentRequest_Fields_ContainerResources) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{37, 0, 2}
}

func (x *UpdateDeploymentRequest_Fields_ContainerResources) GetContainerName() string {
	if x != nil {
		return x.ContainerName
	}
	return ""
}

func (x *UpdateDeploymentRequest_Fields_ContainerResources) GetResources() *UpdateDeploymentRequest_Fields_ContainerResources_ResourceRequirements {
	if x != nil {
		return x.Resources
	}
	return nil
}

type UpdateDeploymentRequest_Fields_ContainerProbes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ContainerName  string `protobuf:"bytes,1,opt,name=container_name,json=containerName,proto3" json:"container_name,omitempty"`
	LivenessProbe  *Probe `protobuf:"bytes,2,opt,name=liveness_probe,json=livenessProbe,proto3,oneof" json:"liveness_probe,omitempty"`
	ReadinessProbe *Probe `protobuf:"bytes,3,opt,name=readiness_probe,json=readinessProbe,proto3,oneof" json:"readiness_probe,omitempty"`
}

func (x *UpdateDeploymentRequest_Fields_ContainerProbes) Reset() {
	*x = UpdateDeploymentRequest_Fields_ContainerProbes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[118]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDeploymentRequest_Fields_ContainerProbes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDeploymentRequest_Fields_ContainerProbes) ProtoMessage() {}

func (x *UpdateDeploymentRequest_Fields_ContainerProbes) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[118]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDeploymentRequest_Fields_ContainerProbes.ProtoReflect.Descriptor instead.
func (*UpdateDeploymentRequest_Fields_ContainerProbes) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{37, 0, 3}
}

func (x *UpdateDeploymentRequest_Fields_ContainerProbes) GetContainerName() string {
	if x != nil {
		return x.ContainerName
	}
	return ""
}

func (x *UpdateDeploymentRequest_Fields_ContainerProbes) GetLivenessProbe() *Probe {
	if x != nil {
		return x.LivenessProbe
	}
	return nil
}

func (x *UpdateDeploymentRequest_Fields_ContainerProbes) GetReadinessProbe() *Probe {
	if x != nil {
		return x.ReadinessProbe
	}
	return nil
}

type UpdateDeploymentRequest_Fields_ContainerResources_ResourceRequirements struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Limits   map[string]string `protobuf:"bytes,1,rep,name=limits,proto3" json:"limits,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Requests map[string]string `protobuf:"bytes,2,rep,name=requests,proto3" json:"requests,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *UpdateDeploymentRequest_Fields_ContainerResources_ResourceRequirements) Reset() {
	*x = UpdateDeploymentRequest_Fields_ContainerResources_ResourceRequirements{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[119]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDeploymentRequest_Fields_ContainerResources_ResourceRequirements) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDeploymentRequest_Fields_ContainerResources_ResourceRequirements) ProtoMessage() {}

func (x *UpdateDeploymentRequest_Fields_ContainerResources_ResourceRequirements) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[119]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDeploymentRequest_Fields_ContainerResources_ResourceRequirements.ProtoReflect.Descriptor instead.
func (*UpdateDeploymentRequest_Fields_ContainerResources_ResourceRequirements) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{37, 0, 2, 0}
}

func (x *UpdateDeploymentRequest_Fields_ContainerResources_ResourceRequirements) GetLimits() map[string]string {
	if x != nil {
		return x.Limits
	}
	return nil
}

func (x *UpdateDeploymentRequest_Fields_ContainerResources_ResourceRequirements) GetRequests() map[string]string {
	if x != nil {
		return x.Requests
	}
	return nil
}

type StatefulSet_Status struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Replicas        uint32 `protobuf:"varint,1,opt,name=replicas,proto3" json:"replicas,omitempty"`
	UpdatedReplicas uint32 `protobuf:"varint,2,opt,name=updated_replicas,json=updatedReplicas,proto3" json:"updated_replicas,omitempty"`
	ReadyReplicas   uint32 `protobuf:"varint,3,opt,name=ready_replicas,json=readyReplicas,proto3" json:"ready_replicas,omitempty"`
}

func (x *StatefulSet_Status) Reset() {
	*x = StatefulSet_Status{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[124]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatefulSet_Status) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatefulSet_Status) ProtoMessage() {}

func (x *StatefulSet_Status) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[124]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatefulSet_Status.ProtoReflect.Descriptor instead.
func (*StatefulSet_Status) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{41, 2}
}

func (x *StatefulSet_Status) GetReplicas() uint32 {
	if x != nil {
		return x.Replicas
	}
	return 0
}

func (x *StatefulSet_Status) GetUpdatedReplicas() uint32 {
	if x != nil {
		return x.UpdatedReplicas
	}
	return 0
}

func (x *StatefulSet_Status) GetReadyReplicas() uint32 {
	if x != nil {
		return x.ReadyReplicas
	}
	return 0
}

// Fields are merged with the existing statefulset object, existing
// labels and annotations are not deleted in the update process.
// Currently this api does not support removing Fields from the statefulset object.
// A two way strategic merge is done on the old and new statefulset objects.
// https://kubernetes.io/docs/tasks/manage-kubernetes-objects/update-api-object-kubectl-patch/#use-a-json-merge-patch-to-update-a-deployment
type UpdateStatefulSetRequest_Fields struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Labels      map[string]string `protobuf:"bytes,1,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Annotations map[string]string `protobuf:"bytes,2,rep,name=annotations,proto3" json:"annotations,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *UpdateStatefulSetRequest_Fields) Reset() {
	*x = UpdateStatefulSetRequest_Fields{}
	if protoimpl.UnsafeEnabled {
		mi := &file_k8s_v1_k8s_proto_msgTypes[125]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateStatefulSetRequest_Fields) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateStatefulSetRequest_Fields) ProtoMessage() {}

func (x *UpdateStatefulSetRequest_Fields) ProtoReflect() protoreflect.Message {
	mi := &file_k8s_v1_k8s_proto_msgTypes[125]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateStatefulSetRequest_Fields.ProtoReflect.Descriptor instead.
func (*UpdateStatefulSetRequest_Fields) Descriptor() ([]byte, []int) {
	return file_k8s_v1_k8s_proto_rawDescGZIP(), []int{48, 0}
}

func (x *UpdateStatefulSetRequest_Fields) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *UpdateStatefulSetRequest_Fields) GetAnnotations() map[string]string {
	if x != nil {
		return x.Annotations
	}
	return nil
}

var File_k8s_v1_k8s_proto protoreflect.FileDescriptor

var file_k8s_v1_k8s_proto_rawDesc = []byte{
	0x0a, 0x10, 0x6b, 0x38, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6b, 0x38, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0d, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76,
	0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf5, 0x01, 0x0a, 0x1a, 0x4c,
	0x69, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x09, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74,
	0x12, 0x21, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52,
	0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x63, 0x6c, 0x75, 0x74,
	0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x3a, 0x36, 0xb2, 0xe1, 0x1c, 0x32,
	0x0a, 0x30, 0x0a, 0x17, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x15, 0x7b, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x7d, 0x22, 0x51, 0x0a, 0x1b, 0x4c, 0x69, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x32, 0x0a, 0x06, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x42, 0x04, 0xa8, 0xe1, 0x1c, 0x00, 0x52, 0x06, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x73, 0x22, 0xdb, 0x01, 0x0a, 0x12, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x50, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x09,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x73, 0x65, 0x74, 0x12, 0x21, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x07, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x20, 0x01, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x1b, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x20, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x3a, 0x37, 0xb2, 0xe1, 0x1c, 0x33,
	0x0a, 0x31, 0x0a, 0x11, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x6f, 0x64, 0x12, 0x1c, 0x7b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x7d,
	0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x7d, 0x2f, 0x7b, 0x6e, 0x61,
	0x6d, 0x65, 0x7d, 0x22, 0x46, 0x0a, 0x13, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x50,
	0x6f, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x24, 0x0a, 0x03, 0x70, 0x6f,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68,
	0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6f, 0x64, 0x52, 0x03, 0x70, 0x6f, 0x64,
	0x3a, 0x09, 0xaa, 0xe1, 0x1c, 0x05, 0x0a, 0x03, 0x70, 0x6f, 0x64, 0x22, 0xdd, 0x03, 0x0a, 0x09,
	0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x12, 0x34, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x65, 0x61,
	0x64, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x72, 0x65, 0x61, 0x64, 0x79, 0x12,
	0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x74, 0x61, 0x72, 0x74, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x42, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x77, 0x61,
	0x69, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x6c,
	0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x57, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x48, 0x00, 0x52, 0x0c, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x57, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x42, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x5f, 0x72, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x48, 0x00, 0x52, 0x0c,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x52, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x4b, 0x0a, 0x10,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x64,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e,
	0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x54, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x74, 0x65, 0x64, 0x48, 0x00, 0x52, 0x0f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x54,
	0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x64, 0x22, 0x4f, 0x0a, 0x05, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x01,
	0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x54, 0x45, 0x44, 0x10, 0x02,
	0x12, 0x0b, 0x0a, 0x07, 0x52, 0x55, 0x4e, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x0b, 0x0a,
	0x07, 0x57, 0x41, 0x49, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x04, 0x42, 0x0f, 0x0a, 0x0d, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x40, 0x0a, 0x0c, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x57, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x49, 0x0a,
	0x0c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x39, 0x0a,
	0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x78, 0x0a, 0x0f, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x65, 0x78, 0x69, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x65, 0x78, 0x69, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x69,
	0x67, 0x6e, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x69, 0x67, 0x6e,
	0x61, 0x6c, 0x22, 0xa7, 0x02, 0x0a, 0x0c, 0x50, 0x6f, 0x64, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x20, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x6f, 0x64, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x3a, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x63, 0x6c, 0x75, 0x74,
	0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6f, 0x64, 0x43, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x61, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a,
	0x10, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x49, 0x4e, 0x45, 0x52,
	0x53, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x59, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x49, 0x4e, 0x49,
	0x54, 0x49, 0x41, 0x4c, 0x49, 0x5a, 0x45, 0x44, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x52, 0x45,
	0x41, 0x44, 0x59, 0x10, 0x03, 0x12, 0x11, 0x0a, 0x0d, 0x50, 0x4f, 0x44, 0x5f, 0x53, 0x43, 0x48,
	0x45, 0x44, 0x55, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x22, 0x42, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x54, 0x52,
	0x55, 0x45, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x46, 0x41, 0x4c, 0x53, 0x45, 0x10, 0x02, 0x12,
	0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x03, 0x22, 0xa3, 0x07, 0x0a,
	0x03, 0x50, 0x6f, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x1c,
	0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x38, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x52, 0x0a,
	0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x6e, 0x6f,
	0x64, 0x65, 0x5f, 0x69, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6e, 0x6f, 0x64,
	0x65, 0x49, 0x70, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x6f, 0x64, 0x5f, 0x69, 0x70, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x6f, 0x64, 0x49, 0x70, 0x12, 0x2e, 0x0a, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x63, 0x6c, 0x75, 0x74,
	0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6f, 0x64, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x36, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18,
	0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b,
	0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6f, 0x64, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x45, 0x0a,
	0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0a, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x6f, 0x64, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x72, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x42, 0x0a, 0x0e, 0x70, 0x6f, 0x64, 0x5f, 0x63,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x50, 0x6f, 0x64, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x70, 0x6f,
	0x64, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x41, 0x0a, 0x0f, 0x69,
	0x6e, 0x69, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x73, 0x18, 0x0d,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x52, 0x0e,
	0x69, 0x6e, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x73, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x69, 0x6c, 0x6c, 0x69, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x69, 0x6c, 0x6c,
	0x69, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3e, 0x0a,
	0x10, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x5a, 0x0a,
	0x05, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f,
	0x57, 0x4e, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10,
	0x02, 0x12, 0x0b, 0x0a, 0x07, 0x52, 0x55, 0x4e, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x0d,
	0x0a, 0x09, 0x53, 0x55, 0x43, 0x43, 0x45, 0x45, 0x44, 0x45, 0x44, 0x10, 0x04, 0x12, 0x0a, 0x0a,
	0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x05, 0x3a, 0x37, 0xb2, 0xe1, 0x1c, 0x33, 0x0a,
	0x31, 0x0a, 0x11, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x6f, 0x64, 0x12, 0x1c, 0x7b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x7d, 0x2f,
	0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d,
	0x65, 0x7d, 0x22, 0xca, 0x01, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x3e, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x09, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x4c,
	0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x73, 0x12, 0x40, 0x0a, 0x1c, 0x73, 0x75, 0x70, 0x70, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x61, 0x6c, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1a, 0x73, 0x75, 0x70, 0x70, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0xfa, 0x01, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6f, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52,
	0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x12, 0x21, 0x0a, 0x07, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x20, 0x01, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x25, 0x0a,
	0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x12, 0x3e, 0x0a, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b,
	0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x07, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x3a, 0x36, 0xb2, 0xe1, 0x1c, 0x32, 0x0a, 0x30, 0x0a, 0x17, 0x63, 0x6c,
	0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x61, 0x6d, 0x65,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x15, 0x7b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x7d,
	0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x7d, 0x22, 0x8b, 0x01, 0x0a,
	0x10, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6f, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x2c, 0x0a, 0x04, 0x70, 0x6f, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x50, 0x6f, 0x64, 0x42, 0x04, 0xa8, 0xe1, 0x1c, 0x00, 0x52, 0x04, 0x70, 0x6f, 0x64, 0x73, 0x12,
	0x3d, 0x0a, 0x10, 0x70, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x75,
	0x72, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0f, 0x70,
	0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x73, 0x3a, 0x0a,
	0xaa, 0xe1, 0x1c, 0x06, 0x0a, 0x04, 0x70, 0x6f, 0x64, 0x73, 0x22, 0xd9, 0x01, 0x0a, 0x10, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x25, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x09, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x12, 0x21, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01,
	0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x09, 0x6e, 0x61, 0x6d,
	0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x12, 0x1b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x3a, 0x37, 0xb2,
	0xe1, 0x1c, 0x33, 0x0a, 0x31, 0x0a, 0x11, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6f, 0x64, 0x12, 0x1c, 0x7b, 0x63, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x7d, 0x2f,
	0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x22, 0x13, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x50, 0x6f, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xf2, 0x03, 0x0a, 0x10,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x25, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x09, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x12, 0x21, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20,
	0x01, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x09, 0x6e, 0x61,
	0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x12, 0x1b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x66,
	0x0a, 0x1b, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x6f, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4f, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x52, 0x18, 0x65, 0x78,
	0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x4d, 0x65, 0x74, 0x61,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x4d, 0x0a, 0x12, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x46, 0x69, 0x65,
	0x6c, 0x64, 0x73, 0x52, 0x10, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x60, 0x0a, 0x19, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x5f,
	0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63,
	0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x4f,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x52,
	0x16, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x4d, 0x65, 0x74,
	0x61, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x3a, 0x37, 0xb2, 0xe1, 0x1c, 0x33, 0x0a, 0x31, 0x0a,
	0x11, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x50,
	0x6f, 0x64, 0x12, 0x1c, 0x7b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x7d, 0x2f, 0x7b, 0x6e,
	0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d,
	0x22, 0x13, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x93, 0x02, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x64,
	0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x09, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73,
	0x65, 0x74, 0x12, 0x21, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x07, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20,
	0x01, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x20, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x37, 0x0a, 0x07, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6c, 0x75,
	0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6f, 0x64, 0x4c, 0x6f,
	0x67, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x3a, 0x37, 0xb2, 0xe1, 0x1c, 0x33, 0x0a, 0x31, 0x0a, 0x11, 0x63, 0x6c, 0x75, 0x74,
	0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6f, 0x64, 0x12, 0x1c, 0x7b,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x22, 0x94, 0x01, 0x0a, 0x0e,
	0x50, 0x6f, 0x64, 0x4c, 0x6f, 0x67, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x25,
	0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65,
	0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75,
	0x73, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x69, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x69, 0x6e, 0x63, 0x65, 0x54, 0x73, 0x12, 0x24, 0x0a, 0x0e,
	0x74, 0x61, 0x69, 0x6c, 0x5f, 0x6e, 0x75, 0x6d, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x74, 0x61, 0x69, 0x6c, 0x4e, 0x75, 0x6d, 0x4c, 0x69, 0x6e,
	0x65, 0x73, 0x22, 0x66, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x64, 0x4c, 0x6f, 0x67, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x74, 0x65,
	0x73, 0x74, 0x5f, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x74,
	0x65, 0x73, 0x74, 0x54, 0x73, 0x12, 0x2d, 0x0a, 0x04, 0x6c, 0x6f, 0x67, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6f, 0x64, 0x4c, 0x6f, 0x67, 0x4c, 0x69, 0x6e, 0x65, 0x52, 0x04,
	0x6c, 0x6f, 0x67, 0x73, 0x3a, 0x04, 0xb8, 0xe1, 0x1c, 0x01, 0x22, 0x2a, 0x0a, 0x0a, 0x50, 0x6f,
	0x64, 0x4c, 0x6f, 0x67, 0x4c, 0x69, 0x6e, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x74, 0x73, 0x12, 0x0c, 0x0a, 0x01, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x01, 0x73, 0x22, 0xde, 0x04, 0x0a, 0x03, 0x48, 0x50, 0x41, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d,
	0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x31, 0x0a, 0x06, 0x73, 0x69,
	0x7a, 0x69, 0x6e, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6c, 0x75,
	0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x50, 0x41, 0x2e, 0x53,
	0x69, 0x7a, 0x69, 0x6e, 0x67, 0x52, 0x06, 0x73, 0x69, 0x7a, 0x69, 0x6e, 0x67, 0x12, 0x36, 0x0a,
	0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x50,
	0x41, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x45, 0x0a, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x6c, 0x75,
	0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x50, 0x41, 0x2e, 0x41,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0xa4, 0x01, 0x0a,
	0x06, 0x53, 0x69, 0x7a, 0x69, 0x6e, 0x67, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x69, 0x6e, 0x5f, 0x72,
	0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x6d,
	0x69, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x61,
	0x78, 0x5f, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0b, 0x6d, 0x61, 0x78, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x12, 0x29, 0x0a,
	0x10, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x64, 0x65, 0x73, 0x69,
	0x72, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0f, 0x64, 0x65, 0x73, 0x69, 0x72, 0x65, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3e,
	0x0a, 0x10, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x3a, 0x37,
	0xb2, 0xe1, 0x1c, 0x33, 0x0a, 0x31, 0x0a, 0x11, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b,
	0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x50, 0x41, 0x12, 0x1c, 0x7b, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x7d,
	0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x22, 0xa0, 0x03, 0x0a, 0x10, 0x52, 0x65, 0x73, 0x69,
	0x7a, 0x65, 0x48, 0x50, 0x41, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x09,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x73, 0x65, 0x74, 0x12, 0x21, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x07, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x20, 0x01, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x1b, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x20, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x48, 0x0a, 0x06, 0x73, 0x69,
	0x7a, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x6c, 0x75,
	0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x69, 0x7a,
	0x65, 0x48, 0x50, 0x41, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x53, 0x69, 0x7a, 0x69,
	0x6e, 0x67, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x73, 0x69,
	0x7a, 0x69, 0x6e, 0x67, 0x12, 0x4d, 0x0a, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f,
	0x73, 0x69, 0x7a, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63,
	0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73,
	0x69, 0x7a, 0x65, 0x48, 0x50, 0x41, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x53, 0x69,
	0x7a, 0x69, 0x6e, 0x67, 0x52, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x69, 0x7a,
	0x69, 0x6e, 0x67, 0x1a, 0x2c, 0x0a, 0x06, 0x53, 0x69, 0x7a, 0x69, 0x6e, 0x67, 0x12, 0x10, 0x0a,
	0x03, 0x6d, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6d, 0x69, 0x6e, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x61, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x6d, 0x61,
	0x78, 0x3a, 0x37, 0xb2, 0xe1, 0x1c, 0x33, 0x0a, 0x31, 0x0a, 0x11, 0x63, 0x6c, 0x75, 0x74, 0x63,
	0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x50, 0x41, 0x12, 0x1c, 0x7b, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x22, 0x13, 0x0a, 0x11, 0x52, 0x65,
	0x73, 0x69, 0x7a, 0x65, 0x48, 0x50, 0x41, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0xd9, 0x01, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x48, 0x50, 0x41, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01,
	0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x12, 0x21, 0x0a, 0x07, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x25,
	0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x3a, 0x37, 0xb2, 0xe1, 0x1c, 0x33, 0x0a, 0x31, 0x0a, 0x11, 0x63, 0x6c, 0x75, 0x74,
	0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x50, 0x41, 0x12, 0x1c, 0x7b,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x22, 0x13, 0x0a, 0x11, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x48, 0x50, 0x41, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x26, 0x0a, 0x0a, 0x45, 0x78, 0x65, 0x63, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x22, 0xdc, 0x01, 0x0a, 0x0d, 0x48, 0x54, 0x54,
	0x50, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x04, 0x70, 0x61,
	0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x72, 0x07, 0x32,
	0x05, 0x5e, 0x2f, 0x2e, 0x2a, 0x24, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x1f, 0x0a, 0x04,
	0x70, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x1a,
	0x06, 0x18, 0xff, 0xff, 0x03, 0x28, 0x00, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x1b, 0x0a,
	0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x68, 0x01, 0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x2d, 0x0a, 0x06, 0x73, 0x63,
	0x68, 0x65, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0xfa, 0x42, 0x12, 0x72,
	0x10, 0x32, 0x0e, 0x5e, 0x28, 0x68, 0x74, 0x74, 0x70, 0x7c, 0x68, 0x74, 0x74, 0x70, 0x73, 0x29,
	0x24, 0x52, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x0c, 0x68, 0x74, 0x74,
	0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x48, 0x54, 0x54, 0x50, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0b, 0x68, 0x74, 0x74, 0x70,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x22, 0x53, 0x0a, 0x0a, 0x48, 0x54, 0x54, 0x50, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x19,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x4f, 0x0a, 0x0f,
	0x54, 0x43, 0x50, 0x53, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x1f, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0xfa,
	0x42, 0x08, 0x1a, 0x06, 0x18, 0xff, 0xff, 0x03, 0x28, 0x00, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74,
	0x12, 0x1b, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x68, 0x01, 0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x22, 0x63, 0x0a,
	0x0a, 0x47, 0x52, 0x50, 0x43, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x04, 0x70,
	0x6f, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x1a, 0x06,
	0x18, 0xff, 0xff, 0x03, 0x28, 0x00, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x34, 0x0a, 0x07,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa,
	0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x24, 0x7c, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x41, 0x2d,
	0x5a, 0x30, 0x2d, 0x39, 0x2e, 0x2d, 0x5d, 0x2b, 0x24, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x22, 0xc7, 0x05, 0x0a, 0x05, 0x50, 0x72, 0x6f, 0x62, 0x65, 0x12, 0x2f, 0x0a, 0x04,
	0x65, 0x78, 0x65, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6c, 0x75,
	0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x04, 0x65, 0x78, 0x65, 0x63, 0x12, 0x39, 0x0a,
	0x08, 0x68, 0x74, 0x74, 0x70, 0x5f, 0x67, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x48, 0x54, 0x54, 0x50, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52,
	0x07, 0x68, 0x74, 0x74, 0x70, 0x47, 0x65, 0x74, 0x12, 0x3f, 0x0a, 0x0a, 0x74, 0x63, 0x70, 0x5f,
	0x73, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63,
	0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x43, 0x50,
	0x53, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x09,
	0x74, 0x63, 0x70, 0x53, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x2f, 0x0a, 0x04, 0x67, 0x72, 0x70,
	0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68,
	0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x52, 0x50, 0x43, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x48, 0x00, 0x52, 0x04, 0x67, 0x72, 0x70, 0x63, 0x12, 0x37, 0x0a, 0x15, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x5f, 0x73, 0x65, 0x63, 0x6f,
	0x6e, 0x64, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x48, 0x01, 0x52, 0x13, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x61, 0x6c, 0x44, 0x65, 0x6c, 0x61, 0x79, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73,
	0x88, 0x01, 0x01, 0x12, 0x2c, 0x0a, 0x0f, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x5f, 0x73,
	0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x48, 0x02, 0x52, 0x0e,
	0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x88, 0x01,
	0x01, 0x12, 0x2a, 0x0a, 0x0e, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x5f, 0x73, 0x65, 0x63, 0x6f,
	0x6e, 0x64, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x48, 0x03, 0x52, 0x0d, 0x70, 0x65, 0x72,
	0x69, 0x6f, 0x64, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x88, 0x01, 0x01, 0x12, 0x30, 0x0a,
	0x11, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f,
	0x6c, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x48, 0x04, 0x52, 0x10, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x88, 0x01, 0x01, 0x12,
	0x30, 0x0a, 0x11, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73,
	0x68, 0x6f, 0x6c, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x48, 0x05, 0x52, 0x10, 0x66, 0x61,
	0x69, 0x6c, 0x75, 0x72, 0x65, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x88, 0x01,
	0x01, 0x12, 0x4c, 0x0a, 0x20, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x67, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x5f, 0x73, 0x65,
	0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x48, 0x06, 0x52, 0x1d, 0x74,
	0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x61, 0x63, 0x65, 0x50,
	0x65, 0x72, 0x69, 0x6f, 0x64, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x88, 0x01, 0x01, 0x42,
	0x09, 0x0a, 0x07, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x72, 0x42, 0x18, 0x0a, 0x16, 0x5f, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x5f, 0x73, 0x65, 0x63,
	0x6f, 0x6e, 0x64, 0x73, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74,
	0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x70, 0x65, 0x72,
	0x69, 0x6f, 0x64, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x42, 0x14, 0x0a, 0x12, 0x5f,
	0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c,
	0x64, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x74, 0x68,
	0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x42, 0x23, 0x0a, 0x21, 0x5f, 0x74, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x67, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x70, 0x65,
	0x72, 0x69, 0x6f, 0x64, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x22, 0xd3, 0x13, 0x0a,
	0x0a, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3d, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68,
	0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06,
	0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x4c, 0x0a, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x6c,
	0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x70, 0x6c,
	0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x57, 0x0a, 0x11, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2a, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x10, 0x64, 0x65, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x30, 0x0a,
	0x14, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d,
	0x69, 0x6c, 0x6c, 0x69, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x69, 0x6c, 0x6c, 0x69, 0x73, 0x12,
	0x51, 0x0a, 0x0f, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x70,
	0x65, 0x63, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63,
	0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x70,
	0x65, 0x63, 0x52, 0x0e, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x70,
	0x65, 0x63, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3e, 0x0a,
	0x10, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x96, 0x06,
	0x0a, 0x10, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x12, 0x29,
	0x0a, 0x10, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x61,
	0x64, 0x79, 0x5f, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0d, 0x72, 0x65, 0x61, 0x64, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73,
	0x12, 0x2d, 0x0a, 0x12, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x72, 0x65,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x11, 0x61, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x12,
	0x31, 0x0a, 0x14, 0x75, 0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x72,
	0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x13, 0x75,
	0x6e, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x73, 0x12, 0x69, 0x0a, 0x15, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x34, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x43, 0x6f,
	0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x14, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0xc6, 0x03,
	0x0a, 0x09, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4d, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x63, 0x6c, 0x75, 0x74,
	0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x6f, 0x0a, 0x10, 0x63, 0x6f,
	0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x44, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x59, 0x0a,
	0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57,
	0x4e, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41, 0x42, 0x4c, 0x45,
	0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x49, 0x4e,
	0x47, 0x10, 0x03, 0x12, 0x13, 0x0a, 0x0f, 0x52, 0x45, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x5f, 0x46,
	0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x04, 0x22, 0x6c, 0x0a, 0x0f, 0x43, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x19, 0x0a, 0x15, 0x43,
	0x4f, 0x4e, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x4f, 0x4e, 0x44, 0x49, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x52, 0x55, 0x45, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x4f,
	0x4e, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x4c, 0x53, 0x45, 0x10, 0x02, 0x12,
	0x15, 0x0a, 0x11, 0x43, 0x4f, 0x4e, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x4b,
	0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x03, 0x1a, 0xb9, 0x08, 0x0a, 0x0e, 0x44, 0x65, 0x70, 0x6c, 0x6f,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x70, 0x65, 0x63, 0x12, 0x54, 0x0a, 0x08, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x63, 0x6c,
	0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x70, 0x6c,
	0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x53, 0x70, 0x65, 0x63, 0x2e, 0x50, 0x6f, 0x64, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x53, 0x70, 0x65, 0x63, 0x52, 0x08, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x1a,
	0xd0, 0x07, 0x0a, 0x0f, 0x50, 0x6f, 0x64, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x53,
	0x70, 0x65, 0x63, 0x12, 0x54, 0x0a, 0x04, 0x73, 0x70, 0x65, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x40, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x70, 0x65, 0x63, 0x2e, 0x50, 0x6f, 0x64, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x53, 0x70, 0x65, 0x63, 0x2e, 0x50, 0x6f, 0x64, 0x53,
	0x70, 0x65, 0x63, 0x52, 0x04, 0x73, 0x70, 0x65, 0x63, 0x1a, 0xe6, 0x06, 0x0a, 0x07, 0x50, 0x6f,
	0x64, 0x53, 0x70, 0x65, 0x63, 0x12, 0x6a, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4a, 0x2e, 0x63, 0x6c, 0x75, 0x74,
	0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53,
	0x70, 0x65, 0x63, 0x2e, 0x50, 0x6f, 0x64, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x53,
	0x70, 0x65, 0x63, 0x2e, 0x50, 0x6f, 0x64, 0x53, 0x70, 0x65, 0x63, 0x2e, 0x43, 0x6f, 0x6e, 0x74,
	0x61, 0x69, 0x6e, 0x65, 0x72, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72,
	0x73, 0x1a, 0xee, 0x05, 0x0a, 0x09, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x7d, 0x0a, 0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x5f, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e,
	0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x70, 0x65, 0x63,
	0x2e, 0x50, 0x6f, 0x64, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x53, 0x70, 0x65, 0x63,
	0x2e, 0x50, 0x6f, 0x64, 0x53, 0x70, 0x65, 0x63, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x73, 0x12, 0x40, 0x0a, 0x0e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70,
	0x72, 0x6f, 0x62, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6c, 0x75,
	0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x62, 0x65,
	0x48, 0x00, 0x52, 0x0d, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x72, 0x6f, 0x62,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x42, 0x0a, 0x0f, 0x72, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72,
	0x6f, 0x62, 0x65, 0x48, 0x01, 0x52, 0x0e, 0x72, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x50, 0x72, 0x6f, 0x62, 0x65, 0x88, 0x01, 0x01, 0x1a, 0xa0, 0x03, 0x0a, 0x14, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x12, 0x83, 0x01, 0x0a, 0x06, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x6b, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x65,
	0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x70, 0x65, 0x63, 0x2e, 0x50, 0x6f, 0x64,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x53, 0x70, 0x65, 0x63, 0x2e, 0x50, 0x6f, 0x64,
	0x53, 0x70, 0x65, 0x63, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x06, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x12, 0x89, 0x01, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x6d, 0x2e, 0x63, 0x6c, 0x75,
	0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x53, 0x70, 0x65, 0x63, 0x2e, 0x50, 0x6f, 0x64, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x53, 0x70, 0x65, 0x63, 0x2e, 0x50, 0x6f, 0x64, 0x53, 0x70, 0x65, 0x63, 0x2e, 0x43, 0x6f, 0x6e,
	0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3b,
	0x0a, 0x0d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x11, 0x0a, 0x0f, 0x5f,
	0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x42, 0x12,
	0x0a, 0x10, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x72, 0x6f,
	0x62, 0x65, 0x3a, 0x3e, 0xb2, 0xe1, 0x1c, 0x3a, 0x0a, 0x38, 0x0a, 0x18, 0x63, 0x6c, 0x75, 0x74,
	0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1c, 0x7b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x7d, 0x2f,
	0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d,
	0x65, 0x7d, 0x22, 0xe9, 0x01, 0x0a, 0x19, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x44,
	0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x25, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x09, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x12, 0x21, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20,
	0x01, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x09, 0x6e, 0x61,
	0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x12, 0x1b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x3a, 0x3e,
	0xb2, 0xe1, 0x1c, 0x3a, 0x0a, 0x38, 0x0a, 0x18, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b,
	0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x12, 0x1c, 0x7b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d,
	0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x22, 0x6f,
	0x0a, 0x1a, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3f, 0x0a, 0x0a,
	0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x04, 0xa8, 0xe1, 0x1c,
	0x00, 0x52, 0x0a, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x3a, 0x10, 0xaa,
	0xe1, 0x1c, 0x0c, 0x0a, 0x0a, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x22,
	0x81, 0x02, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x09, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65,
	0x74, 0x12, 0x21, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x07, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01,
	0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x3e, 0x0a, 0x07, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63,
	0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02,
	0x10, 0x01, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x3a, 0x36, 0xb2, 0xe1, 0x1c,
	0x32, 0x0a, 0x30, 0x0a, 0x17, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x15, 0x7b, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x7d, 0x22, 0x6f, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x41,
	0x0a, 0x0b, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x04,
	0xa8, 0xe1, 0x1c, 0x00, 0x52, 0x0b, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x3a, 0x11, 0xaa, 0xe1, 0x1c, 0x0d, 0x0a, 0x0b, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x22, 0x94, 0x0d, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44,
	0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x25, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x09, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x12, 0x21, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20,
	0x01, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x09, 0x6e, 0x61,
	0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x12, 0x1b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x4f,
	0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d,
	0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x1a,
	0xd9, 0x0a, 0x0a, 0x06, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x5f, 0x0a, 0x06, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x63, 0x6c, 0x75,
	0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x9a, 0x01, 0x06, 0x22, 0x04, 0x72,
	0x02, 0x20, 0x01, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x6e, 0x0a, 0x0b, 0x61,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x3e, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x2e,
	0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x42, 0x0c, 0xfa, 0x42, 0x09, 0x9a, 0x01, 0x06, 0x22, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x0b,
	0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x71, 0x0a, 0x13, 0x63,
	0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63,
	0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44,
	0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65,
	0x72, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x52, 0x12, 0x63, 0x6f, 0x6e, 0x74,
	0x61, 0x69, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x68,
	0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x62,
	0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63,
	0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44,
	0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65,
	0x72, 0x50, 0x72, 0x6f, 0x62, 0x65, 0x73, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x50, 0x72, 0x6f, 0x62, 0x65, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0x3e, 0x0a, 0x10, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0xbd, 0x04, 0x0a, 0x12, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65,
	0x72, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f,
	0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x73, 0x0a, 0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x55, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6c, 0x6f,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x65,
	0x6c, 0x64, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x09, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x1a, 0x8a, 0x03, 0x0a, 0x14, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12,
	0x79, 0x0a, 0x06, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x61, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x2e, 0x43,
	0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x73, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x06, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x12, 0x7f, 0x0a, 0x08, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x63, 0x2e, 0x63,
	0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x74,
	0x61, 0x69, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x2e, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x08, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3b, 0x0a, 0x0d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0xe5, 0x01, 0x0a, 0x0f, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65,
	0x72, 0x50, 0x72, 0x6f, 0x62, 0x65, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x74, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x40,
	0x0a, 0x0e, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x72, 0x6f, 0x62, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e,
	0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x62, 0x65, 0x48, 0x00, 0x52, 0x0d,
	0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x72, 0x6f, 0x62, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x42, 0x0a, 0x0f, 0x72, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x72,
	0x6f, 0x62, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6c, 0x75, 0x74,
	0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x62, 0x65, 0x48,
	0x01, 0x52, 0x0e, 0x72, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x50, 0x72, 0x6f, 0x62,
	0x65, 0x88, 0x01, 0x01, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x6c, 0x69, 0x76, 0x65, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x72, 0x65, 0x61, 0x64,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x3a, 0x3e, 0xb2, 0xe1, 0x1c,
	0x3a, 0x0a, 0x38, 0x0a, 0x18, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1c, 0x7b,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x22, 0x1a, 0x0a, 0x18, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xe7, 0x01, 0x0a, 0x17, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52,
	0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x12, 0x21, 0x0a, 0x07, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x20, 0x01, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x25, 0x0a,
	0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x3a, 0x3e, 0xb2, 0xe1, 0x1c, 0x3a, 0x0a, 0x38, 0x0a, 0x18, 0x63, 0x6c, 0x75, 0x74, 0x63,
	0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x12, 0x1c, 0x7b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x7d, 0x2f, 0x7b,
	0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x22, 0x1a, 0x0a, 0x18, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6c, 0x6f,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x89, 0x05,
	0x0a, 0x0b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x53, 0x65, 0x74, 0x12, 0x18, 0x0a,
	0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3e, 0x0a, 0x06, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x6c, 0x75, 0x74,
	0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x66,
	0x75, 0x6c, 0x53, 0x65, 0x74, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x4d, 0x0a, 0x0b, 0x61, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b,
	0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x53, 0x65, 0x74, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x61, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x39, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63,
	0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75,
	0x6c, 0x53, 0x65, 0x74, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x69, 0x6c, 0x6c, 0x69, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x12, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x4d,
	0x69, 0x6c, 0x6c, 0x69, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x1a, 0x3e, 0x0a, 0x10, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x1a, 0x76, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x72, 0x65,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x73, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x61, 0x64, 0x79, 0x5f, 0x72, 0x65, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x72, 0x65, 0x61, 0x64, 0x79,
	0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x3a, 0x3f, 0xb2, 0xe1, 0x1c, 0x3b, 0x0a, 0x39,
	0x0a, 0x19, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x53, 0x65, 0x74, 0x12, 0x1c, 0x7b, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x22, 0xeb, 0x01, 0x0a, 0x1a, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x53, 0x65,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x20, 0x01, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x12,
	0x21, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x12, 0x25, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x09,
	0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x3a, 0x3f, 0xb2, 0xe1, 0x1c, 0x3b, 0x0a, 0x39, 0x0a, 0x19,
	0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x53, 0x65, 0x74, 0x12, 0x1c, 0x7b, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x7d,
	0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x22, 0x76, 0x0a, 0x1b, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x53, 0x65, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x43, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x74, 0x65, 0x66,
	0x75, 0x6c, 0x5f, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63,
	0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x66, 0x75, 0x6c, 0x53, 0x65, 0x74, 0x42, 0x04, 0xa8, 0xe1, 0x1c, 0x00, 0x52, 0x0b,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x53, 0x65, 0x74, 0x3a, 0x12, 0xaa, 0xe1, 0x1c,
	0x0e, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x5f, 0x73, 0x65, 0x74, 0x22,
	0x82, 0x02, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c,
	0x53, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x09, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73,
	0x65, 0x74, 0x12, 0x21, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x07, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20,
	0x01, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x3e, 0x0a, 0x07,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x3a, 0x36, 0xb2, 0xe1,
	0x1c, 0x32, 0x0a, 0x30, 0x0a, 0x17, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x15, 0x7b,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x7d, 0x22, 0x76, 0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x66, 0x75, 0x6c, 0x53, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x45, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x5f, 0x73, 0x65, 0x74,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68,
	0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c,
	0x53, 0x65, 0x74, 0x42, 0x04, 0xa8, 0xe1, 0x1c, 0x00, 0x52, 0x0c, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x66, 0x75, 0x6c, 0x53, 0x65, 0x74, 0x73, 0x3a, 0x13, 0xaa, 0xe1, 0x1c, 0x0f, 0x0a, 0x0d, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x5f, 0x73, 0x65, 0x74, 0x73, 0x22, 0xe9, 0x01, 0x0a,
	0x18, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x53,
	0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x09, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74,
	0x12, 0x21, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52,
	0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20,
	0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x3a, 0x3f, 0xb2, 0xe1, 0x1c, 0x3b, 0x0a, 0x39, 0x0a,
	0x19, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x53, 0x65, 0x74, 0x12, 0x1c, 0x7b, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x22, 0x1b, 0x0a, 0x19, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x53, 0x65, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x94, 0x05, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x53, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x25, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x09,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x12, 0x21, 0x0a, 0x07, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x20, 0x01, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x09,
	0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x50, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2e, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x53,
	0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x73, 0x1a, 0xd6, 0x02, 0x0a, 0x06, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x60, 0x0a,
	0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e,
	0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x53, 0x65, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x2e, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x9a, 0x01,
	0x06, 0x22, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12,
	0x6f, 0x0a, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x66, 0x75, 0x6c, 0x53, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x73, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x9a, 0x01, 0x06, 0x22, 0x04, 0x72,
	0x02, 0x20, 0x01, 0x52, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3e, 0x0a, 0x10, 0x41,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x3a, 0x3f, 0xb2, 0xe1, 0x1c,
	0x3b, 0x0a, 0x39, 0x0a, 0x19, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x53, 0x65, 0x74, 0x12, 0x1c,
	0x7b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x22, 0x1b, 0x0a, 0x19,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x53, 0x65,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xaf, 0x05, 0x0a, 0x07, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12,
	0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x2f, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1b, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x3a, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x49,
	0x0a, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x41, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x40, 0x0a, 0x08, 0x73, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x6c,
	0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x08, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x1a, 0x39, 0x0a, 0x0b, 0x4c,
	0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3e, 0x0a, 0x10, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3b, 0x0a, 0x0d, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x69, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07,
	0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x43, 0x4c, 0x55,
	0x53, 0x54, 0x45, 0x52, 0x5f, 0x49, 0x50, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x44,
	0x45, 0x5f, 0x50, 0x4f, 0x52, 0x54, 0x10, 0x03, 0x12, 0x11, 0x0a, 0x0d, 0x4c, 0x4f, 0x41, 0x44,
	0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x52, 0x10, 0x04, 0x12, 0x11, 0x0a, 0x0d, 0x45,
	0x58, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x10, 0x05, 0x3a, 0x3b,
	0xb2, 0xe1, 0x1c, 0x37, 0x0a, 0x35, 0x0a, 0x15, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b,
	0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1c, 0x7b,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x22, 0xe3, 0x01, 0x0a, 0x16,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x20, 0x01, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x12, 0x21, 0x0a,
	0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x12, 0x25, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x09, 0x6e, 0x61,
	0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x3a, 0x3b, 0xb2, 0xe1, 0x1c, 0x37, 0x0a, 0x35, 0x0a, 0x15, 0x63, 0x6c,
	0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x1c, 0x7b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x7d, 0x2f, 0x7b,
	0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x22, 0x60, 0x0a, 0x17, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36, 0x0a, 0x07,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x04, 0xa8, 0xe1, 0x1c, 0x00, 0x52, 0x07, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x3a, 0x0d, 0xaa, 0xe1, 0x1c, 0x09, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x22, 0xc6, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x09, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73,
	0x65, 0x74, 0x12, 0x21, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x07, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20,
	0x01, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x3e, 0x0a, 0x07,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x60, 0x0a, 0x14,
	0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e,
	0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42, 0x04,
	0xa8, 0xe1, 0x1c, 0x00, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x3a, 0x0e,
	0xaa, 0xe1, 0x1c, 0x0a, 0x0a, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x22, 0xe1,
	0x01, 0x0a, 0x14, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x20, 0x01, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x12, 0x21,
	0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x12, 0x25, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x09, 0x6e,
	0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x3a, 0x3b, 0xb2, 0xe1, 0x1c, 0x37, 0x0a, 0x35, 0x0a, 0x15, 0x63,
	0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x1c, 0x7b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x7d, 0x2f,
	0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d,
	0x65, 0x7d, 0x22, 0x17, 0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xfb, 0x05, 0x0a, 0x07,
	0x43, 0x72, 0x6f, 0x6e, 0x4a, 0x6f, 0x62, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12,
	0x3a, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x72, 0x6f, 0x6e, 0x4a, 0x6f, 0x62, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x49, 0x0a, 0x0b, 0x61,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x6f, 0x6e, 0x4a, 0x6f, 0x62, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x73, 0x70, 0x65, 0x6e,
	0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x73, 0x70, 0x65, 0x6e, 0x64,
	0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x75, 0x6d, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x6a,
	0x6f, 0x62, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x6e, 0x75, 0x6d, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x4a, 0x6f, 0x62, 0x73, 0x12, 0x57, 0x0a, 0x12, 0x63, 0x6f, 0x6e, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x6f, 0x6e, 0x4a, 0x6f, 0x62, 0x2e, 0x43, 0x6f, 0x6e,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x11,
	0x63, 0x6f, 0x6e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x50, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x12, 0x57, 0x0a, 0x19, 0x73, 0x74, 0x61, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65,
	0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x17, 0x73, 0x74, 0x61, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x61, 0x64, 0x6c,
	0x69, 0x6e, 0x65, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3e, 0x0a, 0x10, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x55, 0x0a, 0x11, 0x43, 0x6f, 0x6e, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x63, 0x79, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x55,
	0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x41, 0x4c, 0x4c, 0x4f,
	0x57, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x4f, 0x52, 0x42, 0x49, 0x44, 0x10, 0x03, 0x12,
	0x0b, 0x0a, 0x07, 0x52, 0x45, 0x50, 0x4c, 0x41, 0x43, 0x45, 0x10, 0x04, 0x3a, 0x3b, 0xb2, 0xe1,
	0x1c, 0x37, 0x0a, 0x35, 0x0a, 0x15, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x6f, 0x6e, 0x4a, 0x6f, 0x62, 0x12, 0x1c, 0x7b, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x22, 0xe3, 0x01, 0x0a, 0x16, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x43, 0x72, 0x6f, 0x6e, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01,
	0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x12, 0x21, 0x0a, 0x07, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x25,
	0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x3a, 0x3b, 0xb2, 0xe1, 0x1c, 0x37, 0x0a, 0x35, 0x0a, 0x15, 0x63, 0x6c, 0x75, 0x74,
	0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x6f, 0x6e, 0x4a, 0x6f,
	0x62, 0x12, 0x1c, 0x7b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x7d, 0x2f, 0x7b, 0x6e, 0x61,
	0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x22,
	0x5d, 0x0a, 0x17, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x43, 0x72, 0x6f, 0x6e, 0x4a,
	0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36, 0x0a, 0x07, 0x63, 0x72,
	0x6f, 0x6e, 0x6a, 0x6f, 0x62, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6c,
	0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x6f, 0x6e,
	0x4a, 0x6f, 0x62, 0x42, 0x04, 0xa8, 0xe1, 0x1c, 0x00, 0x52, 0x07, 0x63, 0x72, 0x6f, 0x6e, 0x6a,
	0x6f, 0x62, 0x3a, 0x0a, 0xaa, 0xe1, 0x1c, 0x06, 0x0a, 0x04, 0x63, 0x72, 0x6f, 0x6e, 0x22, 0xfe,
	0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x72, 0x6f, 0x6e, 0x4a, 0x6f, 0x62, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x20, 0x01, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x12, 0x21, 0x0a,
	0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x12, 0x25, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x09, 0x6e, 0x61,
	0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x3e, 0x0a, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63,
	0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x07,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x3a, 0x36, 0xb2, 0xe1, 0x1c, 0x32, 0x0a, 0x30, 0x0a,
	0x17, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4e,
	0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x15, 0x7b, 0x63, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x7d, 0x22,
	0x62, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x72, 0x6f, 0x6e, 0x4a, 0x6f, 0x62, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x39, 0x0a, 0x09, 0x63, 0x72, 0x6f, 0x6e, 0x5f,
	0x6a, 0x6f, 0x62, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6c, 0x75,
	0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x6f, 0x6e, 0x4a,
	0x6f, 0x62, 0x42, 0x04, 0xa8, 0xe1, 0x1c, 0x00, 0x52, 0x08, 0x63, 0x72, 0x6f, 0x6e, 0x4a, 0x6f,
	0x62, 0x73, 0x3a, 0x0f, 0xaa, 0xe1, 0x1c, 0x0b, 0x0a, 0x09, 0x63, 0x72, 0x6f, 0x6e, 0x5f, 0x6a,
	0x6f, 0x62, 0x73, 0x22, 0xe1, 0x01, 0x0a, 0x14, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x72,
	0x6f, 0x6e, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x09,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x73, 0x65, 0x74, 0x12, 0x21, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x07, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x20, 0x01, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x1b, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x20, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x3a, 0x3b, 0xb2, 0xe1, 0x1c, 0x37,
	0x0a, 0x35, 0x0a, 0x15, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x6f, 0x6e, 0x4a, 0x6f, 0x62, 0x12, 0x1c, 0x7b, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x7d,
	0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x22, 0x17, 0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x43, 0x72, 0x6f, 0x6e, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x97, 0x05, 0x0a, 0x09, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d,
	0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x4b, 0x0a, 0x0b, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x29, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x61, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x3c, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68,
	0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61,
	0x70, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x36, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x2e, 0x44, 0x61,
	0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x49, 0x0a,
	0x0b, 0x62, 0x69, 0x6e, 0x61, 0x72, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x07, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x2e, 0x42, 0x69, 0x6e,
	0x61, 0x72, 0x79, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x62, 0x69,
	0x6e, 0x61, 0x72, 0x79, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x3e, 0x0a, 0x10, 0x41, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0x37, 0x0a, 0x09, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3d, 0x0a, 0x0f,
	0x42, 0x69, 0x6e, 0x61, 0x72, 0x79, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x3a, 0x3d, 0xb2, 0xe1, 0x1c,
	0x39, 0x0a, 0x37, 0x0a, 0x17, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x12, 0x1c, 0x7b, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x22, 0x80, 0x02, 0x0a, 0x15, 0x4c,
	0x69, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01,
	0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x12, 0x21, 0x0a, 0x07, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x25,
	0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x3e, 0x0a, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e,
	0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x07, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x3a, 0x36, 0xb2, 0xe1, 0x1c, 0x32, 0x0a, 0x30, 0x0a, 0x17, 0x63,
	0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x61, 0x6d,
	0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x15, 0x7b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x7d, 0x22, 0x6c, 0x0a,
	0x16, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3f, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x6d, 0x61, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63,
	0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x42, 0x04, 0xa8, 0xe1, 0x1c, 0x00, 0x52, 0x0a, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x73, 0x3a, 0x11, 0xaa, 0xe1, 0x1c, 0x0d, 0x0a, 0x0b,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x6d, 0x61, 0x70, 0x73, 0x22, 0xe7, 0x01, 0x0a, 0x18,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61,
	0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x20, 0x01, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x12,
	0x21, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x12, 0x25, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x09,
	0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x3a, 0x3d, 0xb2, 0xe1, 0x1c, 0x39, 0x0a, 0x37, 0x0a, 0x17,
	0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x12, 0x1c, 0x7b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x7d, 0x2f, 0x7b,
	0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x22, 0x6c, 0x0a, 0x19, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x3d, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x6d, 0x61, 0x70,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e,
	0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70,
	0x42, 0x04, 0xa8, 0xe1, 0x1c, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61,
	0x70, 0x3a, 0x10, 0xaa, 0xe1, 0x1c, 0x0c, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f,
	0x6d, 0x61, 0x70, 0x22, 0xe5, 0x01, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25,
	0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x73, 0x65, 0x74, 0x12, 0x21, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52,
	0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x20, 0x01, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12,
	0x1b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x3a, 0x3d, 0xb2, 0xe1,
	0x1c, 0x39, 0x0a, 0x37, 0x0a, 0x17, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x12, 0x1c, 0x7b,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x22, 0x19, 0x0a, 0x17, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x84, 0x03, 0x0a, 0x03, 0x4a, 0x6f, 0x62, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d,
	0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x45, 0x0a, 0x0b, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x23, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x4a, 0x6f, 0x62, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x36, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x1a, 0x3e, 0x0a, 0x10, 0x41, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x3a, 0x37, 0xb2, 0xe1, 0x1c, 0x33, 0x0a, 0x31, 0x0a, 0x11, 0x63, 0x6c,
	0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x12,
	0x1c, 0x7b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x22, 0xdb, 0x01,
	0x0a, 0x12, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01,
	0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x12, 0x21, 0x0a, 0x07, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x25,
	0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x3a, 0x37, 0xb2, 0xe1, 0x1c, 0x33, 0x0a, 0x31, 0x0a, 0x11, 0x63, 0x6c, 0x75, 0x74,
	0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x12, 0x1c, 0x7b,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x22, 0x4c, 0x0a, 0x13, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x2a, 0x0a, 0x03, 0x6a, 0x6f, 0x62, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x4a, 0x6f, 0x62, 0x42, 0x04, 0xa8, 0xe1, 0x1c, 0x00, 0x52, 0x03, 0x6a, 0x6f, 0x62, 0x3a, 0x09,
	0xaa, 0xe1, 0x1c, 0x05, 0x0a, 0x03, 0x6a, 0x6f, 0x62, 0x22, 0xfa, 0x01, 0x0a, 0x0f, 0x4c, 0x69,
	0x73, 0x74, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a,
	0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x73, 0x65, 0x74, 0x12, 0x21, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x07,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x20, 0x01, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x3e,
	0x0a, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x3a, 0x36,
	0xb2, 0xe1, 0x1c, 0x32, 0x0a, 0x30, 0x0a, 0x17, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b,
	0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12,
	0x15, 0x7b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x7d, 0x22, 0x4c, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f,
	0x62, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2c, 0x0a, 0x04, 0x6a, 0x6f,
	0x62, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63,
	0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x42, 0x04, 0xa8, 0xe1,
	0x1c, 0x00, 0x52, 0x04, 0x6a, 0x6f, 0x62, 0x73, 0x3a, 0x0a, 0xaa, 0xe1, 0x1c, 0x06, 0x0a, 0x04,
	0x6a, 0x6f, 0x62, 0x73, 0x22, 0xd9, 0x01, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4a,
	0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x09, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74,
	0x12, 0x21, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52,
	0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20,
	0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x3a, 0x37, 0xb2, 0xe1, 0x1c, 0x33, 0x0a, 0x31, 0x0a,
	0x11, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4a,
	0x6f, 0x62, 0x12, 0x1c, 0x7b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x7d, 0x2f, 0x7b, 0x6e,
	0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d,
	0x22, 0x13, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x39, 0x0a, 0x09, 0x4a, 0x6f, 0x62, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x2c, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x22, 0xee, 0x01, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73,
	0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20,
	0x01, 0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x12, 0x21, 0x0a, 0x07,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12,
	0x25, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x09, 0x6e, 0x61, 0x6d,
	0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x37, 0x0a, 0x0a, 0x6a, 0x6f, 0x62, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6c, 0x75,
	0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x09, 0x6a, 0x6f, 0x62, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x3a,
	0x30, 0xb2, 0xe1, 0x1c, 0x2c, 0x0a, 0x2a, 0x0a, 0x11, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e,
	0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x12, 0x15, 0x7b, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x7d, 0x22, 0x3f, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2a, 0x0a, 0x03, 0x6a, 0x6f, 0x62, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x4a, 0x6f, 0x62, 0x42, 0x04, 0xa8, 0xe1, 0x1c, 0x00, 0x52, 0x03, 0x6a,
	0x6f, 0x62, 0x22, 0xf2, 0x02, 0x0a, 0x09, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x4b,
	0x0a, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x41, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b,
	0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x3c, 0x0a, 0x06, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x6c,
	0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x61, 0x6d, 0x65,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x1a, 0x3e, 0x0a, 0x10, 0x41, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x3a, 0x31, 0xb2, 0xe1, 0x1c, 0x2d, 0x0a, 0x2b, 0x0a, 0x17, 0x63, 0x6c,
	0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x61, 0x6d, 0x65,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x10, 0x7b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x7d,
	0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x22, 0xb4, 0x01, 0x0a, 0x18, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01,
	0x52, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x12, 0x21, 0x0a, 0x07, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x1b,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x3a, 0x31, 0xb2, 0xe1, 0x1c,
	0x2d, 0x0a, 0x2b, 0x0a, 0x17, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x10, 0x7b, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x22, 0x6a,
	0x0a, 0x19, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3c, 0x0a, 0x09, 0x6e,
	0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4e,
	0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x42, 0x04, 0xa8, 0xe1, 0x1c, 0x00, 0x52, 0x09,
	0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x3a, 0x0f, 0xaa, 0xe1, 0x1c, 0x0b, 0x0a,
	0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x22, 0x9e, 0x03, 0x0a, 0x05, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09,
	0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x69, 0x6e,
	0x76, 0x6f, 0x6c, 0x76, 0x65, 0x64, 0x5f, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x69, 0x6e, 0x76, 0x6f, 0x6c, 0x76,
	0x65, 0x64, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2d, 0x0a, 0x04,
	0x6b, 0x69, 0x6e, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x63, 0x6c, 0x75,
	0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x4b, 0x69, 0x6e, 0x64, 0x52, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x69, 0x6c,
	0x6c, 0x69, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x69, 0x6c, 0x6c, 0x69, 0x73, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x32, 0x0a, 0x15, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x69, 0x6c, 0x6c, 0x69, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x13, 0x6c, 0x61, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d,
	0x69, 0x6c, 0x6c, 0x69, 0x73, 0x12, 0x34, 0x0a, 0x16, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x6d, 0x69, 0x6c, 0x6c, 0x69, 0x73, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x14, 0x66, 0x69, 0x72, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x4d, 0x69, 0x6c, 0x6c, 0x69, 0x73, 0x22, 0xad, 0x02, 0x0a, 0x11,
	0x4c, 0x69, 0x73, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x25, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x09, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x12, 0x21, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x20, 0x01, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x09, 0x6e,
	0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x12, 0x28, 0x0a, 0x0b, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01,
	0x52, 0x0a, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x37, 0x0a, 0x04,
	0x6b, 0x69, 0x6e, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x63, 0x6c, 0x75,
	0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x4b, 0x69, 0x6e, 0x64, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x04, 0x6b, 0x69, 0x6e, 0x64, 0x3a, 0x44, 0xb2, 0xe1, 0x1c, 0x40, 0x0a, 0x3e, 0x0a, 0x17, 0x63,
	0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x61, 0x6d,
	0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x23, 0x7b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x7d, 0x2f, 0x7b, 0x6f,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x22, 0x56, 0x0a, 0x12, 0x4c,
	0x69, 0x73, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x32, 0x0a, 0x06, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x42, 0x04, 0xa8, 0xe1, 0x1c, 0x00, 0x52, 0x06, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x73, 0x3a, 0x0c, 0xaa, 0xe1, 0x1c, 0x08, 0x0a, 0x06, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x73, 0x22, 0x62, 0x0a, 0x0e, 0x4e, 0x75, 0x6c, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x30, 0x0a, 0x04, 0x6e, 0x75, 0x6c, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4e, 0x75, 0x6c, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x48,
	0x00, 0x52, 0x04, 0x6e, 0x75, 0x6c, 0x6c, 0x12, 0x16, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x42,
	0x06, 0x0a, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x22, 0x98, 0x03, 0x0a, 0x18, 0x45, 0x78, 0x70, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x73, 0x12, 0x59, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x2e, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x9a, 0x01,
	0x06, 0x22, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12,
	0x68, 0x0a, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x2e, 0x41, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x0c,
	0xfa, 0x42, 0x09, 0x9a, 0x01, 0x06, 0x22, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x0b, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x58, 0x0a, 0x0b, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x33, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6c, 0x75, 0x74,
	0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x75, 0x6c, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0x5d, 0x0a, 0x10, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x33, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63,
	0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x75, 0x6c, 0x6c, 0x61, 0x62, 0x6c,
	0x65, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0xc2, 0x02, 0x0a, 0x10, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x4d, 0x65, 0x74,
	0x61, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x51, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68,
	0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x4d, 0x65,
	0x74, 0x61, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x9a, 0x01, 0x06, 0x22, 0x04, 0x72, 0x02,
	0x20, 0x01, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x60, 0x0a, 0x0b, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x30, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73,
	0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x9a, 0x01, 0x06, 0x22, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52,
	0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x39, 0x0a, 0x0b,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3e, 0x0a, 0x10, 0x41, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x66, 0x0a, 0x16, 0x52, 0x65, 0x6d, 0x6f, 0x76,
	0x65, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x73, 0x12, 0x20, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x18, 0x01, 0x52, 0x06, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x12, 0x2a, 0x0a, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02,
	0x18, 0x01, 0x52, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22,
	0x88, 0x01, 0x0a, 0x04, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x24, 0x0a, 0x0d, 0x75, 0x6e, 0x73, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x75,
	0x6e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x3a, 0x2c, 0xb2, 0xe1,
	0x1c, 0x28, 0x0a, 0x26, 0x0a, 0x12, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x7b, 0x63, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x22, 0xaa, 0x01, 0x0a, 0x13, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x25, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x09,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x12, 0x21, 0x0a, 0x07, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x20, 0x01, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x20, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x3a, 0x2c, 0xb2, 0xe1, 0x1c, 0x28, 0x0a,
	0x26, 0x0a, 0x12, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x7b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x7d,
	0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x22, 0x51, 0x0a, 0x14, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x2d, 0x0a, 0x04, 0x6e, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e,
	0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f,
	0x64, 0x65, 0x42, 0x04, 0xa8, 0xe1, 0x1c, 0x00, 0x52, 0x04, 0x6e, 0x6f, 0x64, 0x65, 0x3a, 0x0a,
	0xaa, 0xe1, 0x1c, 0x06, 0x0a, 0x04, 0x6e, 0x6f, 0x64, 0x65, 0x22, 0xce, 0x01, 0x0a, 0x11, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x25, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20, 0x01, 0x52, 0x09, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x73, 0x65, 0x74, 0x12, 0x21, 0x0a, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20,
	0x01, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x20,
	0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x75, 0x6e, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d,
	0x75, 0x6e, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x3a, 0x2c, 0xb2,
	0xe1, 0x1c, 0x28, 0x0a, 0x26, 0x0a, 0x12, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x7b, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x7d, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x22, 0x14, 0x0a, 0x12, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2a, 0x33, 0x0a, 0x0a, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x4b, 0x69, 0x6e, 0x64, 0x12,
	0x0f, 0x0a, 0x0b, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x01, 0x12, 0x07, 0x0a,
	0x03, 0x50, 0x4f, 0x44, 0x10, 0x02, 0x2a, 0x45, 0x0a, 0x09, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x4e, 0x4f, 0x52,
	0x4d, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x57, 0x41, 0x52, 0x4e, 0x49, 0x4e, 0x47,
	0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x03, 0x32, 0xa1, 0x22,
	0x0a, 0x06, 0x4b, 0x38, 0x73, 0x41, 0x50, 0x49, 0x12, 0x7a, 0x0a, 0x0b, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x50, 0x6f, 0x64, 0x12, 0x21, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68,
	0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x50, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x63, 0x6c, 0x75,
	0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x50, 0x6f, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24,
	0xaa, 0xe1, 0x1c, 0x02, 0x08, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18, 0x3a, 0x01, 0x2a, 0x22,
	0x13, 0x2f, 0x76, 0x31, 0x2f, 0x6b, 0x38, 0x73, 0x2f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x50, 0x6f, 0x64, 0x12, 0x6e, 0x0a, 0x08, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6f, 0x64, 0x73,
	0x12, 0x1e, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6f, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1f, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6f, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x21, 0xaa, 0xe1, 0x1c, 0x02, 0x08, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15, 0x3a,
	0x01, 0x2a, 0x22, 0x10, 0x2f, 0x76, 0x31, 0x2f, 0x6b, 0x38, 0x73, 0x2f, 0x6c, 0x69, 0x73, 0x74,
	0x50, 0x6f, 0x64, 0x73, 0x12, 0x72, 0x0a, 0x09, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x6f,
	0x64, 0x12, 0x1f, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x20, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x6f, 0x64, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x22, 0xaa, 0xe1, 0x1c, 0x02, 0x08, 0x04, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x16, 0x3a, 0x01, 0x2a, 0x22, 0x11, 0x2f, 0x76, 0x31, 0x2f, 0x6b, 0x38, 0x73, 0x2f, 0x64,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x6f, 0x64, 0x12, 0x72, 0x0a, 0x09, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x50, 0x6f, 0x64, 0x12, 0x1f, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b,
	0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e,
	0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x22, 0xaa, 0xe1, 0x1c, 0x02, 0x08, 0x03,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x3a, 0x01, 0x2a, 0x22, 0x11, 0x2f, 0x76, 0x31, 0x2f, 0x6b,
	0x38, 0x73, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x64, 0x12, 0x76, 0x0a, 0x0a,
	0x47, 0x65, 0x74, 0x50, 0x6f, 0x64, 0x4c, 0x6f, 0x67, 0x73, 0x12, 0x20, 0x2e, 0x63, 0x6c, 0x75,
	0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6f,
	0x64, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x63,
	0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x50, 0x6f, 0x64, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x23, 0xaa, 0xe1, 0x1c, 0x02, 0x08, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x17, 0x3a, 0x01, 0x2a,
	0x22, 0x12, 0x2f, 0x76, 0x31, 0x2f, 0x6b, 0x38, 0x73, 0x2f, 0x67, 0x65, 0x74, 0x50, 0x6f, 0x64,
	0x4c, 0x6f, 0x67, 0x73, 0x12, 0x72, 0x0a, 0x09, 0x52, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x48, 0x50,
	0x41, 0x12, 0x1f, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x48, 0x50, 0x41, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x20, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x48, 0x50, 0x41, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x22, 0xaa, 0xe1, 0x1c, 0x02, 0x08, 0x03, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x16, 0x3a, 0x01, 0x2a, 0x22, 0x11, 0x2f, 0x76, 0x31, 0x2f, 0x6b, 0x38, 0x73, 0x2f, 0x72,
	0x65, 0x73, 0x69, 0x7a, 0x65, 0x48, 0x50, 0x41, 0x12, 0x72, 0x0a, 0x09, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x48, 0x50, 0x41, 0x12, 0x1f, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b,
	0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x48, 0x50, 0x41, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e,
	0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x48, 0x50, 0x41,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x22, 0xaa, 0xe1, 0x1c, 0x02, 0x08, 0x04,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x3a, 0x01, 0x2a, 0x22, 0x11, 0x2f, 0x76, 0x31, 0x2f, 0x6b,
	0x38, 0x73, 0x2f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x48, 0x50, 0x41, 0x12, 0x96, 0x01, 0x0a,
	0x12, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x12, 0x28, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x44, 0x65, 0x70, 0x6c,
	0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e,
	0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2b, 0xaa, 0xe1, 0x1c, 0x02, 0x08, 0x02,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x3a, 0x01, 0x2a, 0x22, 0x1a, 0x2f, 0x76, 0x31, 0x2f, 0x6b,
	0x38, 0x73, 0x2f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x44, 0x65, 0x70, 0x6c, 0x6f,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x8a, 0x01, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x65,
	0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x25, 0x2e, 0x63, 0x6c, 0x75, 0x74,
	0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x65,
	0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x26, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x28, 0xaa, 0xe1, 0x1c, 0x02, 0x08, 0x02,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x3a, 0x01, 0x2a, 0x22, 0x17, 0x2f, 0x76, 0x31, 0x2f, 0x6b,
	0x38, 0x73, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x12, 0x8e, 0x01, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x26, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68,
	0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65,
	0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x27, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x29, 0xaa, 0xe1, 0x1c, 0x02, 0x08, 0x03,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x3a, 0x01, 0x2a, 0x22, 0x18, 0x2f, 0x76, 0x31, 0x2f, 0x6b,
	0x38, 0x73, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x12, 0x8e, 0x01, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x65,
	0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x26, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63,
	0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44,
	0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x27, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x29, 0xaa, 0xe1, 0x1c, 0x02, 0x08,
	0x04, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x3a, 0x01, 0x2a, 0x22, 0x18, 0x2f, 0x76, 0x31, 0x2f,
	0x6b, 0x38, 0x73, 0x2f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x12, 0x8a, 0x01, 0x0a, 0x0f, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x25, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63,
	0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x26, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x28, 0xaa, 0xe1, 0x1c, 0x02, 0x08, 0x02, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x3a, 0x01, 0x2a, 0x22, 0x17, 0x2f, 0x76, 0x31, 0x2f, 0x6b, 0x38,
	0x73, 0x2f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x7e, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x12, 0x22, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b,
	0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x25, 0xaa, 0xe1, 0x1c, 0x02,
	0x08, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19, 0x3a, 0x01, 0x2a, 0x22, 0x14, 0x2f, 0x76, 0x31,
	0x2f, 0x6b, 0x38, 0x73, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x12, 0x82, 0x01, 0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x23, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63,
	0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x26,
	0xaa, 0xe1, 0x1c, 0x02, 0x08, 0x04, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x3a, 0x01, 0x2a, 0x22,
	0x15, 0x2f, 0x76, 0x31, 0x2f, 0x6b, 0x38, 0x73, 0x2f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x9a, 0x01, 0x0a, 0x13, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x53, 0x65, 0x74, 0x12, 0x29,
	0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x53,
	0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x63, 0x6c, 0x75, 0x74,
	0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x53, 0x65, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2c, 0xaa, 0xe1, 0x1c, 0x02, 0x08, 0x02, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x20, 0x3a, 0x01, 0x2a, 0x22, 0x1b, 0x2f, 0x76, 0x31, 0x2f, 0x6b, 0x38, 0x73, 0x2f,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c,
	0x53, 0x65, 0x74, 0x12, 0x8e, 0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x66, 0x75, 0x6c, 0x53, 0x65, 0x74, 0x73, 0x12, 0x26, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63,
	0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x66, 0x75, 0x6c, 0x53, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x27, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x53, 0x65, 0x74,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x29, 0xaa, 0xe1, 0x1c, 0x02, 0x08,
	0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x3a, 0x01, 0x2a, 0x22, 0x18, 0x2f, 0x76, 0x31, 0x2f,
	0x6b, 0x38, 0x73, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c,
	0x53, 0x65, 0x74, 0x73, 0x12, 0x92, 0x01, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x53, 0x65, 0x74, 0x12, 0x27, 0x2e, 0x63, 0x6c, 0x75,
	0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x53, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x66,
	0x75, 0x6c, 0x53, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2a, 0xaa,
	0xe1, 0x1c, 0x02, 0x08, 0x03, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x3a, 0x01, 0x2a, 0x22, 0x19,
	0x2f, 0x76, 0x31, 0x2f, 0x6b, 0x38, 0x73, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x53, 0x65, 0x74, 0x12, 0x92, 0x01, 0x0a, 0x11, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x53, 0x65, 0x74, 0x12,
	0x27, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x53, 0x65,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63,
	0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x53, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x2a, 0xaa, 0xe1, 0x1c, 0x02, 0x08, 0x04, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e,
	0x3a, 0x01, 0x2a, 0x22, 0x19, 0x2f, 0x76, 0x31, 0x2f, 0x6b, 0x38, 0x73, 0x2f, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x53, 0x65, 0x74, 0x12, 0x8a,
	0x01, 0x0a, 0x0f, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x43, 0x72, 0x6f, 0x6e, 0x4a,
	0x6f, 0x62, 0x12, 0x25, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x43, 0x72, 0x6f, 0x6e, 0x4a,
	0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x63, 0x6c, 0x75, 0x74,
	0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x43, 0x72, 0x6f, 0x6e, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x28, 0xaa, 0xe1, 0x1c, 0x02, 0x08, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x3a,
	0x01, 0x2a, 0x22, 0x17, 0x2f, 0x76, 0x31, 0x2f, 0x6b, 0x38, 0x73, 0x2f, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x43, 0x72, 0x6f, 0x6e, 0x4a, 0x6f, 0x62, 0x12, 0x7e, 0x0a, 0x0c, 0x4c,
	0x69, 0x73, 0x74, 0x43, 0x72, 0x6f, 0x6e, 0x4a, 0x6f, 0x62, 0x73, 0x12, 0x22, 0x2e, 0x63, 0x6c,
	0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x43, 0x72, 0x6f, 0x6e, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x23, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x43, 0x72, 0x6f, 0x6e, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x25, 0xaa, 0xe1, 0x1c, 0x02, 0x08, 0x02, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x19, 0x3a, 0x01, 0x2a, 0x22, 0x14, 0x2f, 0x76, 0x31, 0x2f, 0x6b, 0x38, 0x73, 0x2f, 0x6c,
	0x69, 0x73, 0x74, 0x43, 0x72, 0x6f, 0x6e, 0x4a, 0x6f, 0x62, 0x73, 0x12, 0x82, 0x01, 0x0a, 0x0d,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x72, 0x6f, 0x6e, 0x4a, 0x6f, 0x62, 0x12, 0x23, 0x2e,
	0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x43, 0x72, 0x6f, 0x6e, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x24, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x72, 0x6f, 0x6e, 0x4a, 0x6f, 0x62,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x26, 0xaa, 0xe1, 0x1c, 0x02, 0x08, 0x04,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x3a, 0x01, 0x2a, 0x22, 0x15, 0x2f, 0x76, 0x31, 0x2f, 0x6b,
	0x38, 0x73, 0x2f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x72, 0x6f, 0x6e, 0x4a, 0x6f, 0x62,
	0x12, 0x86, 0x01, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d,
	0x61, 0x70, 0x73, 0x12, 0x24, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61,
	0x70, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x63, 0x6c, 0x75, 0x74,
	0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x27, 0xaa, 0xe1, 0x1c, 0x02, 0x08, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x3a, 0x01,
	0x2a, 0x22, 0x16, 0x2f, 0x76, 0x31, 0x2f, 0x6b, 0x38, 0x73, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x73, 0x12, 0x92, 0x01, 0x0a, 0x11, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x12,
	0x27, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61,
	0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63,
	0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x2a, 0xaa, 0xe1, 0x1c, 0x02, 0x08, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e,
	0x3a, 0x01, 0x2a, 0x22, 0x19, 0x2f, 0x76, 0x31, 0x2f, 0x6b, 0x38, 0x73, 0x2f, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x12, 0x8a,
	0x01, 0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d,
	0x61, 0x70, 0x12, 0x25, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d,
	0x61, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x63, 0x6c, 0x75, 0x74,
	0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x28, 0xaa, 0xe1, 0x1c, 0x02, 0x08, 0x04, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x3a,
	0x01, 0x2a, 0x22, 0x17, 0x2f, 0x76, 0x31, 0x2f, 0x6b, 0x38, 0x73, 0x2f, 0x64, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x12, 0x7a, 0x0a, 0x0b, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4a, 0x6f, 0x62, 0x12, 0x21, 0x2e, 0x63, 0x6c, 0x75,
	0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e,
	0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x24, 0xaa, 0xe1, 0x1c, 0x02, 0x08, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18, 0x3a,
	0x01, 0x2a, 0x22, 0x13, 0x2f, 0x76, 0x31, 0x2f, 0x6b, 0x38, 0x73, 0x2f, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x4a, 0x6f, 0x62, 0x12, 0x6e, 0x0a, 0x08, 0x4c, 0x69, 0x73, 0x74, 0x4a,
	0x6f, 0x62, 0x73, 0x12, 0x1e, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f, 0x62, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x21, 0xaa, 0xe1, 0x1c, 0x02, 0x08, 0x02, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x15, 0x3a, 0x01, 0x2a, 0x22, 0x10, 0x2f, 0x76, 0x31, 0x2f, 0x6b, 0x38, 0x73, 0x2f, 0x6c,
	0x69, 0x73, 0x74, 0x4a, 0x6f, 0x62, 0x73, 0x12, 0x72, 0x0a, 0x09, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x4a, 0x6f, 0x62, 0x12, 0x1f, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b,
	0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x22, 0xaa, 0xe1, 0x1c, 0x02, 0x08, 0x04, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x16, 0x3a, 0x01, 0x2a, 0x22, 0x11, 0x2f, 0x76, 0x31, 0x2f, 0x6b, 0x38,
	0x73, 0x2f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x12, 0x72, 0x0a, 0x09, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x12, 0x1f, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63,
	0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4a,
	0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x63, 0x6c, 0x75, 0x74,
	0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x22, 0xaa, 0xe1, 0x1c,
	0x02, 0x08, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x3a, 0x01, 0x2a, 0x22, 0x11, 0x2f, 0x76,
	0x31, 0x2f, 0x6b, 0x38, 0x73, 0x2f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x12,
	0x92, 0x01, 0x0a, 0x11, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x27, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b,
	0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28,
	0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2a, 0xaa, 0xe1, 0x1c, 0x02, 0x08, 0x02,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x3a, 0x01, 0x2a, 0x22, 0x19, 0x2f, 0x76, 0x31, 0x2f, 0x6b,
	0x38, 0x73, 0x2f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x12, 0x76, 0x0a, 0x0a, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x73, 0x12, 0x20, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x23, 0xaa, 0xe1, 0x1c, 0x02, 0x08, 0x02, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x17, 0x3a, 0x01, 0x2a, 0x22, 0x12, 0x2f, 0x76, 0x31, 0x2f, 0x6b, 0x38,
	0x73, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x7e, 0x0a, 0x0c,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x22, 0x2e, 0x63,
	0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x23, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x25, 0xaa, 0xe1, 0x1c, 0x02, 0x08, 0x02, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x19, 0x3a, 0x01, 0x2a, 0x22, 0x14, 0x2f, 0x76, 0x31, 0x2f, 0x6b, 0x38, 0x73, 0x2f,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x76, 0x0a, 0x0a,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x20, 0x2e, 0x63, 0x6c, 0x75,
	0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x63,
	0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x23, 0xaa, 0xe1, 0x1c, 0x02, 0x08, 0x03, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x17, 0x3a, 0x01, 0x2a,
	0x22, 0x12, 0x2f, 0x76, 0x31, 0x2f, 0x6b, 0x38, 0x73, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x4e, 0x6f, 0x64, 0x65, 0x12, 0x9a, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x29, 0x2e, 0x63,
	0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68,
	0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x2c, 0xaa, 0xe1, 0x1c, 0x02, 0x08, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x20, 0x3a, 0x01, 0x2a, 0x22, 0x1b, 0x2f, 0x76, 0x31, 0x2f, 0x6b, 0x38, 0x73, 0x2f, 0x6c, 0x69,
	0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x73, 0x42, 0x31, 0x5a, 0x2f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x6c, 0x79, 0x66, 0x74, 0x2f, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2f, 0x62, 0x61, 0x63, 0x6b,
	0x65, 0x6e, 0x64, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6b, 0x38, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x6b,
	0x38, 0x73, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_k8s_v1_k8s_proto_rawDescOnce sync.Once
	file_k8s_v1_k8s_proto_rawDescData = file_k8s_v1_k8s_proto_rawDesc
)

func file_k8s_v1_k8s_proto_rawDescGZIP() []byte {
	file_k8s_v1_k8s_proto_rawDescOnce.Do(func() {
		file_k8s_v1_k8s_proto_rawDescData = protoimpl.X.CompressGZIP(file_k8s_v1_k8s_proto_rawDescData)
	})
	return file_k8s_v1_k8s_proto_rawDescData
}

var file_k8s_v1_k8s_proto_enumTypes = make([]protoimpl.EnumInfo, 10)
var file_k8s_v1_k8s_proto_msgTypes = make([]protoimpl.MessageInfo, 145)
var file_k8s_v1_k8s_proto_goTypes = []interface{}{
	(ObjectKind)(0),          // 0: clutch.k8s.v1.ObjectKind
	(EventType)(0),           // 1: clutch.k8s.v1.EventType
	(Container_State)(0),     // 2: clutch.k8s.v1.Container.State
	(PodCondition_Type)(0),   // 3: clutch.k8s.v1.PodCondition.Type
	(PodCondition_Status)(0), // 4: clutch.k8s.v1.PodCondition.Status
	(Pod_State)(0),           // 5: clutch.k8s.v1.Pod.State
	(Deployment_DeploymentStatus_Condition_Type)(0),            // 6: clutch.k8s.v1.Deployment.DeploymentStatus.Condition.Type
	(Deployment_DeploymentStatus_Condition_ConditionStatus)(0), // 7: clutch.k8s.v1.Deployment.DeploymentStatus.Condition.ConditionStatus
	(Service_Type)(0),                                 // 8: clutch.k8s.v1.Service.Type
	(CronJob_ConcurrencyPolicy)(0),                    // 9: clutch.k8s.v1.CronJob.ConcurrencyPolicy
	(*ListNamespaceEventsRequest)(nil),                // 10: clutch.k8s.v1.ListNamespaceEventsRequest
	(*ListNamespaceEventsResponse)(nil),               // 11: clutch.k8s.v1.ListNamespaceEventsResponse
	(*DescribePodRequest)(nil),                        // 12: clutch.k8s.v1.DescribePodRequest
	(*DescribePodResponse)(nil),                       // 13: clutch.k8s.v1.DescribePodResponse
	(*Container)(nil),                                 // 14: clutch.k8s.v1.Container
	(*StateWaiting)(nil),                              // 15: clutch.k8s.v1.StateWaiting
	(*StateRunning)(nil),                              // 16: clutch.k8s.v1.StateRunning
	(*StateTerminated)(nil),                           // 17: clutch.k8s.v1.StateTerminated
	(*PodCondition)(nil),                              // 18: clutch.k8s.v1.PodCondition
	(*Pod)(nil),                                       // 19: clutch.k8s.v1.Pod
	(*ListOptions)(nil),                               // 20: clutch.k8s.v1.ListOptions
	(*ListPodsRequest)(nil),                           // 21: clutch.k8s.v1.ListPodsRequest
	(*ListPodsResponse)(nil),                          // 22: clutch.k8s.v1.ListPodsResponse
	(*DeletePodRequest)(nil),                          // 23: clutch.k8s.v1.DeletePodRequest
	(*DeletePodResponse)(nil),                         // 24: clutch.k8s.v1.DeletePodResponse
	(*UpdatePodRequest)(nil),                          // 25: clutch.k8s.v1.UpdatePodRequest
	(*UpdatePodResponse)(nil),                         // 26: clutch.k8s.v1.UpdatePodResponse
	(*GetPodLogsRequest)(nil),                         // 27: clutch.k8s.v1.GetPodLogsRequest
	(*PodLogsOptions)(nil),                            // 28: clutch.k8s.v1.PodLogsOptions
	(*GetPodLogsResponse)(nil),                        // 29: clutch.k8s.v1.GetPodLogsResponse
	(*PodLogLine)(nil),                                // 30: clutch.k8s.v1.PodLogLine
	(*HPA)(nil),                                       // 31: clutch.k8s.v1.HPA
	(*ResizeHPARequest)(nil),                          // 32: clutch.k8s.v1.ResizeHPARequest
	(*ResizeHPAResponse)(nil),                         // 33: clutch.k8s.v1.ResizeHPAResponse
	(*DeleteHPARequest)(nil),                          // 34: clutch.k8s.v1.DeleteHPARequest
	(*DeleteHPAResponse)(nil),                         // 35: clutch.k8s.v1.DeleteHPAResponse
	(*ExecAction)(nil),                                // 36: clutch.k8s.v1.ExecAction
	(*HTTPGetAction)(nil),                             // 37: clutch.k8s.v1.HTTPGetAction
	(*HTTPHeader)(nil),                                // 38: clutch.k8s.v1.HTTPHeader
	(*TCPSocketAction)(nil),                           // 39: clutch.k8s.v1.TCPSocketAction
	(*GRPCAction)(nil),                                // 40: clutch.k8s.v1.GRPCAction
	(*Probe)(nil),                                     // 41: clutch.k8s.v1.Probe
	(*Deployment)(nil),                                // 42: clutch.k8s.v1.Deployment
	(*DescribeDeploymentRequest)(nil),                 // 43: clutch.k8s.v1.DescribeDeploymentRequest
	(*DescribeDeploymentResponse)(nil),                // 44: clutch.k8s.v1.DescribeDeploymentResponse
	(*ListDeploymentsRequest)(nil),                    // 45: clutch.k8s.v1.ListDeploymentsRequest
	(*ListDeploymentsResponse)(nil),                   // 46: clutch.k8s.v1.ListDeploymentsResponse
	(*UpdateDeploymentRequest)(nil),                   // 47: clutch.k8s.v1.UpdateDeploymentRequest
	(*UpdateDeploymentResponse)(nil),                  // 48: clutch.k8s.v1.UpdateDeploymentResponse
	(*DeleteDeploymentRequest)(nil),                   // 49: clutch.k8s.v1.DeleteDeploymentRequest
	(*DeleteDeploymentResponse)(nil),                  // 50: clutch.k8s.v1.DeleteDeploymentResponse
	(*StatefulSet)(nil),                               // 51: clutch.k8s.v1.StatefulSet
	(*DescribeStatefulSetRequest)(nil),                // 52: clutch.k8s.v1.DescribeStatefulSetRequest
	(*DescribeStatefulSetResponse)(nil),               // 53: clutch.k8s.v1.DescribeStatefulSetResponse
	(*ListStatefulSetsRequest)(nil),                   // 54: clutch.k8s.v1.ListStatefulSetsRequest
	(*ListStatefulSetsResponse)(nil),                  // 55: clutch.k8s.v1.ListStatefulSetsResponse
	(*DeleteStatefulSetRequest)(nil),                  // 56: clutch.k8s.v1.DeleteStatefulSetRequest
	(*DeleteStatefulSetResponse)(nil),                 // 57: clutch.k8s.v1.DeleteStatefulSetResponse
	(*UpdateStatefulSetRequest)(nil),                  // 58: clutch.k8s.v1.UpdateStatefulSetRequest
	(*UpdateStatefulSetResponse)(nil),                 // 59: clutch.k8s.v1.UpdateStatefulSetResponse
	(*Service)(nil),                                   // 60: clutch.k8s.v1.Service
	(*DescribeServiceRequest)(nil),                    // 61: clutch.k8s.v1.DescribeServiceRequest
	(*DescribeServiceResponse)(nil),                   // 62: clutch.k8s.v1.DescribeServiceResponse
	(*ListServicesRequest)(nil),                       // 63: clutch.k8s.v1.ListServicesRequest
	(*ListServicesResponse)(nil),                      // 64: clutch.k8s.v1.ListServicesResponse
	(*DeleteServiceRequest)(nil),                      // 65: clutch.k8s.v1.DeleteServiceRequest
	(*DeleteServiceResponse)(nil),                     // 66: clutch.k8s.v1.DeleteServiceResponse
	(*CronJob)(nil),                                   // 67: clutch.k8s.v1.CronJob
	(*DescribeCronJobRequest)(nil),                    // 68: clutch.k8s.v1.DescribeCronJobRequest
	(*DescribeCronJobResponse)(nil),                   // 69: clutch.k8s.v1.DescribeCronJobResponse
	(*ListCronJobsRequest)(nil),                       // 70: clutch.k8s.v1.ListCronJobsRequest
	(*ListCronJobsResponse)(nil),                      // 71: clutch.k8s.v1.ListCronJobsResponse
	(*DeleteCronJobRequest)(nil),                      // 72: clutch.k8s.v1.DeleteCronJobRequest
	(*DeleteCronJobResponse)(nil),                     // 73: clutch.k8s.v1.DeleteCronJobResponse
	(*ConfigMap)(nil),                                 // 74: clutch.k8s.v1.ConfigMap
	(*ListConfigMapsRequest)(nil),                     // 75: clutch.k8s.v1.ListConfigMapsRequest
	(*ListConfigMapsResponse)(nil),                    // 76: clutch.k8s.v1.ListConfigMapsResponse
	(*DescribeConfigMapRequest)(nil),                  // 77: clutch.k8s.v1.DescribeConfigMapRequest
	(*DescribeConfigMapResponse)(nil),                 // 78: clutch.k8s.v1.DescribeConfigMapResponse
	(*DeleteConfigMapRequest)(nil),                    // 79: clutch.k8s.v1.DeleteConfigMapRequest
	(*DeleteConfigMapResponse)(nil),                   // 80: clutch.k8s.v1.DeleteConfigMapResponse
	(*Job)(nil),                                       // 81: clutch.k8s.v1.Job
	(*DescribeJobRequest)(nil),                        // 82: clutch.k8s.v1.DescribeJobRequest
	(*DescribeJobResponse)(nil),                       // 83: clutch.k8s.v1.DescribeJobResponse
	(*ListJobsRequest)(nil),                           // 84: clutch.k8s.v1.ListJobsRequest
	(*ListJobsResponse)(nil),                          // 85: clutch.k8s.v1.ListJobsResponse
	(*DeleteJobRequest)(nil),                          // 86: clutch.k8s.v1.DeleteJobRequest
	(*DeleteJobResponse)(nil),                         // 87: clutch.k8s.v1.DeleteJobResponse
	(*JobConfig)(nil),                                 // 88: clutch.k8s.v1.JobConfig
	(*CreateJobRequest)(nil),                          // 89: clutch.k8s.v1.CreateJobRequest
	(*CreateJobResponse)(nil),                         // 90: clutch.k8s.v1.CreateJobResponse
	(*Namespace)(nil),                                 // 91: clutch.k8s.v1.Namespace
	(*DescribeNamespaceRequest)(nil),                  // 92: clutch.k8s.v1.DescribeNamespaceRequest
	(*DescribeNamespaceResponse)(nil),                 // 93: clutch.k8s.v1.DescribeNamespaceResponse
	(*Event)(nil),                                     // 94: clutch.k8s.v1.Event
	(*ListEventsRequest)(nil),                         // 95: clutch.k8s.v1.ListEventsRequest
	(*ListEventsResponse)(nil),                        // 96: clutch.k8s.v1.ListEventsResponse
	(*NullableString)(nil),                            // 97: clutch.k8s.v1.NullableString
	(*ExpectedObjectMetaFields)(nil),                  // 98: clutch.k8s.v1.ExpectedObjectMetaFields
	(*ObjectMetaFields)(nil),                          // 99: clutch.k8s.v1.ObjectMetaFields
	(*RemoveObjectMetaFields)(nil),                    // 100: clutch.k8s.v1.RemoveObjectMetaFields
	(*Node)(nil),                                      // 101: clutch.k8s.v1.Node
	(*DescribeNodeRequest)(nil),                       // 102: clutch.k8s.v1.DescribeNodeRequest
	(*DescribeNodeResponse)(nil),                      // 103: clutch.k8s.v1.DescribeNodeResponse
	(*UpdateNodeRequest)(nil),                         // 104: clutch.k8s.v1.UpdateNodeRequest
	(*UpdateNodeResponse)(nil),                        // 105: clutch.k8s.v1.UpdateNodeResponse
	nil,                                               // 106: clutch.k8s.v1.Pod.LabelsEntry
	nil,                                               // 107: clutch.k8s.v1.Pod.AnnotationsEntry
	nil,                                               // 108: clutch.k8s.v1.ListOptions.LabelsEntry
	(*HPA_Sizing)(nil),                                // 109: clutch.k8s.v1.HPA.Sizing
	nil,                                               // 110: clutch.k8s.v1.HPA.LabelsEntry
	nil,                                               // 111: clutch.k8s.v1.HPA.AnnotationsEntry
	(*ResizeHPARequest_Sizing)(nil),                   // 112: clutch.k8s.v1.ResizeHPARequest.Sizing
	nil,                                               // 113: clutch.k8s.v1.Deployment.LabelsEntry
	nil,                                               // 114: clutch.k8s.v1.Deployment.AnnotationsEntry
	(*Deployment_DeploymentStatus)(nil),               // 115: clutch.k8s.v1.Deployment.DeploymentStatus
	(*Deployment_DeploymentSpec)(nil),                 // 116: clutch.k8s.v1.Deployment.DeploymentSpec
	(*Deployment_DeploymentStatus_Condition)(nil),     // 117: clutch.k8s.v1.Deployment.DeploymentStatus.Condition
	(*Deployment_DeploymentSpec_PodTemplateSpec)(nil), // 118: clutch.k8s.v1.Deployment.DeploymentSpec.PodTemplateSpec
	(*Deployment_DeploymentSpec_PodTemplateSpec_PodSpec)(nil),                                // 119: clutch.k8s.v1.Deployment.DeploymentSpec.PodTemplateSpec.PodSpec
	(*Deployment_DeploymentSpec_PodTemplateSpec_PodSpec_Container)(nil),                      // 120: clutch.k8s.v1.Deployment.DeploymentSpec.PodTemplateSpec.PodSpec.Container
	(*Deployment_DeploymentSpec_PodTemplateSpec_PodSpec_Container_ResourceRequirements)(nil), // 121: clutch.k8s.v1.Deployment.DeploymentSpec.PodTemplateSpec.PodSpec.Container.ResourceRequirements
	nil,                                    // 122: clutch.k8s.v1.Deployment.DeploymentSpec.PodTemplateSpec.PodSpec.Container.ResourceRequirements.LimitsEntry
	nil,                                    // 123: clutch.k8s.v1.Deployment.DeploymentSpec.PodTemplateSpec.PodSpec.Container.ResourceRequirements.RequestsEntry
	(*UpdateDeploymentRequest_Fields)(nil), // 124: clutch.k8s.v1.UpdateDeploymentRequest.Fields
	nil,                                    // 125: clutch.k8s.v1.UpdateDeploymentRequest.Fields.LabelsEntry
	nil,                                    // 126: clutch.k8s.v1.UpdateDeploymentRequest.Fields.AnnotationsEntry
	(*UpdateDeploymentRequest_Fields_ContainerResources)(nil),                      // 127: clutch.k8s.v1.UpdateDeploymentRequest.Fields.ContainerResources
	(*UpdateDeploymentRequest_Fields_ContainerProbes)(nil),                         // 128: clutch.k8s.v1.UpdateDeploymentRequest.Fields.ContainerProbes
	(*UpdateDeploymentRequest_Fields_ContainerResources_ResourceRequirements)(nil), // 129: clutch.k8s.v1.UpdateDeploymentRequest.Fields.ContainerResources.ResourceRequirements
	nil,                                     // 130: clutch.k8s.v1.UpdateDeploymentRequest.Fields.ContainerResources.ResourceRequirements.LimitsEntry
	nil,                                     // 131: clutch.k8s.v1.UpdateDeploymentRequest.Fields.ContainerResources.ResourceRequirements.RequestsEntry
	nil,                                     // 132: clutch.k8s.v1.StatefulSet.LabelsEntry
	nil,                                     // 133: clutch.k8s.v1.StatefulSet.AnnotationsEntry
	(*StatefulSet_Status)(nil),              // 134: clutch.k8s.v1.StatefulSet.Status
	(*UpdateStatefulSetRequest_Fields)(nil), // 135: clutch.k8s.v1.UpdateStatefulSetRequest.Fields
	nil,                                     // 136: clutch.k8s.v1.UpdateStatefulSetRequest.Fields.LabelsEntry
	nil,                                     // 137: clutch.k8s.v1.UpdateStatefulSetRequest.Fields.AnnotationsEntry
	nil,                                     // 138: clutch.k8s.v1.Service.LabelsEntry
	nil,                                     // 139: clutch.k8s.v1.Service.AnnotationsEntry
	nil,                                     // 140: clutch.k8s.v1.Service.SelectorEntry
	nil,                                     // 141: clutch.k8s.v1.CronJob.LabelsEntry
	nil,                                     // 142: clutch.k8s.v1.CronJob.AnnotationsEntry
	nil,                                     // 143: clutch.k8s.v1.ConfigMap.AnnotationsEntry
	nil,                                     // 144: clutch.k8s.v1.ConfigMap.LabelsEntry
	nil,                                     // 145: clutch.k8s.v1.ConfigMap.DataEntry
	nil,                                     // 146: clutch.k8s.v1.ConfigMap.BinaryDataEntry
	nil,                                     // 147: clutch.k8s.v1.Job.AnnotationsEntry
	nil,                                     // 148: clutch.k8s.v1.Job.LabelsEntry
	nil,                                     // 149: clutch.k8s.v1.Namespace.AnnotationsEntry
	nil,                                     // 150: clutch.k8s.v1.Namespace.LabelsEntry
	nil,                                     // 151: clutch.k8s.v1.ExpectedObjectMetaFields.LabelsEntry
	nil,                                     // 152: clutch.k8s.v1.ExpectedObjectMetaFields.AnnotationsEntry
	nil,                                     // 153: clutch.k8s.v1.ObjectMetaFields.LabelsEntry
	nil,                                     // 154: clutch.k8s.v1.ObjectMetaFields.AnnotationsEntry
	(*timestamppb.Timestamp)(nil),           // 155: google.protobuf.Timestamp
	(*status.Status)(nil),                   // 156: google.rpc.Status
	(*wrapperspb.Int64Value)(nil),           // 157: google.protobuf.Int64Value
	(*structpb.Value)(nil),                  // 158: google.protobuf.Value
	(structpb.NullValue)(0),                 // 159: google.protobuf.NullValue
}
var file_k8s_v1_k8s_proto_depIdxs = []int32{
	1,   // 0: clutch.k8s.v1.ListNamespaceEventsRequest.types:type_name -> clutch.k8s.v1.EventType
	94,  // 1: clutch.k8s.v1.ListNamespaceEventsResponse.events:type_name -> clutch.k8s.v1.Event
	19,  // 2: clutch.k8s.v1.DescribePodResponse.pod:type_name -> clutch.k8s.v1.Pod
	2,   // 3: clutch.k8s.v1.Container.state:type_name -> clutch.k8s.v1.Container.State
	15,  // 4: clutch.k8s.v1.Container.state_waiting:type_name -> clutch.k8s.v1.StateWaiting
	16,  // 5: clutch.k8s.v1.Container.state_running:type_name -> clutch.k8s.v1.StateRunning
	17,  // 6: clutch.k8s.v1.Container.state_terminated:type_name -> clutch.k8s.v1.StateTerminated
	155, // 7: clutch.k8s.v1.StateRunning.start_time:type_name -> google.protobuf.Timestamp
	3,   // 8: clutch.k8s.v1.PodCondition.type:type_name -> clutch.k8s.v1.PodCondition.Type
	4,   // 9: clutch.k8s.v1.PodCondition.status:type_name -> clutch.k8s.v1.PodCondition.Status
	14,  // 10: clutch.k8s.v1.Pod.containers:type_name -> clutch.k8s.v1.Container
	5,   // 11: clutch.k8s.v1.Pod.state:type_name -> clutch.k8s.v1.Pod.State
	155, // 12: clutch.k8s.v1.Pod.start_time:type_name -> google.protobuf.Timestamp
	106, // 13: clutch.k8s.v1.Pod.labels:type_name -> clutch.k8s.v1.Pod.LabelsEntry
	107, // 14: clutch.k8s.v1.Pod.annotations:type_name -> clutch.k8s.v1.Pod.AnnotationsEntry
	18,  // 15: clutch.k8s.v1.Pod.pod_conditions:type_name -> clutch.k8s.v1.PodCondition
	14,  // 16: clutch.k8s.v1.Pod.init_containers:type_name -> clutch.k8s.v1.Container
	108, // 17: clutch.k8s.v1.ListOptions.labels:type_name -> clutch.k8s.v1.ListOptions.LabelsEntry
	20,  // 18: clutch.k8s.v1.ListPodsRequest.options:type_name -> clutch.k8s.v1.ListOptions
	19,  // 19: clutch.k8s.v1.ListPodsResponse.pods:type_name -> clutch.k8s.v1.Pod
	156, // 20: clutch.k8s.v1.ListPodsResponse.partial_failures:type_name -> google.rpc.Status
	98,  // 21: clutch.k8s.v1.UpdatePodRequest.expected_object_meta_fields:type_name -> clutch.k8s.v1.ExpectedObjectMetaFields
	99,  // 22: clutch.k8s.v1.UpdatePodRequest.object_meta_fields:type_name -> clutch.k8s.v1.ObjectMetaFields
	100, // 23: clutch.k8s.v1.UpdatePodRequest.remove_object_meta_fields:type_name -> clutch.k8s.v1.RemoveObjectMetaFields
	28,  // 24: clutch.k8s.v1.GetPodLogsRequest.options:type_name -> clutch.k8s.v1.PodLogsOptions
	30,  // 25: clutch.k8s.v1.GetPodLogsResponse.logs:type_name -> clutch.k8s.v1.PodLogLine
	109, // 26: clutch.k8s.v1.HPA.sizing:type_name -> clutch.k8s.v1.HPA.Sizing
	110, // 27: clutch.k8s.v1.HPA.labels:type_name -> clutch.k8s.v1.HPA.LabelsEntry
	111, // 28: clutch.k8s.v1.HPA.annotations:type_name -> clutch.k8s.v1.HPA.AnnotationsEntry
	112, // 29: clutch.k8s.v1.ResizeHPARequest.sizing:type_name -> clutch.k8s.v1.ResizeHPARequest.Sizing
	112, // 30: clutch.k8s.v1.ResizeHPARequest.current_sizing:type_name -> clutch.k8s.v1.ResizeHPARequest.Sizing
	38,  // 31: clutch.k8s.v1.HTTPGetAction.http_headers:type_name -> clutch.k8s.v1.HTTPHeader
	36,  // 32: clutch.k8s.v1.Probe.exec:type_name -> clutch.k8s.v1.ExecAction
	37,  // 33: clutch.k8s.v1.Probe.http_get:type_name -> clutch.k8s.v1.HTTPGetAction
	39,  // 34: clutch.k8s.v1.Probe.tcp_socket:type_name -> clutch.k8s.v1.TCPSocketAction
	40,  // 35: clutch.k8s.v1.Probe.grpc:type_name -> clutch.k8s.v1.GRPCAction
	113, // 36: clutch.k8s.v1.Deployment.labels:type_name -> clutch.k8s.v1.Deployment.LabelsEntry
	114, // 37: clutch.k8s.v1.Deployment.annotations:type_name -> clutch.k8s.v1.Deployment.AnnotationsEntry
	115, // 38: clutch.k8s.v1.Deployment.deployment_status:type_name -> clutch.k8s.v1.Deployment.DeploymentStatus
	116, // 39: clutch.k8s.v1.Deployment.deployment_spec:type_name -> clutch.k8s.v1.Deployment.DeploymentSpec
	42,  // 40: clutch.k8s.v1.DescribeDeploymentResponse.deployment:type_name -> clutch.k8s.v1.Deployment
	20,  // 41: clutch.k8s.v1.ListDeploymentsRequest.options:type_name -> clutch.k8s.v1.ListOptions
	42,  // 42: clutch.k8s.v1.ListDeploymentsResponse.deployments:type_name -> clutch.k8s.v1.Deployment
	124, // 43: clutch.k8s.v1.UpdateDeploymentRequest.fields:type_name -> clutch.k8s.v1.UpdateDeploymentRequest.Fields
	132, // 44: clutch.k8s.v1.StatefulSet.labels:type_name -> clutch.k8s.v1.StatefulSet.LabelsEntry
	133, // 45: clutch.k8s.v1.StatefulSet.annotations:type_name -> clutch.k8s.v1.StatefulSet.AnnotationsEntry
	134, // 46: clutch.k8s.v1.StatefulSet.status:type_name -> clutch.k8s.v1.StatefulSet.Status
	51,  // 47: clutch.k8s.v1.DescribeStatefulSetResponse.stateful_set:type_name -> clutch.k8s.v1.StatefulSet
	20,  // 48: clutch.k8s.v1.ListStatefulSetsRequest.options:type_name -> clutch.k8s.v1.ListOptions
	51,  // 49: clutch.k8s.v1.ListStatefulSetsResponse.stateful_sets:type_name -> clutch.k8s.v1.StatefulSet
	135, // 50: clutch.k8s.v1.UpdateStatefulSetRequest.fields:type_name -> clutch.k8s.v1.UpdateStatefulSetRequest.Fields
	8,   // 51: clutch.k8s.v1.Service.type:type_name -> clutch.k8s.v1.Service.Type
	138, // 52: clutch.k8s.v1.Service.labels:type_name -> clutch.k8s.v1.Service.LabelsEntry
	139, // 53: clutch.k8s.v1.Service.annotations:type_name -> clutch.k8s.v1.Service.AnnotationsEntry
	140, // 54: clutch.k8s.v1.Service.selector:type_name -> clutch.k8s.v1.Service.SelectorEntry
	60,  // 55: clutch.k8s.v1.DescribeServiceResponse.service:type_name -> clutch.k8s.v1.Service
	20,  // 56: clutch.k8s.v1.ListServicesRequest.options:type_name -> clutch.k8s.v1.ListOptions
	60,  // 57: clutch.k8s.v1.ListServicesResponse.services:type_name -> clutch.k8s.v1.Service
	141, // 58: clutch.k8s.v1.CronJob.labels:type_name -> clutch.k8s.v1.CronJob.LabelsEntry
	142, // 59: clutch.k8s.v1.CronJob.annotations:type_name -> clutch.k8s.v1.CronJob.AnnotationsEntry
	9,   // 60: clutch.k8s.v1.CronJob.concurrency_policy:type_name -> clutch.k8s.v1.CronJob.ConcurrencyPolicy
	157, // 61: clutch.k8s.v1.CronJob.starting_deadline_seconds:type_name -> google.protobuf.Int64Value
	67,  // 62: clutch.k8s.v1.DescribeCronJobResponse.cronjob:type_name -> clutch.k8s.v1.CronJob
	20,  // 63: clutch.k8s.v1.ListCronJobsRequest.options:type_name -> clutch.k8s.v1.ListOptions
	67,  // 64: clutch.k8s.v1.ListCronJobsResponse.cron_jobs:type_name -> clutch.k8s.v1.CronJob
	143, // 65: clutch.k8s.v1.ConfigMap.annotations:type_name -> clutch.k8s.v1.ConfigMap.AnnotationsEntry
	144, // 66: clutch.k8s.v1.ConfigMap.labels:type_name -> clutch.k8s.v1.ConfigMap.LabelsEntry
	145, // 67: clutch.k8s.v1.ConfigMap.data:type_name -> clutch.k8s.v1.ConfigMap.DataEntry
	146, // 68: clutch.k8s.v1.ConfigMap.binary_data:type_name -> clutch.k8s.v1.ConfigMap.BinaryDataEntry
	20,  // 69: clutch.k8s.v1.ListConfigMapsRequest.options:type_name -> clutch.k8s.v1.ListOptions
	74,  // 70: clutch.k8s.v1.ListConfigMapsResponse.config_maps:type_name -> clutch.k8s.v1.ConfigMap
	74,  // 71: clutch.k8s.v1.DescribeConfigMapResponse.config_map:type_name -> clutch.k8s.v1.ConfigMap
	147, // 72: clutch.k8s.v1.Job.annotations:type_name -> clutch.k8s.v1.Job.AnnotationsEntry
	148, // 73: clutch.k8s.v1.Job.labels:type_name -> clutch.k8s.v1.Job.LabelsEntry
	81,  // 74: clutch.k8s.v1.DescribeJobResponse.job:type_name -> clutch.k8s.v1.Job
	20,  // 75: clutch.k8s.v1.ListJobsRequest.options:type_name -> clutch.k8s.v1.ListOptions
	81,  // 76: clutch.k8s.v1.ListJobsResponse.jobs:type_name -> clutch.k8s.v1.Job
	158, // 77: clutch.k8s.v1.JobConfig.value:type_name -> google.protobuf.Value
	88,  // 78: clutch.k8s.v1.CreateJobRequest.job_config:type_name -> clutch.k8s.v1.JobConfig
	81,  // 79: clutch.k8s.v1.CreateJobResponse.job:type_name -> clutch.k8s.v1.Job
	149, // 80: clutch.k8s.v1.Namespace.annotations:type_name -> clutch.k8s.v1.Namespace.AnnotationsEntry
	150, // 81: clutch.k8s.v1.Namespace.labels:type_name -> clutch.k8s.v1.Namespace.LabelsEntry
	91,  // 82: clutch.k8s.v1.DescribeNamespaceResponse.namespace:type_name -> clutch.k8s.v1.Namespace
	0,   // 83: clutch.k8s.v1.Event.kind:type_name -> clutch.k8s.v1.ObjectKind
	0,   // 84: clutch.k8s.v1.ListEventsRequest.kind:type_name -> clutch.k8s.v1.ObjectKind
	94,  // 85: clutch.k8s.v1.ListEventsResponse.events:type_name -> clutch.k8s.v1.Event
	159, // 86: clutch.k8s.v1.NullableString.null:type_name -> google.protobuf.NullValue
	151, // 87: clutch.k8s.v1.ExpectedObjectMetaFields.labels:type_name -> clutch.k8s.v1.ExpectedObjectMetaFields.LabelsEntry
	152, // 88: clutch.k8s.v1.ExpectedObjectMetaFields.annotations:type_name -> clutch.k8s.v1.ExpectedObjectMetaFields.AnnotationsEntry
	153, // 89: clutch.k8s.v1.ObjectMetaFields.labels:type_name -> clutch.k8s.v1.ObjectMetaFields.LabelsEntry
	154, // 90: clutch.k8s.v1.ObjectMetaFields.annotations:type_name -> clutch.k8s.v1.ObjectMetaFields.AnnotationsEntry
	101, // 91: clutch.k8s.v1.DescribeNodeResponse.node:type_name -> clutch.k8s.v1.Node
	117, // 92: clutch.k8s.v1.Deployment.DeploymentStatus.deployment_conditions:type_name -> clutch.k8s.v1.Deployment.DeploymentStatus.Condition
	118, // 93: clutch.k8s.v1.Deployment.DeploymentSpec.template:type_name -> clutch.k8s.v1.Deployment.DeploymentSpec.PodTemplateSpec
	6,   // 94: clutch.k8s.v1.Deployment.DeploymentStatus.Condition.type:type_name -> clutch.k8s.v1.Deployment.DeploymentStatus.Condition.Type
	7,   // 95: clutch.k8s.v1.Deployment.DeploymentStatus.Condition.condition_status:type_name -> clutch.k8s.v1.Deployment.DeploymentStatus.Condition.ConditionStatus
	119, // 96: clutch.k8s.v1.Deployment.DeploymentSpec.PodTemplateSpec.spec:type_name -> clutch.k8s.v1.Deployment.DeploymentSpec.PodTemplateSpec.PodSpec
	120, // 97: clutch.k8s.v1.Deployment.DeploymentSpec.PodTemplateSpec.PodSpec.containers:type_name -> clutch.k8s.v1.Deployment.DeploymentSpec.PodTemplateSpec.PodSpec.Container
	121, // 98: clutch.k8s.v1.Deployment.DeploymentSpec.PodTemplateSpec.PodSpec.Container.resources:type_name -> clutch.k8s.v1.Deployment.DeploymentSpec.PodTemplateSpec.PodSpec.Container.ResourceRequirements
	41,  // 99: clutch.k8s.v1.Deployment.DeploymentSpec.PodTemplateSpec.PodSpec.Container.liveness_probe:type_name -> clutch.k8s.v1.Probe
	41,  // 100: clutch.k8s.v1.Deployment.DeploymentSpec.PodTemplateSpec.PodSpec.Container.readiness_probe:type_name -> clutch.k8s.v1.Probe
	122, // 101: clutch.k8s.v1.Deployment.DeploymentSpec.PodTemplateSpec.PodSpec.Container.ResourceRequirements.limits:type_name -> clutch.k8s.v1.Deployment.DeploymentSpec.PodTemplateSpec.PodSpec.Container.ResourceRequirements.LimitsEntry
	123, // 102: clutch.k8s.v1.Deployment.DeploymentSpec.PodTemplateSpec.PodSpec.Container.ResourceRequirements.requests:type_name -> clutch.k8s.v1.Deployment.DeploymentSpec.PodTemplateSpec.PodSpec.Container.ResourceRequirements.RequestsEntry
	125, // 103: clutch.k8s.v1.UpdateDeploymentRequest.Fields.labels:type_name -> clutch.k8s.v1.UpdateDeploymentRequest.Fields.LabelsEntry
	126, // 104: clutch.k8s.v1.UpdateDeploymentRequest.Fields.annotations:type_name -> clutch.k8s.v1.UpdateDeploymentRequest.Fields.AnnotationsEntry
	127, // 105: clutch.k8s.v1.UpdateDeploymentRequest.Fields.container_resources:type_name -> clutch.k8s.v1.UpdateDeploymentRequest.Fields.ContainerResources
	128, // 106: clutch.k8s.v1.UpdateDeploymentRequest.Fields.container_probes:type_name -> clutch.k8s.v1.UpdateDeploymentRequest.Fields.ContainerProbes
	129, // 107: clutch.k8s.v1.UpdateDeploymentRequest.Fields.ContainerResources.resources:type_name -> clutch.k8s.v1.UpdateDeploymentRequest.Fields.ContainerResources.ResourceRequirements
	41,  // 108: clutch.k8s.v1.UpdateDeploymentRequest.Fields.ContainerProbes.liveness_probe:type_name -> clutch.k8s.v1.Probe
	41,  // 109: clutch.k8s.v1.UpdateDeploymentRequest.Fields.ContainerProbes.readiness_probe:type_name -> clutch.k8s.v1.Probe
	130, // 110: clutch.k8s.v1.UpdateDeploymentRequest.Fields.ContainerResources.ResourceRequirements.limits:type_name -> clutch.k8s.v1.UpdateDeploymentRequest.Fields.ContainerResources.ResourceRequirements.LimitsEntry
	131, // 111: clutch.k8s.v1.UpdateDeploymentRequest.Fields.ContainerResources.ResourceRequirements.requests:type_name -> clutch.k8s.v1.UpdateDeploymentRequest.Fields.ContainerResources.ResourceRequirements.RequestsEntry
	136, // 112: clutch.k8s.v1.UpdateStatefulSetRequest.Fields.labels:type_name -> clutch.k8s.v1.UpdateStatefulSetRequest.Fields.LabelsEntry
	137, // 113: clutch.k8s.v1.UpdateStatefulSetRequest.Fields.annotations:type_name -> clutch.k8s.v1.UpdateStatefulSetRequest.Fields.AnnotationsEntry
	97,  // 114: clutch.k8s.v1.ExpectedObjectMetaFields.LabelsEntry.value:type_name -> clutch.k8s.v1.NullableString
	97,  // 115: clutch.k8s.v1.ExpectedObjectMetaFields.AnnotationsEntry.value:type_name -> clutch.k8s.v1.NullableString
	12,  // 116: clutch.k8s.v1.K8sAPI.DescribePod:input_type -> clutch.k8s.v1.DescribePodRequest
	21,  // 117: clutch.k8s.v1.K8sAPI.ListPods:input_type -> clutch.k8s.v1.ListPodsRequest
	23,  // 118: clutch.k8s.v1.K8sAPI.DeletePod:input_type -> clutch.k8s.v1.DeletePodRequest
	25,  // 119: clutch.k8s.v1.K8sAPI.UpdatePod:input_type -> clutch.k8s.v1.UpdatePodRequest
	27,  // 120: clutch.k8s.v1.K8sAPI.GetPodLogs:input_type -> clutch.k8s.v1.GetPodLogsRequest
	32,  // 121: clutch.k8s.v1.K8sAPI.ResizeHPA:input_type -> clutch.k8s.v1.ResizeHPARequest
	34,  // 122: clutch.k8s.v1.K8sAPI.DeleteHPA:input_type -> clutch.k8s.v1.DeleteHPARequest
	43,  // 123: clutch.k8s.v1.K8sAPI.DescribeDeployment:input_type -> clutch.k8s.v1.DescribeDeploymentRequest
	45,  // 124: clutch.k8s.v1.K8sAPI.ListDeployments:input_type -> clutch.k8s.v1.ListDeploymentsRequest
	47,  // 125: clutch.k8s.v1.K8sAPI.UpdateDeployment:input_type -> clutch.k8s.v1.UpdateDeploymentRequest
	49,  // 126: clutch.k8s.v1.K8sAPI.DeleteDeployment:input_type -> clutch.k8s.v1.DeleteDeploymentRequest
	61,  // 127: clutch.k8s.v1.K8sAPI.DescribeService:input_type -> clutch.k8s.v1.DescribeServiceRequest
	63,  // 128: clutch.k8s.v1.K8sAPI.ListServices:input_type -> clutch.k8s.v1.ListServicesRequest
	65,  // 129: clutch.k8s.v1.K8sAPI.DeleteService:input_type -> clutch.k8s.v1.DeleteServiceRequest
	52,  // 130: clutch.k8s.v1.K8sAPI.DescribeStatefulSet:input_type -> clutch.k8s.v1.DescribeStatefulSetRequest
	54,  // 131: clutch.k8s.v1.K8sAPI.ListStatefulSets:input_type -> clutch.k8s.v1.ListStatefulSetsRequest
	58,  // 132: clutch.k8s.v1.K8sAPI.UpdateStatefulSet:input_type -> clutch.k8s.v1.UpdateStatefulSetRequest
	56,  // 133: clutch.k8s.v1.K8sAPI.DeleteStatefulSet:input_type -> clutch.k8s.v1.DeleteStatefulSetRequest
	68,  // 134: clutch.k8s.v1.K8sAPI.DescribeCronJob:input_type -> clutch.k8s.v1.DescribeCronJobRequest
	70,  // 135: clutch.k8s.v1.K8sAPI.ListCronJobs:input_type -> clutch.k8s.v1.ListCronJobsRequest
	72,  // 136: clutch.k8s.v1.K8sAPI.DeleteCronJob:input_type -> clutch.k8s.v1.DeleteCronJobRequest
	75,  // 137: clutch.k8s.v1.K8sAPI.ListConfigMaps:input_type -> clutch.k8s.v1.ListConfigMapsRequest
	77,  // 138: clutch.k8s.v1.K8sAPI.DescribeConfigMap:input_type -> clutch.k8s.v1.DescribeConfigMapRequest
	79,  // 139: clutch.k8s.v1.K8sAPI.DeleteConfigMap:input_type -> clutch.k8s.v1.DeleteConfigMapRequest
	82,  // 140: clutch.k8s.v1.K8sAPI.DescribeJob:input_type -> clutch.k8s.v1.DescribeJobRequest
	84,  // 141: clutch.k8s.v1.K8sAPI.ListJobs:input_type -> clutch.k8s.v1.ListJobsRequest
	86,  // 142: clutch.k8s.v1.K8sAPI.DeleteJob:input_type -> clutch.k8s.v1.DeleteJobRequest
	89,  // 143: clutch.k8s.v1.K8sAPI.CreateJob:input_type -> clutch.k8s.v1.CreateJobRequest
	92,  // 144: clutch.k8s.v1.K8sAPI.DescribeNamespace:input_type -> clutch.k8s.v1.DescribeNamespaceRequest
	95,  // 145: clutch.k8s.v1.K8sAPI.ListEvents:input_type -> clutch.k8s.v1.ListEventsRequest
	102, // 146: clutch.k8s.v1.K8sAPI.DescribeNode:input_type -> clutch.k8s.v1.DescribeNodeRequest
	104, // 147: clutch.k8s.v1.K8sAPI.UpdateNode:input_type -> clutch.k8s.v1.UpdateNodeRequest
	10,  // 148: clutch.k8s.v1.K8sAPI.ListNamespaceEvents:input_type -> clutch.k8s.v1.ListNamespaceEventsRequest
	13,  // 149: clutch.k8s.v1.K8sAPI.DescribePod:output_type -> clutch.k8s.v1.DescribePodResponse
	22,  // 150: clutch.k8s.v1.K8sAPI.ListPods:output_type -> clutch.k8s.v1.ListPodsResponse
	24,  // 151: clutch.k8s.v1.K8sAPI.DeletePod:output_type -> clutch.k8s.v1.DeletePodResponse
	26,  // 152: clutch.k8s.v1.K8sAPI.UpdatePod:output_type -> clutch.k8s.v1.UpdatePodResponse
	29,  // 153: clutch.k8s.v1.K8sAPI.GetPodLogs:output_type -> clutch.k8s.v1.GetPodLogsResponse
	33,  // 154: clutch.k8s.v1.K8sAPI.ResizeHPA:output_type -> clutch.k8s.v1.ResizeHPAResponse
	35,  // 155: clutch.k8s.v1.K8sAPI.DeleteHPA:output_type -> clutch.k8s.v1.DeleteHPAResponse
	44,  // 156: clutch.k8s.v1.K8sAPI.DescribeDeployment:output_type -> clutch.k8s.v1.DescribeDeploymentResponse
	46,  // 157: clutch.k8s.v1.K8sAPI.ListDeployments:output_type -> clutch.k8s.v1.ListDeploymentsResponse
	48,  // 158: clutch.k8s.v1.K8sAPI.UpdateDeployment:output_type -> clutch.k8s.v1.UpdateDeploymentResponse
	50,  // 159: clutch.k8s.v1.K8sAPI.DeleteDeployment:output_type -> clutch.k8s.v1.DeleteDeploymentResponse
	62,  // 160: clutch.k8s.v1.K8sAPI.DescribeService:output_type -> clutch.k8s.v1.DescribeServiceResponse
	64,  // 161: clutch.k8s.v1.K8sAPI.ListServices:output_type -> clutch.k8s.v1.ListServicesResponse
	66,  // 162: clutch.k8s.v1.K8sAPI.DeleteService:output_type -> clutch.k8s.v1.DeleteServiceResponse
	53,  // 163: clutch.k8s.v1.K8sAPI.DescribeStatefulSet:output_type -> clutch.k8s.v1.DescribeStatefulSetResponse
	55,  // 164: clutch.k8s.v1.K8sAPI.ListStatefulSets:output_type -> clutch.k8s.v1.ListStatefulSetsResponse
	59,  // 165: clutch.k8s.v1.K8sAPI.UpdateStatefulSet:output_type -> clutch.k8s.v1.UpdateStatefulSetResponse
	57,  // 166: clutch.k8s.v1.K8sAPI.DeleteStatefulSet:output_type -> clutch.k8s.v1.DeleteStatefulSetResponse
	69,  // 167: clutch.k8s.v1.K8sAPI.DescribeCronJob:output_type -> clutch.k8s.v1.DescribeCronJobResponse
	71,  // 168: clutch.k8s.v1.K8sAPI.ListCronJobs:output_type -> clutch.k8s.v1.ListCronJobsResponse
	73,  // 169: clutch.k8s.v1.K8sAPI.DeleteCronJob:output_type -> clutch.k8s.v1.DeleteCronJobResponse
	76,  // 170: clutch.k8s.v1.K8sAPI.ListConfigMaps:output_type -> clutch.k8s.v1.ListConfigMapsResponse
	78,  // 171: clutch.k8s.v1.K8sAPI.DescribeConfigMap:output_type -> clutch.k8s.v1.DescribeConfigMapResponse
	80,  // 172: clutch.k8s.v1.K8sAPI.DeleteConfigMap:output_type -> clutch.k8s.v1.DeleteConfigMapResponse
	83,  // 173: clutch.k8s.v1.K8sAPI.DescribeJob:output_type -> clutch.k8s.v1.DescribeJobResponse
	85,  // 174: clutch.k8s.v1.K8sAPI.ListJobs:output_type -> clutch.k8s.v1.ListJobsResponse
	87,  // 175: clutch.k8s.v1.K8sAPI.DeleteJob:output_type -> clutch.k8s.v1.DeleteJobResponse
	90,  // 176: clutch.k8s.v1.K8sAPI.CreateJob:output_type -> clutch.k8s.v1.CreateJobResponse
	93,  // 177: clutch.k8s.v1.K8sAPI.DescribeNamespace:output_type -> clutch.k8s.v1.DescribeNamespaceResponse
	96,  // 178: clutch.k8s.v1.K8sAPI.ListEvents:output_type -> clutch.k8s.v1.ListEventsResponse
	103, // 179: clutch.k8s.v1.K8sAPI.DescribeNode:output_type -> clutch.k8s.v1.DescribeNodeResponse
	105, // 180: clutch.k8s.v1.K8sAPI.UpdateNode:output_type -> clutch.k8s.v1.UpdateNodeResponse
	11,  // 181: clutch.k8s.v1.K8sAPI.ListNamespaceEvents:output_type -> clutch.k8s.v1.ListNamespaceEventsResponse
	149, // [149:182] is the sub-list for method output_type
	116, // [116:149] is the sub-list for method input_type
	116, // [116:116] is the sub-list for extension type_name
	116, // [116:116] is the sub-list for extension extendee
	0,   // [0:116] is the sub-list for field type_name
}

func init() { file_k8s_v1_k8s_proto_init() }
func file_k8s_v1_k8s_proto_init() {
	if File_k8s_v1_k8s_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_k8s_v1_k8s_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListNamespaceEventsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListNamespaceEventsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribePodRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribePodResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Container); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StateWaiting); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StateRunning); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StateTerminated); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PodCondition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Pod); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPodsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPodsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePodRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePodResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePodRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePodResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPodLogsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PodLogsOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPodLogsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PodLogLine); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HPA); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResizeHPARequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResizeHPAResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteHPARequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteHPAResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExecAction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HTTPGetAction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HTTPHeader); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TCPSocketAction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GRPCAction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Probe); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Deployment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeDeploymentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeDeploymentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDeploymentsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDeploymentsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDeploymentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDeploymentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteDeploymentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteDeploymentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatefulSet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeStatefulSetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeStatefulSetResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListStatefulSetsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListStatefulSetsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteStatefulSetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteStatefulSetResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateStatefulSetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateStatefulSetResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Service); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeServiceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeServiceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServicesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServicesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteServiceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteServiceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CronJob); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeCronJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeCronJobResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCronJobsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCronJobsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteCronJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteCronJobResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConfigMap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListConfigMapsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListConfigMapsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeConfigMapRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeConfigMapResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteConfigMapRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteConfigMapResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Job); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[73].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeJobResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[74].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListJobsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[75].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListJobsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[76].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[77].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteJobResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[78].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JobConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[79].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[80].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateJobResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[81].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Namespace); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[82].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeNamespaceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[83].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeNamespaceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[84].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Event); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[85].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListEventsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[86].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListEventsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[87].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NullableString); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[88].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpectedObjectMetaFields); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[89].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ObjectMetaFields); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[90].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoveObjectMetaFields); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[91].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Node); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[92].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeNodeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[93].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeNodeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[94].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateNodeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[95].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateNodeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[99].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HPA_Sizing); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[102].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResizeHPARequest_Sizing); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[105].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Deployment_DeploymentStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[106].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Deployment_DeploymentSpec); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[107].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Deployment_DeploymentStatus_Condition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[108].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Deployment_DeploymentSpec_PodTemplateSpec); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[109].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Deployment_DeploymentSpec_PodTemplateSpec_PodSpec); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[110].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Deployment_DeploymentSpec_PodTemplateSpec_PodSpec_Container); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[111].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Deployment_DeploymentSpec_PodTemplateSpec_PodSpec_Container_ResourceRequirements); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[114].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDeploymentRequest_Fields); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[117].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDeploymentRequest_Fields_ContainerResources); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[118].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDeploymentRequest_Fields_ContainerProbes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[119].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDeploymentRequest_Fields_ContainerResources_ResourceRequirements); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[124].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatefulSet_Status); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_k8s_v1_k8s_proto_msgTypes[125].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateStatefulSetRequest_Fields); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_k8s_v1_k8s_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*Container_StateWaiting)(nil),
		(*Container_StateRunning)(nil),
		(*Container_StateTerminated)(nil),
	}
	file_k8s_v1_k8s_proto_msgTypes[28].OneofWrappers = []interface{}{}
	file_k8s_v1_k8s_proto_msgTypes[31].OneofWrappers = []interface{}{
		(*Probe_Exec)(nil),
		(*Probe_HttpGet)(nil),
		(*Probe_TcpSocket)(nil),
		(*Probe_Grpc)(nil),
	}
	file_k8s_v1_k8s_proto_msgTypes[87].OneofWrappers = []interface{}{
		(*NullableString_Null)(nil),
		(*NullableString_Value)(nil),
	}
	file_k8s_v1_k8s_proto_msgTypes[110].OneofWrappers = []interface{}{}
	file_k8s_v1_k8s_proto_msgTypes[118].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_k8s_v1_k8s_proto_rawDesc,
			NumEnums:      10,
			NumMessages:   145,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_k8s_v1_k8s_proto_goTypes,
		DependencyIndexes: file_k8s_v1_k8s_proto_depIdxs,
		EnumInfos:         file_k8s_v1_k8s_proto_enumTypes,
		MessageInfos:      file_k8s_v1_k8s_proto_msgTypes,
	}.Build()
	File_k8s_v1_k8s_proto = out.File
	file_k8s_v1_k8s_proto_rawDesc = nil
	file_k8s_v1_k8s_proto_goTypes = nil
	file_k8s_v1_k8s_proto_depIdxs = nil
}
