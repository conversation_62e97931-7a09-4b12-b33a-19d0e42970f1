CREATE TABLE feedback (
  -- client_id: uuid generated by the client and used to identify the feedback submission
  client_id text PRIMARY KEY,

  submitted_at TIMESTAMP WITH TIME ZONE,

  user_id text,

  score smallint,

  -- details: json blob of the feedback details
  details JSONB,

  -- metadata: json blob of info providing more context on the feedback details, such as:
  -- the survey questions, the origin, whether the feedback was formally submitted, etc.
  metadata JSONB
);

CREATE INDEX IF NOT EXISTS sort_submissions ON feedback (submitted_at);
CREATE INDEX IF NOT EXISTS sort_user_id ON feedback (user_id);
CREATE INDEX IF NOT EXISTS sort_score ON feedback (score);
CREATE INDEX IF NOT EXISTS details_json ON feedback USING GIN (details jsonb_path_ops);
CREATE INDEX IF NOT EXISTS metadata_json ON feedback USING GIN (metadata jsonb_path_ops);
