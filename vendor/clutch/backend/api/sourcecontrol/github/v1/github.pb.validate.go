// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: sourcecontrol/github/v1/github.proto

package githubv1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on RepositoryParameters with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RepositoryParameters) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RepositoryParameters with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RepositoryParametersMultiError, or nil if none found.
func (m *RepositoryParameters) ValidateAll() error {
	return m.validate(true)
}

func (m *RepositoryParameters) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _RepositoryParameters_Visibility_NotInLookup[m.GetVisibility()]; ok {
		err := RepositoryParametersValidationError{
			field:  "Visibility",
			reason: "value must not be in list [UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := RepositoryParameters_Visibility_name[int32(m.GetVisibility())]; !ok {
		err := RepositoryParametersValidationError{
			field:  "Visibility",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAllowMergeCommit()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RepositoryParametersValidationError{
					field:  "AllowMergeCommit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RepositoryParametersValidationError{
					field:  "AllowMergeCommit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAllowMergeCommit()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RepositoryParametersValidationError{
				field:  "AllowMergeCommit",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAllowRebaseMerge()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RepositoryParametersValidationError{
					field:  "AllowRebaseMerge",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RepositoryParametersValidationError{
					field:  "AllowRebaseMerge",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAllowRebaseMerge()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RepositoryParametersValidationError{
				field:  "AllowRebaseMerge",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAllowSquashMerge()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RepositoryParametersValidationError{
					field:  "AllowSquashMerge",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RepositoryParametersValidationError{
					field:  "AllowSquashMerge",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAllowSquashMerge()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RepositoryParametersValidationError{
				field:  "AllowSquashMerge",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RepositoryParametersMultiError(errors)
	}

	return nil
}

// RepositoryParametersMultiError is an error wrapping multiple validation
// errors returned by RepositoryParameters.ValidateAll() if the designated
// constraints aren't met.
type RepositoryParametersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RepositoryParametersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RepositoryParametersMultiError) AllErrors() []error { return m }

// RepositoryParametersValidationError is the validation error returned by
// RepositoryParameters.Validate if the designated constraints aren't met.
type RepositoryParametersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RepositoryParametersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RepositoryParametersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RepositoryParametersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RepositoryParametersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RepositoryParametersValidationError) ErrorName() string {
	return "RepositoryParametersValidationError"
}

// Error satisfies the builtin error interface
func (e RepositoryParametersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRepositoryParameters.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RepositoryParametersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RepositoryParametersValidationError{}

var _RepositoryParameters_Visibility_NotInLookup = map[RepositoryParameters_Visibility]struct{}{
	0: {},
}

// Validate checks the field values on CreateRepositoryOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateRepositoryOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateRepositoryOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateRepositoryOptionsMultiError, or nil if none found.
func (m *CreateRepositoryOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateRepositoryOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetParameters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRepositoryOptionsValidationError{
					field:  "Parameters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRepositoryOptionsValidationError{
					field:  "Parameters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetParameters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRepositoryOptionsValidationError{
				field:  "Parameters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AutoInit

	if len(errors) > 0 {
		return CreateRepositoryOptionsMultiError(errors)
	}

	return nil
}

// CreateRepositoryOptionsMultiError is an error wrapping multiple validation
// errors returned by CreateRepositoryOptions.ValidateAll() if the designated
// constraints aren't met.
type CreateRepositoryOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateRepositoryOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateRepositoryOptionsMultiError) AllErrors() []error { return m }

// CreateRepositoryOptionsValidationError is the validation error returned by
// CreateRepositoryOptions.Validate if the designated constraints aren't met.
type CreateRepositoryOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateRepositoryOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateRepositoryOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateRepositoryOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateRepositoryOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateRepositoryOptionsValidationError) ErrorName() string {
	return "CreateRepositoryOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e CreateRepositoryOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateRepositoryOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateRepositoryOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateRepositoryOptionsValidationError{}

// Validate checks the field values on UpdateRepositoryOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateRepositoryOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateRepositoryOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateRepositoryOptionsMultiError, or nil if none found.
func (m *UpdateRepositoryOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateRepositoryOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetParameters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateRepositoryOptionsValidationError{
					field:  "Parameters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateRepositoryOptionsValidationError{
					field:  "Parameters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetParameters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateRepositoryOptionsValidationError{
				field:  "Parameters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Archived

	if len(errors) > 0 {
		return UpdateRepositoryOptionsMultiError(errors)
	}

	return nil
}

// UpdateRepositoryOptionsMultiError is an error wrapping multiple validation
// errors returned by UpdateRepositoryOptions.ValidateAll() if the designated
// constraints aren't met.
type UpdateRepositoryOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateRepositoryOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateRepositoryOptionsMultiError) AllErrors() []error { return m }

// UpdateRepositoryOptionsValidationError is the validation error returned by
// UpdateRepositoryOptions.Validate if the designated constraints aren't met.
type UpdateRepositoryOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateRepositoryOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateRepositoryOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateRepositoryOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateRepositoryOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateRepositoryOptionsValidationError) ErrorName() string {
	return "UpdateRepositoryOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateRepositoryOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateRepositoryOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateRepositoryOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateRepositoryOptionsValidationError{}

// Validate checks the field values on CommitComparison with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CommitComparison) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CommitComparison with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CommitComparisonMultiError, or nil if none found.
func (m *CommitComparison) ValidateAll() error {
	return m.validate(true)
}

func (m *CommitComparison) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	if len(errors) > 0 {
		return CommitComparisonMultiError(errors)
	}

	return nil
}

// CommitComparisonMultiError is an error wrapping multiple validation errors
// returned by CommitComparison.ValidateAll() if the designated constraints
// aren't met.
type CommitComparisonMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CommitComparisonMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CommitComparisonMultiError) AllErrors() []error { return m }

// CommitComparisonValidationError is the validation error returned by
// CommitComparison.Validate if the designated constraints aren't met.
type CommitComparisonValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CommitComparisonValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CommitComparisonValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CommitComparisonValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CommitComparisonValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CommitComparisonValidationError) ErrorName() string { return "CommitComparisonValidationError" }

// Error satisfies the builtin error interface
func (e CommitComparisonValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCommitComparison.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CommitComparisonValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CommitComparisonValidationError{}
