---
title: Development Guide
sidebar_label: Guide
{{ .EditURL }}
---

import LinkCard from '@site/src/components/LinkCard';

:wave: Welcome! Getting started with Clutch development is easy. Clutch has a number of extension points that make it easy to customize for a new use case without forks or rewrites.

Familiarizing yourself with concepts in the [Architecture Reference](/docs/about/architecture) is recommended prior to starting development.

## Guides

### Basic

<LinkCard title="Custom Gateway" description="Create a customized instance of Clutch in your own repository with private or custom extensions." to="/docs/development/custom-gateway" />
<LinkCard title="Feature Development" description="See how to take a feature from idea to implementation across the frontend and backend." to="/docs/development/feature" />

### In-depth

<LinkCard title="API Definitions" description="Define API endpoints and objects plus backend config in Protobuf and generate the corresponding backend code." to="/docs/development/api" />
<LinkCard title="Frontend" description="Develop new features and workflows using React that users can interact with from the web UI." to="/docs/development/frontend/overview" />
<LinkCard title="Backend" description="Integrate with other systems, execute tasks, and store data." to="/docs/development/backend" />

## Recommended Editors

The Clutch team recommends [Visual Studio Code](https://code.visualstudio.com/) for frontend development and [Goland](https://www.jetbrains.com/go/) for backend development.

## More Information

Looking for something else? [Contact us](/docs/community).
