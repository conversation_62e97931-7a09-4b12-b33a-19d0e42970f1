// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: resolver/k8s/v1/k8s.proto

package k8sv1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on PodID with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PodID) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PodID with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in PodIDMultiError, or nil if none found.
func (m *PodID) ValidateAll() error {
	return m.validate(true)
}

func (m *PodID) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Clientset

	// no validation rules for Namespace

	if len(errors) > 0 {
		return PodIDMultiError(errors)
	}

	return nil
}

// PodIDMultiError is an error wrapping multiple validation errors returned by
// PodID.ValidateAll() if the designated constraints aren't met.
type PodIDMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PodIDMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PodIDMultiError) AllErrors() []error { return m }

// PodIDValidationError is the validation error returned by PodID.Validate if
// the designated constraints aren't met.
type PodIDValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PodIDValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PodIDValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PodIDValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PodIDValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PodIDValidationError) ErrorName() string { return "PodIDValidationError" }

// Error satisfies the builtin error interface
func (e PodIDValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPodID.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PodIDValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PodIDValidationError{}

// Validate checks the field values on IPAddress with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *IPAddress) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IPAddress with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in IPAddressMultiError, or nil
// if none found.
func (m *IPAddress) ValidateAll() error {
	return m.validate(true)
}

func (m *IPAddress) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IpAddress

	if len(errors) > 0 {
		return IPAddressMultiError(errors)
	}

	return nil
}

// IPAddressMultiError is an error wrapping multiple validation errors returned
// by IPAddress.ValidateAll() if the designated constraints aren't met.
type IPAddressMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IPAddressMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IPAddressMultiError) AllErrors() []error { return m }

// IPAddressValidationError is the validation error returned by
// IPAddress.Validate if the designated constraints aren't met.
type IPAddressValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IPAddressValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IPAddressValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IPAddressValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IPAddressValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IPAddressValidationError) ErrorName() string { return "IPAddressValidationError" }

// Error satisfies the builtin error interface
func (e IPAddressValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIPAddress.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IPAddressValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IPAddressValidationError{}

// Validate checks the field values on HPAName with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *HPAName) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HPAName with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in HPANameMultiError, or nil if none found.
func (m *HPAName) ValidateAll() error {
	return m.validate(true)
}

func (m *HPAName) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Clientset

	// no validation rules for Namespace

	if len(errors) > 0 {
		return HPANameMultiError(errors)
	}

	return nil
}

// HPANameMultiError is an error wrapping multiple validation errors returned
// by HPAName.ValidateAll() if the designated constraints aren't met.
type HPANameMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HPANameMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HPANameMultiError) AllErrors() []error { return m }

// HPANameValidationError is the validation error returned by HPAName.Validate
// if the designated constraints aren't met.
type HPANameValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HPANameValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HPANameValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HPANameValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HPANameValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HPANameValidationError) ErrorName() string { return "HPANameValidationError" }

// Error satisfies the builtin error interface
func (e HPANameValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHPAName.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HPANameValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HPANameValidationError{}

// Validate checks the field values on Deployment with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Deployment) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Deployment with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DeploymentMultiError, or
// nil if none found.
func (m *Deployment) ValidateAll() error {
	return m.validate(true)
}

func (m *Deployment) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Clientset

	// no validation rules for Namespace

	if len(errors) > 0 {
		return DeploymentMultiError(errors)
	}

	return nil
}

// DeploymentMultiError is an error wrapping multiple validation errors
// returned by Deployment.ValidateAll() if the designated constraints aren't met.
type DeploymentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeploymentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeploymentMultiError) AllErrors() []error { return m }

// DeploymentValidationError is the validation error returned by
// Deployment.Validate if the designated constraints aren't met.
type DeploymentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeploymentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeploymentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeploymentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeploymentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeploymentValidationError) ErrorName() string { return "DeploymentValidationError" }

// Error satisfies the builtin error interface
func (e DeploymentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeployment.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeploymentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeploymentValidationError{}

// Validate checks the field values on StatefulSet with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StatefulSet) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StatefulSet with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in StatefulSetMultiError, or
// nil if none found.
func (m *StatefulSet) ValidateAll() error {
	return m.validate(true)
}

func (m *StatefulSet) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Clientset

	// no validation rules for Namespace

	if len(errors) > 0 {
		return StatefulSetMultiError(errors)
	}

	return nil
}

// StatefulSetMultiError is an error wrapping multiple validation errors
// returned by StatefulSet.ValidateAll() if the designated constraints aren't met.
type StatefulSetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StatefulSetMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StatefulSetMultiError) AllErrors() []error { return m }

// StatefulSetValidationError is the validation error returned by
// StatefulSet.Validate if the designated constraints aren't met.
type StatefulSetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StatefulSetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StatefulSetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StatefulSetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StatefulSetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StatefulSetValidationError) ErrorName() string { return "StatefulSetValidationError" }

// Error satisfies the builtin error interface
func (e StatefulSetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStatefulSet.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StatefulSetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StatefulSetValidationError{}

// Validate checks the field values on Service with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Service) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Service with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ServiceMultiError, or nil if none found.
func (m *Service) ValidateAll() error {
	return m.validate(true)
}

func (m *Service) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Clientset

	// no validation rules for Namespace

	if len(errors) > 0 {
		return ServiceMultiError(errors)
	}

	return nil
}

// ServiceMultiError is an error wrapping multiple validation errors returned
// by Service.ValidateAll() if the designated constraints aren't met.
type ServiceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServiceMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServiceMultiError) AllErrors() []error { return m }

// ServiceValidationError is the validation error returned by Service.Validate
// if the designated constraints aren't met.
type ServiceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServiceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServiceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServiceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServiceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServiceValidationError) ErrorName() string { return "ServiceValidationError" }

// Error satisfies the builtin error interface
func (e ServiceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sService.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServiceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServiceValidationError{}

// Validate checks the field values on CronJob with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CronJob) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CronJob with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in CronJobMultiError, or nil if none found.
func (m *CronJob) ValidateAll() error {
	return m.validate(true)
}

func (m *CronJob) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Clientset

	// no validation rules for Namespace

	if len(errors) > 0 {
		return CronJobMultiError(errors)
	}

	return nil
}

// CronJobMultiError is an error wrapping multiple validation errors returned
// by CronJob.ValidateAll() if the designated constraints aren't met.
type CronJobMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CronJobMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CronJobMultiError) AllErrors() []error { return m }

// CronJobValidationError is the validation error returned by CronJob.Validate
// if the designated constraints aren't met.
type CronJobValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CronJobValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CronJobValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CronJobValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CronJobValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CronJobValidationError) ErrorName() string { return "CronJobValidationError" }

// Error satisfies the builtin error interface
func (e CronJobValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCronJob.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CronJobValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CronJobValidationError{}

// Validate checks the field values on ConfigMap with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ConfigMap) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConfigMap with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ConfigMapMultiError, or nil
// if none found.
func (m *ConfigMap) ValidateAll() error {
	return m.validate(true)
}

func (m *ConfigMap) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Clientset

	// no validation rules for Namespace

	if len(errors) > 0 {
		return ConfigMapMultiError(errors)
	}

	return nil
}

// ConfigMapMultiError is an error wrapping multiple validation errors returned
// by ConfigMap.ValidateAll() if the designated constraints aren't met.
type ConfigMapMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConfigMapMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConfigMapMultiError) AllErrors() []error { return m }

// ConfigMapValidationError is the validation error returned by
// ConfigMap.Validate if the designated constraints aren't met.
type ConfigMapValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConfigMapValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConfigMapValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConfigMapValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConfigMapValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConfigMapValidationError) ErrorName() string { return "ConfigMapValidationError" }

// Error satisfies the builtin error interface
func (e ConfigMapValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConfigMap.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConfigMapValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConfigMapValidationError{}

// Validate checks the field values on Job with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Job) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Job with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in JobMultiError, or nil if none found.
func (m *Job) ValidateAll() error {
	return m.validate(true)
}

func (m *Job) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Clientset

	// no validation rules for Namespace

	if len(errors) > 0 {
		return JobMultiError(errors)
	}

	return nil
}

// JobMultiError is an error wrapping multiple validation errors returned by
// Job.ValidateAll() if the designated constraints aren't met.
type JobMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m JobMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m JobMultiError) AllErrors() []error { return m }

// JobValidationError is the validation error returned by Job.Validate if the
// designated constraints aren't met.
type JobValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e JobValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e JobValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e JobValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e JobValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e JobValidationError) ErrorName() string { return "JobValidationError" }

// Error satisfies the builtin error interface
func (e JobValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sJob.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = JobValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = JobValidationError{}

// Validate checks the field values on Namespace with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Namespace) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Namespace with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in NamespaceMultiError, or nil
// if none found.
func (m *Namespace) ValidateAll() error {
	return m.validate(true)
}

func (m *Namespace) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Clientset

	if len(errors) > 0 {
		return NamespaceMultiError(errors)
	}

	return nil
}

// NamespaceMultiError is an error wrapping multiple validation errors returned
// by Namespace.ValidateAll() if the designated constraints aren't met.
type NamespaceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NamespaceMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NamespaceMultiError) AllErrors() []error { return m }

// NamespaceValidationError is the validation error returned by
// Namespace.Validate if the designated constraints aren't met.
type NamespaceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NamespaceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NamespaceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NamespaceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NamespaceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NamespaceValidationError) ErrorName() string { return "NamespaceValidationError" }

// Error satisfies the builtin error interface
func (e NamespaceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNamespace.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NamespaceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NamespaceValidationError{}

// Validate checks the field values on Node with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Node) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Node with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in NodeMultiError, or nil if none found.
func (m *Node) ValidateAll() error {
	return m.validate(true)
}

func (m *Node) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Cluster

	// no validation rules for Clientset

	if len(errors) > 0 {
		return NodeMultiError(errors)
	}

	return nil
}

// NodeMultiError is an error wrapping multiple validation errors returned by
// Node.ValidateAll() if the designated constraints aren't met.
type NodeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NodeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NodeMultiError) AllErrors() []error { return m }

// NodeValidationError is the validation error returned by Node.Validate if the
// designated constraints aren't met.
type NodeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NodeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NodeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NodeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NodeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NodeValidationError) ErrorName() string { return "NodeValidationError" }

// Error satisfies the builtin error interface
func (e NodeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNode.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NodeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NodeValidationError{}
