import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../store/store';
import { fetchDatabaseStats, executeQuery, clearQueryResult } from '../store/slices/databaseSlice';
import {
  CircleStackIcon,
  PlayIcon,
  TrashIcon,
} from '@heroicons/react/24/outline';

const DatabaseAdmin: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { stats, loading, error, queryResult, queryLoading } = useSelector(
    (state: RootState) => state.database
  );

  const [query, setQuery] = useState('');

  useEffect(() => {
    dispatch(fetchDatabaseStats());
  }, [dispatch]);

  const handleExecuteQuery = () => {
    if (query.trim()) {
      dispatch(executeQuery(query));
    }
  };

  const handleClearResult = () => {
    dispatch(clearQueryResult());
  };

  const sampleQueries = [
    'SELECT * FROM resource_cache LIMIT 10',
    'SELECT provider, COUNT(*) as count FROM resource_cache GROUP BY provider',
    'SELECT * FROM workflow_executions WHERE status = "running"',
    'SELECT * FROM envoy_configs ORDER BY updated_at DESC LIMIT 5',
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-white">Database Administration</h1>
        <p className="mt-1 text-sm text-gray-400">
          Monitor database health and execute queries
        </p>
      </div>

      {/* Database Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div className="bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CircleStackIcon className="h-6 w-6 text-blue-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-400 truncate">Total Resources</dt>
                  <dd className="text-lg font-medium text-white">
                    {stats?.total_resources?.toLocaleString() || 0}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CircleStackIcon className="h-6 w-6 text-green-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-400 truncate">Workflows</dt>
                  <dd className="text-lg font-medium text-white">
                    {stats?.total_workflows?.toLocaleString() || 0}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CircleStackIcon className="h-6 w-6 text-purple-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-400 truncate">Envoy Configs</dt>
                  <dd className="text-lg font-medium text-white">
                    {stats?.total_envoy_configs?.toLocaleString() || 0}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CircleStackIcon className="h-6 w-6 text-yellow-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-400 truncate">Cache Hit Rate</dt>
                  <dd className="text-lg font-medium text-white">
                    {stats?.cache_hit_rate ? `${stats.cache_hit_rate}%` : 'N/A'}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Database Info */}
      <div className="bg-gray-800 shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-white mb-4">Database Information</h3>
          
          {loading ? (
            <div className="flex items-center justify-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500"></div>
            </div>
          ) : (
            <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
              <div>
                <dt className="text-sm font-medium text-gray-400">Database Size</dt>
                <dd className="mt-1 text-sm text-white">{stats?.database_size || 'Unknown'}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-400">Cache Size</dt>
                <dd className="mt-1 text-sm text-white">{stats?.cache_size || 'Unknown'}</dd>
              </div>
            </dl>
          )}
        </div>
      </div>

      {/* Query Interface */}
      <div className="bg-gray-800 shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-white mb-4">SQL Query Interface</h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                SQL Query
              </label>
              <textarea
                className="block w-full h-32 px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white font-mono text-sm focus:outline-none focus:ring-2 focus:ring-cyan-500"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                placeholder="Enter your SQL query here..."
              />
            </div>

            <div className="flex items-center space-x-3">
              <button
                onClick={handleExecuteQuery}
                disabled={queryLoading || !query.trim()}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 disabled:opacity-50"
              >
                <PlayIcon className="h-4 w-4 mr-2" />
                {queryLoading ? 'Executing...' : 'Execute Query'}
              </button>

              {queryResult && (
                <button
                  onClick={handleClearResult}
                  className="inline-flex items-center px-4 py-2 border border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-300 bg-gray-700 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                >
                  <TrashIcon className="h-4 w-4 mr-2" />
                  Clear Result
                </button>
              )}
            </div>

            {/* Sample Queries */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Sample Queries
              </label>
              <div className="space-y-2">
                {sampleQueries.map((sampleQuery, index) => (
                  <button
                    key={index}
                    onClick={() => setQuery(sampleQuery)}
                    className="block w-full text-left px-3 py-2 text-sm text-gray-300 bg-gray-700 hover:bg-gray-600 rounded border border-gray-600"
                  >
                    {sampleQuery}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {error && (
            <div className="mt-4 bg-red-50 border border-red-200 rounded-md p-4">
              <div className="text-sm text-red-700">{error}</div>
            </div>
          )}

          {/* Query Results */}
          {queryResult && (
            <div className="mt-6">
              <h4 className="text-md font-medium text-white mb-3">Query Results</h4>
              <div className="bg-gray-700 rounded-md p-4 overflow-x-auto">
                <pre className="text-sm text-gray-300">
                  {JSON.stringify(queryResult, null, 2)}
                </pre>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DatabaseAdmin;
