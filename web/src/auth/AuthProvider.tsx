import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';

// Authentication types based on Clutch implementation
interface User {
  sub: string;
  email: string;
  name: string;
  groups: string[];
  roles: string[];
  permissions: string[];
  preferences: {
    theme: string;
    timezone: string;
    language: string;
  };
}

interface AuthToken {
  access_token: string;
  refresh_token?: string;
  token_type: string;
  expires_in: number;
}

interface AuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: User | null;
  token: AuthToken | null;
  error: string | null;
}

interface AuthContextType extends AuthState {
  login: (redirectUrl?: string) => Promise<void>;
  logout: () => Promise<void>;
  checkPermission: (action: string, resource: string) => Promise<boolean>;
  refreshToken: () => Promise<void>;
}

// Create auth context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth provider component
interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    isLoading: true,
    user: null,
    token: null,
    error: null,
  });

  // Initialize authentication on mount
  useEffect(() => {
    initializeAuth();
  }, []);

  // Initialize authentication
  const initializeAuth = async () => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

      // Check if user is already authenticated
      const response = await fetch('/v1/auth/user', {
        credentials: 'include',
      });

      if (response.ok) {
        const user = await response.json();
        setAuthState(prev => ({
          ...prev,
          isAuthenticated: true,
          user,
          isLoading: false,
        }));
      } else {
        // Try to refresh token
        await refreshToken();
      }
    } catch (error) {
      console.error('Auth initialization failed:', error);
      setAuthState(prev => ({
        ...prev,
        isAuthenticated: false,
        isLoading: false,
        error: 'Authentication initialization failed',
      }));
    }
  };

  // Login function
  const login = async (redirectUrl: string = '/') => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

      // Get auth URL from backend
      const response = await fetch(`/v1/auth/login?redirect_url=${encodeURIComponent(redirectUrl)}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to initiate login');
      }

      const data = await response.json();

      if (data.token) {
        // User was already authenticated, use existing token
        const userResponse = await fetch('/v1/auth/user', {
          credentials: 'include',
        });

        if (userResponse.ok) {
          const user = await userResponse.json();
          setAuthState(prev => ({
            ...prev,
            isAuthenticated: true,
            user,
            token: data.token,
            isLoading: false,
          }));
          
          // Redirect to the intended URL
          window.location.href = redirectUrl;
          return;
        }
      }

      // Redirect to OAuth provider
      if (data.auth_url) {
        window.location.href = data.auth_url;
      } else {
        throw new Error('No auth URL provided');
      }
    } catch (error) {
      console.error('Login failed:', error);
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Login failed',
      }));
    }
  };

  // Logout function
  const logout = async () => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true }));

      const response = await fetch('/v1/auth/logout', {
        method: 'POST',
        credentials: 'include',
      });

      const data = await response.json();

      setAuthState({
        isAuthenticated: false,
        isLoading: false,
        user: null,
        token: null,
        error: null,
      });

      // Redirect to logout URL if provided
      if (data.logout_url) {
        window.location.href = data.logout_url;
      } else {
        window.location.href = '/';
      }
    } catch (error) {
      console.error('Logout failed:', error);
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Logout failed',
      }));
    }
  };

  // Check permission function
  const checkPermission = async (action: string, resource: string): Promise<boolean> => {
    try {
      const response = await fetch('/v1/auth/check', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ action, resource }),
      });

      if (!response.ok) {
        return false;
      }

      const data = await response.json();
      return data.decision?.allowed || false;
    } catch (error) {
      console.error('Permission check failed:', error);
      return false;
    }
  };

  // Refresh token function
  const refreshToken = async () => {
    try {
      const response = await fetch('/v1/auth/refresh', {
        method: 'POST',
        credentials: 'include',
      });

      if (response.ok) {
        const tokenData = await response.json();
        
        // Get updated user info
        const userResponse = await fetch('/v1/auth/user', {
          credentials: 'include',
        });

        if (userResponse.ok) {
          const user = await userResponse.json();
          setAuthState(prev => ({
            ...prev,
            isAuthenticated: true,
            user,
            token: tokenData,
            isLoading: false,
            error: null,
          }));
        }
      } else {
        throw new Error('Token refresh failed');
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
      setAuthState(prev => ({
        ...prev,
        isAuthenticated: false,
        isLoading: false,
        user: null,
        token: null,
        error: 'Session expired',
      }));
    }
  };

  // Auto-refresh token before expiry
  useEffect(() => {
    if (authState.token && authState.isAuthenticated) {
      const refreshInterval = setInterval(() => {
        refreshToken();
      }, 50 * 60 * 1000); // Refresh every 50 minutes (token expires in 60 minutes)

      return () => clearInterval(refreshInterval);
    }
  }, [authState.token, authState.isAuthenticated]);

  const contextValue: AuthContextType = {
    ...authState,
    login,
    logout,
    checkPermission,
    refreshToken,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// Hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Higher-order component for protected routes
interface ProtectedRouteProps {
  children: ReactNode;
  requiredPermission?: { action: string; resource: string };
  fallback?: ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredPermission,
  fallback = <div>Access Denied</div>,
}) => {
  const { isAuthenticated, isLoading, checkPermission } = useAuth();
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);

  useEffect(() => {
    if (isAuthenticated && requiredPermission) {
      checkPermission(requiredPermission.action, requiredPermission.resource)
        .then(setHasPermission);
    } else if (isAuthenticated) {
      setHasPermission(true);
    }
  }, [isAuthenticated, requiredPermission, checkPermission]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!isAuthenticated) {
    return <div>Please log in to access this page.</div>;
  }

  if (requiredPermission && hasPermission === false) {
    return <>{fallback}</>;
  }

  if (requiredPermission && hasPermission === null) {
    return <div>Checking permissions...</div>;
  }

  return <>{children}</>;
};

// Login component
export const LoginPage: React.FC = () => {
  const { login, isLoading, error } = useAuth();

  const handleLogin = () => {
    const redirectUrl = new URLSearchParams(window.location.search).get('redirect') || '/';
    login(redirectUrl);
  };

  return (
    <div style={{ 
      display: 'flex', 
      flexDirection: 'column', 
      alignItems: 'center', 
      justifyContent: 'center', 
      height: '100vh',
      padding: '20px'
    }}>
      <h1>CAINuro Orchestrator</h1>
      <p>Please log in to continue</p>
      
      {error && (
        <div style={{ color: 'red', marginBottom: '20px' }}>
          {error}
        </div>
      )}
      
      <button 
        onClick={handleLogin} 
        disabled={isLoading}
        style={{
          padding: '12px 24px',
          fontSize: '16px',
          backgroundColor: '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: isLoading ? 'not-allowed' : 'pointer',
          opacity: isLoading ? 0.6 : 1,
        }}
      >
        {isLoading ? 'Logging in...' : 'Log in with SSO'}
      </button>
      
      <div style={{ marginTop: '20px', fontSize: '14px', color: '#666' }}>
        <p>Features:</p>
        <ul style={{ textAlign: 'left' }}>
          <li>Single Sign-On (SSO) with OIDC/OAuth2</li>
          <li>Role-based access control (RBAC)</li>
          <li>Multi-factor authentication (MFA)</li>
          <li>Session management with refresh tokens</li>
          <li>Audit logging</li>
        </ul>
      </div>
    </div>
  );
};

export default AuthProvider;
