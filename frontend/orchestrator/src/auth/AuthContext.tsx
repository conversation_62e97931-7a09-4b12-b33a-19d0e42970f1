import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';

// Authentication types based on Clutch implementation
interface User {
  sub: string;
  email: string;
  name: string;
  groups: string[];
  roles: string[];
  permissions: string[];
  preferences: {
    theme: string;
    timezone: string;
    language: string;
  };
}

interface AuthToken {
  access_token: string;
  refresh_token?: string;
  token_type: string;
  expires_in: number;
}

interface AuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: User | null;
  token: AuthToken | null;
  error: string | null;
}

interface AuthContextType extends AuthState {
  login: (redirectUrl?: string) => Promise<void>;
  logout: () => Promise<void>;
  checkPermission: (action: string, resource: string) => Promise<boolean>;
  refreshToken: () => Promise<void>;
}

// Create auth context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth provider component
interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    isLoading: true,
    user: null,
    token: null,
    error: null,
  });

  // Initialize authentication on mount
  useEffect(() => {
    initializeAuth();
  }, []);

  // Initialize authentication
  const initializeAuth = async () => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

      // Check if user is already authenticated
      const response = await fetch('/v1/auth/user', {
        credentials: 'include',
      });

      if (response.ok) {
        const user = await response.json();
        setAuthState(prev => ({
          ...prev,
          isAuthenticated: true,
          user,
          isLoading: false,
        }));
      } else {
        // Try to refresh token
        await refreshToken();
      }
    } catch (error) {
      console.error('Auth initialization failed:', error);
      setAuthState(prev => ({
        ...prev,
        isAuthenticated: false,
        isLoading: false,
        error: 'Authentication initialization failed',
      }));
    }
  };

  // Login function with demo OAuth simulation
  const login = async (redirectUrl: string = '/') => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

      // Get auth URL from backend
      const response = await fetch(`/v1/auth/login?redirect_url=${encodeURIComponent(redirectUrl)}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to initiate login');
      }

      const data = await response.json();

      if (data.token) {
        // User was already authenticated, use existing token
        const userResponse = await fetch('/v1/auth/user', {
          credentials: 'include',
        });

        if (userResponse.ok) {
          const user = await userResponse.json();
          setAuthState(prev => ({
            ...prev,
            isAuthenticated: true,
            user,
            token: data.token,
            isLoading: false,
          }));
          return;
        }
      }

      // For demo purposes, simulate OAuth callback instead of redirecting
      if (data.auth_url && data.state) {
        // In production, this would redirect to: window.location.href = data.auth_url;
        // For demo, simulate the OAuth callback
        const callbackResponse = await fetch('/v1/auth/callback', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({
            code: 'demo_auth_code_' + Date.now(),
            state: data.state,
          }),
        });

        if (callbackResponse.ok) {
          const tokenData = await callbackResponse.json();
          
          // Get user info
          const userResponse = await fetch('/v1/auth/user', {
            credentials: 'include',
          });

          if (userResponse.ok) {
            const user = await userResponse.json();
            setAuthState(prev => ({
              ...prev,
              isAuthenticated: true,
              user,
              token: tokenData,
              isLoading: false,
            }));
          }
        } else {
          const errorData = await callbackResponse.json();
          throw new Error(errorData.error || 'OAuth callback failed');
        }
      }
    } catch (error) {
      console.error('Login failed:', error);
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Login failed',
      }));
    }
  };

  // Logout function
  const logout = async () => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true }));

      const response = await fetch('/v1/auth/logout', {
        method: 'POST',
        credentials: 'include',
      });

      setAuthState({
        isAuthenticated: false,
        isLoading: false,
        user: null,
        token: null,
        error: null,
      });

      // Optionally redirect to logout URL if provided
      if (response.ok) {
        const data = await response.json();
        if (data.logout_url && data.logout_url !== window.location.origin) {
          window.location.href = data.logout_url;
          return;
        }
      }
      
      // Reload page to clear any cached state
      window.location.reload();
    } catch (error) {
      console.error('Logout failed:', error);
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Logout failed',
      }));
    }
  };

  // Check permission function
  const checkPermission = async (action: string, resource: string): Promise<boolean> => {
    try {
      const response = await fetch('/v1/auth/check', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ action, resource }),
      });

      if (!response.ok) {
        return false;
      }

      const data = await response.json();
      return data.decision?.allowed || false;
    } catch (error) {
      console.error('Permission check failed:', error);
      return false;
    }
  };

  // Refresh token function
  const refreshToken = async () => {
    try {
      const response = await fetch('/v1/auth/refresh', {
        method: 'POST',
        credentials: 'include',
      });

      if (response.ok) {
        const tokenData = await response.json();
        
        // Get updated user info
        const userResponse = await fetch('/v1/auth/user', {
          credentials: 'include',
        });

        if (userResponse.ok) {
          const user = await userResponse.json();
          setAuthState(prev => ({
            ...prev,
            isAuthenticated: true,
            user,
            token: tokenData,
            isLoading: false,
            error: null,
          }));
        }
      } else {
        throw new Error('Token refresh failed');
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
      setAuthState(prev => ({
        ...prev,
        isAuthenticated: false,
        isLoading: false,
        user: null,
        token: null,
        error: 'Session expired',
      }));
    }
  };

  // Auto-refresh token before expiry
  useEffect(() => {
    if (authState.token && authState.isAuthenticated) {
      const refreshInterval = setInterval(() => {
        refreshToken();
      }, 50 * 60 * 1000); // Refresh every 50 minutes (token expires in 60 minutes)

      return () => clearInterval(refreshInterval);
    }
  }, [authState.token, authState.isAuthenticated]);

  const contextValue: AuthContextType = {
    ...authState,
    login,
    logout,
    checkPermission,
    refreshToken,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// Hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Higher-order component for protected routes
interface ProtectedRouteProps {
  children: ReactNode;
  requiredPermission?: { action: string; resource: string };
  fallback?: ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredPermission,
  fallback = <div className="p-4 text-red-600">Access Denied</div>,
}) => {
  const { isAuthenticated, isLoading, checkPermission } = useAuth();
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);

  useEffect(() => {
    if (isAuthenticated && requiredPermission) {
      checkPermission(requiredPermission.action, requiredPermission.resource)
        .then(setHasPermission);
    } else if (isAuthenticated) {
      setHasPermission(true);
    }
  }, [isAuthenticated, requiredPermission, checkPermission]);

  if (isLoading) {
    return <div className="p-4">Loading...</div>;
  }

  if (!isAuthenticated) {
    return <div className="p-4">Please log in to access this page.</div>;
  }

  if (requiredPermission && hasPermission === false) {
    return <>{fallback}</>;
  }

  if (requiredPermission && hasPermission === null) {
    return <div className="p-4">Checking permissions...</div>;
  }

  return <>{children}</>;
};
