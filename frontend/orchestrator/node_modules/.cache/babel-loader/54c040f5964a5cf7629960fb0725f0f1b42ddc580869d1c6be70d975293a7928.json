{"ast": null, "code": "import React,{useEffect,useState}from'react';import{Link}from'react-router-dom';import{useAuth}from'../auth/AuthContext';import{CogIcon,MagnifyingGlassIcon,GlobeAltIcon,ArrowTrendingUpIcon,ShieldCheckIcon,CircleStackIcon,UsersIcon,ServerIcon,ClockIcon,CheckCircleIcon,ExclamationTriangleIcon,PlayIcon,EyeIcon}from'@heroicons/react/24/outline';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const serviceIcons={audit:ShieldCheckIcon,autoscaler:ArrowTrendingUpIcon,db_admin:CircleStackIcon,discovery:MagnifyingGlassIcon,envoy_control_plane:GlobeAltIcon,workflow:CogIcon};const quickActions=[{name:'Discovery Wizard',description:'Search across AWS, GCP, and Azure resources',icon:MagnifyingGlassIcon,href:'/discovery',color:'bg-blue-500'},{name:'Run Workflow',description:'Execute automation workflows',icon:PlayIcon,href:'/workflows',color:'bg-green-500'},{name:'Search Resources',description:'Advanced resource search and filtering',icon:EyeIcon,href:'/search',color:'bg-purple-500'},{name:'Envoy Config',description:'Manage Envoy proxy configurations',icon:GlobeAltIcon,href:'/envoy',color:'bg-orange-500'},{name:'Audit Logs',description:'View tamper-proof audit trail',icon:ShieldCheckIcon,href:'/audit',color:'bg-red-500'},{name:'Database Admin',description:'Manage database connections and queries',icon:CircleStackIcon,href:'/database',color:'bg-indigo-500'}];export function Dashboard(){const{user}=useAuth();const[health,setHealth]=useState(null);const[stats,setStats]=useState(null);const[loading,setLoading]=useState(true);useEffect(()=>{const fetchData=async()=>{try{// Fetch health status\nconst healthResponse=await fetch('/health');const healthData=await healthResponse.json();setHealth(healthData);// Fetch dashboard stats\nconst statsResponse=await fetch('/v1/admin/stats',{credentials:'include'});if(statsResponse.ok){const statsData=await statsResponse.json();setStats({totalUsers:statsData.totalUsers||0,activeConnections:statsData.activeConnections||0,runningWorkflows:statsData.runningWorkflows||0,systemUptime:statsData.systemUptime||'Unknown'});}}catch(error){console.error('Failed to fetch dashboard data:',error);}finally{setLoading(false);}};fetchData();const interval=setInterval(fetchData,30000);// Refresh every 30 seconds\nreturn()=>clearInterval(interval);},[]);if(loading){return/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center h-64\",children:/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"})});}return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white shadow rounded-lg p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-900\",children:\"CAINuro Orchestrator\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-gray-600\",children:[\"Welcome back, \",user===null||user===void 0?void 0:user.name]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center space-x-2\",children:(health===null||health===void 0?void 0:health.status)==='healthy'?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(CheckCircleIcon,{className:\"h-8 w-8 text-green-500\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-green-600\",children:\"All Systems Operational\"})]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(ExclamationTriangleIcon,{className:\"h-8 w-8 text-red-500\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-red-600\",children:\"System Issues Detected\"})]})})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white overflow-hidden shadow rounded-lg\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"p-5\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(UsersIcon,{className:\"h-6 w-6 text-gray-400\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"ml-5 w-0 flex-1\",children:/*#__PURE__*/_jsxs(\"dl\",{children:[/*#__PURE__*/_jsx(\"dt\",{className:\"text-sm font-medium text-gray-500 truncate\",children:\"Total Users\"}),/*#__PURE__*/_jsx(\"dd\",{className:\"text-lg font-medium text-gray-900\",children:(stats===null||stats===void 0?void 0:stats.totalUsers)||0})]})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-gray-50 px-5 py-3\",children:/*#__PURE__*/_jsx(\"div\",{className:\"text-sm\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-green-600 font-medium\",children:\"Active\"})})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white overflow-hidden shadow rounded-lg\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"p-5\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(ServerIcon,{className:\"h-6 w-6 text-gray-400\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"ml-5 w-0 flex-1\",children:/*#__PURE__*/_jsxs(\"dl\",{children:[/*#__PURE__*/_jsx(\"dt\",{className:\"text-sm font-medium text-gray-500 truncate\",children:\"Connections\"}),/*#__PURE__*/_jsx(\"dd\",{className:\"text-lg font-medium text-gray-900\",children:(stats===null||stats===void 0?void 0:stats.activeConnections)||0})]})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-gray-50 px-5 py-3\",children:/*#__PURE__*/_jsx(\"div\",{className:\"text-sm\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-blue-600 font-medium\",children:\"Active\"})})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white overflow-hidden shadow rounded-lg\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"p-5\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(CogIcon,{className:\"h-6 w-6 text-gray-400\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"ml-5 w-0 flex-1\",children:/*#__PURE__*/_jsxs(\"dl\",{children:[/*#__PURE__*/_jsx(\"dt\",{className:\"text-sm font-medium text-gray-500 truncate\",children:\"Workflows\"}),/*#__PURE__*/_jsx(\"dd\",{className:\"text-lg font-medium text-gray-900\",children:(stats===null||stats===void 0?void 0:stats.runningWorkflows)||0})]})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-gray-50 px-5 py-3\",children:/*#__PURE__*/_jsx(\"div\",{className:\"text-sm\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-purple-600 font-medium\",children:\"Running\"})})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white overflow-hidden shadow rounded-lg\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"p-5\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(ClockIcon,{className:\"h-6 w-6 text-gray-400\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"ml-5 w-0 flex-1\",children:/*#__PURE__*/_jsxs(\"dl\",{children:[/*#__PURE__*/_jsx(\"dt\",{className:\"text-sm font-medium text-gray-500 truncate\",children:\"Uptime\"}),/*#__PURE__*/_jsx(\"dd\",{className:\"text-lg font-medium text-gray-900\",children:(stats===null||stats===void 0?void 0:stats.systemUptime)||'Unknown'})]})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-gray-50 px-5 py-3\",children:/*#__PURE__*/_jsx(\"div\",{className:\"text-sm\",children:/*#__PURE__*/_jsxs(\"span\",{className:\"text-green-600 font-medium\",children:[health===null||health===void 0?void 0:health.mode,\" Mode\"]})})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white shadow rounded-lg\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"px-6 py-4 border-b border-gray-200\",children:/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900\",children:\"System Services\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"p-6\",children:/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3\",children:(health===null||health===void 0?void 0:health.services)&&Object.entries(health.services).map(_ref=>{let[serviceName,status]=_ref;const IconComponent=serviceIcons[serviceName]||CogIcon;const isActive=status==='active';return/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center p-4 border border-gray-200 rounded-lg\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(IconComponent,{className:\"h-6 w-6 \".concat(isActive?'text-green-500':'text-red-500')})}),/*#__PURE__*/_jsxs(\"div\",{className:\"ml-4 flex-1\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"text-sm font-medium text-gray-900\",children:serviceName.replace(/_/g,' ').replace(/\\b\\w/g,l=>l.toUpperCase())}),/*#__PURE__*/_jsx(\"span\",{className:\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(isActive?'bg-green-100 text-green-800':'bg-red-100 text-red-800'),children:status})]})]},serviceName);})})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white shadow rounded-lg\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"px-6 py-4 border-b border-gray-200\",children:/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900\",children:\"Quick Actions\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"p-6\",children:/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3\",children:quickActions.map(action=>{const IconComponent=action.icon;return/*#__PURE__*/_jsxs(Link,{to:action.href,className:\"relative group bg-white p-6 border border-gray-200 rounded-lg hover:border-gray-300 hover:shadow-md transition-all duration-200\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"span\",{className:\"rounded-lg inline-flex p-3 \".concat(action.color,\" text-white\"),children:/*#__PURE__*/_jsx(IconComponent,{className:\"h-6 w-6\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-4\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 group-hover:text-blue-600\",children:action.name}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-sm text-gray-500\",children:action.description})]})]},action.name);})})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white shadow rounded-lg\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"px-6 py-4 border-b border-gray-200\",children:/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900\",children:\"System Information\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"p-6\",children:/*#__PURE__*/_jsxs(\"dl\",{className:\"grid grid-cols-1 gap-6 sm:grid-cols-3\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"dt\",{className:\"text-sm font-medium text-gray-500\",children:\"Version\"}),/*#__PURE__*/_jsx(\"dd\",{className:\"mt-1 text-lg font-semibold text-gray-900\",children:health===null||health===void 0?void 0:health.version})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"dt\",{className:\"text-sm font-medium text-gray-500\",children:\"Mode\"}),/*#__PURE__*/_jsx(\"dd\",{className:\"mt-1 text-lg font-semibold text-gray-900\",children:/*#__PURE__*/_jsx(\"span\",{className:\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800\",children:health===null||health===void 0?void 0:health.mode})})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"dt\",{className:\"text-sm font-medium text-gray-500\",children:\"Active Services\"}),/*#__PURE__*/_jsxs(\"dd\",{className:\"mt-1 text-lg font-semibold text-gray-900\",children:[health!==null&&health!==void 0&&health.services?Object.keys(health.services).length:0,\" / \",health!==null&&health!==void 0&&health.services?Object.keys(health.services).length:0]})]})]})})]})]});}", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Link", "useAuth", "CogIcon", "MagnifyingGlassIcon", "GlobeAltIcon", "ArrowTrendingUpIcon", "ShieldCheckIcon", "CircleStackIcon", "UsersIcon", "ServerIcon", "ClockIcon", "CheckCircleIcon", "ExclamationTriangleIcon", "PlayIcon", "EyeIcon", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "serviceIcons", "audit", "autoscaler", "db_admin", "discovery", "envoy_control_plane", "workflow", "quickActions", "name", "description", "icon", "href", "color", "Dashboard", "user", "health", "setHealth", "stats", "setStats", "loading", "setLoading", "fetchData", "healthResponse", "fetch", "healthData", "json", "statsResponse", "credentials", "ok", "statsData", "totalUsers", "activeConnections", "runningWorkflows", "systemUptime", "error", "console", "interval", "setInterval", "clearInterval", "className", "children", "status", "mode", "services", "Object", "entries", "map", "_ref", "serviceName", "IconComponent", "isActive", "concat", "replace", "l", "toUpperCase", "action", "to", "version", "keys", "length"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/Dashboard.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport { useAuth } from '../auth/AuthContext';\nimport {\n  ChartBarIcon,\n  CogIcon,\n  MagnifyingGlassIcon,\n  GlobeAltIcon,\n  ArrowTrendingUpIcon,\n  ShieldCheckIcon,\n  CircleStackIcon,\n  UsersIcon,\n  ServerIcon,\n  ClockIcon,\n  CheckCircleIcon,\n  ExclamationTriangleIcon,\n  PlayIcon,\n  EyeIcon,\n} from '@heroicons/react/24/outline';\n\ninterface HealthStatus {\n  mode: string;\n  services: Record<string, string>;\n  status: string;\n  version: string;\n}\n\ninterface DashboardStats {\n  totalUsers: number;\n  activeConnections: number;\n  runningWorkflows: number;\n  systemUptime: string;\n}\n\ninterface QuickAction {\n  name: string;\n  description: string;\n  icon: any;\n  href: string;\n  color: string;\n}\n\nconst serviceIcons: Record<string, any> = {\n  audit: ShieldCheckIcon,\n  autoscaler: ArrowTrendingUpIcon,\n  db_admin: CircleStackIcon,\n  discovery: MagnifyingGlassIcon,\n  envoy_control_plane: GlobeAltIcon,\n  workflow: CogIcon,\n};\n\nconst quickActions: QuickAction[] = [\n  {\n    name: 'Discovery Wizard',\n    description: 'Search across AWS, GCP, and Azure resources',\n    icon: MagnifyingGlassIcon,\n    href: '/discovery',\n    color: 'bg-blue-500',\n  },\n  {\n    name: 'Run Workflow',\n    description: 'Execute automation workflows',\n    icon: PlayIcon,\n    href: '/workflows',\n    color: 'bg-green-500',\n  },\n  {\n    name: 'Search Resources',\n    description: 'Advanced resource search and filtering',\n    icon: EyeIcon,\n    href: '/search',\n    color: 'bg-purple-500',\n  },\n  {\n    name: 'Envoy Config',\n    description: 'Manage Envoy proxy configurations',\n    icon: GlobeAltIcon,\n    href: '/envoy',\n    color: 'bg-orange-500',\n  },\n  {\n    name: 'Audit Logs',\n    description: 'View tamper-proof audit trail',\n    icon: ShieldCheckIcon,\n    href: '/audit',\n    color: 'bg-red-500',\n  },\n  {\n    name: 'Database Admin',\n    description: 'Manage database connections and queries',\n    icon: CircleStackIcon,\n    href: '/database',\n    color: 'bg-indigo-500',\n  },\n];\n\nexport function Dashboard() {\n  const { user } = useAuth();\n  const [health, setHealth] = useState<HealthStatus | null>(null);\n  const [stats, setStats] = useState<DashboardStats | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        // Fetch health status\n        const healthResponse = await fetch('/health');\n        const healthData = await healthResponse.json();\n        setHealth(healthData);\n\n        // Fetch dashboard stats\n        const statsResponse = await fetch('/v1/admin/stats', {\n          credentials: 'include',\n        });\n        if (statsResponse.ok) {\n          const statsData = await statsResponse.json();\n          setStats({\n            totalUsers: statsData.totalUsers || 0,\n            activeConnections: statsData.activeConnections || 0,\n            runningWorkflows: statsData.runningWorkflows || 0,\n            systemUptime: statsData.systemUptime || 'Unknown',\n          });\n        }\n      } catch (error) {\n        console.error('Failed to fetch dashboard data:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n    const interval = setInterval(fetchData, 30000); // Refresh every 30 seconds\n\n    return () => clearInterval(interval);\n  }, []);\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">CAINuro Orchestrator</h1>\n            <p className=\"text-gray-600\">Welcome back, {user?.name}</p>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            {health?.status === 'healthy' ? (\n              <>\n                <CheckCircleIcon className=\"h-8 w-8 text-green-500\" />\n                <span className=\"text-sm font-medium text-green-600\">All Systems Operational</span>\n              </>\n            ) : (\n              <>\n                <ExclamationTriangleIcon className=\"h-8 w-8 text-red-500\" />\n                <span className=\"text-sm font-medium text-red-600\">System Issues Detected</span>\n              </>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Statistics Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <UsersIcon className=\"h-6 w-6 text-gray-400\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">Total Users</dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">{stats?.totalUsers || 0}</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n          <div className=\"bg-gray-50 px-5 py-3\">\n            <div className=\"text-sm\">\n              <span className=\"text-green-600 font-medium\">Active</span>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <ServerIcon className=\"h-6 w-6 text-gray-400\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">Connections</dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">{stats?.activeConnections || 0}</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n          <div className=\"bg-gray-50 px-5 py-3\">\n            <div className=\"text-sm\">\n              <span className=\"text-blue-600 font-medium\">Active</span>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <CogIcon className=\"h-6 w-6 text-gray-400\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">Workflows</dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">{stats?.runningWorkflows || 0}</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n          <div className=\"bg-gray-50 px-5 py-3\">\n            <div className=\"text-sm\">\n              <span className=\"text-purple-600 font-medium\">Running</span>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <ClockIcon className=\"h-6 w-6 text-gray-400\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">Uptime</dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">{stats?.systemUptime || 'Unknown'}</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n          <div className=\"bg-gray-50 px-5 py-3\">\n            <div className=\"text-sm\">\n              <span className=\"text-green-600 font-medium\">{health?.mode} Mode</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Services Status */}\n      <div className=\"bg-white shadow rounded-lg\">\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <h3 className=\"text-lg font-medium text-gray-900\">System Services</h3>\n        </div>\n        <div className=\"p-6\">\n          <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3\">\n            {health?.services && Object.entries(health.services).map(([serviceName, status]) => {\n              const IconComponent = serviceIcons[serviceName] || CogIcon;\n              const isActive = status === 'active';\n\n              return (\n                <div key={serviceName} className=\"flex items-center p-4 border border-gray-200 rounded-lg\">\n                  <div className=\"flex-shrink-0\">\n                    <IconComponent className={`h-6 w-6 ${isActive ? 'text-green-500' : 'text-red-500'}`} />\n                  </div>\n                  <div className=\"ml-4 flex-1\">\n                    <h4 className=\"text-sm font-medium text-gray-900\">\n                      {serviceName.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase())}\n                    </h4>\n                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                      isActive\n                        ? 'bg-green-100 text-green-800'\n                        : 'bg-red-100 text-red-800'\n                    }`}>\n                      {status}\n                    </span>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"bg-white shadow rounded-lg\">\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <h3 className=\"text-lg font-medium text-gray-900\">Quick Actions</h3>\n        </div>\n        <div className=\"p-6\">\n          <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3\">\n            {quickActions.map((action) => {\n              const IconComponent = action.icon;\n              return (\n                <Link\n                  key={action.name}\n                  to={action.href}\n                  className=\"relative group bg-white p-6 border border-gray-200 rounded-lg hover:border-gray-300 hover:shadow-md transition-all duration-200\"\n                >\n                  <div>\n                    <span className={`rounded-lg inline-flex p-3 ${action.color} text-white`}>\n                      <IconComponent className=\"h-6 w-6\" />\n                    </span>\n                  </div>\n                  <div className=\"mt-4\">\n                    <h3 className=\"text-lg font-medium text-gray-900 group-hover:text-blue-600\">\n                      {action.name}\n                    </h3>\n                    <p className=\"mt-2 text-sm text-gray-500\">\n                      {action.description}\n                    </p>\n                  </div>\n                </Link>\n              );\n            })}\n          </div>\n        </div>\n      </div>\n\n      {/* System Information */}\n      <div className=\"bg-white shadow rounded-lg\">\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <h3 className=\"text-lg font-medium text-gray-900\">System Information</h3>\n        </div>\n        <div className=\"p-6\">\n          <dl className=\"grid grid-cols-1 gap-6 sm:grid-cols-3\">\n            <div>\n              <dt className=\"text-sm font-medium text-gray-500\">Version</dt>\n              <dd className=\"mt-1 text-lg font-semibold text-gray-900\">{health?.version}</dd>\n            </div>\n            <div>\n              <dt className=\"text-sm font-medium text-gray-500\">Mode</dt>\n              <dd className=\"mt-1 text-lg font-semibold text-gray-900\">\n                <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800\">\n                  {health?.mode}\n                </span>\n              </dd>\n            </div>\n            <div>\n              <dt className=\"text-sm font-medium text-gray-500\">Active Services</dt>\n              <dd className=\"mt-1 text-lg font-semibold text-gray-900\">\n                {health?.services ? Object.keys(health.services).length : 0} / {health?.services ? Object.keys(health.services).length : 0}\n              </dd>\n            </div>\n          </dl>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,IAAI,KAAQ,kBAAkB,CACvC,OAASC,OAAO,KAAQ,qBAAqB,CAC7C,OAEEC,OAAO,CACPC,mBAAmB,CACnBC,YAAY,CACZC,mBAAmB,CACnBC,eAAe,CACfC,eAAe,CACfC,SAAS,CACTC,UAAU,CACVC,SAAS,CACTC,eAAe,CACfC,uBAAuB,CACvBC,QAAQ,CACRC,OAAO,KACF,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAwBrC,KAAM,CAAAC,YAAiC,CAAG,CACxCC,KAAK,CAAEhB,eAAe,CACtBiB,UAAU,CAAElB,mBAAmB,CAC/BmB,QAAQ,CAAEjB,eAAe,CACzBkB,SAAS,CAAEtB,mBAAmB,CAC9BuB,mBAAmB,CAAEtB,YAAY,CACjCuB,QAAQ,CAAEzB,OACZ,CAAC,CAED,KAAM,CAAA0B,YAA2B,CAAG,CAClC,CACEC,IAAI,CAAE,kBAAkB,CACxBC,WAAW,CAAE,6CAA6C,CAC1DC,IAAI,CAAE5B,mBAAmB,CACzB6B,IAAI,CAAE,YAAY,CAClBC,KAAK,CAAE,aACT,CAAC,CACD,CACEJ,IAAI,CAAE,cAAc,CACpBC,WAAW,CAAE,8BAA8B,CAC3CC,IAAI,CAAElB,QAAQ,CACdmB,IAAI,CAAE,YAAY,CAClBC,KAAK,CAAE,cACT,CAAC,CACD,CACEJ,IAAI,CAAE,kBAAkB,CACxBC,WAAW,CAAE,wCAAwC,CACrDC,IAAI,CAAEjB,OAAO,CACbkB,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,eACT,CAAC,CACD,CACEJ,IAAI,CAAE,cAAc,CACpBC,WAAW,CAAE,mCAAmC,CAChDC,IAAI,CAAE3B,YAAY,CAClB4B,IAAI,CAAE,QAAQ,CACdC,KAAK,CAAE,eACT,CAAC,CACD,CACEJ,IAAI,CAAE,YAAY,CAClBC,WAAW,CAAE,+BAA+B,CAC5CC,IAAI,CAAEzB,eAAe,CACrB0B,IAAI,CAAE,QAAQ,CACdC,KAAK,CAAE,YACT,CAAC,CACD,CACEJ,IAAI,CAAE,gBAAgB,CACtBC,WAAW,CAAE,yCAAyC,CACtDC,IAAI,CAAExB,eAAe,CACrByB,IAAI,CAAE,WAAW,CACjBC,KAAK,CAAE,eACT,CAAC,CACF,CAED,MAAO,SAAS,CAAAC,SAASA,CAAA,CAAG,CAC1B,KAAM,CAAEC,IAAK,CAAC,CAAGlC,OAAO,CAAC,CAAC,CAC1B,KAAM,CAACmC,MAAM,CAAEC,SAAS,CAAC,CAAGtC,QAAQ,CAAsB,IAAI,CAAC,CAC/D,KAAM,CAACuC,KAAK,CAAEC,QAAQ,CAAC,CAAGxC,QAAQ,CAAwB,IAAI,CAAC,CAC/D,KAAM,CAACyC,OAAO,CAAEC,UAAU,CAAC,CAAG1C,QAAQ,CAAC,IAAI,CAAC,CAE5CD,SAAS,CAAC,IAAM,CACd,KAAM,CAAA4C,SAAS,CAAG,KAAAA,CAAA,GAAY,CAC5B,GAAI,CACF;AACA,KAAM,CAAAC,cAAc,CAAG,KAAM,CAAAC,KAAK,CAAC,SAAS,CAAC,CAC7C,KAAM,CAAAC,UAAU,CAAG,KAAM,CAAAF,cAAc,CAACG,IAAI,CAAC,CAAC,CAC9CT,SAAS,CAACQ,UAAU,CAAC,CAErB;AACA,KAAM,CAAAE,aAAa,CAAG,KAAM,CAAAH,KAAK,CAAC,iBAAiB,CAAE,CACnDI,WAAW,CAAE,SACf,CAAC,CAAC,CACF,GAAID,aAAa,CAACE,EAAE,CAAE,CACpB,KAAM,CAAAC,SAAS,CAAG,KAAM,CAAAH,aAAa,CAACD,IAAI,CAAC,CAAC,CAC5CP,QAAQ,CAAC,CACPY,UAAU,CAAED,SAAS,CAACC,UAAU,EAAI,CAAC,CACrCC,iBAAiB,CAAEF,SAAS,CAACE,iBAAiB,EAAI,CAAC,CACnDC,gBAAgB,CAAEH,SAAS,CAACG,gBAAgB,EAAI,CAAC,CACjDC,YAAY,CAAEJ,SAAS,CAACI,YAAY,EAAI,SAC1C,CAAC,CAAC,CACJ,CACF,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,CAAEA,KAAK,CAAC,CACzD,CAAC,OAAS,CACRd,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAEDC,SAAS,CAAC,CAAC,CACX,KAAM,CAAAe,QAAQ,CAAGC,WAAW,CAAChB,SAAS,CAAE,KAAK,CAAC,CAAE;AAEhD,MAAO,IAAMiB,aAAa,CAACF,QAAQ,CAAC,CACtC,CAAC,CAAE,EAAE,CAAC,CAEN,GAAIjB,OAAO,CAAE,CACX,mBACExB,IAAA,QAAK4C,SAAS,CAAC,uCAAuC,CAAAC,QAAA,cACpD7C,IAAA,QAAK4C,SAAS,CAAC,gEAAgE,CAAM,CAAC,CACnF,CAAC,CAEV,CAEA,mBACE1C,KAAA,QAAK0C,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExB7C,IAAA,QAAK4C,SAAS,CAAC,gCAAgC,CAAAC,QAAA,cAC7C3C,KAAA,QAAK0C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChD3C,KAAA,QAAA2C,QAAA,eACE7C,IAAA,OAAI4C,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,sBAAoB,CAAI,CAAC,cAC1E3C,KAAA,MAAG0C,SAAS,CAAC,eAAe,CAAAC,QAAA,EAAC,gBAAc,CAAC1B,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEN,IAAI,EAAI,CAAC,EACxD,CAAC,cACNb,IAAA,QAAK4C,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CACzC,CAAAzB,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAE0B,MAAM,IAAK,SAAS,cAC3B5C,KAAA,CAAAE,SAAA,EAAAyC,QAAA,eACE7C,IAAA,CAACL,eAAe,EAACiD,SAAS,CAAC,wBAAwB,CAAE,CAAC,cACtD5C,IAAA,SAAM4C,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAAC,yBAAuB,CAAM,CAAC,EACnF,CAAC,cAEH3C,KAAA,CAAAE,SAAA,EAAAyC,QAAA,eACE7C,IAAA,CAACJ,uBAAuB,EAACgD,SAAS,CAAC,sBAAsB,CAAE,CAAC,cAC5D5C,IAAA,SAAM4C,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,wBAAsB,CAAM,CAAC,EAChF,CACH,CACE,CAAC,EACH,CAAC,CACH,CAAC,cAGN3C,KAAA,QAAK0C,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eACnE3C,KAAA,QAAK0C,SAAS,CAAC,4CAA4C,CAAAC,QAAA,eACzD7C,IAAA,QAAK4C,SAAS,CAAC,KAAK,CAAAC,QAAA,cAClB3C,KAAA,QAAK0C,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC7C,IAAA,QAAK4C,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5B7C,IAAA,CAACR,SAAS,EAACoD,SAAS,CAAC,uBAAuB,CAAE,CAAC,CAC5C,CAAC,cACN5C,IAAA,QAAK4C,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B3C,KAAA,OAAA2C,QAAA,eACE7C,IAAA,OAAI4C,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CAAC,aAAW,CAAI,CAAC,cAC3E7C,IAAA,OAAI4C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAE,CAAAvB,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEa,UAAU,GAAI,CAAC,CAAK,CAAC,EAC7E,CAAC,CACF,CAAC,EACH,CAAC,CACH,CAAC,cACNnC,IAAA,QAAK4C,SAAS,CAAC,sBAAsB,CAAAC,QAAA,cACnC7C,IAAA,QAAK4C,SAAS,CAAC,SAAS,CAAAC,QAAA,cACtB7C,IAAA,SAAM4C,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,QAAM,CAAM,CAAC,CACvD,CAAC,CACH,CAAC,EACH,CAAC,cAEN3C,KAAA,QAAK0C,SAAS,CAAC,4CAA4C,CAAAC,QAAA,eACzD7C,IAAA,QAAK4C,SAAS,CAAC,KAAK,CAAAC,QAAA,cAClB3C,KAAA,QAAK0C,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC7C,IAAA,QAAK4C,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5B7C,IAAA,CAACP,UAAU,EAACmD,SAAS,CAAC,uBAAuB,CAAE,CAAC,CAC7C,CAAC,cACN5C,IAAA,QAAK4C,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B3C,KAAA,OAAA2C,QAAA,eACE7C,IAAA,OAAI4C,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CAAC,aAAW,CAAI,CAAC,cAC3E7C,IAAA,OAAI4C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAE,CAAAvB,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEc,iBAAiB,GAAI,CAAC,CAAK,CAAC,EACpF,CAAC,CACF,CAAC,EACH,CAAC,CACH,CAAC,cACNpC,IAAA,QAAK4C,SAAS,CAAC,sBAAsB,CAAAC,QAAA,cACnC7C,IAAA,QAAK4C,SAAS,CAAC,SAAS,CAAAC,QAAA,cACtB7C,IAAA,SAAM4C,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAC,QAAM,CAAM,CAAC,CACtD,CAAC,CACH,CAAC,EACH,CAAC,cAEN3C,KAAA,QAAK0C,SAAS,CAAC,4CAA4C,CAAAC,QAAA,eACzD7C,IAAA,QAAK4C,SAAS,CAAC,KAAK,CAAAC,QAAA,cAClB3C,KAAA,QAAK0C,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC7C,IAAA,QAAK4C,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5B7C,IAAA,CAACd,OAAO,EAAC0D,SAAS,CAAC,uBAAuB,CAAE,CAAC,CAC1C,CAAC,cACN5C,IAAA,QAAK4C,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B3C,KAAA,OAAA2C,QAAA,eACE7C,IAAA,OAAI4C,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CAAC,WAAS,CAAI,CAAC,cACzE7C,IAAA,OAAI4C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAE,CAAAvB,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEe,gBAAgB,GAAI,CAAC,CAAK,CAAC,EACnF,CAAC,CACF,CAAC,EACH,CAAC,CACH,CAAC,cACNrC,IAAA,QAAK4C,SAAS,CAAC,sBAAsB,CAAAC,QAAA,cACnC7C,IAAA,QAAK4C,SAAS,CAAC,SAAS,CAAAC,QAAA,cACtB7C,IAAA,SAAM4C,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,SAAO,CAAM,CAAC,CACzD,CAAC,CACH,CAAC,EACH,CAAC,cAEN3C,KAAA,QAAK0C,SAAS,CAAC,4CAA4C,CAAAC,QAAA,eACzD7C,IAAA,QAAK4C,SAAS,CAAC,KAAK,CAAAC,QAAA,cAClB3C,KAAA,QAAK0C,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC7C,IAAA,QAAK4C,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5B7C,IAAA,CAACN,SAAS,EAACkD,SAAS,CAAC,uBAAuB,CAAE,CAAC,CAC5C,CAAC,cACN5C,IAAA,QAAK4C,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B3C,KAAA,OAAA2C,QAAA,eACE7C,IAAA,OAAI4C,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CAAC,QAAM,CAAI,CAAC,cACtE7C,IAAA,OAAI4C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAE,CAAAvB,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEgB,YAAY,GAAI,SAAS,CAAK,CAAC,EACvF,CAAC,CACF,CAAC,EACH,CAAC,CACH,CAAC,cACNtC,IAAA,QAAK4C,SAAS,CAAC,sBAAsB,CAAAC,QAAA,cACnC7C,IAAA,QAAK4C,SAAS,CAAC,SAAS,CAAAC,QAAA,cACtB3C,KAAA,SAAM0C,SAAS,CAAC,4BAA4B,CAAAC,QAAA,EAAEzB,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAE2B,IAAI,CAAC,OAAK,EAAM,CAAC,CACpE,CAAC,CACH,CAAC,EACH,CAAC,EACH,CAAC,cAGN7C,KAAA,QAAK0C,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzC7C,IAAA,QAAK4C,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjD7C,IAAA,OAAI4C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,CACnE,CAAC,cACN7C,IAAA,QAAK4C,SAAS,CAAC,KAAK,CAAAC,QAAA,cAClB7C,IAAA,QAAK4C,SAAS,CAAC,sDAAsD,CAAAC,QAAA,CAClE,CAAAzB,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAE4B,QAAQ,GAAIC,MAAM,CAACC,OAAO,CAAC9B,MAAM,CAAC4B,QAAQ,CAAC,CAACG,GAAG,CAACC,IAAA,EAA2B,IAA1B,CAACC,WAAW,CAAEP,MAAM,CAAC,CAAAM,IAAA,CAC7E,KAAM,CAAAE,aAAa,CAAGjD,YAAY,CAACgD,WAAW,CAAC,EAAInE,OAAO,CAC1D,KAAM,CAAAqE,QAAQ,CAAGT,MAAM,GAAK,QAAQ,CAEpC,mBACE5C,KAAA,QAAuB0C,SAAS,CAAC,yDAAyD,CAAAC,QAAA,eACxF7C,IAAA,QAAK4C,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5B7C,IAAA,CAACsD,aAAa,EAACV,SAAS,YAAAY,MAAA,CAAaD,QAAQ,CAAG,gBAAgB,CAAG,cAAc,CAAG,CAAE,CAAC,CACpF,CAAC,cACNrD,KAAA,QAAK0C,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B7C,IAAA,OAAI4C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC9CQ,WAAW,CAACI,OAAO,CAAC,IAAI,CAAE,GAAG,CAAC,CAACA,OAAO,CAAC,OAAO,CAAEC,CAAC,EAAIA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,CACpE,CAAC,cACL3D,IAAA,SAAM4C,SAAS,4EAAAY,MAAA,CACbD,QAAQ,CACJ,6BAA6B,CAC7B,yBAAyB,CAC5B,CAAAV,QAAA,CACAC,MAAM,CACH,CAAC,EACJ,CAAC,GAfEO,WAgBL,CAAC,CAEV,CAAC,CAAC,CACC,CAAC,CACH,CAAC,EACH,CAAC,cAGNnD,KAAA,QAAK0C,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzC7C,IAAA,QAAK4C,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjD7C,IAAA,OAAI4C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,eAAa,CAAI,CAAC,CACjE,CAAC,cACN7C,IAAA,QAAK4C,SAAS,CAAC,KAAK,CAAAC,QAAA,cAClB7C,IAAA,QAAK4C,SAAS,CAAC,sDAAsD,CAAAC,QAAA,CAClEjC,YAAY,CAACuC,GAAG,CAAES,MAAM,EAAK,CAC5B,KAAM,CAAAN,aAAa,CAAGM,MAAM,CAAC7C,IAAI,CACjC,mBACEb,KAAA,CAAClB,IAAI,EAEH6E,EAAE,CAAED,MAAM,CAAC5C,IAAK,CAChB4B,SAAS,CAAC,iIAAiI,CAAAC,QAAA,eAE3I7C,IAAA,QAAA6C,QAAA,cACE7C,IAAA,SAAM4C,SAAS,+BAAAY,MAAA,CAAgCI,MAAM,CAAC3C,KAAK,eAAc,CAAA4B,QAAA,cACvE7C,IAAA,CAACsD,aAAa,EAACV,SAAS,CAAC,SAAS,CAAE,CAAC,CACjC,CAAC,CACJ,CAAC,cACN1C,KAAA,QAAK0C,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB7C,IAAA,OAAI4C,SAAS,CAAC,6DAA6D,CAAAC,QAAA,CACxEe,MAAM,CAAC/C,IAAI,CACV,CAAC,cACLb,IAAA,MAAG4C,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CACtCe,MAAM,CAAC9C,WAAW,CAClB,CAAC,EACD,CAAC,GAhBD8C,MAAM,CAAC/C,IAiBR,CAAC,CAEX,CAAC,CAAC,CACC,CAAC,CACH,CAAC,EACH,CAAC,cAGNX,KAAA,QAAK0C,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzC7C,IAAA,QAAK4C,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjD7C,IAAA,OAAI4C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,oBAAkB,CAAI,CAAC,CACtE,CAAC,cACN7C,IAAA,QAAK4C,SAAS,CAAC,KAAK,CAAAC,QAAA,cAClB3C,KAAA,OAAI0C,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACnD3C,KAAA,QAAA2C,QAAA,eACE7C,IAAA,OAAI4C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,SAAO,CAAI,CAAC,cAC9D7C,IAAA,OAAI4C,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAEzB,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAE0C,OAAO,CAAK,CAAC,EAC5E,CAAC,cACN5D,KAAA,QAAA2C,QAAA,eACE7C,IAAA,OAAI4C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,MAAI,CAAI,CAAC,cAC3D7C,IAAA,OAAI4C,SAAS,CAAC,0CAA0C,CAAAC,QAAA,cACtD7C,IAAA,SAAM4C,SAAS,CAAC,+FAA+F,CAAAC,QAAA,CAC5GzB,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAE2B,IAAI,CACT,CAAC,CACL,CAAC,EACF,CAAC,cACN7C,KAAA,QAAA2C,QAAA,eACE7C,IAAA,OAAI4C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,cACtE3C,KAAA,OAAI0C,SAAS,CAAC,0CAA0C,CAAAC,QAAA,EACrDzB,MAAM,SAANA,MAAM,WAANA,MAAM,CAAE4B,QAAQ,CAAGC,MAAM,CAACc,IAAI,CAAC3C,MAAM,CAAC4B,QAAQ,CAAC,CAACgB,MAAM,CAAG,CAAC,CAAC,KAAG,CAAC5C,MAAM,SAANA,MAAM,WAANA,MAAM,CAAE4B,QAAQ,CAAGC,MAAM,CAACc,IAAI,CAAC3C,MAAM,CAAC4B,QAAQ,CAAC,CAACgB,MAAM,CAAG,CAAC,EACxH,CAAC,EACF,CAAC,EACJ,CAAC,CACF,CAAC,EACH,CAAC,EACH,CAAC,CAEV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}