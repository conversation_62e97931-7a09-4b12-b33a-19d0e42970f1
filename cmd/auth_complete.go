package main

import (
	"context"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"net/url"
	"strings"
	"time"

	"github.com/dgrijalva/jwt-go"
	"github.com/gofiber/fiber/v2"
	"golang.org/x/oauth2"
)

// Complete Authentication System based on Clutch implementation

// JWT Claims structure
type AuthClaims struct {
	jwt.StandardClaims
	Email  string   `json:"email"`
	Groups []string `json:"grp,omitempty"`
	Name   string   `json:"name,omitempty"`
}

// State claims for OAuth2 flow
type StateClaims struct {
	jwt.StandardClaims
	RedirectURL string `json:"redirect"`
}

// OIDC Configuration
type OIDCConfig struct {
	Issuer       string   `json:"issuer"`
	ClientID     string   `json:"client_id"`
	ClientSecret string   `json:"client_secret"`
	RedirectURL  string   `json:"redirect_url"`
	Scopes       []string `json:"scopes"`
}

// Authentication Service
type AuthService struct {
	config        *OIDCConfig
	oauth2Config  *oauth2.Config
	sessionSecret string
}

// Initialize the authentication service
func NewAuthService() *AuthService {
	// Mock OIDC configuration - in production this would come from config
	config := &OIDCConfig{
		Issuer:       "https://auth.cainuro.com",
		ClientID:     "cainuro-orchestrator",
		ClientSecret: "your-client-secret",
		RedirectURL:  "http://localhost:8080/v1/auth/callback",
		Scopes:       []string{"openid", "email", "profile", "groups"},
	}

	oauth2Config := &oauth2.Config{
		ClientID:     config.ClientID,
		ClientSecret: config.ClientSecret,
		RedirectURL:  config.RedirectURL,
		Scopes:       config.Scopes,
		Endpoint: oauth2.Endpoint{
			AuthURL:  config.Issuer + "/oauth2/authorize",
			TokenURL: config.Issuer + "/oauth2/token",
		},
	}

	return &AuthService{
		config:        config,
		oauth2Config:  oauth2Config,
		sessionSecret: "your-session-secret-key", // In production, use a secure random key
	}
}

// Generate state token for CSRF protection
func (a *AuthService) generateStateToken(redirectURL string) (string, error) {
	// Validate redirect URL (only allow relative URLs)
	u, err := url.Parse(redirectURL)
	if err != nil {
		return "", err
	}
	if u.Scheme != "" || u.Host != "" {
		return "", fmt.Errorf("only relative redirects are supported")
	}

	dest := u.RequestURI()
	if !strings.HasPrefix(dest, "/") {
		dest = fmt.Sprintf("/%s", dest)
	}

	// Generate random state
	randomBytes := make([]byte, 32)
	if _, err := rand.Read(randomBytes); err != nil {
		return "", err
	}
	state := base64.URLEncoding.EncodeToString(randomBytes)

	claims := &StateClaims{
		StandardClaims: jwt.StandardClaims{
			Subject:   state, // Use random state as subject for CSRF protection
			ExpiresAt: time.Now().Add(5 * time.Minute).Unix(),
			IssuedAt:  time.Now().Unix(),
			Issuer:    "cainuro-orchestrator",
		},
		RedirectURL: dest,
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(a.sessionSecret))
}

// Validate state token and extract redirect URL
func (a *AuthService) validateStateToken(stateToken string) (string, error) {
	claims := &StateClaims{}
	token, err := jwt.ParseWithClaims(stateToken, claims, func(token *jwt.Token) (interface{}, error) {
		return []byte(a.sessionSecret), nil
	})

	if err != nil {
		return "", err
	}

	if !token.Valid {
		return "", fmt.Errorf("invalid state token")
	}

	if err := claims.Valid(); err != nil {
		return "", err
	}

	return claims.RedirectURL, nil
}

// Create JWT access token
func (a *AuthService) createAccessToken(email, name string, groups []string) (string, error) {
	claims := &AuthClaims{
		StandardClaims: jwt.StandardClaims{
			Subject:   email,
			ExpiresAt: time.Now().Add(1 * time.Hour).Unix(),
			IssuedAt:  time.Now().Unix(),
			Issuer:    "cainuro-orchestrator",
		},
		Email:  email,
		Name:   name,
		Groups: groups,
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(a.sessionSecret))
}

// Create refresh token
func (a *AuthService) createRefreshToken(email string) (string, error) {
	claims := &jwt.StandardClaims{
		Subject:   email,
		ExpiresAt: time.Now().Add(24 * time.Hour).Unix(), // 24 hour refresh token
		IssuedAt:  time.Now().Unix(),
		Issuer:    "cainuro-orchestrator",
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(a.sessionSecret))
}

// Verify JWT token
func (a *AuthService) verifyToken(tokenString string) (*AuthClaims, error) {
	claims := &AuthClaims{}
	token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (interface{}, error) {
		return []byte(a.sessionSecret), nil
	})

	if err != nil {
		return nil, err
	}

	if !token.Valid {
		return nil, fmt.Errorf("invalid token")
	}

	if err := claims.Valid(); err != nil {
		return nil, err
	}

	return claims, nil
}

// Enhanced Authentication Handlers
var authService = NewAuthService()

// Login handler - initiates OAuth2 flow
func getAuthLoginEnhanced(c *fiber.Ctx) error {
	redirectURL := c.Query("redirect_url", "/")

	// Check for existing refresh token
	refreshToken := c.Cookies("refreshToken")
	if refreshToken != "" {
		// Attempt to refresh the session
		if newTokens, err := refreshTokens(refreshToken); err == nil {
			// Set new cookies
			c.Cookie(&fiber.Cookie{
				Name:     "token",
				Value:    newTokens.AccessToken,
				HTTPOnly: true,
				Secure:   false, // Set to true in production with HTTPS
				SameSite: "Lax",
				MaxAge:   3600, // 1 hour
			})

			if newTokens.RefreshToken != "" {
				c.Cookie(&fiber.Cookie{
					Name:     "refreshToken",
					Value:    newTokens.RefreshToken,
					HTTPOnly: true,
					Secure:   false,
					SameSite: "Lax",
					MaxAge:   86400, // 24 hours
				})
			}

			return c.JSON(fiber.Map{
				"token": fiber.Map{
					"access_token":  newTokens.AccessToken,
					"refresh_token": newTokens.RefreshToken,
					"token_type":    "Bearer",
					"expires_in":    3600,
				},
				"redirect_url": redirectURL,
			})
		}
	}

	// Generate state token for CSRF protection
	state, err := authService.generateStateToken(redirectURL)
	if err != nil {
		return c.Status(500).JSON(fiber.Map{"error": "Failed to generate state token"})
	}

	// Generate authorization URL
	authURL := authService.oauth2Config.AuthCodeURL(state, oauth2.AccessTypeOffline)

	return c.JSON(fiber.Map{
		"auth_url":     authURL,
		"state":        state,
		"redirect_url": redirectURL,
	})
}

// OAuth2 callback handler
func postAuthCallbackEnhanced(c *fiber.Ctx) error {
	var req struct {
		Code  string `json:"code"`
		State string `json:"state"`
		Error string `json:"error,omitempty"`
	}

	if err := c.BodyParser(&req); err != nil {
		return c.Status(400).JSON(fiber.Map{"error": "Invalid request body"})
	}

	if req.Error != "" {
		return c.Status(400).JSON(fiber.Map{"error": req.Error})
	}

	// Validate state token
	redirectURL, err := authService.validateStateToken(req.State)
	if err != nil {
		return c.Status(400).JSON(fiber.Map{"error": "Invalid state token"})
	}

	// Exchange authorization code for tokens
	ctx := context.Background()
	_, err = authService.oauth2Config.Exchange(ctx, req.Code)
	if err != nil {
		return c.Status(400).JSON(fiber.Map{"error": "Failed to exchange code for token"})
	}

	// In a real implementation, you would:
	// 1. Extract ID token from the OAuth2 token
	// 2. Verify the ID token with the OIDC provider
	// 3. Extract user claims (email, name, groups) from the ID token

	// Mock user data (in production, extract from ID token)
	email := "<EMAIL>"
	name := "CAINuro User"
	groups := []string{"cainuro-users", "cainuro-operators"}

	// Create our own JWT tokens
	accessToken, err := authService.createAccessToken(email, name, groups)
	if err != nil {
		return c.Status(500).JSON(fiber.Map{"error": "Failed to create access token"})
	}

	refreshToken, err := authService.createRefreshToken(email)
	if err != nil {
		return c.Status(500).JSON(fiber.Map{"error": "Failed to create refresh token"})
	}

	// Set HTTP-only cookies
	c.Cookie(&fiber.Cookie{
		Name:     "token",
		Value:    accessToken,
		HTTPOnly: true,
		Secure:   false, // Set to true in production with HTTPS
		SameSite: "Lax",
		MaxAge:   3600, // 1 hour
	})

	c.Cookie(&fiber.Cookie{
		Name:     "refreshToken",
		Value:    refreshToken,
		HTTPOnly: true,
		Secure:   false,
		SameSite: "Lax",
		MaxAge:   86400, // 24 hours
	})

	return c.JSON(fiber.Map{
		"access_token":  accessToken,
		"refresh_token": refreshToken,
		"token_type":    "Bearer",
		"expires_in":    3600,
		"redirect_url":  redirectURL,
		"user": fiber.Map{
			"email":  email,
			"name":   name,
			"groups": groups,
		},
	})
}

// Helper function to refresh tokens
func refreshTokens(refreshTokenString string) (*oauth2.Token, error) {
	// Verify refresh token
	claims := &jwt.StandardClaims{}
	token, err := jwt.ParseWithClaims(refreshTokenString, claims, func(token *jwt.Token) (interface{}, error) {
		return []byte(authService.sessionSecret), nil
	})

	if err != nil || !token.Valid {
		return nil, fmt.Errorf("invalid refresh token")
	}

	if err := claims.Valid(); err != nil {
		return nil, err
	}

	// Create new tokens
	email := claims.Subject
	name := "CAINuro User" // In production, fetch from user store
	groups := []string{"cainuro-users", "cainuro-operators"}

	accessToken, err := authService.createAccessToken(email, name, groups)
	if err != nil {
		return nil, err
	}

	newRefreshToken, err := authService.createRefreshToken(email)
	if err != nil {
		return nil, err
	}

	return &oauth2.Token{
		AccessToken:  accessToken,
		RefreshToken: newRefreshToken,
		TokenType:    "Bearer",
		Expiry:       time.Now().Add(1 * time.Hour),
	}, nil
}
