package main

import (
	"sort"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
)

// Search helper functions for different providers

// Search AWS resources
func searchAWSResources(query string, limit int) []fiber.Map {
	results := []fiber.Map{}

	// Mock AWS EC2 instances
	instances := []fiber.Map{
		{
			"id":            "i-1234567890abcdef0",
			"name":          "web-server-1",
			"type":          "aws.ec2.instance",
			"provider":      "aws",
			"region":        "us-east-1",
			"account":       "************",
			"status":        "running",
			"instance_type": "t3.medium",
			"created_at":    time.Now().Add(-24 * time.Hour),
			"tags": map[string]string{
				"Environment": "production",
				"Team":        "platform",
			},
		},
		{
			"id":            "i-0987654321fedcba0",
			"name":          "database-server",
			"type":          "aws.ec2.instance",
			"provider":      "aws",
			"region":        "us-west-2",
			"account":       "************",
			"status":        "running",
			"instance_type": "r5.large",
			"created_at":    time.Now().Add(-48 * time.Hour),
			"tags": map[string]string{
				"Environment": "production",
				"Team":        "data",
			},
		},
		{
			"id":            "i-abcdef1234567890",
			"name":          "api-server-staging",
			"type":          "aws.ec2.instance",
			"provider":      "aws",
			"region":        "us-east-1",
			"account":       "************",
			"status":        "running",
			"instance_type": "t3.small",
			"created_at":    time.Now().Add(-12 * time.Hour),
			"tags": map[string]string{
				"Environment": "staging",
				"Team":        "api",
			},
		},
	}

	// Filter based on query
	for _, instance := range instances {
		if matchesQuery(instance, query) {
			results = append(results, instance)
			if len(results) >= limit {
				break
			}
		}
	}

	return results
}

// Search GCP resources
func searchGCPResources(query string, limit int) []fiber.Map {
	results := []fiber.Map{}

	// Mock GCP Compute instances
	instances := []fiber.Map{
		{
			"id":           "gcp-instance-1",
			"name":         "web-frontend",
			"type":         "gcp.compute.instance",
			"provider":     "gcp",
			"project":      "my-prod-project",
			"zone":         "us-central1-a",
			"status":       "running",
			"machine_type": "n1-standard-2",
			"created_at":   time.Now().Add(-36 * time.Hour),
			"labels": map[string]string{
				"environment": "production",
				"team":        "frontend",
			},
		},
		{
			"id":           "gcp-instance-2",
			"name":         "analytics-worker",
			"type":         "gcp.compute.instance",
			"provider":     "gcp",
			"project":      "my-prod-project",
			"zone":         "us-central1-b",
			"status":       "running",
			"machine_type": "n1-highmem-4",
			"created_at":   time.Now().Add(-18 * time.Hour),
			"labels": map[string]string{
				"environment": "production",
				"team":        "analytics",
			},
		},
	}

	// Filter based on query
	for _, instance := range instances {
		if matchesQuery(instance, query) {
			results = append(results, instance)
			if len(results) >= limit {
				break
			}
		}
	}

	return results
}

// Search Azure resources
func searchAzureResources(query string, limit int) []fiber.Map {
	results := []fiber.Map{}

	// Mock Azure Virtual Machines
	vms := []fiber.Map{
		{
			"id":             "azure-vm-1",
			"name":           "app-server-vm",
			"type":           "azure.compute.virtualmachine",
			"provider":       "azure",
			"resource_group": "prod-rg",
			"subscription":   "sub-prod-123",
			"location":       "East US",
			"status":         "running",
			"vm_size":        "Standard_D2s_v3",
			"created_at":     time.Now().Add(-60 * time.Hour),
			"tags": map[string]string{
				"Environment": "production",
				"Team":        "application",
			},
		},
		{
			"id":             "azure-vm-2",
			"name":           "cache-server-vm",
			"type":           "azure.compute.virtualmachine",
			"provider":       "azure",
			"resource_group": "prod-rg",
			"subscription":   "sub-prod-123",
			"location":       "West US 2",
			"status":         "running",
			"vm_size":        "Standard_E4s_v3",
			"created_at":     time.Now().Add(-30 * time.Hour),
			"tags": map[string]string{
				"Environment": "production",
				"Team":        "infrastructure",
			},
		},
	}

	// Filter based on query
	for _, vm := range vms {
		if matchesQuery(vm, query) {
			results = append(results, vm)
			if len(results) >= limit {
				break
			}
		}
	}

	return results
}

// Search Kubernetes resources
func searchKubernetesResources(query string, limit int) []fiber.Map {
	results := []fiber.Map{}

	// Mock Kubernetes pods
	pods := []fiber.Map{
		{
			"id":         "nginx-deployment-abc123",
			"name":       "nginx-deployment-abc123",
			"type":       "k8s.pod",
			"provider":   "kubernetes",
			"cluster":    "prod-cluster",
			"namespace":  "default",
			"status":     "running",
			"node":       "node-1",
			"created_at": time.Now().Add(-6 * time.Hour),
			"labels": map[string]string{
				"app":     "nginx",
				"version": "1.21",
			},
		},
		{
			"id":         "redis-master-def456",
			"name":       "redis-master-def456",
			"type":       "k8s.pod",
			"provider":   "kubernetes",
			"cluster":    "prod-cluster",
			"namespace":  "cache",
			"status":     "running",
			"node":       "node-2",
			"created_at": time.Now().Add(-12 * time.Hour),
			"labels": map[string]string{
				"app":  "redis",
				"role": "master",
			},
		},
		{
			"id":         "api-service-ghi789",
			"name":       "api-service-ghi789",
			"type":       "k8s.pod",
			"provider":   "kubernetes",
			"cluster":    "staging-cluster",
			"namespace":  "api",
			"status":     "running",
			"node":       "node-3",
			"created_at": time.Now().Add(-3 * time.Hour),
			"labels": map[string]string{
				"app":         "api",
				"environment": "staging",
			},
		},
	}

	// Filter based on query
	for _, pod := range pods {
		if matchesQuery(pod, query) {
			results = append(results, pod)
			if len(results) >= limit {
				break
			}
		}
	}

	return results
}

// Search custom resources
func searchCustomResources(query string, limit int) []fiber.Map {
	results := []fiber.Map{}

	// Mock custom application instances
	apps := []fiber.Map{
		{
			"id":          "app-web-prod",
			"name":        "web-application",
			"type":        "custom.application",
			"provider":    "custom",
			"environment": "production",
			"version":     "v2.1.0",
			"status":      "healthy",
			"instances":   3,
			"created_at":  time.Now().Add(-72 * time.Hour),
			"metadata": map[string]interface{}{
				"team":       "frontend",
				"repository": "github.com/company/web-app",
				"language":   "nodejs",
			},
		},
		{
			"id":          "app-api-staging",
			"name":        "api-service",
			"type":        "custom.application",
			"provider":    "custom",
			"environment": "staging",
			"version":     "v1.5.2",
			"status":      "healthy",
			"instances":   2,
			"created_at":  time.Now().Add(-24 * time.Hour),
			"metadata": map[string]interface{}{
				"team":       "backend",
				"repository": "github.com/company/api-service",
				"language":   "golang",
			},
		},
	}

	// Filter based on query
	for _, app := range apps {
		if matchesQuery(app, query) {
			results = append(results, app)
			if len(results) >= limit {
				break
			}
		}
	}

	return results
}

// Generate AWS autocomplete results
func generateAWSAutocomplete(query string, limit int) []AutocompleteResult {
	suggestions := []AutocompleteResult{
		{ID: "i-1234567890abcdef0", Label: "web-server-1 (i-1234567890abcdef0)", Type: "aws.ec2.instance"},
		{ID: "i-0987654321fedcba0", Label: "database-server (i-0987654321fedcba0)", Type: "aws.ec2.instance"},
		{ID: "i-abcdef1234567890", Label: "api-server-staging (i-abcdef1234567890)", Type: "aws.ec2.instance"},
		{ID: "sg-12345678", Label: "web-security-group", Type: "aws.ec2.securitygroup"},
		{ID: "vpc-87654321", Label: "production-vpc", Type: "aws.ec2.vpc"},
	}

	results := []AutocompleteResult{}
	query = strings.ToLower(query)

	for _, suggestion := range suggestions {
		if strings.Contains(strings.ToLower(suggestion.Label), query) ||
			strings.Contains(strings.ToLower(suggestion.ID), query) {
			results = append(results, suggestion)
			if len(results) >= limit {
				break
			}
		}
	}

	return results
}

// Generate GCP autocomplete results
func generateGCPAutocomplete(query string, limit int) []AutocompleteResult {
	suggestions := []AutocompleteResult{
		{ID: "gcp-instance-1", Label: "web-frontend", Type: "gcp.compute.instance"},
		{ID: "gcp-instance-2", Label: "analytics-worker", Type: "gcp.compute.instance"},
		{ID: "gcp-disk-1", Label: "web-frontend-disk", Type: "gcp.compute.disk"},
		{ID: "gcp-network-1", Label: "production-network", Type: "gcp.compute.network"},
	}

	results := []AutocompleteResult{}
	query = strings.ToLower(query)

	for _, suggestion := range suggestions {
		if strings.Contains(strings.ToLower(suggestion.Label), query) ||
			strings.Contains(strings.ToLower(suggestion.ID), query) {
			results = append(results, suggestion)
			if len(results) >= limit {
				break
			}
		}
	}

	return results
}

// Generate Azure autocomplete results
func generateAzureAutocomplete(query string, limit int) []AutocompleteResult {
	suggestions := []AutocompleteResult{
		{ID: "azure-vm-1", Label: "app-server-vm", Type: "azure.compute.virtualmachine"},
		{ID: "azure-vm-2", Label: "cache-server-vm", Type: "azure.compute.virtualmachine"},
		{ID: "azure-storage-1", Label: "production-storage", Type: "azure.storage.account"},
		{ID: "azure-network-1", Label: "production-vnet", Type: "azure.network.virtualnetwork"},
	}

	results := []AutocompleteResult{}
	query = strings.ToLower(query)

	for _, suggestion := range suggestions {
		if strings.Contains(strings.ToLower(suggestion.Label), query) ||
			strings.Contains(strings.ToLower(suggestion.ID), query) {
			results = append(results, suggestion)
			if len(results) >= limit {
				break
			}
		}
	}

	return results
}

// Generate Kubernetes autocomplete results
func generateK8sAutocomplete(query string, limit int) []AutocompleteResult {
	suggestions := []AutocompleteResult{
		{ID: "nginx-deployment-abc123", Label: "nginx-deployment-abc123", Type: "k8s.pod"},
		{ID: "redis-master-def456", Label: "redis-master-def456", Type: "k8s.pod"},
		{ID: "api-service-ghi789", Label: "api-service-ghi789", Type: "k8s.pod"},
		{ID: "nginx-service", Label: "nginx-service", Type: "k8s.service"},
		{ID: "api-deployment", Label: "api-deployment", Type: "k8s.deployment"},
	}

	results := []AutocompleteResult{}
	query = strings.ToLower(query)

	for _, suggestion := range suggestions {
		if strings.Contains(strings.ToLower(suggestion.Label), query) ||
			strings.Contains(strings.ToLower(suggestion.ID), query) {
			results = append(results, suggestion)
			if len(results) >= limit {
				break
			}
		}
	}

	return results
}

// matchesQuery function is defined in main.go to avoid duplication

// Sort results based on criteria
func sortResults(results []fiber.Map, sortBy, sortOrder string) []fiber.Map {
	sort.Slice(results, func(i, j int) bool {
		var valI, valJ interface{}

		if val, ok := results[i][sortBy]; ok {
			valI = val
		}
		if val, ok := results[j][sortBy]; ok {
			valJ = val
		}

		// Handle different types
		switch v1 := valI.(type) {
		case string:
			if v2, ok := valJ.(string); ok {
				if sortOrder == "desc" {
					return v1 > v2
				}
				return v1 < v2
			}
		case time.Time:
			if v2, ok := valJ.(time.Time); ok {
				if sortOrder == "desc" {
					return v1.After(v2)
				}
				return v1.Before(v2)
			}
		}

		return false
	})

	return results
}
