// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.17.3
// source: proxy/v1/proxy.proto

package proxyv1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ProxyAPI_RequestProxy_FullMethodName    = "/clutch.proxy.v1.ProxyAPI/RequestProxy"
	ProxyAPI_RequestProxyGet_FullMethodName = "/clutch.proxy.v1.ProxyAPI/RequestProxyGet"
)

// ProxyAPIClient is the client API for ProxyAPI service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ProxyAPIClient interface {
	RequestProxy(ctx context.Context, in *RequestProxyRequest, opts ...grpc.CallOption) (*RequestProxyResponse, error)
	// RequestProxyGet is a clone of the request proxy, but only permits requests with the http_method set to GET.
	// This is useful for RBAC purposes since the action type is READ.
	RequestProxyGet(ctx context.Context, in *RequestProxyGetRequest, opts ...grpc.CallOption) (*RequestProxyGetResponse, error)
}

type proxyAPIClient struct {
	cc grpc.ClientConnInterface
}

func NewProxyAPIClient(cc grpc.ClientConnInterface) ProxyAPIClient {
	return &proxyAPIClient{cc}
}

func (c *proxyAPIClient) RequestProxy(ctx context.Context, in *RequestProxyRequest, opts ...grpc.CallOption) (*RequestProxyResponse, error) {
	out := new(RequestProxyResponse)
	err := c.cc.Invoke(ctx, ProxyAPI_RequestProxy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *proxyAPIClient) RequestProxyGet(ctx context.Context, in *RequestProxyGetRequest, opts ...grpc.CallOption) (*RequestProxyGetResponse, error) {
	out := new(RequestProxyGetResponse)
	err := c.cc.Invoke(ctx, ProxyAPI_RequestProxyGet_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ProxyAPIServer is the server API for ProxyAPI service.
// All implementations should embed UnimplementedProxyAPIServer
// for forward compatibility
type ProxyAPIServer interface {
	RequestProxy(context.Context, *RequestProxyRequest) (*RequestProxyResponse, error)
	// RequestProxyGet is a clone of the request proxy, but only permits requests with the http_method set to GET.
	// This is useful for RBAC purposes since the action type is READ.
	RequestProxyGet(context.Context, *RequestProxyGetRequest) (*RequestProxyGetResponse, error)
}

// UnimplementedProxyAPIServer should be embedded to have forward compatible implementations.
type UnimplementedProxyAPIServer struct {
}

func (UnimplementedProxyAPIServer) RequestProxy(context.Context, *RequestProxyRequest) (*RequestProxyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RequestProxy not implemented")
}
func (UnimplementedProxyAPIServer) RequestProxyGet(context.Context, *RequestProxyGetRequest) (*RequestProxyGetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RequestProxyGet not implemented")
}

// UnsafeProxyAPIServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ProxyAPIServer will
// result in compilation errors.
type UnsafeProxyAPIServer interface {
	mustEmbedUnimplementedProxyAPIServer()
}

func RegisterProxyAPIServer(s grpc.ServiceRegistrar, srv ProxyAPIServer) {
	s.RegisterService(&ProxyAPI_ServiceDesc, srv)
}

func _ProxyAPI_RequestProxy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RequestProxyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProxyAPIServer).RequestProxy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProxyAPI_RequestProxy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProxyAPIServer).RequestProxy(ctx, req.(*RequestProxyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProxyAPI_RequestProxyGet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RequestProxyGetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProxyAPIServer).RequestProxyGet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProxyAPI_RequestProxyGet_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProxyAPIServer).RequestProxyGet(ctx, req.(*RequestProxyGetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ProxyAPI_ServiceDesc is the grpc.ServiceDesc for ProxyAPI service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ProxyAPI_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "clutch.proxy.v1.ProxyAPI",
	HandlerType: (*ProxyAPIServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RequestProxy",
			Handler:    _ProxyAPI_RequestProxy_Handler,
		},
		{
			MethodName: "RequestProxyGet",
			Handler:    _ProxyAPI_RequestProxyGet_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proxy/v1/proxy.proto",
}
