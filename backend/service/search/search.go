package search

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"cloud.google.com/go/asset/apiv1/assetpb"
	"github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/resources/armresources"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/resourcegroupstaggingapi"
	"github.com/aws/aws-sdk-go-v2/service/s3/types"
	"github.com/cainuro/orchestrator/internal/cache"
	appconfig "github.com/cainuro/orchestrator/internal/config"
	"github.com/cainuro/orchestrator/internal/db"
	orchestratorv1 "github.com/cainuro/orchestrator/proto/orchestrator/v1"
	"go.uber.org/zap"
	"google.golang.org/api/option"
)

// Resource represents a cloud resource
type Resource struct {
	Provider string            `json:"provider"`
	Type     string            `json:"type"`
	Name     string            `json:"name"`
	ID       string            `json:"id"`
	Tags     map[string]string `json:"tags"`
}

// SearchRequest represents a search request
type SearchRequest struct {
	Provider string `json:"provider"`
	Query    string `json:"query"`
	Limit    int32  `json:"limit"`
}

// SearchResponse represents a search response
type SearchResponse struct {
	Resources  []*Resource `json:"resources"`
	TotalCount int32       `json:"total_count"`
}

// Service defines the search/discovery service interface
type Service interface {
	SearchResources(ctx context.Context, req *orchestratorv1.SearchResourcesRequest) (*orchestratorv1.SearchResourcesResponse, error)
	GetResource(ctx context.Context, req *orchestratorv1.GetResourceRequest) (*orchestratorv1.GetResourceResponse, error)
	DiscoverAWSResources(ctx context.Context, regions []string) error
	DiscoverGCPResources(ctx context.Context, projects []string) error
	DiscoverAzureResources(ctx context.Context, subscriptions []string) error
}

// service implements the Service interface
type service struct {
	config *appconfig.Config
	logger *zap.Logger
	cache  cache.Cache
	store  db.Store

	// Cloud provider clients
	awsClient   AWSClient
	gcpClient   GCPClient
	azureClient AzureClient
}

// Cloud provider client interfaces for mocking
type AWSClient interface {
	GetResources(ctx context.Context, params *resourcegroupstaggingapi.GetResourcesInput, optFns ...func(*resourcegroupstaggingapi.Options)) (*resourcegroupstaggingapi.GetResourcesOutput, error)
}

type GCPClient interface {
	SearchAllResources(ctx context.Context, req *assetpb.SearchAllResourcesRequest, opts ...option.ClientOption) (*assetpb.SearchAllResourcesResponse, error)
}

type AzureClient interface {
	NewListPager(options *armresources.ClientListOptions) *armresources.ClientListPager
}

// NewService creates a new search service
func NewService(
	config *appconfig.Config,
	logger *zap.Logger,
	cacheInstance cache.Cache,
	store db.Store,
) (Service, error) {
	s := &service{
		config: config,
		logger: logger,
		cache:  cacheInstance,
		store:  store,
	}

	// Initialize cloud provider clients
	if err := s.initCloudClients(); err != nil {
		return nil, fmt.Errorf("failed to initialize cloud clients: %w", err)
	}

	return s, nil
}

// SearchResources searches for resources across cloud providers
func (s *service) SearchResources(ctx context.Context, req *orchestratorv1.SearchResourcesRequest) (*orchestratorv1.SearchResourcesResponse, error) {
	s.logger.Info("Searching resources",
		zap.String("provider", req.Provider),
		zap.String("query", req.Query))

	// Check cache first
	cacheKey := fmt.Sprintf("search:%s:%s", req.Provider, req.Query)
	var cachedResponse orchestratorv1.SearchResourcesResponse
	if found, err := s.cache.Get(ctx, cacheKey, &cachedResponse); err == nil && found {
		s.logger.Debug("Returning cached search results", zap.String("cache_key", cacheKey))
		return &cachedResponse, nil
	}

	// Mock implementation - return sample resources
	mockResources := []*orchestratorv1.Resource{
		{
			Provider: "aws",
			Type:     "ec2.instance",
			Name:     "web-server-1",
			Id:       "i-1234567890abcdef0",
			Tags: map[string]string{
				"Environment": "production",
				"Team":        "backend",
			},
		},
		{
			Provider: "gcp",
			Type:     "compute.instance",
			Name:     "api-server-1",
			Id:       "projects/my-project/zones/us-central1-a/instances/api-server-1",
			Tags: map[string]string{
				"env":  "prod",
				"role": "api",
			},
		},
		{
			Provider: "azure",
			Type:     "microsoft.compute/virtualmachines",
			Name:     "db-server-1",
			Id:       "/subscriptions/sub-id/resourceGroups/rg/providers/Microsoft.Compute/virtualMachines/db-server-1",
			Tags: map[string]string{
				"environment": "production",
				"service":     "database",
			},
		},
	}

	// Filter by provider if specified
	var filteredResources []*orchestratorv1.Resource
	for _, resource := range mockResources {
		if req.Provider == "" || strings.EqualFold(resource.Provider, req.Provider) {
			if req.Query == "" || strings.Contains(strings.ToLower(resource.Name), strings.ToLower(req.Query)) {
				filteredResources = append(filteredResources, resource)
			}
		}
	}

	// Apply limit
	if req.Limit > 0 && len(filteredResources) > int(req.Limit) {
		filteredResources = filteredResources[:req.Limit]
	}

	return &orchestratorv1.SearchResourcesResponse{
		Resources:  filteredResources,
		TotalCount: int32(len(filteredResources)),
	}, nil
}

// GetResource retrieves a specific resource by ID
func (s *service) GetResource(ctx context.Context, req *orchestratorv1.GetResourceRequest) (*orchestratorv1.GetResourceResponse, error) {
	resourceID := req.ResourceId
	s.logger.Info("Getting resource", zap.String("resource_id", resourceID))

	// Mock implementation for now
	mockResource := &orchestratorv1.Resource{
		Provider: "aws",
		Type:     "ec2.instance",
		Name:     "web-server-1",
		Id:       resourceID,
		Tags: map[string]string{
			"Environment": "production",
			"Team":        "backend",
		},
	}

	return &orchestratorv1.GetResourceResponse{
		Resource: mockResource,
	}, nil

}

// DiscoverAWSResources discovers AWS resources and caches them
func (s *service) DiscoverAWSResources(ctx context.Context, regions []string) error {
	s.logger.Info("Starting AWS resource discovery", zap.Strings("regions", regions))

	for _, region := range regions {
		s.logger.Info("Discovering AWS resources", zap.String("region", region))

		// Get all resources in the region
		input := &resourcegroupstaggingapi.GetResourcesInput{
			ResourcesPerPage: aws.Int32(100),
		}

		output, err := s.awsClient.GetResources(ctx, input)
		if err != nil {
			s.logger.Error("Failed to get AWS resources", zap.String("region", region), zap.Error(err))
			continue
		}

		// Process and cache resources
		for _, resource := range output.ResourceTagMappingList {
			cachedResource := db.CachedResource{
				Provider:   "aws",
				Type:       s.extractAWSResourceType(*resource.ResourceARN),
				Name:       s.extractAWSResourceName(*resource.ResourceARN),
				ResourceID: *resource.ResourceARN,
				Region:     region,
				Tags:       s.convertAWSTagsToJSON(resource.Tags),
				LastSeen:   time.Now(),
			}

			if err := s.store.Insert(ctx, "resource_cache", cachedResource); err != nil {
				s.logger.Error("Failed to cache AWS resource", zap.Error(err))
			}
		}
	}

	s.logger.Info("AWS resource discovery completed")
	return nil
}

// DiscoverGCPResources discovers GCP resources and caches them
func (s *service) DiscoverGCPResources(ctx context.Context, projects []string) error {
	s.logger.Info("Starting GCP resource discovery", zap.Strings("projects", projects))

	for _, project := range projects {
		s.logger.Info("Discovering GCP resources", zap.String("project", project))

		req := &assetpb.SearchAllResourcesRequest{
			Scope: fmt.Sprintf("projects/%s", project),
		}

		resp, err := s.gcpClient.SearchAllResources(ctx, req)
		if err != nil {
			s.logger.Error("Failed to get GCP resources", zap.String("project", project), zap.Error(err))
			continue
		}

		// Process and cache resources
		for _, resource := range resp.Results {
			cachedResource := db.CachedResource{
				Provider:   "gcp",
				Type:       resource.AssetType,
				Name:       resource.DisplayName,
				ResourceID: resource.Name,
				Region:     s.extractGCPRegion(resource.Name),
				Tags:       s.convertGCPLabelsToJSON(resource.Labels),
				LastSeen:   time.Now(),
			}

			if err := s.store.Insert(ctx, "resource_cache", cachedResource); err != nil {
				s.logger.Error("Failed to cache GCP resource", zap.Error(err))
			}
		}
	}

	s.logger.Info("GCP resource discovery completed")
	return nil
}

// DiscoverAzureResources discovers Azure resources and caches them
func (s *service) DiscoverAzureResources(ctx context.Context, subscriptions []string) error {
	s.logger.Info("Starting Azure resource discovery", zap.Strings("subscriptions", subscriptions))

	for _, subscription := range subscriptions {
		s.logger.Info("Discovering Azure resources", zap.String("subscription", subscription))

		pager := s.azureClient.NewListPager(&armresources.ClientListOptions{})
		for pager.More() {
			page, err := pager.NextPage(ctx)
			if err != nil {
				s.logger.Error("Failed to get Azure resources", zap.String("subscription", subscription), zap.Error(err))
				break
			}

			// Process and cache resources
			for _, resource := range page.Value {
				cachedResource := db.CachedResource{
					Provider:   "azure",
					Type:       *resource.Type,
					Name:       *resource.Name,
					ResourceID: *resource.ID,
					Region:     *resource.Location,
					Tags:       s.convertAzureTagsToJSON(resource.Tags),
					LastSeen:   time.Now(),
				}

				if err := s.store.Insert(ctx, "resource_cache", cachedResource); err != nil {
					s.logger.Error("Failed to cache Azure resource", zap.Error(err))
				}
			}
		}
	}

	s.logger.Info("Azure resource discovery completed")
	return nil
}

// initCloudClients initializes cloud provider clients
func (s *service) initCloudClients() error {
	// Mock implementation for now
	s.logger.Info("Cloud clients initialized (mock)")
	return nil
}

// searchAWSResources searches AWS resources
func (s *service) searchAWSResources(ctx context.Context, req *SearchRequest) ([]*Resource, error) {
	// Query cached resources from database
	query := "SELECT * FROM resource_cache WHERE provider = 'aws'"
	if req.Query != "" {
		query += " AND (name LIKE ? OR type LIKE ?)"
	}

	var args []interface{}
	if req.Query != "" {
		searchPattern := "%" + req.Query + "%"
		args = append(args, searchPattern, searchPattern)
	}

	result, err := s.store.Query(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to query AWS resources: %w", err)
	}
	defer result.Close()

	var resources []*Resource
	for result.Next() {
		var cachedRes db.CachedResource
		if err := result.Scan(&cachedRes); err != nil {
			continue
		}

		var tags map[string]string
		json.Unmarshal([]byte(cachedRes.Tags), &tags)

		resources = append(resources, &Resource{
			Provider: cachedRes.Provider,
			Type:     cachedRes.Type,
			Name:     cachedRes.Name,
			ID:       cachedRes.ResourceID,
			Tags:     tags,
		})
	}

	return resources, nil
}

// searchGCPResources searches GCP resources
func (s *service) searchGCPResources(ctx context.Context, req *SearchRequest) ([]*Resource, error) {
	// Query cached resources from database
	query := "SELECT * FROM resource_cache WHERE provider = 'gcp'"
	if req.Query != "" {
		query += " AND (name LIKE ? OR type LIKE ?)"
	}

	var args []interface{}
	if req.Query != "" {
		searchPattern := "%" + req.Query + "%"
		args = append(args, searchPattern, searchPattern)
	}

	result, err := s.store.Query(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to query GCP resources: %w", err)
	}
	defer result.Close()

	var resources []*Resource
	for result.Next() {
		var cachedRes db.CachedResource
		if err := result.Scan(&cachedRes); err != nil {
			continue
		}

		var tags map[string]string
		json.Unmarshal([]byte(cachedRes.Tags), &tags)

		resources = append(resources, &Resource{
			Provider: cachedRes.Provider,
			Type:     cachedRes.Type,
			Name:     cachedRes.Name,
			ID:       cachedRes.ResourceID,
			Tags:     tags,
		})
	}

	return resources, nil
}

// searchAzureResources searches Azure resources
func (s *service) searchAzureResources(ctx context.Context, req *SearchRequest) ([]*Resource, error) {
	// Query cached resources from database
	query := "SELECT * FROM resource_cache WHERE provider = 'azure'"
	if req.Query != "" {
		query += " AND (name LIKE ? OR type LIKE ?)"
	}

	var args []interface{}
	if req.Query != "" {
		searchPattern := "%" + req.Query + "%"
		args = append(args, searchPattern, searchPattern)
	}

	result, err := s.store.Query(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to query Azure resources: %w", err)
	}
	defer result.Close()

	var resources []*Resource
	for result.Next() {
		var cachedRes db.CachedResource
		if err := result.Scan(&cachedRes); err != nil {
			continue
		}

		var tags map[string]string
		json.Unmarshal([]byte(cachedRes.Tags), &tags)

		resources = append(resources, &Resource{
			Provider: cachedRes.Provider,
			Type:     cachedRes.Type,
			Name:     cachedRes.Name,
			ID:       cachedRes.ResourceID,
			Tags:     tags,
		})
	}

	return resources, nil
}

// searchAllProviders searches across all cloud providers
func (s *service) searchAllProviders(ctx context.Context, req *SearchRequest) ([]*Resource, error) {
	var allResources []*Resource

	// Search AWS
	awsReq := &SearchRequest{Provider: "aws", Query: req.Query, Limit: req.Limit}
	awsResources, err := s.searchAWSResources(ctx, awsReq)
	if err == nil {
		allResources = append(allResources, awsResources...)
	}

	// Search GCP
	gcpReq := &SearchRequest{Provider: "gcp", Query: req.Query, Limit: req.Limit}
	gcpResources, err := s.searchGCPResources(ctx, gcpReq)
	if err == nil {
		allResources = append(allResources, gcpResources...)
	}

	// Search Azure
	azureReq := &SearchRequest{Provider: "azure", Query: req.Query, Limit: req.Limit}
	azureResources, err := s.searchAzureResources(ctx, azureReq)
	if err == nil {
		allResources = append(allResources, azureResources...)
	}

	return allResources, nil
}

// Helper methods for resource parsing and conversion

// extractAWSResourceType extracts resource type from AWS ARN
func (s *service) extractAWSResourceType(arn string) string {
	parts := strings.Split(arn, ":")
	if len(parts) >= 3 {
		return parts[2]
	}
	return "unknown"
}

// extractAWSResourceName extracts resource name from AWS ARN
func (s *service) extractAWSResourceName(arn string) string {
	parts := strings.Split(arn, "/")
	if len(parts) > 0 {
		return parts[len(parts)-1]
	}
	return arn
}

// convertAWSTagsToJSON converts AWS tags to JSON string
func (s *service) convertAWSTagsToJSON(tags []types.Tag) string {
	tagMap := make(map[string]string)
	for _, tag := range tags {
		if tag.Key != nil && tag.Value != nil {
			tagMap[*tag.Key] = *tag.Value
		}
	}
	jsonBytes, _ := json.Marshal(tagMap)
	return string(jsonBytes)
}

// extractGCPRegion extracts region from GCP resource name
func (s *service) extractGCPRegion(resourceName string) string {
	// Extract region from resource name like projects/project/zones/us-central1-a/instances/instance-name
	parts := strings.Split(resourceName, "/")
	for i, part := range parts {
		if part == "zones" || part == "regions" {
			if i+1 < len(parts) {
				return parts[i+1]
			}
		}
	}
	return "global"
}

// convertGCPLabelsToJSON converts GCP labels to JSON string
func (s *service) convertGCPLabelsToJSON(labels map[string]string) string {
	if labels == nil {
		labels = make(map[string]string)
	}
	jsonBytes, _ := json.Marshal(labels)
	return string(jsonBytes)
}

// convertAzureTagsToJSON converts Azure tags to JSON string
func (s *service) convertAzureTagsToJSON(tags map[string]*string) string {
	tagMap := make(map[string]string)
	for key, value := range tags {
		if value != nil {
			tagMap[key] = *value
		}
	}
	jsonBytes, _ := json.Marshal(tagMap)
	return string(jsonBytes)
}
