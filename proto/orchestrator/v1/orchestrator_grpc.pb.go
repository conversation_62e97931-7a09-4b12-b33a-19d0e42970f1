// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: proto/orchestrator/v1/orchestrator.proto

package orchestratorv1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	AutoScalerService_GetAutoscalerStatus_FullMethodName    = "/orchestrator.v1.AutoScalerService/GetAutoscalerStatus"
	AutoScalerService_UpdateAutoscalerConfig_FullMethodName = "/orchestrator.v1.AutoScalerService/UpdateAutoscalerConfig"
)

// AutoScalerServiceClient is the client API for AutoScalerService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// AutoScaler Service
type AutoScalerServiceClient interface {
	GetAutoscalerStatus(ctx context.Context, in *GetAutoscalerStatusRequest, opts ...grpc.CallOption) (*GetAutoscalerStatusResponse, error)
	UpdateAutoscalerConfig(ctx context.Context, in *UpdateAutoscalerConfigRequest, opts ...grpc.CallOption) (*UpdateAutoscalerConfigResponse, error)
}

type autoScalerServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAutoScalerServiceClient(cc grpc.ClientConnInterface) AutoScalerServiceClient {
	return &autoScalerServiceClient{cc}
}

func (c *autoScalerServiceClient) GetAutoscalerStatus(ctx context.Context, in *GetAutoscalerStatusRequest, opts ...grpc.CallOption) (*GetAutoscalerStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAutoscalerStatusResponse)
	err := c.cc.Invoke(ctx, AutoScalerService_GetAutoscalerStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *autoScalerServiceClient) UpdateAutoscalerConfig(ctx context.Context, in *UpdateAutoscalerConfigRequest, opts ...grpc.CallOption) (*UpdateAutoscalerConfigResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateAutoscalerConfigResponse)
	err := c.cc.Invoke(ctx, AutoScalerService_UpdateAutoscalerConfig_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AutoScalerServiceServer is the server API for AutoScalerService service.
// All implementations must embed UnimplementedAutoScalerServiceServer
// for forward compatibility.
//
// AutoScaler Service
type AutoScalerServiceServer interface {
	GetAutoscalerStatus(context.Context, *GetAutoscalerStatusRequest) (*GetAutoscalerStatusResponse, error)
	UpdateAutoscalerConfig(context.Context, *UpdateAutoscalerConfigRequest) (*UpdateAutoscalerConfigResponse, error)
	mustEmbedUnimplementedAutoScalerServiceServer()
}

// UnimplementedAutoScalerServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAutoScalerServiceServer struct{}

func (UnimplementedAutoScalerServiceServer) GetAutoscalerStatus(context.Context, *GetAutoscalerStatusRequest) (*GetAutoscalerStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAutoscalerStatus not implemented")
}
func (UnimplementedAutoScalerServiceServer) UpdateAutoscalerConfig(context.Context, *UpdateAutoscalerConfigRequest) (*UpdateAutoscalerConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAutoscalerConfig not implemented")
}
func (UnimplementedAutoScalerServiceServer) mustEmbedUnimplementedAutoScalerServiceServer() {}
func (UnimplementedAutoScalerServiceServer) testEmbeddedByValue()                           {}

// UnsafeAutoScalerServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AutoScalerServiceServer will
// result in compilation errors.
type UnsafeAutoScalerServiceServer interface {
	mustEmbedUnimplementedAutoScalerServiceServer()
}

func RegisterAutoScalerServiceServer(s grpc.ServiceRegistrar, srv AutoScalerServiceServer) {
	// If the following call pancis, it indicates UnimplementedAutoScalerServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AutoScalerService_ServiceDesc, srv)
}

func _AutoScalerService_GetAutoscalerStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAutoscalerStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AutoScalerServiceServer).GetAutoscalerStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AutoScalerService_GetAutoscalerStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AutoScalerServiceServer).GetAutoscalerStatus(ctx, req.(*GetAutoscalerStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AutoScalerService_UpdateAutoscalerConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAutoscalerConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AutoScalerServiceServer).UpdateAutoscalerConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AutoScalerService_UpdateAutoscalerConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AutoScalerServiceServer).UpdateAutoscalerConfig(ctx, req.(*UpdateAutoscalerConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AutoScalerService_ServiceDesc is the grpc.ServiceDesc for AutoScalerService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AutoScalerService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "orchestrator.v1.AutoScalerService",
	HandlerType: (*AutoScalerServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAutoscalerStatus",
			Handler:    _AutoScalerService_GetAutoscalerStatus_Handler,
		},
		{
			MethodName: "UpdateAutoscalerConfig",
			Handler:    _AutoScalerService_UpdateAutoscalerConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/orchestrator/v1/orchestrator.proto",
}

const (
	WorkflowService_ExecuteWorkflow_FullMethodName   = "/orchestrator.v1.WorkflowService/ExecuteWorkflow"
	WorkflowService_GetWorkflowStatus_FullMethodName = "/orchestrator.v1.WorkflowService/GetWorkflowStatus"
	WorkflowService_ListWorkflows_FullMethodName     = "/orchestrator.v1.WorkflowService/ListWorkflows"
)

// WorkflowServiceClient is the client API for WorkflowService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Workflow Service
type WorkflowServiceClient interface {
	ExecuteWorkflow(ctx context.Context, in *ExecuteWorkflowRequest, opts ...grpc.CallOption) (*ExecuteWorkflowResponse, error)
	GetWorkflowStatus(ctx context.Context, in *GetWorkflowStatusRequest, opts ...grpc.CallOption) (*GetWorkflowStatusResponse, error)
	ListWorkflows(ctx context.Context, in *ListWorkflowsRequest, opts ...grpc.CallOption) (*ListWorkflowsResponse, error)
}

type workflowServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewWorkflowServiceClient(cc grpc.ClientConnInterface) WorkflowServiceClient {
	return &workflowServiceClient{cc}
}

func (c *workflowServiceClient) ExecuteWorkflow(ctx context.Context, in *ExecuteWorkflowRequest, opts ...grpc.CallOption) (*ExecuteWorkflowResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ExecuteWorkflowResponse)
	err := c.cc.Invoke(ctx, WorkflowService_ExecuteWorkflow_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) GetWorkflowStatus(ctx context.Context, in *GetWorkflowStatusRequest, opts ...grpc.CallOption) (*GetWorkflowStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetWorkflowStatusResponse)
	err := c.cc.Invoke(ctx, WorkflowService_GetWorkflowStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) ListWorkflows(ctx context.Context, in *ListWorkflowsRequest, opts ...grpc.CallOption) (*ListWorkflowsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListWorkflowsResponse)
	err := c.cc.Invoke(ctx, WorkflowService_ListWorkflows_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WorkflowServiceServer is the server API for WorkflowService service.
// All implementations must embed UnimplementedWorkflowServiceServer
// for forward compatibility.
//
// Workflow Service
type WorkflowServiceServer interface {
	ExecuteWorkflow(context.Context, *ExecuteWorkflowRequest) (*ExecuteWorkflowResponse, error)
	GetWorkflowStatus(context.Context, *GetWorkflowStatusRequest) (*GetWorkflowStatusResponse, error)
	ListWorkflows(context.Context, *ListWorkflowsRequest) (*ListWorkflowsResponse, error)
	mustEmbedUnimplementedWorkflowServiceServer()
}

// UnimplementedWorkflowServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedWorkflowServiceServer struct{}

func (UnimplementedWorkflowServiceServer) ExecuteWorkflow(context.Context, *ExecuteWorkflowRequest) (*ExecuteWorkflowResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExecuteWorkflow not implemented")
}
func (UnimplementedWorkflowServiceServer) GetWorkflowStatus(context.Context, *GetWorkflowStatusRequest) (*GetWorkflowStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWorkflowStatus not implemented")
}
func (UnimplementedWorkflowServiceServer) ListWorkflows(context.Context, *ListWorkflowsRequest) (*ListWorkflowsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListWorkflows not implemented")
}
func (UnimplementedWorkflowServiceServer) mustEmbedUnimplementedWorkflowServiceServer() {}
func (UnimplementedWorkflowServiceServer) testEmbeddedByValue()                         {}

// UnsafeWorkflowServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WorkflowServiceServer will
// result in compilation errors.
type UnsafeWorkflowServiceServer interface {
	mustEmbedUnimplementedWorkflowServiceServer()
}

func RegisterWorkflowServiceServer(s grpc.ServiceRegistrar, srv WorkflowServiceServer) {
	// If the following call pancis, it indicates UnimplementedWorkflowServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&WorkflowService_ServiceDesc, srv)
}

func _WorkflowService_ExecuteWorkflow_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExecuteWorkflowRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).ExecuteWorkflow(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkflowService_ExecuteWorkflow_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).ExecuteWorkflow(ctx, req.(*ExecuteWorkflowRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_GetWorkflowStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWorkflowStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).GetWorkflowStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkflowService_GetWorkflowStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).GetWorkflowStatus(ctx, req.(*GetWorkflowStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_ListWorkflows_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListWorkflowsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).ListWorkflows(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WorkflowService_ListWorkflows_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).ListWorkflows(ctx, req.(*ListWorkflowsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// WorkflowService_ServiceDesc is the grpc.ServiceDesc for WorkflowService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var WorkflowService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "orchestrator.v1.WorkflowService",
	HandlerType: (*WorkflowServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ExecuteWorkflow",
			Handler:    _WorkflowService_ExecuteWorkflow_Handler,
		},
		{
			MethodName: "GetWorkflowStatus",
			Handler:    _WorkflowService_GetWorkflowStatus_Handler,
		},
		{
			MethodName: "ListWorkflows",
			Handler:    _WorkflowService_ListWorkflows_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/orchestrator/v1/orchestrator.proto",
}

const (
	DiscoveryService_SearchResources_FullMethodName = "/orchestrator.v1.DiscoveryService/SearchResources"
	DiscoveryService_GetResource_FullMethodName     = "/orchestrator.v1.DiscoveryService/GetResource"
)

// DiscoveryServiceClient is the client API for DiscoveryService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Discovery Service
type DiscoveryServiceClient interface {
	SearchResources(ctx context.Context, in *SearchResourcesRequest, opts ...grpc.CallOption) (*SearchResourcesResponse, error)
	GetResource(ctx context.Context, in *GetResourceRequest, opts ...grpc.CallOption) (*GetResourceResponse, error)
}

type discoveryServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDiscoveryServiceClient(cc grpc.ClientConnInterface) DiscoveryServiceClient {
	return &discoveryServiceClient{cc}
}

func (c *discoveryServiceClient) SearchResources(ctx context.Context, in *SearchResourcesRequest, opts ...grpc.CallOption) (*SearchResourcesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchResourcesResponse)
	err := c.cc.Invoke(ctx, DiscoveryService_SearchResources_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *discoveryServiceClient) GetResource(ctx context.Context, in *GetResourceRequest, opts ...grpc.CallOption) (*GetResourceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetResourceResponse)
	err := c.cc.Invoke(ctx, DiscoveryService_GetResource_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DiscoveryServiceServer is the server API for DiscoveryService service.
// All implementations must embed UnimplementedDiscoveryServiceServer
// for forward compatibility.
//
// Discovery Service
type DiscoveryServiceServer interface {
	SearchResources(context.Context, *SearchResourcesRequest) (*SearchResourcesResponse, error)
	GetResource(context.Context, *GetResourceRequest) (*GetResourceResponse, error)
	mustEmbedUnimplementedDiscoveryServiceServer()
}

// UnimplementedDiscoveryServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedDiscoveryServiceServer struct{}

func (UnimplementedDiscoveryServiceServer) SearchResources(context.Context, *SearchResourcesRequest) (*SearchResourcesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchResources not implemented")
}
func (UnimplementedDiscoveryServiceServer) GetResource(context.Context, *GetResourceRequest) (*GetResourceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetResource not implemented")
}
func (UnimplementedDiscoveryServiceServer) mustEmbedUnimplementedDiscoveryServiceServer() {}
func (UnimplementedDiscoveryServiceServer) testEmbeddedByValue()                          {}

// UnsafeDiscoveryServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DiscoveryServiceServer will
// result in compilation errors.
type UnsafeDiscoveryServiceServer interface {
	mustEmbedUnimplementedDiscoveryServiceServer()
}

func RegisterDiscoveryServiceServer(s grpc.ServiceRegistrar, srv DiscoveryServiceServer) {
	// If the following call pancis, it indicates UnimplementedDiscoveryServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&DiscoveryService_ServiceDesc, srv)
}

func _DiscoveryService_SearchResources_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchResourcesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscoveryServiceServer).SearchResources(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DiscoveryService_SearchResources_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscoveryServiceServer).SearchResources(ctx, req.(*SearchResourcesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DiscoveryService_GetResource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetResourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DiscoveryServiceServer).GetResource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DiscoveryService_GetResource_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DiscoveryServiceServer).GetResource(ctx, req.(*GetResourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// DiscoveryService_ServiceDesc is the grpc.ServiceDesc for DiscoveryService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DiscoveryService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "orchestrator.v1.DiscoveryService",
	HandlerType: (*DiscoveryServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SearchResources",
			Handler:    _DiscoveryService_SearchResources_Handler,
		},
		{
			MethodName: "GetResource",
			Handler:    _DiscoveryService_GetResource_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/orchestrator/v1/orchestrator.proto",
}

const (
	EnvoyControlPlaneService_PushSnapshot_FullMethodName = "/orchestrator.v1.EnvoyControlPlaneService/PushSnapshot"
	EnvoyControlPlaneService_GetSnapshot_FullMethodName  = "/orchestrator.v1.EnvoyControlPlaneService/GetSnapshot"
)

// EnvoyControlPlaneServiceClient is the client API for EnvoyControlPlaneService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Envoy Control Plane Service
type EnvoyControlPlaneServiceClient interface {
	PushSnapshot(ctx context.Context, in *PushSnapshotRequest, opts ...grpc.CallOption) (*PushSnapshotResponse, error)
	GetSnapshot(ctx context.Context, in *GetSnapshotRequest, opts ...grpc.CallOption) (*GetSnapshotResponse, error)
}

type envoyControlPlaneServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewEnvoyControlPlaneServiceClient(cc grpc.ClientConnInterface) EnvoyControlPlaneServiceClient {
	return &envoyControlPlaneServiceClient{cc}
}

func (c *envoyControlPlaneServiceClient) PushSnapshot(ctx context.Context, in *PushSnapshotRequest, opts ...grpc.CallOption) (*PushSnapshotResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PushSnapshotResponse)
	err := c.cc.Invoke(ctx, EnvoyControlPlaneService_PushSnapshot_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *envoyControlPlaneServiceClient) GetSnapshot(ctx context.Context, in *GetSnapshotRequest, opts ...grpc.CallOption) (*GetSnapshotResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetSnapshotResponse)
	err := c.cc.Invoke(ctx, EnvoyControlPlaneService_GetSnapshot_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EnvoyControlPlaneServiceServer is the server API for EnvoyControlPlaneService service.
// All implementations must embed UnimplementedEnvoyControlPlaneServiceServer
// for forward compatibility.
//
// Envoy Control Plane Service
type EnvoyControlPlaneServiceServer interface {
	PushSnapshot(context.Context, *PushSnapshotRequest) (*PushSnapshotResponse, error)
	GetSnapshot(context.Context, *GetSnapshotRequest) (*GetSnapshotResponse, error)
	mustEmbedUnimplementedEnvoyControlPlaneServiceServer()
}

// UnimplementedEnvoyControlPlaneServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedEnvoyControlPlaneServiceServer struct{}

func (UnimplementedEnvoyControlPlaneServiceServer) PushSnapshot(context.Context, *PushSnapshotRequest) (*PushSnapshotResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushSnapshot not implemented")
}
func (UnimplementedEnvoyControlPlaneServiceServer) GetSnapshot(context.Context, *GetSnapshotRequest) (*GetSnapshotResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSnapshot not implemented")
}
func (UnimplementedEnvoyControlPlaneServiceServer) mustEmbedUnimplementedEnvoyControlPlaneServiceServer() {
}
func (UnimplementedEnvoyControlPlaneServiceServer) testEmbeddedByValue() {}

// UnsafeEnvoyControlPlaneServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EnvoyControlPlaneServiceServer will
// result in compilation errors.
type UnsafeEnvoyControlPlaneServiceServer interface {
	mustEmbedUnimplementedEnvoyControlPlaneServiceServer()
}

func RegisterEnvoyControlPlaneServiceServer(s grpc.ServiceRegistrar, srv EnvoyControlPlaneServiceServer) {
	// If the following call pancis, it indicates UnimplementedEnvoyControlPlaneServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&EnvoyControlPlaneService_ServiceDesc, srv)
}

func _EnvoyControlPlaneService_PushSnapshot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushSnapshotRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EnvoyControlPlaneServiceServer).PushSnapshot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EnvoyControlPlaneService_PushSnapshot_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EnvoyControlPlaneServiceServer).PushSnapshot(ctx, req.(*PushSnapshotRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EnvoyControlPlaneService_GetSnapshot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSnapshotRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EnvoyControlPlaneServiceServer).GetSnapshot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EnvoyControlPlaneService_GetSnapshot_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EnvoyControlPlaneServiceServer).GetSnapshot(ctx, req.(*GetSnapshotRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// EnvoyControlPlaneService_ServiceDesc is the grpc.ServiceDesc for EnvoyControlPlaneService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var EnvoyControlPlaneService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "orchestrator.v1.EnvoyControlPlaneService",
	HandlerType: (*EnvoyControlPlaneServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PushSnapshot",
			Handler:    _EnvoyControlPlaneService_PushSnapshot_Handler,
		},
		{
			MethodName: "GetSnapshot",
			Handler:    _EnvoyControlPlaneService_GetSnapshot_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/orchestrator/v1/orchestrator.proto",
}

const (
	DBAdminService_GetDBStatus_FullMethodName  = "/orchestrator.v1.DBAdminService/GetDBStatus"
	DBAdminService_ExecuteQuery_FullMethodName = "/orchestrator.v1.DBAdminService/ExecuteQuery"
)

// DBAdminServiceClient is the client API for DBAdminService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// DB Admin Service
type DBAdminServiceClient interface {
	GetDBStatus(ctx context.Context, in *GetDBStatusRequest, opts ...grpc.CallOption) (*GetDBStatusResponse, error)
	ExecuteQuery(ctx context.Context, in *ExecuteQueryRequest, opts ...grpc.CallOption) (*ExecuteQueryResponse, error)
}

type dBAdminServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDBAdminServiceClient(cc grpc.ClientConnInterface) DBAdminServiceClient {
	return &dBAdminServiceClient{cc}
}

func (c *dBAdminServiceClient) GetDBStatus(ctx context.Context, in *GetDBStatusRequest, opts ...grpc.CallOption) (*GetDBStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetDBStatusResponse)
	err := c.cc.Invoke(ctx, DBAdminService_GetDBStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dBAdminServiceClient) ExecuteQuery(ctx context.Context, in *ExecuteQueryRequest, opts ...grpc.CallOption) (*ExecuteQueryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ExecuteQueryResponse)
	err := c.cc.Invoke(ctx, DBAdminService_ExecuteQuery_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DBAdminServiceServer is the server API for DBAdminService service.
// All implementations must embed UnimplementedDBAdminServiceServer
// for forward compatibility.
//
// DB Admin Service
type DBAdminServiceServer interface {
	GetDBStatus(context.Context, *GetDBStatusRequest) (*GetDBStatusResponse, error)
	ExecuteQuery(context.Context, *ExecuteQueryRequest) (*ExecuteQueryResponse, error)
	mustEmbedUnimplementedDBAdminServiceServer()
}

// UnimplementedDBAdminServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedDBAdminServiceServer struct{}

func (UnimplementedDBAdminServiceServer) GetDBStatus(context.Context, *GetDBStatusRequest) (*GetDBStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDBStatus not implemented")
}
func (UnimplementedDBAdminServiceServer) ExecuteQuery(context.Context, *ExecuteQueryRequest) (*ExecuteQueryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExecuteQuery not implemented")
}
func (UnimplementedDBAdminServiceServer) mustEmbedUnimplementedDBAdminServiceServer() {}
func (UnimplementedDBAdminServiceServer) testEmbeddedByValue()                        {}

// UnsafeDBAdminServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DBAdminServiceServer will
// result in compilation errors.
type UnsafeDBAdminServiceServer interface {
	mustEmbedUnimplementedDBAdminServiceServer()
}

func RegisterDBAdminServiceServer(s grpc.ServiceRegistrar, srv DBAdminServiceServer) {
	// If the following call pancis, it indicates UnimplementedDBAdminServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&DBAdminService_ServiceDesc, srv)
}

func _DBAdminService_GetDBStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDBStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DBAdminServiceServer).GetDBStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DBAdminService_GetDBStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DBAdminServiceServer).GetDBStatus(ctx, req.(*GetDBStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DBAdminService_ExecuteQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExecuteQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DBAdminServiceServer).ExecuteQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DBAdminService_ExecuteQuery_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DBAdminServiceServer).ExecuteQuery(ctx, req.(*ExecuteQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// DBAdminService_ServiceDesc is the grpc.ServiceDesc for DBAdminService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DBAdminService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "orchestrator.v1.DBAdminService",
	HandlerType: (*DBAdminServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetDBStatus",
			Handler:    _DBAdminService_GetDBStatus_Handler,
		},
		{
			MethodName: "ExecuteQuery",
			Handler:    _DBAdminService_ExecuteQuery_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/orchestrator/v1/orchestrator.proto",
}

const (
	AuditService_GetAuditLogs_FullMethodName     = "/orchestrator.v1.AuditService/GetAuditLogs"
	AuditService_VerifyAuditEntry_FullMethodName = "/orchestrator.v1.AuditService/VerifyAuditEntry"
)

// AuditServiceClient is the client API for AuditService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Audit Service
type AuditServiceClient interface {
	GetAuditLogs(ctx context.Context, in *GetAuditLogsRequest, opts ...grpc.CallOption) (*GetAuditLogsResponse, error)
	VerifyAuditEntry(ctx context.Context, in *VerifyAuditEntryRequest, opts ...grpc.CallOption) (*VerifyAuditEntryResponse, error)
}

type auditServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAuditServiceClient(cc grpc.ClientConnInterface) AuditServiceClient {
	return &auditServiceClient{cc}
}

func (c *auditServiceClient) GetAuditLogs(ctx context.Context, in *GetAuditLogsRequest, opts ...grpc.CallOption) (*GetAuditLogsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAuditLogsResponse)
	err := c.cc.Invoke(ctx, AuditService_GetAuditLogs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *auditServiceClient) VerifyAuditEntry(ctx context.Context, in *VerifyAuditEntryRequest, opts ...grpc.CallOption) (*VerifyAuditEntryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(VerifyAuditEntryResponse)
	err := c.cc.Invoke(ctx, AuditService_VerifyAuditEntry_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AuditServiceServer is the server API for AuditService service.
// All implementations must embed UnimplementedAuditServiceServer
// for forward compatibility.
//
// Audit Service
type AuditServiceServer interface {
	GetAuditLogs(context.Context, *GetAuditLogsRequest) (*GetAuditLogsResponse, error)
	VerifyAuditEntry(context.Context, *VerifyAuditEntryRequest) (*VerifyAuditEntryResponse, error)
	mustEmbedUnimplementedAuditServiceServer()
}

// UnimplementedAuditServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAuditServiceServer struct{}

func (UnimplementedAuditServiceServer) GetAuditLogs(context.Context, *GetAuditLogsRequest) (*GetAuditLogsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAuditLogs not implemented")
}
func (UnimplementedAuditServiceServer) VerifyAuditEntry(context.Context, *VerifyAuditEntryRequest) (*VerifyAuditEntryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyAuditEntry not implemented")
}
func (UnimplementedAuditServiceServer) mustEmbedUnimplementedAuditServiceServer() {}
func (UnimplementedAuditServiceServer) testEmbeddedByValue()                      {}

// UnsafeAuditServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AuditServiceServer will
// result in compilation errors.
type UnsafeAuditServiceServer interface {
	mustEmbedUnimplementedAuditServiceServer()
}

func RegisterAuditServiceServer(s grpc.ServiceRegistrar, srv AuditServiceServer) {
	// If the following call pancis, it indicates UnimplementedAuditServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AuditService_ServiceDesc, srv)
}

func _AuditService_GetAuditLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAuditLogsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuditServiceServer).GetAuditLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuditService_GetAuditLogs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuditServiceServer).GetAuditLogs(ctx, req.(*GetAuditLogsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuditService_VerifyAuditEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyAuditEntryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuditServiceServer).VerifyAuditEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuditService_VerifyAuditEntry_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuditServiceServer).VerifyAuditEntry(ctx, req.(*VerifyAuditEntryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AuditService_ServiceDesc is the grpc.ServiceDesc for AuditService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AuditService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "orchestrator.v1.AuditService",
	HandlerType: (*AuditServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAuditLogs",
			Handler:    _AuditService_GetAuditLogs_Handler,
		},
		{
			MethodName: "VerifyAuditEntry",
			Handler:    _AuditService_VerifyAuditEntry_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/orchestrator/v1/orchestrator.proto",
}
