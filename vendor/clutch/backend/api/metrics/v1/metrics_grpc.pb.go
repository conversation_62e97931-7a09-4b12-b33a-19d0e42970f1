// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.17.3
// source: metrics/v1/metrics.proto

package metricsv1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	MetricsAPI_GetMetrics_FullMethodName = "/clutch.metrics.v1.MetricsAPI/GetMetrics"
)

// MetricsAPIClient is the client API for MetricsAPI service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MetricsAPIClient interface {
	GetMetrics(ctx context.Context, in *GetMetricsRequest, opts ...grpc.CallOption) (*GetMetricsResponse, error)
}

type metricsAPIClient struct {
	cc grpc.ClientConnInterface
}

func NewMetricsAPIClient(cc grpc.ClientConnInterface) MetricsAPIClient {
	return &metricsAPIClient{cc}
}

func (c *metricsAPIClient) GetMetrics(ctx context.Context, in *GetMetricsRequest, opts ...grpc.CallOption) (*GetMetricsResponse, error) {
	out := new(GetMetricsResponse)
	err := c.cc.Invoke(ctx, MetricsAPI_GetMetrics_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MetricsAPIServer is the server API for MetricsAPI service.
// All implementations should embed UnimplementedMetricsAPIServer
// for forward compatibility
type MetricsAPIServer interface {
	GetMetrics(context.Context, *GetMetricsRequest) (*GetMetricsResponse, error)
}

// UnimplementedMetricsAPIServer should be embedded to have forward compatible implementations.
type UnimplementedMetricsAPIServer struct {
}

func (UnimplementedMetricsAPIServer) GetMetrics(context.Context, *GetMetricsRequest) (*GetMetricsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMetrics not implemented")
}

// UnsafeMetricsAPIServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MetricsAPIServer will
// result in compilation errors.
type UnsafeMetricsAPIServer interface {
	mustEmbedUnimplementedMetricsAPIServer()
}

func RegisterMetricsAPIServer(s grpc.ServiceRegistrar, srv MetricsAPIServer) {
	s.RegisterService(&MetricsAPI_ServiceDesc, srv)
}

func _MetricsAPI_GetMetrics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMetricsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MetricsAPIServer).GetMetrics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MetricsAPI_GetMetrics_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MetricsAPIServer).GetMetrics(ctx, req.(*GetMetricsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// MetricsAPI_ServiceDesc is the grpc.ServiceDesc for MetricsAPI service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MetricsAPI_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "clutch.metrics.v1.MetricsAPI",
	HandlerType: (*MetricsAPIServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetMetrics",
			Handler:    _MetricsAPI_GetMetrics_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "metrics/v1/metrics.proto",
}
