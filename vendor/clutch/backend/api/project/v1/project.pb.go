// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v3.17.3
// source: project/v1/project.proto

package projectv1

import (
	_ "github.com/lyft/clutch/backend/api/api/v1"
	v1 "github.com/lyft/clutch/backend/api/core/project/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	status "google.golang.org/genproto/googleapis/rpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetProjectsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Projects []string `protobuf:"bytes,1,rep,name=projects,proto3" json:"projects,omitempty"`
	Users    []string `protobuf:"bytes,2,rep,name=users,proto3" json:"users,omitempty"`
	// If this is set to true, the api will exclude all project dependencies for the requested projects.
	// https://github.com/lyft/clutch/blob/2304adf0e0189734d8a36803964214ee3bc73fbc/api/core/project/v1/project.proto#L19
	ExcludeDependencies bool `protobuf:"varint,3,opt,name=exclude_dependencies,json=excludeDependencies,proto3" json:"exclude_dependencies,omitempty"`
}

func (x *GetProjectsRequest) Reset() {
	*x = GetProjectsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_project_v1_project_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProjectsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProjectsRequest) ProtoMessage() {}

func (x *GetProjectsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_project_v1_project_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProjectsRequest.ProtoReflect.Descriptor instead.
func (*GetProjectsRequest) Descriptor() ([]byte, []int) {
	return file_project_v1_project_proto_rawDescGZIP(), []int{0}
}

func (x *GetProjectsRequest) GetProjects() []string {
	if x != nil {
		return x.Projects
	}
	return nil
}

func (x *GetProjectsRequest) GetUsers() []string {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *GetProjectsRequest) GetExcludeDependencies() bool {
	if x != nil {
		return x.ExcludeDependencies
	}
	return false
}

type ProjectResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	From    *ProjectResult_From `protobuf:"bytes,1,opt,name=from,proto3" json:"from,omitempty"`
	Project *v1.Project         `protobuf:"bytes,2,opt,name=project,proto3" json:"project,omitempty"`
}

func (x *ProjectResult) Reset() {
	*x = ProjectResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_project_v1_project_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProjectResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProjectResult) ProtoMessage() {}

func (x *ProjectResult) ProtoReflect() protoreflect.Message {
	mi := &file_project_v1_project_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProjectResult.ProtoReflect.Descriptor instead.
func (*ProjectResult) Descriptor() ([]byte, []int) {
	return file_project_v1_project_proto_rawDescGZIP(), []int{1}
}

func (x *ProjectResult) GetFrom() *ProjectResult_From {
	if x != nil {
		return x.From
	}
	return nil
}

func (x *ProjectResult) GetProject() *v1.Project {
	if x != nil {
		return x.Project
	}
	return nil
}

type GetProjectsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Results         map[string]*ProjectResult `protobuf:"bytes,1,rep,name=results,proto3" json:"results,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	PartialFailures []*status.Status          `protobuf:"bytes,2,rep,name=partial_failures,json=partialFailures,proto3" json:"partial_failures,omitempty"`
}

func (x *GetProjectsResponse) Reset() {
	*x = GetProjectsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_project_v1_project_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetProjectsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProjectsResponse) ProtoMessage() {}

func (x *GetProjectsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_project_v1_project_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProjectsResponse.ProtoReflect.Descriptor instead.
func (*GetProjectsResponse) Descriptor() ([]byte, []int) {
	return file_project_v1_project_proto_rawDescGZIP(), []int{2}
}

func (x *GetProjectsResponse) GetResults() map[string]*ProjectResult {
	if x != nil {
		return x.Results
	}
	return nil
}

func (x *GetProjectsResponse) GetPartialFailures() []*status.Status {
	if x != nil {
		return x.PartialFailures
	}
	return nil
}

type ProjectResult_From struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Selected bool     `protobuf:"varint,1,opt,name=selected,proto3" json:"selected,omitempty"`
	Users    []string `protobuf:"bytes,2,rep,name=users,proto3" json:"users,omitempty"`
}

func (x *ProjectResult_From) Reset() {
	*x = ProjectResult_From{}
	if protoimpl.UnsafeEnabled {
		mi := &file_project_v1_project_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProjectResult_From) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProjectResult_From) ProtoMessage() {}

func (x *ProjectResult_From) ProtoReflect() protoreflect.Message {
	mi := &file_project_v1_project_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProjectResult_From.ProtoReflect.Descriptor instead.
func (*ProjectResult_From) Descriptor() ([]byte, []int) {
	return file_project_v1_project_proto_rawDescGZIP(), []int{1, 0}
}

func (x *ProjectResult_From) GetSelected() bool {
	if x != nil {
		return x.Selected
	}
	return false
}

func (x *ProjectResult_From) GetUsers() []string {
	if x != nil {
		return x.Users
	}
	return nil
}

var File_project_v1_project_proto protoreflect.FileDescriptor

var file_project_v1_project_proto_rawDesc = []byte{
	0x0a, 0x18, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x63, 0x6c, 0x75, 0x74,
	0x63, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x18, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x72, 0x70, 0x63,
	0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x79, 0x0a,
	0x12, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x12,
	0x14, 0x0a, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05,
	0x75, 0x73, 0x65, 0x72, 0x73, 0x12, 0x31, 0x0a, 0x14, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65,
	0x5f, 0x64, 0x65, 0x70, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x69, 0x65, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x13, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x44, 0x65, 0x70, 0x65,
	0x6e, 0x64, 0x65, 0x6e, 0x63, 0x69, 0x65, 0x73, 0x22, 0xbf, 0x01, 0x0a, 0x0d, 0x50, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x39, 0x0a, 0x04, 0x66, 0x72,
	0x6f, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63,
	0x68, 0x2e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x46, 0x72, 0x6f, 0x6d, 0x52,
	0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0x39, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x1a, 0x38, 0x0a, 0x04, 0x46, 0x72, 0x6f, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x73, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x22, 0x87, 0x02, 0x0a, 0x13, 0x47,
	0x65, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x53, 0x0a, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x04, 0xa8, 0xe1, 0x1c, 0x00, 0x52, 0x07,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x12, 0x3d, 0x0a, 0x10, 0x70, 0x61, 0x72, 0x74, 0x69,
	0x61, 0x6c, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0f, 0x70, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x46, 0x61,
	0x69, 0x6c, 0x75, 0x72, 0x65, 0x73, 0x1a, 0x5c, 0x0a, 0x0c, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x36, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68,
	0x2e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x32, 0x95, 0x01, 0x0a, 0x0a, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x41, 0x50, 0x49, 0x12, 0x86, 0x01, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x73, 0x12, 0x25, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x63, 0x6c, 0x75,
	0x74, 0x63, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x28, 0xaa, 0xe1, 0x1c, 0x02, 0x08, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c,
	0x3a, 0x01, 0x2a, 0x22, 0x17, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x2f, 0x67, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x42, 0x39, 0x5a, 0x37,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6c, 0x79, 0x66, 0x74, 0x2f,
	0x63, 0x6c, 0x75, 0x74, 0x63, 0x68, 0x2f, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_project_v1_project_proto_rawDescOnce sync.Once
	file_project_v1_project_proto_rawDescData = file_project_v1_project_proto_rawDesc
)

func file_project_v1_project_proto_rawDescGZIP() []byte {
	file_project_v1_project_proto_rawDescOnce.Do(func() {
		file_project_v1_project_proto_rawDescData = protoimpl.X.CompressGZIP(file_project_v1_project_proto_rawDescData)
	})
	return file_project_v1_project_proto_rawDescData
}

var file_project_v1_project_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_project_v1_project_proto_goTypes = []interface{}{
	(*GetProjectsRequest)(nil),  // 0: clutch.project.v1.GetProjectsRequest
	(*ProjectResult)(nil),       // 1: clutch.project.v1.ProjectResult
	(*GetProjectsResponse)(nil), // 2: clutch.project.v1.GetProjectsResponse
	(*ProjectResult_From)(nil),  // 3: clutch.project.v1.ProjectResult.From
	nil,                         // 4: clutch.project.v1.GetProjectsResponse.ResultsEntry
	(*v1.Project)(nil),          // 5: clutch.core.project.v1.Project
	(*status.Status)(nil),       // 6: google.rpc.Status
}
var file_project_v1_project_proto_depIdxs = []int32{
	3, // 0: clutch.project.v1.ProjectResult.from:type_name -> clutch.project.v1.ProjectResult.From
	5, // 1: clutch.project.v1.ProjectResult.project:type_name -> clutch.core.project.v1.Project
	4, // 2: clutch.project.v1.GetProjectsResponse.results:type_name -> clutch.project.v1.GetProjectsResponse.ResultsEntry
	6, // 3: clutch.project.v1.GetProjectsResponse.partial_failures:type_name -> google.rpc.Status
	1, // 4: clutch.project.v1.GetProjectsResponse.ResultsEntry.value:type_name -> clutch.project.v1.ProjectResult
	0, // 5: clutch.project.v1.ProjectAPI.GetProjects:input_type -> clutch.project.v1.GetProjectsRequest
	2, // 6: clutch.project.v1.ProjectAPI.GetProjects:output_type -> clutch.project.v1.GetProjectsResponse
	6, // [6:7] is the sub-list for method output_type
	5, // [5:6] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_project_v1_project_proto_init() }
func file_project_v1_project_proto_init() {
	if File_project_v1_project_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_project_v1_project_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProjectsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_project_v1_project_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProjectResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_project_v1_project_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetProjectsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_project_v1_project_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProjectResult_From); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_project_v1_project_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_project_v1_project_proto_goTypes,
		DependencyIndexes: file_project_v1_project_proto_depIdxs,
		MessageInfos:      file_project_v1_project_proto_msgTypes,
	}.Build()
	File_project_v1_project_proto = out.File
	file_project_v1_project_proto_rawDesc = nil
	file_project_v1_project_proto_goTypes = nil
	file_project_v1_project_proto_depIdxs = nil
}
