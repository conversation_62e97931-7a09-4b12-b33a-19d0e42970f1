import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { MagnifyingGlassIcon, XMarkIcon } from '@heroicons/react/24/outline';

interface SearchResult {
  id: string;
  label: string;
  path: string;
  category: string;
  description?: string;
}

const SearchField: React.FC = () => {
  const [query, setQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [results, setResults] = useState<SearchResult[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const navigate = useNavigate();
  const inputRef = useRef<HTMLInputElement>(null);
  const resultsRef = useRef<HTMLDivElement>(null);

  // Mock search data - in a real app, this would come from an API
  const searchData: SearchResult[] = [
    { id: '1', label: 'Dashboard', path: '/dashboard', category: 'Navigation', description: 'Main dashboard overview' },
    { id: '2', label: 'Search Resources', path: '/search', category: 'Navigation', description: 'Search across all resources' },
    { id: '3', label: 'Discovery Wizard', path: '/discovery', category: 'Navigation', description: 'Discover new resources' },
    { id: '4', label: 'Workflows', path: '/workflows', category: 'Navigation', description: 'Manage workflows' },
    { id: '5', label: 'Envoy Config', path: '/envoy', category: 'Navigation', description: 'Configure Envoy proxy' },
    { id: '6', label: 'Autoscaler', path: '/autoscaler', category: 'Navigation', description: 'Autoscaling configuration' },
    { id: '7', label: 'Audit Logs', path: '/audit', category: 'Navigation', description: 'View audit logs' },
    { id: '8', label: 'Database Admin', path: '/admin/database', category: 'Admin', description: 'Database administration' },
    { id: '9', label: 'User Management', path: '/admin/users', category: 'Admin', description: 'Manage users' },
    { id: '10', label: 'RBAC Management', path: '/admin/rbac', category: 'Admin', description: 'Role-based access control' },
    { id: '11', label: 'Feature Flags', path: '/admin/featureflags', category: 'Admin', description: 'Manage feature flags' },
    { id: '12', label: 'Settings', path: '/admin/settings', category: 'Admin', description: 'System settings' },
  ];

  const performSearch = (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setResults([]);
      setShowResults(false);
      return;
    }

    const filtered = searchData.filter(item =>
      item.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.category.toLowerCase().includes(searchQuery.toLowerCase())
    );

    setResults(filtered);
    setShowResults(true);
    setSelectedIndex(-1);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);
    performSearch(value);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showResults || results.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => (prev < results.length - 1 ? prev + 1 : 0));
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => (prev > 0 ? prev - 1 : results.length - 1));
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < results.length) {
          handleResultClick(results[selectedIndex]);
        } else if (results.length > 0) {
          handleResultClick(results[0]);
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setShowResults(false);
        setQuery('');
        inputRef.current?.blur();
        break;
    }
  };

  const handleResultClick = (result: SearchResult) => {
    navigate(result.path);
    setIsOpen(false);
    setShowResults(false);
    setQuery('');
  };

  const handleSearchIconClick = () => {
    setIsOpen(true);
    setTimeout(() => inputRef.current?.focus(), 100);
  };

  const handleClose = () => {
    setIsOpen(false);
    setShowResults(false);
    setQuery('');
  };

  const handleClickOutside = (e: MouseEvent) => {
    if (resultsRef.current && !resultsRef.current.contains(e.target as Node)) {
      setShowResults(false);
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Global keyboard shortcut (/)
  useEffect(() => {
    const handleGlobalKeyDown = (e: KeyboardEvent) => {
      if (e.key === '/' && e.target !== inputRef.current && (e.target as HTMLElement).tagName !== 'INPUT') {
        e.preventDefault();
        setIsOpen(true);
        setTimeout(() => inputRef.current?.focus(), 100);
      }
    };

    document.addEventListener('keydown', handleGlobalKeyDown);
    return () => document.removeEventListener('keydown', handleGlobalKeyDown);
  }, []);

  if (!isOpen) {
    return (
      <button
        onClick={handleSearchIconClick}
        className="p-2 text-gray-400 hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-md"
        title="Search (Press / to focus)"
      >
        <MagnifyingGlassIcon className="h-5 w-5" />
      </button>
    );
  }

  return (
    <div className="relative w-full max-w-lg" ref={resultsRef}>
      <div className="relative">
        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
          <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
        </div>
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          className="block w-full pl-10 pr-10 py-2 border border-gray-600 rounded-md bg-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="Search pages, features..."
          autoComplete="off"
        />
        <button
          onClick={handleClose}
          className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-300"
        >
          <XMarkIcon className="h-5 w-5" />
        </button>
      </div>

      {/* Search Results */}
      {showResults && results.length > 0 && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-96 overflow-y-auto">
          {results.map((result, index) => (
            <div
              key={result.id}
              onClick={() => handleResultClick(result)}
              className={`px-4 py-3 cursor-pointer border-b border-gray-100 last:border-b-0 ${
                index === selectedIndex ? 'bg-blue-50' : 'hover:bg-gray-50'
              }`}
            >
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="text-sm font-medium text-gray-900">{result.label}</div>
                  {result.description && (
                    <div className="text-xs text-gray-500 mt-1">{result.description}</div>
                  )}
                </div>
                <div className="text-xs text-gray-400 ml-2">{result.category}</div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* No Results */}
      {showResults && query && results.length === 0 && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg">
          <div className="px-4 py-3 text-sm text-gray-500 text-center">
            No results found for "{query}"
          </div>
        </div>
      )}
    </div>
  );
};

export default SearchField;
