{"ast": null, "code": "import React,{useEffect,useState}from'react';import{useAuth}from'../auth/AuthContext';import{UsersIcon,CogIcon,ShieldCheckIcon,ChartBarIcon,ServerIcon,ClockIcon}from'@heroicons/react/24/outline';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AdminDashboard=()=>{const{user}=useAuth();const[stats,setStats]=useState(null);const[recentActivity,setRecentActivity]=useState([]);const[loading,setLoading]=useState(true);useEffect(()=>{fetchAdminData();},[]);const fetchAdminData=async()=>{try{// Fetch system statistics\nconst statsResponse=await fetch('/v1/admin/stats',{credentials:'include'});if(statsResponse.ok){const statsData=await statsResponse.json();setStats(statsData);}// Fetch recent activity\nconst activityResponse=await fetch('/v1/admin/activity',{credentials:'include'});if(activityResponse.ok){const activityData=await activityResponse.json();setRecentActivity(activityData.activities||[]);}}catch(error){console.error('Failed to fetch admin data:',error);}finally{setLoading(false);}};const getActivityIcon=type=>{switch(type){case'user_login':return/*#__PURE__*/_jsx(UsersIcon,{className:\"h-5 w-5 text-blue-500\"});case'user_created':return/*#__PURE__*/_jsx(UsersIcon,{className:\"h-5 w-5 text-green-500\"});case'connection_added':return/*#__PURE__*/_jsx(ServerIcon,{className:\"h-5 w-5 text-purple-500\"});case'workflow_executed':return/*#__PURE__*/_jsx(CogIcon,{className:\"h-5 w-5 text-orange-500\"});default:return/*#__PURE__*/_jsx(ClockIcon,{className:\"h-5 w-5 text-gray-500\"});}};const getActivityColor=type=>{switch(type){case'user_login':return'bg-blue-50 border-blue-200';case'user_created':return'bg-green-50 border-green-200';case'connection_added':return'bg-purple-50 border-purple-200';case'workflow_executed':return'bg-orange-50 border-orange-200';default:return'bg-gray-50 border-gray-200';}};if(loading){return/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center h-64\",children:/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"})});}return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white shadow rounded-lg p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-900\",children:\"Admin Dashboard\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-gray-600\",children:[\"Welcome back, \",user===null||user===void 0?void 0:user.name]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(ShieldCheckIcon,{className:\"h-8 w-8 text-green-500\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-green-600\",children:\"System Healthy\"})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white overflow-hidden shadow rounded-lg\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"p-5\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(UsersIcon,{className:\"h-6 w-6 text-gray-400\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"ml-5 w-0 flex-1\",children:/*#__PURE__*/_jsxs(\"dl\",{children:[/*#__PURE__*/_jsx(\"dt\",{className:\"text-sm font-medium text-gray-500 truncate\",children:\"Total Users\"}),/*#__PURE__*/_jsx(\"dd\",{className:\"text-lg font-medium text-gray-900\",children:(stats===null||stats===void 0?void 0:stats.totalUsers)||0})]})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-gray-50 px-5 py-3\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-green-600 font-medium\",children:(stats===null||stats===void 0?void 0:stats.activeUsers)||0}),/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-500\",children:\" active\"})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white overflow-hidden shadow rounded-lg\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"p-5\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(ServerIcon,{className:\"h-6 w-6 text-gray-400\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"ml-5 w-0 flex-1\",children:/*#__PURE__*/_jsxs(\"dl\",{children:[/*#__PURE__*/_jsx(\"dt\",{className:\"text-sm font-medium text-gray-500 truncate\",children:\"Connections\"}),/*#__PURE__*/_jsx(\"dd\",{className:\"text-lg font-medium text-gray-900\",children:(stats===null||stats===void 0?void 0:stats.totalConnections)||0})]})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-gray-50 px-5 py-3\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-green-600 font-medium\",children:(stats===null||stats===void 0?void 0:stats.activeConnections)||0}),/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-500\",children:\" active\"})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white overflow-hidden shadow rounded-lg\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"p-5\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(CogIcon,{className:\"h-6 w-6 text-gray-400\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"ml-5 w-0 flex-1\",children:/*#__PURE__*/_jsxs(\"dl\",{children:[/*#__PURE__*/_jsx(\"dt\",{className:\"text-sm font-medium text-gray-500 truncate\",children:\"Workflows\"}),/*#__PURE__*/_jsx(\"dd\",{className:\"text-lg font-medium text-gray-900\",children:(stats===null||stats===void 0?void 0:stats.totalWorkflows)||0})]})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-gray-50 px-5 py-3\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-blue-600 font-medium\",children:(stats===null||stats===void 0?void 0:stats.runningWorkflows)||0}),/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-500\",children:\" running\"})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white overflow-hidden shadow rounded-lg\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"p-5\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(ChartBarIcon,{className:\"h-6 w-6 text-gray-400\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"ml-5 w-0 flex-1\",children:/*#__PURE__*/_jsxs(\"dl\",{children:[/*#__PURE__*/_jsx(\"dt\",{className:\"text-sm font-medium text-gray-500 truncate\",children:\"System Uptime\"}),/*#__PURE__*/_jsx(\"dd\",{className:\"text-lg font-medium text-gray-900\",children:(stats===null||stats===void 0?void 0:stats.systemUptime)||'N/A'})]})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-gray-50 px-5 py-3\",children:/*#__PURE__*/_jsx(\"div\",{className:\"text-sm\",children:/*#__PURE__*/_jsxs(\"span\",{className:\"text-gray-500\",children:[\"Last backup: \",(stats===null||stats===void 0?void 0:stats.lastBackup)||'N/A']})})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white shadow rounded-lg\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"px-6 py-4 border-b border-gray-200\",children:/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900\",children:\"Recent Activity\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"divide-y divide-gray-200\",children:recentActivity.length>0?recentActivity.map(activity=>/*#__PURE__*/_jsx(\"div\",{className:\"px-6 py-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0 p-2 rounded-full border \".concat(getActivityColor(activity.type)),children:getActivityIcon(activity.type)}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 min-w-0\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-gray-900\",children:activity.description}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-gray-500\",children:[\"by \",activity.user]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0 text-sm text-gray-500\",children:activity.timestamp})]})},activity.id)):/*#__PURE__*/_jsxs(\"div\",{className:\"px-6 py-8 text-center\",children:[/*#__PURE__*/_jsx(ClockIcon,{className:\"mx-auto h-12 w-12 text-gray-400\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"mt-2 text-sm font-medium text-gray-900\",children:\"No recent activity\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-gray-500\",children:\"Activity will appear here as users interact with the system.\"})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white shadow rounded-lg p-6\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-4\",children:\"Quick Actions\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-3 gap-4\",children:[/*#__PURE__*/_jsxs(\"button\",{className:\"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50\",children:[/*#__PURE__*/_jsx(UsersIcon,{className:\"h-5 w-5 mr-2\"}),\"Manage Users\"]}),/*#__PURE__*/_jsxs(\"button\",{className:\"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50\",children:[/*#__PURE__*/_jsx(ServerIcon,{className:\"h-5 w-5 mr-2\"}),\"View Connections\"]}),/*#__PURE__*/_jsxs(\"button\",{className:\"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50\",children:[/*#__PURE__*/_jsx(CogIcon,{className:\"h-5 w-5 mr-2\"}),\"System Settings\"]})]})]})]});};export default AdminDashboard;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useAuth", "UsersIcon", "CogIcon", "ShieldCheckIcon", "ChartBarIcon", "ServerIcon", "ClockIcon", "jsx", "_jsx", "jsxs", "_jsxs", "AdminDashboard", "user", "stats", "setStats", "recentActivity", "setRecentActivity", "loading", "setLoading", "fetchAdminData", "statsResponse", "fetch", "credentials", "ok", "statsData", "json", "activityResponse", "activityData", "activities", "error", "console", "getActivityIcon", "type", "className", "getActivityColor", "children", "name", "totalUsers", "activeUsers", "totalConnections", "activeConnections", "totalWorkflows", "runningWorkflows", "systemUptime", "lastBackup", "length", "map", "activity", "concat", "description", "timestamp", "id"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/AdminDashboard.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useAuth } from '../auth/AuthContext';\nimport {\n  UsersIcon,\n  CogIcon,\n  ShieldCheckIcon,\n  ChartBarIcon,\n  ServerIcon,\n  ClockIcon,\n  ExclamationTriangleIcon,\n  CheckCircleIcon,\n} from '@heroicons/react/24/outline';\n\ninterface SystemStats {\n  totalUsers: number;\n  activeUsers: number;\n  totalConnections: number;\n  activeConnections: number;\n  totalWorkflows: number;\n  runningWorkflows: number;\n  systemUptime: string;\n  lastBackup: string;\n}\n\ninterface RecentActivity {\n  id: string;\n  type: 'user_login' | 'user_created' | 'connection_added' | 'workflow_executed';\n  user: string;\n  description: string;\n  timestamp: string;\n}\n\nconst AdminDashboard: React.FC = () => {\n  const { user } = useAuth();\n  const [stats, setStats] = useState<SystemStats | null>(null);\n  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    fetchAdminData();\n  }, []);\n\n  const fetchAdminData = async () => {\n    try {\n      // Fetch system statistics\n      const statsResponse = await fetch('/v1/admin/stats', {\n        credentials: 'include',\n      });\n      \n      if (statsResponse.ok) {\n        const statsData = await statsResponse.json();\n        setStats(statsData);\n      }\n\n      // Fetch recent activity\n      const activityResponse = await fetch('/v1/admin/activity', {\n        credentials: 'include',\n      });\n      \n      if (activityResponse.ok) {\n        const activityData = await activityResponse.json();\n        setRecentActivity(activityData.activities || []);\n      }\n    } catch (error) {\n      console.error('Failed to fetch admin data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getActivityIcon = (type: string) => {\n    switch (type) {\n      case 'user_login':\n        return <UsersIcon className=\"h-5 w-5 text-blue-500\" />;\n      case 'user_created':\n        return <UsersIcon className=\"h-5 w-5 text-green-500\" />;\n      case 'connection_added':\n        return <ServerIcon className=\"h-5 w-5 text-purple-500\" />;\n      case 'workflow_executed':\n        return <CogIcon className=\"h-5 w-5 text-orange-500\" />;\n      default:\n        return <ClockIcon className=\"h-5 w-5 text-gray-500\" />;\n    }\n  };\n\n  const getActivityColor = (type: string) => {\n    switch (type) {\n      case 'user_login':\n        return 'bg-blue-50 border-blue-200';\n      case 'user_created':\n        return 'bg-green-50 border-green-200';\n      case 'connection_added':\n        return 'bg-purple-50 border-purple-200';\n      case 'workflow_executed':\n        return 'bg-orange-50 border-orange-200';\n      default:\n        return 'bg-gray-50 border-gray-200';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">Admin Dashboard</h1>\n            <p className=\"text-gray-600\">Welcome back, {user?.name}</p>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            <ShieldCheckIcon className=\"h-8 w-8 text-green-500\" />\n            <span className=\"text-sm font-medium text-green-600\">System Healthy</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Statistics Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <UsersIcon className=\"h-6 w-6 text-gray-400\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">Total Users</dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">{stats?.totalUsers || 0}</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n          <div className=\"bg-gray-50 px-5 py-3\">\n            <div className=\"text-sm\">\n              <span className=\"text-green-600 font-medium\">{stats?.activeUsers || 0}</span>\n              <span className=\"text-gray-500\"> active</span>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <ServerIcon className=\"h-6 w-6 text-gray-400\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">Connections</dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">{stats?.totalConnections || 0}</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n          <div className=\"bg-gray-50 px-5 py-3\">\n            <div className=\"text-sm\">\n              <span className=\"text-green-600 font-medium\">{stats?.activeConnections || 0}</span>\n              <span className=\"text-gray-500\"> active</span>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <CogIcon className=\"h-6 w-6 text-gray-400\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">Workflows</dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">{stats?.totalWorkflows || 0}</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n          <div className=\"bg-gray-50 px-5 py-3\">\n            <div className=\"text-sm\">\n              <span className=\"text-blue-600 font-medium\">{stats?.runningWorkflows || 0}</span>\n              <span className=\"text-gray-500\"> running</span>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <ChartBarIcon className=\"h-6 w-6 text-gray-400\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">System Uptime</dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">{stats?.systemUptime || 'N/A'}</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n          <div className=\"bg-gray-50 px-5 py-3\">\n            <div className=\"text-sm\">\n              <span className=\"text-gray-500\">Last backup: {stats?.lastBackup || 'N/A'}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Recent Activity */}\n      <div className=\"bg-white shadow rounded-lg\">\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <h3 className=\"text-lg font-medium text-gray-900\">Recent Activity</h3>\n        </div>\n        <div className=\"divide-y divide-gray-200\">\n          {recentActivity.length > 0 ? (\n            recentActivity.map((activity) => (\n              <div key={activity.id} className=\"px-6 py-4\">\n                <div className=\"flex items-center space-x-3\">\n                  <div className={`flex-shrink-0 p-2 rounded-full border ${getActivityColor(activity.type)}`}>\n                    {getActivityIcon(activity.type)}\n                  </div>\n                  <div className=\"flex-1 min-w-0\">\n                    <p className=\"text-sm font-medium text-gray-900\">{activity.description}</p>\n                    <p className=\"text-sm text-gray-500\">by {activity.user}</p>\n                  </div>\n                  <div className=\"flex-shrink-0 text-sm text-gray-500\">\n                    {activity.timestamp}\n                  </div>\n                </div>\n              </div>\n            ))\n          ) : (\n            <div className=\"px-6 py-8 text-center\">\n              <ClockIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n              <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No recent activity</h3>\n              <p className=\"mt-1 text-sm text-gray-500\">Activity will appear here as users interact with the system.</p>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Quick Actions</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <button className=\"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50\">\n            <UsersIcon className=\"h-5 w-5 mr-2\" />\n            Manage Users\n          </button>\n          <button className=\"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50\">\n            <ServerIcon className=\"h-5 w-5 mr-2\" />\n            View Connections\n          </button>\n          <button className=\"flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50\">\n            <CogIcon className=\"h-5 w-5 mr-2\" />\n            System Settings\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AdminDashboard;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,OAAO,KAAQ,qBAAqB,CAC7C,OACEC,SAAS,CACTC,OAAO,CACPC,eAAe,CACfC,YAAY,CACZC,UAAU,CACVC,SAAS,KAGJ,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAqBrC,KAAM,CAAAC,cAAwB,CAAGA,CAAA,GAAM,CACrC,KAAM,CAAEC,IAAK,CAAC,CAAGZ,OAAO,CAAC,CAAC,CAC1B,KAAM,CAACa,KAAK,CAAEC,QAAQ,CAAC,CAAGf,QAAQ,CAAqB,IAAI,CAAC,CAC5D,KAAM,CAACgB,cAAc,CAAEC,iBAAiB,CAAC,CAAGjB,QAAQ,CAAmB,EAAE,CAAC,CAC1E,KAAM,CAACkB,OAAO,CAAEC,UAAU,CAAC,CAAGnB,QAAQ,CAAC,IAAI,CAAC,CAE5CD,SAAS,CAAC,IAAM,CACdqB,cAAc,CAAC,CAAC,CAClB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAA,cAAc,CAAG,KAAAA,CAAA,GAAY,CACjC,GAAI,CACF;AACA,KAAM,CAAAC,aAAa,CAAG,KAAM,CAAAC,KAAK,CAAC,iBAAiB,CAAE,CACnDC,WAAW,CAAE,SACf,CAAC,CAAC,CAEF,GAAIF,aAAa,CAACG,EAAE,CAAE,CACpB,KAAM,CAAAC,SAAS,CAAG,KAAM,CAAAJ,aAAa,CAACK,IAAI,CAAC,CAAC,CAC5CX,QAAQ,CAACU,SAAS,CAAC,CACrB,CAEA;AACA,KAAM,CAAAE,gBAAgB,CAAG,KAAM,CAAAL,KAAK,CAAC,oBAAoB,CAAE,CACzDC,WAAW,CAAE,SACf,CAAC,CAAC,CAEF,GAAII,gBAAgB,CAACH,EAAE,CAAE,CACvB,KAAM,CAAAI,YAAY,CAAG,KAAM,CAAAD,gBAAgB,CAACD,IAAI,CAAC,CAAC,CAClDT,iBAAiB,CAACW,YAAY,CAACC,UAAU,EAAI,EAAE,CAAC,CAClD,CACF,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACrD,CAAC,OAAS,CACRX,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAa,eAAe,CAAIC,IAAY,EAAK,CACxC,OAAQA,IAAI,EACV,IAAK,YAAY,CACf,mBAAOxB,IAAA,CAACP,SAAS,EAACgC,SAAS,CAAC,uBAAuB,CAAE,CAAC,CACxD,IAAK,cAAc,CACjB,mBAAOzB,IAAA,CAACP,SAAS,EAACgC,SAAS,CAAC,wBAAwB,CAAE,CAAC,CACzD,IAAK,kBAAkB,CACrB,mBAAOzB,IAAA,CAACH,UAAU,EAAC4B,SAAS,CAAC,yBAAyB,CAAE,CAAC,CAC3D,IAAK,mBAAmB,CACtB,mBAAOzB,IAAA,CAACN,OAAO,EAAC+B,SAAS,CAAC,yBAAyB,CAAE,CAAC,CACxD,QACE,mBAAOzB,IAAA,CAACF,SAAS,EAAC2B,SAAS,CAAC,uBAAuB,CAAE,CAAC,CAC1D,CACF,CAAC,CAED,KAAM,CAAAC,gBAAgB,CAAIF,IAAY,EAAK,CACzC,OAAQA,IAAI,EACV,IAAK,YAAY,CACf,MAAO,4BAA4B,CACrC,IAAK,cAAc,CACjB,MAAO,8BAA8B,CACvC,IAAK,kBAAkB,CACrB,MAAO,gCAAgC,CACzC,IAAK,mBAAmB,CACtB,MAAO,gCAAgC,CACzC,QACE,MAAO,4BAA4B,CACvC,CACF,CAAC,CAED,GAAIf,OAAO,CAAE,CACX,mBACET,IAAA,QAAKyB,SAAS,CAAC,uCAAuC,CAAAE,QAAA,cACpD3B,IAAA,QAAKyB,SAAS,CAAC,gEAAgE,CAAM,CAAC,CACnF,CAAC,CAEV,CAEA,mBACEvB,KAAA,QAAKuB,SAAS,CAAC,WAAW,CAAAE,QAAA,eAExB3B,IAAA,QAAKyB,SAAS,CAAC,gCAAgC,CAAAE,QAAA,cAC7CzB,KAAA,QAAKuB,SAAS,CAAC,mCAAmC,CAAAE,QAAA,eAChDzB,KAAA,QAAAyB,QAAA,eACE3B,IAAA,OAAIyB,SAAS,CAAC,kCAAkC,CAAAE,QAAA,CAAC,iBAAe,CAAI,CAAC,cACrEzB,KAAA,MAAGuB,SAAS,CAAC,eAAe,CAAAE,QAAA,EAAC,gBAAc,CAACvB,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEwB,IAAI,EAAI,CAAC,EACxD,CAAC,cACN1B,KAAA,QAAKuB,SAAS,CAAC,6BAA6B,CAAAE,QAAA,eAC1C3B,IAAA,CAACL,eAAe,EAAC8B,SAAS,CAAC,wBAAwB,CAAE,CAAC,cACtDzB,IAAA,SAAMyB,SAAS,CAAC,oCAAoC,CAAAE,QAAA,CAAC,gBAAc,CAAM,CAAC,EACvE,CAAC,EACH,CAAC,CACH,CAAC,cAGNzB,KAAA,QAAKuB,SAAS,CAAC,sDAAsD,CAAAE,QAAA,eACnEzB,KAAA,QAAKuB,SAAS,CAAC,4CAA4C,CAAAE,QAAA,eACzD3B,IAAA,QAAKyB,SAAS,CAAC,KAAK,CAAAE,QAAA,cAClBzB,KAAA,QAAKuB,SAAS,CAAC,mBAAmB,CAAAE,QAAA,eAChC3B,IAAA,QAAKyB,SAAS,CAAC,eAAe,CAAAE,QAAA,cAC5B3B,IAAA,CAACP,SAAS,EAACgC,SAAS,CAAC,uBAAuB,CAAE,CAAC,CAC5C,CAAC,cACNzB,IAAA,QAAKyB,SAAS,CAAC,iBAAiB,CAAAE,QAAA,cAC9BzB,KAAA,OAAAyB,QAAA,eACE3B,IAAA,OAAIyB,SAAS,CAAC,4CAA4C,CAAAE,QAAA,CAAC,aAAW,CAAI,CAAC,cAC3E3B,IAAA,OAAIyB,SAAS,CAAC,mCAAmC,CAAAE,QAAA,CAAE,CAAAtB,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEwB,UAAU,GAAI,CAAC,CAAK,CAAC,EAC7E,CAAC,CACF,CAAC,EACH,CAAC,CACH,CAAC,cACN7B,IAAA,QAAKyB,SAAS,CAAC,sBAAsB,CAAAE,QAAA,cACnCzB,KAAA,QAAKuB,SAAS,CAAC,SAAS,CAAAE,QAAA,eACtB3B,IAAA,SAAMyB,SAAS,CAAC,4BAA4B,CAAAE,QAAA,CAAE,CAAAtB,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEyB,WAAW,GAAI,CAAC,CAAO,CAAC,cAC7E9B,IAAA,SAAMyB,SAAS,CAAC,eAAe,CAAAE,QAAA,CAAC,SAAO,CAAM,CAAC,EAC3C,CAAC,CACH,CAAC,EACH,CAAC,cAENzB,KAAA,QAAKuB,SAAS,CAAC,4CAA4C,CAAAE,QAAA,eACzD3B,IAAA,QAAKyB,SAAS,CAAC,KAAK,CAAAE,QAAA,cAClBzB,KAAA,QAAKuB,SAAS,CAAC,mBAAmB,CAAAE,QAAA,eAChC3B,IAAA,QAAKyB,SAAS,CAAC,eAAe,CAAAE,QAAA,cAC5B3B,IAAA,CAACH,UAAU,EAAC4B,SAAS,CAAC,uBAAuB,CAAE,CAAC,CAC7C,CAAC,cACNzB,IAAA,QAAKyB,SAAS,CAAC,iBAAiB,CAAAE,QAAA,cAC9BzB,KAAA,OAAAyB,QAAA,eACE3B,IAAA,OAAIyB,SAAS,CAAC,4CAA4C,CAAAE,QAAA,CAAC,aAAW,CAAI,CAAC,cAC3E3B,IAAA,OAAIyB,SAAS,CAAC,mCAAmC,CAAAE,QAAA,CAAE,CAAAtB,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAE0B,gBAAgB,GAAI,CAAC,CAAK,CAAC,EACnF,CAAC,CACF,CAAC,EACH,CAAC,CACH,CAAC,cACN/B,IAAA,QAAKyB,SAAS,CAAC,sBAAsB,CAAAE,QAAA,cACnCzB,KAAA,QAAKuB,SAAS,CAAC,SAAS,CAAAE,QAAA,eACtB3B,IAAA,SAAMyB,SAAS,CAAC,4BAA4B,CAAAE,QAAA,CAAE,CAAAtB,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAE2B,iBAAiB,GAAI,CAAC,CAAO,CAAC,cACnFhC,IAAA,SAAMyB,SAAS,CAAC,eAAe,CAAAE,QAAA,CAAC,SAAO,CAAM,CAAC,EAC3C,CAAC,CACH,CAAC,EACH,CAAC,cAENzB,KAAA,QAAKuB,SAAS,CAAC,4CAA4C,CAAAE,QAAA,eACzD3B,IAAA,QAAKyB,SAAS,CAAC,KAAK,CAAAE,QAAA,cAClBzB,KAAA,QAAKuB,SAAS,CAAC,mBAAmB,CAAAE,QAAA,eAChC3B,IAAA,QAAKyB,SAAS,CAAC,eAAe,CAAAE,QAAA,cAC5B3B,IAAA,CAACN,OAAO,EAAC+B,SAAS,CAAC,uBAAuB,CAAE,CAAC,CAC1C,CAAC,cACNzB,IAAA,QAAKyB,SAAS,CAAC,iBAAiB,CAAAE,QAAA,cAC9BzB,KAAA,OAAAyB,QAAA,eACE3B,IAAA,OAAIyB,SAAS,CAAC,4CAA4C,CAAAE,QAAA,CAAC,WAAS,CAAI,CAAC,cACzE3B,IAAA,OAAIyB,SAAS,CAAC,mCAAmC,CAAAE,QAAA,CAAE,CAAAtB,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAE4B,cAAc,GAAI,CAAC,CAAK,CAAC,EACjF,CAAC,CACF,CAAC,EACH,CAAC,CACH,CAAC,cACNjC,IAAA,QAAKyB,SAAS,CAAC,sBAAsB,CAAAE,QAAA,cACnCzB,KAAA,QAAKuB,SAAS,CAAC,SAAS,CAAAE,QAAA,eACtB3B,IAAA,SAAMyB,SAAS,CAAC,2BAA2B,CAAAE,QAAA,CAAE,CAAAtB,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAE6B,gBAAgB,GAAI,CAAC,CAAO,CAAC,cACjFlC,IAAA,SAAMyB,SAAS,CAAC,eAAe,CAAAE,QAAA,CAAC,UAAQ,CAAM,CAAC,EAC5C,CAAC,CACH,CAAC,EACH,CAAC,cAENzB,KAAA,QAAKuB,SAAS,CAAC,4CAA4C,CAAAE,QAAA,eACzD3B,IAAA,QAAKyB,SAAS,CAAC,KAAK,CAAAE,QAAA,cAClBzB,KAAA,QAAKuB,SAAS,CAAC,mBAAmB,CAAAE,QAAA,eAChC3B,IAAA,QAAKyB,SAAS,CAAC,eAAe,CAAAE,QAAA,cAC5B3B,IAAA,CAACJ,YAAY,EAAC6B,SAAS,CAAC,uBAAuB,CAAE,CAAC,CAC/C,CAAC,cACNzB,IAAA,QAAKyB,SAAS,CAAC,iBAAiB,CAAAE,QAAA,cAC9BzB,KAAA,OAAAyB,QAAA,eACE3B,IAAA,OAAIyB,SAAS,CAAC,4CAA4C,CAAAE,QAAA,CAAC,eAAa,CAAI,CAAC,cAC7E3B,IAAA,OAAIyB,SAAS,CAAC,mCAAmC,CAAAE,QAAA,CAAE,CAAAtB,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAE8B,YAAY,GAAI,KAAK,CAAK,CAAC,EACnF,CAAC,CACF,CAAC,EACH,CAAC,CACH,CAAC,cACNnC,IAAA,QAAKyB,SAAS,CAAC,sBAAsB,CAAAE,QAAA,cACnC3B,IAAA,QAAKyB,SAAS,CAAC,SAAS,CAAAE,QAAA,cACtBzB,KAAA,SAAMuB,SAAS,CAAC,eAAe,CAAAE,QAAA,EAAC,eAAa,CAAC,CAAAtB,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAE+B,UAAU,GAAI,KAAK,EAAO,CAAC,CAC7E,CAAC,CACH,CAAC,EACH,CAAC,EACH,CAAC,cAGNlC,KAAA,QAAKuB,SAAS,CAAC,4BAA4B,CAAAE,QAAA,eACzC3B,IAAA,QAAKyB,SAAS,CAAC,oCAAoC,CAAAE,QAAA,cACjD3B,IAAA,OAAIyB,SAAS,CAAC,mCAAmC,CAAAE,QAAA,CAAC,iBAAe,CAAI,CAAC,CACnE,CAAC,cACN3B,IAAA,QAAKyB,SAAS,CAAC,0BAA0B,CAAAE,QAAA,CACtCpB,cAAc,CAAC8B,MAAM,CAAG,CAAC,CACxB9B,cAAc,CAAC+B,GAAG,CAAEC,QAAQ,eAC1BvC,IAAA,QAAuByB,SAAS,CAAC,WAAW,CAAAE,QAAA,cAC1CzB,KAAA,QAAKuB,SAAS,CAAC,6BAA6B,CAAAE,QAAA,eAC1C3B,IAAA,QAAKyB,SAAS,0CAAAe,MAAA,CAA2Cd,gBAAgB,CAACa,QAAQ,CAACf,IAAI,CAAC,CAAG,CAAAG,QAAA,CACxFJ,eAAe,CAACgB,QAAQ,CAACf,IAAI,CAAC,CAC5B,CAAC,cACNtB,KAAA,QAAKuB,SAAS,CAAC,gBAAgB,CAAAE,QAAA,eAC7B3B,IAAA,MAAGyB,SAAS,CAAC,mCAAmC,CAAAE,QAAA,CAAEY,QAAQ,CAACE,WAAW,CAAI,CAAC,cAC3EvC,KAAA,MAAGuB,SAAS,CAAC,uBAAuB,CAAAE,QAAA,EAAC,KAAG,CAACY,QAAQ,CAACnC,IAAI,EAAI,CAAC,EACxD,CAAC,cACNJ,IAAA,QAAKyB,SAAS,CAAC,qCAAqC,CAAAE,QAAA,CACjDY,QAAQ,CAACG,SAAS,CAChB,CAAC,EACH,CAAC,EAZEH,QAAQ,CAACI,EAad,CACN,CAAC,cAEFzC,KAAA,QAAKuB,SAAS,CAAC,uBAAuB,CAAAE,QAAA,eACpC3B,IAAA,CAACF,SAAS,EAAC2B,SAAS,CAAC,iCAAiC,CAAE,CAAC,cACzDzB,IAAA,OAAIyB,SAAS,CAAC,wCAAwC,CAAAE,QAAA,CAAC,oBAAkB,CAAI,CAAC,cAC9E3B,IAAA,MAAGyB,SAAS,CAAC,4BAA4B,CAAAE,QAAA,CAAC,8DAA4D,CAAG,CAAC,EACvG,CACN,CACE,CAAC,EACH,CAAC,cAGNzB,KAAA,QAAKuB,SAAS,CAAC,gCAAgC,CAAAE,QAAA,eAC7C3B,IAAA,OAAIyB,SAAS,CAAC,wCAAwC,CAAAE,QAAA,CAAC,eAAa,CAAI,CAAC,cACzEzB,KAAA,QAAKuB,SAAS,CAAC,uCAAuC,CAAAE,QAAA,eACpDzB,KAAA,WAAQuB,SAAS,CAAC,oJAAoJ,CAAAE,QAAA,eACpK3B,IAAA,CAACP,SAAS,EAACgC,SAAS,CAAC,cAAc,CAAE,CAAC,eAExC,EAAQ,CAAC,cACTvB,KAAA,WAAQuB,SAAS,CAAC,oJAAoJ,CAAAE,QAAA,eACpK3B,IAAA,CAACH,UAAU,EAAC4B,SAAS,CAAC,cAAc,CAAE,CAAC,mBAEzC,EAAQ,CAAC,cACTvB,KAAA,WAAQuB,SAAS,CAAC,oJAAoJ,CAAAE,QAAA,eACpK3B,IAAA,CAACN,OAAO,EAAC+B,SAAS,CAAC,cAAc,CAAE,CAAC,kBAEtC,EAAQ,CAAC,EACN,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAtB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}