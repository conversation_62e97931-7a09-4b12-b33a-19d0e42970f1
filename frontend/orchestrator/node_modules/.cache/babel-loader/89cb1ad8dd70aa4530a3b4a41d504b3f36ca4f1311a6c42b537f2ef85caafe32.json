{"ast": null, "code": "import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\nimport axios from 'axios';\nconst initialState = {\n  stats: null,\n  loading: false,\n  error: null,\n  queryResult: null,\n  queryLoading: false\n};\nexport const fetchDatabaseStats = createAsyncThunk('database/fetchStats', async () => {\n  const response = await axios.get('/v1/database/stats');\n  return response.data;\n});\nexport const executeQuery = createAsyncThunk('database/executeQuery', async query => {\n  const response = await axios.post('/v1/database/query', {\n    query\n  });\n  return response.data;\n});\nconst databaseSlice = createSlice({\n  name: 'database',\n  initialState,\n  reducers: {\n    clearQueryResult: state => {\n      state.queryResult = null;\n    }\n  },\n  extraReducers: builder => {\n    builder.addCase(fetchDatabaseStats.pending, state => {\n      state.loading = true;\n      state.error = null;\n    }).addCase(fetchDatabaseStats.fulfilled, (state, action) => {\n      state.loading = false;\n      state.stats = action.payload;\n    }).addCase(fetchDatabaseStats.rejected, (state, action) => {\n      state.loading = false;\n      state.error = action.error.message || 'Failed to fetch database stats';\n    }).addCase(executeQuery.pending, state => {\n      state.queryLoading = true;\n      state.error = null;\n    }).addCase(executeQuery.fulfilled, (state, action) => {\n      state.queryLoading = false;\n      state.queryResult = action.payload;\n    }).addCase(executeQuery.rejected, (state, action) => {\n      state.queryLoading = false;\n      state.error = action.error.message || 'Query execution failed';\n    });\n  }\n});\nexport const {\n  clearQueryResult\n} = databaseSlice.actions;\nexport default databaseSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "axios", "initialState", "stats", "loading", "error", "query<PERSON><PERSON>ult", "queryLoading", "fetchDatabaseStats", "response", "get", "data", "execute<PERSON>uery", "query", "post", "databaseSlice", "name", "reducers", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state", "extraReducers", "builder", "addCase", "pending", "fulfilled", "action", "payload", "rejected", "message", "actions", "reducer"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/databaseSlice.ts"], "sourcesContent": ["import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';\nimport axios from 'axios';\n\nexport interface DatabaseStats {\n  total_resources: number;\n  total_workflows: number;\n  total_envoy_configs: number;\n  database_size: string;\n  cache_hit_rate: number;\n  cache_size: string;\n}\n\nexport interface DatabaseState {\n  stats: DatabaseStats | null;\n  loading: boolean;\n  error: string | null;\n  queryResult: any;\n  queryLoading: boolean;\n}\n\nconst initialState: DatabaseState = {\n  stats: null,\n  loading: false,\n  error: null,\n  queryResult: null,\n  queryLoading: false,\n};\n\nexport const fetchDatabaseStats = createAsyncThunk(\n  'database/fetchStats',\n  async () => {\n    const response = await axios.get('/v1/database/stats');\n    return response.data;\n  }\n);\n\nexport const executeQuery = createAsyncThunk(\n  'database/executeQuery',\n  async (query: string) => {\n    const response = await axios.post('/v1/database/query', { query });\n    return response.data;\n  }\n);\n\nconst databaseSlice = createSlice({\n  name: 'database',\n  initialState,\n  reducers: {\n    clearQueryResult: (state) => {\n      state.queryResult = null;\n    },\n  },\n  extraReducers: (builder) => {\n    builder\n      .addCase(fetchDatabaseStats.pending, (state) => {\n        state.loading = true;\n        state.error = null;\n      })\n      .addCase(fetchDatabaseStats.fulfilled, (state, action) => {\n        state.loading = false;\n        state.stats = action.payload;\n      })\n      .addCase(fetchDatabaseStats.rejected, (state, action) => {\n        state.loading = false;\n        state.error = action.error.message || 'Failed to fetch database stats';\n      })\n      .addCase(executeQuery.pending, (state) => {\n        state.queryLoading = true;\n        state.error = null;\n      })\n      .addCase(executeQuery.fulfilled, (state, action) => {\n        state.queryLoading = false;\n        state.queryResult = action.payload;\n      })\n      .addCase(executeQuery.rejected, (state, action) => {\n        state.queryLoading = false;\n        state.error = action.error.message || 'Query execution failed';\n      });\n  },\n});\n\nexport const { clearQueryResult } = databaseSlice.actions;\nexport default databaseSlice.reducer;\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,gBAAgB,QAAuB,kBAAkB;AAC/E,OAAOC,KAAK,MAAM,OAAO;AAmBzB,MAAMC,YAA2B,GAAG;EAClCC,KAAK,EAAE,IAAI;EACXC,OAAO,EAAE,KAAK;EACdC,KAAK,EAAE,IAAI;EACXC,WAAW,EAAE,IAAI;EACjBC,YAAY,EAAE;AAChB,CAAC;AAED,OAAO,MAAMC,kBAAkB,GAAGR,gBAAgB,CAChD,qBAAqB,EACrB,YAAY;EACV,MAAMS,QAAQ,GAAG,MAAMR,KAAK,CAACS,GAAG,CAAC,oBAAoB,CAAC;EACtD,OAAOD,QAAQ,CAACE,IAAI;AACtB,CACF,CAAC;AAED,OAAO,MAAMC,YAAY,GAAGZ,gBAAgB,CAC1C,uBAAuB,EACvB,MAAOa,KAAa,IAAK;EACvB,MAAMJ,QAAQ,GAAG,MAAMR,KAAK,CAACa,IAAI,CAAC,oBAAoB,EAAE;IAAED;EAAM,CAAC,CAAC;EAClE,OAAOJ,QAAQ,CAACE,IAAI;AACtB,CACF,CAAC;AAED,MAAMI,aAAa,GAAGhB,WAAW,CAAC;EAChCiB,IAAI,EAAE,UAAU;EAChBd,YAAY;EACZe,QAAQ,EAAE;IACRC,gBAAgB,EAAGC,KAAK,IAAK;MAC3BA,KAAK,CAACb,WAAW,GAAG,IAAI;IAC1B;EACF,CAAC;EACDc,aAAa,EAAGC,OAAO,IAAK;IAC1BA,OAAO,CACJC,OAAO,CAACd,kBAAkB,CAACe,OAAO,EAAGJ,KAAK,IAAK;MAC9CA,KAAK,CAACf,OAAO,GAAG,IAAI;MACpBe,KAAK,CAACd,KAAK,GAAG,IAAI;IACpB,CAAC,CAAC,CACDiB,OAAO,CAACd,kBAAkB,CAACgB,SAAS,EAAE,CAACL,KAAK,EAAEM,MAAM,KAAK;MACxDN,KAAK,CAACf,OAAO,GAAG,KAAK;MACrBe,KAAK,CAAChB,KAAK,GAAGsB,MAAM,CAACC,OAAO;IAC9B,CAAC,CAAC,CACDJ,OAAO,CAACd,kBAAkB,CAACmB,QAAQ,EAAE,CAACR,KAAK,EAAEM,MAAM,KAAK;MACvDN,KAAK,CAACf,OAAO,GAAG,KAAK;MACrBe,KAAK,CAACd,KAAK,GAAGoB,MAAM,CAACpB,KAAK,CAACuB,OAAO,IAAI,gCAAgC;IACxE,CAAC,CAAC,CACDN,OAAO,CAACV,YAAY,CAACW,OAAO,EAAGJ,KAAK,IAAK;MACxCA,KAAK,CAACZ,YAAY,GAAG,IAAI;MACzBY,KAAK,CAACd,KAAK,GAAG,IAAI;IACpB,CAAC,CAAC,CACDiB,OAAO,CAACV,YAAY,CAACY,SAAS,EAAE,CAACL,KAAK,EAAEM,MAAM,KAAK;MAClDN,KAAK,CAACZ,YAAY,GAAG,KAAK;MAC1BY,KAAK,CAACb,WAAW,GAAGmB,MAAM,CAACC,OAAO;IACpC,CAAC,CAAC,CACDJ,OAAO,CAACV,YAAY,CAACe,QAAQ,EAAE,CAACR,KAAK,EAAEM,MAAM,KAAK;MACjDN,KAAK,CAACZ,YAAY,GAAG,KAAK;MAC1BY,KAAK,CAACd,KAAK,GAAGoB,MAAM,CAACpB,KAAK,CAACuB,OAAO,IAAI,wBAAwB;IAChE,CAAC,CAAC;EACN;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEV;AAAiB,CAAC,GAAGH,aAAa,CAACc,OAAO;AACzD,eAAed,aAAa,CAACe,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}