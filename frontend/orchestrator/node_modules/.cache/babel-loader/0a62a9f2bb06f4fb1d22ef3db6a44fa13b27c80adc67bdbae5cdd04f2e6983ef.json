{"ast": null, "code": "import _objectSpread from\"/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useEffect,useState}from'react';import{useAuth}from'../auth/AuthContext';import{PlusIcon,PencilIcon,TrashIcon,EyeIcon,CheckCircleIcon,XCircleIcon,MagnifyingGlassIcon}from'@heroicons/react/24/outline';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const UserManagement=()=>{const{user:currentUser}=useAuth();const[users,setUsers]=useState([]);const[loading,setLoading]=useState(true);const[searchTerm,setSearchTerm]=useState('');const[showCreateModal,setShowCreateModal]=useState(false);const[showEditModal,setShowEditModal]=useState(false);const[showDeleteModal,setShowDeleteModal]=useState(false);const[selectedUser,setSelectedUser]=useState(null);const[createForm,setCreateForm]=useState({username:'',email:'',name:'',password:'',roles:['viewer'],groups:['cainuro-users']});const[editForm,setEditForm]=useState({email:'',name:'',roles:['viewer'],groups:['cainuro-users'],active:true,password:''});useEffect(()=>{fetchUsers();},[]);const fetchUsers=async()=>{try{const response=await fetch('/v1/users',{credentials:'include'});if(response.ok){const data=await response.json();setUsers(data.users||[]);}}catch(error){console.error('Failed to fetch users:',error);}finally{setLoading(false);}};const handleCreateUser=async e=>{e.preventDefault();try{const response=await fetch('/v1/users',{method:'POST',headers:{'Content-Type':'application/json'},credentials:'include',body:JSON.stringify(createForm)});if(response.ok){setShowCreateModal(false);setCreateForm({username:'',email:'',name:'',password:'',roles:['viewer'],groups:['cainuro-users']});fetchUsers();}}catch(error){console.error('Failed to create user:',error);}};const handleEditUser=user=>{setSelectedUser(user);setEditForm({email:user.email,name:user.name,roles:user.roles,groups:user.groups,active:user.active||true,password:''});setShowEditModal(true);};const handleUpdateUser=async e=>{e.preventDefault();if(!selectedUser)return;try{const updateData={email:editForm.email,name:editForm.name,roles:editForm.roles,groups:editForm.groups,active:editForm.active};if(editForm.password){updateData.password=editForm.password;}const response=await fetch(\"/v1/users/\".concat(selectedUser.id),{method:'PUT',headers:{'Content-Type':'application/json'},credentials:'include',body:JSON.stringify(updateData)});if(response.ok){setShowEditModal(false);setSelectedUser(null);fetchUsers();}}catch(error){console.error('Failed to update user:',error);}};const handleDeleteUser=user=>{setSelectedUser(user);setShowDeleteModal(true);};const confirmDeleteUser=async()=>{if(!selectedUser)return;try{const response=await fetch(\"/v1/users/\".concat(selectedUser.id),{method:'DELETE',credentials:'include'});if(response.ok){setShowDeleteModal(false);setSelectedUser(null);fetchUsers();}}catch(error){console.error('Failed to delete user:',error);}};const filteredUsers=users.filter(user=>user.username.toLowerCase().includes(searchTerm.toLowerCase())||user.email.toLowerCase().includes(searchTerm.toLowerCase())||user.name.toLowerCase().includes(searchTerm.toLowerCase()));const getRoleBadgeColor=role=>{switch(role){case'admin':return'bg-red-100 text-red-800';case'operator':return'bg-blue-100 text-blue-800';case'viewer':return'bg-green-100 text-green-800';default:return'bg-gray-100 text-gray-800';}};if(loading){return/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center h-64\",children:/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"})});}return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white shadow rounded-lg p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-900\",children:\"User Management\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Manage system users and their permissions\"})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setShowCreateModal(true),className:\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700\",children:[/*#__PURE__*/_jsx(PlusIcon,{className:\"h-4 w-4 mr-2\"}),\"Create User\"]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white shadow rounded-lg p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 relative\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",children:/*#__PURE__*/_jsx(MagnifyingGlassIcon,{className:\"h-5 w-5 text-gray-400\"})}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:\"Search users...\",value:searchTerm,onChange:e=>setSearchTerm(e.target.value),className:\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm text-gray-500\",children:[filteredUsers.length,\" of \",users.length,\" users\"]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white shadow rounded-lg overflow-hidden\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"px-6 py-4 border-b border-gray-200\",children:/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900\",children:\"Users\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"overflow-x-auto\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"min-w-full divide-y divide-gray-200\",children:[/*#__PURE__*/_jsx(\"thead\",{className:\"bg-gray-50\",children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"User\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Roles\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Status\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Last Login\"}),/*#__PURE__*/_jsx(\"th\",{className:\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{className:\"bg-white divide-y divide-gray-200\",children:filteredUsers.map(user=>/*#__PURE__*/_jsxs(\"tr\",{className:\"hover:bg-gray-50\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0 h-10 w-10\",children:/*#__PURE__*/_jsx(\"div\",{className:\"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-gray-700\",children:user.name.split(' ').map(n=>n[0]).join('').toUpperCase()})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"ml-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-sm font-medium text-gray-900\",children:user.name}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-500\",children:user.email}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-xs text-gray-400\",children:[\"@\",user.username]})]})]})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap gap-1\",children:user.roles.map(role=>/*#__PURE__*/_jsx(\"span\",{className:\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(getRoleBadgeColor(role)),children:role},role))})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap\",children:/*#__PURE__*/_jsx(\"span\",{className:\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(user.active?'bg-green-100 text-green-800':'bg-red-100 text-red-800'),children:user.active?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(CheckCircleIcon,{className:\"h-3 w-3 mr-1\"}),\"Active\"]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(XCircleIcon,{className:\"h-3 w-3 mr-1\"}),\"Inactive\"]})})}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",children:user.last_login?new Date(user.last_login).toLocaleDateString():'Never'}),/*#__PURE__*/_jsx(\"td\",{className:\"px-6 py-4 whitespace-nowrap text-sm font-medium\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-2\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setSelectedUser(user),className:\"text-blue-600 hover:text-blue-900\",title:\"View User\",children:/*#__PURE__*/_jsx(EyeIcon,{className:\"h-4 w-4\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleEditUser(user),className:\"text-yellow-600 hover:text-yellow-900\",title:\"Edit User\",children:/*#__PURE__*/_jsx(PencilIcon,{className:\"h-4 w-4\"})}),user.username!==(currentUser===null||currentUser===void 0?void 0:currentUser.username)&&user.id!==(currentUser===null||currentUser===void 0?void 0:currentUser.id)&&/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleDeleteUser(user),className:\"text-red-600 hover:text-red-900\",title:\"Delete User\",children:/*#__PURE__*/_jsx(TrashIcon,{className:\"h-4 w-4\"})})]})})]},user.id))})]})})]}),showCreateModal&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",children:/*#__PURE__*/_jsx(\"div\",{className:\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"mt-3\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-4\",children:\"Create New User\"}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleCreateUser,className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Username\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",required:true,value:createForm.username,onChange:e=>setCreateForm(_objectSpread(_objectSpread({},createForm),{},{username:e.target.value})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Email\"}),/*#__PURE__*/_jsx(\"input\",{type:\"email\",required:true,value:createForm.email,onChange:e=>setCreateForm(_objectSpread(_objectSpread({},createForm),{},{email:e.target.value})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Full Name\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",required:true,value:createForm.name,onChange:e=>setCreateForm(_objectSpread(_objectSpread({},createForm),{},{name:e.target.value})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Password\"}),/*#__PURE__*/_jsx(\"input\",{type:\"password\",required:true,value:createForm.password,onChange:e=>setCreateForm(_objectSpread(_objectSpread({},createForm),{},{password:e.target.value})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-end space-x-3 pt-4\",children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:()=>setShowCreateModal(false),className:\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\",children:\"Cancel\"}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700\",children:\"Create User\"})]})]})]})})}),showEditModal&&selectedUser&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",children:/*#__PURE__*/_jsx(\"div\",{className:\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"mt-3\",children:[/*#__PURE__*/_jsxs(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-4\",children:[\"Edit User: \",selectedUser.name]}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleUpdateUser,className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Email\"}),/*#__PURE__*/_jsx(\"input\",{type:\"email\",required:true,value:editForm.email,onChange:e=>setEditForm(_objectSpread(_objectSpread({},editForm),{},{email:e.target.value})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Full Name\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",required:true,value:editForm.name,onChange:e=>setEditForm(_objectSpread(_objectSpread({},editForm),{},{name:e.target.value})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"Roles\"}),/*#__PURE__*/_jsxs(\"select\",{multiple:true,value:editForm.roles,onChange:e=>setEditForm(_objectSpread(_objectSpread({},editForm),{},{roles:Array.from(e.target.selectedOptions,option=>option.value)})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"admin\",children:\"Admin\"}),/*#__PURE__*/_jsx(\"option\",{value:\"operator\",children:\"Operator\"}),/*#__PURE__*/_jsx(\"option\",{value:\"viewer\",children:\"Viewer\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-700\",children:\"New Password (optional)\"}),/*#__PURE__*/_jsx(\"input\",{type:\"password\",value:editForm.password,onChange:e=>setEditForm(_objectSpread(_objectSpread({},editForm),{},{password:e.target.value})),className:\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500\",placeholder:\"Leave blank to keep current password\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:editForm.active,onChange:e=>setEditForm(_objectSpread(_objectSpread({},editForm),{},{active:e.target.checked})),className:\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"}),/*#__PURE__*/_jsx(\"label\",{className:\"ml-2 block text-sm text-gray-900\",children:\"Active User\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-end space-x-3 pt-4\",children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:()=>setShowEditModal(false),className:\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\",children:\"Cancel\"}),/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700\",children:\"Update User\"})]})]})]})})}),showDeleteModal&&selectedUser&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",children:/*#__PURE__*/_jsx(\"div\",{className:\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"mt-3\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900 mb-4\",children:\"Delete User\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-gray-500 mb-4\",children:[\"Are you sure you want to delete \",/*#__PURE__*/_jsx(\"strong\",{children:selectedUser.name}),\" (\",selectedUser.email,\")? This action cannot be undone.\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-end space-x-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setShowDeleteModal(false),className:\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\",children:\"Cancel\"}),/*#__PURE__*/_jsx(\"button\",{onClick:confirmDeleteUser,className:\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700\",children:\"Delete User\"})]})]})})})]});};export default UserManagement;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useAuth", "PlusIcon", "PencilIcon", "TrashIcon", "EyeIcon", "CheckCircleIcon", "XCircleIcon", "MagnifyingGlassIcon", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "UserManagement", "user", "currentUser", "users", "setUsers", "loading", "setLoading", "searchTerm", "setSearchTerm", "showCreateModal", "setShowCreateModal", "showEditModal", "setShowEditModal", "showDeleteModal", "setShowDeleteModal", "selected<PERSON>ser", "setSelectedUser", "createForm", "setCreateForm", "username", "email", "name", "password", "roles", "groups", "editForm", "setEditForm", "active", "fetchUsers", "response", "fetch", "credentials", "ok", "data", "json", "error", "console", "handleCreateUser", "e", "preventDefault", "method", "headers", "body", "JSON", "stringify", "handleEditUser", "handleUpdateUser", "updateData", "concat", "id", "handleDeleteUser", "confirmDeleteUser", "filteredUsers", "filter", "toLowerCase", "includes", "getRoleBadgeColor", "role", "className", "children", "onClick", "type", "placeholder", "value", "onChange", "target", "length", "map", "split", "n", "join", "toUpperCase", "last_login", "Date", "toLocaleDateString", "title", "onSubmit", "required", "_objectSpread", "multiple", "Array", "from", "selectedOptions", "option", "checked"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/UserManagement.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useAuth } from '../auth/AuthContext';\nimport {\n  UsersIcon,\n  PlusIcon,\n  PencilIcon,\n  TrashIcon,\n  EyeIcon,\n  CheckCircleIcon,\n  XCircleIcon,\n  MagnifyingGlassIcon,\n} from '@heroicons/react/24/outline';\n\ninterface User {\n  id: string;\n  username: string;\n  email: string;\n  name: string;\n  roles: string[];\n  groups: string[];\n  active: boolean;\n  created_at: string;\n  last_login?: string;\n}\n\ninterface CreateUserForm {\n  username: string;\n  email: string;\n  name: string;\n  password: string;\n  roles: string[];\n  groups: string[];\n}\n\ninterface EditUserForm {\n  email: string;\n  name: string;\n  roles: string[];\n  groups: string[];\n  active: boolean;\n  password?: string;\n}\n\nconst UserManagement: React.FC = () => {\n  const { user: currentUser } = useAuth();\n  const [users, setUsers] = useState<User[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showCreateModal, setShowCreateModal] = useState(false);\n  const [showEditModal, setShowEditModal] = useState(false);\n  const [showDeleteModal, setShowDeleteModal] = useState(false);\n  const [selectedUser, setSelectedUser] = useState<User | null>(null);\n  const [createForm, setCreateForm] = useState<CreateUserForm>({\n    username: '',\n    email: '',\n    name: '',\n    password: '',\n    roles: ['viewer'],\n    groups: ['cainuro-users'],\n  });\n  const [editForm, setEditForm] = useState<EditUserForm>({\n    email: '',\n    name: '',\n    roles: ['viewer'],\n    groups: ['cainuro-users'],\n    active: true,\n    password: '',\n  });\n\n  useEffect(() => {\n    fetchUsers();\n  }, []);\n\n  const fetchUsers = async () => {\n    try {\n      const response = await fetch('/v1/users', {\n        credentials: 'include',\n      });\n      \n      if (response.ok) {\n        const data = await response.json();\n        setUsers(data.users || []);\n      }\n    } catch (error) {\n      console.error('Failed to fetch users:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreateUser = async (e: React.FormEvent) => {\n    e.preventDefault();\n    try {\n      const response = await fetch('/v1/users', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include',\n        body: JSON.stringify(createForm),\n      });\n\n      if (response.ok) {\n        setShowCreateModal(false);\n        setCreateForm({\n          username: '',\n          email: '',\n          name: '',\n          password: '',\n          roles: ['viewer'],\n          groups: ['cainuro-users'],\n        });\n        fetchUsers();\n      }\n    } catch (error) {\n      console.error('Failed to create user:', error);\n    }\n  };\n\n  const handleEditUser = (user: User) => {\n    setSelectedUser(user);\n    setEditForm({\n      email: user.email,\n      name: user.name,\n      roles: user.roles,\n      groups: user.groups,\n      active: user.active || true,\n      password: '',\n    });\n    setShowEditModal(true);\n  };\n\n  const handleUpdateUser = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!selectedUser) return;\n\n    try {\n      const updateData: any = {\n        email: editForm.email,\n        name: editForm.name,\n        roles: editForm.roles,\n        groups: editForm.groups,\n        active: editForm.active,\n      };\n\n      if (editForm.password) {\n        updateData.password = editForm.password;\n      }\n\n      const response = await fetch(`/v1/users/${selectedUser.id}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include',\n        body: JSON.stringify(updateData),\n      });\n\n      if (response.ok) {\n        setShowEditModal(false);\n        setSelectedUser(null);\n        fetchUsers();\n      }\n    } catch (error) {\n      console.error('Failed to update user:', error);\n    }\n  };\n\n  const handleDeleteUser = (user: User) => {\n    setSelectedUser(user);\n    setShowDeleteModal(true);\n  };\n\n  const confirmDeleteUser = async () => {\n    if (!selectedUser) return;\n\n    try {\n      const response = await fetch(`/v1/users/${selectedUser.id}`, {\n        method: 'DELETE',\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        setShowDeleteModal(false);\n        setSelectedUser(null);\n        fetchUsers();\n      }\n    } catch (error) {\n      console.error('Failed to delete user:', error);\n    }\n  };\n\n  const filteredUsers = users.filter(user =>\n    user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    user.name.toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  const getRoleBadgeColor = (role: string) => {\n    switch (role) {\n      case 'admin':\n        return 'bg-red-100 text-red-800';\n      case 'operator':\n        return 'bg-blue-100 text-blue-800';\n      case 'viewer':\n        return 'bg-green-100 text-green-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">User Management</h1>\n            <p className=\"text-gray-600\">Manage system users and their permissions</p>\n          </div>\n          <button\n            onClick={() => setShowCreateModal(true)}\n            className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700\"\n          >\n            <PlusIcon className=\"h-4 w-4 mr-2\" />\n            Create User\n          </button>\n        </div>\n      </div>\n\n      {/* Search and Filters */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <div className=\"flex items-center space-x-4\">\n          <div className=\"flex-1 relative\">\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <input\n              type=\"text\"\n              placeholder=\"Search users...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\n            />\n          </div>\n          <div className=\"text-sm text-gray-500\">\n            {filteredUsers.length} of {users.length} users\n          </div>\n        </div>\n      </div>\n\n      {/* Users Table */}\n      <div className=\"bg-white shadow rounded-lg overflow-hidden\">\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <h3 className=\"text-lg font-medium text-gray-900\">Users</h3>\n        </div>\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full divide-y divide-gray-200\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  User\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Roles\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Status\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Last Login\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Actions\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {filteredUsers.map((user) => (\n                <tr key={user.id} className=\"hover:bg-gray-50\">\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"flex items-center\">\n                      <div className=\"flex-shrink-0 h-10 w-10\">\n                        <div className=\"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center\">\n                          <span className=\"text-sm font-medium text-gray-700\">\n                            {user.name.split(' ').map(n => n[0]).join('').toUpperCase()}\n                          </span>\n                        </div>\n                      </div>\n                      <div className=\"ml-4\">\n                        <div className=\"text-sm font-medium text-gray-900\">{user.name}</div>\n                        <div className=\"text-sm text-gray-500\">{user.email}</div>\n                        <div className=\"text-xs text-gray-400\">@{user.username}</div>\n                      </div>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"flex flex-wrap gap-1\">\n                      {user.roles.map((role) => (\n                        <span\n                          key={role}\n                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleBadgeColor(role)}`}\n                        >\n                          {role}\n                        </span>\n                      ))}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                      user.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'\n                    }`}>\n                      {user.active ? (\n                        <>\n                          <CheckCircleIcon className=\"h-3 w-3 mr-1\" />\n                          Active\n                        </>\n                      ) : (\n                        <>\n                          <XCircleIcon className=\"h-3 w-3 mr-1\" />\n                          Inactive\n                        </>\n                      )}\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                    {user.last_login ? new Date(user.last_login).toLocaleDateString() : 'Never'}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                    <div className=\"flex space-x-2\">\n                      <button\n                        onClick={() => setSelectedUser(user)}\n                        className=\"text-blue-600 hover:text-blue-900\"\n                        title=\"View User\"\n                      >\n                        <EyeIcon className=\"h-4 w-4\" />\n                      </button>\n                      <button\n                        onClick={() => handleEditUser(user)}\n                        className=\"text-yellow-600 hover:text-yellow-900\"\n                        title=\"Edit User\"\n                      >\n                        <PencilIcon className=\"h-4 w-4\" />\n                      </button>\n                      {user.username !== currentUser?.username && user.id !== currentUser?.id && (\n                        <button\n                          onClick={() => handleDeleteUser(user)}\n                          className=\"text-red-600 hover:text-red-900\"\n                          title=\"Delete User\"\n                        >\n                          <TrashIcon className=\"h-4 w-4\" />\n                        </button>\n                      )}\n                    </div>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n\n      {/* Create User Modal */}\n      {showCreateModal && (\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n          <div className=\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\">\n            <div className=\"mt-3\">\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Create New User</h3>\n              <form onSubmit={handleCreateUser} className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">Username</label>\n                  <input\n                    type=\"text\"\n                    required\n                    value={createForm.username}\n                    onChange={(e) => setCreateForm({...createForm, username: e.target.value})}\n                    className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">Email</label>\n                  <input\n                    type=\"email\"\n                    required\n                    value={createForm.email}\n                    onChange={(e) => setCreateForm({...createForm, email: e.target.value})}\n                    className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">Full Name</label>\n                  <input\n                    type=\"text\"\n                    required\n                    value={createForm.name}\n                    onChange={(e) => setCreateForm({...createForm, name: e.target.value})}\n                    className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">Password</label>\n                  <input\n                    type=\"password\"\n                    required\n                    value={createForm.password}\n                    onChange={(e) => setCreateForm({...createForm, password: e.target.value})}\n                    className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                  />\n                </div>\n                <div className=\"flex justify-end space-x-3 pt-4\">\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowCreateModal(false)}\n                    className=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\"\n                  >\n                    Cancel\n                  </button>\n                  <button\n                    type=\"submit\"\n                    className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700\"\n                  >\n                    Create User\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Edit User Modal */}\n      {showEditModal && selectedUser && (\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n          <div className=\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\">\n            <div className=\"mt-3\">\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Edit User: {selectedUser.name}</h3>\n              <form onSubmit={handleUpdateUser} className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">Email</label>\n                  <input\n                    type=\"email\"\n                    required\n                    value={editForm.email}\n                    onChange={(e) => setEditForm({...editForm, email: e.target.value})}\n                    className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">Full Name</label>\n                  <input\n                    type=\"text\"\n                    required\n                    value={editForm.name}\n                    onChange={(e) => setEditForm({...editForm, name: e.target.value})}\n                    className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">Roles</label>\n                  <select\n                    multiple\n                    value={editForm.roles}\n                    onChange={(e) => setEditForm({...editForm, roles: Array.from(e.target.selectedOptions, option => option.value)})}\n                    className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                  >\n                    <option value=\"admin\">Admin</option>\n                    <option value=\"operator\">Operator</option>\n                    <option value=\"viewer\">Viewer</option>\n                  </select>\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700\">New Password (optional)</label>\n                  <input\n                    type=\"password\"\n                    value={editForm.password}\n                    onChange={(e) => setEditForm({...editForm, password: e.target.value})}\n                    className=\"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                    placeholder=\"Leave blank to keep current password\"\n                  />\n                </div>\n                <div className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={editForm.active}\n                    onChange={(e) => setEditForm({...editForm, active: e.target.checked})}\n                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  />\n                  <label className=\"ml-2 block text-sm text-gray-900\">Active User</label>\n                </div>\n                <div className=\"flex justify-end space-x-3 pt-4\">\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowEditModal(false)}\n                    className=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\"\n                  >\n                    Cancel\n                  </button>\n                  <button\n                    type=\"submit\"\n                    className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700\"\n                  >\n                    Update User\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Delete User Modal */}\n      {showDeleteModal && selectedUser && (\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n          <div className=\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\">\n            <div className=\"mt-3\">\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Delete User</h3>\n              <p className=\"text-sm text-gray-500 mb-4\">\n                Are you sure you want to delete <strong>{selectedUser.name}</strong> ({selectedUser.email})?\n                This action cannot be undone.\n              </p>\n              <div className=\"flex justify-end space-x-3\">\n                <button\n                  onClick={() => setShowDeleteModal(false)}\n                  className=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\"\n                >\n                  Cancel\n                </button>\n                <button\n                  onClick={confirmDeleteUser}\n                  className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700\"\n                >\n                  Delete User\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default UserManagement;\n"], "mappings": "yJAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,OAAO,KAAQ,qBAAqB,CAC7C,OAEEC,QAAQ,CACRC,UAAU,CACVC,SAAS,CACTC,OAAO,CACPC,eAAe,CACfC,WAAW,CACXC,mBAAmB,KACd,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAgCrC,KAAM,CAAAC,cAAwB,CAAGA,CAAA,GAAM,CACrC,KAAM,CAAEC,IAAI,CAAEC,WAAY,CAAC,CAAGhB,OAAO,CAAC,CAAC,CACvC,KAAM,CAACiB,KAAK,CAAEC,QAAQ,CAAC,CAAGnB,QAAQ,CAAS,EAAE,CAAC,CAC9C,KAAM,CAACoB,OAAO,CAAEC,UAAU,CAAC,CAAGrB,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACsB,UAAU,CAAEC,aAAa,CAAC,CAAGvB,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACwB,eAAe,CAAEC,kBAAkB,CAAC,CAAGzB,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAAC0B,aAAa,CAAEC,gBAAgB,CAAC,CAAG3B,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAAC4B,eAAe,CAAEC,kBAAkB,CAAC,CAAG7B,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAAC8B,YAAY,CAAEC,eAAe,CAAC,CAAG/B,QAAQ,CAAc,IAAI,CAAC,CACnE,KAAM,CAACgC,UAAU,CAAEC,aAAa,CAAC,CAAGjC,QAAQ,CAAiB,CAC3DkC,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,EAAE,CACTC,IAAI,CAAE,EAAE,CACRC,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,CAAC,QAAQ,CAAC,CACjBC,MAAM,CAAE,CAAC,eAAe,CAC1B,CAAC,CAAC,CACF,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGzC,QAAQ,CAAe,CACrDmC,KAAK,CAAE,EAAE,CACTC,IAAI,CAAE,EAAE,CACRE,KAAK,CAAE,CAAC,QAAQ,CAAC,CACjBC,MAAM,CAAE,CAAC,eAAe,CAAC,CACzBG,MAAM,CAAE,IAAI,CACZL,QAAQ,CAAE,EACZ,CAAC,CAAC,CAEFtC,SAAS,CAAC,IAAM,CACd4C,UAAU,CAAC,CAAC,CACd,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAA,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,WAAW,CAAE,CACxCC,WAAW,CAAE,SACf,CAAC,CAAC,CAEF,GAAIF,QAAQ,CAACG,EAAE,CAAE,CACf,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAJ,QAAQ,CAACK,IAAI,CAAC,CAAC,CAClC9B,QAAQ,CAAC6B,IAAI,CAAC9B,KAAK,EAAI,EAAE,CAAC,CAC5B,CACF,CAAE,MAAOgC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAChD,CAAC,OAAS,CACR7B,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAA+B,gBAAgB,CAAG,KAAO,CAAAC,CAAkB,EAAK,CACrDA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClB,GAAI,CACF,KAAM,CAAAV,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,WAAW,CAAE,CACxCU,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDV,WAAW,CAAE,SAAS,CACtBW,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC3B,UAAU,CACjC,CAAC,CAAC,CAEF,GAAIY,QAAQ,CAACG,EAAE,CAAE,CACftB,kBAAkB,CAAC,KAAK,CAAC,CACzBQ,aAAa,CAAC,CACZC,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,EAAE,CACTC,IAAI,CAAE,EAAE,CACRC,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,CAAC,QAAQ,CAAC,CACjBC,MAAM,CAAE,CAAC,eAAe,CAC1B,CAAC,CAAC,CACFI,UAAU,CAAC,CAAC,CACd,CACF,CAAE,MAAOO,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAChD,CACF,CAAC,CAED,KAAM,CAAAU,cAAc,CAAI5C,IAAU,EAAK,CACrCe,eAAe,CAACf,IAAI,CAAC,CACrByB,WAAW,CAAC,CACVN,KAAK,CAAEnB,IAAI,CAACmB,KAAK,CACjBC,IAAI,CAAEpB,IAAI,CAACoB,IAAI,CACfE,KAAK,CAAEtB,IAAI,CAACsB,KAAK,CACjBC,MAAM,CAAEvB,IAAI,CAACuB,MAAM,CACnBG,MAAM,CAAE1B,IAAI,CAAC0B,MAAM,EAAI,IAAI,CAC3BL,QAAQ,CAAE,EACZ,CAAC,CAAC,CACFV,gBAAgB,CAAC,IAAI,CAAC,CACxB,CAAC,CAED,KAAM,CAAAkC,gBAAgB,CAAG,KAAO,CAAAR,CAAkB,EAAK,CACrDA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClB,GAAI,CAACxB,YAAY,CAAE,OAEnB,GAAI,CACF,KAAM,CAAAgC,UAAe,CAAG,CACtB3B,KAAK,CAAEK,QAAQ,CAACL,KAAK,CACrBC,IAAI,CAAEI,QAAQ,CAACJ,IAAI,CACnBE,KAAK,CAAEE,QAAQ,CAACF,KAAK,CACrBC,MAAM,CAAEC,QAAQ,CAACD,MAAM,CACvBG,MAAM,CAAEF,QAAQ,CAACE,MACnB,CAAC,CAED,GAAIF,QAAQ,CAACH,QAAQ,CAAE,CACrByB,UAAU,CAACzB,QAAQ,CAAGG,QAAQ,CAACH,QAAQ,CACzC,CAEA,KAAM,CAAAO,QAAQ,CAAG,KAAM,CAAAC,KAAK,cAAAkB,MAAA,CAAcjC,YAAY,CAACkC,EAAE,EAAI,CAC3DT,MAAM,CAAE,KAAK,CACbC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDV,WAAW,CAAE,SAAS,CACtBW,IAAI,CAAEC,IAAI,CAACC,SAAS,CAACG,UAAU,CACjC,CAAC,CAAC,CAEF,GAAIlB,QAAQ,CAACG,EAAE,CAAE,CACfpB,gBAAgB,CAAC,KAAK,CAAC,CACvBI,eAAe,CAAC,IAAI,CAAC,CACrBY,UAAU,CAAC,CAAC,CACd,CACF,CAAE,MAAOO,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAChD,CACF,CAAC,CAED,KAAM,CAAAe,gBAAgB,CAAIjD,IAAU,EAAK,CACvCe,eAAe,CAACf,IAAI,CAAC,CACrBa,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAC,CAED,KAAM,CAAAqC,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI,CAACpC,YAAY,CAAE,OAEnB,GAAI,CACF,KAAM,CAAAc,QAAQ,CAAG,KAAM,CAAAC,KAAK,cAAAkB,MAAA,CAAcjC,YAAY,CAACkC,EAAE,EAAI,CAC3DT,MAAM,CAAE,QAAQ,CAChBT,WAAW,CAAE,SACf,CAAC,CAAC,CAEF,GAAIF,QAAQ,CAACG,EAAE,CAAE,CACflB,kBAAkB,CAAC,KAAK,CAAC,CACzBE,eAAe,CAAC,IAAI,CAAC,CACrBY,UAAU,CAAC,CAAC,CACd,CACF,CAAE,MAAOO,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAChD,CACF,CAAC,CAED,KAAM,CAAAiB,aAAa,CAAGjD,KAAK,CAACkD,MAAM,CAACpD,IAAI,EACrCA,IAAI,CAACkB,QAAQ,CAACmC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChD,UAAU,CAAC+C,WAAW,CAAC,CAAC,CAAC,EAC9DrD,IAAI,CAACmB,KAAK,CAACkC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChD,UAAU,CAAC+C,WAAW,CAAC,CAAC,CAAC,EAC3DrD,IAAI,CAACoB,IAAI,CAACiC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChD,UAAU,CAAC+C,WAAW,CAAC,CAAC,CAC3D,CAAC,CAED,KAAM,CAAAE,iBAAiB,CAAIC,IAAY,EAAK,CAC1C,OAAQA,IAAI,EACV,IAAK,OAAO,CACV,MAAO,yBAAyB,CAClC,IAAK,UAAU,CACb,MAAO,2BAA2B,CACpC,IAAK,QAAQ,CACX,MAAO,6BAA6B,CACtC,QACE,MAAO,2BAA2B,CACtC,CACF,CAAC,CAED,GAAIpD,OAAO,CAAE,CACX,mBACEV,IAAA,QAAK+D,SAAS,CAAC,uCAAuC,CAAAC,QAAA,cACpDhE,IAAA,QAAK+D,SAAS,CAAC,gEAAgE,CAAM,CAAC,CACnF,CAAC,CAEV,CAEA,mBACE7D,KAAA,QAAK6D,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExBhE,IAAA,QAAK+D,SAAS,CAAC,gCAAgC,CAAAC,QAAA,cAC7C9D,KAAA,QAAK6D,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChD9D,KAAA,QAAA8D,QAAA,eACEhE,IAAA,OAAI+D,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,cACrEhE,IAAA,MAAG+D,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,2CAAyC,CAAG,CAAC,EACvE,CAAC,cACN9D,KAAA,WACE+D,OAAO,CAAEA,CAAA,GAAMlD,kBAAkB,CAAC,IAAI,CAAE,CACxCgD,SAAS,CAAC,gJAAgJ,CAAAC,QAAA,eAE1JhE,IAAA,CAACR,QAAQ,EAACuE,SAAS,CAAC,cAAc,CAAE,CAAC,cAEvC,EAAQ,CAAC,EACN,CAAC,CACH,CAAC,cAGN/D,IAAA,QAAK+D,SAAS,CAAC,gCAAgC,CAAAC,QAAA,cAC7C9D,KAAA,QAAK6D,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C9D,KAAA,QAAK6D,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BhE,IAAA,QAAK+D,SAAS,CAAC,sEAAsE,CAAAC,QAAA,cACnFhE,IAAA,CAACF,mBAAmB,EAACiE,SAAS,CAAC,uBAAuB,CAAE,CAAC,CACtD,CAAC,cACN/D,IAAA,UACEkE,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,iBAAiB,CAC7BC,KAAK,CAAExD,UAAW,CAClByD,QAAQ,CAAG1B,CAAC,EAAK9B,aAAa,CAAC8B,CAAC,CAAC2B,MAAM,CAACF,KAAK,CAAE,CAC/CL,SAAS,CAAC,2NAA2N,CACtO,CAAC,EACC,CAAC,cACN7D,KAAA,QAAK6D,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EACnCP,aAAa,CAACc,MAAM,CAAC,MAAI,CAAC/D,KAAK,CAAC+D,MAAM,CAAC,QAC1C,EAAK,CAAC,EACH,CAAC,CACH,CAAC,cAGNrE,KAAA,QAAK6D,SAAS,CAAC,4CAA4C,CAAAC,QAAA,eACzDhE,IAAA,QAAK+D,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjDhE,IAAA,OAAI+D,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,OAAK,CAAI,CAAC,CACzD,CAAC,cACNhE,IAAA,QAAK+D,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9B9D,KAAA,UAAO6D,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eACpDhE,IAAA,UAAO+D,SAAS,CAAC,YAAY,CAAAC,QAAA,cAC3B9D,KAAA,OAAA8D,QAAA,eACEhE,IAAA,OAAI+D,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,MAE/F,CAAI,CAAC,cACLhE,IAAA,OAAI+D,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,OAE/F,CAAI,CAAC,cACLhE,IAAA,OAAI+D,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,QAE/F,CAAI,CAAC,cACLhE,IAAA,OAAI+D,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,YAE/F,CAAI,CAAC,cACLhE,IAAA,OAAI+D,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAAC,SAE/F,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cACRhE,IAAA,UAAO+D,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CACjDP,aAAa,CAACe,GAAG,CAAElE,IAAI,eACtBJ,KAAA,OAAkB6D,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC5ChE,IAAA,OAAI+D,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzC9D,KAAA,QAAK6D,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChChE,IAAA,QAAK+D,SAAS,CAAC,yBAAyB,CAAAC,QAAA,cACtChE,IAAA,QAAK+D,SAAS,CAAC,qEAAqE,CAAAC,QAAA,cAClFhE,IAAA,SAAM+D,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAChD1D,IAAI,CAACoB,IAAI,CAAC+C,KAAK,CAAC,GAAG,CAAC,CAACD,GAAG,CAACE,CAAC,EAAIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC,CACvD,CAAC,CACJ,CAAC,CACH,CAAC,cACN1E,KAAA,QAAK6D,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBhE,IAAA,QAAK+D,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAE1D,IAAI,CAACoB,IAAI,CAAM,CAAC,cACpE1B,IAAA,QAAK+D,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAE1D,IAAI,CAACmB,KAAK,CAAM,CAAC,cACzDvB,KAAA,QAAK6D,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAC,GAAC,CAAC1D,IAAI,CAACkB,QAAQ,EAAM,CAAC,EAC1D,CAAC,EACH,CAAC,CACJ,CAAC,cACLxB,IAAA,OAAI+D,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzChE,IAAA,QAAK+D,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAClC1D,IAAI,CAACsB,KAAK,CAAC4C,GAAG,CAAEV,IAAI,eACnB9D,IAAA,SAEE+D,SAAS,4EAAAV,MAAA,CAA6EQ,iBAAiB,CAACC,IAAI,CAAC,CAAG,CAAAE,QAAA,CAE/GF,IAAI,EAHAA,IAID,CACP,CAAC,CACC,CAAC,CACJ,CAAC,cACL9D,IAAA,OAAI+D,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cACzChE,IAAA,SAAM+D,SAAS,4EAAAV,MAAA,CACb/C,IAAI,CAAC0B,MAAM,CAAG,6BAA6B,CAAG,yBAAyB,CACtE,CAAAgC,QAAA,CACA1D,IAAI,CAAC0B,MAAM,cACV9B,KAAA,CAAAE,SAAA,EAAA4D,QAAA,eACEhE,IAAA,CAACJ,eAAe,EAACmE,SAAS,CAAC,cAAc,CAAE,CAAC,SAE9C,EAAE,CAAC,cAEH7D,KAAA,CAAAE,SAAA,EAAA4D,QAAA,eACEhE,IAAA,CAACH,WAAW,EAACkE,SAAS,CAAC,cAAc,CAAE,CAAC,WAE1C,EAAE,CACH,CACG,CAAC,CACL,CAAC,cACL/D,IAAA,OAAI+D,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAC9D1D,IAAI,CAACuE,UAAU,CAAG,GAAI,CAAAC,IAAI,CAACxE,IAAI,CAACuE,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC,CAAG,OAAO,CACzE,CAAC,cACL/E,IAAA,OAAI+D,SAAS,CAAC,iDAAiD,CAAAC,QAAA,cAC7D9D,KAAA,QAAK6D,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BhE,IAAA,WACEiE,OAAO,CAAEA,CAAA,GAAM5C,eAAe,CAACf,IAAI,CAAE,CACrCyD,SAAS,CAAC,mCAAmC,CAC7CiB,KAAK,CAAC,WAAW,CAAAhB,QAAA,cAEjBhE,IAAA,CAACL,OAAO,EAACoE,SAAS,CAAC,SAAS,CAAE,CAAC,CACzB,CAAC,cACT/D,IAAA,WACEiE,OAAO,CAAEA,CAAA,GAAMf,cAAc,CAAC5C,IAAI,CAAE,CACpCyD,SAAS,CAAC,uCAAuC,CACjDiB,KAAK,CAAC,WAAW,CAAAhB,QAAA,cAEjBhE,IAAA,CAACP,UAAU,EAACsE,SAAS,CAAC,SAAS,CAAE,CAAC,CAC5B,CAAC,CACRzD,IAAI,CAACkB,QAAQ,IAAKjB,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEiB,QAAQ,GAAIlB,IAAI,CAACgD,EAAE,IAAK/C,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAE+C,EAAE,gBACrEtD,IAAA,WACEiE,OAAO,CAAEA,CAAA,GAAMV,gBAAgB,CAACjD,IAAI,CAAE,CACtCyD,SAAS,CAAC,iCAAiC,CAC3CiB,KAAK,CAAC,aAAa,CAAAhB,QAAA,cAEnBhE,IAAA,CAACN,SAAS,EAACqE,SAAS,CAAC,SAAS,CAAE,CAAC,CAC3B,CACT,EACE,CAAC,CACJ,CAAC,GA3EEzD,IAAI,CAACgD,EA4EV,CACL,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,EACH,CAAC,CAGLxC,eAAe,eACdd,IAAA,QAAK+D,SAAS,CAAC,4EAA4E,CAAAC,QAAA,cACzFhE,IAAA,QAAK+D,SAAS,CAAC,uEAAuE,CAAAC,QAAA,cACpF9D,KAAA,QAAK6D,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBhE,IAAA,OAAI+D,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,cAC3E9D,KAAA,SAAM+E,QAAQ,CAAEvC,gBAAiB,CAACqB,SAAS,CAAC,WAAW,CAAAC,QAAA,eACrD9D,KAAA,QAAA8D,QAAA,eACEhE,IAAA,UAAO+D,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,UAAQ,CAAO,CAAC,cAC3EhE,IAAA,UACEkE,IAAI,CAAC,MAAM,CACXgB,QAAQ,MACRd,KAAK,CAAE9C,UAAU,CAACE,QAAS,CAC3B6C,QAAQ,CAAG1B,CAAC,EAAKpB,aAAa,CAAA4D,aAAA,CAAAA,aAAA,IAAK7D,UAAU,MAAEE,QAAQ,CAAEmB,CAAC,CAAC2B,MAAM,CAACF,KAAK,EAAC,CAAE,CAC1EL,SAAS,CAAC,mJAAmJ,CAC9J,CAAC,EACC,CAAC,cACN7D,KAAA,QAAA8D,QAAA,eACEhE,IAAA,UAAO+D,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,OAAK,CAAO,CAAC,cACxEhE,IAAA,UACEkE,IAAI,CAAC,OAAO,CACZgB,QAAQ,MACRd,KAAK,CAAE9C,UAAU,CAACG,KAAM,CACxB4C,QAAQ,CAAG1B,CAAC,EAAKpB,aAAa,CAAA4D,aAAA,CAAAA,aAAA,IAAK7D,UAAU,MAAEG,KAAK,CAAEkB,CAAC,CAAC2B,MAAM,CAACF,KAAK,EAAC,CAAE,CACvEL,SAAS,CAAC,mJAAmJ,CAC9J,CAAC,EACC,CAAC,cACN7D,KAAA,QAAA8D,QAAA,eACEhE,IAAA,UAAO+D,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,WAAS,CAAO,CAAC,cAC5EhE,IAAA,UACEkE,IAAI,CAAC,MAAM,CACXgB,QAAQ,MACRd,KAAK,CAAE9C,UAAU,CAACI,IAAK,CACvB2C,QAAQ,CAAG1B,CAAC,EAAKpB,aAAa,CAAA4D,aAAA,CAAAA,aAAA,IAAK7D,UAAU,MAAEI,IAAI,CAAEiB,CAAC,CAAC2B,MAAM,CAACF,KAAK,EAAC,CAAE,CACtEL,SAAS,CAAC,mJAAmJ,CAC9J,CAAC,EACC,CAAC,cACN7D,KAAA,QAAA8D,QAAA,eACEhE,IAAA,UAAO+D,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,UAAQ,CAAO,CAAC,cAC3EhE,IAAA,UACEkE,IAAI,CAAC,UAAU,CACfgB,QAAQ,MACRd,KAAK,CAAE9C,UAAU,CAACK,QAAS,CAC3B0C,QAAQ,CAAG1B,CAAC,EAAKpB,aAAa,CAAA4D,aAAA,CAAAA,aAAA,IAAK7D,UAAU,MAAEK,QAAQ,CAAEgB,CAAC,CAAC2B,MAAM,CAACF,KAAK,EAAC,CAAE,CAC1EL,SAAS,CAAC,mJAAmJ,CAC9J,CAAC,EACC,CAAC,cACN7D,KAAA,QAAK6D,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC9ChE,IAAA,WACEkE,IAAI,CAAC,QAAQ,CACbD,OAAO,CAAEA,CAAA,GAAMlD,kBAAkB,CAAC,KAAK,CAAE,CACzCgD,SAAS,CAAC,gGAAgG,CAAAC,QAAA,CAC3G,QAED,CAAQ,CAAC,cACThE,IAAA,WACEkE,IAAI,CAAC,QAAQ,CACbH,SAAS,CAAC,uHAAuH,CAAAC,QAAA,CAClI,aAED,CAAQ,CAAC,EACN,CAAC,EACF,CAAC,EACJ,CAAC,CACH,CAAC,CACH,CACN,CAGAhD,aAAa,EAAII,YAAY,eAC5BpB,IAAA,QAAK+D,SAAS,CAAC,4EAA4E,CAAAC,QAAA,cACzFhE,IAAA,QAAK+D,SAAS,CAAC,uEAAuE,CAAAC,QAAA,cACpF9D,KAAA,QAAK6D,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB9D,KAAA,OAAI6D,SAAS,CAAC,wCAAwC,CAAAC,QAAA,EAAC,aAAW,CAAC5C,YAAY,CAACM,IAAI,EAAK,CAAC,cAC1FxB,KAAA,SAAM+E,QAAQ,CAAE9B,gBAAiB,CAACY,SAAS,CAAC,WAAW,CAAAC,QAAA,eACrD9D,KAAA,QAAA8D,QAAA,eACEhE,IAAA,UAAO+D,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,OAAK,CAAO,CAAC,cACxEhE,IAAA,UACEkE,IAAI,CAAC,OAAO,CACZgB,QAAQ,MACRd,KAAK,CAAEtC,QAAQ,CAACL,KAAM,CACtB4C,QAAQ,CAAG1B,CAAC,EAAKZ,WAAW,CAAAoD,aAAA,CAAAA,aAAA,IAAKrD,QAAQ,MAAEL,KAAK,CAAEkB,CAAC,CAAC2B,MAAM,CAACF,KAAK,EAAC,CAAE,CACnEL,SAAS,CAAC,mJAAmJ,CAC9J,CAAC,EACC,CAAC,cACN7D,KAAA,QAAA8D,QAAA,eACEhE,IAAA,UAAO+D,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,WAAS,CAAO,CAAC,cAC5EhE,IAAA,UACEkE,IAAI,CAAC,MAAM,CACXgB,QAAQ,MACRd,KAAK,CAAEtC,QAAQ,CAACJ,IAAK,CACrB2C,QAAQ,CAAG1B,CAAC,EAAKZ,WAAW,CAAAoD,aAAA,CAAAA,aAAA,IAAKrD,QAAQ,MAAEJ,IAAI,CAAEiB,CAAC,CAAC2B,MAAM,CAACF,KAAK,EAAC,CAAE,CAClEL,SAAS,CAAC,mJAAmJ,CAC9J,CAAC,EACC,CAAC,cACN7D,KAAA,QAAA8D,QAAA,eACEhE,IAAA,UAAO+D,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,OAAK,CAAO,CAAC,cACxE9D,KAAA,WACEkF,QAAQ,MACRhB,KAAK,CAAEtC,QAAQ,CAACF,KAAM,CACtByC,QAAQ,CAAG1B,CAAC,EAAKZ,WAAW,CAAAoD,aAAA,CAAAA,aAAA,IAAKrD,QAAQ,MAAEF,KAAK,CAAEyD,KAAK,CAACC,IAAI,CAAC3C,CAAC,CAAC2B,MAAM,CAACiB,eAAe,CAAEC,MAAM,EAAIA,MAAM,CAACpB,KAAK,CAAC,EAAC,CAAE,CACjHL,SAAS,CAAC,mJAAmJ,CAAAC,QAAA,eAE7JhE,IAAA,WAAQoE,KAAK,CAAC,OAAO,CAAAJ,QAAA,CAAC,OAAK,CAAQ,CAAC,cACpChE,IAAA,WAAQoE,KAAK,CAAC,UAAU,CAAAJ,QAAA,CAAC,UAAQ,CAAQ,CAAC,cAC1ChE,IAAA,WAAQoE,KAAK,CAAC,QAAQ,CAAAJ,QAAA,CAAC,QAAM,CAAQ,CAAC,EAChC,CAAC,EACN,CAAC,cACN9D,KAAA,QAAA8D,QAAA,eACEhE,IAAA,UAAO+D,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,yBAAuB,CAAO,CAAC,cAC1FhE,IAAA,UACEkE,IAAI,CAAC,UAAU,CACfE,KAAK,CAAEtC,QAAQ,CAACH,QAAS,CACzB0C,QAAQ,CAAG1B,CAAC,EAAKZ,WAAW,CAAAoD,aAAA,CAAAA,aAAA,IAAKrD,QAAQ,MAAEH,QAAQ,CAAEgB,CAAC,CAAC2B,MAAM,CAACF,KAAK,EAAC,CAAE,CACtEL,SAAS,CAAC,mJAAmJ,CAC7JI,WAAW,CAAC,sCAAsC,CACnD,CAAC,EACC,CAAC,cACNjE,KAAA,QAAK6D,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChChE,IAAA,UACEkE,IAAI,CAAC,UAAU,CACfuB,OAAO,CAAE3D,QAAQ,CAACE,MAAO,CACzBqC,QAAQ,CAAG1B,CAAC,EAAKZ,WAAW,CAAAoD,aAAA,CAAAA,aAAA,IAAKrD,QAAQ,MAAEE,MAAM,CAAEW,CAAC,CAAC2B,MAAM,CAACmB,OAAO,EAAC,CAAE,CACtE1B,SAAS,CAAC,mEAAmE,CAC9E,CAAC,cACF/D,IAAA,UAAO+D,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,aAAW,CAAO,CAAC,EACpE,CAAC,cACN9D,KAAA,QAAK6D,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC9ChE,IAAA,WACEkE,IAAI,CAAC,QAAQ,CACbD,OAAO,CAAEA,CAAA,GAAMhD,gBAAgB,CAAC,KAAK,CAAE,CACvC8C,SAAS,CAAC,gGAAgG,CAAAC,QAAA,CAC3G,QAED,CAAQ,CAAC,cACThE,IAAA,WACEkE,IAAI,CAAC,QAAQ,CACbH,SAAS,CAAC,uHAAuH,CAAAC,QAAA,CAClI,aAED,CAAQ,CAAC,EACN,CAAC,EACF,CAAC,EACJ,CAAC,CACH,CAAC,CACH,CACN,CAGA9C,eAAe,EAAIE,YAAY,eAC9BpB,IAAA,QAAK+D,SAAS,CAAC,4EAA4E,CAAAC,QAAA,cACzFhE,IAAA,QAAK+D,SAAS,CAAC,uEAAuE,CAAAC,QAAA,cACpF9D,KAAA,QAAK6D,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBhE,IAAA,OAAI+D,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,aAAW,CAAI,CAAC,cACvE9D,KAAA,MAAG6D,SAAS,CAAC,4BAA4B,CAAAC,QAAA,EAAC,kCACR,cAAAhE,IAAA,WAAAgE,QAAA,CAAS5C,YAAY,CAACM,IAAI,CAAS,CAAC,KAAE,CAACN,YAAY,CAACK,KAAK,CAAC,kCAE5F,EAAG,CAAC,cACJvB,KAAA,QAAK6D,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzChE,IAAA,WACEiE,OAAO,CAAEA,CAAA,GAAM9C,kBAAkB,CAAC,KAAK,CAAE,CACzC4C,SAAS,CAAC,gGAAgG,CAAAC,QAAA,CAC3G,QAED,CAAQ,CAAC,cACThE,IAAA,WACEiE,OAAO,CAAET,iBAAkB,CAC3BO,SAAS,CAAC,qHAAqH,CAAAC,QAAA,CAChI,aAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACH,CAAC,CACH,CACN,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAA3D,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}