// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v3.17.3
// source: test.proto

package testpb

import (
	_ "github.com/lyft/clutch/backend/api/api/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type LogOptionsTester struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrLogFalse      string                            `protobuf:"bytes,1,opt,name=str_log_false,json=strLogFalse,proto3" json:"str_log_false,omitempty"`
	StrLogTrue       string                            `protobuf:"bytes,2,opt,name=str_log_true,json=strLogTrue,proto3" json:"str_log_true,omitempty"`
	StrWithoutOption string                            `protobuf:"bytes,3,opt,name=str_without_option,json=strWithoutOption,proto3" json:"str_without_option,omitempty"`
	NestedNoLog      *NestedLogOptionTester            `protobuf:"bytes,4,opt,name=nested_no_log,json=nestedNoLog,proto3" json:"nested_no_log,omitempty"`
	Nested           *NestedLogOptionTester            `protobuf:"bytes,5,opt,name=nested,proto3" json:"nested,omitempty"`
	MessageMap       map[string]*NestedLogOptionTester `protobuf:"bytes,6,rep,name=message_map,json=messageMap,proto3" json:"message_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	RepeatedMessage  []*NestedLogOptionTester          `protobuf:"bytes,7,rep,name=repeated_message,json=repeatedMessage,proto3" json:"repeated_message,omitempty"`
}

func (x *LogOptionsTester) Reset() {
	*x = LogOptionsTester{}
	if protoimpl.UnsafeEnabled {
		mi := &file_test_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LogOptionsTester) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogOptionsTester) ProtoMessage() {}

func (x *LogOptionsTester) ProtoReflect() protoreflect.Message {
	mi := &file_test_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogOptionsTester.ProtoReflect.Descriptor instead.
func (*LogOptionsTester) Descriptor() ([]byte, []int) {
	return file_test_proto_rawDescGZIP(), []int{0}
}

func (x *LogOptionsTester) GetStrLogFalse() string {
	if x != nil {
		return x.StrLogFalse
	}
	return ""
}

func (x *LogOptionsTester) GetStrLogTrue() string {
	if x != nil {
		return x.StrLogTrue
	}
	return ""
}

func (x *LogOptionsTester) GetStrWithoutOption() string {
	if x != nil {
		return x.StrWithoutOption
	}
	return ""
}

func (x *LogOptionsTester) GetNestedNoLog() *NestedLogOptionTester {
	if x != nil {
		return x.NestedNoLog
	}
	return nil
}

func (x *LogOptionsTester) GetNested() *NestedLogOptionTester {
	if x != nil {
		return x.Nested
	}
	return nil
}

func (x *LogOptionsTester) GetMessageMap() map[string]*NestedLogOptionTester {
	if x != nil {
		return x.MessageMap
	}
	return nil
}

func (x *LogOptionsTester) GetRepeatedMessage() []*NestedLogOptionTester {
	if x != nil {
		return x.RepeatedMessage
	}
	return nil
}

type NestedLogOptionTester struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrLogFalse      string `protobuf:"bytes,1,opt,name=str_log_false,json=strLogFalse,proto3" json:"str_log_false,omitempty"`
	StrWithoutOption string `protobuf:"bytes,2,opt,name=str_without_option,json=strWithoutOption,proto3" json:"str_without_option,omitempty"`
}

func (x *NestedLogOptionTester) Reset() {
	*x = NestedLogOptionTester{}
	if protoimpl.UnsafeEnabled {
		mi := &file_test_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NestedLogOptionTester) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NestedLogOptionTester) ProtoMessage() {}

func (x *NestedLogOptionTester) ProtoReflect() protoreflect.Message {
	mi := &file_test_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NestedLogOptionTester.ProtoReflect.Descriptor instead.
func (*NestedLogOptionTester) Descriptor() ([]byte, []int) {
	return file_test_proto_rawDescGZIP(), []int{1}
}

func (x *NestedLogOptionTester) GetStrLogFalse() string {
	if x != nil {
		return x.StrLogFalse
	}
	return ""
}

func (x *NestedLogOptionTester) GetStrWithoutOption() string {
	if x != nil {
		return x.StrWithoutOption
	}
	return ""
}

var File_test_proto protoreflect.FileDescriptor

var file_test_proto_rawDesc = []byte{
	0x0a, 0x0a, 0x74, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x16, 0x63, 0x6c,
	0x75, 0x74, 0x63, 0x68, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x74, 0x65,
	0x73, 0x74, 0x70, 0x62, 0x1a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd5,
	0x04, 0x0a, 0x10, 0x4c, 0x6f, 0x67, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x54, 0x65, 0x73,
	0x74, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x0d, 0x73, 0x74, 0x72, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x66,
	0x61, 0x6c, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x04, 0xa8, 0xe1, 0x1c, 0x00,
	0x52, 0x0b, 0x73, 0x74, 0x72, 0x4c, 0x6f, 0x67, 0x46, 0x61, 0x6c, 0x73, 0x65, 0x12, 0x26, 0x0a,
	0x0c, 0x73, 0x74, 0x72, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x74, 0x72, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x04, 0xa8, 0xe1, 0x1c, 0x01, 0x52, 0x0a, 0x73, 0x74, 0x72, 0x4c, 0x6f,
	0x67, 0x54, 0x72, 0x75, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x74, 0x72, 0x5f, 0x77, 0x69, 0x74,
	0x68, 0x6f, 0x75, 0x74, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x73, 0x74, 0x72, 0x57, 0x69, 0x74, 0x68, 0x6f, 0x75, 0x74, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x57, 0x0a, 0x0d, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x6e, 0x6f,
	0x5f, 0x6c, 0x6f, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x63, 0x6c, 0x75,
	0x74, 0x63, 0x68, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x74, 0x65, 0x73,
	0x74, 0x70, 0x62, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x67, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x65, 0x73, 0x74, 0x65, 0x72, 0x42, 0x04, 0xa8, 0xe1, 0x1c, 0x00, 0x52,
	0x0b, 0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4e, 0x6f, 0x4c, 0x6f, 0x67, 0x12, 0x45, 0x0a, 0x06,
	0x6e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x63,
	0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x74,
	0x65, 0x73, 0x74, 0x70, 0x62, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x67, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x73, 0x74, 0x65, 0x72, 0x52, 0x06, 0x6e, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x12, 0x59, 0x0a, 0x0b, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x6d,
	0x61, 0x70, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63,
	0x68, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x70,
	0x62, 0x2e, 0x4c, 0x6f, 0x67, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x54, 0x65, 0x73, 0x74,
	0x65, 0x72, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x0a, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4d, 0x61, 0x70, 0x12, 0x58,
	0x0a, 0x10, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x63, 0x6c, 0x75, 0x74, 0x63,
	0x68, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x74, 0x65, 0x73, 0x74, 0x70,
	0x62, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x67, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x65, 0x73, 0x74, 0x65, 0x72, 0x52, 0x0f, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x6c, 0x0a, 0x0f, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x43, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x63,
	0x6c, 0x75, 0x74, 0x63, 0x68, 0x2e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x74,
	0x65, 0x73, 0x74, 0x70, 0x62, 0x2e, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x67, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x73, 0x74, 0x65, 0x72, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x6f, 0x0a, 0x15, 0x4e, 0x65, 0x73, 0x74, 0x65, 0x64,
	0x4c, 0x6f, 0x67, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x73, 0x74, 0x65, 0x72, 0x12,
	0x28, 0x0a, 0x0d, 0x73, 0x74, 0x72, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x66, 0x61, 0x6c, 0x73, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x04, 0xa8, 0xe1, 0x1c, 0x00, 0x52, 0x0b, 0x73, 0x74,
	0x72, 0x4c, 0x6f, 0x67, 0x46, 0x61, 0x6c, 0x73, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x74, 0x72,
	0x5f, 0x77, 0x69, 0x74, 0x68, 0x6f, 0x75, 0x74, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x74, 0x72, 0x57, 0x69, 0x74, 0x68, 0x6f, 0x75,
	0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x38, 0x5a, 0x36, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6c, 0x79, 0x66, 0x74, 0x2f, 0x63, 0x6c, 0x75, 0x74, 0x63,
	0x68, 0x2f, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x2f, 0x70, 0x62, 0x3b, 0x74, 0x65, 0x73, 0x74, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_test_proto_rawDescOnce sync.Once
	file_test_proto_rawDescData = file_test_proto_rawDesc
)

func file_test_proto_rawDescGZIP() []byte {
	file_test_proto_rawDescOnce.Do(func() {
		file_test_proto_rawDescData = protoimpl.X.CompressGZIP(file_test_proto_rawDescData)
	})
	return file_test_proto_rawDescData
}

var file_test_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_test_proto_goTypes = []interface{}{
	(*LogOptionsTester)(nil),      // 0: clutch.internal.testpb.LogOptionsTester
	(*NestedLogOptionTester)(nil), // 1: clutch.internal.testpb.NestedLogOptionTester
	nil,                           // 2: clutch.internal.testpb.LogOptionsTester.MessageMapEntry
}
var file_test_proto_depIdxs = []int32{
	1, // 0: clutch.internal.testpb.LogOptionsTester.nested_no_log:type_name -> clutch.internal.testpb.NestedLogOptionTester
	1, // 1: clutch.internal.testpb.LogOptionsTester.nested:type_name -> clutch.internal.testpb.NestedLogOptionTester
	2, // 2: clutch.internal.testpb.LogOptionsTester.message_map:type_name -> clutch.internal.testpb.LogOptionsTester.MessageMapEntry
	1, // 3: clutch.internal.testpb.LogOptionsTester.repeated_message:type_name -> clutch.internal.testpb.NestedLogOptionTester
	1, // 4: clutch.internal.testpb.LogOptionsTester.MessageMapEntry.value:type_name -> clutch.internal.testpb.NestedLogOptionTester
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_test_proto_init() }
func file_test_proto_init() {
	if File_test_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_test_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LogOptionsTester); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_test_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NestedLogOptionTester); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_test_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_test_proto_goTypes,
		DependencyIndexes: file_test_proto_depIdxs,
		MessageInfos:      file_test_proto_msgTypes,
	}.Build()
	File_test_proto = out.File
	file_test_proto_rawDesc = nil
	file_test_proto_goTypes = nil
	file_test_proto_depIdxs = nil
}
