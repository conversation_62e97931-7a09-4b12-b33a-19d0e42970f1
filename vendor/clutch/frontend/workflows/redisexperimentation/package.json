{"name": "@clutch-sh/redis-experimentation", "version": "4.0.0-beta", "description": "Redis Fault Experimentation Workflows", "license": "Apache-2.0", "author": "<EMAIL>", "main": "dist/index.js", "scripts": {"clean": "yarn run package:clean", "compile": "yarn run package:compile", "compile:dev": "yarn run package:compile:dev", "compile:watch": "yarn run package:compile:watch", "lint": "yarn run package:lint", "lint:fix": "yarn run lint --fix", "publishBeta": "../../../tools/publish-frontend.sh redis-experimentation", "test": "yarn run package:test", "test:coverage": "yarn run test --collect-coverage", "test:watch": "yarn run test --watch"}, "dependencies": {"@clutch-sh/core": "workspace:^", "@clutch-sh/data-layout": "workspace:^", "@clutch-sh/experimentation": "workspace:^", "@clutch-sh/wizard": "workspace:^", "@hookform/resolvers": "2.8.8", "history": "^5.0.0", "react-hook-form": "^7.25.3", "react-is": "^17.0.2", "yup": "^0.32.8"}, "devDependencies": {"@clutch-sh/tools": "workspace:^"}, "peerDependencies": {"@mui/material": "^5.11.0", "react": "^17.0.2", "react-dom": "^17.0.2", "typescript": "^4.2.3"}, "engines": {"node": ">=18 <19", "yarn": "^4.5.0"}, "packageManager": "yarn@4.5.0", "stableVersion": "3.0.0-beta"}