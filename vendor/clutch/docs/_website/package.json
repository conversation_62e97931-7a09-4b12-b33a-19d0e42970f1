{"name": "website", "version": "0.0.0", "private": true, "scripts": {"start": "node watcher.ts & docusaurus start", "build": "docusaurus build", "swizzle": "docusaurus swizzle", "lint": "eslint src *.js .*.js", "lint:fix": "yarn run lint --fix"}, "dependencies": {"@algolia/client-search": "^4.14.2", "@docusaurus/core": "^2.0.0-beta", "@docusaurus/plugin-debug": "^2.0.0-beta", "@docusaurus/preset-classic": "^2.0.0-beta", "classnames": "^2.2.6", "prismjs": "^1.21.0", "react": "^17.0.0", "react-dom": "^17.0.0", "react-loadable": "^5.5.0", "remark-toc": "^8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@docusaurus/module-type-aliases": "^2.0.0-beta", "@tsconfig/docusaurus": "^1.0.4", "@types/node": "^18.0.0", "@types/react": "^17.0.0", "@types/react-helmet": "^6.1.0", "@types/react-router-dom": "^5.1.7", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "chokidar": "^3.5.1", "eslint": "^8.0.1", "eslint-config-prettier": "^8.5.0", "eslint-config-standard-with-typescript": "^23.0.0", "eslint-plugin-import": "^2.25.2", "eslint-plugin-n": "^15.0.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-promise": "^6.0.0", "eslint-plugin-react": "^7.31.10", "prettier": "^2.7.1", "react-tiny-popover": "^7.0.0", "typescript": "*"}, "engines": {"node": ">=18 <19", "yarn": "^4.5.0"}, "packageManager": "yarn@4.5.0"}