# @clutch-sh/core

The Core package consists of various reusable components that are shared between workflows and/or other Clutch packages.
This can span from things like the application provider, which renders the Clutch app, and resolver component to something as simple as a centralized button component.

For more information see the [documentation](https://clutch.sh/docs/development/frontend/overview#clutch-shcore).