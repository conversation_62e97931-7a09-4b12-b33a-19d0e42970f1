{"ast": null, "code": "import React,{useEffect,useState}from'react';import{useDispatch,useSelector}from'react-redux';import{fetchDatabaseStats,executeQuery,clearQueryResult}from'../store/slices/databaseSlice';import{CircleStackIcon,PlayIcon,TrashIcon}from'@heroicons/react/24/outline';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const DatabaseAdmin=()=>{var _stats$total_resource,_stats$total_workflow,_stats$total_envoy_co;const dispatch=useDispatch();const{stats,loading,error,queryResult,queryLoading}=useSelector(state=>state.database);const[query,setQuery]=useState('');useEffect(()=>{dispatch(fetchDatabaseStats());},[dispatch]);const handleExecuteQuery=()=>{if(query.trim()){dispatch(executeQuery(query));}};const handleClearResult=()=>{dispatch(clearQueryResult());};const sampleQueries=['SELECT * FROM resource_cache LIMIT 10','SELECT provider, COUNT(*) as count FROM resource_cache GROUP BY provider','SELECT * FROM workflow_executions WHERE status = \"running\"','SELECT * FROM envoy_configs ORDER BY updated_at DESC LIMIT 5'];return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-white\",children:\"Database Administration\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-gray-400\",children:\"Monitor database health and execute queries\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-gray-800 overflow-hidden shadow rounded-lg\",children:/*#__PURE__*/_jsx(\"div\",{className:\"p-5\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(CircleStackIcon,{className:\"h-6 w-6 text-blue-400\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"ml-5 w-0 flex-1\",children:/*#__PURE__*/_jsxs(\"dl\",{children:[/*#__PURE__*/_jsx(\"dt\",{className:\"text-sm font-medium text-gray-400 truncate\",children:\"Total Resources\"}),/*#__PURE__*/_jsx(\"dd\",{className:\"text-lg font-medium text-white\",children:(stats===null||stats===void 0?void 0:(_stats$total_resource=stats.total_resources)===null||_stats$total_resource===void 0?void 0:_stats$total_resource.toLocaleString())||0})]})})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-gray-800 overflow-hidden shadow rounded-lg\",children:/*#__PURE__*/_jsx(\"div\",{className:\"p-5\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(CircleStackIcon,{className:\"h-6 w-6 text-green-400\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"ml-5 w-0 flex-1\",children:/*#__PURE__*/_jsxs(\"dl\",{children:[/*#__PURE__*/_jsx(\"dt\",{className:\"text-sm font-medium text-gray-400 truncate\",children:\"Workflows\"}),/*#__PURE__*/_jsx(\"dd\",{className:\"text-lg font-medium text-white\",children:(stats===null||stats===void 0?void 0:(_stats$total_workflow=stats.total_workflows)===null||_stats$total_workflow===void 0?void 0:_stats$total_workflow.toLocaleString())||0})]})})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-gray-800 overflow-hidden shadow rounded-lg\",children:/*#__PURE__*/_jsx(\"div\",{className:\"p-5\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(CircleStackIcon,{className:\"h-6 w-6 text-purple-400\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"ml-5 w-0 flex-1\",children:/*#__PURE__*/_jsxs(\"dl\",{children:[/*#__PURE__*/_jsx(\"dt\",{className:\"text-sm font-medium text-gray-400 truncate\",children:\"Envoy Configs\"}),/*#__PURE__*/_jsx(\"dd\",{className:\"text-lg font-medium text-white\",children:(stats===null||stats===void 0?void 0:(_stats$total_envoy_co=stats.total_envoy_configs)===null||_stats$total_envoy_co===void 0?void 0:_stats$total_envoy_co.toLocaleString())||0})]})})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-gray-800 overflow-hidden shadow rounded-lg\",children:/*#__PURE__*/_jsx(\"div\",{className:\"p-5\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(CircleStackIcon,{className:\"h-6 w-6 text-yellow-400\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"ml-5 w-0 flex-1\",children:/*#__PURE__*/_jsxs(\"dl\",{children:[/*#__PURE__*/_jsx(\"dt\",{className:\"text-sm font-medium text-gray-400 truncate\",children:\"Cache Hit Rate\"}),/*#__PURE__*/_jsx(\"dd\",{className:\"text-lg font-medium text-white\",children:stats!==null&&stats!==void 0&&stats.cache_hit_rate?\"\".concat(stats.cache_hit_rate,\"%\"):'N/A'})]})})]})})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-gray-800 shadow rounded-lg\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"px-4 py-5 sm:p-6\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg leading-6 font-medium text-white mb-4\",children:\"Database Information\"}),loading?/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center h-32\",children:/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500\"})}):/*#__PURE__*/_jsxs(\"dl\",{className:\"grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"dt\",{className:\"text-sm font-medium text-gray-400\",children:\"Database Size\"}),/*#__PURE__*/_jsx(\"dd\",{className:\"mt-1 text-sm text-white\",children:(stats===null||stats===void 0?void 0:stats.database_size)||'Unknown'})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"dt\",{className:\"text-sm font-medium text-gray-400\",children:\"Cache Size\"}),/*#__PURE__*/_jsx(\"dd\",{className:\"mt-1 text-sm text-white\",children:(stats===null||stats===void 0?void 0:stats.cache_size)||'Unknown'})]})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-gray-800 shadow rounded-lg\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"px-4 py-5 sm:p-6\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg leading-6 font-medium text-white mb-4\",children:\"SQL Query Interface\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-300 mb-2\",children:\"SQL Query\"}),/*#__PURE__*/_jsx(\"textarea\",{className:\"block w-full h-32 px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white font-mono text-sm focus:outline-none focus:ring-2 focus:ring-cyan-500\",value:query,onChange:e=>setQuery(e.target.value),placeholder:\"Enter your SQL query here...\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:handleExecuteQuery,disabled:queryLoading||!query.trim(),className:\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 disabled:opacity-50\",children:[/*#__PURE__*/_jsx(PlayIcon,{className:\"h-4 w-4 mr-2\"}),queryLoading?'Executing...':'Execute Query']}),queryResult&&/*#__PURE__*/_jsxs(\"button\",{onClick:handleClearResult,className:\"inline-flex items-center px-4 py-2 border border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-300 bg-gray-700 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500\",children:[/*#__PURE__*/_jsx(TrashIcon,{className:\"h-4 w-4 mr-2\"}),\"Clear Result\"]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-gray-300 mb-2\",children:\"Sample Queries\"}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-2\",children:sampleQueries.map((sampleQuery,index)=>/*#__PURE__*/_jsx(\"button\",{onClick:()=>setQuery(sampleQuery),className:\"block w-full text-left px-3 py-2 text-sm text-gray-300 bg-gray-700 hover:bg-gray-600 rounded border border-gray-600\",children:sampleQuery},index))})]})]}),error&&/*#__PURE__*/_jsx(\"div\",{className:\"mt-4 bg-red-50 border border-red-200 rounded-md p-4\",children:/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-red-700\",children:error})}),queryResult&&/*#__PURE__*/_jsxs(\"div\",{className:\"mt-6\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"text-md font-medium text-white mb-3\",children:\"Query Results\"}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-gray-700 rounded-md p-4 overflow-x-auto\",children:/*#__PURE__*/_jsx(\"pre\",{className:\"text-sm text-gray-300\",children:JSON.stringify(queryResult,null,2)})})]})]})})]});};export default DatabaseAdmin;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "fetchDatabaseStats", "execute<PERSON>uery", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "CircleStackIcon", "PlayIcon", "TrashIcon", "jsx", "_jsx", "jsxs", "_jsxs", "DatabaseAdmin", "_stats$total_resource", "_stats$total_workflow", "_stats$total_envoy_co", "dispatch", "stats", "loading", "error", "query<PERSON><PERSON>ult", "queryLoading", "state", "database", "query", "<PERSON><PERSON><PERSON><PERSON>", "handleExecuteQuery", "trim", "handleClearResult", "sampleQueries", "className", "children", "total_resources", "toLocaleString", "total_workflows", "total_envoy_configs", "cache_hit_rate", "concat", "database_size", "cache_size", "value", "onChange", "e", "target", "placeholder", "onClick", "disabled", "map", "sampleQuery", "index", "JSON", "stringify"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/DatabaseAdmin.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { RootState, AppDispatch } from '../store/store';\nimport { fetchDatabaseStats, executeQuery, clearQueryResult } from '../store/slices/databaseSlice';\nimport {\n  CircleStackIcon,\n  PlayIcon,\n  TrashIcon,\n} from '@heroicons/react/24/outline';\n\nconst DatabaseAdmin: React.FC = () => {\n  const dispatch = useDispatch<AppDispatch>();\n  const { stats, loading, error, queryResult, queryLoading } = useSelector(\n    (state: RootState) => state.database\n  );\n\n  const [query, setQuery] = useState('');\n\n  useEffect(() => {\n    dispatch(fetchDatabaseStats());\n  }, [dispatch]);\n\n  const handleExecuteQuery = () => {\n    if (query.trim()) {\n      dispatch(executeQuery(query));\n    }\n  };\n\n  const handleClearResult = () => {\n    dispatch(clearQueryResult());\n  };\n\n  const sampleQueries = [\n    'SELECT * FROM resource_cache LIMIT 10',\n    'SELECT provider, COUNT(*) as count FROM resource_cache GROUP BY provider',\n    'SELECT * FROM workflow_executions WHERE status = \"running\"',\n    'SELECT * FROM envoy_configs ORDER BY updated_at DESC LIMIT 5',\n  ];\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div>\n        <h1 className=\"text-2xl font-bold text-white\">Database Administration</h1>\n        <p className=\"mt-1 text-sm text-gray-400\">\n          Monitor database health and execute queries\n        </p>\n      </div>\n\n      {/* Database Stats */}\n      <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\">\n        <div className=\"bg-gray-800 overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <CircleStackIcon className=\"h-6 w-6 text-blue-400\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-400 truncate\">Total Resources</dt>\n                  <dd className=\"text-lg font-medium text-white\">\n                    {stats?.total_resources?.toLocaleString() || 0}\n                  </dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-gray-800 overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <CircleStackIcon className=\"h-6 w-6 text-green-400\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-400 truncate\">Workflows</dt>\n                  <dd className=\"text-lg font-medium text-white\">\n                    {stats?.total_workflows?.toLocaleString() || 0}\n                  </dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-gray-800 overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <CircleStackIcon className=\"h-6 w-6 text-purple-400\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-400 truncate\">Envoy Configs</dt>\n                  <dd className=\"text-lg font-medium text-white\">\n                    {stats?.total_envoy_configs?.toLocaleString() || 0}\n                  </dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-gray-800 overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <CircleStackIcon className=\"h-6 w-6 text-yellow-400\" />\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-400 truncate\">Cache Hit Rate</dt>\n                  <dd className=\"text-lg font-medium text-white\">\n                    {stats?.cache_hit_rate ? `${stats.cache_hit_rate}%` : 'N/A'}\n                  </dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Database Info */}\n      <div className=\"bg-gray-800 shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <h3 className=\"text-lg leading-6 font-medium text-white mb-4\">Database Information</h3>\n          \n          {loading ? (\n            <div className=\"flex items-center justify-center h-32\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500\"></div>\n            </div>\n          ) : (\n            <dl className=\"grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2\">\n              <div>\n                <dt className=\"text-sm font-medium text-gray-400\">Database Size</dt>\n                <dd className=\"mt-1 text-sm text-white\">{stats?.database_size || 'Unknown'}</dd>\n              </div>\n              <div>\n                <dt className=\"text-sm font-medium text-gray-400\">Cache Size</dt>\n                <dd className=\"mt-1 text-sm text-white\">{stats?.cache_size || 'Unknown'}</dd>\n              </div>\n            </dl>\n          )}\n        </div>\n      </div>\n\n      {/* Query Interface */}\n      <div className=\"bg-gray-800 shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <h3 className=\"text-lg leading-6 font-medium text-white mb-4\">SQL Query Interface</h3>\n          \n          <div className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                SQL Query\n              </label>\n              <textarea\n                className=\"block w-full h-32 px-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white font-mono text-sm focus:outline-none focus:ring-2 focus:ring-cyan-500\"\n                value={query}\n                onChange={(e) => setQuery(e.target.value)}\n                placeholder=\"Enter your SQL query here...\"\n              />\n            </div>\n\n            <div className=\"flex items-center space-x-3\">\n              <button\n                onClick={handleExecuteQuery}\n                disabled={queryLoading || !query.trim()}\n                className=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 disabled:opacity-50\"\n              >\n                <PlayIcon className=\"h-4 w-4 mr-2\" />\n                {queryLoading ? 'Executing...' : 'Execute Query'}\n              </button>\n\n              {queryResult && (\n                <button\n                  onClick={handleClearResult}\n                  className=\"inline-flex items-center px-4 py-2 border border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-300 bg-gray-700 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500\"\n                >\n                  <TrashIcon className=\"h-4 w-4 mr-2\" />\n                  Clear Result\n                </button>\n              )}\n            </div>\n\n            {/* Sample Queries */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Sample Queries\n              </label>\n              <div className=\"space-y-2\">\n                {sampleQueries.map((sampleQuery, index) => (\n                  <button\n                    key={index}\n                    onClick={() => setQuery(sampleQuery)}\n                    className=\"block w-full text-left px-3 py-2 text-sm text-gray-300 bg-gray-700 hover:bg-gray-600 rounded border border-gray-600\"\n                  >\n                    {sampleQuery}\n                  </button>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {error && (\n            <div className=\"mt-4 bg-red-50 border border-red-200 rounded-md p-4\">\n              <div className=\"text-sm text-red-700\">{error}</div>\n            </div>\n          )}\n\n          {/* Query Results */}\n          {queryResult && (\n            <div className=\"mt-6\">\n              <h4 className=\"text-md font-medium text-white mb-3\">Query Results</h4>\n              <div className=\"bg-gray-700 rounded-md p-4 overflow-x-auto\">\n                <pre className=\"text-sm text-gray-300\">\n                  {JSON.stringify(queryResult, null, 2)}\n                </pre>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default DatabaseAdmin;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CAEtD,OAASC,kBAAkB,CAAEC,YAAY,CAAEC,gBAAgB,KAAQ,+BAA+B,CAClG,OACEC,eAAe,CACfC,QAAQ,CACRC,SAAS,KACJ,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAErC,KAAM,CAAAC,aAAuB,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CACpC,KAAM,CAAAC,QAAQ,CAAGhB,WAAW,CAAc,CAAC,CAC3C,KAAM,CAAEiB,KAAK,CAAEC,OAAO,CAAEC,KAAK,CAAEC,WAAW,CAAEC,YAAa,CAAC,CAAGpB,WAAW,CACrEqB,KAAgB,EAAKA,KAAK,CAACC,QAC9B,CAAC,CAED,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAG1B,QAAQ,CAAC,EAAE,CAAC,CAEtCD,SAAS,CAAC,IAAM,CACdkB,QAAQ,CAACd,kBAAkB,CAAC,CAAC,CAAC,CAChC,CAAC,CAAE,CAACc,QAAQ,CAAC,CAAC,CAEd,KAAM,CAAAU,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,GAAIF,KAAK,CAACG,IAAI,CAAC,CAAC,CAAE,CAChBX,QAAQ,CAACb,YAAY,CAACqB,KAAK,CAAC,CAAC,CAC/B,CACF,CAAC,CAED,KAAM,CAAAI,iBAAiB,CAAGA,CAAA,GAAM,CAC9BZ,QAAQ,CAACZ,gBAAgB,CAAC,CAAC,CAAC,CAC9B,CAAC,CAED,KAAM,CAAAyB,aAAa,CAAG,CACpB,uCAAuC,CACvC,0EAA0E,CAC1E,4DAA4D,CAC5D,8DAA8D,CAC/D,CAED,mBACElB,KAAA,QAAKmB,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExBpB,KAAA,QAAAoB,QAAA,eACEtB,IAAA,OAAIqB,SAAS,CAAC,+BAA+B,CAAAC,QAAA,CAAC,yBAAuB,CAAI,CAAC,cAC1EtB,IAAA,MAAGqB,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,6CAE1C,CAAG,CAAC,EACD,CAAC,cAGNpB,KAAA,QAAKmB,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eACnEtB,IAAA,QAAKqB,SAAS,CAAC,+CAA+C,CAAAC,QAAA,cAC5DtB,IAAA,QAAKqB,SAAS,CAAC,KAAK,CAAAC,QAAA,cAClBpB,KAAA,QAAKmB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCtB,IAAA,QAAKqB,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5BtB,IAAA,CAACJ,eAAe,EAACyB,SAAS,CAAC,uBAAuB,CAAE,CAAC,CAClD,CAAC,cACNrB,IAAA,QAAKqB,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9BpB,KAAA,OAAAoB,QAAA,eACEtB,IAAA,OAAIqB,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,cAC/EtB,IAAA,OAAIqB,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAC3C,CAAAd,KAAK,SAALA,KAAK,kBAAAJ,qBAAA,CAALI,KAAK,CAAEe,eAAe,UAAAnB,qBAAA,iBAAtBA,qBAAA,CAAwBoB,cAAc,CAAC,CAAC,GAAI,CAAC,CAC5C,CAAC,EACH,CAAC,CACF,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,cAENxB,IAAA,QAAKqB,SAAS,CAAC,+CAA+C,CAAAC,QAAA,cAC5DtB,IAAA,QAAKqB,SAAS,CAAC,KAAK,CAAAC,QAAA,cAClBpB,KAAA,QAAKmB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCtB,IAAA,QAAKqB,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5BtB,IAAA,CAACJ,eAAe,EAACyB,SAAS,CAAC,wBAAwB,CAAE,CAAC,CACnD,CAAC,cACNrB,IAAA,QAAKqB,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9BpB,KAAA,OAAAoB,QAAA,eACEtB,IAAA,OAAIqB,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CAAC,WAAS,CAAI,CAAC,cACzEtB,IAAA,OAAIqB,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAC3C,CAAAd,KAAK,SAALA,KAAK,kBAAAH,qBAAA,CAALG,KAAK,CAAEiB,eAAe,UAAApB,qBAAA,iBAAtBA,qBAAA,CAAwBmB,cAAc,CAAC,CAAC,GAAI,CAAC,CAC5C,CAAC,EACH,CAAC,CACF,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,cAENxB,IAAA,QAAKqB,SAAS,CAAC,+CAA+C,CAAAC,QAAA,cAC5DtB,IAAA,QAAKqB,SAAS,CAAC,KAAK,CAAAC,QAAA,cAClBpB,KAAA,QAAKmB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCtB,IAAA,QAAKqB,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5BtB,IAAA,CAACJ,eAAe,EAACyB,SAAS,CAAC,yBAAyB,CAAE,CAAC,CACpD,CAAC,cACNrB,IAAA,QAAKqB,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9BpB,KAAA,OAAAoB,QAAA,eACEtB,IAAA,OAAIqB,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CAAC,eAAa,CAAI,CAAC,cAC7EtB,IAAA,OAAIqB,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAC3C,CAAAd,KAAK,SAALA,KAAK,kBAAAF,qBAAA,CAALE,KAAK,CAAEkB,mBAAmB,UAAApB,qBAAA,iBAA1BA,qBAAA,CAA4BkB,cAAc,CAAC,CAAC,GAAI,CAAC,CAChD,CAAC,EACH,CAAC,CACF,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,cAENxB,IAAA,QAAKqB,SAAS,CAAC,+CAA+C,CAAAC,QAAA,cAC5DtB,IAAA,QAAKqB,SAAS,CAAC,KAAK,CAAAC,QAAA,cAClBpB,KAAA,QAAKmB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCtB,IAAA,QAAKqB,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5BtB,IAAA,CAACJ,eAAe,EAACyB,SAAS,CAAC,yBAAyB,CAAE,CAAC,CACpD,CAAC,cACNrB,IAAA,QAAKqB,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9BpB,KAAA,OAAAoB,QAAA,eACEtB,IAAA,OAAIqB,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CAAC,gBAAc,CAAI,CAAC,cAC9EtB,IAAA,OAAIqB,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAC3Cd,KAAK,SAALA,KAAK,WAALA,KAAK,CAAEmB,cAAc,IAAAC,MAAA,CAAMpB,KAAK,CAACmB,cAAc,MAAM,KAAK,CACzD,CAAC,EACH,CAAC,CACF,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,EACH,CAAC,cAGN3B,IAAA,QAAKqB,SAAS,CAAC,+BAA+B,CAAAC,QAAA,cAC5CpB,KAAA,QAAKmB,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BtB,IAAA,OAAIqB,SAAS,CAAC,+CAA+C,CAAAC,QAAA,CAAC,sBAAoB,CAAI,CAAC,CAEtFb,OAAO,cACNT,IAAA,QAAKqB,SAAS,CAAC,uCAAuC,CAAAC,QAAA,cACpDtB,IAAA,QAAKqB,SAAS,CAAC,8DAA8D,CAAM,CAAC,CACjF,CAAC,cAENnB,KAAA,OAAImB,SAAS,CAAC,iDAAiD,CAAAC,QAAA,eAC7DpB,KAAA,QAAAoB,QAAA,eACEtB,IAAA,OAAIqB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,eAAa,CAAI,CAAC,cACpEtB,IAAA,OAAIqB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAE,CAAAd,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEqB,aAAa,GAAI,SAAS,CAAK,CAAC,EAC7E,CAAC,cACN3B,KAAA,QAAAoB,QAAA,eACEtB,IAAA,OAAIqB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,YAAU,CAAI,CAAC,cACjEtB,IAAA,OAAIqB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAE,CAAAd,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEsB,UAAU,GAAI,SAAS,CAAK,CAAC,EAC1E,CAAC,EACJ,CACL,EACE,CAAC,CACH,CAAC,cAGN9B,IAAA,QAAKqB,SAAS,CAAC,+BAA+B,CAAAC,QAAA,cAC5CpB,KAAA,QAAKmB,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BtB,IAAA,OAAIqB,SAAS,CAAC,+CAA+C,CAAAC,QAAA,CAAC,qBAAmB,CAAI,CAAC,cAEtFpB,KAAA,QAAKmB,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBpB,KAAA,QAAAoB,QAAA,eACEtB,IAAA,UAAOqB,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,WAEhE,CAAO,CAAC,cACRtB,IAAA,aACEqB,SAAS,CAAC,4JAA4J,CACtKU,KAAK,CAAEhB,KAAM,CACbiB,QAAQ,CAAGC,CAAC,EAAKjB,QAAQ,CAACiB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC1CI,WAAW,CAAC,8BAA8B,CAC3C,CAAC,EACC,CAAC,cAENjC,KAAA,QAAKmB,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CpB,KAAA,WACEkC,OAAO,CAAEnB,kBAAmB,CAC5BoB,QAAQ,CAAEzB,YAAY,EAAI,CAACG,KAAK,CAACG,IAAI,CAAC,CAAE,CACxCG,SAAS,CAAC,4OAA4O,CAAAC,QAAA,eAEtPtB,IAAA,CAACH,QAAQ,EAACwB,SAAS,CAAC,cAAc,CAAE,CAAC,CACpCT,YAAY,CAAG,cAAc,CAAG,eAAe,EAC1C,CAAC,CAERD,WAAW,eACVT,KAAA,WACEkC,OAAO,CAAEjB,iBAAkB,CAC3BE,SAAS,CAAC,wNAAwN,CAAAC,QAAA,eAElOtB,IAAA,CAACF,SAAS,EAACuB,SAAS,CAAC,cAAc,CAAE,CAAC,eAExC,EAAQ,CACT,EACE,CAAC,cAGNnB,KAAA,QAAAoB,QAAA,eACEtB,IAAA,UAAOqB,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,gBAEhE,CAAO,CAAC,cACRtB,IAAA,QAAKqB,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBF,aAAa,CAACkB,GAAG,CAAC,CAACC,WAAW,CAAEC,KAAK,gBACpCxC,IAAA,WAEEoC,OAAO,CAAEA,CAAA,GAAMpB,QAAQ,CAACuB,WAAW,CAAE,CACrClB,SAAS,CAAC,qHAAqH,CAAAC,QAAA,CAE9HiB,WAAW,EAJPC,KAKC,CACT,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CAEL9B,KAAK,eACJV,IAAA,QAAKqB,SAAS,CAAC,qDAAqD,CAAAC,QAAA,cAClEtB,IAAA,QAAKqB,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAEZ,KAAK,CAAM,CAAC,CAChD,CACN,CAGAC,WAAW,eACVT,KAAA,QAAKmB,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBtB,IAAA,OAAIqB,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,eAAa,CAAI,CAAC,cACtEtB,IAAA,QAAKqB,SAAS,CAAC,4CAA4C,CAAAC,QAAA,cACzDtB,IAAA,QAAKqB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CACnCmB,IAAI,CAACC,SAAS,CAAC/B,WAAW,CAAE,IAAI,CAAE,CAAC,CAAC,CAClC,CAAC,CACH,CAAC,EACH,CACN,EACE,CAAC,CACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAR,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}