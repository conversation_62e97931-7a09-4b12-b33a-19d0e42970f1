import { configureStore } from '@reduxjs/toolkit';
import searchReducer from './slices/searchSlice';
import workflowReducer from './slices/workflowSlice';
import envoyReducer from './slices/envoySlice';
import autoscalerReducer from './slices/autoscalerSlice';
import auditReducer from './slices/auditSlice';
import databaseReducer from './slices/databaseSlice';

export const store = configureStore({
  reducer: {
    search: searchReducer,
    workflow: workflowReducer,
    envoy: envoyReducer,
    autoscaler: autoscalerReducer,
    audit: auditReducer,
    database: databaseReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
