{"ast": null, "code": "import _objectSpread from\"/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{createContext,useContext,useEffect,useState}from'react';// Authentication types based on Clutch implementation\nimport{jsx as _jsx,Fragment as _Fragment}from\"react/jsx-runtime\";// Create auth context\nconst AuthContext=/*#__PURE__*/createContext(undefined);// Auth provider component\nexport const AuthProvider=_ref=>{let{children}=_ref;const[authState,setAuthState]=useState({isAuthenticated:false,isLoading:true,user:null,token:null,error:null});// Initialize authentication on mount\nuseEffect(()=>{initializeAuth();},[]);// Initialize authentication\nconst initializeAuth=async()=>{try{setAuthState(prev=>_objectSpread(_objectSpread({},prev),{},{isLoading:true,error:null}));// Check if user is already authenticated\nconst response=await fetch('/v1/auth/user',{credentials:'include'});if(response.ok){const user=await response.json();setAuthState(prev=>_objectSpread(_objectSpread({},prev),{},{isAuthenticated:true,user,isLoading:false}));}else{// Try to refresh token\nawait refreshToken();}}catch(error){console.error('Auth initialization failed:',error);setAuthState(prev=>_objectSpread(_objectSpread({},prev),{},{isAuthenticated:false,isLoading:false,error:'Authentication initialization failed'}));}};// Login function with username and password\nconst login=async function(){let username=arguments.length>0&&arguments[0]!==undefined?arguments[0]:'admin';let password=arguments.length>1&&arguments[1]!==undefined?arguments[1]:'admin';try{setAuthState(prev=>_objectSpread(_objectSpread({},prev),{},{isLoading:true,error:null}));// Login with username and password\nconst response=await fetch('/v1/auth/login',{method:'POST',headers:{'Content-Type':'application/json'},credentials:'include',body:JSON.stringify({username,password})});if(!response.ok){const errorData=await response.json();throw new Error(errorData.error||'Login failed');}const data=await response.json();setAuthState(prev=>_objectSpread(_objectSpread({},prev),{},{isAuthenticated:true,user:data.user,token:null,// Using cookie-based auth\nisLoading:false,error:null}));}catch(error){console.error('Login failed:',error);setAuthState(prev=>_objectSpread(_objectSpread({},prev),{},{isLoading:false,error:error instanceof Error?error.message:'Login failed'}));}};// Logout function\nconst logout=async()=>{try{setAuthState(prev=>_objectSpread(_objectSpread({},prev),{},{isLoading:true}));const response=await fetch('/v1/auth/logout',{method:'POST',credentials:'include'});setAuthState({isAuthenticated:false,isLoading:false,user:null,token:null,error:null});// Optionally redirect to logout URL if provided\nif(response.ok){const data=await response.json();if(data.logout_url&&data.logout_url!==window.location.origin){window.location.href=data.logout_url;return;}}// Reload page to clear any cached state\nwindow.location.reload();}catch(error){console.error('Logout failed:',error);setAuthState(prev=>_objectSpread(_objectSpread({},prev),{},{isLoading:false,error:'Logout failed'}));}};// Check permission function\nconst checkPermission=async(action,resource)=>{try{var _data$decision;const response=await fetch('/v1/auth/check',{method:'POST',headers:{'Content-Type':'application/json'},credentials:'include',body:JSON.stringify({action,resource})});if(!response.ok){return false;}const data=await response.json();return((_data$decision=data.decision)===null||_data$decision===void 0?void 0:_data$decision.allowed)||false;}catch(error){console.error('Permission check failed:',error);return false;}};// Refresh token function\nconst refreshToken=async()=>{try{const response=await fetch('/v1/auth/refresh',{method:'POST',credentials:'include'});if(response.ok){const tokenData=await response.json();// Get updated user info\nconst userResponse=await fetch('/v1/auth/user',{credentials:'include'});if(userResponse.ok){const user=await userResponse.json();setAuthState(prev=>_objectSpread(_objectSpread({},prev),{},{isAuthenticated:true,user,token:tokenData,isLoading:false,error:null}));}}else{throw new Error('Token refresh failed');}}catch(error){console.error('Token refresh failed:',error);setAuthState(prev=>_objectSpread(_objectSpread({},prev),{},{isAuthenticated:false,isLoading:false,user:null,token:null,error:'Session expired'}));}};// Auto-refresh token before expiry\nuseEffect(()=>{if(authState.token&&authState.isAuthenticated){const refreshInterval=setInterval(()=>{refreshToken();},50*60*1000);// Refresh every 50 minutes (token expires in 60 minutes)\nreturn()=>clearInterval(refreshInterval);}},[authState.token,authState.isAuthenticated]);const contextValue=_objectSpread(_objectSpread({},authState),{},{login,logout,checkPermission,refreshToken});return/*#__PURE__*/_jsx(AuthContext.Provider,{value:contextValue,children:children});};// Hook to use auth context\nexport const useAuth=()=>{const context=useContext(AuthContext);if(context===undefined){throw new Error('useAuth must be used within an AuthProvider');}return context;};// Higher-order component for protected routes\nexport const ProtectedRoute=_ref2=>{let{children,requiredPermission,fallback=/*#__PURE__*/_jsx(\"div\",{className:\"p-4 text-red-600\",children:\"Access Denied\"})}=_ref2;const{isAuthenticated,isLoading,checkPermission}=useAuth();const[hasPermission,setHasPermission]=useState(null);useEffect(()=>{if(isAuthenticated&&requiredPermission){checkPermission(requiredPermission.action,requiredPermission.resource).then(setHasPermission);}else if(isAuthenticated){setHasPermission(true);}},[isAuthenticated,requiredPermission,checkPermission]);if(isLoading){return/*#__PURE__*/_jsx(\"div\",{className:\"p-4\",children:\"Loading...\"});}if(!isAuthenticated){return/*#__PURE__*/_jsx(\"div\",{className:\"p-4\",children:\"Please log in to access this page.\"});}if(requiredPermission&&hasPermission===false){return/*#__PURE__*/_jsx(_Fragment,{children:fallback});}if(requiredPermission&&hasPermission===null){return/*#__PURE__*/_jsx(\"div\",{className:\"p-4\",children:\"Checking permissions...\"});}return/*#__PURE__*/_jsx(_Fragment,{children:children});};", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "jsx", "_jsx", "Fragment", "_Fragment", "AuthContext", "undefined", "<PERSON>th<PERSON><PERSON><PERSON>", "_ref", "children", "authState", "setAuthState", "isAuthenticated", "isLoading", "user", "token", "error", "initializeAuth", "prev", "_objectSpread", "response", "fetch", "credentials", "ok", "json", "refreshToken", "console", "login", "username", "arguments", "length", "password", "method", "headers", "body", "JSON", "stringify", "errorData", "Error", "data", "message", "logout", "logout_url", "window", "location", "origin", "href", "reload", "checkPermission", "action", "resource", "_data$decision", "decision", "allowed", "tokenData", "userResponse", "refreshInterval", "setInterval", "clearInterval", "contextValue", "Provider", "value", "useAuth", "context", "ProtectedRoute", "_ref2", "requiredPermission", "fallback", "className", "hasPermission", "setHasPermission", "then"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/auth/AuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';\n\n// Authentication types based on Clutch implementation\ninterface User {\n  sub: string;\n  email: string;\n  name: string;\n  groups: string[];\n  roles: string[];\n  permissions: string[];\n  preferences: {\n    theme: string;\n    timezone: string;\n    language: string;\n  };\n}\n\ninterface AuthToken {\n  access_token: string;\n  refresh_token?: string;\n  token_type: string;\n  expires_in: number;\n}\n\ninterface AuthState {\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  user: User | null;\n  token: AuthToken | null;\n  error: string | null;\n}\n\ninterface AuthContextType extends AuthState {\n  login: (username?: string, password?: string) => Promise<void>;\n  logout: () => Promise<void>;\n  checkPermission: (action: string, resource: string) => Promise<boolean>;\n  refreshToken: () => Promise<void>;\n}\n\n// Create auth context\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\n// Auth provider component\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [authState, setAuthState] = useState<AuthState>({\n    isAuthenticated: false,\n    isLoading: true,\n    user: null,\n    token: null,\n    error: null,\n  });\n\n  // Initialize authentication on mount\n  useEffect(() => {\n    initializeAuth();\n  }, []);\n\n  // Initialize authentication\n  const initializeAuth = async () => {\n    try {\n      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));\n\n      // Check if user is already authenticated\n      const response = await fetch('/v1/auth/user', {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const user = await response.json();\n        setAuthState(prev => ({\n          ...prev,\n          isAuthenticated: true,\n          user,\n          isLoading: false,\n        }));\n      } else {\n        // Try to refresh token\n        await refreshToken();\n      }\n    } catch (error) {\n      console.error('Auth initialization failed:', error);\n      setAuthState(prev => ({\n        ...prev,\n        isAuthenticated: false,\n        isLoading: false,\n        error: 'Authentication initialization failed',\n      }));\n    }\n  };\n\n  // Login function with username and password\n  const login = async (username: string = 'admin', password: string = 'admin') => {\n    try {\n      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));\n\n      // Login with username and password\n      const response = await fetch('/v1/auth/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include',\n        body: JSON.stringify({\n          username,\n          password,\n        }),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || 'Login failed');\n      }\n\n      const data = await response.json();\n\n      setAuthState(prev => ({\n        ...prev,\n        isAuthenticated: true,\n        user: data.user,\n        token: null, // Using cookie-based auth\n        isLoading: false,\n        error: null,\n      }));\n    } catch (error) {\n      console.error('Login failed:', error);\n      setAuthState(prev => ({\n        ...prev,\n        isLoading: false,\n        error: error instanceof Error ? error.message : 'Login failed',\n      }));\n    }\n  };\n\n  // Logout function\n  const logout = async () => {\n    try {\n      setAuthState(prev => ({ ...prev, isLoading: true }));\n\n      const response = await fetch('/v1/auth/logout', {\n        method: 'POST',\n        credentials: 'include',\n      });\n\n      setAuthState({\n        isAuthenticated: false,\n        isLoading: false,\n        user: null,\n        token: null,\n        error: null,\n      });\n\n      // Optionally redirect to logout URL if provided\n      if (response.ok) {\n        const data = await response.json();\n        if (data.logout_url && data.logout_url !== window.location.origin) {\n          window.location.href = data.logout_url;\n          return;\n        }\n      }\n      \n      // Reload page to clear any cached state\n      window.location.reload();\n    } catch (error) {\n      console.error('Logout failed:', error);\n      setAuthState(prev => ({\n        ...prev,\n        isLoading: false,\n        error: 'Logout failed',\n      }));\n    }\n  };\n\n  // Check permission function\n  const checkPermission = async (action: string, resource: string): Promise<boolean> => {\n    try {\n      const response = await fetch('/v1/auth/check', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include',\n        body: JSON.stringify({ action, resource }),\n      });\n\n      if (!response.ok) {\n        return false;\n      }\n\n      const data = await response.json();\n      return data.decision?.allowed || false;\n    } catch (error) {\n      console.error('Permission check failed:', error);\n      return false;\n    }\n  };\n\n  // Refresh token function\n  const refreshToken = async () => {\n    try {\n      const response = await fetch('/v1/auth/refresh', {\n        method: 'POST',\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const tokenData = await response.json();\n        \n        // Get updated user info\n        const userResponse = await fetch('/v1/auth/user', {\n          credentials: 'include',\n        });\n\n        if (userResponse.ok) {\n          const user = await userResponse.json();\n          setAuthState(prev => ({\n            ...prev,\n            isAuthenticated: true,\n            user,\n            token: tokenData,\n            isLoading: false,\n            error: null,\n          }));\n        }\n      } else {\n        throw new Error('Token refresh failed');\n      }\n    } catch (error) {\n      console.error('Token refresh failed:', error);\n      setAuthState(prev => ({\n        ...prev,\n        isAuthenticated: false,\n        isLoading: false,\n        user: null,\n        token: null,\n        error: 'Session expired',\n      }));\n    }\n  };\n\n  // Auto-refresh token before expiry\n  useEffect(() => {\n    if (authState.token && authState.isAuthenticated) {\n      const refreshInterval = setInterval(() => {\n        refreshToken();\n      }, 50 * 60 * 1000); // Refresh every 50 minutes (token expires in 60 minutes)\n\n      return () => clearInterval(refreshInterval);\n    }\n  }, [authState.token, authState.isAuthenticated]);\n\n  const contextValue: AuthContextType = {\n    ...authState,\n    login,\n    logout,\n    checkPermission,\n    refreshToken,\n  };\n\n  return (\n    <AuthContext.Provider value={contextValue}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\n// Hook to use auth context\nexport const useAuth = (): AuthContextType => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\n// Higher-order component for protected routes\ninterface ProtectedRouteProps {\n  children: ReactNode;\n  requiredPermission?: { action: string; resource: string };\n  fallback?: ReactNode;\n}\n\nexport const ProtectedRoute: React.FC<ProtectedRouteProps> = ({\n  children,\n  requiredPermission,\n  fallback = <div className=\"p-4 text-red-600\">Access Denied</div>,\n}) => {\n  const { isAuthenticated, isLoading, checkPermission } = useAuth();\n  const [hasPermission, setHasPermission] = useState<boolean | null>(null);\n\n  useEffect(() => {\n    if (isAuthenticated && requiredPermission) {\n      checkPermission(requiredPermission.action, requiredPermission.resource)\n        .then(setHasPermission);\n    } else if (isAuthenticated) {\n      setHasPermission(true);\n    }\n  }, [isAuthenticated, requiredPermission, checkPermission]);\n\n  if (isLoading) {\n    return <div className=\"p-4\">Loading...</div>;\n  }\n\n  if (!isAuthenticated) {\n    return <div className=\"p-4\">Please log in to access this page.</div>;\n  }\n\n  if (requiredPermission && hasPermission === false) {\n    return <>{fallback}</>;\n  }\n\n  if (requiredPermission && hasPermission === null) {\n    return <div className=\"p-4\">Checking permissions...</div>;\n  }\n\n  return <>{children}</>;\n};\n"], "mappings": "yJAAA,MAAO,CAAAA,KAAK,EAAIC,aAAa,CAAEC,UAAU,CAAEC,SAAS,CAAEC,QAAQ,KAAmB,OAAO,CAExF;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,yBAqCA;AACA,KAAM,CAAAC,WAAW,cAAGR,aAAa,CAA8BS,SAAS,CAAC,CAEzE;AAKA,MAAO,MAAM,CAAAC,YAAyC,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACpE,KAAM,CAACE,SAAS,CAAEC,YAAY,CAAC,CAAGX,QAAQ,CAAY,CACpDY,eAAe,CAAE,KAAK,CACtBC,SAAS,CAAE,IAAI,CACfC,IAAI,CAAE,IAAI,CACVC,KAAK,CAAE,IAAI,CACXC,KAAK,CAAE,IACT,CAAC,CAAC,CAEF;AACAjB,SAAS,CAAC,IAAM,CACdkB,cAAc,CAAC,CAAC,CAClB,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAA,cAAc,CAAG,KAAAA,CAAA,GAAY,CACjC,GAAI,CACFN,YAAY,CAACO,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAEL,SAAS,CAAE,IAAI,CAAEG,KAAK,CAAE,IAAI,EAAG,CAAC,CAEjE;AACA,KAAM,CAAAI,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,eAAe,CAAE,CAC5CC,WAAW,CAAE,SACf,CAAC,CAAC,CAEF,GAAIF,QAAQ,CAACG,EAAE,CAAE,CACf,KAAM,CAAAT,IAAI,CAAG,KAAM,CAAAM,QAAQ,CAACI,IAAI,CAAC,CAAC,CAClCb,YAAY,CAACO,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACZD,IAAI,MACPN,eAAe,CAAE,IAAI,CACrBE,IAAI,CACJD,SAAS,CAAE,KAAK,EAChB,CAAC,CACL,CAAC,IAAM,CACL;AACA,KAAM,CAAAY,YAAY,CAAC,CAAC,CACtB,CACF,CAAE,MAAOT,KAAK,CAAE,CACdU,OAAO,CAACV,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnDL,YAAY,CAACO,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACZD,IAAI,MACPN,eAAe,CAAE,KAAK,CACtBC,SAAS,CAAE,KAAK,CAChBG,KAAK,CAAE,sCAAsC,EAC7C,CAAC,CACL,CACF,CAAC,CAED;AACA,KAAM,CAAAW,KAAK,CAAG,cAAAA,CAAA,CAAkE,IAA3D,CAAAC,QAAgB,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAvB,SAAA,CAAAuB,SAAA,IAAG,OAAO,IAAE,CAAAE,QAAgB,CAAAF,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAvB,SAAA,CAAAuB,SAAA,IAAG,OAAO,CACzE,GAAI,CACFlB,YAAY,CAACO,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAEL,SAAS,CAAE,IAAI,CAAEG,KAAK,CAAE,IAAI,EAAG,CAAC,CAEjE;AACA,KAAM,CAAAI,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,gBAAgB,CAAE,CAC7CW,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDX,WAAW,CAAE,SAAS,CACtBY,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CACnBR,QAAQ,CACRG,QACF,CAAC,CACH,CAAC,CAAC,CAEF,GAAI,CAACX,QAAQ,CAACG,EAAE,CAAE,CAChB,KAAM,CAAAc,SAAS,CAAG,KAAM,CAAAjB,QAAQ,CAACI,IAAI,CAAC,CAAC,CACvC,KAAM,IAAI,CAAAc,KAAK,CAACD,SAAS,CAACrB,KAAK,EAAI,cAAc,CAAC,CACpD,CAEA,KAAM,CAAAuB,IAAI,CAAG,KAAM,CAAAnB,QAAQ,CAACI,IAAI,CAAC,CAAC,CAElCb,YAAY,CAACO,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACZD,IAAI,MACPN,eAAe,CAAE,IAAI,CACrBE,IAAI,CAAEyB,IAAI,CAACzB,IAAI,CACfC,KAAK,CAAE,IAAI,CAAE;AACbF,SAAS,CAAE,KAAK,CAChBG,KAAK,CAAE,IAAI,EACX,CAAC,CACL,CAAE,MAAOA,KAAK,CAAE,CACdU,OAAO,CAACV,KAAK,CAAC,eAAe,CAAEA,KAAK,CAAC,CACrCL,YAAY,CAACO,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACZD,IAAI,MACPL,SAAS,CAAE,KAAK,CAChBG,KAAK,CAAEA,KAAK,WAAY,CAAAsB,KAAK,CAAGtB,KAAK,CAACwB,OAAO,CAAG,cAAc,EAC9D,CAAC,CACL,CACF,CAAC,CAED;AACA,KAAM,CAAAC,MAAM,CAAG,KAAAA,CAAA,GAAY,CACzB,GAAI,CACF9B,YAAY,CAACO,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAEL,SAAS,CAAE,IAAI,EAAG,CAAC,CAEpD,KAAM,CAAAO,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,iBAAiB,CAAE,CAC9CW,MAAM,CAAE,MAAM,CACdV,WAAW,CAAE,SACf,CAAC,CAAC,CAEFX,YAAY,CAAC,CACXC,eAAe,CAAE,KAAK,CACtBC,SAAS,CAAE,KAAK,CAChBC,IAAI,CAAE,IAAI,CACVC,KAAK,CAAE,IAAI,CACXC,KAAK,CAAE,IACT,CAAC,CAAC,CAEF;AACA,GAAII,QAAQ,CAACG,EAAE,CAAE,CACf,KAAM,CAAAgB,IAAI,CAAG,KAAM,CAAAnB,QAAQ,CAACI,IAAI,CAAC,CAAC,CAClC,GAAIe,IAAI,CAACG,UAAU,EAAIH,IAAI,CAACG,UAAU,GAAKC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAE,CACjEF,MAAM,CAACC,QAAQ,CAACE,IAAI,CAAGP,IAAI,CAACG,UAAU,CACtC,OACF,CACF,CAEA;AACAC,MAAM,CAACC,QAAQ,CAACG,MAAM,CAAC,CAAC,CAC1B,CAAE,MAAO/B,KAAK,CAAE,CACdU,OAAO,CAACV,KAAK,CAAC,gBAAgB,CAAEA,KAAK,CAAC,CACtCL,YAAY,CAACO,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACZD,IAAI,MACPL,SAAS,CAAE,KAAK,CAChBG,KAAK,CAAE,eAAe,EACtB,CAAC,CACL,CACF,CAAC,CAED;AACA,KAAM,CAAAgC,eAAe,CAAG,KAAAA,CAAOC,MAAc,CAAEC,QAAgB,GAAuB,CACpF,GAAI,KAAAC,cAAA,CACF,KAAM,CAAA/B,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,gBAAgB,CAAE,CAC7CW,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDX,WAAW,CAAE,SAAS,CACtBY,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CAAEa,MAAM,CAAEC,QAAS,CAAC,CAC3C,CAAC,CAAC,CAEF,GAAI,CAAC9B,QAAQ,CAACG,EAAE,CAAE,CAChB,MAAO,MAAK,CACd,CAEA,KAAM,CAAAgB,IAAI,CAAG,KAAM,CAAAnB,QAAQ,CAACI,IAAI,CAAC,CAAC,CAClC,MAAO,EAAA2B,cAAA,CAAAZ,IAAI,CAACa,QAAQ,UAAAD,cAAA,iBAAbA,cAAA,CAAeE,OAAO,GAAI,KAAK,CACxC,CAAE,MAAOrC,KAAK,CAAE,CACdU,OAAO,CAACV,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChD,MAAO,MAAK,CACd,CACF,CAAC,CAED;AACA,KAAM,CAAAS,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CACF,KAAM,CAAAL,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,kBAAkB,CAAE,CAC/CW,MAAM,CAAE,MAAM,CACdV,WAAW,CAAE,SACf,CAAC,CAAC,CAEF,GAAIF,QAAQ,CAACG,EAAE,CAAE,CACf,KAAM,CAAA+B,SAAS,CAAG,KAAM,CAAAlC,QAAQ,CAACI,IAAI,CAAC,CAAC,CAEvC;AACA,KAAM,CAAA+B,YAAY,CAAG,KAAM,CAAAlC,KAAK,CAAC,eAAe,CAAE,CAChDC,WAAW,CAAE,SACf,CAAC,CAAC,CAEF,GAAIiC,YAAY,CAAChC,EAAE,CAAE,CACnB,KAAM,CAAAT,IAAI,CAAG,KAAM,CAAAyC,YAAY,CAAC/B,IAAI,CAAC,CAAC,CACtCb,YAAY,CAACO,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACZD,IAAI,MACPN,eAAe,CAAE,IAAI,CACrBE,IAAI,CACJC,KAAK,CAAEuC,SAAS,CAChBzC,SAAS,CAAE,KAAK,CAChBG,KAAK,CAAE,IAAI,EACX,CAAC,CACL,CACF,CAAC,IAAM,CACL,KAAM,IAAI,CAAAsB,KAAK,CAAC,sBAAsB,CAAC,CACzC,CACF,CAAE,MAAOtB,KAAK,CAAE,CACdU,OAAO,CAACV,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7CL,YAAY,CAACO,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACZD,IAAI,MACPN,eAAe,CAAE,KAAK,CACtBC,SAAS,CAAE,KAAK,CAChBC,IAAI,CAAE,IAAI,CACVC,KAAK,CAAE,IAAI,CACXC,KAAK,CAAE,iBAAiB,EACxB,CAAC,CACL,CACF,CAAC,CAED;AACAjB,SAAS,CAAC,IAAM,CACd,GAAIW,SAAS,CAACK,KAAK,EAAIL,SAAS,CAACE,eAAe,CAAE,CAChD,KAAM,CAAA4C,eAAe,CAAGC,WAAW,CAAC,IAAM,CACxChC,YAAY,CAAC,CAAC,CAChB,CAAC,CAAE,EAAE,CAAG,EAAE,CAAG,IAAI,CAAC,CAAE;AAEpB,MAAO,IAAMiC,aAAa,CAACF,eAAe,CAAC,CAC7C,CACF,CAAC,CAAE,CAAC9C,SAAS,CAACK,KAAK,CAAEL,SAAS,CAACE,eAAe,CAAC,CAAC,CAEhD,KAAM,CAAA+C,YAA6B,CAAAxC,aAAA,CAAAA,aAAA,IAC9BT,SAAS,MACZiB,KAAK,CACLc,MAAM,CACNO,eAAe,CACfvB,YAAY,EACb,CAED,mBACEvB,IAAA,CAACG,WAAW,CAACuD,QAAQ,EAACC,KAAK,CAAEF,YAAa,CAAAlD,QAAA,CACvCA,QAAQ,CACW,CAAC,CAE3B,CAAC,CAED;AACA,MAAO,MAAM,CAAAqD,OAAO,CAAGA,CAAA,GAAuB,CAC5C,KAAM,CAAAC,OAAO,CAAGjE,UAAU,CAACO,WAAW,CAAC,CACvC,GAAI0D,OAAO,GAAKzD,SAAS,CAAE,CACzB,KAAM,IAAI,CAAAgC,KAAK,CAAC,6CAA6C,CAAC,CAChE,CACA,MAAO,CAAAyB,OAAO,CAChB,CAAC,CAED;AAOA,MAAO,MAAM,CAAAC,cAA6C,CAAGC,KAAA,EAIvD,IAJwD,CAC5DxD,QAAQ,CACRyD,kBAAkB,CAClBC,QAAQ,cAAGjE,IAAA,QAAKkE,SAAS,CAAC,kBAAkB,CAAA3D,QAAA,CAAC,eAAa,CAAK,CACjE,CAAC,CAAAwD,KAAA,CACC,KAAM,CAAErD,eAAe,CAAEC,SAAS,CAAEmC,eAAgB,CAAC,CAAGc,OAAO,CAAC,CAAC,CACjE,KAAM,CAACO,aAAa,CAAEC,gBAAgB,CAAC,CAAGtE,QAAQ,CAAiB,IAAI,CAAC,CAExED,SAAS,CAAC,IAAM,CACd,GAAIa,eAAe,EAAIsD,kBAAkB,CAAE,CACzClB,eAAe,CAACkB,kBAAkB,CAACjB,MAAM,CAAEiB,kBAAkB,CAAChB,QAAQ,CAAC,CACpEqB,IAAI,CAACD,gBAAgB,CAAC,CAC3B,CAAC,IAAM,IAAI1D,eAAe,CAAE,CAC1B0D,gBAAgB,CAAC,IAAI,CAAC,CACxB,CACF,CAAC,CAAE,CAAC1D,eAAe,CAAEsD,kBAAkB,CAAElB,eAAe,CAAC,CAAC,CAE1D,GAAInC,SAAS,CAAE,CACb,mBAAOX,IAAA,QAAKkE,SAAS,CAAC,KAAK,CAAA3D,QAAA,CAAC,YAAU,CAAK,CAAC,CAC9C,CAEA,GAAI,CAACG,eAAe,CAAE,CACpB,mBAAOV,IAAA,QAAKkE,SAAS,CAAC,KAAK,CAAA3D,QAAA,CAAC,oCAAkC,CAAK,CAAC,CACtE,CAEA,GAAIyD,kBAAkB,EAAIG,aAAa,GAAK,KAAK,CAAE,CACjD,mBAAOnE,IAAA,CAAAE,SAAA,EAAAK,QAAA,CAAG0D,QAAQ,CAAG,CAAC,CACxB,CAEA,GAAID,kBAAkB,EAAIG,aAAa,GAAK,IAAI,CAAE,CAChD,mBAAOnE,IAAA,QAAKkE,SAAS,CAAC,KAAK,CAAA3D,QAAA,CAAC,yBAAuB,CAAK,CAAC,CAC3D,CAEA,mBAAOP,IAAA,CAAAE,SAAA,EAAAK,QAAA,CAAGA,QAAQ,CAAG,CAAC,CACxB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}