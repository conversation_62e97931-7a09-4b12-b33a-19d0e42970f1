{"ast": null, "code": "import { ReactReduxContext } from '../components/Context';\nimport { useReduxContext as useDefaultReduxContext, createReduxContextHook } from './useReduxContext';\n/**\r\n * Hook factory, which creates a `useStore` hook bound to a given context.\r\n *\r\n * @param {React.Context} [context=ReactReduxContext] Context passed to your `<Provider>`.\r\n * @returns {Function} A `useStore` hook bound to the specified context.\r\n */\n\nexport function createStoreHook() {\n  let context = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : ReactReduxContext;\n  const useReduxContext =\n  // @ts-ignore\n  context === ReactReduxContext ? useDefaultReduxContext :\n  // @ts-ignore\n  createReduxContextHook(context);\n  return function useStore() {\n    const {\n      store\n    } = useReduxContext(); // @ts-ignore\n\n    return store;\n  };\n}\n/**\r\n * A hook to access the redux store.\r\n *\r\n * @returns {any} the redux store\r\n *\r\n * @example\r\n *\r\n * import React from 'react'\r\n * import { useStore } from 'react-redux'\r\n *\r\n * export const ExampleComponent = () => {\r\n *   const store = useStore()\r\n *   return <div>{store.getState()}</div>\r\n * }\r\n */\n\nexport const useStore = /*#__PURE__*/createStoreHook();", "map": {"version": 3, "names": ["ReactReduxContext", "useReduxContext", "useDefaultReduxContext", "createReduxContextHook", "createStoreHook", "context", "arguments", "length", "undefined", "useStore", "store"], "sources": ["/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/node_modules/react-redux/es/hooks/useStore.js"], "sourcesContent": ["import { ReactReduxContext } from '../components/Context';\nimport { useReduxContext as useDefaultReduxContext, createReduxContextHook } from './useReduxContext';\n/**\r\n * Hook factory, which creates a `useStore` hook bound to a given context.\r\n *\r\n * @param {React.Context} [context=ReactReduxContext] Context passed to your `<Provider>`.\r\n * @returns {Function} A `useStore` hook bound to the specified context.\r\n */\n\nexport function createStoreHook(context = ReactReduxContext) {\n  const useReduxContext = // @ts-ignore\n  context === ReactReduxContext ? useDefaultReduxContext : // @ts-ignore\n  createReduxContextHook(context);\n  return function useStore() {\n    const {\n      store\n    } = useReduxContext(); // @ts-ignore\n\n    return store;\n  };\n}\n/**\r\n * A hook to access the redux store.\r\n *\r\n * @returns {any} the redux store\r\n *\r\n * @example\r\n *\r\n * import React from 'react'\r\n * import { useStore } from 'react-redux'\r\n *\r\n * export const ExampleComponent = () => {\r\n *   const store = useStore()\r\n *   return <div>{store.getState()}</div>\r\n * }\r\n */\n\nexport const useStore = /*#__PURE__*/createStoreHook();"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,eAAe,IAAIC,sBAAsB,EAAEC,sBAAsB,QAAQ,mBAAmB;AACrG;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,eAAeA,CAAA,EAA8B;EAAA,IAA7BC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGN,iBAAiB;EACzD,MAAMC,eAAe;EAAG;EACxBI,OAAO,KAAKL,iBAAiB,GAAGE,sBAAsB;EAAG;EACzDC,sBAAsB,CAACE,OAAO,CAAC;EAC/B,OAAO,SAASI,QAAQA,CAAA,EAAG;IACzB,MAAM;MACJC;IACF,CAAC,GAAGT,eAAe,CAAC,CAAC,CAAC,CAAC;;IAEvB,OAAOS,KAAK;EACd,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,MAAMD,QAAQ,GAAG,aAAaL,eAAe,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}