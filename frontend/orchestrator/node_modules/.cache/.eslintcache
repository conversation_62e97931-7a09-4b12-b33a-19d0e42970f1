[{"/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/index.tsx": "1", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/reportWebVitals.ts": "2", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/store.ts": "3", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/App.tsx": "4", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/searchSlice.ts": "5", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/workflowSlice.ts": "6", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/envoySlice.ts": "7", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/auditSlice.ts": "8", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/autoscalerSlice.ts": "9", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/databaseSlice.ts": "10", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/components/Layout.tsx": "11", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/SearchDashboard.tsx": "12", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/Dashboard.tsx": "13", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/WorkflowExecutor.tsx": "14", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/AutoscalerDashboard.tsx": "15", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/DiscoveryWizard.tsx": "16", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/EnvoyConfigEditor.tsx": "17", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/AuditViewer.tsx": "18", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/DatabaseAdmin.tsx": "19", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/auth/AuthContext.tsx": "20", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/auth/LoginPage.tsx": "21", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/AdminDashboard.tsx": "22", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/UserManagement.tsx": "23", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/UserProfile.tsx": "24", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/Settings.tsx": "25", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/RBACAdmin.tsx": "26", "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/FeatureFlagAdmin.tsx": "27"}, {"size": 778, "mtime": 1749058599021, "results": "28", "hashOfConfig": "29"}, {"size": 370, "mtime": 1749058755887, "results": "30", "hashOfConfig": "29"}, {"size": 863, "mtime": 1749063617349, "results": "31", "hashOfConfig": "29"}, {"size": 4948, "mtime": 1749097025918, "results": "32", "hashOfConfig": "29"}, {"size": 2725, "mtime": 1749063531345, "results": "33", "hashOfConfig": "29"}, {"size": 3481, "mtime": 1749063548264, "results": "34", "hashOfConfig": "29"}, {"size": 2849, "mtime": 1749063582338, "results": "35", "hashOfConfig": "29"}, {"size": 1957, "mtime": 1749063595368, "results": "36", "hashOfConfig": "29"}, {"size": 3681, "mtime": 1749063567934, "results": "37", "hashOfConfig": "29"}, {"size": 2204, "mtime": 1749063606689, "results": "38", "hashOfConfig": "29"}, {"size": 9745, "mtime": 1749097040570, "results": "39", "hashOfConfig": "29"}, {"size": 8594, "mtime": 1749063749914, "results": "40", "hashOfConfig": "29"}, {"size": 12377, "mtime": 1749092192506, "results": "41", "hashOfConfig": "29"}, {"size": 10204, "mtime": 1749063787790, "results": "42", "hashOfConfig": "29"}, {"size": 9190, "mtime": 1749063823453, "results": "43", "hashOfConfig": "29"}, {"size": 17002, "mtime": 1749064442452, "results": "44", "hashOfConfig": "29"}, {"size": 11841, "mtime": 1749064586885, "results": "45", "hashOfConfig": "29"}, {"size": 9026, "mtime": 1749063897136, "results": "46", "hashOfConfig": "29"}, {"size": 8828, "mtime": 1749063931077, "results": "47", "hashOfConfig": "29"}, {"size": 10592, "mtime": 1749089058339, "results": "48", "hashOfConfig": "29"}, {"size": 6612, "mtime": 1749087527402, "results": "49", "hashOfConfig": "29"}, {"size": 12215, "mtime": 1749097173645, "results": "50", "hashOfConfig": "29"}, {"size": 21378, "mtime": 1749090687306, "results": "51", "hashOfConfig": "29"}, {"size": 14536, "mtime": 1749090017788, "results": "52", "hashOfConfig": "29"}, {"size": 18823, "mtime": 1749090144932, "results": "53", "hashOfConfig": "29"}, {"size": 19776, "mtime": 1749097000809, "results": "54", "hashOfConfig": "29"}, {"size": 16497, "mtime": 1749096791670, "results": "55", "hashOfConfig": "29"}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ojadns", {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/index.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/reportWebVitals.ts", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/store.ts", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/App.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/searchSlice.ts", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/workflowSlice.ts", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/envoySlice.ts", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/auditSlice.ts", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/autoscalerSlice.ts", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/store/slices/databaseSlice.ts", ["137"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/components/Layout.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/SearchDashboard.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/Dashboard.tsx", ["138"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/WorkflowExecutor.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/AutoscalerDashboard.tsx", ["139"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/DiscoveryWizard.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/EnvoyConfigEditor.tsx", ["140"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/AuditViewer.tsx", ["141"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/DatabaseAdmin.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/auth/AuthContext.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/auth/LoginPage.tsx", [], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/AdminDashboard.tsx", ["142", "143", "144"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/UserManagement.tsx", ["145"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/UserProfile.tsx", ["146"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/Settings.tsx", ["147", "148", "149", "150"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/RBACAdmin.tsx", ["151", "152", "153", "154", "155", "156", "157"], [], "/Users/<USER>/Desktop/CAINuro_Orchestrator/frontend/orchestrator/src/pages/FeatureFlagAdmin.tsx", ["158", "159", "160", "161", "162"], [], {"ruleId": "163", "severity": 1, "message": "164", "line": 1, "column": 41, "nodeType": "165", "messageId": "166", "endLine": 1, "endColumn": 54}, {"ruleId": "163", "severity": 1, "message": "167", "line": 5, "column": 3, "nodeType": "165", "messageId": "166", "endLine": 5, "endColumn": 15}, {"ruleId": "163", "severity": 1, "message": "168", "line": 18, "column": 43, "nodeType": "165", "messageId": "166", "endLine": 18, "endColumn": 50}, {"ruleId": "163", "severity": 1, "message": "169", "line": 19, "column": 52, "nodeType": "165", "messageId": "166", "endLine": 19, "endColumn": 57}, {"ruleId": "163", "severity": 1, "message": "170", "line": 14, "column": 47, "nodeType": "165", "messageId": "166", "endLine": 14, "endColumn": 52}, {"ruleId": "163", "severity": 1, "message": "171", "line": 10, "column": 3, "nodeType": "165", "messageId": "166", "endLine": 10, "endColumn": 26}, {"ruleId": "163", "severity": 1, "message": "172", "line": 13, "column": 3, "nodeType": "165", "messageId": "166", "endLine": 13, "endColumn": 10}, {"ruleId": "163", "severity": 1, "message": "173", "line": 14, "column": 3, "nodeType": "165", "messageId": "166", "endLine": 14, "endColumn": 18}, {"ruleId": "163", "severity": 1, "message": "174", "line": 4, "column": 3, "nodeType": "165", "messageId": "166", "endLine": 4, "endColumn": 12}, {"ruleId": "163", "severity": 1, "message": "175", "line": 8, "column": 3, "nodeType": "165", "messageId": "166", "endLine": 8, "endColumn": 12}, {"ruleId": "163", "severity": 1, "message": "173", "line": 8, "column": 3, "nodeType": "165", "messageId": "166", "endLine": 8, "endColumn": 18}, {"ruleId": "163", "severity": 1, "message": "176", "line": 9, "column": 3, "nodeType": "165", "messageId": "166", "endLine": 9, "endColumn": 12}, {"ruleId": "163", "severity": 1, "message": "177", "line": 42, "column": 11, "nodeType": "165", "messageId": "166", "endLine": 42, "endColumn": 15}, {"ruleId": "178", "severity": 1, "message": "179", "line": 74, "column": 6, "nodeType": "180", "endLine": 74, "endColumn": 8, "suggestions": "181"}, {"ruleId": "163", "severity": 1, "message": "172", "line": 10, "column": 3, "nodeType": "165", "messageId": "166", "endLine": 10, "endColumn": 10}, {"ruleId": "163", "severity": 1, "message": "177", "line": 49, "column": 11, "nodeType": "165", "messageId": "166", "endLine": 49, "endColumn": 15}, {"ruleId": "163", "severity": 1, "message": "182", "line": 56, "column": 10, "nodeType": "165", "messageId": "166", "endLine": 56, "endColumn": 25}, {"ruleId": "163", "severity": 1, "message": "183", "line": 57, "column": 10, "nodeType": "165", "messageId": "166", "endLine": 57, "endColumn": 23}, {"ruleId": "178", "severity": 1, "message": "184", "line": 63, "column": 6, "nodeType": "180", "endLine": 63, "endColumn": 17, "suggestions": "185"}, {"ruleId": "163", "severity": 1, "message": "186", "line": 128, "column": 9, "nodeType": "165", "messageId": "166", "endLine": 128, "endColumn": 21}, {"ruleId": "163", "severity": 1, "message": "187", "line": 148, "column": 9, "nodeType": "165", "messageId": "166", "endLine": 148, "endColumn": 21}, {"ruleId": "163", "severity": 1, "message": "188", "line": 4, "column": 3, "nodeType": "165", "messageId": "166", "endLine": 4, "endColumn": 11}, {"ruleId": "163", "severity": 1, "message": "172", "line": 8, "column": 3, "nodeType": "165", "messageId": "166", "endLine": 8, "endColumn": 10}, {"ruleId": "163", "severity": 1, "message": "189", "line": 12, "column": 3, "nodeType": "165", "messageId": "166", "endLine": 12, "endColumn": 10}, {"ruleId": "163", "severity": 1, "message": "177", "line": 41, "column": 11, "nodeType": "165", "messageId": "166", "endLine": 41, "endColumn": 15}, {"ruleId": "163", "severity": 1, "message": "183", "line": 46, "column": 10, "nodeType": "165", "messageId": "166", "endLine": 46, "endColumn": 23}, "@typescript-eslint/no-unused-vars", "'PayloadAction' is defined but never used.", "Identifier", "unusedVar", "'ChartBarIcon' is defined but never used.", "'metrics' is assigned a value but never used.", "'error' is assigned a value but never used.", "'query' is assigned a value but never used.", "'ExclamationTriangleIcon' is defined but never used.", "'EyeIcon' is defined but never used.", "'CircleStackIcon' is defined but never used.", "'UsersIcon' is defined but never used.", "'ClockIcon' is defined but never used.", "'CloudIcon' is defined but never used.", "'user' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchSettings'. Either include it or remove the dependency array.", "ArrayExpression", ["190"], "'showCreateModal' is assigned a value but never used.", "'showEditModal' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["191"], "'handleCreate' is assigned a value but never used.", "'handleUpdate' is assigned a value but never used.", "'FlagIcon' is defined but never used.", "'CogIcon' is defined but never used.", {"desc": "192", "fix": "193"}, {"desc": "194", "fix": "195"}, "Update the dependencies array to be: [fetchSettings]", {"range": "196", "text": "197"}, "Update the dependencies array to be: [activeTab, fetchData]", {"range": "198", "text": "199"}, [1787, 1789], "[fetchSettings]", [1579, 1590], "[activeTab, fetchData]"]