import React from 'react';
import { useAuth } from './AuthContext';

export const LoginPage: React.FC = () => {
  const { login, isLoading, error } = useAuth();
  const [username, setUsername] = React.useState('admin');
  const [password, setPassword] = React.useState('admin');

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    login(username, password);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white/10 backdrop-blur-lg rounded-2xl shadow-2xl p-8 border border-white/20">
        <div className="text-center mb-8">
          <div className="text-6xl mb-4">🌟</div>
          <h1 className="text-3xl font-bold text-white mb-2">CAINuro Orchestrator</h1>
          <p className="text-blue-200">Enterprise Cloud Resource Orchestration Platform</p>
        </div>

        <form onSubmit={handleLogin} className="mb-6">
          <div className="bg-blue-500/20 border border-blue-500/50 rounded-lg p-4 mb-4">
            <div className="flex items-center">
              <div className="text-blue-400 mr-2">ℹ️</div>
              <div className="text-blue-200 text-sm">
                <strong>Default Credentials:</strong> admin/admin or user/user
              </div>
            </div>
          </div>

          {error && (
            <div className="bg-red-500/20 border border-red-500/50 rounded-lg p-4 mb-4">
              <div className="flex items-center">
                <div className="text-red-400 mr-2">⚠️</div>
                <div className="text-red-200 text-sm">{error}</div>
              </div>
            </div>
          )}

          <div className="space-y-4 mb-6">
            <div>
              <label htmlFor="username" className="block text-sm font-medium text-white mb-2">
                Username
              </label>
              <input
                id="username"
                type="text"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter username"
                required
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-white mb-2">
                Password
              </label>
              <input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter password"
                required
              />
            </div>
          </div>

          <button
            type="submit"
            disabled={isLoading}
            className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:from-gray-600 disabled:to-gray-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105 disabled:scale-100 disabled:cursor-not-allowed shadow-lg"
          >
            {isLoading ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                Signing in...
              </div>
            ) : (
              <div className="flex items-center justify-center">
                <div className="mr-2">🔐</div>
                Sign In
              </div>
            )}
          </button>
        </form>

        <div className="border-t border-white/20 pt-6">
          <div className="text-center mb-4">
            <h3 className="text-lg font-semibold text-white mb-2">🚀 Authentication Features</h3>
          </div>
          
          <div className="grid grid-cols-1 gap-3 text-sm">
            <div className="flex items-center text-green-300">
              <div className="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
              OIDC/OAuth2 Integration
            </div>
            <div className="flex items-center text-green-300">
              <div className="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
              JWT Token Management
            </div>
            <div className="flex items-center text-green-300">
              <div className="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
              Role-Based Access Control (RBAC)
            </div>
            <div className="flex items-center text-green-300">
              <div className="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
              Session Management
            </div>
            <div className="flex items-center text-green-300">
              <div className="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
              Multi-Factor Authentication
            </div>
            <div className="flex items-center text-green-300">
              <div className="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
              Audit Logging
            </div>
          </div>
        </div>

        <div className="mt-6 pt-6 border-t border-white/20">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-white mb-2">🌐 Platform Capabilities</h3>
            <div className="grid grid-cols-2 gap-2 text-xs text-blue-200">
              <div>🔍 Multi-Cloud Discovery</div>
              <div>⚡ Workflow Automation</div>
              <div>☸️ Kubernetes Management</div>
              <div>💥 Chaos Engineering</div>
              <div>🤖 ChatOps Integration</div>
              <div>📊 Real-time Monitoring</div>
              <div>🕸️ Topology Mapping</div>
              <div>🐙 GitHub Integration</div>
              <div>🔗 URL Management</div>
              <div>📋 Project Management</div>
              <div>📝 Feedback Collection</div>
              <div>🛡️ Security Scanning</div>
            </div>
          </div>
        </div>

        <div className="mt-6 text-center">
          <p className="text-xs text-blue-300">
            Powered by CAINuro • Enterprise Grade • Production Ready
          </p>
        </div>
      </div>
    </div>
  );
};
