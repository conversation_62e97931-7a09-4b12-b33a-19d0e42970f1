<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="CAINuro Orchestrator - Cloud Infrastructure Management" />
    <title>CAINuro Orchestrator</title>
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
                'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
                sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            background-color: #111827;
            color: white;
        }
        
        .loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
        }
        
        .logo {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #06b6d4, #3b82f6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #374151;
            border-top: 4px solid #06b6d4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 1rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text {
            color: #9ca3af;
            font-size: 1rem;
        }
        
        .features {
            margin-top: 2rem;
            text-align: center;
            max-width: 600px;
        }
        
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .feature {
            padding: 0.5rem;
            background: rgba(55, 65, 81, 0.5);
            border-radius: 0.5rem;
            font-size: 0.875rem;
            color: #d1d5db;
        }
    </style>
</head>
<body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root">
        <div class="loading-container">
            <div class="logo">🚀 CAINuro Orchestrator</div>
            <div class="loading-spinner"></div>
            <div class="loading-text">Loading YOLO Mode...</div>
            
            <div class="features">
                <div class="feature-list">
                    <div class="feature">🔍 Multi-Cloud Discovery</div>
                    <div class="feature">⚡ Workflow Automation</div>
                    <div class="feature">🌐 Envoy Control Plane</div>
                    <div class="feature">📈 Auto Scaling</div>
                    <div class="feature">🛡️ Tamper-Proof Audit</div>
                    <div class="feature">💾 Embedded Database</div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Simple loading simulation
        setTimeout(() => {
            document.querySelector('.loading-text').textContent = 'Initializing services...';
        }, 1000);
        
        setTimeout(() => {
            document.querySelector('.loading-text').textContent = 'Starting React app...';
        }, 2000);
        
        // In a real build, React would replace this content
        setTimeout(() => {
            document.querySelector('#root').innerHTML = `
                <div class="loading-container">
                    <div class="logo">🚀 CAINuro Orchestrator</div>
                    <div style="color: #10b981; margin-bottom: 1rem;">✅ System Ready</div>
                    <div style="color: #9ca3af; text-align: center;">
                        <p>React frontend is not built yet.</p>
                        <p>Run <code style="background: #374151; padding: 0.25rem 0.5rem; border-radius: 0.25rem;">npm run build</code> in the frontend directory.</p>
                        <p style="margin-top: 1rem;">
                            <a href="/v1/autoscaler/status" style="color: #06b6d4;">Test API →</a>
                        </p>
                    </div>
                </div>
            `;
        }, 3000);
    </script>
</body>
</html>
