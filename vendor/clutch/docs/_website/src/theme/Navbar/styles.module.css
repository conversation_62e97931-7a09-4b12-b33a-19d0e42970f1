/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

@media screen and (max-width: 997px) {
  .displayOnlyInLargeViewport {
    display: none !important;
  }
}

.navbarHideable {
  transition: top 0.2s ease-in-out;
}

/* Customization */
.navbarItemIcon {
  margin-right: 0.33rem;
}

.navbarItemLabel {
  font-weight: 700;
  width: 100%;
  text-align: left;
}

.navbarCustom {
  font-size: 1.1rem;
  height: fit-content;
}

.navbarLogoCustom {
  height: 3rem;
}

.navbarLogoTextCustom {
  height: 1.5rem;
  margin-left: 0.33rem;
}
