package resolver

import (
	"context"
	"fmt"
	"regexp"
	"sort"
	"strings"
	"time"

	"go.uber.org/zap"
)

// AdvancedResolver implements sophisticated search and resolution capabilities
type AdvancedResolver struct {
	logger    *zap.Logger
	providers map[string]Provider
	schemas   map[string]*Schema
}

// Provider interface for different cloud providers
type Provider interface {
	Name() string
	Regions() []string
	Search(ctx context.Context, req *SearchRequest) (*SearchResults, error)
	Resolve(ctx context.Context, req *ResolveRequest) (*ResolveResults, error)
	Autocomplete(ctx context.Context, req *AutocompleteRequest) (*AutocompleteResults, error)
	GetSchema(resourceType string) (*Schema, error)
}

// SearchRequest represents an advanced search request
type SearchRequest struct {
	Query        string            `json:"query"`
	ResourceType string            `json:"resource_type,omitempty"`
	Provider     string            `json:"provider,omitempty"`
	Region       string            `json:"region,omitempty"`
	Account      string            `json:"account,omitempty"`
	Tags         map[string]string `json:"tags,omitempty"`
	Filters      map[string]string `json:"filters,omitempty"`
	Limit        uint32            `json:"limit,omitempty"`
	Offset       uint32            `json:"offset,omitempty"`
	SortBy       string            `json:"sort_by,omitempty"`
	SortOrder    string            `json:"sort_order,omitempty"`
}

// ResolveRequest represents a resource resolution request
type ResolveRequest struct {
	ResourceType string                 `json:"resource_type"`
	Input        map[string]interface{} `json:"input"`
	Provider     string                 `json:"provider,omitempty"`
	Limit        uint32                 `json:"limit,omitempty"`
}

// AutocompleteRequest represents an autocomplete request
type AutocompleteRequest struct {
	Query         string `json:"query"`
	ResourceType  string `json:"resource_type,omitempty"`
	Provider      string `json:"provider,omitempty"`
	Limit         uint32 `json:"limit,omitempty"`
	CaseSensitive bool   `json:"case_sensitive,omitempty"`
}

// SearchResults represents search results
type SearchResults struct {
	Resources       []Resource        `json:"resources"`
	TotalCount      int               `json:"total_count"`
	HasMore         bool              `json:"has_more"`
	SearchTime      time.Duration     `json:"search_time"`
	PartialFailures []PartialFailure  `json:"partial_failures,omitempty"`
	Facets          map[string][]Facet `json:"facets,omitempty"`
}

// ResolveResults represents resolution results
type ResolveResults struct {
	Resources       []Resource       `json:"resources"`
	PartialFailures []PartialFailure `json:"partial_failures,omitempty"`
}

// AutocompleteResults represents autocomplete results
type AutocompleteResults struct {
	Suggestions []AutocompleteSuggestion `json:"suggestions"`
}

// Resource represents a cloud resource
type Resource struct {
	ID           string                 `json:"id"`
	Name         string                 `json:"name"`
	Type         string                 `json:"type"`
	Provider     string                 `json:"provider"`
	Region       string                 `json:"region"`
	Account      string                 `json:"account,omitempty"`
	Status       string                 `json:"status"`
	Tags         map[string]string      `json:"tags,omitempty"`
	Metadata     map[string]interface{} `json:"metadata,omitempty"`
	CreatedAt    time.Time              `json:"created_at"`
	UpdatedAt    time.Time              `json:"updated_at"`
	Score        float64                `json:"score,omitempty"`
}

// PartialFailure represents a partial failure during search/resolution
type PartialFailure struct {
	Provider string `json:"provider"`
	Region   string `json:"region,omitempty"`
	Error    string `json:"error"`
}

// AutocompleteSuggestion represents an autocomplete suggestion
type AutocompleteSuggestion struct {
	ID       string  `json:"id"`
	Label    string  `json:"label"`
	Type     string  `json:"type"`
	Provider string  `json:"provider"`
	Score    float64 `json:"score"`
}

// Facet represents a search facet
type Facet struct {
	Value string `json:"value"`
	Count int    `json:"count"`
}

// Schema represents a resource schema
type Schema struct {
	TypeURL     string  `json:"type_url"`
	DisplayName string  `json:"display_name"`
	Fields      []Field `json:"fields"`
}

// Field represents a schema field
type Field struct {
	Name        string      `json:"name"`
	DisplayName string      `json:"display_name"`
	Type        string      `json:"type"`
	Required    bool        `json:"required"`
	Searchable  bool        `json:"searchable"`
	Options     []Option    `json:"options,omitempty"`
}

// Option represents a field option
type Option struct {
	Value       string `json:"value"`
	DisplayName string `json:"display_name"`
}

// NewAdvancedResolver creates a new advanced resolver
func NewAdvancedResolver(logger *zap.Logger) *AdvancedResolver {
	resolver := &AdvancedResolver{
		logger:    logger,
		providers: make(map[string]Provider),
		schemas:   make(map[string]*Schema),
	}

	// Initialize with mock providers
	resolver.initializeMockProviders()
	resolver.initializeSchemas()

	return resolver
}

// RegisterProvider registers a new provider
func (r *AdvancedResolver) RegisterProvider(provider Provider) {
	r.providers[provider.Name()] = provider
	r.logger.Info("Provider registered", zap.String("provider", provider.Name()))
}

// Search performs an advanced search across providers
func (r *AdvancedResolver) Search(ctx context.Context, req *SearchRequest) (*SearchResults, error) {
	startTime := time.Now()

	r.logger.Info("Advanced search started",
		zap.String("query", req.Query),
		zap.String("resource_type", req.ResourceType),
		zap.String("provider", req.Provider))

	// Determine which providers to search
	providers := r.getProvidersForSearch(req.Provider)
	
	// Perform parallel search across providers
	results := &SearchResults{
		Resources:       []Resource{},
		PartialFailures: []PartialFailure{},
		Facets:          make(map[string][]Facet),
	}

	// Search each provider
	for _, provider := range providers {
		providerResults, err := provider.Search(ctx, req)
		if err != nil {
			results.PartialFailures = append(results.PartialFailures, PartialFailure{
				Provider: provider.Name(),
				Error:    err.Error(),
			})
			continue
		}

		// Merge results
		results.Resources = append(results.Resources, providerResults.Resources...)
		results.PartialFailures = append(results.PartialFailures, providerResults.PartialFailures...)
		
		// Merge facets
		for facetName, facetValues := range providerResults.Facets {
			results.Facets[facetName] = append(results.Facets[facetName], facetValues...)
		}
	}

	// Apply post-processing
	r.postProcessSearchResults(results, req)

	results.SearchTime = time.Since(startTime)
	results.TotalCount = len(results.Resources)

	r.logger.Info("Advanced search completed",
		zap.Int("results", len(results.Resources)),
		zap.Duration("duration", results.SearchTime),
		zap.Int("partial_failures", len(results.PartialFailures)))

	return results, nil
}

// Resolve resolves resources based on input criteria
func (r *AdvancedResolver) Resolve(ctx context.Context, req *ResolveRequest) (*ResolveResults, error) {
	r.logger.Info("Resource resolution started",
		zap.String("resource_type", req.ResourceType),
		zap.String("provider", req.Provider))

	// Determine which providers to use
	providers := r.getProvidersForSearch(req.Provider)

	results := &ResolveResults{
		Resources:       []Resource{},
		PartialFailures: []PartialFailure{},
	}

	// Resolve with each provider
	for _, provider := range providers {
		providerResults, err := provider.Resolve(ctx, req)
		if err != nil {
			results.PartialFailures = append(results.PartialFailures, PartialFailure{
				Provider: provider.Name(),
				Error:    err.Error(),
			})
			continue
		}

		results.Resources = append(results.Resources, providerResults.Resources...)
		results.PartialFailures = append(results.PartialFailures, providerResults.PartialFailures...)
	}

	r.logger.Info("Resource resolution completed",
		zap.Int("results", len(results.Resources)),
		zap.Int("partial_failures", len(results.PartialFailures)))

	return results, nil
}

// Autocomplete provides intelligent autocomplete suggestions
func (r *AdvancedResolver) Autocomplete(ctx context.Context, req *AutocompleteRequest) (*AutocompleteResults, error) {
	if len(req.Query) < 2 {
		return &AutocompleteResults{Suggestions: []AutocompleteSuggestion{}}, nil
	}

	r.logger.Debug("Autocomplete started", zap.String("query", req.Query))

	// Determine which providers to use
	providers := r.getProvidersForSearch(req.Provider)

	var allSuggestions []AutocompleteSuggestion

	// Get suggestions from each provider
	for _, provider := range providers {
		providerResults, err := provider.Autocomplete(ctx, req)
		if err != nil {
			r.logger.Warn("Provider autocomplete failed",
				zap.String("provider", provider.Name()),
				zap.Error(err))
			continue
		}

		allSuggestions = append(allSuggestions, providerResults.Suggestions...)
	}

	// Sort by score and apply limit
	sort.Slice(allSuggestions, func(i, j int) bool {
		return allSuggestions[i].Score > allSuggestions[j].Score
	})

	limit := req.Limit
	if limit == 0 {
		limit = 10
	}

	if len(allSuggestions) > int(limit) {
		allSuggestions = allSuggestions[:limit]
	}

	r.logger.Debug("Autocomplete completed",
		zap.Int("suggestions", len(allSuggestions)))

	return &AutocompleteResults{Suggestions: allSuggestions}, nil
}

// GetSchemas returns all available schemas
func (r *AdvancedResolver) GetSchemas(ctx context.Context) (map[string]*Schema, error) {
	return r.schemas, nil
}

// GetSchema returns a specific schema
func (r *AdvancedResolver) GetSchema(ctx context.Context, resourceType string) (*Schema, error) {
	schema, exists := r.schemas[resourceType]
	if !exists {
		return nil, fmt.Errorf("schema not found for resource type: %s", resourceType)
	}
	return schema, nil
}

// getProvidersForSearch determines which providers to search
func (r *AdvancedResolver) getProvidersForSearch(providerFilter string) []Provider {
	if providerFilter != "" {
		if provider, exists := r.providers[providerFilter]; exists {
			return []Provider{provider}
		}
		return []Provider{}
	}

	// Return all providers
	var providers []Provider
	for _, provider := range r.providers {
		providers = append(providers, provider)
	}
	return providers
}

// postProcessSearchResults applies post-processing to search results
func (r *AdvancedResolver) postProcessSearchResults(results *SearchResults, req *SearchRequest) {
	// Calculate relevance scores
	r.calculateRelevanceScores(results.Resources, req.Query)

	// Apply sorting
	r.applySorting(results.Resources, req.SortBy, req.SortOrder)

	// Apply pagination
	r.applyPagination(results, req.Limit, req.Offset)

	// Aggregate facets
	r.aggregateFacets(results.Facets)
}

// calculateRelevanceScores calculates relevance scores for resources
func (r *AdvancedResolver) calculateRelevanceScores(resources []Resource, query string) {
	if query == "" {
		return
	}

	query = strings.ToLower(query)

	for i := range resources {
		score := 0.0

		// Exact name match gets highest score
		if strings.ToLower(resources[i].Name) == query {
			score += 100
		} else if strings.Contains(strings.ToLower(resources[i].Name), query) {
			score += 50
		}

		// ID match
		if strings.Contains(strings.ToLower(resources[i].ID), query) {
			score += 40
		}

		// Type match
		if strings.Contains(strings.ToLower(resources[i].Type), query) {
			score += 30
		}

		// Tag matches
		for key, value := range resources[i].Tags {
			if strings.Contains(strings.ToLower(key), query) ||
			   strings.Contains(strings.ToLower(value), query) {
				score += 10
			}
		}

		resources[i].Score = score
	}
}

// applySorting applies sorting to resources
func (r *AdvancedResolver) applySorting(resources []Resource, sortBy, sortOrder string) {
	if sortBy == "" {
		sortBy = "score" // Default sort by relevance score
	}

	ascending := sortOrder != "desc"

	sort.Slice(resources, func(i, j int) bool {
		var less bool

		switch sortBy {
		case "name":
			less = resources[i].Name < resources[j].Name
		case "type":
			less = resources[i].Type < resources[j].Type
		case "provider":
			less = resources[i].Provider < resources[j].Provider
		case "region":
			less = resources[i].Region < resources[j].Region
		case "created_at":
			less = resources[i].CreatedAt.Before(resources[j].CreatedAt)
		case "updated_at":
			less = resources[i].UpdatedAt.Before(resources[j].UpdatedAt)
		case "score":
			less = resources[i].Score < resources[j].Score
		default:
			less = resources[i].Score > resources[j].Score // Default: highest score first
		}

		if !ascending {
			less = !less
		}

		return less
	})
}

// applyPagination applies pagination to results
func (r *AdvancedResolver) applyPagination(results *SearchResults, limit, offset uint32) {
	if limit == 0 {
		limit = 50 // Default limit
	}

	totalCount := len(results.Resources)
	
	// Apply offset
	if offset > 0 && int(offset) < totalCount {
		results.Resources = results.Resources[offset:]
	}

	// Apply limit
	if int(limit) < len(results.Resources) {
		results.Resources = results.Resources[:limit]
		results.HasMore = true
	}

	results.TotalCount = totalCount
}

// aggregateFacets aggregates facet counts
func (r *AdvancedResolver) aggregateFacets(facets map[string][]Facet) {
	for facetName, facetValues := range facets {
		// Count occurrences
		counts := make(map[string]int)
		for _, facet := range facetValues {
			counts[facet.Value] += facet.Count
		}

		// Convert back to facet list
		var aggregated []Facet
		for value, count := range counts {
			aggregated = append(aggregated, Facet{
				Value: value,
				Count: count,
			})
		}

		// Sort by count (descending)
		sort.Slice(aggregated, func(i, j int) bool {
			return aggregated[i].Count > aggregated[j].Count
		})

		facets[facetName] = aggregated
	}
}

// initializeMockProviders initializes mock providers for demonstration
func (r *AdvancedResolver) initializeMockProviders() {
	// This would be replaced with real provider implementations
	r.logger.Info("Mock providers initialized")
}

// initializeSchemas initializes resource schemas
func (r *AdvancedResolver) initializeSchemas() {
	r.schemas["aws.ec2.instance"] = &Schema{
		TypeURL:     "aws.ec2.instance",
		DisplayName: "EC2 Instance",
		Fields: []Field{
			{Name: "instance_id", DisplayName: "Instance ID", Type: "string", Required: true, Searchable: true},
			{Name: "name", DisplayName: "Name", Type: "string", Searchable: true},
			{Name: "instance_type", DisplayName: "Instance Type", Type: "string", Searchable: true},
			{Name: "state", DisplayName: "State", Type: "string", Searchable: true},
			{Name: "region", DisplayName: "Region", Type: "string", Searchable: true},
		},
	}

	r.schemas["gcp.compute.instance"] = &Schema{
		TypeURL:     "gcp.compute.instance",
		DisplayName: "Compute Instance",
		Fields: []Field{
			{Name: "name", DisplayName: "Name", Type: "string", Required: true, Searchable: true},
			{Name: "machine_type", DisplayName: "Machine Type", Type: "string", Searchable: true},
			{Name: "status", DisplayName: "Status", Type: "string", Searchable: true},
			{Name: "zone", DisplayName: "Zone", Type: "string", Searchable: true},
		},
	}

	r.schemas["k8s.pod"] = &Schema{
		TypeURL:     "k8s.pod",
		DisplayName: "Kubernetes Pod",
		Fields: []Field{
			{Name: "name", DisplayName: "Name", Type: "string", Required: true, Searchable: true},
			{Name: "namespace", DisplayName: "Namespace", Type: "string", Required: true, Searchable: true},
			{Name: "phase", DisplayName: "Phase", Type: "string", Searchable: true},
			{Name: "node", DisplayName: "Node", Type: "string", Searchable: true},
		},
	}

	r.logger.Info("Resource schemas initialized", zap.Int("count", len(r.schemas)))
}

// Health checks the health of the resolver
func (r *AdvancedResolver) Health(ctx context.Context) error {
	if len(r.providers) == 0 {
		return fmt.Errorf("no providers registered")
	}
	return nil
}
