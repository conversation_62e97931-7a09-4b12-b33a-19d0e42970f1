---
title: Build Guides
{{ .EditURL }}
---

import LinkCard from '@site/src/components/LinkCard';

:wave: Welcome! Getting started with Clutch is easy. There are several guides for building and running Clutch:

<LinkCard title="Docker" description="Build and/or run Clutch inside of a container." to="/docs/getting-started/docker" />
<LinkCard title="Local Build" description="Use Go and Node.js to build Clutch locally." to="/docs/getting-started/local-build" />
<LinkCard title="Mock Gateway" description="Test and develop features while avoiding interaction with real systems." to="/docs/getting-started/mock-gateway" />

#### Pre-built Binaries
At this time, Clutch does not publish pre-built binaries. To express interest in pre-built binaries please [contact us](/docs/community).
